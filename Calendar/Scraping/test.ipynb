{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fcda8911", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Booksy Biz - Inicio de sesión y registro para negocios\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.chrome.service import Service\n", "\n", "options = Options()\n", "options.add_argument('--headless')\n", "options.add_argument('--no-sandbox')\n", "options.add_argument('--disable-dev-shm-usage')\n", "service = Service(executable_path=\"/usr/local/bin/chromedriver\")\n", "driver = webdriver.Chrome(service=service, options=options)\n", "\n", "driver.get(\"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")\n", "print(driver.title)\n", "driver.close()"]}, {"cell_type": "code", "execution_count": 8, "id": "e3a11954", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Booksy Biz - Inicio de sesión y registro para negocios\n"]}], "source": ["import undetected_chromedriver as uc\n", "from selenium.webdriver.chrome.options import Options\n", "\n", "options = Options()\n", "options.binary_location = \"/usr/bin/google-chrome\"        # Chrome real\n", "options.add_argument(\"--no-sandbox\")\n", "options.add_argument(\"--disable-dev-shm-usage\")\n", "options.headless = False\n", "\n", "# No pasamos driver_executable_path, así uc se encarga del chromedriver\n", "driver = uc.Chrome(\n", "    version_main=137,                # tu major version de Chrome\n", "    browser_executable_path=options.binary_location,\n", "    options=options,\n", "    port=0\n", ")\n", "\n", "driver.get(\"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")\n", "print(driver.title)\n", "driver.quit()"]}, {"cell_type": "code", "execution_count": 4, "id": "6f894b1b", "metadata": {}, "outputs": [{"ename": "SessionNotCreatedException", "evalue": "Message: session not created: cannot connect to chrome at 127.0.0.1:44363\nfrom chrome not reachable\nStacktrace:\n#0 0x617001ed8c9a <unknown>\n#1 0x61700197e533 <unknown>\n#2 0x617001969eb3 <unknown>\n#3 0x6170019bdd5c <unknown>\n#4 0x6170019b39b3 <unknown>\n#5 0x617001a048b6 <unknown>\n#6 0x617001a03f76 <unknown>\n#7 0x6170019f5c03 <unknown>\n#8 0x6170019c247b <unknown>\n#9 0x6170019c30e1 <unknown>\n#10 0x617001e9d44b <unknown>\n#11 0x617001ea137f <unknown>\n#12 0x617001e84f89 <unknown>\n#13 0x617001ea1f18 <unknown>\n#14 0x617001e696df <unknown>\n#15 0x617001ec6308 <unknown>\n#16 0x617001ec64e6 <unknown>\n#17 0x617001ed7b76 <unknown>\n#18 0x7684c0a9caa4 <unknown>\n#19 0x7684c0b29c3c <unknown>\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mSessionNotCreatedException\u001b[39m                Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 16\u001b[39m\n\u001b[32m     13\u001b[39m driver = uc.Chrome(options=options, headless=\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[32m     15\u001b[39m \u001b[38;5;66;03m# undetected-chromedriver crea un perfil temporal interno\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m16\u001b[39m driver = \u001b[43muc\u001b[49m\u001b[43m.\u001b[49m\u001b[43mChrome\u001b[49m\u001b[43m(\u001b[49m\u001b[43mheadless\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     17\u001b[39m \u001b[38;5;66;03m#driver.get(\"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")\u001b[39;00m\n\u001b[32m     18\u001b[39m \n\u001b[32m     19\u001b[39m \u001b[38;5;66;03m# Configurar opciones\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     33\u001b[39m \u001b[38;5;66;03m# <PERSON><PERSON><PERSON><PERSON> el driver\u001b[39;00m\n\u001b[32m     34\u001b[39m \u001b[38;5;66;03m#driver = webdriver.Chrome(service=service, options=options)\u001b[39;00m\n\u001b[32m     36\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m     37\u001b[39m     \u001b[38;5;66;03m# Navegar a la página de inicio de sesión\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/undetected_chromedriver/__init__.py:466\u001b[39m, in \u001b[36mChrome.__init__\u001b[39m\u001b[34m(self, options, user_data_dir, driver_executable_path, browser_executable_path, port, enable_cdp_events, desired_capabilities, advanced_elements, keep_alive, log_level, headless, version_main, patcher_force_close, suppress_welcome, use_subprocess, debug, no_sandbox, user_multi_procs, **kw)\u001b[39m\n\u001b[32m    459\u001b[39m     \u001b[38;5;28mself\u001b[39m.browser_pid = browser.pid\n\u001b[32m    462\u001b[39m service = selenium.webdriver.chromium.service.ChromiumService(\n\u001b[32m    463\u001b[39m     \u001b[38;5;28mself\u001b[39m.patcher.executable_path\n\u001b[32m    464\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m466\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mChrome\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    467\u001b[39m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[43m=\u001b[49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    468\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    469\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    470\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    472\u001b[39m \u001b[38;5;28mself\u001b[39m.reactor = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    474\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m enable_cdp_events:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/selenium/webdriver/chrome/webdriver.py:45\u001b[39m, in \u001b[36mWebDriver.__init__\u001b[39m\u001b[34m(self, options, service, keep_alive)\u001b[39m\n\u001b[32m     42\u001b[39m service = service \u001b[38;5;28;01mif\u001b[39;00m service \u001b[38;5;28;01melse\u001b[39;00m Service()\n\u001b[32m     43\u001b[39m options = options \u001b[38;5;28;01mif\u001b[39;00m options \u001b[38;5;28;01melse\u001b[39;00m Options()\n\u001b[32m---> \u001b[39m\u001b[32m45\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m     46\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbrowser_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mDesiredCapabilities\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCHROME\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mbrowserName\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     47\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvendor_prefix\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mgoog\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     48\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     49\u001b[39m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[43m=\u001b[49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     50\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     51\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/selenium/webdriver/chromium/webdriver.py:66\u001b[39m, in \u001b[36mChromiumDriver.__init__\u001b[39m\u001b[34m(self, browser_name, vendor_prefix, options, service, keep_alive)\u001b[39m\n\u001b[32m     57\u001b[39m executor = ChromiumRemoteConnection(\n\u001b[32m     58\u001b[39m     remote_server_addr=\u001b[38;5;28mself\u001b[39m.service.service_url,\n\u001b[32m     59\u001b[39m     browser_name=browser_name,\n\u001b[32m   (...)\u001b[39m\u001b[32m     62\u001b[39m     ignore_proxy=options._ignore_local_proxy,\n\u001b[32m     63\u001b[39m )\n\u001b[32m     65\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m66\u001b[39m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcommand_executor\u001b[49m\u001b[43m=\u001b[49m\u001b[43mexecutor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     67\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[32m     68\u001b[39m     \u001b[38;5;28mself\u001b[39m.quit()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:250\u001b[39m, in \u001b[36mWebDriver.__init__\u001b[39m\u001b[34m(self, command_executor, keep_alive, file_detector, options, locator_converter, web_element_cls, client_config)\u001b[39m\n\u001b[32m    248\u001b[39m \u001b[38;5;28mself\u001b[39m._authenticator_id = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    249\u001b[39m \u001b[38;5;28mself\u001b[39m.start_client()\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstart_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcapabilities\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    251\u001b[39m \u001b[38;5;28mself\u001b[39m._fedcm = FedCM(\u001b[38;5;28mself\u001b[39m)\n\u001b[32m    253\u001b[39m \u001b[38;5;28mself\u001b[39m._websocket_connection = \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/undetected_chromedriver/__init__.py:724\u001b[39m, in \u001b[36mChrome.start_session\u001b[39m\u001b[34m(self, capabilities, browser_profile)\u001b[39m\n\u001b[32m    722\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m capabilities:\n\u001b[32m    723\u001b[39m     capabilities = \u001b[38;5;28mself\u001b[39m.options.to_capabilities()\n\u001b[32m--> \u001b[39m\u001b[32m724\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mselenium\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwebdriver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mchrome\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwebdriver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mWebDriver\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstart_session\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    725\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcapabilities\u001b[49m\n\u001b[32m    726\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:342\u001b[39m, in \u001b[36mWebDriver.start_session\u001b[39m\u001b[34m(self, capabilities)\u001b[39m\n\u001b[32m    333\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Creates a new session with the desired capabilities.\u001b[39;00m\n\u001b[32m    334\u001b[39m \n\u001b[32m    335\u001b[39m \u001b[33;03mParameters:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    338\u001b[39m \u001b[33;03m    - A capabilities dict to start the session with.\u001b[39;00m\n\u001b[32m    339\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    341\u001b[39m caps = _create_caps(capabilities)\n\u001b[32m--> \u001b[39m\u001b[32m342\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[43m.\u001b[49m\u001b[43mNEW_SESSION\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaps\u001b[49m\u001b[43m)\u001b[49m[\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m    343\u001b[39m \u001b[38;5;28mself\u001b[39m.session_id = response.get(\u001b[33m\"\u001b[39m\u001b[33msessionId\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    344\u001b[39m \u001b[38;5;28mself\u001b[39m.caps = response.get(\u001b[33m\"\u001b[39m\u001b[33mcapabilities\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:429\u001b[39m, in \u001b[36mWebDriver.execute\u001b[39m\u001b[34m(self, driver_command, params)\u001b[39m\n\u001b[32m    427\u001b[39m response = \u001b[38;5;28mself\u001b[39m.command_executor.execute(driver_command, params)\n\u001b[32m    428\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[32m--> \u001b[39m\u001b[32m429\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43merror_handler\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheck_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    430\u001b[39m     response[\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28mself\u001b[39m._unwrap_value(response.get(\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[32m    431\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/selenium/webdriver/remote/errorhandler.py:232\u001b[39m, in \u001b[36mErrorHandler.check_response\u001b[39m\u001b[34m(self, response)\u001b[39m\n\u001b[32m    230\u001b[39m         alert_text = value[\u001b[33m\"\u001b[39m\u001b[33malert\u001b[39m\u001b[33m\"\u001b[39m].get(\u001b[33m\"\u001b[39m\u001b[33mtext\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    231\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace, alert_text)  \u001b[38;5;66;03m# type: ignore[call-arg]  # mypy is not smart enough here\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m232\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace)\n", "\u001b[31mSessionNotCreatedException\u001b[39m: Message: session not created: cannot connect to chrome at 127.0.0.1:44363\nfrom chrome not reachable\nStacktrace:\n#0 0x617001ed8c9a <unknown>\n#1 0x61700197e533 <unknown>\n#2 0x617001969eb3 <unknown>\n#3 0x6170019bdd5c <unknown>\n#4 0x6170019b39b3 <unknown>\n#5 0x617001a048b6 <unknown>\n#6 0x617001a03f76 <unknown>\n#7 0x6170019f5c03 <unknown>\n#8 0x6170019c247b <unknown>\n#9 0x6170019c30e1 <unknown>\n#10 0x617001e9d44b <unknown>\n#11 0x617001ea137f <unknown>\n#12 0x617001e84f89 <unknown>\n#13 0x617001ea1f18 <unknown>\n#14 0x617001e696df <unknown>\n#15 0x617001ec6308 <unknown>\n#16 0x617001ec64e6 <unknown>\n#17 0x617001ed7b76 <unknown>\n#18 0x7684c0a9caa4 <unknown>\n#19 0x7684c0b29c3c <unknown>\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import time\n", "import undetected_chromedriver as uc\n", "\n", "options = Options()\n", "options.binary_location = \"/usr/bin/google-chrome\"  # adapta a tu ruta\n", "\n", "driver = uc.Chrome(options=options, headless=False)\n", "\n", "# undetected-chromedriver crea un perfil temporal interno\n", "driver = uc.Chrome(headless=False)\n", "#driver.get(\"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")\n", "\n", "# Configurar opciones\n", "#options = Options()\n", "# Comenta la siguiente línea si quieres ver el navegador en acción\n", "# options.add_argument('--headless')\n", "#options.add_argument('--no-sandbox')\n", "#options.add_argument(\"user-data-dir=/home/<USER>/.config/chrome-selenium-profile\")  # perfil real\n", "\n", "#options.add_argument('--disable-dev-shm-usage')\n", "\n", "\n", "# Usar el chromedriver que ya tienes descargado\n", "#chromedriver_path = \"/usr/local/bin/chromedriver\"\n", "#service = Service(executable_path=chromedriver_path)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> el driver\n", "#driver = webdriver.Chrome(service=service, options=options)\n", "\n", "try:\n", "    # Navegar a la página de inicio de sesión\n", "    driver.get(\"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")  # Reemplaza con la URL real\n", "    \n", "    # Esperar a que la página cargue y los elementos estén disponibles\n", "    wait = WebDriverWait(driver, 10)\n", "    \n", "    # wait one second\n", "    time.sleep(5)\n", "    # Localizar el campo de correo electrónico e ingresar el correo\n", "    email_field = wait.until(EC.presence_of_element_located((By.ID, \"uid-40-input\")))  # Reemplaza \"email\" con el ID real\n", "    email_field.clear()\n", "    email_field.send_keys(\"<EMAIL>\")  # Reemplaza con tu correo\n", "\n", "    # wait one second\n", "    time.sleep(5)\n", "    \n", "    # Localizar el campo de contraseña e ingresar la contraseña\n", "    password_field = driver.find_element(By.ID, \"uid-43-input\")  # <PERSON>emplaza \"password\" con el ID real\n", "    password_field.clear()\n", "    password_field.send_keys(\"Vanadis23.\")  # Reemplaza con tu contraseña\n", "\n", "\n", "    # wait one second\n", "    time.sleep(5)\n", "    \n", "    # Localizar el botón de inicio de sesión y hacer clic\n", "    login_button = driver.find_element(By.ID, \"uid-44-input\")  # Ajusta según el texto real\n", "    login_button.click()\n", "    print(\"LOGGED IN\")\n", "    \n", "    # Esperar a que se complete el inicio de sesión (ajusta según sea necesario)\n", "    wait.until(EC.url_changes(\"https://ejemplo.com/login\"))\n", "    \n", "    # Verificar que el inicio de sesión fue exitoso\n", "    print(\"Inicio de sesión exitoso\")\n", "    print(f\"URL actual: {driver.current_url}\")\n", "    \n", "    # Aquí puedes continuar con otras acciones después del inicio de sesión\n", "    \n", "    # Esperar un momento para ver el resultado (opcional)\n", "    time.sleep(3)\n", "    \n", "except Exception as e:\n", "    print(f\"Error durante el proceso: {e}\")\n", "    \n", "finally:\n", "    # Cerrar el navegador\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": 9, "id": "567b4f61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LOGGED IN\n", "Inicio de sesión exitoso\n", "URL actual: https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import time\n", "import undetected_chromedriver as uc\n", "\n", "\n", "options = Options()\n", "options.binary_location = \"/usr/bin/google-chrome\"        # Chrome real\n", "options.add_argument(\"--no-sandbox\")\n", "options.add_argument(\"--disable-dev-shm-usage\")\n", "options.headless = False\n", "\n", "# No pasamos driver_executable_path, así uc se encarga del chromedriver\n", "driver = uc.Chrome(\n", "    version_main=137,                # tu major version de Chrome\n", "    browser_executable_path=options.binary_location,\n", "    options=options,\n", "    port=0\n", ")\n", "\n", "\n", "\n", "try:\n", "    # Navegar a la página de inicio de sesión\n", "    driver.get(\"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")  # Reemplaza con la URL real\n", "    \n", "    # Esperar a que la página cargue y los elementos estén disponibles\n", "    wait = WebDriverWait(driver, 10)\n", "    \n", "    # wait one second\n", "    time.sleep(5)\n", "    # Localizar el campo de correo electrónico e ingresar el correo\n", "    email_field = wait.until(EC.presence_of_element_located((By.ID, \"uid-40-input\")))  # Reemplaza \"email\" con el ID real\n", "    email_field.clear()\n", "    email_field.send_keys(\"<EMAIL>\")  # Reemplaza con tu correo\n", "\n", "    # wait one second\n", "    time.sleep(5)\n", "    \n", "    # Localizar el campo de contraseña e ingresar la contraseña\n", "    password_field = driver.find_element(By.ID, \"uid-43-input\")  # <PERSON>emplaza \"password\" con el ID real\n", "    password_field.clear()\n", "    password_field.send_keys(\"Vanadis23.\")  # Reemplaza con tu contraseña\n", "\n", "\n", "    # wait one second\n", "    time.sleep(5)\n", "    \n", "    # Localizar el botón de inicio de sesión y hacer clic\n", "    login_button = driver.find_element(By.ID, \"uid-44-input\")  # Ajusta según el texto real\n", "    login_button.click()\n", "    print(\"LOGGED IN\")\n", "    \n", "    # Esperar a que se complete el inicio de sesión (ajusta según sea necesario)\n", "    wait.until(EC.url_changes(\"https://ejemplo.com/login\"))\n", "    \n", "    # Verificar que el inicio de sesión fue exitoso\n", "    print(\"Inicio de sesión exitoso\")\n", "    print(f\"URL actual: {driver.current_url}\")\n", "    \n", "    # Aquí puedes continuar con otras acciones después del inicio de sesión\n", "    \n", "    # Esperar un momento para ver el resultado (opcional)\n", "    time.sleep(3)\n", "    \n", "except Exception as e:\n", "    print(f\"Error durante el proceso: {e}\")\n", "    \n", "finally:\n", "    # Cerrar el navegador\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": 10, "id": "a4873795", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'de7b2653a7d558a39d88d869c12f3b71' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[10]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mde7b2653a7d558a39d88d869c12f3b71\u001b[49m\n", "\u001b[31mNameError\u001b[39m: name 'de7b2653a7d558a39d88d869c12f3b71' is not defined"]}], "source": ["de7b2653a7d558a39d88d869c12f3b71"]}, {"cell_type": "code", "execution_count": 13, "id": "314eb2b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> enviado, esperando redirección...\n"]}, {"ename": "TimeoutException", "evalue": "Message: \nStacktrace:\n#0 0x583cae37bc9a <unknown>\n#1 0x583cade216e0 <unknown>\n#2 0x583cade73117 <unknown>\n#3 0x583cade73311 <unknown>\n#4 0x583cadec1ec4 <unknown>\n#5 0x583cade98e5d <unknown>\n#6 0x583cadebf2cc <unknown>\n#7 0x583cade98c03 <unknown>\n#8 0x583cade6547b <unknown>\n#9 0x583cade660e1 <unknown>\n#10 0x583cae34044b <unknown>\n#11 0x583cae34437f <unknown>\n#12 0x583cae327f89 <unknown>\n#13 0x583cae344f18 <unknown>\n#14 0x583cae30c6df <unknown>\n#15 0x583cae369308 <unknown>\n#16 0x583cae3694e6 <unknown>\n#17 0x583cae37ab76 <unknown>\n#18 0x71d142c9caa4 <unknown>\n#19 0x71d142d29c3c <unknown>\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mTimeoutException\u001b[39m                          Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 49\u001b[39m\n\u001b[32m     45\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mL<PERSON>in enviado, esperando redirección...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     47\u001b[39m \u001b[38;5;66;03m# 2) Localizamos sitekey de reCAPTCHA v2\u001b[39;00m\n\u001b[32m     48\u001b[39m \u001b[38;5;66;03m#    (suele estar en un <div class=\"g-recaptcha\" data-sitekey=\"...\">)\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m49\u001b[39m captcha_element = \u001b[43mwait\u001b[49m\u001b[43m.\u001b[49m\u001b[43muntil\u001b[49m\u001b[43m(\u001b[49m\u001b[43mEC\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpresence_of_element_located\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     50\u001b[39m \u001b[43m    \u001b[49m\u001b[43m(\u001b[49m\u001b[43mBy\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCLASS_NAME\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mg-recaptcha\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     51\u001b[39m site_key = captcha_element.get_attribute(\u001b[33m\"\u001b[39m\u001b[33mdata-sitekey\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     52\u001b[39m page_url = driver.current_url\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/selenium/webdriver/support/wait.py:146\u001b[39m, in \u001b[36mWebDriverWait.until\u001b[39m\u001b[34m(self, method, message)\u001b[39m\n\u001b[32m    144\u001b[39m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[32m    145\u001b[39m     time.sleep(\u001b[38;5;28mself\u001b[39m._poll)\n\u001b[32m--> \u001b[39m\u001b[32m146\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m TimeoutException(message, screen, stacktrace)\n", "\u001b[31mTimeoutException\u001b[39m: Message: \nStacktrace:\n#0 0x583cae37bc9a <unknown>\n#1 0x583cade216e0 <unknown>\n#2 0x583cade73117 <unknown>\n#3 0x583cade73311 <unknown>\n#4 0x583cadec1ec4 <unknown>\n#5 0x583cade98e5d <unknown>\n#6 0x583cadebf2cc <unknown>\n#7 0x583cade98c03 <unknown>\n#8 0x583cade6547b <unknown>\n#9 0x583cade660e1 <unknown>\n#10 0x583cae34044b <unknown>\n#11 0x583cae34437f <unknown>\n#12 0x583cae327f89 <unknown>\n#13 0x583cae344f18 <unknown>\n#14 0x583cae30c6df <unknown>\n#15 0x583cae369308 <unknown>\n#16 0x583cae3694e6 <unknown>\n#17 0x583cae37ab76 <unknown>\n#18 0x71d142c9caa4 <unknown>\n#19 0x71d142d29c3c <unknown>\n"]}], "source": ["import time\n", "import requests\n", "\n", "import undetected_chromedriver as uc\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# — CONFIGURACIÓN —\n", "API_KEY_2CAPTCHA = \"de7b2653a7d558a39d88d869c12f3b71\"\n", "LOGIN_URL         = \"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\"\n", "EMAIL             = \"<EMAIL>\"\n", "PASSWORD          = \"Vanadis23.\"\n", "\n", "\n", "# Opciones de Chrome / undetected‑chromedriver\n", "options = Options()\n", "options.binary_location = \"/usr/bin/google-chrome\"        # Chrome real\n", "options.add_argument(\"--no-sandbox\")\n", "options.add_argument(\"--disable-dev-shm-usage\")\n", "options.headless = False\n", "\n", "\n", "# No pasamos driver_executable_path, así uc se encarga del chromedriver\n", "driver = uc.Chrome(\n", "    version_main=137,                # tu major version de Chrome\n", "    browser_executable_path=options.binary_location,\n", "    options=options,\n", "    port=0\n", ")\n", "wait   = Web<PERSON>riverWait(driver, 20)\n", "\n", "try:\n", "    # 1) Vamos a la página de login\n", "    driver.get(LOGIN_URL)\n", "\n", "\n", "    # 6) <PERSON><PERSON><PERSON> credenciales\n", "    wait.until(EC.presence_of_element_located((By.ID, \"uid-40-input\"))).send_keys(EMAIL)\n", "    driver.find_element(By.ID, \"uid-43-input\").send_keys(PASSWORD)\n", "\n", "    # 7) Enviar formulario de login\n", "    driver.find_element(By.ID, \"uid-44-input\").click()\n", "    print(\"Login enviado, esperando redirección...\")\n", "\n", "    # 2) Localizamos sitekey de reCAPTCHA v2\n", "    #    (suele estar en un <div class=\"g-recaptcha\" data-sitekey=\"...\">)\n", "    captcha_element = wait.until(EC.presence_of_element_located(\n", "        (By.CLASS_NAME, \"g-recaptcha\")))\n", "    site_key = captcha_element.get_attribute(\"data-sitekey\")\n", "    page_url = driver.current_url\n", "\n", "    # 3) Enviar tarea a 2Captcha\n", "    captcha_id = requests.post(\"http://2captcha.com/in.php\", data={\n", "        \"key\"       : API_KEY_2CAPTCHA,\n", "        \"method\"    : \"userrecaptcha\",\n", "        \"googlekey\" : site_key,\n", "        \"pageurl\"   : page_url,\n", "        \"json\"      : 1\n", "    }).json()[\"request\"]\n", "\n", "    # 4) <PERSON><PERSON><PERSON> respuesta (token) de 2Captcha\n", "    recaptcha_response = None\n", "    fetch_url = f\"http://2captcha.com/res.php?key={API_KEY_2CAPTCHA}&action=get&id={captcha_id}&json=1\"\n", "    for _ in range(24):  # hasta ~2 minutos\n", "        time.sleep(5)\n", "        resp = requests.get(fetch_url).json()\n", "        if resp[\"status\"] == 1:\n", "            recaptcha_response = resp[\"request\"]\n", "            break\n", "    if not recaptcha_response:\n", "        raise Exception(\"No se resolvió el CAPTCHA en el tiempo esperado\")\n", "\n", "    # 5) Inyectar el token en la página\n", "    driver.execute_script(\n", "        'document.getElementById(\"g-recaptcha-response\").innerHTML = arguments[0];',\n", "        recaptcha_response\n", "    )\n", "    # A veces es necesario invocar el callback de reCAPTCHA también:\n", "    driver.execute_script(\n", "        '___grecaptcha_cfg.clients[0].L.L.callback(arguments[0]);',\n", "        recaptcha_response\n", "    )\n", "\n", "    # 8) Comprobación de éxito\n", "    wait.until(EC.url_changes(LOGIN_URL))\n", "    print(\"✅ Login exitoso, URL actual:\", driver.current_url)\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "6d7ed408", "metadata": {}, "outputs": [{"ename": "Error", "evalue": "It looks like you are using Playwright Sync API inside the asyncio loop.\nPlease use the Async API instead.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON>\u001b[39m                                     <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[14]\u001b[39m\u001b[32m, line 17\u001b[39m\n\u001b[32m     14\u001b[39m         \u001b[38;5;28;01<PERSON><PERSON>se\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mhCaptcha no resuelto: \u001b[39m\u001b[33m\"\u001b[39m + solver.error_code)\n\u001b[32m     15\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m token\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m sync_playwright() \u001b[38;5;28;01mas\u001b[39;00m p:\n\u001b[32m     18\u001b[39m     browser = p.chromium.launch(headless=\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[32m     19\u001b[39m     context = browser.new_context()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/playwright/sync_api/_context_manager.py:47\u001b[39m, in \u001b[36mPlaywrightContextManager.__enter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     45\u001b[39m             \u001b[38;5;28mself\u001b[39m._own_loop = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m     46\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._loop.is_running():\n\u001b[32m---> \u001b[39m\u001b[32m47\u001b[39m             \u001b[38;5;28;01mraise\u001b[39;00m Error(\n\u001b[32m     48\u001b[39m \u001b[38;5;250m                \u001b[39m\u001b[33;03m\"\"\"It looks like you are using Playwright Sync API inside the asyncio loop.\u001b[39;00m\n\u001b[32m     49\u001b[39m \u001b[33;03mPlease use the Async API instead.\"\"\"\u001b[39;00m\n\u001b[32m     50\u001b[39m             )\n\u001b[32m     52\u001b[39m         \u001b[38;5;66;03m# Create a new fiber for the protocol dispatcher. It will be pumping events\u001b[39;00m\n\u001b[32m     53\u001b[39m         \u001b[38;5;66;03m# until the end of times. We will pass control to that fiber every time we\u001b[39;00m\n\u001b[32m     54\u001b[39m         \u001b[38;5;66;03m# block while waiting for a response.\u001b[39;00m\n\u001b[32m     55\u001b[39m         \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mgreenlet_main\u001b[39m() -> \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[31mError\u001b[39m: It looks like you are using Playwright Sync API inside the asyncio loop.\nPlease use the Async API instead."]}], "source": ["from playwright.sync_api import sync_playwright\n", "from anticaptchaofficial.hcaptchaproxyless import hCaptchaProxyless\n", "\n", "API_KEY = \"TU_API_KEY_HCAPTCHAPROXYLESS\"  # Anti‑Captcha / 2Captcha hCaptcha endpoint\n", "\n", "def solve_hcaptcha(page_url, site_key):\n", "    solver = hCaptchaProxyless()\n", "    solver.set_verbose(1)\n", "    solver.set_key(API_KEY)\n", "    solver.set_website_url(page_url)\n", "    solver.set_website_key(site_key)\n", "    token = solver.solve_and_return_solution()\n", "    if not token:\n", "        raise Exception(\"hCaptcha no resuelto: \" + solver.error_code)\n", "    return token\n", "\n", "with sync_playwright() as p:\n", "    browser = p.chromium.launch(headless=False)\n", "    context = browser.new_context()\n", "    page    = context.new_page()\n", "    page.goto(\"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")\n", "\n", "    # 1) <PERSON><PERSON><PERSON> email y password\n", "    page.fill(\"#uid-40-input\", \"<EMAIL>\")\n", "    page.fill(\"#uid-43-input\", \"Vanadis23.\")\n", "\n", "    # 2) Extraer sitekey de hCaptcha\n", "    #    Suele estar en <div data-sitekey=\"…\"> o en el iframe src\n", "    frame = page.frame_locator(\"iframe[src*='hcaptcha.com']\")\n", "    src   = frame.get_attribute(\"src\")\n", "    # parsea sitekey de la URL:\n", "    import urllib.parse as up\n", "    site_key = dict(up.parse_qsl(up.urlparse(src).query))[\"sitekey\"]\n", "\n", "    # 3) <PERSON><PERSON><PERSON><PERSON> h<PERSON><PERSON>\n", "    token = solve_hcaptcha(page.url, site_key)\n", "\n", "    # 4) Inyecta el token\n", "    page.evaluate(\n", "        \"\"\"(token) => {\n", "            document.querySelector('textarea[name=\"h-captcha-response\"]').value = token;\n", "        }\"\"\",\n", "        token\n", "    )\n", "\n", "    # 5) Enviar el formulario\n", "    page.click(\"#uid-44-input\")\n", "    page.wait_for_url(lambda url: url != \"https://booksy.com/pro/es-gb/onboarding/Login?authTab=sign-in\")\n", "    print(\"✅ Login exitoso:\", page.url)\n", "\n", "    browser.close()\n"]}, {"cell_type": "code", "execution_count": null, "id": "d20c246f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}