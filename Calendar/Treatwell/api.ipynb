{"cells": [{"cell_type": "code", "execution_count": 13, "id": "87414714", "metadata": {}, "outputs": [], "source": ["# -*- coding: utf-8 -*-\n", "from seleniumwire import webdriver   # ojo: seleniumwire, no selenium\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import requests\n", "from datetime import datetime, timedelta\n", "\n", "NOMBRE_USUARIO_TREATWELL = \"<EMAIL>\" # Nombre de usuario de Treatwell\n", "CONTRASENA_TREATWELL = \"Mandril2869\" # Contraseña de Treatwell\n", "APPOINTMENT_TIME = \"2025-06-20T10:00:00Z\" # Hora de la cita\n", "DURATION = 60 # Duración de la cita en minutos\n", "DIAS = 3 # Días desde mañana que se le ofrecerán al usuario para reservar\n", "HORA_APERTURA_CENTRO = 8 # Hora de apertura del centro\n", "HORA_CIERRE_CENTRO = 19 # Hora de cierre del\n", "HORA_APERTURA_SABADO = 10\n", "HORA_CIERRE_SABADO = 14\n", "AGENTES_TRABAJANDO = 2 # Número de agentes trabajando en el centro (NÚMERO DE CITAS QUE SE PUEDEN RESERVAR A LA VEZ)"]}, {"cell_type": "code", "execution_count": 14, "id": "508a2ef3", "metadata": {}, "outputs": [], "source": ["def busy_slots_raw(data):\n", "    bsr = []\n", "    for appointment in data['data']['appointments']:\n", "        if NOMBRE_USUARIO_TREATWELL == \"<EMAIL>\":\n", "            if appointment['data'] is None or appointment['state'] != 'booked' or appointment['data']['staff_member']['first_name'] != 'Miriam':\n", "                continue\n", "            else:\n", "                app = appointment['time_no_tz']\n", "                app_dur = appointment['data']['staff_member_treatment']['total_duration']\n", "                start = datetime.fromisoformat(app)\n", "                end = start + <PERSON><PERSON><PERSON>(seconds=app_dur)\n", "                bsr.append((start, end))\n", "        else:\n", "            if appointment['data'] is None or appointment['state'] != 'booked':\n", "                continue\n", "            else:\n", "                app = appointment['time_no_tz']\n", "                app_dur = appointment['data']['staff_member_treatment']['total_duration']\n", "                start = datetime.fromisoformat(app)\n", "                end = start + <PERSON><PERSON><PERSON>(seconds=app_dur)\n", "                bsr.append((start, end))\n", "    return bsr\n", "\n", "def busy_slots(busy_slots_raw):\n", "    busy_slots = {}\n", "    l = len(busy_slots_raw)\n", "    for i in range(l):\n", "\n", "        # Define the current slot to compare\n", "        start = busy_slots_raw[i][0]\n", "        end = busy_slots_raw[i][1]\n", "\n", "        # Add for the current slot if not already present\n", "        if (start, end) not in busy_slots.keys():\n", "            busy_slots[(start, end)] = 1\n", "\n", "            # Compare it with the slots in the busy_slots count\n", "            busy_slots_copy = busy_slots.copy()\n", "            for bs in busy_slots.keys():\n", "                # Define next slot to compare with\n", "                start2 = bs[0]\n", "                end2 = bs[1]\n", "\n", "                # No join\n", "                if end2 <= start or end <= start2:\n", "                    continue\n", "                \n", "                # Left join\n", "                elif start2 < start < end2 <= end :\n", "                    if (start, end2) not in busy_slots_copy.keys():\n", "                        busy_slots_copy[(start, end2)] = 2\n", "                    else:\n", "                        busy_slots_copy[(start, end2)] += 1\n", "                    \n", "                # Right join\n", "                elif start <= start2 < end < end2:\n", "                    # Add inner join slot\n", "                    if (start2, end) not in busy_slots_copy.keys():\n", "                        busy_slots_copy[(start2, end)] = 2\n", "                    else:\n", "                        busy_slots_copy[(start2, end)] += 1\n", "\n", "                # Outer join\n", "                elif start2 < start and end < end2:\n", "                    busy_slots_copy[(start, end)] += 1\n", "        else:\n", "            busy_slots[(start, end)] += 1\n", "\n", "        busy_slots = busy_slots_copy\n", "    bs = [k for k in busy_slots.keys() if busy_slots[k] > AGENTES_TRABAJANDO]\n", "    bs.sort(key=lambda x: x[0])  # Sort by start time\n", "    return bs\n", "\n", "def change_appointment(appointment_start_time, slots_to_remove):\n", "    datetime_format = appointment_start_time[:10] + ' ' + appointment_start_time[11:13] + appointment_start_time[13:16] + appointment_start_time[16:-1]\n", "    appointment_start = datetime.fromisoformat(datetime_format)\n", "    for slot in slots_to_remove:\n", "        if slot[0] <= appointment_start < slot[1]:\n", "            return True\n", "    return False\n", "\n", "def free_slots(slots_to_remove, days, cita):\n", "    #current_date = datetime.now() + <PERSON><PERSON><PERSON>(hours=17) # BUENO\n", "    current_date = datetime.now() - <PERSON><PERSON><PERSON>(hours=40) # PRUEBAS\n", "    end_date = current_date + <PERSON><PERSON>ta(days=days)\n", "    free_slots = []\n", "    while current_date <= end_date:\n", "        # Exc<PERSON>ir domingos (weekday()==6 → domingo)\n", "        if current_date.weekday() != 6:\n", "            if current_date.weekday() != 5:  # <PERSON><PERSON><PERSON><PERSON>\n", "                # Ventana diaria 08:00–19:00 en hora local Madrid\n", "                window_start = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_APERTURA_CENTRO)\n", "                window_end   = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_CIERRE_CENTRO)\n", "            else:  # <PERSON><PERSON><PERSON><PERSON>\n", "                # Ventana diaria 10:00–14:00 en hora local Madrid\n", "                window_start = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_APERTURA_SABADO)\n", "                window_end   = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_CIERRE_SABADO)\n", "            while window_start + <PERSON><PERSON><PERSON>(minutes=cita) <= window_end:\n", "                cita_start = window_start \n", "                cita_end =  window_start + <PERSON><PERSON><PERSON>(minutes=cita)\n", "                if not any(slot[0] <= cita_start < slot[1] or slot[0] < cita_end < slot[1] for slot in slots_to_remove):\n", "                    free_slots.append(cita_start)\n", "                window_start += <PERSON><PERSON>ta(minutes=cita)\n", "        current_date = current_date + <PERSON><PERSON><PERSON>(days=1)\n", "    return free_slots\n", "\n", "def human_parse(free_slots):\n", "    # ADAPTARLOS PARA FORMATOO HUMANO\n", "    free_slots_human = [slot.strftime(\"%Y-%m-%d %H:%M\") for slot in free_slots]\n", "    horarios = {}\n", "    for slots in free_slots_human:\n", "        month_day = slots[8:10]\n", "        if month_day not in horarios:\n", "            horarios[month_day] = [slots[11:18]]\n", "        else:\n", "            horarios[month_day].append(slots[11:18])\n", "    return horarios\n", "def formato_humano(fecha_dict):\n", "    \"\"\"\n", "    Convierte un diccionario de fechas y horas a formato humano.\n", "    \n", "    Input: {'06-13': ['08:00', '09:00', ...], '06-14': ['08:00', '09:00', ...]}\n", "    Output: \"el trece de junio a las ocho, nueve de la mañana, ... el catorce de junio a las ocho, nueve de la mañana, ...\"\n", "    \"\"\"\n", "    # Mapeo de números de mes a nombres\n", "    '''\n", "    meses = {\n", "        '01': 'enero', '02': 'febrero', '03': 'marzo', '04': 'abril',\n", "        '05': 'mayo', '06': 'junio', '07': 'julio', '08': 'agosto',\n", "        '09': 'septiembre', '10': 'octubre', '11': 'noviembre', '12': 'diciembre'\n", "    }'''\n", "    \n", "    # Mapeo de números de día a texto\n", "    dias_texto = {\n", "        '01': 'uno', '02': 'dos', '03': 'tres', '04': 'cuatro', '05': 'cinco',\n", "        '06': 'seis', '07': 'siete', '08': 'ocho', '09': 'nueve', '10': 'diez',\n", "        '11': 'once', '12': 'doce', '13': 'trece', '14': 'catorce', '15': 'quince',\n", "        '16': 'diecis<PERSON><PERSON>', '17': 'diecis<PERSON>e', '18': 'diecio<PERSON>', '19': 'diecin<PERSON><PERSON>',\n", "        '20': 'veinte', '21': 'veinti<PERSON>', '22': 'veintid<PERSON>', '23': 'veintitr<PERSON>',\n", "        '24': 'veinticuatro', '25': 'veinticin<PERSON>', '26': 'veintiséis', \n", "        '27': 'veintisiete', '28': 'veinti<PERSON>o', '29': 'veintinueve', '30': 'treinta',\n", "        '31': 'treinta y uno'\n", "    }\n", "    \n", "    # Mapeo de números de hora a texto (formato 12 horas)\n", "    horas_texto = {\n", "        '1': 'una', '2': 'dos', '3': 'tres', '4': 'cuatro', '5': 'cinco',\n", "        '6': 'seis', '7': 'siete', '8': 'ocho', '9': 'nueve', '10': 'diez',\n", "        '11': 'once', '12': 'doce'\n", "    }\n", "    \n", "    resultado = []\n", "    \n", "    for dia, horas in fecha_dict.items():\n", "        # Separar mes y día\n", "        \n", "        # Convertir horas a formato humano (texto con mañana/tarde)\n", "        for hora in horas:\n", "            h_24 = int(hora.split(':')[0])\n", "            \n", "            # Determinar si es mañana, tarde o noche\n", "            if h_24 < 12:\n", "                periodo = \"de la mañana\"\n", "                h_12 = h_24\n", "            elif h_24 == 12:\n", "                periodo = \"del mediodía\"\n", "                h_12 = 12\n", "            elif h_24 < 20:\n", "                periodo = \"de la tarde\"\n", "                h_12 = h_24 - 12\n", "            else:\n", "                periodo = \"de la noche\"\n", "                h_12 = h_24 - 12\n", "            \n", "            # Convertir a texto\n", "            hora_texto = \"{} {}\".format(horas_texto[str(h_12)], periodo)\n", "            #horas_humanas.append(hora_texto)\n", "        \n", "        # Formatear texto\n", "            if horas_texto[str(h_12)] == \"una\":\n", "                texto_fecha = \"{} a las {}, \".forrmat(dias_texto[dia], hora_texto)\n", "            else:\n", "                texto_fecha = \"el {} a las {}, \".format(dias_texto[dia], hora_texto)\n", "            resultado.append(texto_fecha)\n", "    \n", "    return \"\".join(resultado)"]}, {"cell_type": "code", "execution_count": 15, "id": "4e556edb", "metadata": {}, "outputs": [{"ename": "ConnectionError", "evalue": "HTTPSConnectionPool(host='pro.treatwell.es', port=443): Max retries exceeded with url: /login (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x7b462860e350>: Failed to resolve 'pro.treatwell.es' ([Errno -3] Temporary failure in name resolution)\"))", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connection.py:198\u001b[39m, in \u001b[36mHTTPConnection._new_conn\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    197\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m198\u001b[39m     sock = \u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate_connection\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    199\u001b[39m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_dns_host\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mport\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    200\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    201\u001b[39m \u001b[43m        \u001b[49m\u001b[43msource_address\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msource_address\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    202\u001b[39m \u001b[43m        \u001b[49m\u001b[43msocket_options\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msocket_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    203\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    204\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m socket.gaierror \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/util/connection.py:60\u001b[39m, in \u001b[36mcreate_connection\u001b[39m\u001b[34m(address, timeout, source_address, socket_options)\u001b[39m\n\u001b[32m     58\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m LocationParseError(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mhost\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m, label empty or too long\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m60\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m res \u001b[38;5;129;01min\u001b[39;00m \u001b[43msocket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetaddrinfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mport\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfamily\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msocket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mSOCK_STREAM\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[32m     61\u001b[39m     af, socktype, proto, canonname, sa = res\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/socket.py:977\u001b[39m, in \u001b[36mgetaddrinfo\u001b[39m\u001b[34m(host, port, family, type, proto, flags)\u001b[39m\n\u001b[32m    976\u001b[39m addrlist = []\n\u001b[32m--> \u001b[39m\u001b[32m977\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m res \u001b[38;5;129;01min\u001b[39;00m \u001b[43m_socket\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetaddrinfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mport\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfamily\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mtype\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproto\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflags\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[32m    978\u001b[39m     af, socktype, proto, canonname, sa = res\n", "\u001b[31mgaierror\u001b[39m: [Errno -3] Temporary failure in name resolution", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mNameResolutionError\u001b[39m                       <PERSON>back (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connectionpool.py:787\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    786\u001b[39m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m787\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    788\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    789\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    790\u001b[39m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    791\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    792\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    793\u001b[39m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    794\u001b[39m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    795\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m=\u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    797\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    798\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    799\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    800\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    802\u001b[39m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connectionpool.py:488\u001b[39m, in \u001b[36mHTTPConnectionPool._make_request\u001b[39m\u001b[34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    487\u001b[39m         new_e = _wrap_proxy_error(new_e, conn.proxy.scheme)\n\u001b[32m--> \u001b[39m\u001b[32m488\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m new_e\n\u001b[32m    490\u001b[39m \u001b[38;5;66;03m# conn.request() calls http.client.*.request, not the method in\u001b[39;00m\n\u001b[32m    491\u001b[39m \u001b[38;5;66;03m# urllib3.request. It also calls makefile (recv) on the socket.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connectionpool.py:464\u001b[39m, in \u001b[36mHTTPConnectionPool._make_request\u001b[39m\u001b[34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[39m\n\u001b[32m    463\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m464\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_validate_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    465\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connectionpool.py:1093\u001b[39m, in \u001b[36mHTTPSConnectionPool._validate_conn\u001b[39m\u001b[34m(self, conn)\u001b[39m\n\u001b[32m   1092\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m conn.is_closed:\n\u001b[32m-> \u001b[39m\u001b[32m1093\u001b[39m     \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1095\u001b[39m \u001b[38;5;66;03m# TODO revise this, see https://github.com/urllib3/urllib3/issues/2791\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connection.py:704\u001b[39m, in \u001b[36mHTTPSConnection.connect\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    703\u001b[39m sock: socket.socket | ssl.SSLSocket\n\u001b[32m--> \u001b[39m\u001b[32m704\u001b[39m \u001b[38;5;28mself\u001b[39m.sock = sock = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    705\u001b[39m server_hostname: \u001b[38;5;28mstr\u001b[39m = \u001b[38;5;28mself\u001b[39m.host\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connection.py:205\u001b[39m, in \u001b[36mHTTPConnection._new_conn\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    204\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m socket.gaierror \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m205\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m NameResolutionError(\u001b[38;5;28mself\u001b[39m.host, \u001b[38;5;28mself\u001b[39m, e) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m    206\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m SocketTimeout \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[31mNameResolutionError\u001b[39m: <urllib3.connection.HTTPSConnection object at 0x7b462860e350>: Failed to resolve 'pro.treatwell.es' ([Errno -3] Temporary failure in name resolution)", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mMaxRetryError\u001b[39m                             <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/requests/adapters.py:667\u001b[39m, in \u001b[36mHTTPAdapter.send\u001b[39m\u001b[34m(self, request, stream, timeout, verify, cert, proxies)\u001b[39m\n\u001b[32m    666\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m667\u001b[39m     resp = \u001b[43mconn\u001b[49m\u001b[43m.\u001b[49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    668\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    669\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    670\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    671\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m.\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    672\u001b[39m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    673\u001b[39m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    674\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    675\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    676\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    677\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    678\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m=\u001b[49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    679\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    681\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/connectionpool.py:841\u001b[39m, in \u001b[36mHTTPConnectionPool.urlopen\u001b[39m\u001b[34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[39m\n\u001b[32m    839\u001b[39m     new_e = ProtocolError(\u001b[33m\"\u001b[39m\u001b[33mConnection aborted.\u001b[39m\u001b[33m\"\u001b[39m, new_e)\n\u001b[32m--> \u001b[39m\u001b[32m841\u001b[39m retries = \u001b[43mretries\u001b[49m\u001b[43m.\u001b[49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    842\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnew_e\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[43m=\u001b[49m\u001b[43msys\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[32m    843\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    844\u001b[39m retries.sleep()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/urllib3/util/retry.py:519\u001b[39m, in \u001b[36mRetry.increment\u001b[39m\u001b[34m(self, method, url, response, error, _pool, _stacktrace)\u001b[39m\n\u001b[32m    518\u001b[39m     reason = error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause)\n\u001b[32m--> \u001b[39m\u001b[32m519\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, reason) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mreason\u001b[39;00m  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[32m    521\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mIncremented Retry for (url=\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[33m\"\u001b[39m, url, new_retry)\n", "\u001b[31mMaxRetryError\u001b[39m: HTTPSConnectionPool(host='pro.treatwell.es', port=443): Max retries exceeded with url: /login (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x7b462860e350>: Failed to resolve 'pro.treatwell.es' ([Errno -3] Temporary failure in name resolution)\"))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mConnectionError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 15\u001b[39m\n\u001b[32m     10\u001b[39m \u001b[38;5;66;03m# Ajusta el payload según el formulario real de Treatwell\u001b[39;00m\n\u001b[32m     11\u001b[39m login_payload = {\n\u001b[32m     12\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33memail\u001b[39m\u001b[33m\"\u001b[39m:    NOMBRE_USUARIO_TREATWELL,\n\u001b[32m     13\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mpassword\u001b[39m\u001b[33m\"\u001b[39m: CONTRASENA_TREATWELL,\n\u001b[32m     14\u001b[39m }\n\u001b[32m---> \u001b[39m\u001b[32m15\u001b[39m login_resp = \u001b[43msession\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mhttps://pro.treatwell.es/login\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[32m     17\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlogin_payload\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     18\u001b[39m \u001b[43m    \u001b[49m\u001b[43mallow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\n\u001b[32m     19\u001b[39m \u001b[43m)\u001b[49m\n\u001b[32m     20\u001b[39m login_resp.raise_for_status()\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33m/agenda\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m login_resp.url:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/requests/sessions.py:637\u001b[39m, in \u001b[36mSession.post\u001b[39m\u001b[34m(self, url, data, json, **kwargs)\u001b[39m\n\u001b[32m    626\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\u001b[38;5;28mself\u001b[39m, url, data=\u001b[38;5;28;01mNone\u001b[39;00m, json=\u001b[38;5;28;01mNone\u001b[39;00m, **kwargs):\n\u001b[32m    627\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33mr\u001b[39m\u001b[33;03m\"\"\"Sends a POST request. Returns :class:`Response` object.\u001b[39;00m\n\u001b[32m    628\u001b[39m \n\u001b[32m    629\u001b[39m \u001b[33;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    634\u001b[39m \u001b[33;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[32m    635\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m637\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mPOST\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[43m=\u001b[49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/requests/sessions.py:589\u001b[39m, in \u001b[36mSession.request\u001b[39m\u001b[34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[39m\n\u001b[32m    584\u001b[39m send_kwargs = {\n\u001b[32m    585\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mtimeout\u001b[39m\u001b[33m\"\u001b[39m: timeout,\n\u001b[32m    586\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mallow_redirects\u001b[39m\u001b[33m\"\u001b[39m: allow_redirects,\n\u001b[32m    587\u001b[39m }\n\u001b[32m    588\u001b[39m send_kwargs.update(settings)\n\u001b[32m--> \u001b[39m\u001b[32m589\u001b[39m resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    591\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/requests/sessions.py:703\u001b[39m, in \u001b[36mSession.send\u001b[39m\u001b[34m(self, request, **kwargs)\u001b[39m\n\u001b[32m    700\u001b[39m start = preferred_clock()\n\u001b[32m    702\u001b[39m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m703\u001b[39m r = \u001b[43madapter\u001b[49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    705\u001b[39m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[32m    706\u001b[39m elapsed = preferred_clock() - start\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/requests/adapters.py:700\u001b[39m, in \u001b[36mHTTPAdapter.send\u001b[39m\u001b[34m(self, request, stream, timeout, verify, cert, proxies)\u001b[39m\n\u001b[32m    696\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e.reason, _SSLError):\n\u001b[32m    697\u001b[39m         \u001b[38;5;66;03m# This branch is for urllib3 v1.22 and later.\u001b[39;00m\n\u001b[32m    698\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request=request)\n\u001b[32m--> \u001b[39m\u001b[32m700\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request=request)\n\u001b[32m    702\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ClosedPoolError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    703\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request=request)\n", "\u001b[31mConnectionError\u001b[39m: HTTPSConnectionPool(host='pro.treatwell.es', port=443): Max retries exceeded with url: /login (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x7b462860e350>: Failed to resolve 'pro.treatwell.es' ([Errno -3] Temporary failure in name resolution)\"))"]}], "source": ["session = requests.Session()\n", "session.headers.update({\n", "    \"User-Agent\": (\"Mozilla/5.0 (X11; Linux x86_64) \"\n", "                   \"AppleWebKit/537.36 (KHTML, like Gecko) \"\n", "                   \"Chrome/********* Safari/537.36\"),\n", "    \"Accept\": \"application/json, text/javascript, */*; q=0.01\",\n", "    \"X-Requested-With\": \"XMLHttpRequest\",\n", "})\n", "\n", "# Ajusta el payload según el formulario real de Treatwell\n", "login_payload = {\n", "    \"email\":    NOMBRE_USUARIO_TREATWELL,\n", "    \"password\": CONTRASENA_TREATWELL,\n", "}\n", "login_resp = session.post(\n", "    \"https://pro.treatwell.es/login\", \n", "    data=login_payload,\n", "    allow_redirects=True\n", ")\n", "login_resp.raise_for_status()\n", "\n", "if \"/agenda\" not in login_resp.url:\n", "    raise RuntimeError(\"Login fallido: no redirigió a /agenda\")\n", "\n", "# ————————————————\n", "# 2) Generar token\n", "# ————————————————\n", "GEN_URL = (\n", "    \"https://api.uala.com/api/v1/venues/52097\"\n", "    \"/salesforce/auth/generate_token\"\n", ")\n", "token_resp = session.get(GEN_URL)\n", "token_resp.raise_for_status()\n", "\n", "# La API devuelve el token en JSON o en headers; aj<PERSON><PERSON><PERSON> si hace falta:\n", "try:\n", "    token = token_resp.json().get(\"token\")\n", "except ValueError:\n", "    token = token_resp.headers.get(\"authorization\")\n", "\n", "if not token:\n", "    raise RuntimeError(\"No se capturó token en la respuesta\")\n", "\n", "# ————————————————\n", "# 3) Llamada final a appointments\n", "# ————————————————\n", "from_time = datetime.now()\n", "to_time   = from_time + timedelta(days=DIAS)\n", "\n", "session.headers.update({\n", "    \"Authorization\": token,\n", "    \"Referer\":       \"https://pro.treatwell.es/agenda\",\n", "    \"Origin\":        \"https://pro.treatwell.es\",\n", "})\n", "\n", "APPTS_URL = (\n", "    \"https://api.uala.com/api/v1/venues/52097/appointments.json\"\n", "    \"?from_time={from_iso}&to_time={to_iso}\"\n", ").format(\n", "    from_iso=from_time.isoformat(),\n", "    to_iso=to_time.isoformat()\n", ")\n", "\n", "resp = session.get(APPTS_URL)\n", "resp.raise_for_status()\n", "data = resp.json()\n", "\n", "# ─── <PERSON><PERSON> puedes procesar `data` con tus funciones busy_slots_raw, etc. ───\n", "\n", "bsr = busy_slots_raw(data)\n", "bs  = busy_slots(bsr)\n", "ca  = change_appointment(APPOINTMENT_TIME, bs)\n", "fs  = free_slots(bs, DIAS, int(DURATION))\n", "human = human_parse(fs)\n", "fh    = formato_humano(human)\n", "\n", "result = {\n", "    \"free_slots\":        fh,\n", "    \"change_appointment\": ca\n", "}\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "id": "399388df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token capturado: Token token=\"HojsG5vM64_Z8wMRKbmY\"\n", "Datos obtenidos: {'success': True, 'info': ['Listing appointments'], 'data': {'appointments': [{'id': 368649933, 'customer_id': 35271118, 'staff_member_treatment_id': 6810363, 'staff_member_id': 189654, 'state': 'discarded', 'by_venue': True, 'custom_duration': 1800, 'data': {'venue_id': 52097, 'treatment_id': 15771, 'staff_member_treatment': {'name': 'Limpieza Facial Profunda con Microdermoabrasión', 'short_name': 'LFP', 'duration': 3600, 'total_duration': 3600, 'price': 60.0, 'venue_treatment_id': 2245388, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-04T18:55:11+02:00', 'updated_at': '2025-06-04T18:55:29+02:00', 'booking_token': '346ccc638628c3e11eb3a76b4d46d277', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Cristina Pineda', 'customer_phone_number': '+34669655194', 'time': '2025-06-25T18:30:00+02:00', 'time_no_tz': '2025-06-25T18:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '18:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 368649932, 'customer_id': 35271118, 'staff_member_treatment_id': 9583231, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 39897, 'staff_member_treatment': {'name': 'Camilla Andulación', 'short_name': 'CA', 'duration': 1800, 'total_duration': 1800, 'price': 35.0, 'venue_treatment_id': 2835227, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': 'Cita repetida desde el 19 junio', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-04T18:55:11+02:00', 'updated_at': '2025-06-04T18:55:11+02:00', 'booking_token': '346ccc638628c3e11eb3a76b4d46d277', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': '95a63f73b43adb0a543a2774b1043d69', 'cancel_charge_requested': None, 'customer_full_name': 'Cristina Pineda', 'customer_phone_number': '+34669655194', 'time': '2025-06-25T18:30:00+02:00', 'time_no_tz': '2025-06-25T18:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '18:30:00', 'paid_online': False, 'paid_online_amount': 0.0, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371539960, 'customer_id': 35271186, 'staff_member_treatment_id': 6809944, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 13600, 'staff_member_treatment': {'name': 'Depilación Láser Piernas Completas Hombre', 'short_name': 'DLP', 'duration': 1800, 'total_duration': 1800, 'price': 70.0, 'venue_treatment_id': 2245293, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': '50', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-17T12:23:08+02:00', 'updated_at': '2025-06-17T12:23:34+02:00', 'booking_token': '213015e337b87170e65d0d2ce517973e', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Elena Garcia Martinez', 'customer_phone_number': '+34650166722', 'time': '2025-06-25T18:30:00+02:00', 'time_no_tz': '2025-06-25T18:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '18:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 368649930, 'customer_id': 35271118, 'staff_member_treatment_id': 7030944, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': 'Cita repetida desde el 19 junio', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-04T18:55:11+02:00', 'updated_at': '2025-06-04T18:55:11+02:00', 'booking_token': '346ccc638628c3e11eb3a76b4d46d277', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': '278099e21b44de6ad0f0ddf42a4bc7bf', 'cancel_charge_requested': None, 'customer_full_name': 'Cristina Pineda', 'customer_phone_number': '+34669655194', 'time': '2025-06-25T18:00:00+02:00', 'time_no_tz': '2025-06-25T18:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '18:00:00', 'paid_online': False, 'paid_online_amount': 0.0, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 363710497, 'customer_id': 35272645, 'staff_member_treatment_id': 6810804, 'staff_member_id': 189654, 'state': 'canceled', 'by_venue': True, 'custom_duration': 900, 'data': {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': 'Cita repetida cada 7 días desde el 4 junio hasta el 30 julio', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': True, 'created_at': '2025-05-14T17:54:08+02:00', 'updated_at': '2025-05-30T20:10:00+02:00', 'booking_token': '70735dc2619ebbd3b2d0439dd22f36a8', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': '71643ac16c2e3932d0049b3f672b8743', 'cancel_charge_requested': None, 'customer_full_name': 'Vicky Carnicero', 'customer_phone_number': '+34637463392', 'time': '2025-06-25T18:00:00+02:00', 'time_no_tz': '2025-06-25T18:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '18:00:00', 'paid_online': False, 'paid_online_amount': 0.0, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 363658365, 'customer_id': 47501299, 'staff_member_treatment_id': 6810240, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': 1800, 'data': {'venue_id': 52097, 'treatment_id': 65, 'staff_member_treatment': {'name': 'Tratamiento de Hidratación Profunda', 'short_name': 'TDH', 'duration': 2700, 'total_duration': 2700, 'price': 45.0, 'venue_treatment_id': 2245365, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-14T15:48:53+02:00', 'updated_at': '2025-06-17T13:22:40+02:00', 'booking_token': '10fa874089a35b60c23aaca8f48553af', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Monica Perez Lujan', 'customer_phone_number': '+34659980708', 'time': '2025-06-25T18:00:00+02:00', 'time_no_tz': '2025-06-25T18:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '18:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370554367, 'customer_id': 35271289, 'staff_member_treatment_id': 6809385, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 510, 'staff_member_treatment': {'name': 'Depilación Láser Ingles Brasileñas', 'short_name': 'DLI', 'duration': 900, 'total_duration': 900, 'price': 35.0, 'venue_treatment_id': 2245138, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': 63376, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-12T15:32:36+02:00', 'updated_at': '2025-06-12T15:32:42+02:00', 'booking_token': '51ec4fc8de3e09ea10d99e34e532bfa9', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Esther Saugar', 'customer_phone_number': '+34637468155', 'time': '2025-06-25T17:45:00+02:00', 'time_no_tz': '2025-06-25T17:45:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:45:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370554371, 'customer_id': 35271289, 'staff_member_treatment_id': 6809437, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 6554, 'staff_member_treatment': {'name': 'Depilación Láser Facial Completa', 'short_name': 'DLF', 'duration': 900, 'total_duration': 900, 'price': 40.0, 'venue_treatment_id': 2245154, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': 25.0}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-12T15:32:36+02:00', 'updated_at': '2025-06-12T15:32:44+02:00', 'booking_token': '51ec4fc8de3e09ea10d99e34e532bfa9', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Esther Saugar', 'customer_phone_number': '+34637468155', 'time': '2025-06-25T17:45:00+02:00', 'time_no_tz': '2025-06-25T17:45:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:45:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370554373, 'customer_id': 35271289, 'staff_member_treatment_id': 6810084, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 36, 'staff_member_treatment': {'name': 'Depilación de Cejas (Mantenimiento)', 'short_name': 'DDC', 'duration': 900, 'total_duration': 900, 'price': 7.0, 'venue_treatment_id': 2245332, 'color': 'FBD4C5', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-12T15:32:37+02:00', 'updated_at': '2025-06-12T15:32:37+02:00', 'booking_token': '51ec4fc8de3e09ea10d99e34e532bfa9', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Esther Saugar', 'customer_phone_number': '+34637468155', 'time': '2025-06-25T17:30:00+02:00', 'time_no_tz': '2025-06-25T17:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370751035, 'customer_id': 48217697, 'staff_member_treatment_id': 7002543, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': 900, 'data': {'venue_id': 52097, 'treatment_id': 13195, 'staff_member_treatment': {'name': 'Retirada Esmaltado Semipermanente (En manos)', 'short_name': 'RES', 'duration': 1800, 'total_duration': 1800, 'price': 7.0, 'venue_treatment_id': 2244320, 'color': 'A1ACBB', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-13T11:33:52+02:00', 'updated_at': '2025-06-13T11:33:52+02:00', 'booking_token': '62f72aac633bf4967e6f6561b91e674f', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Rosa Cosmes', 'customer_phone_number': '+34645200480', 'time': '2025-06-25T17:30:00+02:00', 'time_no_tz': '2025-06-25T17:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 363710484, 'customer_id': 35272645, 'staff_member_treatment_id': 7030944, 'staff_member_id': 189654, 'state': 'canceled', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': 'Cita repetida cada 7 días desde el 4 junio hasta el 30 julio', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': True, 'created_at': '2025-05-14T17:54:07+02:00', 'updated_at': '2025-05-30T20:09:53+02:00', 'booking_token': '70735dc2619ebbd3b2d0439dd22f36a8', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': '27903823cfafd2c5d31122fa9de91939', 'cancel_charge_requested': None, 'customer_full_name': 'Vicky Carnicero', 'customer_phone_number': '+34637463392', 'time': '2025-06-25T17:30:00+02:00', 'time_no_tz': '2025-06-25T17:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:30:00', 'paid_online': False, 'paid_online_amount': 0.0, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371382024, 'customer_id': 48026182, 'staff_member_treatment_id': 9583234, 'staff_member_id': 194878, 'state': 'booked', 'by_venue': True, 'custom_duration': 900, 'data': {'venue_id': 52097, 'treatment_id': 39897, 'staff_member_treatment': {'name': 'Camilla Andulación', 'short_name': 'CA', 'duration': 1800, 'total_duration': 1800, 'price': 35.0, 'venue_treatment_id': 2835227, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Cabina extra', 'last_name': None}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T19:00:44+02:00', 'updated_at': '2025-06-16T19:00:49+02:00', 'booking_token': '4138c93e04e8be542856ab42f228a280', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 8, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Laura Monteoca', 'customer_phone_number': '+34690657250', 'time': '2025-06-25T17:00:00+02:00', 'time_no_tz': '2025-06-25T17:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 372578725, 'customer_id': 35270723, 'staff_member_treatment_id': 6806271, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 1074, 'staff_member_treatment': {'name': 'Depilación de Medias Piernas', 'short_name': 'DDM', 'duration': 900, 'total_duration': 900, 'price': 15.0, 'venue_treatment_id': 2244368, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-20T19:12:55+02:00', 'updated_at': '2025-06-22T16:00:49+02:00', 'booking_token': '200ae937d62a7d8ddf3e5c43015b3207', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Alicia Estepa Harriero', 'customer_phone_number': '+34635505029', 'time': '2025-06-25T17:00:00+02:00', 'time_no_tz': '2025-06-25T17:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 361908257, 'customer_id': 35272196, 'staff_member_treatment_id': 7955068, 'staff_member_id': 189654, 'state': 'canceled', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'ultra slimmnig', 'short_name': 'US', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2524513, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': True, 'created_at': '2025-05-07T12:42:59+02:00', 'updated_at': '2025-06-20T15:42:37+02:00', 'booking_token': '1cac6874bcf5f4807204f0c0a4c2e28e', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Paloma Barrios', 'customer_phone_number': '+34665608282', 'time': '2025-06-25T17:00:00+02:00', 'time_no_tz': '2025-06-25T17:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '17:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370750591, 'customer_id': None, 'staff_member_treatment_id': None, 'staff_member_id': 191815, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': None, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-13T11:33:01+02:00', 'updated_at': '2025-06-13T11:33:02+02:00', 'booking_token': None, 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': None, 'customer_phone_number': None, 'time': '2025-06-25T16:45:00+02:00', 'time_no_tz': '2025-06-25T16:45:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:45:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 364724918, 'customer_id': 35272138, 'staff_member_treatment_id': 7002494, 'staff_member_id': 191815, 'state': 'canceled', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 341, 'staff_member_treatment': {'name': 'Pedicura Básica Semipermanente', 'short_name': 'PBS', 'duration': 5400, 'total_duration': 5400, 'price': 27.0, 'venue_treatment_id': 2244289, 'color': 'A0D0EE', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': True, 'created_at': '2025-05-19T17:04:56+02:00', 'updated_at': '2025-06-12T17:54:33+02:00', 'booking_token': '659d25ab67d24e06afcc5649de86100e', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Nines Martinez', 'customer_phone_number': '+34635340177', 'time': '2025-06-25T16:30:00+02:00', 'time_no_tz': '2025-06-25T16:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370750601, 'customer_id': None, 'staff_member_treatment_id': None, 'staff_member_id': 191815, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': None, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-13T11:33:03+02:00', 'updated_at': '2025-06-13T11:33:04+02:00', 'booking_token': None, 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': None, 'customer_phone_number': None, 'time': '2025-06-25T16:30:00+02:00', 'time_no_tz': '2025-06-25T16:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370750617, 'customer_id': 48217697, 'staff_member_treatment_id': 7782073, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 40602, 'staff_member_treatment': {'name': 'Manicura con refuerzo', 'short_name': 'MCR', 'duration': 3600, 'total_duration': 3600, 'price': 20.0, 'venue_treatment_id': 2484259, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-13T11:33:05+02:00', 'updated_at': '2025-06-13T11:33:52+02:00', 'booking_token': '62f72aac633bf4967e6f6561b91e674f', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Rosa Cosmes', 'customer_phone_number': '+34645200480', 'time': '2025-06-25T16:30:00+02:00', 'time_no_tz': '2025-06-25T16:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 334528846, 'customer_id': 36395752, 'staff_member_treatment_id': 7024355, 'staff_member_id': 189653, 'state': 'discarded', 'by_venue': True, 'custom_duration': 2700, 'data': {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'Consulta', 'short_name': 'C', 'duration': 900, 'total_duration': 900, 'price': 10.0, 'venue_treatment_id': 2301892, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Nuria', 'last_name': ''}, 'custom_price': 15.0}, 'external_id': None, 'notes': 'Cita repetida cada 7 días desde el 8 enero hasta el 1 julio', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2024-12-30T15:52:17+01:00', 'updated_at': '2025-05-30T20:10:08+02:00', 'booking_token': '6c87659c7de270414da12f3edfb356a3', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': 'c7d242281192f70cb2d5c0930a772d79', 'cancel_charge_requested': None, 'customer_full_name': 'Anonimo 2', 'customer_phone_number': '', 'time': '2025-06-25T16:30:00+02:00', 'time_no_tz': '2025-06-25T16:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:30:00', 'paid_online': False, 'paid_online_amount': 0.0, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371381959, 'customer_id': 48026182, 'staff_member_treatment_id': 7030944, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T19:00:22+02:00', 'updated_at': '2025-06-17T13:22:29+02:00', 'booking_token': '4138c93e04e8be542856ab42f228a280', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Laura Monteoca', 'customer_phone_number': '+34690657250', 'time': '2025-06-25T16:30:00+02:00', 'time_no_tz': '2025-06-25T16:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 368601010, 'customer_id': 35270737, 'staff_member_treatment_id': 7873068, 'staff_member_id': 194878, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 40732, 'staff_member_treatment': {'name': 'hiemtpro', 'short_name': 'H', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2289025, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Cabina extra', 'last_name': None}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-04T17:00:20+02:00', 'updated_at': '2025-06-17T13:22:28+02:00', 'booking_token': '4221fddbe150c799a35c7516777566d7', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 8, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Almudena Martinez Sanchez', 'customer_phone_number': '+34649345289', 'time': '2025-06-25T16:30:00+02:00', 'time_no_tz': '2025-06-25T16:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 368575970, 'customer_id': None, 'staff_member_treatment_id': None, 'staff_member_id': 189653, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': None, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-04T16:02:06+02:00', 'updated_at': '2025-06-04T16:02:07+02:00', 'booking_token': None, 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': None, 'customer_phone_number': None, 'time': '2025-06-25T16:15:00+02:00', 'time_no_tz': '2025-06-25T16:15:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:15:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 364724612, 'customer_id': 35272138, 'staff_member_treatment_id': 7002543, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': 900, 'data': {'venue_id': 52097, 'treatment_id': 13195, 'staff_member_treatment': {'name': 'Retirada Esmaltado Semipermanente (En manos)', 'short_name': 'RES', 'duration': 1800, 'total_duration': 1800, 'price': 7.0, 'venue_treatment_id': 2244320, 'color': 'A1ACBB', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-19T17:03:42+02:00', 'updated_at': '2025-06-10T16:11:01+02:00', 'booking_token': '659d25ab67d24e06afcc5649de86100e', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Nines Martinez', 'customer_phone_number': '+34635340177', 'time': '2025-06-25T16:15:00+02:00', 'time_no_tz': '2025-06-25T16:15:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:15:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 368600910, 'customer_id': 35270737, 'staff_member_treatment_id': 7955068, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'ultra slimmnig', 'short_name': 'US', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2524513, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-04T17:00:07+02:00', 'updated_at': '2025-06-05T10:25:21+02:00', 'booking_token': '4221fddbe150c799a35c7516777566d7', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Almudena Martinez Sanchez', 'customer_phone_number': '+34649345289', 'time': '2025-06-25T16:00:00+02:00', 'time_no_tz': '2025-06-25T16:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 369157982, 'customer_id': None, 'staff_member_treatment_id': None, 'staff_member_id': 189653, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': None, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-06T14:51:43+02:00', 'updated_at': '2025-06-06T14:51:46+02:00', 'booking_token': None, 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': None, 'customer_phone_number': None, 'time': '2025-06-25T16:00:00+02:00', 'time_no_tz': '2025-06-25T16:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371365809, 'customer_id': 35272469, 'staff_member_treatment_id': 7578564, 'staff_member_id': 194878, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Cabina extra', 'last_name': None}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T17:52:56+02:00', 'updated_at': '2025-06-16T17:53:03+02:00', 'booking_token': '3f19bcd0034d1975da49bc1c88834509', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 8, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Sara Gonzalez', 'customer_phone_number': '+34636623378', 'time': '2025-06-25T16:00:00+02:00', 'time_no_tz': '2025-06-25T16:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '16:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366929141, 'customer_id': 35272744, 'staff_member_treatment_id': 6978108, 'staff_member_id': 189653, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 40732, 'staff_member_treatment': {'name': 'hiemtpro', 'short_name': 'H', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2289025, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-28T16:05:03+02:00', 'updated_at': '2025-05-28T16:05:48+02:00', 'booking_token': '76a0a6124b0c5464b6ec2a77411c8293', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Mar Gonzalez Garcia', 'customer_phone_number': '+34606925598', 'time': '2025-06-25T15:30:00+02:00', 'time_no_tz': '2025-06-25T15:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '15:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371365731, 'customer_id': 35272469, 'staff_member_treatment_id': 7030944, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T17:52:36+02:00', 'updated_at': '2025-06-16T17:52:56+02:00', 'booking_token': '3f19bcd0034d1975da49bc1c88834509', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Sara Gonzalez', 'customer_phone_number': '+34636623378', 'time': '2025-06-25T15:30:00+02:00', 'time_no_tz': '2025-06-25T15:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '15:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371986019, 'customer_id': 48173798, 'staff_member_treatment_id': 6810803, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': 900, 'data': {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-18T18:06:16+02:00', 'updated_at': '2025-06-18T18:06:16+02:00', 'booking_token': '94242c4c1572cc419559d22327c714fd', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Salu Bravo', 'customer_phone_number': '+34648116496', 'time': '2025-06-25T15:30:00+02:00', 'time_no_tz': '2025-06-25T15:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '15:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 364724454, 'customer_id': 35272138, 'staff_member_treatment_id': 8407641, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 336, 'staff_member_treatment': {'name': 'Manicura semipermanente con base ruber', 'short_name': 'MSC', 'duration': 3600, 'total_duration': 3600, 'price': 20.0, 'venue_treatment_id': 2623630, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-19T17:03:05+02:00', 'updated_at': '2025-06-10T16:11:03+02:00', 'booking_token': '659d25ab67d24e06afcc5649de86100e', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Nines Martinez', 'customer_phone_number': '+34635340177', 'time': '2025-06-25T15:15:00+02:00', 'time_no_tz': '2025-06-25T15:15:00', 'time_datepart': '2025-06-25', 'time_timepart': '15:15:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366929047, 'customer_id': 35272744, 'staff_member_treatment_id': 7030943, 'staff_member_id': 189653, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-28T16:04:51+02:00', 'updated_at': '2025-05-28T16:05:48+02:00', 'booking_token': '76a0a6124b0c5464b6ec2a77411c8293', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Mar Gonzalez Garcia', 'customer_phone_number': '+34606925598', 'time': '2025-06-25T15:00:00+02:00', 'time_no_tz': '2025-06-25T15:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '15:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371342623, 'customer_id': 35272411, 'staff_member_treatment_id': 9154566, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 834, 'staff_member_treatment': {'name': 'Radiofrecuencia Facial', 'short_name': 'RF', 'duration': 1800, 'total_duration': 1800, 'price': 45.0, 'venue_treatment_id': 2758416, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T16:20:43+02:00', 'updated_at': '2025-06-16T16:21:03+02:00', 'booking_token': 'de315e81d2f51f43f766d588f1bbdeee', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Rosalia De Miguel', 'customer_phone_number': '+34639594008', 'time': '2025-06-25T15:00:00+02:00', 'time_no_tz': '2025-06-25T15:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '15:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371985897, 'customer_id': 48173798, 'staff_member_treatment_id': 7030943, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-18T18:05:56+02:00', 'updated_at': '2025-06-18T18:06:16+02:00', 'booking_token': '94242c4c1572cc419559d22327c714fd', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Salu Bravo', 'customer_phone_number': '+34648116496', 'time': '2025-06-25T15:00:00+02:00', 'time_no_tz': '2025-06-25T15:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '15:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 367286112, 'customer_id': 35271879, 'staff_member_treatment_id': 7002543, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': 900, 'data': {'venue_id': 52097, 'treatment_id': 13195, 'staff_member_treatment': {'name': 'Retirada Esmaltado Semipermanente (En manos)', 'short_name': 'RES', 'duration': 1800, 'total_duration': 1800, 'price': 7.0, 'venue_treatment_id': 2244320, 'color': 'A1ACBB', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-29T18:41:56+02:00', 'updated_at': '2025-05-29T18:41:56+02:00', 'booking_token': '33cff95184bbc62968a1bdb25b7c0cc2', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Marileni', 'customer_phone_number': '+34608246615', 'time': '2025-06-25T14:00:00+02:00', 'time_no_tz': '2025-06-25T14:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '14:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 367286108, 'customer_id': 35271879, 'staff_member_treatment_id': 6958679, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 6679, 'staff_member_treatment': {'name': 'Depilación con Hilo Labio Superior', 'short_name': 'DCH', 'duration': 900, 'total_duration': 900, 'price': 6.0, 'venue_treatment_id': 2284175, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-29T18:41:55+02:00', 'updated_at': '2025-05-29T18:41:56+02:00', 'booking_token': '33cff95184bbc62968a1bdb25b7c0cc2', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Marileni', 'customer_phone_number': '+34608246615', 'time': '2025-06-25T13:45:00+02:00', 'time_no_tz': '2025-06-25T13:45:00', 'time_datepart': '2025-06-25', 'time_timepart': '13:45:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 367286104, 'customer_id': 35271879, 'staff_member_treatment_id': 6958679, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 6679, 'staff_member_treatment': {'name': 'Depilación con Hilo Labio Superior', 'short_name': 'DCH', 'duration': 900, 'total_duration': 900, 'price': 6.0, 'venue_treatment_id': 2284175, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-29T18:41:55+02:00', 'updated_at': '2025-05-29T18:41:56+02:00', 'booking_token': '33cff95184bbc62968a1bdb25b7c0cc2', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Marileni', 'customer_phone_number': '+34608246615', 'time': '2025-06-25T13:30:00+02:00', 'time_no_tz': '2025-06-25T13:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '13:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 367285895, 'customer_id': 35271879, 'staff_member_treatment_id': 8407641, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 336, 'staff_member_treatment': {'name': 'Manicura semipermanente con base ruber', 'short_name': 'MSC', 'duration': 3600, 'total_duration': 3600, 'price': 20.0, 'venue_treatment_id': 2623630, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-29T18:41:21+02:00', 'updated_at': '2025-05-29T18:41:56+02:00', 'booking_token': '33cff95184bbc62968a1bdb25b7c0cc2', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Marileni', 'customer_phone_number': '+34608246615', 'time': '2025-06-25T12:30:00+02:00', 'time_no_tz': '2025-06-25T12:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '12:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366323104, 'customer_id': 44898121, 'staff_member_treatment_id': 7002729, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 510, 'staff_member_treatment': {'name': 'Depilación Láser Ingles Brasileñas', 'short_name': 'DLI', 'duration': 900, 'total_duration': 900, 'price': 35.0, 'venue_treatment_id': 2245138, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': 'compra bono ely', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': 63376, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-26T15:41:40+02:00', 'updated_at': '2025-05-26T15:42:26+02:00', 'booking_token': '473f81df01d49cc1abf46c57648c33ef', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Olaya Barriuso', 'customer_phone_number': '+34689702876', 'time': '2025-06-25T12:15:00+02:00', 'time_no_tz': '2025-06-25T12:15:00', 'time_datepart': '2025-06-25', 'time_timepart': '12:15:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 370628116, 'customer_id': 35270816, 'staff_member_treatment_id': 6810362, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': 1800, 'data': {'venue_id': 52097, 'treatment_id': 15771, 'staff_member_treatment': {'name': 'Limpieza Facial Profunda con Microdermoabrasión', 'short_name': 'LFP', 'duration': 3600, 'total_duration': 3600, 'price': 60.0, 'venue_treatment_id': 2245388, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': 'hoy hablamos del bono. Miriam', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-12T18:29:00+02:00', 'updated_at': '2025-06-13T11:10:10+02:00', 'booking_token': '98a863bcd4c46926321b3ffe67d0ed74', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Ana Belen Jimenez', 'customer_phone_number': '+34699393894', 'time': '2025-06-25T12:00:00+02:00', 'time_no_tz': '2025-06-25T12:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '12:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366322882, 'customer_id': None, 'staff_member_treatment_id': None, 'staff_member_id': 191815, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': None, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-26T15:40:52+02:00', 'updated_at': '2025-05-26T15:42:02+02:00', 'booking_token': None, 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': None, 'customer_phone_number': None, 'time': '2025-06-25T12:00:00+02:00', 'time_no_tz': '2025-06-25T12:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '12:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366323010, 'customer_id': 44898121, 'staff_member_treatment_id': 7002605, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 5796, 'staff_member_treatment': {'name': 'Depilación Láser Medias Piernas', 'short_name': 'DLM', 'duration': 900, 'total_duration': 900, 'price': 45.0, 'venue_treatment_id': 2245162, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-26T15:41:18+02:00', 'updated_at': '2025-05-26T15:41:50+02:00', 'booking_token': '473f81df01d49cc1abf46c57648c33ef', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Olaya Barriuso', 'customer_phone_number': '+34689702876', 'time': '2025-06-25T12:00:00+02:00', 'time_no_tz': '2025-06-25T12:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '12:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366323103, 'customer_id': 44898121, 'staff_member_treatment_id': 7002645, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 5879, 'staff_member_treatment': {'name': 'Depilación Láser Axilas', 'short_name': 'DLA', 'duration': 900, 'total_duration': 900, 'price': 35.0, 'venue_treatment_id': 2245128, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': 63376, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-26T15:41:40+02:00', 'updated_at': '2025-05-26T15:41:52+02:00', 'booking_token': '473f81df01d49cc1abf46c57648c33ef', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Olaya Barriuso', 'customer_phone_number': '+34689702876', 'time': '2025-06-25T12:00:00+02:00', 'time_no_tz': '2025-06-25T12:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '12:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366816725, 'customer_id': 47135818, 'staff_member_treatment_id': 6952913, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': 1800, 'data': {'venue_id': 52097, 'treatment_id': 5995, 'staff_member_treatment': {'name': 'Tratamiento Facial Dermapen', 'short_name': 'TFD', 'duration': 2700, 'total_duration': 2700, 'price': 150.0, 'venue_treatment_id': 2282403, 'color': '7F9CD0', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-28T11:36:13+02:00', 'updated_at': '2025-05-28T11:36:26+02:00', 'booking_token': '6475dd25e042460b1d406647849bdb15', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Noelia Vico', 'customer_phone_number': '+34653397102', 'time': '2025-06-25T11:30:00+02:00', 'time_no_tz': '2025-06-25T11:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '11:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 364902655, 'customer_id': 35272061, 'staff_member_treatment_id': 7002442, 'staff_member_id': 191815, 'state': 'canceled', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': True, 'created_at': '2025-05-20T12:25:32+02:00', 'updated_at': '2025-06-13T14:02:45+02:00', 'booking_token': 'e38edb1b9f3368ef1408df8fb167ef6f', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Mª Carmen Contreras', 'customer_phone_number': '+34669117523', 'time': '2025-06-25T11:30:00+02:00', 'time_no_tz': '2025-06-25T11:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '11:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366816686, 'customer_id': 47135818, 'staff_member_treatment_id': 7955069, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'ultra slimmnig', 'short_name': 'US', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2524513, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': 'Cita repetida desde el 18 junio', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-28T11:36:08+02:00', 'updated_at': '2025-05-28T11:36:08+02:00', 'booking_token': '6475dd25e042460b1d406647849bdb15', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': 'c1aa6671e25eab839f94f8275966dbb3', 'cancel_charge_requested': None, 'customer_full_name': 'Noelia Vico', 'customer_phone_number': '+34653397102', 'time': '2025-06-25T11:00:00+02:00', 'time_no_tz': '2025-06-25T11:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '11:00:00', 'paid_online': False, 'paid_online_amount': 0.0, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 364902514, 'customer_id': 35272061, 'staff_member_treatment_id': 7002526, 'staff_member_id': 191815, 'state': 'canceled', 'by_venue': True, 'custom_duration': 1800, 'data': {'venue_id': 52097, 'treatment_id': 12036, 'staff_member_treatment': {'name': 'Masaje Anticelulítico', 'short_name': 'MA', 'duration': 2700, 'total_duration': 2700, 'price': 45.0, 'venue_treatment_id': 2245521, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': True, 'created_at': '2025-05-20T12:25:17+02:00', 'updated_at': '2025-06-13T14:02:45+02:00', 'booking_token': 'e38edb1b9f3368ef1408df8fb167ef6f', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Mª Carmen Contreras', 'customer_phone_number': '+34669117523', 'time': '2025-06-25T11:00:00+02:00', 'time_no_tz': '2025-06-25T11:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '11:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371254007, 'customer_id': 38603637, 'staff_member_treatment_id': 6958687, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': 3600, 'data': {'venue_id': 52097, 'treatment_id': 336, 'staff_member_treatment': {'name': 'Manicura Semipermanente', 'short_name': 'MS', 'duration': 2700, 'total_duration': 2700, 'price': 16.0, 'venue_treatment_id': 2284178, 'color': 'FBD4C5', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T10:56:10+02:00', 'updated_at': '2025-06-16T10:56:42+02:00', 'booking_token': '463a53c730e9df075b978b384c0cfe41', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Isabel Garcia Altes', 'customer_phone_number': '+34654394595', 'time': '2025-06-25T11:00:00+02:00', 'time_no_tz': '2025-06-25T11:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '11:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371251487, 'customer_id': None, 'staff_member_treatment_id': None, 'staff_member_id': 194878, 'state': 'deleted', 'by_venue': True, 'custom_duration': None, 'data': None, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T10:48:27+02:00', 'updated_at': '2025-06-16T10:48:44+02:00', 'booking_token': None, 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 8, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': None, 'customer_phone_number': None, 'time': '2025-06-25T10:45:00+02:00', 'time_no_tz': '2025-06-25T10:45:00', 'time_datepart': '2025-06-25', 'time_timepart': '10:45:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371678074, 'customer_id': 35271055, 'staff_member_treatment_id': 7030944, 'staff_member_id': 189654, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-17T17:37:02+02:00', 'updated_at': '2025-06-18T12:21:45+02:00', 'booking_token': '829890739664f84dbdf8409ff83e5a1b', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Clara Sanchez Martin', 'customer_phone_number': '+34629987369', 'time': '2025-06-25T10:30:00+02:00', 'time_no_tz': '2025-06-25T10:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '10:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 371251351, 'customer_id': 44633793, 'staff_member_treatment_id': 7002457, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': 1800, 'data': {'venue_id': 52097, 'treatment_id': 65, 'staff_member_treatment': {'name': 'Tratamiento de Hidratación Profunda', 'short_name': 'TDH', 'duration': 2700, 'total_duration': 2700, 'price': 45.0, 'venue_treatment_id': 2245365, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-06-16T10:48:01+02:00', 'updated_at': '2025-06-16T10:48:35+02:00', 'booking_token': '5767aac0923469a5ca3e9d3930df3e56', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Balbina Gordillo', 'customer_phone_number': '+34602513464', 'time': '2025-06-25T10:30:00+02:00', 'time_no_tz': '2025-06-25T10:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '10:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366929475, 'customer_id': 35272744, 'staff_member_treatment_id': 6978108, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 40732, 'staff_member_treatment': {'name': 'hiemtpro', 'short_name': 'H', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2289025, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-28T16:05:48+02:00', 'updated_at': '2025-05-28T16:05:48+02:00', 'booking_token': '76a0a6124b0c5464b6ec2a77411c8293', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Mar Gonzalez Garcia', 'customer_phone_number': '+34606925598', 'time': '2025-06-25T10:30:00+02:00', 'time_no_tz': '2025-06-25T10:30:00', 'time_datepart': '2025-06-25', 'time_timepart': '10:30:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366929474, 'customer_id': 35272744, 'staff_member_treatment_id': 7030943, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': 'Lipodrenaje', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Miriam', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-28T16:05:48+02:00', 'updated_at': '2025-05-28T16:05:48+02:00', 'booking_token': '76a0a6124b0c5464b6ec2a77411c8293', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Mar Gonzalez Garcia', 'customer_phone_number': '+34606925598', 'time': '2025-06-25T10:00:00+02:00', 'time_no_tz': '2025-06-25T10:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '10:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 366929538, 'customer_id': 35272744, 'staff_member_treatment_id': 6965819, 'staff_member_id': 191815, 'state': 'booked', 'by_venue': True, 'custom_duration': None, 'data': {'venue_id': 52097, 'treatment_id': 496, 'staff_member_treatment': {'name': 'diodo completo', 'short_name': 'DC', 'duration': 1800, 'total_duration': 1800, 'price': 120.0, 'venue_treatment_id': 2286222, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Eli', 'last_name': ''}, 'custom_price': None}, 'external_id': None, 'notes': None, 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2025-05-28T16:05:58+02:00', 'updated_at': '2025-05-28T16:06:15+02:00', 'booking_token': '76a0a6124b0c5464b6ec2a77411c8293', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': None, 'cancel_charge_requested': None, 'customer_full_name': 'Mar Gonzalez Garcia', 'customer_phone_number': '+34606925598', 'time': '2025-06-25T10:00:00+02:00', 'time_no_tz': '2025-06-25T10:00:00', 'time_datepart': '2025-06-25', 'time_timepart': '10:00:00', 'paid_online': False, 'paid_online_amount': None, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}, {'id': 334529892, 'customer_id': 36395752, 'staff_member_treatment_id': 7024355, 'staff_member_id': 189653, 'state': 'booked', 'by_venue': True, 'custom_duration': 2700, 'data': {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'Consulta', 'short_name': 'C', 'duration': 900, 'total_duration': 900, 'price': 10.0, 'venue_treatment_id': 2301892, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Nuria', 'last_name': ''}, 'custom_price': 15.0}, 'external_id': None, 'notes': 'Cita repetida cada 7 días desde el 14 enero hasta el 1 julio', 'unsatisfied': False, 'cherry_picked_staff_member': False, 'workstation_id': None, 'proposed_by_staff_member': False, 'canceled_by_venue': False, 'created_at': '2024-12-30T15:55:39+01:00', 'updated_at': '2024-12-30T15:55:39+01:00', 'booking_token': '35f394ca513c7ab9243bf88ae94b0fd8', 'checked_in_at': None, 'checked_out_at': None, 'customer_promotion_data': {}, 'marketing_promotion_data': None, 'staff_member_job_title_id': 7, 'parent_id': None, 'checkout_id': None, 'extra_time_staff_member_after': None, 'extra_time_workstation_after': None, 'always_apply_extra_time_after': False, 'repetition_token': '1b155dc1e1bf05e0b519409bb4d75a6d', 'cancel_charge_requested': None, 'customer_full_name': 'Anonimo 2', 'customer_phone_number': '', 'time': '2025-06-24T16:30:00+02:00', 'time_no_tz': '2025-06-24T16:30:00', 'time_datepart': '2025-06-24', 'time_timepart': '16:30:00', 'paid_online': False, 'paid_online_amount': 0.0, 'paid_online_at': None, 'discount_type': None, 'discount_percent_amount': None, 'discount_absolute_amount': None, 'api_key_id': None, 'source_marketplace': 'uala', 'payment_protection_enabled': False, 'cancellation_protection': {}}], 'appointments_count': 54}}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 15771, 'staff_member_treatment': {'name': 'Limpieza Facial Profunda con Microdermoabrasión', 'short_name': 'LFP', 'duration': 3600, 'total_duration': 3600, 'price': 60.0, 'venue_treatment_id': 2245388, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 39897, 'staff_member_treatment': {'name': 'Camilla Andulación', 'short_name': 'CA', 'duration': 1800, 'total_duration': 1800, 'price': 35.0, 'venue_treatment_id': 2835227, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 13600, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON> Láser Piernas Completas Hombre', 'short_name': 'DLP', 'duration': 1800, 'total_duration': 1800, 'price': 70.0, 'venue_treatment_id': 2245293, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 65, 'staff_member_treatment': {'name': 'Tratamiento de Hidratación Profunda', 'short_name': 'TDH', 'duration': 2700, 'total_duration': 2700, 'price': 45.0, 'venue_treatment_id': 2245365, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 510, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON> Ingles Brasileñas', 'short_name': 'DLI', 'duration': 900, 'total_duration': 900, 'price': 35.0, 'venue_treatment_id': 2245138, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 6554, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON>ción Láser Facial Completa', 'short_name': 'DLF', 'duration': 900, 'total_duration': 900, 'price': 40.0, 'venue_treatment_id': 2245154, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': 25.0}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 36, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON> (Mantenimiento)', 'short_name': 'DDC', 'duration': 900, 'total_duration': 900, 'price': 7.0, 'venue_treatment_id': 2245332, 'color': 'FBD4C5', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 13195, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON> (En manos)', 'short_name': 'RES', 'duration': 1800, 'total_duration': 1800, 'price': 7.0, 'venue_treatment_id': 2244320, 'color': 'A1ACBB', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 39897, 'staff_member_treatment': {'name': 'Camilla Andulación', 'short_name': 'CA', 'duration': 1800, 'total_duration': 1800, 'price': 35.0, 'venue_treatment_id': 2835227, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>abina extra', 'last_name': None}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 1074, 'staff_member_treatment': {'name': 'Depilación de Medias Piernas', 'short_name': 'DDM', 'duration': 900, 'total_duration': 900, 'price': 15.0, 'venue_treatment_id': 2244368, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'ultra slimmnig', 'short_name': 'US', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2524513, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: None\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 341, 'staff_member_treatment': {'name': 'Pedicura Básica Semipermanente', 'short_name': 'PBS', 'duration': 5400, 'total_duration': 5400, 'price': 27.0, 'venue_treatment_id': 2244289, 'color': 'A0D0EE', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: None\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 40602, 'staff_member_treatment': {'name': 'Man<PERSON>ura con refuerzo', 'short_name': 'MCR', 'duration': 3600, 'total_duration': 3600, 'price': 20.0, 'venue_treatment_id': 2484259, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': '<PERSON><PERSON>', 'short_name': 'C', 'duration': 900, 'total_duration': 900, 'price': 10.0, 'venue_treatment_id': 2301892, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON><PERSON>', 'last_name': ''}, 'custom_price': 15.0}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 40732, 'staff_member_treatment': {'name': 'hiemtpro', 'short_name': 'H', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2289025, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Cabina extra', 'last_name': None}, 'custom_price': None}\n", "Skipping appointment: None\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 13195, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON> (En manos)', 'short_name': 'RES', 'duration': 1800, 'total_duration': 1800, 'price': 7.0, 'venue_treatment_id': 2244320, 'color': 'A1ACBB', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'ultra slimmnig', 'short_name': 'US', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2524513, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: None\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Cabina extra', 'last_name': None}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 40732, 'staff_member_treatment': {'name': 'hiemtpro', 'short_name': 'H', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2289025, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 336, 'staff_member_treatment': {'name': 'Manicura semipermanente con base ruber', 'short_name': 'MSC', 'duration': 3600, 'total_duration': 3600, 'price': 20.0, 'venue_treatment_id': 2623630, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 834, 'staff_member_treatment': {'name': 'Radiofrecuencia Facial', 'short_name': 'RF', 'duration': 1800, 'total_duration': 1800, 'price': 45.0, 'venue_treatment_id': 2758416, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': 'Ana', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 13195, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON> (En manos)', 'short_name': 'RES', 'duration': 1800, 'total_duration': 1800, 'price': 7.0, 'venue_treatment_id': 2244320, 'color': 'A1ACBB', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 6679, 'staff_member_treatment': {'name': 'Depilación con Hilo Labio Superior', 'short_name': 'DCH', 'duration': 900, 'total_duration': 900, 'price': 6.0, 'venue_treatment_id': 2284175, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 6679, 'staff_member_treatment': {'name': 'Depilación con Hilo Labio Superior', 'short_name': 'DCH', 'duration': 900, 'total_duration': 900, 'price': 6.0, 'venue_treatment_id': 2284175, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 336, 'staff_member_treatment': {'name': 'Manicura semipermanente con base ruber', 'short_name': 'MSC', 'duration': 3600, 'total_duration': 3600, 'price': 20.0, 'venue_treatment_id': 2623630, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 510, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON> Ingles Brasileñas', 'short_name': 'DLI', 'duration': 900, 'total_duration': 900, 'price': 35.0, 'venue_treatment_id': 2245138, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 15771, 'staff_member_treatment': {'name': 'Limpieza Facial Profunda con Microdermoabrasión', 'short_name': 'LFP', 'duration': 3600, 'total_duration': 3600, 'price': 60.0, 'venue_treatment_id': 2245388, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: None\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 5796, 'staff_member_treatment': {'name': 'Depilación Láser Medias Piernas', 'short_name': 'DLM', 'duration': 900, 'total_duration': 900, 'price': 45.0, 'venue_treatment_id': 2245162, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 5879, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON>ás<PERSON> A<PERSON>', 'short_name': 'DLA', 'duration': 900, 'total_duration': 900, 'price': 35.0, 'venue_treatment_id': 2245128, 'color': 'F11A1F', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 5995, 'staff_member_treatment': {'name': 'Tratamiento Facial Dermapen', 'short_name': 'TFD', 'duration': 2700, 'total_duration': 2700, 'price': 150.0, 'venue_treatment_id': 2282403, 'color': '7F9CD0', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 42, 'staff_member_treatment': {'name': 'Presoterapia', 'short_name': 'P', 'duration': 1800, 'total_duration': 1800, 'price': 25.0, 'venue_treatment_id': 2245497, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': 'ultra slimmnig', 'short_name': 'US', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2524513, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 12036, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON>', 'short_name': 'MA', 'duration': 2700, 'total_duration': 2700, 'price': 45.0, 'venue_treatment_id': 2245521, 'color': 'F7CD09', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 336, 'staff_member_treatment': {'name': 'Manic<PERSON> Semipermanente', 'short_name': 'MS', 'duration': 2700, 'total_duration': 2700, 'price': 16.0, 'venue_treatment_id': 2284178, 'color': 'FBD4C5', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: None\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 65, 'staff_member_treatment': {'name': 'Tratamiento de Hidratación Profunda', 'short_name': 'TDH', 'duration': 2700, 'total_duration': 2700, 'price': 45.0, 'venue_treatment_id': 2245365, 'color': 'F98122', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 40732, 'staff_member_treatment': {'name': 'hiemtpro', 'short_name': 'H', 'duration': 1800, 'total_duration': 1800, 'price': 80.0, 'venue_treatment_id': 2289025, 'color': 'A8DBBD', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Processing appointment: {'venue_id': 52097, 'treatment_id': 2065, 'staff_member_treatment': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'short_name': 'L', 'duration': 1800, 'total_duration': 1800, 'price': 50.0, 'venue_treatment_id': 2303469, 'color': 'D1ADD4', 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 496, 'staff_member_treatment': {'name': 'diodo completo', 'short_name': 'DC', 'duration': 1800, 'total_duration': 1800, 'price': 120.0, 'venue_treatment_id': 2286222, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON>', 'last_name': ''}, 'custom_price': None}\n", "Skipping appointment: {'venue_id': 52097, 'treatment_id': 5164, 'staff_member_treatment': {'name': '<PERSON><PERSON>', 'short_name': 'C', 'duration': 900, 'total_duration': 900, 'price': 10.0, 'venue_treatment_id': 2301892, 'color': None, 'lifecycle_typology': 'appointment'}, 'staff_member': {'first_name': '<PERSON><PERSON>', 'last_name': ''}, 'custom_price': 15.0}\n"]}], "source": ["# FLUJO\n", "\n", "# 1) Login con Selenium\n", "opts = webdriver.ChromeOptions()\n", "opts.add_argument(\"--headless\")\n", "driver = webdriver.Chrome(options=opts)\n", "wait = Web<PERSON>river<PERSON>ait(driver, 15)\n", "\n", "driver.get(\"https://pro.treatwell.es/login\")\n", "wait.until(EC.presence_of_element_located((By.NAME, \"email\"))) \\\n", "    .send_keys(NOMBRE_USUARIO_TREATWELL + Keys.ENTER)\n", "driver.find_element(By.NAME, \"password\") \\\n", "      .send_keys(CONTRASENA_TREATWELL + Keys.ENTER)\n", "wait.until(EC.url_contains(\"/agenda\"))\n", "\n", "# 2) Inyecta el fetch en la página\n", "gen_url = (\n", "    \"https://api.uala.com/api/v1/venues/52097\"\n", "    \"/salesforce/auth/generate_token\"\n", ")\n", "driver.execute_script(\"\"\"\n", "window._genPromise = fetch(\"{0}\", {{\n", "  method: \"GET\",\n", "  credentials: \"include\",\n", "  headers: {{ \"X-Requested-With\": \"XMLHttpRequest\" }}\n", "}});\n", "\"\"\".format(gen_url))\n", "\n", "\n", "# 3) Espera a que aparezca la petición XHR de generate_token\n", "#    Aquí selenium‑wire bloqueará hasta que vea una request que contenga \"generate_token\"\n", "req = driver.wait_for_request(\"generate_token\", timeout=30)\n", "\n", "# 4) Extrae el Authorization de esa petición\n", "token = req.headers.get(\"authorization\")\n", "driver.quit()\n", "\n", "if not token:\n", "    raise RuntimeError(\"No se capturó token en la petición XHR\")\n", "\n", "print(\"Token capturado:\", token)\n", "# => Token capturado: Token token=\"xcrVxcgs9Hnx6dyQy2w_\"\n", "\n", "# 5) <PERSON><PERSON> s<PERSON>, usa requests para la llamada final\n", "from_time = datetime.now()\n", "to_time   = from_time + timedelta(days=DIAS)\n", "\n", "session = requests.Session()\n", "session.headers.update({\n", "    \"Accept\":            \"application/json, text/javascript, */*; q=0.01\",\n", "    \"X-Requested-With\":  \"XMLHttpRequest\",\n", "    \"Origin\":            \"https://pro.treatwell.es\",\n", "    \"Referer\":           \"https://pro.treatwell.es/agenda\",\n", "    \"User-Agent\":        (\"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 \"\n", "                          \"(KHTML, like Gecko) Chrome/********* Safari/537.36\"),\n", "    \"Authorization\":     token,\n", "})\n", "\n", "url = \"https://api.uala.com/api/v1/venues/52097/appointments.json\" + \\\n", "      \"?from_time=\" + str(from_time) + \"&to_time=\" + str(to_time)\n", "resp = session.get(url)\n", "#resp.raise_for_status()\n", "\n", "data = resp.json()\n", "print(\"Datos obtenidos:\", data)\n", "\n", "bsr = busy_slots_raw(data)\n", "bs = busy_slots(bsr)\n", "ca = change_appointment(APPOINTMENT_TIME, bs)\n", "fs = free_slots(bs, DIAS, DURATION)\n", "human = human_parse(fs)\n", "fh = formato_humano(human)\n", "result  = {\n", "    \"free_slots\": fh,\n", "    \"change_appointment\": ca\n", "}\n", "#print(fh)\n", "#print(\"Cambiar la cita:\", ca)"]}, {"cell_type": "code", "execution_count": null, "id": "f6321048", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}