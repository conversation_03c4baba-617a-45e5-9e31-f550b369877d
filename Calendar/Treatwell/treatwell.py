# -*- coding: utf-8 -*-
import requests
from datetime import datetime, timedelta

NOMBRE_USUARIO_TREATWELL = "<EMAIL>" # Nombre de usuario de Treatwell
CONTRASENA_TREATWELL = "Mandril2869" # Contraseña de Treatwell
APPOINTMENT_TIME = "2025-06-20T10:00:00Z" # Hora de la cita
DURATION = 60 # Duración de la cita en minutos
DIAS = 3 # Días desde mañana que se le ofrecerán al usuario para reservar
HORA_APERTURA_CENTRO = 8 # Hora de apertura del centro
HORA_CIERRE_CENTRO = 19 # Hora de cierre del
HORA_APERTURA_SABADO = 10
HORA_CIERRE_SABADO = 14
AGENTES_TRABAJANDO = 2 # Número de agentes trabajando en el centro (NÚMERO DE CITAS QUE SE PUEDEN RESERVAR A LA VEZ)E CITAS QUE SE PUEDEN RESERVAR A LA VEZ)

def busy_slots_raw(data):
    bsr = []
    for appointment in data['data']['appointments']:
        if NOMBRE_USUARIO_TREATWELL == "<EMAIL>":
            if appointment['data'] is None or appointment['state'] != 'booked' or appointment['data']['staff_member']['first_name'] != 'Miriam':
                continue
            else:
                app = appointment['time_no_tz']
                app_dur = appointment['data']['staff_member_treatment']['total_duration']
                start = datetime.fromisoformat(app)
                end = start + timedelta(seconds=app_dur)
                bsr.append((start, end))
        else:
            if appointment['data'] is None or appointment['state'] != 'booked':
                continue
            else:
                app = appointment['time_no_tz']
                app_dur = appointment['data']['staff_member_treatment']['total_duration']
                start = datetime.fromisoformat(app)
                end = start + timedelta(seconds=app_dur)
                bsr.append((start, end))
    return bsr

def busy_slots(busy_slots_raw):
    busy_slots = {}
    l = len(busy_slots_raw)
    for i in range(l):

        # Define the current slot to compare
        start = busy_slots_raw[i][0]
        end = busy_slots_raw[i][1]

        # Add for the current slot if not already present
        if (start, end) not in busy_slots.keys():
            busy_slots[(start, end)] = 1

            # Compare it with the slots in the busy_slots count
            busy_slots_copy = busy_slots.copy()
            for bs in busy_slots.keys():
                # Define next slot to compare with
                start2 = bs[0]
                end2 = bs[1]

                # No join
                if end2 <= start or end <= start2:
                    continue
                
                # Left join
                elif start2 < start < end2 <= end :
                    if (start, end2) not in busy_slots_copy.keys():
                        busy_slots_copy[(start, end2)] = 2
                    else:
                        busy_slots_copy[(start, end2)] += 1
                    
                # Right join
                elif start <= start2 < end < end2:
                    # Add inner join slot
                    if (start2, end) not in busy_slots_copy.keys():
                        busy_slots_copy[(start2, end)] = 2
                    else:
                        busy_slots_copy[(start2, end)] += 1

                # Outer join
                elif start2 < start and end < end2:
                    busy_slots_copy[(start, end)] += 1
        else:
            busy_slots[(start, end)] += 1

        busy_slots = busy_slots_copy
    bs = [k for k in busy_slots.keys() if busy_slots[k] > AGENTES_TRABAJANDO]
    bs.sort(key=lambda x: x[0])  # Sort by start time
    return bs

def change_appointment(appointment_start_time, slots_to_remove):
    datetime_format = appointment_start_time[:10] + ' ' + appointment_start_time[11:13] + appointment_start_time[13:16] + appointment_start_time[16:-1]
    appointment_start = datetime.fromisoformat(datetime_format)
    for slot in slots_to_remove:
        if slot[0] <= appointment_start < slot[1]:
            return True
    return False

def free_slots(slots_to_remove, days, cita):
    #current_date = datetime.now() + timedelta(hours=17) # BUENO
    current_date = datetime.now() - timedelta(hours=40) # PRUEBAS
    end_date = current_date + timedelta(days=days)
    free_slots = []
    while current_date <= end_date:
        # Excluir domingos (weekday()==6 → domingo)
        if current_date.weekday() != 6:
            if current_date.weekday() != 5:  # Sábado
                # Ventana diaria 08:00–19:00 en hora local Madrid
                window_start = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_APERTURA_CENTRO)
                window_end   = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_CIERRE_CENTRO)
            else:  # Sábado
                # Ventana diaria 10:00–14:00 en hora local Madrid
                window_start = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_APERTURA_SABADO)
                window_end   = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=HORA_CIERRE_SABADO)
            while window_start + timedelta(minutes=cita) <= window_end:
                cita_start = window_start 
                cita_end =  window_start + timedelta(minutes=cita)
                if not any(slot[0] <= cita_start < slot[1] or slot[0] < cita_end < slot[1] for slot in slots_to_remove):
                    free_slots.append(cita_start)
                window_start += timedelta(minutes=cita)
        current_date = current_date + timedelta(days=1)
    return free_slots

def human_parse(free_slots):
    # ADAPTARLOS PARA FORMATOO HUMANO
    free_slots_human = [slot.strftime("%Y-%m-%d %H:%M") for slot in free_slots]
    horarios = {}
    for slots in free_slots_human:
        month_day = slots[8:10]
        if month_day not in horarios:
            horarios[month_day] = [slots[11:18]]
        else:
            horarios[month_day].append(slots[11:18])
    return horarios
def formato_humano(fecha_dict):
    """
    Convierte un diccionario de fechas y horas a formato humano.
    
    Input: {'06-13': ['08:00', '09:00', ...], '06-14': ['08:00', '09:00', ...]}
    Output: "el trece de junio a las ocho, nueve de la mañana, ... el catorce de junio a las ocho, nueve de la mañana, ..."
    """
    # Mapeo de números de mes a nombres
    '''
    meses = {
        '01': 'enero', '02': 'febrero', '03': 'marzo', '04': 'abril',
        '05': 'mayo', '06': 'junio', '07': 'julio', '08': 'agosto',
        '09': 'septiembre', '10': 'octubre', '11': 'noviembre', '12': 'diciembre'
    }'''
    
    # Mapeo de números de día a texto
    dias_texto = {
        '01': 'uno', '02': 'dos', '03': 'tres', '04': 'cuatro', '05': 'cinco',
        '06': 'seis', '07': 'siete', '08': 'ocho', '09': 'nueve', '10': 'diez',
        '11': 'once', '12': 'doce', '13': 'trece', '14': 'catorce', '15': 'quince',
        '16': 'dieciséis', '17': 'diecisiete', '18': 'dieciocho', '19': 'diecinueve',
        '20': 'veinte', '21': 'veintiuno', '22': 'veintidós', '23': 'veintitrés',
        '24': 'veinticuatro', '25': 'veinticinco', '26': 'veintiséis', 
        '27': 'veintisiete', '28': 'veintiocho', '29': 'veintinueve', '30': 'treinta',
        '31': 'treinta y uno'
    }
    
    # Mapeo de números de hora a texto (formato 12 horas)
    horas_texto = {
        '1': 'una', '2': 'dos', '3': 'tres', '4': 'cuatro', '5': 'cinco',
        '6': 'seis', '7': 'siete', '8': 'ocho', '9': 'nueve', '10': 'diez',
        '11': 'once', '12': 'doce'
    }
    
    resultado = []
    
    for dia, horas in fecha_dict.items():
        # Separar mes y día
        
        # Convertir horas a formato humano (texto con mañana/tarde)
        for hora in horas:
            h_24 = int(hora.split(':')[0])
            
            # Determinar si es mañana, tarde o noche
            if h_24 < 12:
                periodo = "de la mañana"
                h_12 = h_24
            elif h_24 == 12:
                periodo = "del mediodía"
                h_12 = 12
            elif h_24 < 20:
                periodo = "de la tarde"
                h_12 = h_24 - 12
            else:
                periodo = "de la noche"
                h_12 = h_24 - 12
            
            # Convertir a texto
            hora_texto = "{} {}".format(horas_texto[str(h_12)], periodo)
            #horas_humanas.append(hora_texto)
        
        # Formatear texto
            if horas_texto[str(h_12)] == "una":
                texto_fecha = "{} a las {}, ".forrmat(dias_texto[dia], hora_texto)
            else:
                texto_fecha = "el {} a las {}, ".format(dias_texto[dia], hora_texto)
            resultado.append(texto_fecha)
    
    return "".join(resultado)

# FLUJO

bsr = busy_slots_raw(data)
bs = busy_slots(bsr)
ca = change_appointment(APPOINTMENT_TIME, bs)
fs = free_slots(bs, DIAS, DURATION)
human = human_parse(fs)
fh = formato_humano(human)
result  = {
    "free_slots": fh,
    "change_appointment": ca
}