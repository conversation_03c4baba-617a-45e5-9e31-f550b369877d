{"cells": [{"cell_type": "code", "execution_count": 19, "id": "fe3a8c51", "metadata": {}, "outputs": [], "source": ["fb_obj =  {\n", "        \"kind\": \"calendar#freeBusy\",\n", "        \"timeMin\": \"2025-06-13T04:00:45.000Z\",\n", "        \"timeMax\": \"2025-06-21T11:00:45.000Z\",\n", "        \"calendars\": {\n", "            \"<EMAIL>\": {\n", "                \"busy\": [\n", "                    {\n", "                        \"start\": \"2025-06-14T12:00:00.000Z\",\n", "                        \"end\": \"2025-06-14T13:00:00.000Z\"\n", "                    },\n", "                    {\n", "                        \"start\": \"2025-06-16T14:00:00.000Z\",\n", "                        \"end\": \"2025-06-16T15:00:00.000Z\"\n", "                    }\n", "                ]\n", "            }\n", "        }\n", "    }\n", "appointment_start_time = \"2025-06-16T16:00:00\""]}, {"cell_type": "code", "execution_count": null, "id": "25c11248", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'13': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], '14': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '15:00', '16:00', '17:00', '18:00'], '16': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '17:00', '18:00'], '17': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], '18': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], '19': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], '20': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'], '21': ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00']}\n"]}], "source": ["# -*- coding: utf-8 -*-\n", "from datetime import datetime, timedelta\n", "import pytz  # pip install pytz\n", "\n", "# --- Configuración de zona horaria ---\n", "UTC = pytz.UTC\n", "MADRID = pytz.timezone(\"Europe/Madrid\")\n", "\n", "# --- Helpers para parsear ISO y formatear con zona ---\n", "\n", "def parse_iso_maybe_z(dt_str):\n", "    \"\"\"\n", "    Parsea cadenas ISO y retorna un datetime aware en UTC.\n", "    Asume que las cadenas sin sufijo Z ya están en UTC.\n", "    \"\"\"\n", "    s = dt_str.strip().replace('\"', '')\n", "    # <PERSON><PERSON>ar sufijo 'Z' si existe\n", "    if s.endswith('Z'):\n", "        s = s[:-1]\n", "    # Intentamos con o sin fracciones de segundo\n", "    for fmt in (\"%Y-%m-%dT%H:%M:%S.%f\", \"%Y-%m-%dT%H:%M:%S\"):\n", "        try:\n", "            naive = datetime.strptime(s, fmt)\n", "            # Asignar UTC\n", "            return UTC.localize(naive)\n", "        except ValueError:\n", "            continue\n", "    raise ValueError(f\"Formato inválido: {dt_str}\")\n", "\n", "def isoformat_madrid(dt_input):\n", "    \"\"\"\n", "    Suma 2 horas al datetime y lo formatea como ISO.\n", "    \"\"\"\n", "    # Sumar 2 horas al datetime\n", "    dt_plus_2 = dt_input + <PERSON><PERSON><PERSON>(hours=2)\n", "    \n", "    # Devolver formateado\n", "    return dt_plus_2.strftime(\"%Y-%m-%dT%H:%M:%S\")\n", "\n", "def formato_humano(fecha_dict):\n", "    \"\"\"\n", "    Convierte un diccionario de fechas y horas a formato humano.\n", "    \n", "    Input: {'06-13': ['08:00', '09:00', ...], '06-14': ['08:00', '09:00', ...]}\n", "    Output: \"el trece de junio a las ocho, nueve de la mañana, ... el catorce de junio a las ocho, nueve de la mañana, ...\"\n", "    \"\"\"\n", "    # Mapeo de números de mes a nombres\n", "    '''\n", "    meses = {\n", "        '01': 'enero', '02': 'febrero', '03': 'marzo', '04': 'abril',\n", "        '05': 'mayo', '06': 'junio', '07': 'julio', '08': 'agosto',\n", "        '09': 'septiembre', '10': 'octubre', '11': 'noviembre', '12': 'diciembre'\n", "    }'''\n", "    \n", "    # Mapeo de números de día a texto\n", "    dias_texto = {\n", "        '01': 'uno', '02': 'dos', '03': 'tres', '04': 'cuatro', '05': 'cinco',\n", "        '06': 'seis', '07': 'siete', '08': 'ocho', '09': 'nueve', '10': 'diez',\n", "        '11': 'once', '12': 'doce', '13': 'trece', '14': 'catorce', '15': 'quince',\n", "        '16': 'diecis<PERSON><PERSON>', '17': 'diecis<PERSON>e', '18': 'diecio<PERSON>', '19': 'diecin<PERSON><PERSON>',\n", "        '20': 'veinte', '21': 'veinti<PERSON>', '22': 'veintid<PERSON>', '23': 'veintitr<PERSON>',\n", "        '24': 'veinticuatro', '25': 'veinticin<PERSON>', '26': 'veintiséis', \n", "        '27': 'veintisiete', '28': 'veinti<PERSON>o', '29': 'veintinueve', '30': 'treinta',\n", "        '31': 'treinta y uno'\n", "    }\n", "    \n", "    # Mapeo de números de hora a texto (formato 12 horas)\n", "    horas_texto = {\n", "        '1': 'una', '2': 'dos', '3': 'tres', '4': 'cuatro', '5': 'cinco',\n", "        '6': 'seis', '7': 'siete', '8': 'ocho', '9': 'nueve', '10': 'diez',\n", "        '11': 'once', '12': 'doce'\n", "    }\n", "    \n", "    resultado = []\n", "    \n", "    for dia, horas in fecha_dict.items():\n", "        # Separar mes y día\n", "        \n", "        # Convertir horas a formato humano (texto con mañana/tarde)\n", "        horas_humanas = []\n", "        for hora in horas:\n", "            h_24 = int(hora.split(':')[0])\n", "            \n", "            # Determinar si es mañana, tarde o noche\n", "            if h_24 < 12:\n", "                periodo = \"de la mañana\"\n", "                h_12 = h_24\n", "            elif h_24 == 12:\n", "                periodo = \"del mediodía\"\n", "                h_12 = 12\n", "            elif h_24 < 20:\n", "                periodo = \"de la tarde\"\n", "                h_12 = h_24 - 12\n", "            else:\n", "                periodo = \"de la noche\"\n", "                h_12 = h_24 - 12\n", "            \n", "            # Convertir a texto\n", "            hora_texto = f\"{horas_texto[str(h_12)]} {periodo}\"\n", "            #horas_humanas.append(hora_texto)\n", "        \n", "        # Formatear texto\n", "            texto_fecha = f\"el {dias_texto[dia]} a las {hora_texto}, \"\n", "            resultado.append(texto_fecha)\n", "    \n", "    return \"\".join(resultado)\n", "\n", "# --- 1) <PERSON><PERSON> inputs ---\n", "# fb_obj: entrada de freeBusy de Google Calendar\n", "# appointment_start_time: string \"YYYY-MM-DDTHH:MM:SS\"\n", "\n", "appointment_dt_utc = parse_iso_maybe_z(appointment_start_time)\n", "\n", "# --- 2) Parsear timeMin / timeMax ---\n", "time_min_utc = parse_iso_maybe_z(fb_obj[\"timeMin\"])\n", "time_max_utc = parse_iso_maybe_z(fb_obj[\"timeMax\"])\n", "\n", "# --- 3) Extraer y convertir franjas 'busy' en UTC-aware ---\n", "cal_entry = list(fb_obj[\"calendars\"].values())[0]\n", "busy_slots = []\n", "for slot in cal_entry.get(\"busy\", []):\n", "    start = parse_iso_maybe_z(slot[\"start\"])\n", "    end   = parse_iso_maybe_z(slot[\"end\"])\n", "    busy_slots.append((start, end))\n", "\n", "# --- 4) Determinar rango de fechas en hora local Madrid ---\n", "start_local = time_min_utc.astimezone(MADRID).date()\n", "end_local   = time_max_utc.astimezone(MADRID).date()\n", "\n", "output = []\n", "current_date = start_local\n", "cita  = 1 # horas\n", "while current_date <= end_local:\n", "    # Exc<PERSON>ir domingos (weekday()==6 → domingo)\n", "    if current_date.weekday() != 6:\n", "        # Ventana diaria 08:00–19:00 en hora local Madrid\n", "        window_start_local = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=5.75) # Esta dos horas por debajo lo que nos devuelve Google Calendar\n", "        window_end_local   = datetime.combine(current_date, datetime.min.time(), tzinfo=MADRID) + timedelta(hours=16.75)\n", "        # Convertir ventana a UTC para comparar slots ocupados\n", "        window_start_utc = window_start_local.astimezone(UTC)\n", "        window_end_utc   = window_end_local.astimezone(UTC)\n", "\n", "        # Filtrar franjas busy que se solapen con ventana UTC\n", "        busy_today = []\n", "        for bs, be in busy_slots:\n", "            if be > window_start_utc and bs < window_end_utc:\n", "                busy_today.append((max(bs, window_start_utc), min(be, window_end_utc)))\n", "        busy_today.sort(key=lambda x: x[0])\n", "\n", "        # Calcular huecos libres en UTC y luego convertirlos a hora local\n", "        frees = []\n", "        cursor = window_start_utc\n", "        for bs, be in busy_today:\n", "            if bs > cursor:\n", "                # Verificar que el intervalo libre tenga al menos 30 minutos\n", "                duration = bs - cursor\n", "                if duration >= timedelta(hours=cita):\n", "                    frees.append({\n", "                        \"start\": isoformat_madrid(cursor),\n", "                        \"end\":   isoformat_madrid(bs)\n", "                    })\n", "            cursor = max(cursor, be)\n", "        if cursor < window_end_utc:\n", "            # Verificar que el último intervalo libre tenga al menos 30 minutos\n", "            duration = window_end_utc - cursor\n", "            if duration >= timedelta(minutes=30):\n", "                frees.append({\n", "                    \"start\": isoformat_madrid(cursor),\n", "                    \"end\":   isoformat_madrid(window_end_utc)\n", "                })\n", "\n", "        output.append({\n", "            \"date\": current_date.isoformat(),\n", "            \"free_slots\": frees\n", "        })\n", "    current_date += <PERSON><PERSON><PERSON>(days=1)\n", "\n", "# --- 5) Verificar cita existente en hora local Madrid ---\n", "appointment_dt_utc = datetime.fromisoformat(appointment_dt_utc.strftime(\"%Y-%m-%d %H:%M:%S\"))\n", "#print(f\"Appointment in UTC: {appointment_dt_utc}\")\n", "\n", "change_appointment = True\n", "for day in output:\n", "        l = len(day[\"free_slots\"])\n", "        #print(f\"Checking {day['date']} with {l} free slots\")\n", "        for fs in day[\"free_slots\"]:\n", "            # Parsear y convertir ISO a aware Madrid\n", "            fs_start = datetime.fromisoformat(fs[\"start\"])\n", "            fs_end   = datetime.fromisoformat(fs[\"end\"])\n", "            #print(f\"Free slot: {fs_start},  {fs_end}\")\n", "            if fs_start < appointment_dt_utc < fs_end:\n", "                change_appointment = False\n", "                break\n", "\n", "horarios = {}\n", "free = []\n", "equal = False\n", "for day in output:\n", "    free = []\n", "    for fs in day[\"free_slots\"]:\n", "        start = fs[\"start\"]\n", "        start = datetime.fromisoformat(start)\n", "        while not equal:\n", "            end = start + <PERSON><PERSON><PERSON>(hours=cita)\n", "            if end < datetime.fromisoformat(fs[\"end\"]):\n", "                free.append(start.strftime(\"%H:%M\"))\n", "                start = end\n", "            elif end <= datetime.fromisoformat(fs[\"end\"]):\n", "                free.append(start.strftime(\"%H:%M\"))\n", "                equal = True\n", "            else:\n", "                equal = True\n", "        equal = False\n", "    date = datetime.fromisoformat(day[\"date\"]).strftime(\"%d\")\n", "    horarios[date] = free\n", "print(horarios)\n", "texto_humano = formato_humano(horarios)\n", "\n", "# --- 6) Resultado global ---\n", "result = {\n", "    \"free_slots\": texto_humano,\n", "    \"change_appointment\": change_appointment\n", "}"]}, {"cell_type": "code", "execution_count": 21, "id": "cc9c589a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'free_slots': 'el trece a las ocho de la mañana, el trece a las nueve de la mañana, el trece a las diez de la mañana, el trece a las once de la mañana, el trece a las doce del mediodía, el trece a las una de la tarde, el trece a las dos de la tarde, el trece a las tres de la tarde, el trece a las cuatro de la tarde, el trece a las cinco de la tarde, el trece a las seis de la tarde, el catorce a las ocho de la mañana, el catorce a las nueve de la mañana, el catorce a las diez de la mañana, el catorce a las once de la mañana, el catorce a las doce del mediodía, el catorce a las una de la tarde, el catorce a las tres de la tarde, el catorce a las cuatro de la tarde, el catorce a las cinco de la tarde, el catorce a las seis de la tarde, el dieciséis a las ocho de la mañana, el dieciséis a las nueve de la mañana, el dieciséis a las diez de la mañana, el dieciséis a las once de la mañana, el dieciséis a las doce del mediodía, el dieciséis a las una de la tarde, el dieciséis a las dos de la tarde, el dieciséis a las tres de la tarde, el dieciséis a las cinco de la tarde, el dieciséis a las seis de la tarde, el diecisiete a las ocho de la mañana, el diecisiete a las nueve de la mañana, el diecisiete a las diez de la mañana, el diecisiete a las once de la mañana, el diecisiete a las doce del mediodía, el diecisiete a las una de la tarde, el diecisiete a las dos de la tarde, el diecisiete a las tres de la tarde, el diecisiete a las cuatro de la tarde, el diecisiete a las cinco de la tarde, el diecisiete a las seis de la tarde, el dieciocho a las ocho de la mañana, el dieciocho a las nueve de la mañana, el dieciocho a las diez de la mañana, el dieciocho a las once de la mañana, el dieciocho a las doce del mediodía, el dieciocho a las una de la tarde, el dieciocho a las dos de la tarde, el dieciocho a las tres de la tarde, el dieciocho a las cuatro de la tarde, el dieciocho a las cinco de la tarde, el dieciocho a las seis de la tarde, el diecinueve a las ocho de la mañana, el diecinueve a las nueve de la mañana, el diecinueve a las diez de la mañana, el diecinueve a las once de la mañana, el diecinueve a las doce del mediodía, el diecinueve a las una de la tarde, el diecinueve a las dos de la tarde, el diecinueve a las tres de la tarde, el diecinueve a las cuatro de la tarde, el diecinueve a las cinco de la tarde, el diecinueve a las seis de la tarde, el veinte a las ocho de la mañana, el veinte a las nueve de la mañana, el veinte a las diez de la mañana, el veinte a las once de la mañana, el veinte a las doce del mediodía, el veinte a las una de la tarde, el veinte a las dos de la tarde, el veinte a las tres de la tarde, el veinte a las cuatro de la tarde, el veinte a las cinco de la tarde, el veinte a las seis de la tarde, el veintiuno a las ocho de la mañana, el veintiuno a las nueve de la mañana, el veintiuno a las diez de la mañana, el veintiuno a las once de la mañana, el veintiuno a las doce del mediodía, el veintiuno a las una de la tarde, el veintiuno a las dos de la tarde, el veintiuno a las tres de la tarde, el veintiuno a las cuatro de la tarde, el veintiuno a las cinco de la tarde, el veintiuno a las seis de la tarde, ',\n", " 'change_appointment': True}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "id": "5dab3833", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}