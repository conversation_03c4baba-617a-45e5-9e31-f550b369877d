{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import time"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 49\u001b[39m\n\u001b[32m     47\u001b[39m     \u001b[38;5;28mprint\u001b[39m(a)\n\u001b[32m     48\u001b[39m     \u001b[38;5;66;03m# Espera para asegurar que el archivo se descarga (ajusta el tiempo según el tamaño del archivo y la conexión)\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m49\u001b[39m     \u001b[43mtime\u001b[49m\u001b[43m.\u001b[49m\u001b[43msleep\u001b[49m\u001b[43m(\u001b[49m\u001b[32;43m10\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     51\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m     52\u001b[39m     driver.quit()\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# Configuración de las opciones de Chrome para descargas automáticas\n", "options = Options()\n", "options.add_argument('--headless')\n", "options.add_argument('--no-sandbox')\n", "options.add_argument('--disable-dev-shm-usage')\n", "\n", "download_dir = '/home/<USER>/Documents/Clibel'  # Reemplaza con la ruta deseada\n", "prefs = {\n", "    \"download.default_directory\": download_dir,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "    \"download.directory_upgrade\": True,\n", "    \"safebrowsing.enabled\": True\n", "}\n", "options.add_experimental_option(\"prefs\", prefs)\n", "\n", "# Inicializamos el driver de Chrome\n", "driver = webdriver.Chrome(options=options)\n", "\n", "try:\n", "    # Navegar a la página del dashboard\n", "    driver.get('https://dashboard.vapi.ai/calls')\n", "\n", "    # Si es necesario autenticarse, localiza y completa los campos de login antes\n", "    # Ejemplo:\n", "    # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, \"login-form\")))\n", "    # driver.find_element(By.ID, \"username\").send_keys(\"tu_usuario\")\n", "    # driver.find_element(By.ID, \"password\").send_keys(\"tu_contraseña\")\n", "    # driver.find_element(By.ID, \"login-button\").click()\n", "    \n", "    # Espera a que la página cargue y que se renderice el contenido (ajusta el tiempo o usa un wait explícito)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"root\"))\n", "    )\n", "    \n", "    # Espera adicional para que se renderice el botón de exportación, si es necesario\n", "    #time.sleep(5)\n", "    \n", "    # Localiza el botón \"Export to CSV\". Se recomienda inspeccionar el HTML de la página\n", "    # para ajustar el selector correcto. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, si el botón tiene un texto específico:\n", "    #export_button = WebDriverWait(driver, 10).until(\n", "    #    EC.element_to_be_clickable((By.XPATH, \"/html/body/div[1]/div[2]/div[2]/div[2]/div/div[3]/div[3]/div[1]/button\"))\n", "    #)\n", "\n", "    # Hacer clic en el botón para iniciar la descarga\n", "    #export_button.click()\n", "    a=driver.find_elements('xpath', \"//button[text()='Export as CSV']\")\n", "    print(a)\n", "    # Espera para asegurar que el archivo se descarga (ajusta el tiempo según el tamaño del archivo y la conexión)\n", "    time.sleep(10)\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}