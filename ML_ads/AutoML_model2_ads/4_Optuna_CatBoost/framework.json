{"uid": "858f3dcd-af39-43c5-b908-42f13befc571", "name": "4_Optuna_CatBoost", "preprocessing": [{"scale_y": {"scale": [0.8211706673631429], "mean": [0.4207229727151287], "var": [0.6743212649376294], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8346999263942918], "mean": [0.42996936912635636], "var": [0.6967239671226361], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8351871699359255], "mean": [0.4267552162709529], "var": [0.6975376088255804], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.81886101693931], "mean": [0.41553713076390103], "var": [0.670533365062881], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8337176677697381], "mean": [0.4309213625254967], "var": [0.6950851495514113], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8134753858563379], "mean": [0.41305252889866456], "var": [0.6617422033941178], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8218611565977175], "mean": [0.4247006703486496], "var": [0.6754557607241379], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8298397500459297], "mean": [0.42137784377512366], "var": [0.6886340107562912], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8160733190945939], "mean": [0.4163287823768878], "var": [0.6659756621380668], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8086812963234827], "mean": [0.414534039849729], "var": [0.6539654390234284], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}], "learners": [{"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "a3e3c216-06e3-4d89-a52e-fb0d0c49ea09", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_0", "best_ntree_limit": 107}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "2039cabf-eaa3-4415-a288-1b6100ec28bd", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_1", "best_ntree_limit": 126}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "94816e91-888e-48bd-a295-1c3a8a53bc20", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_2", "best_ntree_limit": 211}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "92a5df56-32b9-43e8-b07c-fd76a56d630f", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_3", "best_ntree_limit": 271}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "6a9795de-26e4-493c-aa2a-0d93b6cfd6be", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_4", "best_ntree_limit": 88}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "c09793aa-534c-4d10-99e8-1da7f939e635", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_5", "best_ntree_limit": 251}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "ad607461-e0b8-41f4-8b3b-bd54c715bbdd", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_6", "best_ntree_limit": 53}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "0b1bdd3e-568a-40ff-87e2-450feaba2fd7", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_7", "best_ntree_limit": 135}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "7e36b2dd-074f-4e07-a680-e5b845ff2d76", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_8", "best_ntree_limit": 75}, {"library_version": "1.2.8", "algorithm_name": "CatBoost", "algorithm_short_name": "CatBoost", "uid": "32b3acc9-1b38-480d-98ff-642516cc4153", "params": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "name": "learner_fold_9", "best_ntree_limit": 119}], "params": {"additional": {"max_rounds": 10000, "early_stopping_rounds": 50, "max_rows_limit": null, "max_cols_limit": null}, "preprocessing": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}, "validation_strategy": {"validation_type": "kfold", "k_folds": 10, "shuffle": true, "X_path": "AutoML_model2_ads/X.data", "y_path": "AutoML_model2_ads/y.data", "results_path": "AutoML_model2_ads"}, "learner": {"model_type": "CatBoost", "ml_task": "regression", "n_jobs": -1, "learning_rate": 0.1, "depth": 8, "rsm": 0.34881782962878705, "loss_function": "RMSE", "seed": 1234, "eval_metric": "mse", "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "min_data_in_leaf": 81, "num_boost_round": 1000, "early_stopping_rounds": 50, "explain_level": 0}, "automl_random_state": 1234, "ml_task": "regression", "explain_level": 0, "name": "4_Optuna_CatBoost", "status": "initialized", "final_loss": null, "train_time": null, "data_type": "original", "optuna_time_budget": 1, "optuna_init_params": {"original_LightGBM": {"learning_rate": 0.1, "num_leaves": 1598, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "bagging_freq": 7, "min_data_in_leaf": 36, "extra_trees": false, "metric": "l2", "custom_eval_metric_name": null, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "seed": 1234}, "original_Xgboost": {"eta": 0.1, "max_depth": 10, "lambda": 2.840098794801191e-06, "alpha": 3.0773599420974e-06, "colsample_bytree": 0.8613105322932351, "subsample": 0.970697557159987, "min_child_weight": 88, "objective": "reg:<PERSON><PERSON><PERSON><PERSON>", "eval_metric": "mse", "max_rounds": 1000, "early_stopping_rounds": 50, "seed": 1234}, "original_CatBoost": {"learning_rate": 0.1, "depth": 8, "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "rsm": 0.34881782962878705, "min_data_in_leaf": 81, "eval_metric": "mse", "num_boost_round": 1000, "early_stopping_rounds": 50, "seed": 1234}}, "optuna_verbose": true, "max_time_for_learner": 3600}, "saved": ["4_<PERSON>tuna_CatBoost/learner_fold_0.catboost", "4_<PERSON>tuna_CatBoost/learner_fold_1.catboost", "4_<PERSON><PERSON>a_CatBoost/learner_fold_2.catboost", "4_<PERSON>tuna_CatBoost/learner_fold_3.catboost", "4_<PERSON>tuna_CatBoost/learner_fold_4.catboost", "4_<PERSON><PERSON>a_CatBoost/learner_fold_5.catboost", "4_<PERSON><PERSON><PERSON>_CatBoost/learner_fold_6.catboost", "4_<PERSON><PERSON>a_CatBoost/learner_fold_7.catboost", "4_<PERSON><PERSON>a_CatBoost/learner_fold_8.catboost", "4_<PERSON><PERSON>a_CatBoost/learner_fold_9.catboost"], "predictions_fname": "4_Optuna_CatBoost/predictions_out_of_folds.csv", "metric_name": "mse", "final_loss": "73.86300753550843", "train_time": 15.549439191818237, "is_stacked": false, "joblib_version": "1.3.2"}