# Summary of 4_Optuna_CatBoost

[<< Go back](../README.md)


## CatBoost
- **n_jobs**: -1
- **learning_rate**: 0.1
- **depth**: 8
- **rsm**: 0.34881782962878705
- **loss_function**: RMSE
- **eval_metric**: mse
- **l2_leaf_reg**: 7.7997800836072235
- **random_strength**: 2.7259260601004898
- **min_data_in_leaf**: 81
- **num_boost_round**: 1000
- **early_stopping_rounds**: 50
- **explain_level**: 0

## Validation
 - **validation_type**: kfold
 - **k_folds**: 10
 - **shuffle**: True

## Optimized metric
mse

## Training time

15.5 seconds

### Metric details:
| Metric   |        Score |
|:---------|-------------:|
| MAE      |  2.07003     |
| MSE      | 73.863       |
| RMSE     |  8.59436     |
| R2       | -0.00725299  |
| MAPE     |  1.63913e+15 |



## Learning curves
![Learning curves](learning_curves.png)
## True vs Predicted

![True vs Predicted](true_vs_predicted.png)


## Predicted vs Residuals

![Predicted vs Residuals](predicted_vs_residuals.png)



[<< Go back](../README.md)
