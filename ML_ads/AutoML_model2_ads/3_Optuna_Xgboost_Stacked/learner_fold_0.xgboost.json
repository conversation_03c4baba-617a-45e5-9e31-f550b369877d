{"learner": {"attributes": {"best_iteration": "7", "best_score": "1.00358"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "58"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.003705224, -0.13736677, 0.43775153, -0.23439275, 0.06710151, 0.2729424, 0.7182777, -0.34767547, -0.17339435, 0.1232402, -0.011207448, 0.012122934, 0.040024187, 0.060790326, 0.08196419, -0.3903293, -0.025272433, -0.074798234, -0.25078976, 0.055659473, 0.024738848, -0.04650588, -0.034623425, -0.016069083, 0.0016353083, -0.32980075, -0.011038887, -0.003510093, 0.017926654, -0.038667403, -0.023398163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [126.81101, 31.007946, 23.486496, 7.324665, 5.05953, 6.1801567, 2.1033478, 1.5025558, 5.257679, 3.2133813, 0.0, 0.0, 0.0, 0.0, 0.0, 0.8435707, 0.0, 2.3722553, 4.2819805, 2.7822196, 0.0, 0.0, 0.0, 0.0, 0.0, 1.346035, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 17, 17, 18, 18, 19, 19, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [0.76372665, 0.48169166, 1.1347722, 0.2243912, 1.0, 0.8025691, 1.3934005, 1.0, 1.2624887, 1.0, -0.011207448, 0.012122934, 0.040024187, 0.060790326, 0.08196419, 0.13875106, -0.025272433, 1.0, 0.450694, 1.0, 0.024738848, -0.04650588, -0.034623425, -0.016069083, 0.0016353083, 1.0, -0.011038887, -0.003510093, 0.017926654, -0.038667403, -0.023398163], "split_indices": [139, 139, 139, 142, 0, 142, 139, 83, 138, 50, 0, 0, 0, 0, 0, 142, 0, 17, 142, 97, 0, 0, 0, 0, 0, 93, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1563.0, 508.0, 1060.0, 503.0, 320.0, 188.0, 371.0, 689.0, 383.0, 120.0, 146.0, 174.0, 90.0, 98.0, 256.0, 115.0, 303.0, 386.0, 248.0, 135.0, 95.0, 161.0, 156.0, 147.0, 247.0, 139.0, 143.0, 105.0, 155.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0025439714, -0.1768963, 0.2999088, -0.25855175, -0.058550544, 0.21793322, 0.0783711, -0.32250378, -0.10989134, 0.011266381, -0.099528395, 0.12127843, 0.33858734, -0.25926468, -0.37645602, 0.000453214, -0.02304999, -0.19184929, -0.023920752, 0.03141028, 0.04705922, 0.01520321, 0.049698327, -0.03312782, -0.013384789, -0.044349547, -0.34517092, -0.027608141, -0.0103994245, 0.010333201, -0.015563855, -0.012183729, 0.015444405, -0.029782547, -0.038746007], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, 21, 23, 25, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [109.59985, 12.379036, 30.657143, 7.2064095, 3.6693664, 7.7084465, 0.0, 1.8083, 3.1465037, 0.0, 2.9456286, 5.2522354, 8.68758, 2.2037334, 0.59983444, 0.0, 0.0, 1.4060407, 3.888657, 0.0, 4.806285, 0.0, 0.0, 0.0, 0.0, 0.0, 0.39042664, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 20, 20, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, 22, 24, 26, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.59381, 0.39416724, 1.321354, 0.33089325, 0.3713269, 0.8318429, 0.0783711, 1.2186605, 0.297281, 0.011266381, 0.52038383, 0.5791541, 0.8692273, 0.19908041, 1.2492511, 0.000453214, -0.02304999, 1.0, 1.0, 0.03141028, 0.71012646, 0.01520321, 0.049698327, -0.03312782, -0.013384789, -0.044349547, 1.0, -0.027608141, -0.0103994245, 0.010333201, -0.015563855, -0.012183729, 0.015444405, -0.029782547, -0.038746007], "split_indices": [139, 142, 139, 140, 141, 139, 0, 138, 142, 0, 143, 141, 141, 142, 138, 0, 0, 93, 122, 0, 142, 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1281.0, 773.0, 758.0, 523.0, 661.0, 112.0, 530.0, 228.0, 101.0, 422.0, 367.0, 294.0, 244.0, 286.0, 117.0, 111.0, 190.0, 232.0, 102.0, 265.0, 135.0, 159.0, 155.0, 89.0, 91.0, 195.0, 97.0, 93.0, 118.0, 114.0, 103.0, 162.0, 92.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023392937, -0.16151482, 0.26064637, -0.2018941, 0.013684541, 0.157824, 0.5810192, -0.25700638, -0.113769025, 0.012009175, -0.0077989334, 0.11098716, 0.039391987, 0.07480166, 0.042424608, -0.038238876, -0.2295642, -0.051756743, -0.025543932, 0.032038443, 0.05717258, -0.12120435, -0.28815413, -0.018718963, 0.003279093, -0.008463993, 0.14952949, 0.0034569711, -0.028736338, -0.02279488, -0.33638138, 0.03140244, 0.0028259486, -0.038094334, -0.028531035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, -1, -1, -1, -1, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [86.819466, 9.140156, 25.76025, 5.099613, 2.3606522, 6.5463247, 4.974327, 2.2227325, 3.5492592, 0.0, 0.0, 5.5667005, 0.0, 0.0, 0.0, 0.0, 3.3648567, 3.217601, 0.0, 0.0, 5.1472645, 4.814288, 0.99881554, 0.0, 0.0, 0.0, 4.747695, 0.0, 0.0, 0.0, 0.43468285, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 16, 16, 17, 17, 20, 20, 21, 21, 22, 22, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, -1, -1, -1, -1, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [0.59381, 0.5292678, 1.1347722, 0.34024993, 1.327816, 1.019717, 1.1510894, 0.13875106, 0.5052178, 0.012009175, -0.0077989334, 0.6552192, 0.039391987, 0.07480166, 0.042424608, -0.038238876, 1.2186605, 1.0, -0.025543932, 0.032038443, 1.0, 0.213057, 1.0, -0.018718963, 0.003279093, -0.008463993, 0.68592155, 0.0034569711, -0.028736338, -0.02279488, 0.285204, 0.03140244, 0.0028259486, -0.038094334, -0.028531035], "split_indices": [139, 142, 139, 140, 138, 141, 143, 142, 140, 0, 0, 139, 0, 0, 0, 0, 138, 59, 0, 0, 39, 143, 124, 0, 0, 0, 141, 0, 0, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1292.0, 782.0, 1050.0, 242.0, 592.0, 190.0, 646.0, 404.0, 112.0, 130.0, 494.0, 98.0, 92.0, 98.0, 116.0, 530.0, 281.0, 123.0, 101.0, 393.0, 186.0, 344.0, 108.0, 173.0, 155.0, 238.0, 96.0, 90.0, 153.0, 191.0, 101.0, 137.0, 102.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018615106, -0.10516521, 0.31539926, -0.1894801, 0.02496397, 0.17834441, 0.4979285, -0.21732779, -0.09345806, 0.055678118, -0.01398046, 0.029884553, 0.006512186, 0.03490147, 0.070411675, -0.23678759, -0.007873079, 0.0036371972, -0.018923433, -0.0061488403, 0.14194831, -0.28399333, -0.14058627, 0.0525167, -0.0138784, 0.03068815, 0.0016220537, -0.019369822, -0.30925447, -0.0012671611, -0.023390105, -0.0066045434, 0.014839741, -0.024286596, -0.34472227, -0.038679738, -0.028326422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, -1, -1, -1, 23, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [67.51487, 17.050219, 12.658356, 2.521576, 3.092104, 3.942954, 6.6628265, 1.9715576, 2.6361434, 2.74692, 0.0, 0.0, 0.0, 0.0, 0.0, 2.910946, 0.0, 0.0, 0.0, 2.3343344, 4.4583845, 0.98080826, 2.5185642, 2.3645072, 0.0, 0.0, 0.0, 0.0, 0.7911606, 0.0, 0.0, 0.0, 0.0, 0.0, 0.56629944, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 28, 28, 34, 34], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, -1, -1, -1, 24, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [0.76372665, 0.44064516, 0.9782575, 1.0, 2.0, 1.0, 1.0, 1.0, 1.2624887, 0.59381, -0.01398046, 0.029884553, 0.006512186, 0.03490147, 0.070411675, 1.0, -0.007873079, 0.0036371972, -0.018923433, 0.5431552, 0.66016495, 1.1783442, 1.2280427, 0.48169166, -0.0138784, 0.03068815, 0.0016220537, -0.019369822, 0.07692308, -0.0012671611, -0.023390105, -0.0066045434, 0.014839741, -0.024286596, 0.26305106, -0.038679738, -0.028326422], "split_indices": [139, 139, 141, 71, 0, 124, 0, 42, 138, 139, 0, 0, 0, 0, 0, 74, 0, 0, 0, 139, 139, 138, 138, 139, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1554.0, 506.0, 943.0, 611.0, 289.0, 217.0, 731.0, 212.0, 515.0, 96.0, 140.0, 149.0, 126.0, 91.0, 641.0, 90.0, 90.0, 122.0, 300.0, 215.0, 430.0, 211.0, 208.0, 92.0, 93.0, 122.0, 94.0, 336.0, 89.0, 122.0, 93.0, 115.0, 117.0, 219.0, 130.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0005603522, -0.086341254, 0.34628668, -0.17018138, 0.02975437, 0.19252284, 0.5030362, -0.035307597, -0.14870189, -0.09733034, 0.07293374, 0.03560762, 0.005741349, 0.07130729, 0.02929996, -0.26069018, -0.116729155, -0.0038788114, -0.015587254, 0.02528905, 0.01867541, -0.019965898, -0.031622853, -0.15506096, 0.0015898889, -0.012393444, 0.08473733, -0.002073121, -0.21992303, -0.0037286833, 0.021040402, -0.0304643, -0.15100573, -0.023282748, -0.0076475004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, -1, 25, -1, -1, 27, -1, -1, 29, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [62.22148, 16.108896, 10.0266075, 3.7752686, 3.808282, 4.6404924, 9.087765, 0.0, 3.0792904, 0.60318506, 5.0578303, 0.0, 0.0, 0.0, 0.0, 0.64740753, 3.4011116, 0.0, 0.0, 0.0, 3.7495897, 0.0, 0.0, 4.5219984, 0.0, 0.0, 4.1709504, 0.0, 2.0435333, 0.0, 0.0, 0.0, 1.1769581, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 20, 20, 23, 23, 26, 26, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, -1, 26, -1, -1, 28, -1, -1, 30, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [0.8318429, 0.44064516, 0.9782575, 0.0999041, 0.44131443, 1.0, 1.1330824, -0.035307597, 1.0, 1.3740345, 0.5077646, 0.03560762, 0.005741349, 0.07130729, 0.02929996, 1.0, 1.0, -0.0038788114, -0.015587254, 0.02528905, 0.6249582, -0.019965898, -0.031622853, 1.2146524, 0.0015898889, -0.012393444, 1.0, -0.002073121, 0.285204, -0.0037286833, 0.021040402, -0.0304643, 1.0, -0.023282748, -0.0076475004], "split_indices": [139, 139, 141, 140, 140, 124, 143, 0, 26, 138, 141, 0, 0, 0, 0, 97, 71, 0, 0, 0, 142, 0, 0, 138, 0, 0, 111, 0, 139, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1655.0, 416.0, 961.0, 694.0, 210.0, 206.0, 101.0, 860.0, 176.0, 518.0, 95.0, 115.0, 103.0, 103.0, 191.0, 669.0, 88.0, 88.0, 120.0, 398.0, 91.0, 100.0, 519.0, 150.0, 126.0, 272.0, 169.0, 350.0, 138.0, 134.0, 157.0, 193.0, 92.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0010738762, -0.058181614, 0.41043434, -0.16294682, 0.044293635, 0.024789011, 0.051742543, -0.23154397, -0.11548638, 0.004744215, 0.20357782, -0.27845928, -0.01662305, 0.011269396, -0.16101898, -0.028352838, 0.017214421, 0.008701413, 0.031270128, -0.021307118, -0.036782295, -0.19939774, -0.003346602, -0.018723033, 0.00047820836, -0.2696423, -0.008924145, 0.10297407, -0.0839726, -0.020730412, -0.032678563, -0.0043586954, 0.032125648, -0.0016880503, -0.024511313, -0.014160344, 0.012143746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, -1, -1, 25, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [50.2602, 19.43187, 4.556389, 2.913807, 5.7641306, 0.0, 0.0, 1.1214962, 5.496121, 4.0611477, 2.3150082, 1.244627, 0.0, 0.0, 2.1588383, 2.8033304, 0.0, 0.0, 0.0, 0.0, 0.0, 2.6231403, 0.0, 0.0, 4.483735, 0.73737717, 0.0, 7.486058, 3.765662, 0.0, 0.0, 0.0, 0.0, 3.238705, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 21, 21, 24, 24, 25, 25, 27, 27, 28, 28, 33, 33], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, -1, -1, 26, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.0084468, 0.40912068, 0.9782575, 0.26347196, 1.0, 0.024789011, 0.051742543, 0.20312513, 0.20498092, 1.2692307, 1.0, 0.14746852, -0.01662305, 0.011269396, 0.50866467, 0.40314692, 0.017214421, 0.008701413, 0.031270128, -0.021307118, -0.036782295, 0.33089325, -0.003346602, -0.018723033, 0.6656422, 1.0, -0.008924145, 0.55698216, -0.03846154, -0.020730412, -0.032678563, -0.0043586954, 0.032125648, 1.0, -0.024511313, -0.014160344, 0.012143746], "split_indices": [139, 140, 141, 141, 42, 0, 0, 143, 143, 1, 53, 139, 0, 0, 142, 142, 0, 0, 0, 0, 0, 140, 0, 0, 143, 97, 0, 143, 1, 0, 0, 0, 0, 53, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1810.0, 262.0, 895.0, 915.0, 104.0, 158.0, 366.0, 529.0, 733.0, 182.0, 213.0, 153.0, 88.0, 441.0, 612.0, 121.0, 88.0, 94.0, 123.0, 90.0, 339.0, 102.0, 94.0, 518.0, 207.0, 132.0, 234.0, 284.0, 99.0, 108.0, 140.0, 94.0, 188.0, 96.0, 88.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.004671584, -0.08112008, 0.22964983, -0.13155362, 0.027742555, 0.10661604, 0.39144766, -0.19584905, -0.09660232, 0.021110538, -0.034042735, 0.0017724648, 0.022930084, 0.02236073, 0.06146396, -0.010456172, -0.23537189, 0.0061444896, -0.120388694, -0.13464284, 0.00981926, -0.027205637, -0.017818725, -0.20545943, -0.074345246, -0.0015581967, -0.02397596, -0.029314449, -0.011261638, -0.18079364, 0.01495031, -0.013894023, -0.022312263, -0.012500976, 0.011905286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, -1, -1, -1, 21, -1, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [36.91976, 8.531969, 10.092646, 2.3865356, 5.5739303, 3.1408186, 8.203869, 1.3493671, 2.5864472, 0.0, 4.8954625, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5475216, 0.0, 2.3423357, 2.6156957, 0.0, 0.0, 0.0, 1.7095976, 3.688084, 0.0, 0.0, 0.0, 0.0, 0.3135743, 3.0743122, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 16, 16, 18, 18, 19, 19, 23, 23, 24, 24, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, -1, -1, -1, 22, -1, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.76372665, 0.48685256, 0.9782575, 0.2243912, 0.4672373, 1.0, 1.0, 1.0, 0.26126823, 0.021110538, 0.676498, 0.0017724648, 0.022930084, 0.02236073, 0.06146396, -0.010456172, 0.46153846, 0.0061444896, 1.0, 1.0, 0.00981926, -0.027205637, -0.017818725, 1.0, 1.0, -0.0015581967, -0.02397596, -0.029314449, -0.011261638, 1.0, 0.40114385, -0.013894023, -0.022312263, -0.012500976, 0.011905286], "split_indices": [139, 139, 141, 142, 141, 12, 0, 53, 142, 0, 140, 0, 0, 0, 0, 0, 1, 0, 53, 15, 0, 0, 0, 23, 17, 0, 0, 0, 0, 71, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1554.0, 507.0, 1062.0, 492.0, 288.0, 219.0, 374.0, 688.0, 124.0, 368.0, 167.0, 121.0, 125.0, 94.0, 113.0, 261.0, 90.0, 598.0, 209.0, 159.0, 159.0, 102.0, 210.0, 388.0, 98.0, 111.0, 108.0, 102.0, 177.0, 211.0, 89.0, 88.0, 90.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004742711, -0.06014258, 0.26602048, -0.117978536, 0.041022297, -0.00063507864, 0.34347606, -0.02595585, -0.10328627, 0.008260359, 0.022986354, 0.05948311, 0.24813446, 0.00487176, -0.1249491, 0.050065964, -0.013805923, 0.008845865, 0.04314659, -0.02519604, -0.105476364, -0.030320995, 0.13586761, -0.038098946, -0.1515437, 0.0065751313, -0.014040388, 0.0008442939, 0.028169801, -0.017879484, 0.023972759, -0.1850416, -0.0043824944, 0.013202776, -0.0070203594, -0.1452654, -0.028863458, -0.023609934, -0.00886497], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, 19, 21, -1, -1, -1, -1, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, 37, -1, -1, -1], "loss_changes": [35.02507, 9.683351, 8.670736, 2.1903782, 3.7244558, 0.0, 7.668663, 0.0, 3.141364, 3.13801, 0.0, 0.0, 6.7914753, 0.0, 2.0651693, 2.752036, 0.0, 0.0, 0.0, 0.0, 2.247221, 2.1786394, 3.5864007, 2.5675714, 1.5515909, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0759504, 1.3515339, 0.0, 0.0, 0.0, 1.218802, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 12, 12, 14, 14, 15, 15, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 30, 30, 31, 31, 35, 35], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, 20, 22, -1, -1, -1, -1, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, 38, -1, -1, -1], "split_conditions": [0.8318429, 0.48169166, 0.70016557, 0.0999041, 0.96153843, -0.00063507864, 0.9223348, -0.02595585, 1.1763812, 0.1923077, 0.022986354, 0.05948311, 1.1962737, 0.00487176, 0.18286213, 1.0, -0.013805923, 0.008845865, 0.04314659, -0.02519604, 1.2624887, 1.0, -0.1923077, 0.2243912, 0.48389733, 0.0065751313, -0.014040388, 0.0008442939, 0.028169801, -0.017879484, 1.0, 0.40145585, -0.0043824944, 0.013202776, -0.0070203594, 0.29649943, -0.028863458, -0.023609934, -0.00886497], "split_indices": [139, 139, 143, 140, 1, 0, 143, 0, 138, 1, 0, 0, 140, 0, 139, 111, 0, 0, 0, 0, 138, 108, 1, 142, 141, 0, 0, 0, 0, 0, 111, 141, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1655.0, 411.0, 1053.0, 602.0, 91.0, 320.0, 99.0, 954.0, 513.0, 89.0, 88.0, 232.0, 119.0, 835.0, 399.0, 114.0, 124.0, 108.0, 111.0, 724.0, 206.0, 193.0, 294.0, 430.0, 110.0, 96.0, 103.0, 90.0, 90.0, 204.0, 328.0, 102.0, 95.0, 109.0, 237.0, 91.0, 91.0, 146.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019838565, -0.044888236, 0.30809003, -0.12607154, 0.03348048, 0.0136891445, 0.041592285, -0.1837013, -0.08589537, -0.000708699, 0.1698676, -0.14867975, -0.026013961, 0.010308674, -0.123951264, -0.04346092, 0.014546804, 0.0017105177, 0.033464503, -0.020467786, -0.004495598, -0.021247258, -0.075780645, -0.024910586, -0.0013774568, -0.01516504, 0.00400562, -0.1143364, 0.111581475, -0.024864083, 0.004072415, -2.124639e-05, 0.024974193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, -1, -1, -1, 25, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [27.471785, 11.54109, 4.633667, 2.0629702, 4.303916, 0.0, 0.0, 0.97977734, 3.775739, 4.612043, 4.6567817, 1.4578905, 0.0, 0.0, 1.8634224, 4.941577, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4871497, 0.0, 6.048108, 0.0, 0.0, 4.9356017, 3.660585, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 22, 22, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, -1, -1, -1, 26, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0194759, 0.40912068, 1.0268755, 0.26347196, 1.0, 0.0136891445, 0.041592285, 0.21959372, 0.20498092, 0.65384614, 0.7065677, 0.19730112, -0.026013961, 0.010308674, 1.0, 1.0, 0.014546804, 0.0017105177, 0.033464503, -0.020467786, -0.004495598, -0.021247258, 0.33089325, -0.024910586, 1.0, -0.01516504, 0.00400562, 0.6846834, 1.0, -0.024864083, 0.004072415, -2.124639e-05, 0.024974193], "split_indices": [139, 140, 142, 141, 42, 0, 0, 141, 143, 1, 142, 143, 0, 0, 59, 2, 0, 0, 0, 0, 0, 0, 140, 0, 111, 0, 0, 140, 12, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1814.0, 251.0, 891.0, 923.0, 97.0, 154.0, 366.0, 525.0, 738.0, 185.0, 251.0, 115.0, 88.0, 437.0, 571.0, 167.0, 96.0, 89.0, 163.0, 88.0, 154.0, 283.0, 97.0, 474.0, 171.0, 112.0, 237.0, 237.0, 127.0, 110.0, 131.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003795112, -0.04209267, 0.2625202, -0.10754299, 0.031021625, 0.037192598, 0.009841148, -0.19839822, -0.079664126, -0.002372876, 0.15262118, -0.023298953, -0.01449389, 0.0076513137, -0.10388149, 0.02895614, -0.0204271, 0.0029676184, 0.027292216, -0.1493368, -0.007820751, 0.06101947, -0.013269643, -0.114158295, -0.028184244, 0.013228533, -0.014655328, 0.13870396, -0.031428583, -0.18511999, 0.008824376, 0.029933235, 0.0057930816, -0.014765777, 0.006462857, -0.01013363, -0.025048314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.092, 8.65192, 4.668154, 2.4164248, 3.4678864, 0.0, 0.0, 0.41422558, 2.7610073, 4.2379317, 2.7214336, 0.0, 0.0, 0.0, 2.7596092, 3.0062094, 0.0, 0.0, 0.0, 1.9997177, 3.945766, 3.4759824, 0.0, 4.8689866, 0.0, 0.0, 0.0, 3.412281, 2.467387, 1.3745661, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 23, 23, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0084468, 0.44064516, 1.1510894, 1.0, 1.0, 0.037192598, 0.009841148, 0.9230769, 1.1763812, 3.0, 1.0, -0.023298953, -0.01449389, 0.0076513137, 0.39416724, 0.95005274, -0.0204271, 0.0029676184, 0.027292216, 1.2926444, 0.4564035, 1.0, -0.013269643, 0.29541504, -0.028184244, 0.013228533, -0.014655328, -0.23076923, 0.56537426, 1.0, 0.008824376, 0.029933235, 0.0057930816, -0.014765777, 0.006462857, -0.01013363, -0.025048314], "split_indices": [139, 139, 143, 26, 42, 0, 0, 1, 138, 0, 53, 0, 0, 0, 142, 143, 0, 0, 0, 138, 143, 122, 0, 139, 0, 0, 0, 1, 142, 126, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1808.0, 260.0, 954.0, 854.0, 156.0, 104.0, 224.0, 730.0, 670.0, 184.0, 136.0, 88.0, 98.0, 632.0, 580.0, 90.0, 91.0, 93.0, 429.0, 203.0, 484.0, 96.0, 339.0, 90.0, 101.0, 102.0, 263.0, 221.0, 251.0, 88.0, 88.0, 175.0, 100.0, 121.0, 110.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.003719244, -0.039209522, 0.25119108, -0.09401574, 0.03668156, 0.010988974, 0.033525642, -0.059421577, -0.13811955, 0.010953397, 0.023036543, -0.030795148, -0.021608045, -0.10269913, -0.024377009, -0.03629734, 0.016280955, 0.044221304, -0.1161143, -0.15872233, -0.0003650096, -0.11528261, 0.04812415, -0.005470459, 0.020485347, -0.019990543, 0.0010022852, -0.01890241, -0.011926687, 0.0045267963, -0.02011585, -0.004180833, 0.015831172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, 17, -1, 19, -1, 21, -1, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.654583, 7.5283384, 2.993391, 1.6035461, 3.7821953, 0.0, 0.0, 2.6414192, 1.7288895, 4.8074613, 0.0, 3.1873696, 0.0, 1.9199693, 0.0, 3.407376, 0.0, 4.211031, 2.4626186, 0.26421976, 0.0, 3.6398807, 2.4476323, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, 18, -1, 20, -1, 22, -1, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0194759, 0.48169166, 1.0268755, 1.0, 1.0769231, 0.010988974, 0.033525642, 2.0, 0.35518932, 1.0, 0.023036543, 0.35939938, -0.021608045, 0.25490266, -0.024377009, 1.0, 0.016280955, 0.30221263, 0.47274625, 0.14816526, -0.0003650096, -0.34615386, 1.0, -0.005470459, 0.020485347, -0.019990543, 0.0010022852, -0.01890241, -0.011926687, 0.0045267963, -0.02011585, -0.004180833, 0.015831172], "split_indices": [139, 139, 142, 23, 1, 0, 0, 0, 143, 42, 0, 141, 0, 142, 0, 111, 0, 140, 141, 143, 0, 1, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1810.0, 252.0, 1051.0, 759.0, 94.0, 158.0, 589.0, 462.0, 670.0, 89.0, 498.0, 91.0, 346.0, 116.0, 511.0, 159.0, 265.0, 233.0, 221.0, 125.0, 264.0, 247.0, 164.0, 101.0, 140.0, 93.0, 125.0, 96.0, 92.0, 172.0, 136.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014028786, -0.0336352, 0.24020633, -0.08369517, 0.022513935, 0.03666583, 0.006664477, -0.10104253, 0.0023107573, 0.019517822, -0.0024495716, -0.08044635, -0.019927049, 0.027128853, -0.013775985, -0.12864456, -0.028732145, 0.061510306, -0.0120177865, -0.16118084, -0.0064665615, 0.058199573, -0.013636191, 0.023280902, 0.019292386, -0.01040809, -0.025090927, 0.0153134065, -0.003673489, 0.09420449, -0.021608617, -0.0066161337, 0.02060024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, 13, 15, -1, 17, -1, 19, 21, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [16.011358, 5.098835, 5.311221, 1.776783, 3.6853118, 0.0, 0.0, 1.669074, 0.0, 0.0, 2.9896924, 1.6999059, 0.0, 3.1046112, 0.0, 0.734817, 3.0782695, 2.496859, 0.0, 1.1988955, 0.0, 1.6402855, 0.0, 6.536057, 0.0, 0.0, 0.0, 0.0, 0.0, 5.3247843, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 10, 10, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, 14, 16, -1, 18, -1, 20, 22, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0194759, 0.44131443, 1.1510894, 0.50866467, 0.48535565, 0.03666583, 0.006664477, 1.3089205, 0.0023107573, 0.019517822, 1.0, 0.2243912, -0.019927049, 1.0, -0.013775985, 0.25195393, 0.3220392, 1.0, -0.0120177865, 0.16041009, -0.0064665615, 0.29402548, -0.013636191, 0.8186932, 0.019292386, -0.01040809, -0.025090927, 0.0153134065, -0.003673489, 0.61407435, -0.021608617, -0.0066161337, 0.02060024], "split_indices": [139, 140, 143, 142, 140, 0, 0, 138, 0, 0, 137, 142, 0, 113, 0, 141, 143, 42, 0, 142, 0, 142, 0, 142, 0, 0, 0, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1814.0, 242.0, 959.0, 855.0, 140.0, 102.0, 825.0, 134.0, 108.0, 747.0, 682.0, 143.0, 613.0, 134.0, 353.0, 329.0, 497.0, 116.0, 234.0, 119.0, 182.0, 147.0, 385.0, 112.0, 143.0, 91.0, 91.0, 91.0, 297.0, 88.0, 122.0, 175.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008554667, -0.05168154, 0.15251444, -0.0937741, 0.0031891104, 0.0019655535, 0.22011615, -0.1055295, -0.0002552156, 0.08953078, -0.040562492, 0.011028617, 0.032367013, -0.12727925, -0.0025605685, -0.0061405003, 0.020718941, -0.087155975, 0.009674395, -0.05266236, -0.15876096, -0.017209066, 0.0002497272, -0.01767523, 0.0064756046, -0.20031933, -0.009160871, -0.024880705, -0.010914129], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, 25, -1, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [16.065886, 3.5753326, 4.607505, 0.93937874, 2.5385382, 0.0, 3.86693, 1.348939, 0.0, 4.013511, 2.8533235, 0.0, 0.0, 1.432931, 0.0, 0.0, 0.0, 2.5356855, 0.0, 2.637251, 1.1972256, 0.0, 0.0, 0.0, 0.0, 1.1715679, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 17, 17, 19, 19, 20, 20, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, 26, -1, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [0.7601444, 0.40912068, 0.8025691, 0.50866467, 0.5074632, 0.0019655535, 1.0, 0.33089325, -0.0002552156, 1.0, 0.78585535, 0.011028617, 0.032367013, 0.14673588, -0.0025605685, -0.0061405003, 0.020718941, 0.58748406, 0.009674395, 0.09001305, 1.0, -0.017209066, 0.0002497272, -0.01767523, 0.0064756046, 1.0, -0.009160871, -0.024880705, -0.010914129], "split_indices": [139, 140, 142, 142, 140, 0, 113, 140, 0, 124, 140, 0, 0, 140, 0, 0, 0, 142, 0, 140, 109, 0, 0, 0, 0, 80, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1548.0, 513.0, 876.0, 672.0, 173.0, 340.0, 776.0, 100.0, 226.0, 446.0, 165.0, 175.0, 610.0, 166.0, 99.0, 127.0, 333.0, 113.0, 181.0, 429.0, 171.0, 162.0, 88.0, 93.0, 265.0, 164.0, 173.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026767773, -0.04445982, 0.124304496, -0.02339525, -0.16906844, -0.0020546147, 0.18153432, -0.0599077, 0.05607815, -0.006136635, -0.024635084, 0.25160936, 0.0011258498, -0.031996727, -0.112805635, 0.018949458, -0.0026067346, 0.012365107, 0.040089402, -0.07393139, 0.007872097, -0.070455655, -0.021868054, -0.014253514, 0.009558862, -0.032657463, -0.019813528, 0.0011795687, -0.013392522, -0.10607408, 0.0063148597, -0.0035737527, -0.01811524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, 17, -1, 19, 21, -1, 23, -1, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [10.972114, 4.084231, 4.2443666, 3.8622537, 1.8727818, 0.0, 4.379076, 1.3465066, 3.2805731, 0.0, 0.0, 4.966572, 0.0, 2.771817, 1.4123964, 0.0, 3.9984326, 0.0, 0.0, 2.2197227, 0.0, 1.1746033, 0.0, 0.0, 0.0, 2.285971, 0.0, 0.0, 0.0, 0.97165704, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 16, 16, 19, 19, 21, 21, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, 18, -1, 20, 22, -1, 24, -1, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [0.76372665, 2.0, 1.0, 0.48169166, 1.0, -0.0020546147, 1.1510894, 0.7307692, 0.48037243, -0.006136635, -0.024635084, 0.793871, 0.0011258498, 1.0, 0.3713269, 0.018949458, 0.6249582, 0.012365107, 0.040089402, 0.36886653, 0.007872097, 1.2186605, -0.021868054, -0.014253514, 0.009558862, 1.0, -0.019813528, 0.0011795687, -0.013392522, 1.0, 0.0063148597, -0.0035737527, -0.01811524], "split_indices": [139, 0, 89, 139, 17, 0, 143, 1, 141, 0, 0, 143, 0, 71, 141, 0, 142, 0, 0, 141, 0, 138, 0, 0, 0, 83, 0, 0, 0, 13, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1556.0, 512.0, 1331.0, 225.0, 145.0, 367.0, 912.0, 419.0, 94.0, 131.0, 260.0, 107.0, 597.0, 315.0, 128.0, 291.0, 140.0, 120.0, 433.0, 164.0, 225.0, 90.0, 120.0, 171.0, 325.0, 108.0, 98.0, 127.0, 184.0, 141.0, 95.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.005150289, -0.017039858, 0.22333404, -0.032334507, 0.1299443, 0.038334984, 0.0074632475, 0.0097133815, -0.06356885, 0.002958772, 0.022917323, -0.05011355, 0.11053285, -0.038120396, -0.020824159, -0.006488036, -0.019199837, 0.03284843, -0.0034768083, -0.10230034, 0.042847756, 0.093039185, -0.01318665, -0.0012406502, -0.124592066, 0.11313861, -0.008378307, 0.019501759, 0.00066003174, -0.09551876, -0.01963243, -0.0036834457, 0.020895476, -0.018112738, -0.004895968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, 15, 17, 19, -1, 21, -1, -1, -1, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [10.017126, 4.221877, 4.5447674, 2.2339888, 1.7626147, 0.0, 0.0, 4.372997, 3.5933363, 0.0, 0.0, 2.8163576, 8.550508, 4.3131213, 0.0, 4.342543, 0.0, 0.0, 0.0, 0.9277997, 3.266663, 1.7100899, 0.0, 0.0, 0.7737174, 3.3912833, 0.0, 0.0, 0.0, 1.0522659, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 20, 20, 21, 21, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, 16, 18, 20, -1, 22, -1, -1, -1, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.1347722, 0.95011306, 1.1510894, -0.03846154, -0.115384616, 0.038334984, 0.0074632475, 0.6023556, 0.67471886, 0.002958772, 0.022917323, 1.0, 0.64812756, 0.32807782, -0.020824159, 0.3602427, -0.019199837, 0.03284843, -0.0034768083, 0.09691013, 0.5288287, 0.25637653, -0.01318665, -0.0012406502, 1.1923077, 0.32620507, -0.008378307, 0.019501759, 0.00066003174, 0.17174886, -0.01963243, -0.0036834457, 0.020895476, -0.018112738, -0.004895968], "split_indices": [139, 140, 143, 1, 1, 0, 0, 143, 143, 0, 0, 0, 141, 140, 0, 143, 0, 0, 0, 139, 140, 139, 0, 0, 1, 139, 0, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1878.0, 191.0, 1701.0, 177.0, 92.0, 99.0, 725.0, 976.0, 88.0, 89.0, 455.0, 270.0, 830.0, 146.0, 348.0, 107.0, 108.0, 162.0, 463.0, 367.0, 194.0, 154.0, 92.0, 371.0, 236.0, 131.0, 89.0, 105.0, 264.0, 107.0, 92.0, 144.0, 93.0, 171.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.003824721, -0.016870312, 0.20881449, -0.06436, 0.025918368, 0.033190694, 0.009079798, -0.049936406, -0.017131226, 0.04587676, -0.016889285, -0.07678848, 0.042452913, 0.020697644, 0.026916845, -0.055543773, -0.01931823, 0.010913538, -0.002348027, 0.04859654, -0.06627287, -0.08888303, 0.00828141, -0.02891777, 0.12001696, -0.015448714, 0.0005605398, -0.015539301, -0.049639575, 0.019162023, -0.12513289, -0.003437274, 0.22677581, 0.0030866384, -0.0094954325, -0.023704471, -0.004023425, 0.0074576437, 0.037897516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, -1, -1, -1, 23, 25, 27, -1, 29, 31, -1, -1, -1, 33, -1, 35, -1, 37, -1, -1, -1, -1, -1, -1], "loss_changes": [8.789984, 3.8242636, 2.7601185, 1.3760304, 3.849237, 0.0, 0.0, 1.9499441, 0.0, 5.048813, 0.0, 1.5059056, 0.7781962, 1.9580896, 0.0, 2.3755653, 0.0, 0.0, 0.0, 3.38256, 1.2427754, 1.083183, 0.0, 6.217194, 5.241426, 0.0, 0.0, 0.0, 0.95215625, 0.0, 1.9382362, 0.0, 4.354952, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 20, 20, 21, 21, 23, 23, 24, 24, 28, 28, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, -1, -1, -1, 24, 26, 28, -1, 30, 32, -1, -1, -1, 34, -1, 36, -1, 38, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1347722, 0.40912068, 1.1510894, 0.3714244, 1.5459449, 0.033190694, 0.009079798, 0.39416724, -0.017131226, 0.870337, -0.016889285, 1.2964996, 0.29029372, 0.74174595, 0.026916845, 0.30221263, -0.01931823, 0.010913538, -0.002348027, 1.0, 0.793871, 1.0, 0.00828141, 0.4762024, 1.0, -0.015448714, 0.0005605398, -0.015539301, 0.13467944, 0.019162023, 0.47112516, -0.003437274, 1.0, 0.0030866384, -0.0094954325, -0.023704471, -0.004023425, 0.0074576437, 0.037897516], "split_indices": [139, 140, 143, 140, 138, 0, 0, 142, 0, 139, 0, 138, 140, 141, 0, 140, 0, 0, 0, 71, 143, 69, 0, 140, 93, 0, 0, 0, 139, 0, 139, 0, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1882.0, 190.0, 892.0, 990.0, 93.0, 97.0, 786.0, 106.0, 898.0, 92.0, 609.0, 177.0, 807.0, 91.0, 515.0, 94.0, 88.0, 89.0, 611.0, 196.0, 415.0, 100.0, 293.0, 318.0, 88.0, 108.0, 154.0, 261.0, 89.0, 204.0, 130.0, 188.0, 94.0, 167.0, 88.0, 116.0, 94.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.002264935, -0.012281503, 0.025448982, -0.024287201, 0.012910137, -0.01118479, -0.0174055, -0.043147083, 0.08516982, -0.029048286, -0.020869887, 0.025163619, 0.027861714, -0.04952015, 0.021247363, -0.01051085, 0.11176806, -0.03250673, -0.016010733, 0.02070091, 0.0013421314, -0.057804342, 0.011528455, -0.102203175, 0.00016445777, -0.0019472415, -0.015429291, -0.005442674, 0.015228378], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [7.536072, 3.2963507, 0.0, 3.5125513, 0.0, 5.069209, 0.0, 2.884923, 3.911347, 5.631675, 0.0, 0.0, 3.4028983, 1.9755387, 0.0, 0.0, 1.7515624, 3.4022772, 0.0, 0.0, 0.0, 1.9998019, 0.0, 1.8961458, 2.7985752, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.321354, 1.0, 0.025448982, 0.95005274, 0.012910137, 0.59734756, -0.0174055, 0.5431552, 0.5791541, 0.4963337, -0.020869887, 0.025163619, 0.71012646, 1.3288147, 0.021247363, -0.01051085, 1.492255, 0.44650272, -0.016010733, 0.02070091, 0.0013421314, 1.0, 0.011528455, 0.19738461, 0.3350134, -0.0019472415, -0.015429291, -0.005442674, 0.015228378], "split_indices": [139, 125, 0, 143, 0, 139, 0, 139, 141, 139, 0, 0, 142, 138, 0, 0, 138, 141, 0, 0, 0, 115, 0, 140, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1942.0, 112.0, 1790.0, 152.0, 1646.0, 144.0, 1236.0, 410.0, 1139.0, 97.0, 105.0, 305.0, 1050.0, 89.0, 118.0, 187.0, 910.0, 140.0, 95.0, 92.0, 777.0, 133.0, 440.0, 337.0, 170.0, 270.0, 248.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0058844993, -0.029281106, 0.09379852, 0.03265522, -0.050829083, 0.13952404, -0.003740087, -0.056420393, 0.019563062, -0.031105047, -0.01815008, 0.06795445, 0.03671835, 0.0040387064, -0.01109512, -0.06288433, 0.078785926, 0.19519848, -0.014753942, -0.0053514615, -0.11564764, 0.027189836, -0.008745868, 0.03776088, 0.0022922042, -0.09617219, 0.013184579, -0.056917537, -0.019431036, -0.004180875, -0.015533238, 0.0029310067, -0.0116157895], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, -1, -1, -1, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [6.405691, 1.975212, 3.5515037, 5.5455456, 2.8299556, 7.1528444, 0.0, 1.3039105, 0.0, 3.331613, 0.0, 9.158383, 0.0, 0.0, 0.0, 2.2463617, 6.870236, 6.599248, 0.0, 4.410965, 1.7832685, 0.0, 0.0, 0.0, 0.0, 0.6850395, 0.0, 1.1289024, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, -1, -1, -1, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.6985336, 0.0, 1.1510894, 0.44427505, 0.57446164, 1.0268755, -0.003740087, 0.2208206, 0.019563062, 0.50866467, -0.01815008, 1.0, 0.03671835, 0.0040387064, -0.01109512, 1.2478638, 0.52127117, 1.0, -0.014753942, 0.23427236, 1.0, 0.027189836, -0.008745868, 0.03776088, 0.0022922042, 0.5769231, 0.013184579, 0.29999205, -0.019431036, -0.004180875, -0.015533238, 0.0029310067, -0.0116157895], "split_indices": [140, 0, 143, 140, 140, 142, 0, 143, 0, 142, 0, 50, 0, 0, 0, 138, 141, 69, 0, 142, 115, 0, 0, 0, 0, 1, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1480.0, 592.0, 382.0, 1098.0, 439.0, 153.0, 247.0, 135.0, 954.0, 144.0, 334.0, 105.0, 89.0, 158.0, 740.0, 214.0, 210.0, 124.0, 354.0, 386.0, 99.0, 115.0, 102.0, 108.0, 213.0, 141.0, 221.0, 165.0, 111.0, 102.0, 90.0, 131.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00186397, -0.015018838, 0.020215072, -0.0065092915, -0.017581379, -0.018506693, 0.020132443, -0.05798991, 0.022910587, -0.039931335, -0.12801704, 0.06948313, -0.07987018, -0.06631568, 0.006810964, -0.007710632, -0.01800718, 0.024260443, 0.0199081, -0.022457166, 0.0059391367, -0.045466155, -0.011230375, 0.014798993, -0.03869731, -0.013175984, -0.01768667, 0.009798559, -0.013226002, -0.009439374, 0.0050112465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, -1, -1, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [7.0028853, 2.6134372, 0.0, 4.5231433, 0.0, 2.804518, 0.0, 1.1103098, 4.0065193, 1.9897121, 0.47702503, 3.3757997, 5.259503, 0.53790283, 0.0, 0.0, 0.0, 3.3262155, 0.0, 0.0, 0.0, 0.92531633, 0.0, 0.0, 3.6191242, 0.0, 1.5185968, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 21, 21, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, -1, -1, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [1.2263254, 1.0517951, 0.020215072, 1.0, -0.017581379, 0.40912068, 0.020132443, 1.3145452, 0.68592155, 1.0, 0.43161404, 1.4224148, 0.7460131, 0.29999205, 0.006810964, -0.007710632, -0.01800718, 0.48535565, 0.0199081, -0.022457166, 0.0059391367, 0.13375174, -0.011230375, 0.014798993, 1.0, -0.013175984, 0.3846154, 0.009798559, -0.013226002, -0.009439374, 0.0050112465], "split_indices": [140, 141, 0, 125, 0, 140, 0, 138, 141, 62, 142, 138, 143, 142, 0, 0, 0, 140, 0, 0, 0, 142, 0, 0, 59, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1910.0, 161.0, 1814.0, 96.0, 1715.0, 99.0, 878.0, 837.0, 698.0, 180.0, 576.0, 261.0, 561.0, 137.0, 91.0, 89.0, 427.0, 149.0, 128.0, 133.0, 386.0, 175.0, 144.0, 283.0, 94.0, 292.0, 115.0, 168.0, 137.0, 155.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0006541434, -0.029518917, 0.07704099, 0.0376648, -0.05295368, 0.017171288, 0.17283249, -0.034220804, 0.01712433, -0.03280236, -0.018539645, -0.07498514, 0.0139055615, 0.036461044, 0.0049646646, 0.0070699616, -0.0092582805, -0.06740515, 0.041788407, 0.008464782, -0.020753754, -0.029325476, -0.14462715, 0.018434567, -0.01167193, -0.08719147, 0.018704299, -0.02251465, -0.008775236, -0.015759194, -0.04751119, 0.0018528051, -0.009323069], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, -1, -1, 21, 23, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [4.7617683, 2.3317373, 3.3549798, 3.6777089, 2.9304473, 4.043673, 5.3154726, 1.5247183, 0.0, 2.4597392, 0.0, 4.337745, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9143229, 6.8241205, 0.0, 0.0, 5.458884, 0.98459625, 0.0, 0.0, 0.9609668, 0.0, 0.0, 0.0, 0.0, 0.664242, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 17, 17, 18, 18, 21, 21, 22, 22, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, -1, -1, 22, 24, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [0.6985336, 0.0, -0.07692308, 0.44427505, 0.57446164, 1.0, 1.0, 0.21818505, 0.01712433, 0.43434185, -0.018539645, 0.9074606, 0.0139055615, 0.036461044, 0.0049646646, 0.0070699616, -0.0092582805, 0.29999205, 0.51408464, 0.008464782, -0.020753754, 0.33089325, 1.0, 0.018434567, -0.01167193, 1.0, 0.018704299, -0.02251465, -0.008775236, -0.015759194, 0.19490282, 0.0018528051, -0.009323069], "split_indices": [140, 0, 1, 140, 140, 61, 69, 143, 0, 142, 0, 140, 0, 0, 0, 0, 0, 142, 141, 0, 0, 140, 69, 0, 0, 69, 0, 0, 0, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1481.0, 585.0, 383.0, 1098.0, 360.0, 225.0, 249.0, 134.0, 953.0, 145.0, 205.0, 155.0, 88.0, 137.0, 89.0, 160.0, 651.0, 302.0, 93.0, 112.0, 436.0, 215.0, 159.0, 143.0, 344.0, 92.0, 89.0, 126.0, 124.0, 220.0, 90.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024558685, -0.012188282, 0.0189078, 0.0006735447, -0.11986016, -0.008754828, 0.015374598, -0.0045924773, -0.020118905, 0.0065908367, -0.09885491, -0.030352658, 0.06366721, -0.023734443, 0.0036229123, -0.005641732, -0.024149375, 0.114885904, -0.0077530253, -0.028454958, 0.016647136, 0.0410611, 0.036149215, 0.00018629599, -0.017345134, 0.015042528, -0.006420189, -0.025716932, 0.009566433, -0.013856182, 0.0002811261], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, -1, 23, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [3.8549314, 2.725399, 0.0, 2.537188, 1.2627468, 2.2896614, 0.0, 0.0, 0.0, 2.98367, 4.508562, 4.4818244, 4.0209646, 0.0, 0.0, 3.0194433, 0.0, 7.4279075, 0.0, 2.819805, 0.0, 3.614768, 0.0, 1.4022987, 0.0, 0.0, 0.0, 1.4357907, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, -1, 24, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.3934005, 1.0, 0.0189078, 1.0, 0.29713437, 1.0, 0.015374598, -0.0045924773, -0.020118905, 0.55698216, 1.0, 0.49346045, 0.95005274, -0.023734443, 0.0036229123, 0.41605026, -0.024149375, 0.874354, -0.0077530253, 0.3602427, 0.016647136, 1.0, 0.036149215, 1.0, -0.017345134, 0.015042528, -0.006420189, 1.0, 0.009566433, -0.013856182, 0.0002811261], "split_indices": [139, 40, 0, 102, 143, 64, 0, 0, 0, 143, 108, 143, 143, 0, 0, 143, 0, 140, 0, 143, 0, 106, 0, 74, 0, 0, 0, 69, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1968.0, 100.0, 1758.0, 210.0, 1656.0, 102.0, 110.0, 100.0, 1415.0, 241.0, 859.0, 556.0, 119.0, 122.0, 769.0, 90.0, 408.0, 148.0, 679.0, 90.0, 314.0, 94.0, 567.0, 112.0, 154.0, 160.0, 446.0, 121.0, 90.0, 356.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0023054047, -0.046132345, 0.043681126, -0.114104055, -0.024815217, 0.024548318, 0.023425883, -0.0071581774, -0.016391585, -0.051834285, 0.0073279478, 0.054846298, -0.018946119, -0.02656816, -0.017058505, -0.075452514, 0.09609474, -0.06250381, 0.010308198, 0.0068414086, -0.019472657, 0.021864545, 0.05871441, -0.025817592, -0.012508615, 0.0017461799, 0.021252861, 0.0055022854, -0.009501704, -0.1069095, 0.015303892, 0.003944584, -0.020447975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [4.1545963, 1.3837581, 4.0765476, 0.48292875, 1.9268608, 6.5878, 0.0, 0.0, 0.0, 1.7102115, 0.0, 4.783415, 0.0, 2.1897588, 0.0, 3.6721444, 3.0967455, 0.8448942, 0.0, 0.0, 0.0, 0.0, 4.5389857, 1.2978348, 0.0, 6.2138724, 0.0, 0.0, 0.0, 3.1415844, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 22, 22, 23, 23, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [0.44064516, 1.0, 1.2307693, 1.0, 1.0, 0.46153846, 0.023425883, -0.0071581774, -0.016391585, 0.42850766, 0.0073279478, 0.53773886, -0.018946119, 0.31486842, -0.017058505, 1.3765976, 0.65298104, 0.61538464, 0.010308198, 0.0068414086, -0.019472657, 0.021864545, -0.07692308, 1.2106528, -0.012508615, 1.0, 0.021252861, 0.0055022854, -0.009501704, 1.0, 0.015303892, 0.003944584, -0.020447975], "split_indices": [139, 26, 1, 12, 71, 1, 0, 0, 0, 140, 0, 143, 0, 142, 0, 138, 141, 1, 0, 0, 0, 0, 1, 138, 0, 61, 0, 0, 0, 13, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 955.0, 1118.0, 228.0, 727.0, 1016.0, 102.0, 123.0, 105.0, 570.0, 157.0, 890.0, 126.0, 470.0, 100.0, 214.0, 676.0, 368.0, 102.0, 97.0, 117.0, 158.0, 518.0, 232.0, 136.0, 378.0, 140.0, 107.0, 125.0, 220.0, 158.0, 88.0, 132.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042351494, -0.016798435, 0.0141085265, -0.026446652, 0.015842874, -0.01796127, -0.0191333, -0.03200128, 0.07914879, -0.020487582, -0.107363656, 0.018970106, -0.005150385, -0.0074725947, -0.08895771, -0.0009258675, -0.020747075, -0.027915698, 0.07102891, -0.019339867, 0.000865063, -0.0061480287, -0.017112402, -0.005495105, 0.017930923, -0.029427327, 0.011368308, -0.00078289304, -0.014911841], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [3.7627714, 3.207124, 0.0, 2.5156236, 0.0, 2.3314588, 0.0, 1.2963431, 3.1198924, 1.1549145, 1.9445581, 0.0, 0.0, 1.7476426, 2.1102223, 0.0, 0.0, 2.6933565, 3.0692594, 0.0, 0.0, 2.0921881, 0.0, 0.0, 0.0, 1.6234645, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.1865025, 1.0, 0.0141085265, 1.5459449, 0.015842874, 0.78585535, -0.0191333, 1.0, 1.0, 1.0, 0.40358648, 0.018970106, -0.005150385, 0.62324387, 1.0, -0.0009258675, -0.020747075, 0.55261385, 0.59734756, -0.019339867, 0.000865063, 1.0, -0.017112402, -0.005495105, 0.017930923, 0.47357202, 0.011368308, -0.00078289304, -0.014911841], "split_indices": [139, 102, 0, 138, 0, 140, 0, 64, 108, 58, 141, 0, 0, 143, 108, 0, 0, 139, 139, 0, 0, 105, 0, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1897.0, 164.0, 1798.0, 99.0, 1710.0, 88.0, 1494.0, 216.0, 1296.0, 198.0, 117.0, 99.0, 1089.0, 207.0, 100.0, 98.0, 864.0, 225.0, 100.0, 107.0, 750.0, 114.0, 104.0, 121.0, 628.0, 122.0, 532.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028002092, 0.011894152, -0.12906288, -0.0036739092, 0.08441854, -0.005244237, -0.022483848, 0.014363917, -0.021724785, -0.004296781, 0.17020936, 0.001892105, 0.020158278, 0.036018845, -0.0027685452, 0.015547634, -0.076487824, -0.008870411, 0.1159946, -0.019257672, 0.005208371, 0.08104249, -0.03844529, -0.0049130237, 0.023247804, 0.026156906, -0.0072628865, 0.009194104, -0.06577316, -0.08934437, 0.0025057234, -0.0036037446, -0.023296464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1], "loss_changes": [3.8442843, 2.0955434, 1.5850885, 5.8864822, 3.584575, 0.0, 0.0, 3.2899559, 0.0, 0.0, 7.3687925, 1.4138918, 0.0, 0.0, 0.0, 2.7593088, 2.9254432, 2.4065428, 4.2315493, 0.0, 0.0, 6.214155, 2.4265258, 0.0, 0.0, 0.0, 0.0, 0.0, 1.205373, 3.422211, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1], "split_conditions": [1.0, 0.90107125, 0.3567492, 0.8124543, 1.0084468, -0.005244237, -0.022483848, 0.8543508, -0.021724785, -0.004296781, 1.1510894, 2.0, 0.020158278, 0.036018845, -0.0027685452, 0.59381, 1.0, 1.2186605, 0.6023556, -0.019257672, 0.005208371, 1.0, 1.0, -0.0049130237, 0.023247804, 0.026156906, -0.0072628865, 0.009194104, 0.5260858, 0.41708985, 0.0025057234, -0.0036037446, -0.023296464], "split_indices": [40, 142, 142, 141, 139, 0, 0, 140, 0, 0, 143, 0, 0, 0, 0, 139, 106, 138, 143, 0, 0, 23, 89, 0, 0, 0, 0, 0, 142, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1856.0, 216.0, 1528.0, 328.0, 120.0, 96.0, 1409.0, 119.0, 132.0, 196.0, 1321.0, 88.0, 100.0, 96.0, 1125.0, 196.0, 905.0, 220.0, 103.0, 93.0, 224.0, 681.0, 91.0, 129.0, 103.0, 121.0, 118.0, 563.0, 447.0, 116.0, 326.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0032242567, -0.004330529, 0.015362619, 0.0065705883, -0.09422601, 0.014106292, -0.013489509, -0.0038862599, -0.016447203, 0.0007615697, 0.013198465, 0.015461273, -0.05582747, 0.00023212736, 0.014375157, -0.10115339, 0.0034824342, 0.015906356, -0.014433597, -0.00032057075, -0.018652061, 0.0128195165, -0.00649469, -0.07054594, 0.038547795, 0.008833003, -0.02051866, -0.0031137536, 0.013334817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1], "loss_changes": [2.3281856, 1.9119045, 0.0, 1.8549155, 0.82059085, 2.5986848, 0.0, 0.0, 0.0, 1.2344536, 0.0, 2.301519, 1.2573166, 2.3860905, 0.0, 1.7057498, 0.0, 2.389618, 0.0, 0.0, 0.0, 0.0, 2.2849422, 6.994912, 3.0718808, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1], "split_conditions": [1.3934005, 1.0, 0.015362619, 1.6076417, 1.0, 0.95011306, -0.013489509, -0.0038862599, -0.016447203, 1.0, 0.013198465, 0.77939034, 0.5942927, 0.6809079, 0.014375157, 0.29029372, 0.0034824342, 1.0, -0.014433597, -0.00032057075, -0.018652061, 0.0128195165, 1.0, 0.33592045, 0.40912068, 0.008833003, -0.02051866, -0.0031137536, 0.013334817], "split_indices": [139, 40, 0, 138, 115, 140, 0, 0, 0, 61, 0, 142, 140, 141, 0, 140, 0, 89, 0, 0, 0, 0, 81, 141, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1951.0, 98.0, 1740.0, 211.0, 1652.0, 88.0, 118.0, 93.0, 1484.0, 168.0, 1178.0, 306.0, 1053.0, 125.0, 204.0, 102.0, 950.0, 103.0, 95.0, 109.0, 158.0, 792.0, 327.0, 465.0, 150.0, 177.0, 268.0, 197.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021546045, 0.0066143386, -0.019714046, -0.008525497, 0.026635284, -0.09982166, 0.0015582812, 0.0094351955, -0.028185947, -0.009349249, 0.0190306, 0.0031094353, -0.01755773, -0.012601512, 0.018129779, -0.02354617, 0.0108274, 0.0008051534, -0.13547492, -0.028194098, 0.02720507, -0.0011112377, -0.024416149, -0.00039708932, -0.011181072], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [3.5359085, 7.7822165, 0.0, 1.7215415, 0.0, 6.5745306, 3.4669724, 0.0, 0.0, 3.2970057, 0.0, 4.1460714, 0.0, 1.8005232, 0.0, 3.401566, 0.0, 8.062566, 3.0141873, 1.8755779, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.019714046, -0.5, 0.026635284, 0.59734756, 0.9214082, 0.0094351955, -0.028185947, 0.8124543, 0.0190306, 0.71012646, -0.01755773, 0.70514965, 0.018129779, 0.51408464, 0.0108274, 0.5260094, 1.0, 0.3799253, 0.02720507, -0.0011112377, -0.024416149, -0.00039708932, -0.011181072], "split_indices": [143, 142, 0, 1, 0, 139, 142, 0, 0, 141, 0, 142, 0, 140, 0, 141, 0, 139, 124, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1979.0, 89.0, 1870.0, 109.0, 186.0, 1684.0, 90.0, 96.0, 1592.0, 92.0, 1481.0, 111.0, 1361.0, 120.0, 1248.0, 113.0, 1025.0, 223.0, 926.0, 99.0, 104.0, 119.0, 718.0, 208.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.004728647, -0.012662201, 0.015953762, 0.00043637943, -0.016059432, -0.013085894, 0.024685828, -0.005286693, -0.013888699, -0.015974557, 0.017772794, -0.0075488314, -0.012984219, -0.038544383, 0.052517075, -0.023243757, -0.013340823, 0.018760651, 0.009818495, -0.04321704, 0.012447529, -0.06484801, 0.009354859, 0.0027212994, -0.0066781985, -0.01485418, 0.0012216533], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, 21, 23, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [2.6872296, 3.8114574, 0.0, 6.0212574, 0.0, 1.6807063, 0.0, 3.1550848, 0.0, 1.4621519, 0.0, 2.64186, 0.0, 1.3585813, 2.786005, 2.37805, 0.0, 0.0, 2.2944229, 1.1783726, 0.0, 1.2512661, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, 22, 24, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.2138374, 1.0711201, 0.015953762, 0.91847384, -0.016059432, 1.5248721, 0.024685828, 0.78346187, -0.013888699, 0.7449002, 0.017772794, 1.0, -0.012984219, 0.54327625, 0.40983817, 0.43161404, -0.013340823, 0.018760651, 1.0, 0.0, 0.012447529, 0.5684248, 0.009354859, 0.0027212994, -0.0066781985, -0.01485418, 0.0012216533], "split_indices": [141, 140, 0, 140, 0, 138, 0, 139, 0, 140, 0, 71, 0, 143, 142, 142, 0, 0, 111, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1967.0, 95.0, 1807.0, 160.0, 1713.0, 94.0, 1613.0, 100.0, 1524.0, 89.0, 1419.0, 105.0, 936.0, 483.0, 806.0, 130.0, 116.0, 367.0, 710.0, 96.0, 194.0, 173.0, 178.0, 532.0, 93.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.00015072773, -0.030555315, 0.027361095, -0.017723791, -0.01595996, 0.009940677, 0.016933747, -0.05764584, 0.010692672, -0.05664852, 0.07818427, -0.013434019, -0.015615284, 0.040565617, -0.007823677, 0.07486626, -0.13445792, 0.027243782, 0.020897698, 0.0057888306, -0.0052574337, 0.017794687, -0.00046434015, -0.008726082, 0.02234828, -0.022578245, -0.0041354313, -0.021459553, 0.10834189, -0.060115565, 0.009052903, 2.2069567e-05, 0.021646306, 0.0027367435, -0.01456759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, -1, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [1.7303675, 1.6111267, 2.7156692, 1.0039825, 0.0, 4.444312, 0.0, 1.6027037, 1.3734541, 5.0653777, 5.3748817, 0.70906097, 0.0, 2.1814215, 0.0, 4.433437, 2.6443214, 0.0, 7.6810074, 0.0, 0.0, 0.0, 1.6175041, 0.0, 0.0, 0.0, 0.0, 0.0, 3.1797311, 1.3473139, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 16, 16, 18, 18, 22, 22, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, -1, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [0.44131443, 0.5361932, 1.2692307, 0.26347196, -0.01595996, 1.0, 0.016933747, 0.21959372, 0.3714244, -0.34615386, 0.54636854, 1.1763812, -0.015615284, 1.2186605, -0.007823677, -0.5, 1.0, 0.027243782, 0.71012646, 0.0057888306, -0.0052574337, 0.017794687, 1.0, -0.008726082, 0.02234828, -0.022578245, -0.0041354313, -0.021459553, -0.34615386, 0.30769232, 0.009052903, 2.2069567e-05, 0.021646306, 0.0027367435, -0.01456759], "split_indices": [140, 139, 1, 141, 0, 124, 0, 141, 140, 1, 141, 138, 0, 138, 0, 1, 59, 0, 142, 0, 0, 0, 93, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 973.0, 1098.0, 885.0, 88.0, 978.0, 120.0, 368.0, 517.0, 495.0, 483.0, 254.0, 114.0, 387.0, 130.0, 184.0, 311.0, 110.0, 373.0, 90.0, 164.0, 89.0, 298.0, 88.0, 96.0, 157.0, 154.0, 101.0, 272.0, 180.0, 118.0, 136.0, 136.0, 89.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00044767914, 0.010192672, -0.08254192, 0.00082737097, 0.011177014, -0.00371893, -0.013091802, 0.012063665, -0.020155214, -0.0010921264, 0.023585998, 0.010647328, -0.009578103, -0.03827339, 0.054932, -0.008310753, -0.021903694, 0.013294927, 0.02346288, -0.040871475, 0.015774889, -0.02397542, 0.020361163, 0.026011808, -0.13388105, 0.023120975, -0.01130614, 0.015747858, -0.0050223353, -0.019409822, -0.0062715285, -0.010494559, 0.008838568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, -1, 23, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6700335, 1.7580085, 0.47609353, 3.8476002, 0.0, 0.0, 0.0, 4.7195816, 0.0, 1.6829567, 0.0, 2.9181917, 0.0, 3.466337, 5.2898073, 2.9684548, 0.0, 4.07148, 0.0, 2.855341, 0.0, 2.0139017, 0.0, 2.675979, 0.8227961, 2.6244833, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, -1, 24, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2263254, 0.30790225, 1.0517951, 0.011177014, -0.00371893, -0.013091802, 1.0, -0.020155214, 0.76232654, 0.023585998, 1.0, -0.009578103, 0.61696106, 0.6720697, 0.52038383, -0.021903694, 1.0, 0.02346288, 0.33801442, 0.015774889, 0.4564035, 0.020361163, 1.0, 1.2930968, 0.21544637, -0.01130614, 0.015747858, -0.0050223353, -0.019409822, -0.0062715285, -0.010494559, 0.008838568], "split_indices": [40, 140, 140, 141, 0, 0, 0, 125, 0, 141, 0, 39, 0, 139, 139, 143, 0, 105, 0, 141, 0, 143, 0, 23, 138, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1848.0, 217.0, 1692.0, 156.0, 112.0, 105.0, 1603.0, 89.0, 1514.0, 89.0, 1347.0, 167.0, 640.0, 707.0, 549.0, 91.0, 574.0, 133.0, 459.0, 90.0, 480.0, 94.0, 267.0, 192.0, 314.0, 166.0, 98.0, 169.0, 104.0, 88.0, 106.0, 208.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002463484, -0.009294504, 0.013464481, 0.0028535044, -0.07158617, -0.012212647, 0.016000187, 0.019635813, -0.020735845, -0.0033122932, -0.0150216995, -0.010005944, 0.014705338, -0.038963452, 0.037602425, -0.011373681, -0.02399746, 0.10922134, -0.034673538, 0.035630845, -0.071831234, -0.0018251563, 0.022919586, 0.007083042, -0.015465845, 0.07690599, -0.0065646106, 0.0030434194, -0.12243682, -0.00013217176, 0.019994376, -0.005886632, -0.019291714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, 15, 17, 19, -1, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.9340571, 1.4884678, 0.0, 3.8971047, 3.9757195, 1.8448877, 0.0, 2.9282455, 0.0, 2.058165, 0.0, 0.0, 0.0, 4.181571, 3.4008467, 1.8840992, 0.0, 5.046856, 4.1394553, 1.5592222, 1.5008093, 0.0, 0.0, 0.0, 0.0, 2.5506163, 0.0, 0.0, 0.86921024, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, 16, 18, 20, -1, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.3934005, 1.0, 0.013464481, 0.9620667, 0.6552192, 0.82126033, 0.016000187, 1.2899821, -0.020735845, 1.0, -0.0150216995, -0.010005944, 0.014705338, 0.66016495, 1.0, 1.0, -0.02399746, 0.44921184, 1.0, 1.3208424, 0.19738461, -0.0018251563, 0.022919586, 0.007083042, -0.015465845, 0.285204, -0.0065646106, 0.0030434194, 1.0, -0.00013217176, 0.019994376, -0.005886632, -0.019291714], "split_indices": [139, 64, 0, 142, 139, 142, 0, 138, 0, 39, 0, 0, 0, 139, 13, 12, 0, 142, 15, 138, 140, 0, 0, 0, 0, 139, 0, 0, 122, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1967.0, 98.0, 1646.0, 321.0, 1502.0, 144.0, 192.0, 129.0, 1411.0, 91.0, 99.0, 93.0, 754.0, 657.0, 663.0, 91.0, 330.0, 327.0, 373.0, 290.0, 160.0, 170.0, 174.0, 153.0, 265.0, 108.0, 96.0, 194.0, 162.0, 103.0, 102.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0028582646, -0.005073749, 0.015519962, -0.031693615, 0.023665529, -0.016594399, -0.01891326, -0.010202082, 0.042895388, -0.064594746, 0.025758844, 0.08519631, -0.030776342, -0.10711742, 0.0042054844, 0.10437112, -0.046429854, 0.04073384, 0.023966504, 0.009940053, -0.015921755, -0.0020149255, -0.019464666, 0.0008969347, 0.019054046, 0.002071622, -0.012429675, 0.017384678, -0.0104986215, 0.01478254, -0.090891145, 0.0006901647, -0.019825066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [2.4904587, 1.4987049, 0.0, 2.417617, 2.2767487, 1.8865967, 0.0, 0.0, 2.546085, 1.9727366, 2.797735, 3.5645225, 4.9825826, 2.3674114, 0.0, 1.9400861, 1.3437138, 2.748341, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.7038674, 0.0, 2.0263047, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 28, 28, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [1.3654754, 1.0, 0.015519962, 1.5248721, 0.27491036, 1.0, -0.01891326, -0.010202082, 0.68592155, 1.0, -0.03846154, 1.0, 1.0, 0.32774028, 0.0042054844, 0.44131443, 0.33801442, 0.0, 0.023966504, 0.009940053, -0.015921755, -0.0020149255, -0.019464666, 0.0008969347, 0.019054046, 0.002071622, -0.012429675, 0.017384678, 1.0, 0.01478254, 0.4521848, 0.0006901647, -0.019825066], "split_indices": [140, 39, 0, 138, 141, 13, 0, 0, 141, 50, 1, 105, 121, 141, 0, 140, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 111, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1959.0, 102.0, 1017.0, 942.0, 928.0, 89.0, 125.0, 817.0, 435.0, 493.0, 519.0, 298.0, 311.0, 124.0, 236.0, 257.0, 403.0, 116.0, 148.0, 150.0, 156.0, 155.0, 112.0, 124.0, 138.0, 119.0, 112.0, 291.0, 98.0, 193.0, 101.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009226396, 0.013455032, -0.008256723, -0.014313922, 0.011601566, -0.02790184, 0.05666029, -0.0017625267, -0.08552592, -0.024954963, 0.021989083, -0.017699692, 0.014592185, 0.003304847, -0.13501786, -0.010527767, 0.0077273906, 0.011631331, -0.034606427, -0.09090015, -0.026386155, 0.022704454, -0.08660372, -0.017495155, 0.00050585135, -0.054378487, 0.10643923, -0.056271125, -0.02024484, -0.011995907, -0.0005859497, -0.0022115342, 0.025019923, -0.13218375, 0.0038145182, -0.0045874775, -0.020490702], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, 13, 15, -1, 17, -1, -1, 19, -1, -1, -1, 21, 23, -1, 25, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [2.0507283, 0.0, 1.4738696, 1.8005192, 0.0, 2.3602996, 3.9966311, 2.537256, 2.869685, 1.6422601, 0.0, 2.204548, 0.0, 0.0, 1.9610782, 0.0, 0.0, 0.0, 2.5747297, 2.072824, 0.0, 2.6528091, 1.5917826, 0.0, 0.0, 0.6809269, 3.6407592, 2.5730934, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2490568, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 14, 14, 18, 18, 19, 19, 21, 21, 22, 22, 25, 25, 26, 26, 27, 27, 33, 33], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, 14, 16, -1, 18, -1, -1, 20, -1, -1, -1, 22, 24, -1, 26, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [-0.5769231, 0.013455032, 1.3608713, 1.0, 0.011601566, 0.6312413, 0.7129567, 0.56020635, -0.30769232, 0.26923078, 0.021989083, 0.10083316, 0.014592185, 0.003304847, 0.8941447, -0.010527767, 0.0077273906, 0.011631331, 1.0, 1.0, -0.026386155, 1.0, 0.4533606, -0.017495155, 0.00050585135, 0.31178114, 0.07692308, 0.29993045, -0.02024484, -0.011995907, -0.0005859497, -0.0022115342, 0.025019923, 0.21818505, 0.0038145182, -0.0045874775, -0.020490702], "split_indices": [1, 0, 139, 42, 0, 143, 142, 143, 1, 1, 0, 139, 0, 0, 139, 0, 0, 0, 108, 12, 0, 17, 142, 0, 0, 141, 1, 139, 0, 0, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 106.0, 1958.0, 1867.0, 91.0, 1567.0, 300.0, 1078.0, 489.0, 200.0, 100.0, 973.0, 105.0, 144.0, 345.0, 112.0, 88.0, 109.0, 864.0, 257.0, 88.0, 411.0, 453.0, 137.0, 120.0, 214.0, 197.0, 359.0, 94.0, 91.0, 123.0, 104.0, 93.0, 199.0, 160.0, 91.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014119827, 0.005773874, -0.015093899, 0.011909946, -0.0006546421, 0.0088810725, -0.01228399, -0.008431559, 0.028588323, 0.0015028191, -0.013932139, -0.016181413, 0.11531225, 0.013796319, -0.07437349, -0.0029114713, 0.030241085, 0.03849637, -0.009395764, -0.10990874, 0.0024335512, 0.0034928003, 0.10161525, -0.002605839, -0.16580896, 0.013337835, -0.0029069025, 0.020773044, -0.0028186326, -0.024422249, -0.0070465184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, -1, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [2.2026842, 1.4249765, 0.0, 0.0, 2.1566446, 8.234109, 0.0, 2.1013, 0.0, 3.022974, 0.0, 2.2678065, 5.4584618, 2.2835915, 1.5503814, 0.0, 0.0, 1.5421515, 0.0, 1.523356, 0.0, 1.8989606, 3.429706, 0.0, 1.4578667, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, -1, -1, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.015093899, 0.011909946, 1.1510894, 1.0268755, -0.01228399, 1.5248721, 0.028588323, 1.0, -0.013932139, 1.0, 0.48754993, 1.0, 1.0, -0.0029114713, 0.030241085, 1.0, -0.009395764, 0.30939576, 0.0024335512, 0.1496468, 1.0, -0.002605839, 1.3576146, 0.013337835, -0.0029069025, 0.020773044, -0.0028186326, -0.024422249, -0.0070465184], "split_indices": [117, 1, 0, 0, 143, 142, 0, 138, 0, 42, 0, 109, 143, 0, 61, 0, 0, 71, 0, 143, 0, 140, 121, 0, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1956.0, 94.0, 105.0, 1851.0, 1717.0, 134.0, 1616.0, 101.0, 1502.0, 114.0, 1300.0, 202.0, 858.0, 442.0, 114.0, 88.0, 698.0, 160.0, 325.0, 117.0, 449.0, 249.0, 130.0, 195.0, 90.0, 359.0, 137.0, 112.0, 107.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041171748, -0.007878343, 0.008858486, 0.0030448968, -0.09970853, -0.008847142, 0.015970534, 0.0056156996, -0.021795137, 0.0044385493, -0.08380059, 0.046852503, -0.036984712, 8.7397326e-05, -0.019266792, -0.008013261, 0.16113512, -0.0064171026, -0.017000154, 0.041592285, -0.0133577315, 0.030414183, 0.003429436, -0.06162655, 0.059442975, -0.0047370545, 0.09894397, 0.00062651094, -0.013758928, 0.012933967, -0.0055595296, 0.0013960939, 0.0170425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3821682, 1.9249166, 0.0, 3.1950657, 3.7597165, 1.5873188, 0.0, 0.0, 0.0, 2.3788755, 2.2123914, 4.1947665, 2.785214, 0.0, 0.0, 2.8153608, 3.9361796, 2.025307, 0.0, 1.6531026, 0.0, 0.0, 0.0, 1.5626426, 2.0423617, 0.0, 1.1967102, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2263254, 0.96076936, 0.008858486, 0.87652975, 0.8080241, 2.0, 0.015970534, 0.0056156996, -0.021795137, 1.0, 0.41954267, 0.52038383, 0.54138494, 8.7397326e-05, -0.019266792, 0.45160067, 0.6588078, 1.0, -0.017000154, 1.0, -0.0133577315, 0.030414183, 0.003429436, 1.0, 1.0, -0.0047370545, 1.0, 0.00062651094, -0.013758928, 0.012933967, -0.0055595296, 0.0013960939, 0.0170425], "split_indices": [140, 143, 0, 139, 140, 0, 0, 0, 0, 106, 143, 143, 140, 0, 0, 140, 143, 39, 0, 97, 0, 0, 0, 126, 15, 0, 111, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1919.0, 161.0, 1715.0, 204.0, 1594.0, 121.0, 88.0, 116.0, 1354.0, 240.0, 669.0, 685.0, 135.0, 105.0, 452.0, 217.0, 557.0, 128.0, 324.0, 128.0, 102.0, 115.0, 303.0, 254.0, 127.0, 197.0, 160.0, 143.0, 158.0, 96.0, 90.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00045112515, -0.057803903, 0.013991727, -0.094337896, 0.002570234, 0.012809971, 0.003947805, -0.054927696, -0.016406361, -0.018145492, 0.019653134, -0.0071140896, -0.0039063143, 0.009053027, 0.010020084, 0.015307217, -0.002443235, -0.041327868, 0.057179864, -0.0077122557, -0.027128918, -0.032582577, 0.14380652, 0.067474745, -0.11802762, 0.007044694, -0.012089361, 0.031701863, -0.0016305505, -0.005351608, 0.026068753, -0.001807328, -0.018175688, 0.0088225985, -0.0085561], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, 13, -1, -1, 15, -1, -1, 17, 19, 21, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.7047191, 1.2630384, 1.884174, 0.7913959, 0.0, 0.0, 4.3997455, 0.04732722, 0.0, 0.0, 1.189363, 0.0, 0.0, 2.0381448, 0.0, 0.0, 2.6430013, 5.3338995, 3.4991193, 4.9931574, 0.0, 2.0108006, 6.3509355, 5.0374126, 1.5542834, 0.0, 0.0, 0.0, 0.0, 1.9515083, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 10, 10, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, 14, -1, -1, 16, -1, -1, 18, 20, 22, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.2770242, 1.0, 0.22975573, 0.20959868, 0.002570234, 0.012809971, 1.2371621, 0.14616992, -0.016406361, -0.018145492, 1.2692307, -0.0071140896, -0.0039063143, 0.3506468, 0.010020084, 0.015307217, 0.793871, 0.9292002, 0.9292002, 1.0, -0.027128918, 0.67962027, 1.1785235, 0.6461537, 1.0, 0.007044694, -0.012089361, 0.031701863, -0.0016305505, 0.48374972, 0.026068753, -0.001807328, -0.018175688, 0.0088225985, -0.0085561], "split_indices": [141, 83, 143, 142, 0, 0, 138, 142, 0, 0, 1, 0, 0, 141, 0, 0, 143, 140, 140, 12, 0, 140, 143, 143, 53, 0, 0, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 414.0, 1644.0, 288.0, 126.0, 133.0, 1511.0, 184.0, 104.0, 118.0, 1393.0, 91.0, 93.0, 1231.0, 162.0, 91.0, 1140.0, 690.0, 450.0, 602.0, 88.0, 221.0, 229.0, 358.0, 244.0, 102.0, 119.0, 110.0, 119.0, 260.0, 98.0, 95.0, 149.0, 120.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0014258137, 0.012211868, -0.0050597275, -0.012314557, 0.008894212, 0.0010130778, -0.08322501, -0.009159712, 0.014934899, 0.012102023, -0.02885448, -0.054789092, 0.016162356, 0.016313488, -0.011096175, -0.10194246, 0.031658735, -0.040287234, 0.05871501, -0.021814773, -0.019927876, 0.017855067, -0.010884656, 0.0051174355, -0.017423104, 0.018162992, 0.0226489, -0.012132562, 0.007660252, -0.071591094, 0.013793686, 0.049413238, -0.009550544, -0.00036599155, -0.013462, -0.021310365, 0.013793804, -0.009553886, 0.004975944], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, 33, -1, 35, -1, -1, -1, 37, -1, -1, -1], "loss_changes": [1.6117003, 0.0, 1.3325639, 1.7143548, 0.0, 2.3042285, 5.617317, 1.6511099, 0.0, 3.6429877, 0.0, 2.0789163, 2.2075117, 0.0, 0.0, 2.5737789, 3.715037, 2.4022617, 3.565072, 1.7726398, 0.0, 0.0, 0.0, 3.005573, 0.0, 1.4990143, 0.0, 0.0, 0.0, 0.80066407, 0.0, 2.0723228, 0.0, 0.0, 0.0, 0.9706745, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 25, 25, 29, 29, 31, 31, 35, 35], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, 34, -1, 36, -1, -1, -1, 38, -1, -1, -1], "split_conditions": [-0.5769231, 0.012211868, 1.1865025, 1.0, 0.008894212, 0.9620667, 0.6552192, -0.115384616, 0.014934899, 0.23076923, -0.02885448, 0.6023556, 1.0, 0.016313488, -0.011096175, 0.3602427, 0.6558667, 0.5421651, 0.59734756, 0.30290008, -0.019927876, 0.017855067, -0.010884656, 0.30718836, -0.017423104, 0.5014162, 0.0226489, -0.012132562, 0.007660252, 0.15674502, 0.013793686, 0.33089325, -0.009550544, -0.00036599155, -0.013462, 0.84615386, 0.013793804, -0.009553886, 0.004975944], "split_indices": [1, 0, 139, 64, 0, 142, 139, 1, 0, 1, 0, 143, 59, 0, 0, 143, 141, 139, 139, 142, 0, 0, 0, 139, 0, 143, 0, 0, 0, 142, 0, 140, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 105.0, 1954.0, 1814.0, 140.0, 1527.0, 287.0, 1429.0, 98.0, 196.0, 91.0, 510.0, 919.0, 88.0, 108.0, 330.0, 180.0, 395.0, 524.0, 181.0, 149.0, 88.0, 92.0, 295.0, 100.0, 422.0, 102.0, 90.0, 91.0, 187.0, 108.0, 331.0, 91.0, 90.0, 97.0, 184.0, 147.0, 90.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-2.0718078e-05, 0.006069734, -0.012923002, -0.0063983705, 0.022788329, 0.007077574, -0.0785451, -0.007580266, 0.014726162, 0.01987366, -0.02556989, -0.047830917, 0.022719113, 0.014311968, -0.008750898, -0.013316806, -0.023802565, -0.0092452755, 0.048139933, -0.0692485, 0.011412247, -0.015689505, 0.12792674, -0.015395952, -0.01854566, 0.011671199, -0.05762451, 0.030799968, 0.025980681, -0.012038212, 0.005179518, -0.0002530417, -0.017360121, -0.0060793334, 0.012347961, -0.0069791707, 0.0075466796], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, -1, 21, 23, -1, 25, 27, 29, -1, -1, 31, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [1.6258248, 5.4565187, 0.0, 1.8161546, 0.0, 3.2342484, 5.1259665, 1.737887, 0.0, 2.5013175, 0.0, 4.017413, 2.3802714, 0.0, 0.0, 3.6922493, 0.0, 0.0, 3.3917701, 2.2529163, 0.0, 2.054335, 5.433885, 1.7353184, 0.0, 0.0, 1.8697045, 0.0, 1.5990107, 0.0, 0.0, 0.9899058, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 26, 26, 28, 28, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, -1, 22, 24, -1, 26, 28, 30, -1, -1, 32, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.012923002, 1.0, 0.022788329, 0.91847384, 0.6552192, 1.0, 0.014726162, 0.26923078, -0.02556989, 0.75931257, -1.0, 0.014311968, -0.008750898, 0.47357202, -0.023802565, -0.0092452755, 0.42291117, 0.35378668, 0.011412247, 1.202536, 0.50344044, 1.2006011, -0.01854566, 0.011671199, 0.31763452, 0.030799968, 0.66095394, -0.012038212, 0.005179518, 0.28000483, -0.017360121, -0.0060793334, 0.012347961, -0.0069791707, 0.0075466796], "split_indices": [143, 142, 0, 64, 0, 140, 139, 59, 0, 1, 0, 143, 0, 0, 0, 140, 0, 0, 142, 141, 0, 138, 141, 138, 0, 0, 142, 0, 141, 0, 0, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1973.0, 93.0, 1868.0, 105.0, 1574.0, 294.0, 1425.0, 149.0, 189.0, 105.0, 612.0, 813.0, 88.0, 101.0, 518.0, 94.0, 147.0, 666.0, 360.0, 158.0, 370.0, 296.0, 246.0, 114.0, 89.0, 281.0, 107.0, 189.0, 96.0, 150.0, 188.0, 93.0, 100.0, 89.0, 98.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00026141945, 0.011352096, -0.0058091516, -0.009065443, 0.0012769288, -0.0153200375, 0.08537566, -0.004710477, -0.009678193, 0.032094974, -0.011239022, -0.022650722, 0.058597293, -0.04915943, 0.036275744, -0.024579383, 0.019065853, -0.024770074, -0.01783802, -0.005443508, 0.012642666, -0.0150251165, 0.01082328, -0.0741452, 0.01434908, -0.02755526, -0.016577205, -0.042486615, 0.01751683, -0.012013379, 0.0063983016, 0.0007380092, -0.013213466], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.4191039, 0.0, 1.177791, 0.0, 2.5235772, 1.3050549, 13.883379, 1.5173709, 0.0, 0.0, 0.0, 1.6261091, 3.2404032, 2.262856, 2.6413853, 3.0210247, 0.0, 1.1666341, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1397926, 3.0802724, 1.4999828, 0.0, 1.1131433, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 23, 23, 24, 24, 25, 25, 27, 27], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.011352096, -0.5, -0.009065443, 0.8318429, 0.7449002, 1.0, 0.521583, -0.009678193, 0.032094974, -0.011239022, 1.0, 1.0, 0.4605518, 0.15384616, 1.0, 0.019065853, 1.0, -0.01783802, -0.005443508, 0.012642666, -0.0150251165, 0.01082328, 1.2546202, 0.3333504, 0.18902422, -0.016577205, 0.2614143, 0.01751683, -0.012013379, 0.0063983016, 0.0007380092, -0.013213466], "split_indices": [1, 0, 1, 0, 139, 140, 69, 139, 0, 0, 0, 93, 105, 140, 1, 69, 0, 111, 0, 0, 0, 0, 0, 138, 140, 140, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 105.0, 1959.0, 151.0, 1808.0, 1510.0, 298.0, 1336.0, 174.0, 136.0, 162.0, 1041.0, 295.0, 718.0, 323.0, 181.0, 114.0, 604.0, 114.0, 161.0, 162.0, 93.0, 88.0, 267.0, 337.0, 177.0, 90.0, 249.0, 88.0, 88.0, 89.0, 160.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0020042115, 0.010944793, -0.074826285, 0.0038992185, 0.010673104, -0.013151549, -0.003207703, -0.064557835, 0.016708234, -0.01612793, 0.0038070243, -0.0011761202, 0.11006149, 0.013773284, -0.012877578, -0.0010257516, 0.021668566, -0.013346819, 0.015464286, 0.009096701, -0.033307265, 0.010974606, -0.107162006, -0.011560807, 0.0420419, -0.02522833, -0.0012371767, 0.012690584, -0.0049740286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, 13, 15, 17, -1, -1, -1, 19, -1, -1, 21, 23, 25, -1, 27, -1, -1, -1, -1], "loss_changes": [1.4102247, 1.241084, 0.51861155, 1.5020739, 0.0, 0.0, 0.0, 2.6801114, 2.4091792, 0.0, 0.0, 2.31003, 2.976309, 4.1413116, 0.0, 0.0, 0.0, 1.8926746, 0.0, 0.0, 2.4953356, 1.8758411, 3.9342399, 0.0, 2.9831865, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 17, 17, 20, 20, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, 14, 16, 18, -1, -1, -1, 20, -1, -1, 22, 24, 26, -1, 28, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.28111374, -0.46153846, 0.010673104, -0.013151549, -0.003207703, 1.0, 1.0, -0.01612793, 0.0038070243, 0.9044961, 0.5260094, 0.6913856, -0.012877578, -0.0010257516, 0.021668566, 1.0, 0.015464286, 0.009096701, 0.421051, -0.15384616, 1.3231709, -0.011560807, 0.7307692, -0.02522833, -0.0012371767, 0.012690584, -0.0049740286], "split_indices": [40, 0, 142, 1, 0, 0, 0, 39, 42, 0, 0, 141, 139, 139, 0, 0, 0, 89, 0, 0, 142, 1, 138, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1839.0, 214.0, 1713.0, 126.0, 92.0, 122.0, 270.0, 1443.0, 139.0, 131.0, 1211.0, 232.0, 1084.0, 127.0, 109.0, 123.0, 909.0, 175.0, 146.0, 763.0, 477.0, 286.0, 94.0, 383.0, 113.0, 173.0, 199.0, 184.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.002382628, 0.014967386, -0.04705962, 0.013134243, 0.00429007, -0.08884581, 0.004728563, 0.015133034, -0.011026458, -0.0019597588, -0.017326266, -0.008230707, 0.008465927, 0.0005512274, 0.015821699, -0.03724427, 0.047223967, -0.002587729, -0.0151976785, -0.01715031, 0.13296731, -0.05780761, 0.109420754, -0.13246241, 0.016847404, 0.023404337, -0.0012010456, 0.00034781755, -0.02355782, 0.008083199, 0.013800949, 0.0021387527, -0.02832354, -0.012449432, 0.0069624395], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, -1, 7, 9, 11, 13, -1, -1, -1, -1, -1, 15, -1, 17, 19, 21, -1, 23, 25, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2911038, 2.0552154, 0.9110573, 0.0, 1.8817993, 1.3620514, 1.3078822, 2.8876085, 0.0, 0.0, 0.0, 0.0, 0.0, 2.2156084, 0.0, 2.7595043, 3.1020522, 3.2966557, 0.0, 6.870919, 3.5315628, 3.6907823, 0.14384699, 4.5928907, 0.0, 0.0, 0.0, 2.3264832, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 27, 27], "right_children": [2, 4, 6, -1, 8, 10, 12, 14, -1, -1, -1, -1, -1, 16, -1, 18, 20, 22, -1, 24, 26, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.202536, 1.0, 0.013134243, 1.3461539, 0.34726515, 1.2177447, 0.84615386, -0.011026458, -0.0019597588, -0.017326266, -0.008230707, 0.008465927, 1.0, 0.015821699, 1.0, -0.1923077, 0.7460131, -0.0151976785, 1.0, 0.68592155, 0.61696106, 0.8266981, 0.6350785, 0.016847404, 0.023404337, -0.0012010456, 1.2736244, -0.02355782, 0.008083199, 0.013800949, 0.0021387527, -0.02832354, -0.012449432, 0.0069624395], "split_indices": [80, 138, 23, 0, 1, 141, 138, 1, 0, 0, 0, 0, 0, 124, 0, 61, 1, 143, 0, 61, 141, 139, 139, 141, 0, 0, 0, 138, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1654.0, 421.0, 139.0, 1515.0, 233.0, 188.0, 1384.0, 131.0, 128.0, 105.0, 90.0, 98.0, 1256.0, 128.0, 694.0, 562.0, 533.0, 161.0, 321.0, 241.0, 357.0, 176.0, 198.0, 123.0, 142.0, 99.0, 269.0, 88.0, 88.0, 88.0, 98.0, 100.0, 96.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0031621088, 0.015267, -0.044506308, 0.023810793, -0.007017091, -0.09555092, 0.018821744, 0.011864735, 0.013805546, -0.00077399, -0.01833619, -0.006780449, 0.0116276285, -0.02664855, 0.06051986, -0.061427455, 0.009008064, 0.10612387, -0.0057722866, 0.04660073, -0.11231228, 0.021642221, 0.03754886, -0.011359922, 0.02230007, 0.0029870998, -0.22361615, -0.017466849, 0.015668845, -0.011113643, 0.010704095, -0.0325504, -0.010179372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, 17, 19, -1, 21, -1, 23, 25, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1938564, 1.2044401, 1.3544409, 2.0471597, 0.0, 1.7888987, 1.5786765, 2.5447137, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0772629, 3.2354052, 3.2102456, 0.0, 3.2750864, 0.0, 5.284483, 5.0948067, 0.0, 6.7506905, 0.0, 0.0, 2.3156233, 2.507268, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, 18, 20, -1, 22, -1, 24, 26, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 0.84615386, -0.007017091, 0.31225175, 1.2252758, 1.0, 0.013805546, -0.00077399, -0.01833619, -0.006780449, 0.0116276285, 1.0, 1.0, -0.34615386, 0.009008064, 0.65298104, -0.0057722866, -0.5, 1.3546785, 0.021642221, 0.9305876, -0.011359922, 0.02230007, 1.0, 0.793871, -0.017466849, 0.015668845, -0.011113643, 0.010704095, -0.0325504, -0.010179372], "split_indices": [80, 1, 23, 1, 0, 140, 138, 124, 0, 0, 0, 0, 0, 62, 106, 1, 0, 141, 0, 1, 138, 0, 142, 0, 0, 105, 143, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1650.0, 419.0, 1500.0, 150.0, 232.0, 187.0, 1358.0, 142.0, 116.0, 116.0, 99.0, 88.0, 758.0, 600.0, 584.0, 174.0, 433.0, 167.0, 187.0, 397.0, 166.0, 267.0, 98.0, 89.0, 195.0, 202.0, 96.0, 171.0, 93.0, 102.0, 110.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0012669459, -0.0052653737, 0.011399284, 0.0046409494, -0.014170247, -0.0020020131, 0.012926874, -0.059911773, 0.01379932, -0.0048080054, -0.019015701, -0.009714902, 0.09023898, -0.009910687, 0.014854669, 0.016902668, -0.016711349, 0.020280397, -0.0007185652, -0.003423088, 0.019469721, 0.019318996, -0.014525102, 0.013079521, -0.011612016, -0.0074139163, 0.008203328], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, -1, -1, 25, -1, -1], "loss_changes": [1.5191144, 2.6356013, 0.0, 1.5051185, 0.0, 1.5793788, 0.0, 2.6554909, 2.4373007, 3.759905, 0.0, 4.3445826, 3.4983459, 0.0, 0.0, 3.205449, 0.0, 0.0, 0.0, 2.567469, 0.0, 2.365378, 0.0, 0.0, 3.1443355, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 19, 19, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, -1, -1, 26, -1, -1], "split_conditions": [3.5, 1.3461539, 0.011399284, 1.1923077, -0.014170247, 1.0, 0.012926874, 1.0, 0.80189264, 1.0, -0.019015701, 0.6699145, 1.0, -0.009910687, 0.014854669, 0.70514965, -0.016711349, 0.020280397, -0.0007185652, 2.0, 0.019469721, 1.204987, -0.014525102, 0.013079521, 1.0, -0.0074139163, 0.008203328], "split_indices": [1, 1, 0, 1, 0, 89, 0, 61, 143, 97, 0, 143, 121, 0, 0, 140, 0, 0, 0, 0, 0, 138, 0, 0, 93, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1950.0, 113.0, 1818.0, 132.0, 1726.0, 92.0, 370.0, 1356.0, 260.0, 110.0, 1037.0, 319.0, 161.0, 99.0, 887.0, 150.0, 148.0, 171.0, 796.0, 91.0, 686.0, 110.0, 149.0, 537.0, 322.0, 215.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007449759, 0.0049337493, -0.011844406, -0.0047988365, 0.017598856, -0.011926329, 0.011102288, 0.004315705, -0.12525144, -0.012349426, 0.13299705, -0.004077949, -0.01956447, -0.001610712, -0.0133087365, 0.002627923, 0.023971483, -0.017633004, 0.074090935, 0.0033908032, -0.111183405, 0.023191119, -0.0034792789, -0.014928759, 0.015625741, -0.005011888, -0.01655308, 0.0010085474, -0.008674799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, -1, -1, -1, 19, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [1.3795383, 3.2780027, 0.0, 1.5379405, 0.0, 3.2303069, 0.0, 3.2917945, 1.3081758, 1.7620387, 2.0044084, 0.0, 0.0, 1.5137163, 0.0, 0.0, 0.0, 2.0257897, 3.746125, 2.3551779, 0.62723327, 0.0, 0.0, 1.3491739, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, -1, -1, -1, 20, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.011844406, 5.0, 0.017598856, 0.8365981, 0.011102288, 0.7601444, 1.0, 0.63804257, 0.6904005, -0.004077949, -0.01956447, 0.4963337, -0.0133087365, 0.002627923, 0.023971483, 1.3089205, 1.0, 0.47274625, 0.40358648, 0.023191119, -0.0034792789, 0.3506468, 0.015625741, -0.005011888, -0.01655308, 0.0010085474, -0.008674799], "split_indices": [143, 142, 0, 0, 0, 143, 0, 139, 39, 139, 143, 0, 0, 139, 0, 0, 0, 138, 53, 141, 141, 0, 0, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1969.0, 95.0, 1863.0, 106.0, 1755.0, 108.0, 1535.0, 220.0, 1359.0, 176.0, 100.0, 120.0, 1248.0, 111.0, 88.0, 88.0, 1030.0, 218.0, 841.0, 189.0, 89.0, 129.0, 751.0, 90.0, 89.0, 100.0, 557.0, 194.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041192723, -0.006389178, 0.010219752, 0.0019129, -0.012211129, -0.0070921965, 0.08476987, -0.06401465, 0.008886811, -0.004010941, 0.020553228, -0.007872759, -0.01955322, -0.0130330045, 0.077315986, 0.017228901, -0.01056435, 0.015461763, -0.01759445, 0.0091507165, 0.02156319, -0.016479554, 0.016999478, 0.012756273, -0.015366577, 0.012456264, -0.010379467, -0.017384335, 0.014642154, 0.00841553, -0.004569236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, 17, 19, -1, -1, 21, -1, 23, -1, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [1.2701904, 1.8801569, 0.0, 1.3624427, 0.0, 1.4980528, 2.6994498, 2.6654944, 1.9289421, 0.0, 0.0, 4.456481, 0.0, 4.52143, 2.9416418, 0.0, 0.0, 4.0919347, 0.0, 4.0294003, 0.0, 1.7357295, 0.0, 0.0, 0.0, 2.0627642, 0.0, 1.2129911, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, 18, 20, -1, -1, 22, -1, 24, -1, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [3.5, 1.3461539, 0.010219752, 0.88461536, -0.012211129, 1.0, 0.34481022, 1.0, 0.80189264, -0.004010941, 0.020553228, 0.5847087, -0.01955322, 0.6699145, 1.0, 0.017228901, -0.01056435, 0.55698216, -0.01759445, -0.26923078, 0.02156319, 0.4533606, 0.016999478, 0.012756273, -0.015366577, 1.0, -0.010379467, 0.14673588, 0.014642154, 0.00841553, -0.004569236], "split_indices": [1, 1, 0, 1, 0, 89, 143, 106, 143, 0, 0, 140, 0, 143, 59, 0, 0, 143, 0, 1, 0, 142, 0, 0, 0, 71, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1957.0, 114.0, 1826.0, 131.0, 1647.0, 179.0, 361.0, 1286.0, 88.0, 91.0, 253.0, 108.0, 974.0, 312.0, 89.0, 164.0, 829.0, 145.0, 209.0, 103.0, 687.0, 142.0, 121.0, 88.0, 516.0, 171.0, 422.0, 94.0, 92.0, 330.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004035032, 0.0050660083, -0.01156124, -0.002550789, 0.013972527, 0.009987136, -0.069660574, -0.00394628, 0.015433336, 0.025768766, -0.023981294, -0.05174164, 0.024646057, 0.014581136, -0.0078822775, 0.002906694, -0.01789171, 0.11510309, -0.00017524665, -0.051000014, 0.012884806, -0.005760444, 0.031467628, 0.06722572, -0.043852333, 0.0057755765, -0.010917173, 0.016279535, -0.0034007288, 0.022804659, -0.09684326, -0.009890038, 0.01638719, -0.0014898643, -0.02127245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [1.3069032, 2.0308316, 0.0, 1.5768167, 0.0, 3.1757421, 4.7900715, 1.9678768, 0.0, 2.3729782, 0.0, 3.7460113, 2.022981, 0.0, 0.0, 2.559485, 0.0, 6.686753, 2.0813215, 1.6701992, 0.0, 0.0, 0.0, 2.6895952, 1.515321, 0.0, 0.0, 0.0, 0.0, 3.262033, 2.2695062, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.01156124, 1.0, 0.013972527, 0.8692273, 0.6552192, -0.115384616, 0.015433336, 0.26923078, -0.023981294, 0.58609116, 0.0, 0.014581136, -0.0078822775, 0.4534852, -0.01789171, 0.39481246, 1.0, 0.266145, 0.012884806, -0.005760444, 0.031467628, 1.0, 1.0, 0.0057755765, -0.010917173, 0.016279535, -0.0034007288, 0.2709653, 1.2904363, -0.009890038, 0.01638719, -0.0014898643, -0.02127245], "split_indices": [143, 142, 0, 64, 0, 141, 139, 1, 0, 1, 0, 141, 0, 0, 0, 141, 0, 140, 124, 140, 0, 0, 0, 13, 1, 0, 0, 0, 0, 142, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1980.0, 94.0, 1874.0, 106.0, 1579.0, 295.0, 1440.0, 139.0, 189.0, 106.0, 539.0, 901.0, 88.0, 101.0, 377.0, 162.0, 194.0, 707.0, 264.0, 113.0, 104.0, 90.0, 278.0, 429.0, 92.0, 172.0, 143.0, 135.0, 190.0, 239.0, 102.0, 88.0, 140.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00095199567, 0.0052694604, -0.010666268, -0.003279077, 0.018231919, 0.004366438, -0.013958551, -0.012472796, 0.09482414, 0.056709245, -0.036367502, -0.0038657666, 0.027468523, 0.12758219, -0.0064448444, -0.086934246, 0.00058815745, 0.020468192, 0.005383459, 0.0059389956, -0.008516041, -0.03287578, -0.019470463, 0.06198578, -0.055478524, 0.0056363107, -0.072721995, -0.008421308, 0.02224837, -0.014829764, 0.02018921, -0.0018350994, -0.015118919, -0.00827468, 0.007326311, 0.011301811, -0.007889777], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, -1, -1, -1, -1, 25, -1, 27, 29, -1, 31, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3607285, 2.9574115, 0.0, 1.9425362, 0.0, 2.6885164, 0.0, 2.4597898, 6.650268, 1.7098004, 2.0668125, 0.0, 0.0, 1.0234647, 1.0468091, 2.7206955, 2.1996686, 0.0, 0.0, 0.0, 0.0, 1.1058638, 0.0, 3.446559, 2.34582, 0.0, 0.91726255, 1.2871017, 0.0, 0.0, 1.6924571, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 21, 21, 23, 23, 24, 24, 26, 26, 27, 27, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, -1, -1, -1, -1, 26, -1, 28, 30, -1, 32, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.6783541, 1.5958704, -0.010666268, 1.0516728, 0.018231919, 1.0, -0.013958551, 0.0, 0.53773886, 1.0, 1.0, -0.0038657666, 0.027468523, 0.415145, 1.0, 1.3051435, 0.49567464, 0.020468192, 0.005383459, 0.0059389956, -0.008516041, 0.13467944, -0.019470463, 0.39416724, 0.6317207, 0.0056363107, 1.0, 0.27227256, 0.02224837, -0.014829764, 1.0, -0.0018350994, -0.015118919, -0.00827468, 0.007326311, 0.011301811, -0.007889777], "split_indices": [138, 138, 0, 142, 0, 42, 0, 0, 143, 106, 39, 0, 0, 142, 97, 138, 143, 0, 0, 0, 0, 139, 0, 142, 141, 0, 108, 139, 0, 0, 105, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1954.0, 115.0, 1864.0, 90.0, 1765.0, 99.0, 1488.0, 277.0, 382.0, 1106.0, 159.0, 118.0, 180.0, 202.0, 467.0, 639.0, 88.0, 92.0, 110.0, 92.0, 311.0, 156.0, 305.0, 334.0, 96.0, 215.0, 212.0, 93.0, 150.0, 184.0, 127.0, 88.0, 111.0, 101.0, 95.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0012225109, -0.0035447234, 0.009809078, 0.003490398, -0.0148885725, -0.0037192903, 0.01503059, 0.010710268, -0.009442492, -0.015783304, 0.009014553, -0.0053583323, -0.018115625, -0.021975731, 0.0995894, -0.004932285, -0.07622978, -0.007324382, 0.023906887, 0.007539687, -0.013119352, 0.0011214826, -0.13578667, -0.0016369075, 0.02177809, -0.017802633, -0.008922699], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, 9, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, -1, -1, 25, -1, -1, -1, -1], "loss_changes": [0.95498973, 2.0153317, 0.0, 1.9899691, 0.0, 1.1365875, 0.0, 0.0, 1.0760229, 2.761861, 0.0, 2.6281447, 0.0, 1.2030033, 4.965978, 1.5589795, 1.619666, 0.0, 0.0, 4.5289736, 0.0, 0.0, 0.36383247, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, 10, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, -1, -1, 26, -1, -1, -1, -1], "split_conditions": [1.3934005, 1.6076417, 0.009809078, 1.0, -0.0148885725, 1.1550102, 0.01503059, 0.010710268, 1.5407921, 1.492255, 0.009014553, 1.4224148, -0.018115625, 1.3546785, 1.0, 0.6699145, 1.0, -0.007324382, 0.023906887, 0.47112516, -0.013119352, 0.0011214826, 0.57154363, -0.0016369075, 0.02177809, -0.017802633, -0.008922699], "split_indices": [139, 138, 0, 114, 0, 138, 0, 0, 138, 138, 0, 138, 0, 138, 109, 143, 53, 0, 0, 139, 0, 0, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1971.0, 97.0, 1880.0, 91.0, 1792.0, 88.0, 88.0, 1704.0, 1602.0, 102.0, 1507.0, 95.0, 1301.0, 206.0, 990.0, 311.0, 92.0, 114.0, 901.0, 89.0, 126.0, 185.0, 809.0, 92.0, 97.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0018844514, 0.010822742, -0.0037438774, -0.008494033, 0.003015031, -0.012092612, 0.08829298, 0.0009753391, -0.011748181, -0.0034553502, 0.027178967, -0.011837058, 0.018553147, 0.01077046, -0.011708555, -0.012722946, -0.0013289816, 0.030804744, -0.065760806, 0.111165784, 0.0029131055, -0.10876901, 0.00544951, 0.026611287, -0.0010872162, 0.010506771, -0.017771084, -0.02780123, -0.028405085, 0.0017169857, -0.008594855, 0.005784335, -0.01255478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, 13, -1, 15, -1, -1, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.2383657, 0.0, 1.0783908, 0.0, 2.3370647, 2.1222975, 4.596097, 3.241876, 0.0, 2.298865, 0.0, 1.5544914, 0.0, 0.0, 0.0, 0.0, 2.4327607, 1.757258, 2.0222485, 3.819705, 1.2297578, 4.0873475, 0.0, 0.0, 0.0, 0.0, 1.1529777, 1.6491787, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 26, 26, 27, 27], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, 14, -1, 16, -1, -1, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010822742, -0.5, -0.008494033, 1.0, 0.9044961, 0.71012646, 0.90635896, -0.011748181, 0.3984236, 0.027178967, -0.3846154, 0.018553147, 0.01077046, -0.011708555, -0.012722946, 1.0, 0.0, 0.5369919, 1.0, 1.0, 0.36692774, 0.00544951, 0.026611287, -0.0010872162, 0.010506771, 0.49346045, 0.6923077, -0.028405085, 0.0017169857, -0.008594855, 0.005784335, -0.01255478], "split_indices": [1, 0, 1, 0, 42, 141, 142, 143, 0, 142, 0, 1, 0, 0, 0, 0, 109, 0, 141, 106, 89, 140, 0, 0, 0, 0, 143, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 104.0, 1965.0, 151.0, 1814.0, 1541.0, 273.0, 1371.0, 170.0, 182.0, 91.0, 1282.0, 89.0, 92.0, 90.0, 107.0, 1175.0, 784.0, 391.0, 202.0, 582.0, 288.0, 103.0, 89.0, 113.0, 98.0, 484.0, 197.0, 91.0, 320.0, 164.0, 105.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0041999533, 0.011221862, -0.015259606, 0.0035425988, 0.015004503, -0.0019779739, 0.011483236, 0.006761154, -0.009074335, -0.020604648, 0.04458086, -0.0017191495, -0.015296454, 0.12968622, -0.02336798, -0.01766985, 0.01244994, 0.03975632, 0.029273668, 0.0070927874, -0.089929774, -0.041092772, 0.005921243, 0.02125, -0.01036535, -0.0019535664, -0.019632084, -0.024438916, -0.012544814, -0.007840773, 0.00051453075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [2.2603676, 2.0948071, 0.0, 1.1439819, 0.0, 1.3761487, 0.0, 1.6714711, 0.0, 2.342202, 3.9207458, 1.6508845, 0.0, 4.4135957, 2.366241, 1.3109881, 0.0, 4.8059897, 0.0, 0.0, 1.6551356, 0.7839016, 0.0, 0.0, 0.0, 0.0, 0.0, 0.74402773, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 21, 21, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.4310507, 1.1865025, -0.015259606, 1.0, 0.015004503, 0.95005274, 0.011483236, 1.0, -0.009074335, 1.3790498, 1.0, 0.46203575, -0.015296454, 1.4224148, -0.15384616, 1.0, 0.01244994, 1.3109853, 0.029273668, 0.0070927874, 0.6003935, 0.3954113, 0.005921243, 0.02125, -0.01036535, -0.0019535664, -0.019632084, 0.14816526, -0.012544814, -0.007840773, 0.00051453075], "split_indices": [143, 139, 0, 114, 0, 143, 0, 71, 0, 138, 121, 139, 0, 138, 1, 62, 0, 138, 0, 0, 139, 141, 0, 0, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1965.0, 88.0, 1862.0, 103.0, 1774.0, 88.0, 1615.0, 159.0, 937.0, 678.0, 820.0, 117.0, 301.0, 377.0, 728.0, 92.0, 194.0, 107.0, 156.0, 221.0, 558.0, 170.0, 88.0, 106.0, 133.0, 88.0, 466.0, 92.0, 165.0, 301.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0018913443, 0.016222363, -0.028394291, -0.002517682, 0.015931766, -0.08629555, 0.07275271, 0.007965312, -0.011322597, -0.13810131, 0.004673107, 0.022966988, -0.008286762, -0.0052302857, 0.014538616, -0.021107046, -0.007024463, 0.0051668673, -0.011664527, -0.010095363, 0.0091977585, 0.049291547, -0.033396482, 0.013617125, -0.003309435, 0.009184676, -0.060027156, -0.08419027, 0.002833402, -0.005088672, -0.01859109], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, -1, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1], "loss_changes": [0.89452344, 3.7515764, 3.8770285, 1.4356061, 0.0, 2.9013405, 5.885101, 2.0490863, 0.0, 1.5002866, 0.0, 0.0, 0.0, 1.1943092, 0.0, 0.0, 0.0, 1.2494045, 0.0, 1.1097927, 0.0, 1.6176318, 1.9211395, 0.0, 0.0, 0.0, 1.0141639, 1.2635956, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 17, 17, 19, 19, 21, 21, 22, 22, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, -1, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1], "split_conditions": [0.6558667, 1.4079123, -0.07692308, 0.602346, 0.015931766, 1.0, 0.15384616, 0.5260094, -0.011322597, 1.0, 0.004673107, 0.022966988, -0.008286762, 1.3520192, 0.014538616, -0.021107046, -0.007024463, 0.43039754, -0.011664527, -0.07692308, 0.0091977585, 0.24635093, 0.09691013, 0.013617125, -0.003309435, 0.009184676, 1.3846154, 0.33651313, 0.002833402, -0.005088672, -0.01859109], "split_indices": [141, 138, 1, 142, 0, 125, 1, 139, 0, 69, 0, 0, 0, 138, 0, 0, 0, 141, 0, 1, 0, 139, 139, 0, 0, 0, 1, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1399.0, 662.0, 1237.0, 162.0, 421.0, 241.0, 1130.0, 107.0, 303.0, 118.0, 120.0, 121.0, 1031.0, 99.0, 146.0, 157.0, 943.0, 88.0, 802.0, 141.0, 226.0, 576.0, 110.0, 116.0, 101.0, 475.0, 373.0, 102.0, 281.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039536892, 0.0018734456, -0.010331903, -0.0059080212, 0.01655466, 0.005207496, -0.090683065, -0.0050593633, 0.010746816, 0.0032622912, -0.01922292, -0.012392622, 0.008351362, -0.03413081, 0.015942724, -0.089089654, 0.016014107, -0.008353907, 0.014586578, -0.022998238, -0.15843144, -0.0063613798, 0.058959503, 0.028838893, -0.010516073, -0.011461855, 0.006862204, -0.009040035, -0.022426795, -0.0071869455, 0.017240562, -0.03039278, 0.015863355, 0.007094469, -0.008476901], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.2020359, 2.4975638, 0.0, 1.7640206, 0.0, 1.737578, 2.717111, 0.9768911, 0.0, 0.0, 0.0, 0.85556734, 0.0, 2.1661425, 1.9034863, 1.718586, 1.4054769, 1.8290627, 0.0, 1.6117015, 0.8196435, 0.0, 3.9628243, 2.82148, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3886081, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 22, 22, 23, 23, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.6783541, 1.5958704, -0.010331903, 0.8578416, 0.01655466, 0.71012646, 1.0, 3.5, 0.010746816, 0.0032622912, -0.01922292, 1.0, 0.008351362, 1.0, 0.6249582, 0.30599797, 1.0, 0.49858245, 0.014586578, 0.16173926, 1.0, -0.0063613798, 0.31366187, 1.0, -0.010516073, -0.011461855, 0.006862204, -0.009040035, -0.022426795, -0.0071869455, 0.017240562, 1.2267936, 0.015863355, 0.007094469, -0.008476901], "split_indices": [138, 138, 0, 142, 0, 142, 39, 1, 0, 0, 0, 39, 0, 13, 142, 142, 53, 140, 0, 143, 59, 0, 142, 93, 0, 0, 0, 0, 0, 0, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1961.0, 115.0, 1872.0, 89.0, 1655.0, 217.0, 1504.0, 151.0, 98.0, 119.0, 1389.0, 115.0, 786.0, 603.0, 375.0, 411.0, 508.0, 95.0, 192.0, 183.0, 144.0, 267.0, 367.0, 141.0, 96.0, 96.0, 90.0, 93.0, 124.0, 143.0, 252.0, 115.0, 88.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008779841, 0.00855047, -0.004713342, -0.061431717, 0.005047181, -0.00034929626, -0.011589413, -0.0071857707, 0.06858685, 0.032964222, -0.06304369, 0.016637892, -0.006180256, -3.934722e-05, 0.11200982, -0.1118733, 0.048993114, 0.02976187, -0.011240462, 0.02216319, -0.0064799944, -0.036367144, -0.02388609, -0.004933038, 0.014731663, 0.063365825, -0.006438473, -0.014800735, 0.0034997778, -0.0056973244, 0.116622284, 0.005441533, 0.017830202], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, 5, 7, -1, -1, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.68580836, 0.0, 1.0972375, 0.91824543, 1.3143766, 0.0, 0.0, 3.180142, 3.4810367, 2.1522496, 3.2441328, 0.0, 0.0, 1.9488981, 4.709888, 3.9599862, 1.7401524, 1.455301, 0.0, 0.0, 0.0, 2.0635035, 0.0, 0.0, 0.0, 2.1725945, 0.0, 0.0, 0.0, 0.0, 0.90167284, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 21, 21, 25, 25, 30, 30], "right_children": [2, -1, 4, 6, 8, -1, -1, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.1550102, 0.00855047, 0.22029229, 1.0, 1.0, -0.00034929626, -0.011589413, 1.0, 1.0, 1.4444497, 1.0, 0.016637892, -0.006180256, 0.6312413, 1.0, 0.71545964, 0.8025691, 1.3358374, -0.011240462, 0.02216319, -0.0064799944, 1.3208424, -0.02388609, -0.004933038, 0.014731663, 0.2243912, -0.006438473, -0.014800735, 0.0034997778, -0.0056973244, 0.3403216, 0.005441533, 0.017830202], "split_indices": [138, 0, 139, 59, 62, 0, 0, 121, 124, 138, 58, 0, 0, 143, 126, 139, 142, 138, 0, 0, 0, 138, 0, 0, 0, 142, 0, 0, 0, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 88.0, 1982.0, 291.0, 1691.0, 141.0, 150.0, 1418.0, 273.0, 825.0, 593.0, 156.0, 117.0, 582.0, 243.0, 413.0, 180.0, 460.0, 122.0, 150.0, 93.0, 259.0, 154.0, 90.0, 90.0, 339.0, 121.0, 101.0, 158.0, 104.0, 235.0, 117.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0028374735, 0.011118363, -0.050822675, 0.0028048554, 0.017115334, -0.01763498, 0.012283845, 0.00915126, -0.010283632, -0.0054619135, 0.008294545, 0.021142393, -0.035050645, 0.009011059, 0.01599921, -0.118017435, 0.0061043785, 0.023565747, -0.009327604, -0.016925829, -0.006270058, 0.0009328616, 0.10796313, 0.077891305, -0.03438079, 0.022077274, 0.0010848753, -0.0013363095, 0.0200701, -0.0938886, -0.00061989855, -0.0044197864, -0.013790038, -0.0058792196, 0.009385539], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.9140355, 2.3708646, 2.178435, 1.1357281, 0.0, 0.0, 0.8651275, 0.8469894, 0.0, 0.0, 0.0, 2.1173306, 2.7186725, 1.7210027, 0.0, 0.51870966, 0.0, 1.933078, 0.0, 0.0, 0.0, 2.1687117, 2.3444622, 2.8129387, 1.098943, 0.0, 0.0, 0.0, 0.0, 0.43302107, 1.9180497, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21, 22, 22, 23, 23, 24, 24, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.5459449, 0.87652975, 1.1347722, 1.0, 0.017115334, -0.01763498, 1.17751, 1.0, -0.010283632, -0.0054619135, 0.008294545, 0.77939034, 1.0, 0.63804257, 0.01599921, 1.0, 0.0061043785, 0.47833824, -0.009327604, -0.016925829, -0.006270058, 1.2186605, 0.5431552, 0.21544637, 0.285204, 0.022077274, 0.0010848753, -0.0013363095, 0.0200701, 0.22842844, 0.5, -0.0044197864, -0.013790038, -0.0058792196, 0.009385539], "split_indices": [138, 139, 139, 117, 0, 0, 142, 0, 0, 0, 0, 142, 106, 139, 0, 71, 0, 139, 0, 0, 0, 138, 139, 142, 139, 0, 0, 0, 0, 142, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1782.0, 275.0, 1694.0, 88.0, 92.0, 183.0, 1598.0, 96.0, 94.0, 89.0, 1257.0, 341.0, 1156.0, 101.0, 183.0, 158.0, 1012.0, 144.0, 95.0, 88.0, 798.0, 214.0, 251.0, 547.0, 99.0, 115.0, 144.0, 107.0, 198.0, 349.0, 93.0, 105.0, 216.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0034150614, 0.009133031, -0.011776897, 0.0002846819, 0.017126597, 0.0067345095, -0.0110301655, -0.01617628, 0.036851842, 0.0041711093, -0.015161845, 0.111241326, -0.018050643, -0.015864888, 0.016425256, 0.010171888, 0.030786728, 0.03431853, -0.009857205, -0.052697722, 0.024871416, 0.01937533, -0.02030792, -0.06385884, 0.023290459, -0.017790232, -0.01739149, -0.0074641854, 0.09334387, 0.0012794873, -0.013880916, -0.04125457, 0.0033641667, 0.018766863, 0.00072931655, 0.001968082, -0.0122501785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, 19, -1, 21, -1, 23, -1, 25, 27, -1, -1, 29, -1, 31, -1, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [1.4302002, 2.8276138, 0.0, 1.3330877, 0.0, 1.2185609, 0.0, 2.764163, 3.1162198, 2.7968457, 0.0, 6.438812, 1.8511928, 1.1628361, 0.0, 8.377872, 0.0, 5.1861115, 0.0, 1.7221742, 2.5075219, 0.0, 0.0, 1.0226495, 0.0, 0.3813537, 0.0, 0.0, 1.7694429, 0.0, 0.0, 1.0743307, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 23, 23, 25, 25, 28, 28, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, 20, -1, 22, -1, 24, -1, 26, 28, -1, -1, 30, -1, 32, -1, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1865025, -0.011776897, 1.5459449, 0.017126597, 1.0, -0.0110301655, 1.3790498, 1.0, 0.46203575, -0.015161845, 1.4224148, 0.6692429, 1.0, 0.016425256, 0.5246115, 0.030786728, 0.52666736, -0.009857205, 0.3714244, 0.22743548, 0.01937533, -0.02030792, 0.40907693, 0.023290459, 0.88461536, -0.01739149, -0.0074641854, 0.31032252, 0.0012794873, -0.013880916, 0.20498092, 0.0033641667, 0.018766863, 0.00072931655, 0.001968082, -0.0122501785], "split_indices": [143, 139, 0, 138, 0, 71, 0, 138, 121, 139, 0, 138, 141, 115, 0, 141, 0, 141, 0, 140, 139, 0, 0, 141, 0, 1, 0, 0, 139, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1971.0, 93.0, 1869.0, 102.0, 1766.0, 103.0, 1003.0, 763.0, 872.0, 131.0, 324.0, 439.0, 775.0, 97.0, 214.0, 110.0, 266.0, 173.0, 407.0, 368.0, 115.0, 99.0, 178.0, 88.0, 316.0, 91.0, 150.0, 218.0, 88.0, 90.0, 217.0, 99.0, 104.0, 114.0, 124.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037742383, 0.0021465763, -0.010541491, -0.0049739755, 0.014985849, 0.005058379, -0.07331153, -0.004120613, 0.011737045, -0.0150434375, -0.0001786282, 0.0047768624, -0.008274407, -0.005122208, 0.013575722, 0.0060249385, -0.012742236, -0.0069476394, 0.014711847, 0.0054336954, -0.01034189, -0.0014389344, 0.011084407], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [1.2463192, 2.0583544, 0.0, 1.2799902, 0.0, 1.6783245, 1.3183789, 1.0528234, 0.0, 0.0, 0.0, 1.7529818, 0.0, 1.7136655, 0.0, 2.10856, 0.0, 1.2601377, 0.0, 1.9537332, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.6783541, 1.5958704, -0.010541491, 0.82126033, 0.014985849, 0.71012646, 1.473343, 0.6558667, 0.011737045, -0.0150434375, -0.0001786282, 1.4331764, -0.008274407, 0.6588078, 0.013575722, 0.56020635, -0.012742236, 0.4564035, 0.014711847, 0.43434185, -0.01034189, -0.0014389344, 0.011084407], "split_indices": [138, 138, 0, 142, 0, 142, 138, 141, 0, 0, 0, 138, 0, 143, 0, 143, 0, 143, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1957.0, 114.0, 1867.0, 90.0, 1628.0, 239.0, 1505.0, 123.0, 115.0, 124.0, 1352.0, 153.0, 1257.0, 95.0, 1152.0, 105.0, 1055.0, 97.0, 935.0, 120.0, 787.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.00019077398, 0.0069730603, -0.0576142, 0.013006488, -0.008798545, -0.00029651876, -0.011976403, 0.0013742965, 0.014448299, 0.010331762, -0.009616253, -0.00044604478, 0.014490606, 0.011052515, -0.016092049, 0.0012298573, 0.014139584, 0.009810887, -0.008521243, 0.02398001, -0.046901796, -0.0019932091, 0.010272625, -0.009837431, 0.00523666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, -1, 19, 21, 23, -1, -1, -1, -1], "loss_changes": [0.8138954, 1.0644948, 0.7404207, 2.6717913, 0.0, 0.0, 0.0, 1.4022608, 0.0, 2.1321118, 0.0, 2.5113518, 0.0, 1.6260034, 0.0, 1.1156639, 0.0, 0.0, 1.3384776, 2.0090477, 2.5139203, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 18, 18, 19, 19, 20, 20], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, -1, 20, 22, 24, -1, -1, -1, -1], "split_conditions": [1.0, 1.6783541, 0.30790225, 1.0084468, -0.008798545, -0.00029651876, -0.011976403, 1.492255, 0.014448299, 0.76372665, -0.009616253, 0.8025691, 0.014490606, 1.0, -0.016092049, 0.1535796, 0.014139584, 0.009810887, 0.1923077, -0.1923077, 1.0, -0.0019932091, 0.010272625, -0.009837431, 0.00523666], "split_indices": [40, 138, 140, 139, 0, 0, 0, 138, 0, 139, 0, 142, 0, 88, 0, 142, 0, 0, 1, 1, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1858.0, 218.0, 1747.0, 111.0, 116.0, 102.0, 1605.0, 142.0, 1470.0, 135.0, 1361.0, 109.0, 1270.0, 91.0, 1181.0, 89.0, 108.0, 1073.0, 581.0, 492.0, 373.0, 208.0, 324.0, 168.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036032938, 0.0031179832, -0.06649285, -0.009596246, 0.020177783, -0.020838363, 0.009200214, 0.0009292049, -0.016805742, 0.007910221, -0.010780538, -0.014503395, 0.03316805, 0.0450299, -0.05224751, -0.0098801255, 0.056772325, -0.0031476412, 0.012010175, -0.008441811, -0.021704035, 0.084355704, -0.010205943, -0.008402797, 0.030364867, -0.0031542534, 0.12579083, 0.00727216, -0.0043647923, 0.07455494, 0.029929414, 0.01676662, -0.0029122112], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, 21, -1, -1, 23, -1, 25, -1, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.871181, 4.703053, 4.4753075, 2.9187822, 0.0, 0.0, 0.0, 1.2456465, 0.0, 0.8729561, 0.0, 1.8358247, 2.2584033, 1.8206801, 3.6094317, 0.0, 2.694387, 0.0, 0.0, 1.1586331, 0.0, 2.5163836, 0.0, 0.0, 0.8182196, 0.0, 3.431384, 0.0, 0.0, 2.8767443, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 19, 19, 21, 21, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, 22, -1, -1, 24, -1, 26, -1, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0517951, 1.0, 1.2583088, 0.9731367, 0.020177783, -0.020838363, 0.009200214, 1.0, -0.016805742, 1.0, -0.010780538, -0.07692308, 0.21544637, 0.42670286, 0.44427505, -0.0098801255, 0.7544639, -0.0031476412, 0.012010175, 0.42307693, -0.021704035, 1.0, -0.010205943, -0.008402797, 0.29402548, -0.0031542534, 1.4148873, 0.00727216, -0.0043647923, 0.4564035, 0.029929414, 0.01676662, -0.0029122112], "split_indices": [141, 125, 140, 140, 0, 0, 0, 84, 0, 39, 0, 1, 142, 140, 140, 0, 141, 0, 0, 1, 0, 5, 0, 0, 142, 0, 138, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1862.0, 199.0, 1750.0, 112.0, 105.0, 94.0, 1641.0, 109.0, 1542.0, 99.0, 817.0, 725.0, 317.0, 500.0, 110.0, 615.0, 157.0, 160.0, 395.0, 105.0, 524.0, 91.0, 134.0, 261.0, 138.0, 386.0, 166.0, 95.0, 298.0, 88.0, 157.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033318668, 0.005923834, -0.0118186725, -0.0017523043, 0.0152659295, 0.0121975755, -0.048993208, 0.06386374, -0.0026522926, -0.020232817, -0.011473122, 0.00420168, 0.018728228, 0.01045184, -0.011811036, 0.0028732663, -0.008318846, -0.0053952746, 0.00823871, -0.0036737134, 0.013447423, -0.027516143, 0.009279219, 0.0043907887, -0.12180294, 0.07518639, -0.08127782, -0.01946963, -0.0050529405, 0.019397398, -0.0040346747, 0.00023812885, -0.015949572], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [2.1952238, 2.1524777, 0.0, 1.196753, 0.0, 1.0756646, 0.782729, 2.304745, 1.6476332, 0.8878044, 0.0, 0.959381, 0.0, 1.713344, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0193837, 0.0, 2.1179156, 0.0, 3.1901693, 0.92477465, 3.952485, 1.5573863, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.6376648, 1.0194759, -0.0118186725, 1.0, 0.0152659295, 0.29891056, 0.3714244, 0.33089325, 1.3461539, 1.0, -0.011473122, 0.23212868, 0.018728228, 0.84615386, -0.011811036, 0.0028732663, -0.008318846, -0.0053952746, 0.00823871, 1.0, 0.013447423, 1.0, 0.009279219, 1.0, -0.15384616, 1.0, 0.470765, -0.01946963, -0.0050529405, 0.019397398, -0.0040346747, 0.00023812885, -0.015949572], "split_indices": [138, 139, 0, 80, 0, 143, 140, 140, 1, 116, 0, 142, 0, 1, 0, 0, 0, 0, 0, 42, 0, 0, 0, 122, 1, 121, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1911.0, 154.0, 1816.0, 95.0, 1402.0, 414.0, 313.0, 1089.0, 288.0, 126.0, 211.0, 102.0, 978.0, 111.0, 162.0, 126.0, 121.0, 90.0, 878.0, 100.0, 704.0, 174.0, 526.0, 178.0, 288.0, 238.0, 88.0, 90.0, 142.0, 146.0, 115.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-1.7881394E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}