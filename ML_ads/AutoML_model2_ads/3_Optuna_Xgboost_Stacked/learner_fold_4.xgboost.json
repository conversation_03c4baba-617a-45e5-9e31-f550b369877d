{"learner": {"attributes": {"best_iteration": "26", "best_score": "0.670018"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "77"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.00012998824, -0.14160635, 0.43622035, -0.23684376, 0.04401676, 0.013542853, 0.5809161, -0.3564595, -0.16895866, -0.014091295, 0.10848336, 0.47024295, 0.080617994, -0.04433091, -0.3207364, -0.09291308, -0.23944534, 0.03110965, 0.046555087, 0.06482051, 0.03127027, -0.022016646, -0.03760205, -0.027052062, -0.022588959, -0.014132636, -0.034226466, -0.040586244, 0.017266208, 0.005850818, -0.014081902, -0.017134381, 0.008726556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, 17, 19, -1, -1, 21, 23, 25, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [128.00821, 27.63113, 22.10984, 8.388088, 6.3185487, 0.0, 8.551201, 1.160347, 3.5323715, 0.0, 4.93116, 6.44833, 0.0, 0.0, 1.4733772, 2.7762752, 3.4502735, 0.0, 3.307729, 0.0, 0.0, 0.0, 0.0, 2.063593, 0.0, 0.0, 0.0, 2.9757316, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 14, 14, 15, 15, 16, 16, 18, 18, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, 18, 20, -1, -1, 22, 24, 26, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.7633737, 0.4666329, 0.7982205, 0.22502147, 0.44131443, 0.013542853, 1.3159062, 0.13580762, 1.2622243, -0.014091295, 1.0, 1.0278977, 0.080617994, -0.04433091, 0.16706225, 1.0, 1.0, 0.03110965, 1.0, 0.06482051, 0.03127027, -0.022016646, -0.03760205, 1.0, -0.022588959, -0.014132636, -0.034226466, 1.0, 0.017266208, 0.005850818, -0.014081902, -0.017134381, 0.008726556], "split_indices": [139, 139, 142, 142, 140, 0, 139, 142, 138, 0, 17, 142, 0, 0, 142, 109, 97, 0, 97, 0, 0, 0, 0, 15, 0, 0, 0, 126, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1563.0, 508.0, 1033.0, 530.0, 165.0, 343.0, 374.0, 659.0, 137.0, 393.0, 230.0, 113.0, 109.0, 265.0, 317.0, 342.0, 92.0, 301.0, 108.0, 122.0, 94.0, 171.0, 212.0, 105.0, 175.0, 167.0, 178.0, 123.0, 121.0, 91.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00018369514, -0.1280609, 0.3920575, -0.21134557, 0.0320656, 0.2101606, 0.6130463, -0.0428844, -0.18367706, -0.013557054, 0.091080375, 0.03276833, 0.008474455, 0.081744134, 0.035603467, -0.10490715, -0.2350863, 0.020295357, 0.004517831, -0.021126442, -0.029591935, -0.18168765, -0.32077843, -0.013491707, 0.011561233, -0.013319135, 0.008775023, -0.2647702, -0.0019166498, -0.04252894, -0.02439067, -0.017050134, -0.035017617], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, -1, 17, -1, -1, -1, -1, 19, 21, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [103.026, 20.657589, 20.299568, 6.1321945, 5.2432947, 4.0827646, 11.977272, 0.0, 3.6607494, 0.0, 3.7961392, 0.0, 0.0, 0.0, 0.0, 2.8596833, 2.502985, 0.0, 3.4233892, 0.0, 2.5407255, 4.5504, 1.6871243, 0.0, 0.0, 0.0, 0.0, 1.7953987, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 15, 15, 16, 16, 18, 18, 20, 20, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, -1, 18, -1, -1, -1, -1, 20, 22, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.7633737, 0.4666329, 0.9782575, 0.097455814, 0.44131443, 1.0, 1.1391696, -0.0428844, 0.03846154, -0.013557054, 1.0, 0.03276833, 0.008474455, 0.081744134, 0.035603467, 1.0, 1.2622243, 0.020295357, -0.1923077, -0.021126442, 1.0, 0.33120996, 0.36545306, -0.013491707, 0.011561233, -0.013319135, 0.008775023, 1.0, -0.0019166498, -0.04252894, -0.02439067, -0.017050134, -0.035017617], "split_indices": [139, 139, 141, 143, 140, 108, 143, 0, 1, 0, 93, 0, 0, 0, 0, 5, 138, 0, 1, 0, 108, 140, 139, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1549.0, 505.0, 1019.0, 530.0, 277.0, 228.0, 115.0, 904.0, 138.0, 392.0, 143.0, 134.0, 127.0, 101.0, 357.0, 547.0, 171.0, 221.0, 148.0, 209.0, 337.0, 210.0, 98.0, 123.0, 111.0, 98.0, 223.0, 114.0, 89.0, 121.0, 106.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.005366005, -0.13050783, 0.31089377, -0.19688164, 0.035739783, 0.21397504, 0.0696926, -0.283792, -0.14865465, 0.019541165, -0.045516714, 0.08882576, 0.34019396, -0.33170456, -0.021144718, 0.0034931817, -0.17795369, -0.02673596, 0.06422469, 0.023706546, -0.010931636, 0.024947243, 0.04390156, -0.026843483, -0.040452443, -0.09327238, -0.25573912, 0.021911398, -0.0072077857, -0.022415092, -0.02678327, -0.31707978, -0.0118852565, 0.009550312, -0.013783931, -0.023480453, -0.039053984], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1], "loss_changes": [82.08337, 16.397251, 21.999283, 4.451294, 5.5011363, 7.424217, 0.0, 1.3136978, 3.6737938, 0.0, 6.8410435, 6.9319177, 2.097866, 1.0504589, 0.0, 0.0, 3.8797264, 0.0, 3.9690194, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.453963, 2.5777874, 0.0, 0.0, 0.0, 2.5395799, 1.2813148, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 18, 18, 25, 25, 26, 26, 30, 30, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1], "split_conditions": [0.70066047, 0.49934718, 1.3159062, 0.26347196, 0.51125157, 0.8327316, 0.0696926, 0.20138733, 0.19540305, 0.019541165, 0.52425903, 1.0, -0.1923077, 0.15612534, -0.021144718, 0.0034931817, 1.0, -0.02673596, 0.61198467, 0.023706546, -0.010931636, 0.024947243, 0.04390156, -0.026843483, -0.040452443, 0.30904695, 0.43578738, 0.021911398, -0.0072077857, -0.022415092, 1.2917116, 0.30958152, -0.0118852565, 0.009550312, -0.013783931, -0.023480453, -0.039053984], "split_indices": [140, 139, 139, 141, 141, 139, 0, 143, 143, 0, 140, 108, 1, 142, 0, 0, 97, 0, 140, 0, 0, 0, 0, 0, 0, 139, 142, 0, 0, 0, 138, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1486.0, 588.0, 1062.0, 424.0, 470.0, 118.0, 379.0, 683.0, 143.0, 281.0, 236.0, 234.0, 228.0, 151.0, 94.0, 589.0, 93.0, 188.0, 135.0, 101.0, 122.0, 112.0, 122.0, 106.0, 282.0, 307.0, 88.0, 100.0, 95.0, 187.0, 212.0, 95.0, 89.0, 98.0, 100.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016163765, -0.10129729, 0.30451828, -0.17211601, 0.033974424, 0.15224192, 0.4916773, -0.24878971, -0.12791309, 0.019071924, -0.008097214, 0.025716025, 0.0049555856, 0.031499404, 0.07284693, -0.29955012, -0.01334656, -0.052378878, -0.18304901, -0.08154402, 0.09414407, -0.023244768, -0.03583863, 0.0050169374, -0.012929009, -0.13457671, -0.029500712, -0.018695613, 0.005668504, 9.8473e-05, 0.018730337, -0.0036597725, -0.029754171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, -1, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [62.862507, 14.88696, 14.420944, 3.4569874, 3.5214694, 3.005847, 9.49704, 2.1835022, 2.6945257, 0.0, 3.1614134, 0.0, 0.0, 0.0, 0.0, 1.022541, 0.0, 2.1531816, 2.0296469, 3.5699, 1.5274436, 0.0, 0.0, 0.0, 0.0, 4.1674247, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, -1, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7633737, 0.4666329, 0.9782575, 0.26347196, 1.3358374, 1.0, 1.0, 1.0, 0.03846154, 0.019071924, 1.0, 0.025716025, 0.0049555856, 0.031499404, 0.07284693, 0.17301962, -0.01334656, 1.0, 0.37509307, 1.0, 0.5930298, -0.023244768, -0.03583863, 0.0050169374, -0.012929009, 1.0, -0.029500712, -0.018695613, 0.005668504, 9.8473e-05, 0.018730337, -0.0036597725, -0.029754171], "split_indices": [139, 139, 141, 141, 138, 124, 0, 83, 1, 0, 97, 0, 0, 0, 0, 141, 0, 59, 139, 12, 141, 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1554.0, 506.0, 1020.0, 534.0, 279.0, 227.0, 373.0, 647.0, 113.0, 421.0, 138.0, 141.0, 130.0, 97.0, 259.0, 114.0, 273.0, 374.0, 245.0, 176.0, 121.0, 138.0, 117.0, 156.0, 261.0, 113.0, 139.0, 106.0, 88.0, 88.0, 163.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0021144757, -0.09614537, 0.30130804, -0.16778578, 0.011487721, 0.0077106687, 0.40983447, -0.23715277, -0.12162339, 0.10109459, -0.104902, 0.056228723, 0.2668029, -0.18187149, -0.28735718, -0.056290295, -0.17397362, 0.030416671, 0.02274391, -0.021837927, 0.00028605733, 0.012200372, 0.041160207, -0.023836566, -0.012663272, -0.025157211, -0.03224193, 0.0028499775, -0.014814623, -0.11791707, -0.029108182, -0.006433169, 0.018417623, -0.018716868, -0.002834159], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [60.884735, 12.02126, 12.4578705, 2.9972076, 6.4974656, 0.0, 7.522911, 1.037983, 1.9221535, 5.6006145, 3.3139522, 0.0, 3.732091, 0.5554781, 0.24592018, 1.9471184, 2.0481787, 0.0, 3.5704312, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3088841, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.7633737, 0.44131443, 0.7982205, 0.22502147, 1.0, 0.0077106687, 1.0, 1.0, 0.32285196, 0.48257783, 0.511963, 0.056228723, 1.1081982, 0.24013717, 1.0, 1.2614771, 1.0, 0.030416671, 0.7801911, -0.021837927, 0.00028605733, 0.012200372, 0.041160207, -0.023836566, -0.012663272, -0.025157211, -0.03224193, 0.0028499775, -0.014814623, 0.5025122, -0.029108182, -0.006433169, 0.018417623, -0.018716868, -0.002834159], "split_indices": [139, 140, 142, 142, 122, 0, 69, 23, 143, 141, 139, 0, 142, 141, 26, 138, 23, 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1559.0, 512.0, 936.0, 623.0, 167.0, 345.0, 374.0, 562.0, 352.0, 271.0, 167.0, 178.0, 178.0, 196.0, 250.0, 312.0, 98.0, 254.0, 132.0, 139.0, 89.0, 89.0, 88.0, 90.0, 97.0, 99.0, 130.0, 120.0, 211.0, 101.0, 165.0, 89.0, 119.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0018938857, -0.08570151, 0.2729895, -0.15913814, 0.006809563, 0.14668345, 0.42577022, -0.21808246, -0.11516567, 0.07744915, -0.07945223, 0.0041715396, 0.029022947, 0.06098447, 0.019248765, -0.011733508, -0.25807953, -0.0016214952, -0.15102519, 0.02305496, 0.025332367, 0.0051958296, -0.1369822, -0.2965031, -0.018123237, -0.2295009, -0.0028098177, 0.015322113, -0.007634464, -0.023123829, -0.00064737507, -0.032300636, -0.02693974, -0.032655194, -0.015263644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, 23, -1, 25, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [49.203224, 10.638937, 9.764351, 2.2627506, 4.222794, 4.1737657, 9.833569, 1.5030384, 1.7741623, 3.6448555, 2.3587337, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7883816, 0.0, 3.540369, 3.7650938, 0.0, 0.0, 2.669363, 0.12787151, 0.0, 1.6709881, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 16, 16, 18, 18, 19, 19, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, 24, -1, 26, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7633737, 0.4156466, 0.9782575, 0.22502147, 1.0, 1.0, 1.1391696, 1.176989, 1.2276405, 0.7927253, 0.44536373, 0.0041715396, 0.029022947, 0.06098447, 0.019248765, -0.011733508, 0.27175885, -0.0016214952, 1.0, 0.50760657, 0.025332367, 0.0051958296, 0.63386166, 0.18572555, -0.018123237, 0.3474657, -0.0028098177, 0.015322113, -0.007634464, -0.023123829, -0.00064737507, -0.032300636, -0.02693974, -0.032655194, -0.015263644], "split_indices": [139, 140, 141, 142, 122, 12, 143, 138, 138, 143, 143, 0, 0, 0, 0, 0, 139, 0, 93, 141, 0, 0, 142, 140, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1566.0, 506.0, 873.0, 693.0, 277.0, 229.0, 373.0, 500.0, 381.0, 312.0, 160.0, 117.0, 128.0, 101.0, 106.0, 267.0, 133.0, 367.0, 291.0, 90.0, 95.0, 217.0, 178.0, 89.0, 224.0, 143.0, 126.0, 165.0, 126.0, 91.0, 90.0, 88.0, 99.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00076547184, -0.102610976, 0.17589734, -0.13160707, 0.014047738, 0.1053103, 0.39719725, -0.17634165, -0.0733827, -0.0063985283, 0.00970749, 0.054874867, 0.025801754, 0.03215195, 0.04753698, -0.0064954036, -0.19893871, 0.0060344576, -0.1246585, -0.020990683, 0.121831164, -0.009554654, -0.24179402, -0.021194506, -0.004052687, 0.057791412, 0.030819401, -0.2892486, -0.017787559, 0.01689428, -0.015092619, -0.022669032, -0.033929516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, -1, -1, -1, -1, 21, -1, 23, -1, 25, -1, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [37.313396, 4.383909, 11.949991, 2.703621, 1.6715461, 4.4670763, 1.0944424, 1.4774971, 3.0924952, 0.0, 0.0, 7.7297573, 0.0, 0.0, 0.0, 0.0, 2.1622791, 0.0, 2.394001, 0.0, 4.1532507, 0.0, 1.0464611, 0.0, 0.0, 6.0086074, 0.0, 0.61990166, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 16, 16, 18, 18, 20, 20, 22, 22, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, -1, -1, -1, -1, 22, -1, 24, -1, 26, -1, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.5981277, 0.52807486, 1.1472435, 0.33120996, 1.0, 1.0, -0.3846154, 0.09178938, 0.2954775, -0.0063985283, 0.00970749, 1.0, 0.025801754, 0.03215195, 0.04753698, -0.0064954036, -0.03846154, 0.0060344576, 1.0, -0.020990683, 0.9613828, -0.009554654, 0.26845157, -0.021194506, -0.004052687, 0.7617074, 0.030819401, 1.0, -0.017787559, 0.01689428, -0.015092619, -0.022669032, -0.033929516], "split_indices": [139, 142, 139, 140, 105, 42, 1, 139, 142, 0, 0, 39, 0, 0, 0, 0, 1, 0, 108, 0, 140, 0, 139, 0, 0, 141, 0, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1296.0, 765.0, 1038.0, 258.0, 580.0, 185.0, 587.0, 451.0, 133.0, 125.0, 436.0, 144.0, 94.0, 91.0, 99.0, 488.0, 125.0, 326.0, 88.0, 348.0, 143.0, 345.0, 160.0, 166.0, 259.0, 89.0, 198.0, 147.0, 169.0, 90.0, 88.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.006131515, -0.06386287, 0.2576276, -0.1276525, 0.012041842, 0.13358456, 0.045316767, -0.15738909, -0.021807883, 0.03880234, -0.0132376375, 0.024972474, 0.0007854811, -0.19076325, -0.0045332764, 0.010291787, -0.012004324, -0.009147649, 0.027550096, -0.008973629, -0.21840051, 0.016711067, -0.070969604, -0.19184071, -0.029783616, -0.018423902, -0.023340404, -0.25280797, -0.0047907634, 0.0059659155, -0.012529264, -0.02996012, -0.020801445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, -1, -1, 21, -1, -1, 23, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [31.459335, 8.207077, 8.998749, 2.8988094, 2.9912803, 3.3147159, 0.0, 2.6889038, 2.4750009, 7.4113526, 0.0, 0.0, 0.0, 1.5468273, 0.0, 0.0, 0.0, 5.916872, 0.0, 0.0, 0.91775894, 0.0, 2.1687617, 2.8607178, 0.0, 0.0, 2.3947437, 0.47998905, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 17, 17, 20, 20, 22, 22, 23, 23, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, -1, -1, 22, -1, -1, 24, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [0.8711886, 0.4294398, 1.0, 1.0, 0.83529425, -0.30769232, 0.045316767, 1.0, 1.2622243, 0.71048903, -0.0132376375, 0.024972474, 0.0007854811, 1.176989, -0.0045332764, 0.010291787, -0.012004324, 1.3240359, 0.027550096, -0.008973629, 0.38016883, 0.016711067, 1.0, 0.2891638, -0.029783616, -0.018423902, 0.57707375, 1.2262855, -0.0047907634, 0.0059659155, -0.012529264, -0.02996012, -0.020801445], "split_indices": [139, 139, 0, 71, 142, 1, 0, 62, 138, 142, 0, 0, 0, 138, 0, 0, 0, 138, 0, 0, 140, 0, 81, 140, 0, 0, 141, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1695.0, 371.0, 921.0, 774.0, 227.0, 144.0, 719.0, 202.0, 653.0, 121.0, 118.0, 109.0, 554.0, 165.0, 89.0, 113.0, 543.0, 110.0, 119.0, 435.0, 141.0, 402.0, 326.0, 109.0, 119.0, 283.0, 229.0, 97.0, 156.0, 127.0, 112.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0068061645, -0.041970234, 0.3310789, -0.11731408, 0.034985695, 0.018577404, 0.048075378, -0.023165924, -0.104406394, -0.04908663, 0.08591559, 0.00094841624, -0.12033515, 0.010486835, -0.143088, 0.03941788, 0.027531907, -0.0023822954, -0.14098121, -0.023654288, -0.0040193214, 0.13270704, -0.053871263, -0.09250792, -0.21400054, 0.03559854, -0.0031262964, 0.011819748, -0.018023428, -0.03992278, -0.0148150325, -0.028727293, -0.015583006, -0.014680321, 0.0062360615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, -1, 19, 21, -1, -1, 23, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [32.661808, 10.40769, 5.8720894, 1.3386679, 3.802234, 0.0, 0.0, 0.0, 1.4785204, 4.848113, 4.870175, 0.0, 1.4247065, 0.0, 2.0001311, 3.8640728, 0.0, 0.0, 2.0847569, 0.0, 0.0, 8.127632, 4.826974, 1.0357904, 1.0016375, 0.0, 0.0, 0.0, 0.0, 1.9896415, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 15, 15, 18, 18, 21, 21, 22, 22, 23, 23, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, -1, 20, 22, -1, -1, 24, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.996009, 0.4268482, 1.0, 0.09120955, 0.55677325, 0.018577404, 0.048075378, -0.023165924, 1.1720463, 0.5054614, 1.0, 0.00094841624, 1.0, 0.010486835, 0.61682194, 1.0, 0.027531907, -0.0023822954, 1.0, -0.023654288, -0.0040193214, 1.0, -0.15384616, 0.34499735, 0.25063372, 0.03559854, -0.0031262964, 0.011819748, -0.018023428, 0.26165146, -0.0148150325, -0.028727293, -0.015583006, -0.014680321, 0.0062360615], "split_indices": [139, 140, 113, 143, 143, 0, 0, 0, 138, 140, 42, 0, 89, 0, 140, 121, 0, 0, 23, 0, 0, 13, 1, 141, 139, 0, 0, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1795.0, 270.0, 907.0, 888.0, 137.0, 133.0, 92.0, 815.0, 335.0, 553.0, 100.0, 715.0, 127.0, 208.0, 444.0, 109.0, 126.0, 589.0, 109.0, 99.0, 222.0, 222.0, 354.0, 235.0, 94.0, 128.0, 94.0, 128.0, 182.0, 172.0, 104.0, 131.0, 89.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.001183253, -0.06728286, 0.16558595, -0.10023031, 0.017917786, 0.11085553, 0.040028572, -0.02262845, -0.087637864, 0.024202844, -0.059462436, 0.062124327, 0.028953655, 0.005193301, -0.10863965, -0.021787839, 0.034109667, -0.04200895, 0.018311156, -0.16625825, -0.053427387, -0.007670448, 0.01329733, 0.013410415, -0.019672517, -0.20374274, -0.009548332, 0.029762406, -0.12749551, -0.16526802, -0.027942374, 0.013483072, -0.010754275, -0.0030683563, -0.0189483, -0.022646477, -0.01047512], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [22.796349, 4.157379, 7.54014, 1.6952677, 7.162137, 4.144693, 0.0, 0.0, 2.846231, 0.0, 4.5507584, 4.7119513, 0.0, 0.0, 2.6849775, 0.0, 2.1144097, 5.476758, 0.0, 1.0956726, 2.6556978, 0.0, 0.0, 0.0, 0.0, 0.7861862, 0.0, 2.9285636, 1.3682568, 0.6629133, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 11, 11, 14, 14, 16, 16, 17, 17, 19, 19, 20, 20, 25, 25, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6968186, 0.49934718, 1.3539915, 0.10190928, 0.46811774, 1.0, 0.040028572, -0.02262845, 1.176989, 0.024202844, 0.52425903, 0.8327316, 0.028953655, 0.005193301, 1.0, -0.021787839, 1.0, 0.6400806, 0.018311156, 0.38096875, 1.0, -0.007670448, 0.01329733, 0.013410415, -0.019672517, 1.0384616, -0.009548332, 1.0, 1.0, 0.2225239, -0.027942374, 0.013483072, -0.010754275, -0.0030683563, -0.0189483, -0.022646477, -0.01047512], "split_indices": [140, 139, 139, 140, 141, 125, 0, 0, 138, 0, 140, 139, 0, 0, 17, 0, 111, 139, 0, 141, 111, 0, 0, 0, 0, 1, 0, 80, 97, 143, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1481.0, 587.0, 1068.0, 413.0, 476.0, 111.0, 97.0, 971.0, 106.0, 307.0, 374.0, 102.0, 127.0, 844.0, 114.0, 193.0, 201.0, 173.0, 413.0, 431.0, 91.0, 102.0, 94.0, 107.0, 270.0, 143.0, 203.0, 228.0, 179.0, 91.0, 115.0, 88.0, 89.0, 139.0, 89.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.002318704, -0.03670442, 0.22687678, -0.09312578, 0.025750246, 0.012590097, 0.036006037, -0.13897061, -0.06386482, 0.047909014, -0.016396863, -0.006259449, -0.17471462, 0.018615521, -0.1282148, 0.094960146, -0.024152676, -0.021739302, -0.0106251305, -0.007883879, 0.0074651763, -0.020095875, -0.08406175, 0.025375873, 0.03523291, -0.01917938, 0.054352418, -0.015846176, 0.00023382243, 0.119951785, -0.0061976556, -0.0046252976, 0.018038558, 0.020050205, 0.003849643], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [16.250727, 6.318133, 3.6175966, 1.2636576, 3.5775497, 0.0, 0.0, 1.0019054, 3.0518756, 2.5836258, 0.0, 0.0, 0.7304754, 1.3761654, 1.0374322, 4.3724, 3.9613655, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2920606, 0.0, 2.758885, 0.0, 2.5993218, 0.0, 0.0, 1.174463, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 22, 22, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0054001, 0.44131443, 1.3539915, 0.22502147, 1.0, 0.012590097, 0.036006037, 1.0, 0.32285196, 1.0, -0.016396863, -0.006259449, 0.23522155, 0.22941884, 1.0, 0.5494912, 1.0, -0.021739302, -0.0106251305, -0.007883879, 0.0074651763, -0.020095875, 0.3490492, 0.025375873, 1.0, -0.01917938, 0.6641927, -0.015846176, 0.00023382243, 0.7783057, -0.0061976556, -0.0046252976, 0.018038558, 0.020050205, 0.003849643], "split_indices": [139, 140, 139, 142, 80, 0, 0, 53, 143, 122, 0, 0, 141, 139, 59, 141, 81, 0, 0, 0, 0, 0, 140, 0, 124, 0, 142, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1793.0, 269.0, 942.0, 851.0, 153.0, 116.0, 367.0, 575.0, 762.0, 89.0, 117.0, 250.0, 252.0, 323.0, 461.0, 301.0, 154.0, 96.0, 92.0, 160.0, 122.0, 201.0, 126.0, 335.0, 96.0, 205.0, 108.0, 93.0, 179.0, 156.0, 114.0, 91.0, 90.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.003861215, -0.025412558, 0.03667074, -0.043055303, 0.01752877, -0.090683654, 0.008690446, 0.0026974909, -0.105018415, 0.017741181, -0.013627191, -0.17315304, -0.07138078, -0.060321886, 0.059750177, -0.01257418, -0.023677323, -0.12665616, -0.0019494923, -0.02214875, -0.018394677, -0.030260231, 0.022918155, -0.09248339, -0.02124764, 0.014546215, -0.010636609, -0.12346298, 0.010579292, -0.020192182, 0.011806869, -0.01704175, 0.0005331864, -0.0018235553, -0.021017816], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, 29, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.419733, 6.8799763, 0.0, 4.4017134, 0.0, 1.5685453, 3.223236, 0.0, 1.8999739, 0.0, 2.5903087, 0.82646847, 2.1300013, 2.1802468, 4.483673, 0.0, 0.0, 0.9062085, 3.7864869, 4.5756965, 0.0, 4.8887773, 0.0, 1.6847156, 0.0, 0.0, 0.0, 1.7975874, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, 30, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3539915, 1.0, 0.03667074, 0.44131443, 0.01752877, 0.09178938, 0.48414886, 0.0026974909, 0.22780173, 0.017741181, 1.4444355, 0.21849972, 1.0, 0.74138474, 1.0, -0.01257418, -0.023677323, 0.37221536, 1.2683326, 0.56573635, -0.018394677, 0.9055765, 0.022918155, 0.30699542, -0.02124764, 0.014546215, -0.010636609, 1.3113952, 0.010579292, -0.020192182, 0.011806869, -0.01704175, 0.0005331864, -0.0018235553, -0.021017816], "split_indices": [139, 125, 0, 140, 0, 139, 140, 0, 139, 0, 138, 143, 115, 141, 53, 0, 0, 140, 138, 142, 0, 142, 0, 143, 0, 0, 0, 138, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1943.0, 113.0, 1786.0, 157.0, 930.0, 856.0, 101.0, 829.0, 100.0, 756.0, 274.0, 555.0, 462.0, 294.0, 157.0, 117.0, 309.0, 246.0, 353.0, 109.0, 192.0, 102.0, 221.0, 88.0, 102.0, 144.0, 197.0, 156.0, 89.0, 103.0, 123.0, 98.0, 89.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0026594123, -0.048182856, 0.12911989, -0.028582, -0.17634228, 0.17070855, -0.004489583, -0.060667183, 0.04558894, -0.025070313, -0.011518567, 0.087414525, 0.052402157, -0.04443319, -0.01689871, 0.01659566, -0.039094478, 0.17637834, -0.0022118932, -0.063934885, 0.0065076314, -0.012467119, 0.005276306, 0.004389888, 0.035743356, -0.015519237, -0.11650044, 0.009385776, -0.08950958, -0.016705556, -0.0056466213, -0.01478556, -0.0031163532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, 17, -1, 19, -1, -1, 21, 23, -1, 25, -1, -1, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [13.251277, 3.6926901, 4.277115, 3.03423, 0.8867922, 14.037567, 0.0, 1.5650334, 3.924361, 0.0, 0.0, 3.761383, 0.0, 1.6529709, 0.0, 0.0, 1.776556, 5.109038, 0.0, 1.6720617, 0.0, 0.0, 0.0, 0.0, 0.0, 2.7677517, 0.95603657, 0.0, 0.69446826, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 16, 16, 17, 17, 19, 19, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, 18, -1, 20, -1, -1, 22, 24, -1, 26, -1, -1, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [0.6968186, 1.0, 1.0, 0.49934718, 0.2604705, 1.3539915, -0.004489583, 0.5153575, 1.0, -0.025070313, -0.011518567, 1.0, 0.052402157, 0.4268482, -0.01689871, 0.01659566, 1.0, 0.7922069, -0.0022118932, 1.0, 0.0065076314, -0.012467119, 0.005276306, 0.004389888, 0.035743356, 0.24084663, 1.0, 0.009385776, 1.0, -0.016705556, -0.0056466213, -0.01478556, -0.0031163532], "split_indices": [140, 40, 119, 139, 142, 139, 0, 140, 53, 0, 0, 50, 0, 140, 0, 0, 69, 139, 0, 23, 0, 0, 0, 0, 0, 139, 111, 0, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1470.0, 591.0, 1275.0, 195.0, 477.0, 114.0, 890.0, 385.0, 88.0, 107.0, 386.0, 91.0, 774.0, 116.0, 159.0, 226.0, 213.0, 173.0, 657.0, 117.0, 117.0, 109.0, 123.0, 90.0, 342.0, 315.0, 138.0, 204.0, 171.0, 144.0, 102.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0043222336, -0.06097261, 0.087902516, -0.045921236, -0.019920755, 0.14493123, -0.050257616, -0.064315185, 0.010763536, 0.08545893, 0.03469741, 0.0073274174, -0.01710238, -0.08434293, 0.017428445, 0.027911887, 0.04368913, 0.0028376027, -0.10728413, -0.007658218, 0.0115617355, -0.013693007, 0.14010991, -0.0005446137, -0.1300479, 0.029172817, 0.005994393, -0.08635952, -0.022737898, -0.15999036, 0.010998935, -0.008545474, -0.022575706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, 19, -1, 21, -1, 23, -1, -1, -1, 25, -1, 27, -1, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [11.285824, 2.415605, 7.1463394, 2.9572644, 0.0, 7.714244, 3.953393, 1.5307262, 0.0, 4.012211, 0.0, 0.0, 0.0, 1.9420176, 1.6984676, 0.0, 7.1055017, 0.0, 1.4465675, 0.0, 0.0, 0.0, 3.23313, 0.0, 2.1686392, 0.0, 0.0, 5.0889816, 0.0, 1.2549009, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 16, 16, 18, 18, 22, 22, 24, 24, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, 20, -1, 22, -1, 24, -1, -1, -1, 26, -1, 28, -1, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [0.55677325, 0.5930298, 0.15384616, 0.5267507, -0.019920755, -0.115384616, 1.0, 1.0, 0.010763536, 0.5655122, 0.03469741, 0.0073274174, -0.01710238, 1.176989, 0.15384616, 0.027911887, 0.7633737, 0.0028376027, 1.0, -0.007658218, 0.0115617355, -0.013693007, 0.9800058, -0.0005446137, 0.37428665, 0.029172817, 0.005994393, 0.34251764, -0.022737898, 1.0, 0.010998935, -0.008545474, -0.022575706], "split_indices": [143, 141, 1, 139, 0, 1, 59, 62, 0, 141, 0, 0, 0, 138, 1, 0, 139, 0, 89, 0, 0, 0, 142, 0, 142, 0, 0, 141, 0, 126, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1161.0, 907.0, 1047.0, 114.0, 642.0, 265.0, 935.0, 112.0, 496.0, 146.0, 131.0, 134.0, 751.0, 184.0, 88.0, 408.0, 127.0, 624.0, 94.0, 90.0, 142.0, 266.0, 114.0, 510.0, 92.0, 174.0, 352.0, 158.0, 256.0, 96.0, 120.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0047477414, -0.03414201, 0.13778725, -0.064864695, 0.016705815, 0.07963601, 0.036020078, 0.0054694465, -0.07877315, -0.043638382, 0.036985654, -0.0077167004, 0.13284715, -0.11111213, -0.012101654, 0.00042777145, -0.019691199, 0.02499099, 0.004850377, -0.0015254606, -0.15071523, -0.012571487, 0.038518082, 0.056856684, -0.015409249, -0.12742096, -0.023298858, 0.01316759, -0.004096516, -0.0068011037, 0.018257968, -0.018310783, -0.0070375875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, 15, -1, -1, 17, 19, 21, 23, -1, -1, -1, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [10.704743, 2.5010529, 6.0529346, 1.6595569, 12.850291, 3.095501, 0.0, 0.0, 1.9275432, 3.4784029, 0.0, 0.0, 2.73495, 2.2853456, 1.6793132, 3.4877641, 0.0, 0.0, 0.0, 0.0, 0.81642723, 0.0, 1.4957062, 4.599733, 0.0, 1.0546513, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 20, 20, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, 16, -1, -1, 18, 20, 22, 24, -1, -1, -1, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7927253, 0.4666329, 1.3159062, 0.09178938, 1.2307693, -0.46153846, 0.036020078, 0.0054694465, 1.0, 1.4899977, 0.036985654, -0.0077167004, 1.0, 1.2103893, 0.26845157, 0.23076923, -0.019691199, 0.02499099, 0.004850377, -0.0015254606, 1.3005717, -0.012571487, 0.3730132, 0.55677325, -0.015409249, 0.28544348, -0.023298858, 0.01316759, -0.004096516, -0.0068011037, 0.018257968, -0.018310783, -0.0070375875], "split_indices": [143, 139, 139, 139, 1, 1, 0, 0, 93, 138, 0, 0, 39, 138, 139, 1, 0, 0, 0, 0, 138, 0, 139, 143, 0, 139, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1601.0, 468.0, 998.0, 603.0, 371.0, 97.0, 104.0, 894.0, 515.0, 88.0, 94.0, 277.0, 602.0, 292.0, 400.0, 115.0, 116.0, 161.0, 176.0, 426.0, 90.0, 202.0, 293.0, 107.0, 332.0, 94.0, 93.0, 109.0, 147.0, 146.0, 168.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.000102559476, -0.026199225, 0.18585253, -0.011877734, -0.13624856, 0.0030879134, 0.02829561, -0.0007156722, -0.019989643, -0.019911451, -0.009052784, -0.03593835, 0.05628544, -0.016621064, -0.019762399, 0.0070584784, 0.02527689, -0.03345945, 0.011183671, 0.08444649, -0.10716124, 0.0010530939, -0.01840142, 0.023776462, -0.006023397, -0.0023344879, -0.018833075, -0.05707053, 0.07648071, 0.0015870627, -0.10510497, 0.022627, -0.0015981786, -0.0028601165, -0.016989194], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [10.122871, 2.8605676, 3.8674555, 3.3704736, 0.6007228, 0.0, 0.0, 3.0437214, 0.0, 0.0, 0.0, 2.926557, 5.600251, 1.810449, 0.0, 4.0925665, 0.0, 3.8450608, 0.0, 6.122271, 1.2722223, 2.6392443, 0.0, 0.0, 0.0, 0.0, 0.0, 1.191254, 3.6286716, 0.0, 1.0160716, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 27, 27, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0129824, 1.0, 0.9782575, 1.0, 0.2604705, 0.0030879134, 0.02829561, 0.55677325, -0.019989643, -0.019911451, -0.009052784, 0.49275038, 1.0, 0.41605026, -0.019762399, 0.73515224, 0.02527689, 0.3515988, 0.011183671, -0.03846154, 1.4357516, 0.30265966, -0.01840142, 0.023776462, -0.006023397, -0.0023344879, -0.018833075, 0.17482516, 1.2480422, 0.0015870627, 1.0, 0.022627, -0.0015981786, -0.0028601165, -0.016989194], "split_indices": [139, 40, 141, 117, 142, 0, 0, 143, 0, 0, 0, 143, 42, 143, 0, 141, 0, 143, 0, 1, 138, 140, 0, 0, 0, 0, 0, 140, 138, 0, 126, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1815.0, 257.0, 1606.0, 209.0, 99.0, 158.0, 1516.0, 90.0, 88.0, 121.0, 937.0, 579.0, 837.0, 100.0, 463.0, 116.0, 740.0, 97.0, 276.0, 187.0, 602.0, 138.0, 134.0, 142.0, 92.0, 95.0, 340.0, 262.0, 135.0, 205.0, 100.0, 162.0, 94.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0013080204, -0.028299544, 0.10164477, -0.05789674, 0.02086358, 0.18837978, -0.005039657, -0.045421008, -0.018265402, -0.03237927, 0.032020673, 0.08946885, 0.038130503, -0.061121166, 0.004354654, 0.012065705, -0.07789777, 0.016006606, 0.001958471, 0.0031434733, -0.08295181, -0.164193, 0.004615161, -0.13229528, -0.020002566, -0.00294558, -0.033327498, -0.09565701, -0.023536555, -0.009225373, 0.011034744, -0.001229779, -0.014163087], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, 21, -1, -1, -1, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [6.101873, 2.3077736, 6.1716595, 1.5408735, 9.49898, 5.686558, 0.0, 1.2571243, 0.0, 3.524788, 0.0, 0.9719236, 0.0, 1.5457246, 0.0, 0.0, 4.1749, 0.0, 0.0, 0.0, 1.9226971, 5.2397738, 0.0, 1.3103805, 2.5616796, 0.0, 0.0, 0.9810798, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 16, 16, 20, 20, 21, 21, 23, 23, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, 22, -1, -1, -1, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.7927253, 0.4666329, 1.1306531, 0.58005387, 1.1923077, 1.5204911, -0.005039657, 0.43214852, -0.018265402, 0.54409826, 0.032020673, 1.4071487, 0.038130503, 1.176989, 0.004354654, 0.012065705, 0.71048903, 0.016006606, 0.001958471, 0.0031434733, 0.28544348, 1.0, 0.004615161, 0.32248607, 1.0, -0.00294558, -0.033327498, 0.15384616, -0.023536555, -0.009225373, 0.011034744, -0.001229779, -0.014163087], "split_indices": [143, 139, 143, 143, 1, 138, 0, 142, 0, 139, 0, 138, 0, 138, 0, 0, 142, 0, 0, 0, 139, 115, 0, 140, 93, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1586.0, 468.0, 990.0, 596.0, 298.0, 170.0, 900.0, 90.0, 506.0, 90.0, 197.0, 101.0, 765.0, 135.0, 116.0, 390.0, 98.0, 99.0, 146.0, 619.0, 230.0, 160.0, 347.0, 272.0, 128.0, 102.0, 256.0, 91.0, 175.0, 97.0, 91.0, 165.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.005414463, -0.007045681, 0.023423864, -0.019234126, 0.020049332, -0.03663731, 0.07630082, -0.01691265, -0.15516137, 0.024383858, -0.0063313954, -0.031362996, 0.016915416, -0.025023578, -0.005662968, -0.011764531, -0.107758574, -0.037560582, 0.07017586, -0.019080026, -0.0008251684, -0.0055057583, -0.01904939, 0.01934705, -0.008868451, 0.09106522, -0.052984577, 0.0005871898, 0.01867979, -0.013095367, 0.0009861188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [5.9076505, 4.9706197, 0.0, 3.0858083, 0.0, 3.670419, 6.6897545, 3.6190305, 2.0983958, 0.0, 0.0, 1.8700476, 0.0, 0.0, 0.0, 2.101056, 2.107121, 3.7061005, 4.661618, 0.0, 0.0, 2.8656723, 0.0, 0.0, 0.0, 1.6800911, 2.053112, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.3443526, 1.1185367, 0.023423864, 0.8080213, 0.020049332, 0.67203194, 0.9504357, 0.6242293, 1.4109486, 0.024383858, -0.0063313954, 1.0, 0.016915416, -0.025023578, -0.005662968, 0.4318777, 1.0, 0.35473838, 1.0, -0.019080026, -0.0008251684, 1.0, -0.01904939, 0.01934705, -0.008868451, 1.0, 0.26347196, 0.0005871898, 0.01867979, -0.013095367, 0.0009861188], "split_indices": [140, 142, 0, 143, 0, 143, 143, 143, 138, 0, 0, 0, 0, 0, 0, 143, 106, 143, 15, 0, 0, 53, 0, 0, 0, 111, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1965.0, 107.0, 1856.0, 109.0, 1570.0, 286.0, 1346.0, 224.0, 130.0, 156.0, 1249.0, 97.0, 114.0, 110.0, 994.0, 255.0, 756.0, 238.0, 139.0, 116.0, 625.0, 131.0, 134.0, 104.0, 206.0, 419.0, 109.0, 97.0, 187.0, 232.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.007185738, -0.02649694, 0.092339225, -0.01217918, -0.12058506, 0.018750794, 0.19724834, -0.0221281, 0.009991194, -0.0058089173, -0.019269567, -0.076448955, 0.014106043, 0.004397448, 0.046074156, -0.00536089, -0.0873262, 0.005319122, -0.019098544, -0.029658902, 0.100252606, -0.01797903, 0.0037455396, -0.002851587, -0.018928425, -0.00028960337, 0.020340128, -0.044404045, 0.08644623, 0.0049574096, -0.08775088, -0.00041791072, 0.017883131, 0.008470349, -0.009645877, -0.0019166606, -0.014472857], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, -1, -1, -1, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, -1, -1, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [5.940037, 1.9991423, 4.5316973, 1.4363587, 0.8832958, 4.0171275, 9.7735615, 1.2932439, 0.0, 0.0, 0.0, 2.8806152, 0.0, 0.0, 0.0, 2.414792, 2.792152, 0.0, 0.0, 3.2735317, 1.8725784, 0.0, 0.0, 2.430406, 0.0, 0.0, 0.0, 0.95642966, 1.7414662, 1.6902965, 0.93004966, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 27, 27, 28, 28, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, -1, -1, -1, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, -1, -1, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6968186, 1.0, -0.115384616, 0.8080213, 1.0, 1.0, 1.0, 0.52861816, 0.009991194, -0.0058089173, -0.019269567, 0.90278953, 0.014106043, 0.004397448, 0.046074156, 0.44437784, 0.6489993, 0.005319122, -0.019098544, 0.39922574, 1.0, -0.01797903, 0.0037455396, 0.33120996, -0.018928425, -0.00028960337, 0.020340128, 0.20441829, 1.0, 1.2140391, 0.23146853, -0.00041791072, 0.017883131, 0.008470349, -0.009645877, -0.0019166606, -0.014472857], "split_indices": [140, 40, 1, 143, 12, 61, 0, 141, 0, 0, 0, 140, 0, 0, 0, 141, 142, 0, 0, 141, 71, 0, 0, 140, 0, 0, 0, 143, 111, 138, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1484.0, 587.0, 1288.0, 196.0, 345.0, 242.0, 1183.0, 105.0, 105.0, 91.0, 194.0, 151.0, 153.0, 89.0, 941.0, 242.0, 91.0, 103.0, 765.0, 176.0, 139.0, 103.0, 655.0, 110.0, 88.0, 88.0, 447.0, 208.0, 209.0, 238.0, 105.0, 103.0, 117.0, 92.0, 108.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.005373039, -0.031644076, 0.06129421, -0.0134416325, -0.091632985, 0.10747423, -0.00922971, -0.034225255, 0.013826362, -0.021004701, -0.036941748, 0.026343403, 0.026288716, -0.052106112, 0.012850858, -0.011135954, 0.008163602, 0.11785678, -0.014943473, -0.067451954, 0.0066786855, -0.008309788, 0.030289426, -0.04165391, -0.14615561, 0.039442673, -0.081198536, -0.023740357, -0.006782006, -0.0064620785, 0.014456803, -0.10612009, 0.0002141251, -0.067571096, -0.019511081, -0.002263808, -0.011823084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, 15, 17, -1, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1], "loss_changes": [3.618429, 1.6182617, 4.1422243, 3.5849435, 2.2342918, 5.6613417, 0.0, 2.90982, 0.0, 0.0, 2.082534, 4.745385, 0.0, 1.6438859, 0.0, 0.0, 0.0, 7.213725, 0.0, 1.6202586, 0.0, 0.0, 0.0, 1.9273674, 1.4081473, 2.155123, 0.8390908, 0.0, 0.0, 0.0, 0.0, 1.0668857, 0.0, 0.49395555, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 11, 11, 13, 13, 17, 17, 19, 19, 23, 23, 24, 24, 25, 25, 26, 26, 31, 31, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, 16, 18, -1, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1], "split_conditions": [0.6968186, 0.52861816, 1.1951114, 0.52807486, 0.500368, 0.96802163, -0.00922971, 3.0384614, 0.013826362, -0.021004701, 0.70742667, 0.80717355, 0.026288716, 0.44610116, 0.012850858, -0.011135954, 0.008163602, -0.115384616, -0.014943473, 0.35473838, 0.0066786855, -0.008309788, 0.030289426, -0.03846154, 1.0, 0.25325468, 1.0, -0.023740357, -0.006782006, -0.0064620785, 0.014456803, 0.2940117, 0.0002141251, 0.16337174, -0.019511081, -0.002263808, -0.011823084], "split_indices": [140, 141, 143, 142, 140, 141, 0, 1, 0, 0, 142, 141, 0, 141, 0, 0, 0, 1, 0, 143, 0, 0, 0, 1, 97, 142, 74, 0, 0, 0, 0, 140, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1482.0, 584.0, 1137.0, 345.0, 449.0, 135.0, 1000.0, 137.0, 109.0, 236.0, 295.0, 154.0, 901.0, 99.0, 145.0, 91.0, 194.0, 101.0, 798.0, 103.0, 93.0, 101.0, 601.0, 197.0, 197.0, 404.0, 91.0, 106.0, 99.0, 98.0, 311.0, 93.0, 217.0, 94.0, 115.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.001702996, -0.0070623136, 0.018784804, -0.023556264, 0.061497394, -0.008039852, -0.11315528, 0.1482774, -0.012640642, -0.027828021, 0.101270735, -0.023860108, 0.0009127984, 0.0070739933, 0.02964601, -0.012582308, -0.017477972, 0.025635233, -0.010205844, -0.030186746, 0.01477377, -0.04721775, 0.0115793245, -0.088596664, 0.044068553, -0.06600459, -0.016568566, 0.010746652, -0.0075063854, 0.0030628087, -0.009070564], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [3.3741887, 2.2333703, 0.0, 2.213286, 6.245311, 2.9352674, 3.604882, 3.0103035, 0.0, 2.5742002, 6.5587854, 0.0, 0.0, 0.0, 0.0, 2.9380596, 0.0, 0.0, 0.0, 2.332042, 0.0, 3.1729562, 0.0, 1.0066442, 1.9788213, 1.0669575, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 15, 15, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.2393746, 0.8080213, 0.018784804, 0.67203194, 1.1306531, 0.55677325, 1.4138024, 0.91670126, -0.012640642, 0.5930298, 1.0, -0.023860108, 0.0009127984, 0.0070739933, 0.02964601, 0.48462236, -0.017477972, 0.025635233, -0.010205844, 3.0384614, 0.01477377, 1.0, 0.0115793245, 0.35473838, 1.2642944, -0.26923078, -0.016568566, 0.010746652, -0.0075063854, 0.0030628087, -0.009070564], "split_indices": [141, 143, 0, 143, 143, 143, 138, 140, 0, 141, 106, 0, 0, 0, 0, 141, 0, 0, 0, 1, 0, 83, 0, 143, 138, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1975.0, 93.0, 1592.0, 383.0, 1357.0, 235.0, 262.0, 121.0, 1149.0, 208.0, 116.0, 119.0, 172.0, 90.0, 1041.0, 108.0, 118.0, 90.0, 938.0, 103.0, 840.0, 98.0, 578.0, 262.0, 447.0, 131.0, 171.0, 91.0, 91.0, 356.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0035408633, -0.028716736, 0.058353584, -0.015882859, -0.016151942, 0.07878441, -0.009952097, -0.03263657, 0.017722571, 0.036669686, 0.03349823, -0.018604474, -0.011773444, -0.019956151, 0.08027423, -0.035269126, 0.0131377345, 0.037846904, 0.02677004, -0.019546116, -0.015956363, -0.0802272, 0.14797373, -0.038158078, 0.010744172, 0.0022320643, -0.016897056, 0.032620873, 0.0017268033, 0.00021290308, -0.09151336, 0.00800745, -0.056370635, -0.0010105013, -0.015309145, -0.011391251, 0.0021159444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, 17, 19, -1, 21, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, 33, -1, 35, -1, -1, -1, -1], "loss_changes": [3.6653268, 2.224207, 2.47719, 3.8499894, 0.0, 7.336998, 0.0, 1.3075408, 0.0, 6.0156403, 0.0, 2.3494306, 0.0, 0.0, 3.9203312, 1.6533232, 0.0, 5.2272573, 0.0, 1.7749829, 0.0, 1.7654859, 4.8456364, 1.3409779, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7216825, 1.3735535, 0.0, 0.99485373, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 14, 14, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22, 23, 23, 29, 29, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, 18, 20, -1, 22, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, 34, -1, 36, -1, -1, -1, -1], "split_conditions": [0.5981277, 0.54409826, 1.4291958, 0.49648446, -0.016151942, 1.1185367, -0.009952097, 1.3181741, 0.017722571, -0.5, 0.03349823, 0.4294398, -0.011773444, -0.019956151, 0.96802163, 0.3730132, 0.0131377345, 1.0, 0.02677004, 0.3262957, -0.015956363, 0.6596693, 0.6458431, 1.0, 0.010744172, 0.0022320643, -0.016897056, 0.032620873, 0.0017268033, 1.204987, 0.1700591, 0.00800745, 1.0, -0.0010105013, -0.015309145, -0.011391251, 0.0021159444], "split_indices": [139, 139, 143, 139, 0, 142, 0, 138, 0, 1, 0, 139, 0, 0, 141, 139, 0, 111, 0, 139, 0, 140, 141, 80, 0, 0, 0, 0, 0, 138, 142, 0, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1305.0, 768.0, 1190.0, 115.0, 680.0, 88.0, 1095.0, 95.0, 584.0, 96.0, 940.0, 155.0, 91.0, 493.0, 846.0, 94.0, 402.0, 91.0, 751.0, 95.0, 194.0, 208.0, 655.0, 96.0, 90.0, 104.0, 88.0, 120.0, 381.0, 274.0, 158.0, 223.0, 118.0, 156.0, 128.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017557915, -0.010572243, 0.019132445, -0.023927597, 0.014162838, -0.012118838, -0.021786373, -0.032396175, 0.097805664, -0.014183083, -0.025736157, -0.009465656, 0.022936216, -0.03653936, 0.0677378, -0.004084715, -0.07883738, -0.031526145, 0.024680221, 0.011762161, -0.039615545, -0.031011423, -0.11130961, 0.00927706, -0.015314986, -0.013131358, -0.0014080228, -0.008040913, 0.0020581705, -0.015724523, -0.007480446, 0.0048286, -0.0112722665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [3.508404, 4.0064387, 0.0, 4.1497426, 0.0, 3.8070915, 0.0, 5.9083285, 6.7350283, 2.443149, 0.0, 0.0, 0.0, 1.43866, 5.083548, 2.5643256, 0.70662117, 2.7816079, 0.0, 0.0, 1.608131, 0.46893936, 0.45443606, 0.0, 0.0, 0.0, 1.7922621, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 22, 22, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2393746, 1.0, 0.019132445, 1.0728176, 0.014162838, 1.4444355, -0.021786373, 0.74138474, -0.26923078, 0.49934718, -0.025736157, -0.009465656, 0.022936216, 1.0, 1.0, 1.204987, 1.0, 0.5361265, 0.024680221, 0.011762161, 0.28544348, 0.46153846, 1.0, 0.00927706, -0.015314986, -0.013131358, 1.3181741, -0.008040913, 0.0020581705, -0.015724523, -0.007480446, 0.0048286, -0.0112722665], "split_indices": [141, 125, 0, 143, 0, 138, 0, 141, 1, 139, 0, 0, 0, 23, 105, 138, 97, 140, 0, 0, 139, 1, 59, 0, 0, 0, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1971.0, 90.0, 1812.0, 159.0, 1708.0, 104.0, 1442.0, 266.0, 1334.0, 108.0, 108.0, 158.0, 1048.0, 286.0, 593.0, 455.0, 184.0, 102.0, 134.0, 459.0, 184.0, 271.0, 91.0, 93.0, 135.0, 324.0, 94.0, 90.0, 120.0, 151.0, 224.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012451395, -0.02349222, 0.07416869, -0.014217786, -0.016719814, 0.14434214, -0.0046013417, -0.0246481, 0.013531883, 0.058568496, 0.032482415, -0.012278231, -0.01810128, 0.018938025, -0.006227662, -0.04028275, 0.035838082, -0.025289899, -0.0128115835, -0.028937582, 0.10088479, -0.07280121, 0.022902086, 0.0074552717, -0.011949162, 0.024894018, 0.0011455334, -0.00031082125, -0.11084323, 0.01771078, -0.038163368, -0.0023506957, -0.017165514, -0.01308881, 0.0050214874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, -1, 17, 19, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [3.476272, 2.1324656, 3.9806566, 2.3442497, 0.0, 4.613217, 0.0, 2.7175658, 0.0, 3.1932087, 0.0, 1.7544111, 0.0, 0.0, 0.0, 1.0837822, 2.0182397, 1.6096345, 0.0, 2.2491515, 3.164482, 0.9385464, 3.286409, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2162375, 0.0, 2.0487125, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, -1, 18, 20, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [0.7927253, 1.5221448, 1.1306531, 0.7633737, -0.016719814, 0.9613828, -0.0046013417, 0.6940378, 0.013531883, 0.74138474, 0.032482415, 0.4156466, -0.01810128, 0.018938025, -0.006227662, 1.3181741, 0.4666329, 0.26347196, -0.0128115835, 1.0, 1.3613431, 1.0, 0.2225239, 0.0074552717, -0.011949162, 0.024894018, 0.0011455334, -0.00031082125, 0.17301962, 0.01771078, 0.28544348, -0.0023506957, -0.017165514, -0.01308881, 0.0050214874], "split_indices": [143, 138, 143, 139, 0, 140, 0, 143, 0, 141, 0, 140, 0, 0, 0, 138, 139, 141, 0, 97, 138, 122, 143, 0, 0, 0, 0, 0, 141, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1600.0, 472.0, 1503.0, 97.0, 298.0, 174.0, 1405.0, 98.0, 202.0, 96.0, 1302.0, 103.0, 97.0, 105.0, 823.0, 479.0, 703.0, 120.0, 240.0, 239.0, 354.0, 349.0, 112.0, 128.0, 90.0, 149.0, 125.0, 229.0, 99.0, 250.0, 94.0, 135.0, 122.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036192886, 0.0045802584, -0.017042886, -0.0043937583, 0.013253013, 0.00892631, -0.10965479, 0.019204011, -0.01699991, -0.0043163183, -0.018817145, -0.006456557, 0.09085036, 0.021167742, -0.08304701, 0.18914787, -0.010944004, -0.0072702304, 0.023830352, -0.16370434, 0.0046004686, 0.0062394566, 0.031130865, 0.016046245, -0.010264112, -0.008770141, -0.023807278, -0.03255595, 0.09997505, -0.00962306, 0.006533839, 0.0018889727, 0.02179173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [2.8025467, 2.2424824, 0.0, 2.558804, 0.0, 2.9790854, 1.0702426, 2.816561, 0.0, 0.0, 0.0, 2.386575, 7.9539723, 5.118994, 3.1122808, 4.1962395, 0.0, 1.6299818, 0.0, 1.0400071, 0.0, 0.0, 0.0, 2.4026043, 0.0, 0.0, 0.0, 2.3250537, 2.0656905, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.178437, -0.017042886, 1.0, 0.013253013, 1.6271712, 0.35341826, 0.6904005, -0.01699991, -0.0043163183, -0.018817145, 1.3520314, 0.07692308, 0.4666329, 0.07692308, 0.7633737, -0.010944004, 1.3076923, 0.023830352, 1.4109486, 0.0046004686, 0.0062394566, 0.031130865, 0.34615386, -0.010264112, -0.008770141, -0.023807278, 1.0, 1.0, -0.00962306, 0.006533839, 0.0018889727, 0.02179173], "split_indices": [117, 142, 0, 40, 0, 138, 142, 143, 0, 0, 0, 138, 1, 139, 1, 139, 0, 1, 0, 138, 0, 0, 0, 1, 0, 0, 0, 12, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1953.0, 96.0, 1825.0, 128.0, 1620.0, 205.0, 1532.0, 88.0, 111.0, 94.0, 1128.0, 404.0, 829.0, 299.0, 271.0, 133.0, 733.0, 96.0, 184.0, 115.0, 133.0, 138.0, 589.0, 144.0, 91.0, 93.0, 373.0, 216.0, 226.0, 147.0, 128.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019152407, -0.011203758, 0.015878431, -0.021567937, 0.010831646, -0.009981777, -0.015886392, 0.025623823, -0.04563033, -0.031967733, 0.12579133, -0.100915484, 0.0037223937, 0.0011356437, -0.017508136, 0.032328468, 0.036966544, -0.017068747, -0.197201, 0.052362323, -0.0077541857, -0.06353741, 0.054753486, 0.020028468, -0.016674206, 0.0085911555, -0.013370973, -0.012281647, -0.027665718, -0.010778913, 0.017525014, 0.00089894, -0.014562823, -0.0009756447, 0.015985398, -0.007541583, 0.005603674], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.0868218, 2.4217148, 0.0, 2.8617287, 0.0, 2.105749, 0.0, 4.7881064, 2.2619042, 2.4966865, 5.3153167, 3.1566308, 1.7312769, 1.4841454, 0.0, 0.0, 6.9532857, 2.5104504, 1.075676, 2.1260455, 0.0, 1.1550348, 1.5865264, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7816952, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3539915, 1.0, 0.015878431, 0.9504357, 0.010831646, 1.0, -0.015886392, 0.52453405, 1.0, 0.4846499, 1.0, 1.0, 1.0, 0.24394238, -0.017508136, 0.032328468, 0.67635286, 1.0, 1.2622243, 1.0, -0.0077541857, 0.17775789, 0.40935713, 0.020028468, -0.016674206, 0.0085911555, -0.013370973, -0.012281647, -0.027665718, 0.28573313, 0.017525014, 0.00089894, -0.014562823, -0.0009756447, 0.015985398, -0.007541583, 0.005603674], "split_indices": [139, 125, 0, 143, 0, 106, 0, 143, 39, 142, 126, 126, 15, 142, 0, 0, 141, 124, 138, 71, 0, 142, 139, 0, 0, 0, 0, 0, 0, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1955.0, 113.0, 1799.0, 156.0, 1659.0, 140.0, 830.0, 829.0, 527.0, 303.0, 391.0, 438.0, 428.0, 99.0, 94.0, 209.0, 209.0, 182.0, 274.0, 164.0, 194.0, 234.0, 116.0, 93.0, 111.0, 98.0, 94.0, 88.0, 181.0, 93.0, 103.0, 91.0, 145.0, 89.0, 92.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.001241613, 0.009864246, -0.013149552, -0.0036345662, 0.026352946, 0.008910225, -0.01196739, -0.00759259, 0.017642511, -0.015772587, 0.011892467, -0.00015762767, -0.111055695, -0.021519408, 0.08108576, -0.0025370219, -0.021407077, 0.027599985, -0.088690184, 0.019820182, -0.0062172245, -0.009023149, 0.011165671, -0.12266867, 0.0014618108, -0.0051933066, 0.009274547, -0.018964954, -0.0024393427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [2.9828515, 6.5059404, 0.0, 2.6260636, 0.0, 4.500553, 0.0, 1.5337374, 0.0, 2.0710754, 0.0, 2.0756617, 1.7300715, 3.1245203, 4.1776752, 0.0, 0.0, 1.6838965, 1.4041042, 0.0, 0.0, 1.6637824, 0.0, 1.98135, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.1391696, 1.1472435, -0.013149552, 0.85769147, 0.026352946, 0.78044474, -0.01196739, 0.7132902, 0.017642511, 2.0, 0.011892467, 0.52807486, 0.36426425, 0.34762505, 0.6190295, -0.0025370219, -0.021407077, 1.0, 1.0, 0.019820182, -0.0062172245, 0.28435913, 0.011165671, 1.3220984, 0.0014618108, -0.0051933066, 0.009274547, -0.018964954, -0.0024393427], "split_indices": [143, 139, 0, 142, 0, 140, 0, 142, 0, 0, 0, 142, 143, 141, 142, 0, 0, 83, 116, 0, 0, 141, 0, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1900.0, 162.0, 1804.0, 96.0, 1628.0, 176.0, 1482.0, 146.0, 1392.0, 90.0, 1196.0, 196.0, 947.0, 249.0, 107.0, 89.0, 547.0, 400.0, 137.0, 112.0, 381.0, 166.0, 301.0, 99.0, 268.0, 113.0, 179.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0040670997, -0.0067930315, 0.012473525, 0.007064369, -0.085643604, -0.0049046604, 0.017418751, 0.005445702, -0.018237976, 0.010630422, -0.024680812, -0.03677964, 0.06594216, -0.0022785524, -0.027649028, 0.0117806755, 0.027416298, -0.0290844, 0.013345649, -0.023259154, 0.012222624, 0.027096888, -0.10221295, 0.05300553, -0.096488446, 0.070552565, -0.004257582, 0.00065657933, -0.017130217, -0.0048409547, 0.014732159, -0.01791535, -0.0022401817, 0.000446532, 0.012774343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, 25, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.7139878, 2.076062, 0.0, 3.2324886, 3.8489945, 5.6670504, 0.0, 0.0, 0.0, 3.7158458, 0.0, 6.3102202, 7.375516, 2.4268744, 0.0, 2.008527, 0.0, 2.2884102, 0.0, 2.200415, 0.0, 0.9537176, 1.818737, 1.8460588, 1.2309988, 0.73323935, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, 26, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1185367, 1.0, 0.012473525, 0.96802163, -0.115384616, 0.83529425, 0.017418751, 0.005445702, -0.018237976, 1.0, -0.024680812, 0.65766674, 0.67494744, 0.52527964, -0.027649028, 1.0, 0.027416298, 1.0, 0.013345649, 0.34762505, 0.012222624, 1.0, 0.21827538, 1.0, 1.0, 0.27905032, -0.004257582, 0.00065657933, -0.017130217, -0.0048409547, 0.014732159, -0.01791535, -0.0022401817, 0.000446532, 0.012774343], "split_indices": [142, 64, 0, 141, 1, 142, 0, 0, 0, 39, 0, 139, 139, 142, 0, 71, 0, 12, 0, 141, 0, 115, 140, 12, 97, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1900.0, 171.0, 1616.0, 284.0, 1508.0, 108.0, 116.0, 168.0, 1417.0, 91.0, 763.0, 654.0, 667.0, 96.0, 519.0, 135.0, 557.0, 110.0, 394.0, 125.0, 315.0, 242.0, 193.0, 201.0, 194.0, 121.0, 94.0, 148.0, 93.0, 100.0, 95.0, 106.0, 90.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011034748, -0.008900458, 0.014444018, 3.911515e-05, -0.018836481, -0.01007654, 0.019976713, -0.00068394863, -0.01903502, 0.008197985, -0.015486275, 0.05049687, -0.029921362, -0.014558709, 0.14430282, 0.01324436, -0.09597013, 0.00803684, -0.07171913, 0.22346178, -0.0034846347, -0.030701106, 0.018191127, -0.01808059, -0.046042845, 0.0019194785, -0.018871492, 0.012558768, 0.03494486, -0.078958265, 0.011948696, -0.012975498, 0.003530005, -0.0026220314, -0.016138898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [2.3433647, 3.1444972, 0.0, 3.7720494, 0.0, 3.0088816, 0.0, 2.312927, 0.0, 2.575012, 0.0, 4.619669, 2.394876, 2.4254549, 4.396191, 3.7653706, 1.406225, 0.0, 2.967597, 2.6511297, 0.0, 2.9208026, 0.0, 0.0, 1.4231622, 0.0, 0.0, 0.0, 0.0, 1.3259034, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 24, 24, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.3443526, 1.6271712, 0.014444018, 1.0054001, -0.018836481, 1.0078171, 0.019976713, 1.0, -0.01903502, 1.0, -0.015486275, 0.51852506, 0.45561248, 0.26255798, 1.0, 0.3898465, 1.0, 0.00803684, 1.0, 1.0, -0.0034846347, 1.0, 0.018191127, -0.01808059, 1.0, 0.0019194785, -0.018871492, 0.012558768, 0.03494486, 0.22744948, 0.011948696, -0.012975498, 0.003530005, -0.0026220314, -0.016138898], "split_indices": [140, 138, 0, 139, 0, 140, 0, 117, 0, 122, 0, 143, 143, 142, 0, 142, 53, 0, 12, 108, 0, 93, 0, 0, 69, 0, 0, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1960.0, 105.0, 1867.0, 93.0, 1777.0, 90.0, 1689.0, 88.0, 1597.0, 92.0, 757.0, 840.0, 447.0, 310.0, 508.0, 332.0, 168.0, 279.0, 215.0, 95.0, 403.0, 105.0, 123.0, 209.0, 157.0, 122.0, 121.0, 94.0, 305.0, 98.0, 103.0, 106.0, 186.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0017746107, -0.0056190263, 0.014406592, 0.0040338063, -0.020887082, 0.0135846045, -0.015526627, 0.0010185719, 0.02425049, -0.010732482, 0.010245585, 0.0047114855, -0.1031808, -0.063685864, 0.02321186, -0.00085426954, -0.023976076, 0.0027442425, -0.017392172, -0.0015562976, 0.105210274, 0.019376721, -0.013873874, 0.026700644, -0.0021039725, -0.0031423613, 0.009148044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [2.1724844, 3.8513198, 0.0, 2.8511837, 0.0, 5.085864, 0.0, 1.9977837, 0.0, 2.144509, 0.0, 1.6285393, 2.7790186, 2.7524967, 2.0573525, 0.0, 0.0, 0.0, 0.0, 2.2341383, 4.8002906, 2.4724529, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [1.2067715, 1.0653163, 0.014406592, 1.0731653, -0.020887082, 0.88646895, -0.015526627, 0.77939034, 0.02425049, 2.0, 0.010245585, -1.0, 1.3147875, 0.41246155, 0.5302295, -0.00085426954, -0.023976076, 0.0027442425, -0.017392172, 0.45108533, 0.6289425, 1.0, -0.013873874, 0.026700644, -0.0021039725, -0.0031423613, 0.009148044], "split_indices": [141, 141, 0, 140, 0, 140, 0, 142, 0, 0, 0, 0, 138, 142, 139, 0, 0, 0, 0, 139, 139, 17, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1963.0, 102.0, 1874.0, 89.0, 1768.0, 106.0, 1676.0, 92.0, 1502.0, 174.0, 1287.0, 215.0, 274.0, 1013.0, 127.0, 88.0, 150.0, 124.0, 778.0, 235.0, 675.0, 103.0, 103.0, 132.0, 396.0, 279.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037152776, -0.010867926, 0.009997121, -0.0033936736, -0.014330004, -0.01483696, 0.07146054, -0.0022333725, -0.020376343, -0.00816395, 0.015894629, -0.014168298, 0.014396943, 0.004072803, -0.073170386, -0.016861564, 0.01085054, 0.0010912775, -0.13714673, -0.0039040954, -0.012821033, -0.024932342, -0.0034318052, -0.02351326, 0.012252829, -0.0043703928, 0.010018108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, -1, -1], "loss_changes": [1.5285053, 1.9083939, 0.0, 1.563255, 0.0, 3.7693617, 3.241366, 2.5894601, 0.0, 0.0, 0.0, 1.4766333, 0.0, 2.2911696, 1.7429038, 1.2595625, 0.0, 0.0, 2.1224356, 1.9387603, 0.0, 0.0, 0.0, 1.6907878, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, -1, -1], "split_conditions": [1.178437, 1.2005786, 0.009997121, 1.4726204, -0.014330004, 0.8186932, 0.7014454, 0.6907201, -0.020376343, -0.00816395, 0.015894629, 0.5153575, 0.014396943, 0.4268482, -0.07692308, 1.3470919, 0.01085054, 0.0010912775, 0.65384614, 0.43214852, -0.012821033, -0.024932342, -0.0034318052, 1.0, 0.012252829, -0.0043703928, 0.010018108], "split_indices": [142, 140, 0, 138, 0, 142, 143, 142, 0, 0, 0, 140, 0, 140, 1, 138, 0, 0, 1, 142, 0, 0, 0, 113, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1928.0, 133.0, 1825.0, 103.0, 1583.0, 242.0, 1484.0, 99.0, 88.0, 154.0, 1372.0, 112.0, 1048.0, 324.0, 873.0, 175.0, 140.0, 184.0, 782.0, 91.0, 88.0, 96.0, 677.0, 105.0, 582.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.005315829, 0.011689431, -0.0122546395, 0.0030369426, 0.010613264, 0.0128801875, -0.07756717, -0.00052295177, 0.024393206, 0.0054368526, -0.02267119, 0.011868461, -0.017424813, -0.0053215944, 0.023808962, 0.0068210326, -0.012524261, -0.048288215, 0.030580712, 0.0031349703, -0.07405344, 0.02046396, 0.008764701, -0.01125934, -0.0015529755, -0.006616577, 0.0067163752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, -1, 23, -1, 25, -1, -1, -1, -1], "loss_changes": [1.6820419, 1.6065533, 0.0, 1.4289241, 0.0, 4.970398, 3.8567934, 3.2656474, 0.0, 0.0, 0.0, 5.506477, 0.0, 1.9163018, 0.0, 1.5647066, 0.0, 0.73868, 3.1707203, 0.0, 0.6134958, 0.0, 3.2468941, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 20, 20, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, -1, 24, -1, 26, -1, -1, -1, -1], "split_conditions": [1.0, 1.1185367, -0.0122546395, 1.0, 0.010613264, 0.9884085, 0.69685656, 0.83529425, 0.024393206, 0.0054368526, -0.02267119, 0.71048903, -0.017424813, 0.7147042, 0.023808962, 0.22502147, -0.012524261, 0.1010785, 0.26255798, 0.0031349703, 1.0, 0.02046396, 1.0, -0.01125934, -0.0015529755, -0.006616577, 0.0067163752], "split_indices": [117, 142, 0, 119, 0, 141, 142, 142, 0, 0, 0, 142, 0, 139, 0, 142, 0, 139, 142, 0, 109, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1966.0, 98.0, 1801.0, 165.0, 1605.0, 196.0, 1517.0, 88.0, 104.0, 92.0, 1416.0, 101.0, 1316.0, 100.0, 1195.0, 121.0, 360.0, 835.0, 88.0, 272.0, 93.0, 742.0, 164.0, 108.0, 325.0, 417.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0016604859, -0.010392042, 0.060000222, -0.0018112077, -0.0153674055, 0.1233339, -0.008250053, -0.012160293, 0.01260862, 0.0009435761, 0.024201766, -0.023912892, 0.009151162, -0.005182637, -0.018597027, -0.029704502, 0.060942877, -0.010416782, -0.016041322, 0.116501994, -0.006295392, -0.032310203, 0.012014027, -0.0021161076, 0.031993743, -0.05017984, 0.0073386766, -0.001748755, -0.011574111], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [1.4414397, 2.0888848, 3.1678095, 2.1217647, 0.0, 3.2848392, 0.0, 1.8069081, 0.0, 0.0, 0.0, 4.0431204, 0.0, 1.9360958, 0.0, 2.1958542, 2.2234018, 2.16948, 0.0, 6.2452393, 0.0, 1.2276981, 0.0, 0.0, 0.0, 1.1917012, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0, 1.6902589, 1.0, 1.5634443, -0.0153674055, 0.43214852, -0.008250053, 0.9271665, 0.01260862, 0.0009435761, 0.024201766, 0.74297655, 0.009151162, 0.5405575, -0.018597027, 0.5054614, 0.7739706, 0.4268482, -0.016041322, 0.5676343, -0.006295392, 1.3846154, 0.012014027, -0.0021161076, 0.031993743, 0.61538464, 0.0073386766, -0.001748755, -0.011574111], "split_indices": [62, 138, 108, 138, 0, 142, 0, 140, 0, 0, 0, 140, 0, 142, 0, 140, 143, 140, 0, 140, 0, 1, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1699.0, 351.0, 1603.0, 96.0, 243.0, 108.0, 1483.0, 120.0, 124.0, 119.0, 1332.0, 151.0, 1194.0, 138.0, 871.0, 323.0, 759.0, 112.0, 223.0, 100.0, 650.0, 109.0, 133.0, 90.0, 556.0, 94.0, 371.0, 185.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018960969, -0.00851173, 0.011114489, -0.01680718, 0.00883577, -0.0059558977, -0.01395669, -0.014253147, 0.010894616, -0.0040541044, -0.013266153, -0.025415566, 0.050277147, 0.0068821493, -0.04310549, -0.0021317545, 0.01847779, -0.00056205085, -0.13807136, 0.011051933, -0.058749158, 0.054778934, -0.077683836, -0.0092469985, -0.021183826, 0.0017733146, -0.013929971, 0.013782731, -0.002779214, -0.014261599, 0.004111246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, 17, 19, -1, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5555019, 1.5790259, 0.0, 2.4111009, 0.0, 1.5854559, 0.0, 1.8730677, 0.0, 1.6573294, 0.0, 1.7087227, 2.8407626, 0.0, 3.48667, 1.8496234, 0.0, 2.5437253, 0.8981533, 0.0, 1.189014, 2.3795161, 1.9207118, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, 18, 20, -1, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3539915, 1.0, 0.011114489, 0.9504357, 0.00883577, 0.9271665, -0.01395669, 0.74297655, 0.010894616, 1.0, -0.013266153, 1.0, 0.5302295, 0.0068821493, 1.0, 1.0, 0.01847779, 0.37787387, 0.4185918, 0.011051933, 0.32285196, 1.0, 1.0, -0.0092469985, -0.021183826, 0.0017733146, -0.013929971, 0.013782731, -0.002779214, -0.014261599, 0.004111246], "split_indices": [139, 125, 0, 143, 0, 140, 0, 140, 0, 50, 0, 89, 139, 0, 109, 126, 0, 139, 140, 0, 143, 23, 121, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1965.0, 115.0, 1810.0, 155.0, 1663.0, 147.0, 1551.0, 112.0, 1428.0, 123.0, 1025.0, 403.0, 162.0, 863.0, 290.0, 113.0, 596.0, 267.0, 97.0, 193.0, 347.0, 249.0, 165.0, 102.0, 99.0, 94.0, 173.0, 174.0, 161.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.008447263, -0.014346092, 0.009121726, -0.006085884, -0.014326179, -0.017518757, 0.016401535, 0.009653016, -0.024223367, -0.031015914, 0.008214649, -0.018681763, -0.013154665, -0.0069064135, -0.011831931, -0.038380407, 0.116430685, 0.007612618, -0.051485788, 0.002014872, 0.020375617, -0.015321143, -0.003651026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, 9, 11, -1, 13, -1, 15, -1, 17, 19, -1, 21, -1, -1, -1, -1], "loss_changes": [1.2099059, 2.069043, 0.0, 3.5511055, 0.0, 1.308322, 0.0, 0.0, 1.1675959, 1.8835006, 0.0, 1.5874298, 0.0, 4.697112, 0.0, 1.4466293, 2.068335, 0.0, 1.3177364, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, 10, 12, -1, 14, -1, 16, -1, 18, 20, -1, 22, -1, -1, -1, -1], "split_conditions": [1.3539915, 1.0653163, 0.009121726, 1.0, -0.014326179, 0.08515571, 0.016401535, 0.009653016, 5.0, 2.0, 0.008214649, 0.79743034, -0.013154665, 0.5981277, -0.011831931, 0.14165041, -0.1923077, 0.007612618, 0.18284848, 0.002014872, 0.020375617, -0.015321143, -0.003651026], "split_indices": [139, 141, 0, 125, 0, 139, 0, 0, 0, 0, 0, 140, 0, 139, 0, 140, 1, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1943.0, 115.0, 1826.0, 117.0, 1711.0, 115.0, 95.0, 1616.0, 1519.0, 97.0, 1353.0, 166.0, 1210.0, 143.0, 964.0, 246.0, 99.0, 865.0, 117.0, 129.0, 111.0, 754.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.003438578, -0.00319175, 0.012559927, 0.00472232, -0.013452256, -0.011847566, 0.089891545, 0.003494571, -0.11134804, -0.012450076, 0.18177398, -0.010799458, 0.018209778, -0.020710794, -0.0035572778, 0.0072074602, 0.033385716, -0.035489924, 0.051773224, -0.007703466, -0.015354124, 0.019060822, 0.002993345, 0.04459344, -0.038849186, 0.005141561, -0.008793287, -0.003372629, 0.016243903, -0.06064408, 0.0015384597, 3.7839403e-05, -0.011691633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, -1, -1, 19, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [1.6677194, 2.029873, 0.0, 2.5995123, 0.0, 2.3539402, 5.909667, 3.4107544, 1.4947834, 0.0, 3.5035195, 1.9111011, 0.0, 0.0, 0.0, 0.0, 0.0, 2.9095616, 2.3703237, 1.1694963, 0.0, 0.0, 1.140339, 2.473543, 0.531909, 0.0, 0.0, 0.0, 0.0, 1.102273, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 17, 17, 18, 18, 19, 19, 22, 22, 23, 23, 24, 24, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, -1, -1, 20, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.3443526, 1.0969085, 0.012559927, 0.7990549, -0.013452256, 0.67203194, 1.0, 0.6242293, 0.715507, -0.012450076, 0.8212258, 0.88461536, 0.018209778, -0.020710794, -0.0035572778, 0.0072074602, 0.033385716, 0.54409826, 1.0, 1.0, -0.015354124, 0.019060822, 0.3515988, 1.0, 0.26923078, 0.005141561, -0.008793287, -0.003372629, 0.016243903, -0.23076923, 0.0015384597, 3.7839403e-05, -0.011691633], "split_indices": [140, 141, 0, 143, 0, 143, 89, 143, 143, 0, 139, 1, 0, 0, 0, 0, 0, 139, 108, 81, 0, 0, 143, 12, 1, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1953.0, 106.0, 1842.0, 111.0, 1542.0, 300.0, 1336.0, 206.0, 90.0, 210.0, 1237.0, 99.0, 91.0, 115.0, 122.0, 88.0, 887.0, 350.0, 718.0, 169.0, 91.0, 259.0, 268.0, 450.0, 169.0, 90.0, 161.0, 107.0, 321.0, 129.0, 154.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00070179784, 0.011927192, -0.0055832635, -0.047464807, 0.016086305, -0.07228127, 0.007208577, 0.16906835, -0.011082636, -0.030555515, -0.026886547, -0.00042505475, 0.03303751, 0.013924527, -0.0148548065, -0.011260976, 0.017143827, -0.009354915, 0.02309407, -0.0056392467, 0.013002852, 0.018941378, -0.016799243, -0.030993236, 0.076418996, 0.0056971866, -0.01169261, 0.1413958, -0.0042427457, -0.007762401, 0.04806391, 0.02819297, 0.004324513, 0.008240089, 0.0014112708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, -1, 19, 21, -1, -1, -1, 23, -1, 25, 27, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [1.5396259, 0.0, 1.780623, 1.9848042, 5.374174, 4.5442533, 0.0, 5.4517145, 3.7745078, 1.7886679, 0.0, 0.0, 0.0, 4.6933227, 0.0, 0.0, 2.3990242, 3.7661486, 0.0, 0.0, 0.0, 2.0435276, 0.0, 1.2012603, 2.55607, 0.9425222, 0.0, 2.9518075, 0.0, 0.0, 0.20634311, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24, 25, 25, 27, 27, 30, 30], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, -1, 20, 22, -1, -1, -1, 24, -1, 26, 28, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.011927192, -0.1923077, 1.1095077, -0.03846154, 1.4838048, 0.007208577, 1.0, 1.0, 0.40983817, -0.026886547, -0.00042505475, 0.03303751, 0.90278953, -0.0148548065, -0.011260976, 1.0, 0.67203194, 0.02309407, -0.0056392467, 0.013002852, 0.32555068, -0.016799243, 0.2985591, 1.0, 0.13915683, -0.01169261, 0.37428665, -0.0042427457, -0.007762401, 1.2195257, 0.02819297, 0.004324513, 0.008240089, 0.0014112708], "split_indices": [1, 0, 1, 143, 1, 138, 0, 111, 64, 142, 0, 0, 0, 140, 0, 0, 97, 143, 0, 0, 0, 140, 0, 141, 109, 142, 0, 142, 0, 0, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 104.0, 1962.0, 669.0, 1293.0, 554.0, 115.0, 195.0, 1098.0, 457.0, 97.0, 94.0, 101.0, 929.0, 169.0, 168.0, 289.0, 839.0, 90.0, 175.0, 114.0, 712.0, 127.0, 381.0, 331.0, 267.0, 114.0, 214.0, 117.0, 90.0, 177.0, 88.0, 126.0, 88.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016034788, 0.0070844195, -0.010159331, -0.00449949, 0.02270568, 0.0076726354, -0.012956808, -0.005278298, 0.019186372, 0.0043597757, -0.016386846, -0.004706742, 0.0142802475, 0.0039273184, -0.0125014335, -0.011527113, 0.06748991, 0.0033465414, -0.010425013, 0.02194698, -0.0030139526, -0.0011893034, 0.011329939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1], "loss_changes": [1.7930002, 4.838919, 0.0, 2.7463212, 0.0, 3.9216743, 0.0, 2.3477821, 0.0, 1.8175203, 0.0, 1.4116522, 0.0, 1.2455865, 0.0, 1.406713, 3.6797526, 1.4728832, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1], "split_conditions": [1.1391696, 1.1472435, -0.010159331, 1.4929663, 0.02270568, 0.7633737, -0.012956808, 0.7982205, 0.019186372, 0.7990549, -0.016386846, 0.69161683, 0.0142802475, 0.49934718, -0.0125014335, 1.3147875, 1.0, 0.4798773, -0.010425013, 0.02194698, -0.0030139526, -0.0011893034, 0.011329939], "split_indices": [143, 139, 0, 138, 0, 139, 0, 142, 0, 143, 0, 143, 0, 139, 0, 138, 53, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1899.0, 165.0, 1804.0, 95.0, 1644.0, 160.0, 1536.0, 108.0, 1448.0, 88.0, 1359.0, 89.0, 1268.0, 91.0, 1020.0, 248.0, 879.0, 141.0, 97.0, 151.0, 772.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.0009002511, -0.0055640195, 0.009731311, -0.09074688, 0.003933548, 0.00830208, -0.027759388, -0.007753114, 0.013371273, 0.006026187, -0.016763678, -0.008079513, 0.017605027, 0.0077549205, -0.10969832, -0.03624767, 0.035533097, -0.0037243653, -0.018215295, 0.0052631986, -0.06764828, 0.008616441, 0.012906845, -0.012728607, -0.013107161, 0.040538628, -0.010987437, -0.009927853, 0.007677184, 0.00057011825, 0.017524337], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, -1, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.2795098, 1.5565741, 0.0, 6.2663193, 2.6253827, 0.0, 0.0, 3.4984999, 0.0, 3.506328, 0.0, 2.1722534, 0.0, 1.42766, 0.9554405, 1.2614756, 1.8026435, 0.0, 0.0, 0.0, 1.1633847, 2.1030622, 0.0, 1.3865801, 0.0, 2.0554328, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 20, 20, 21, 21, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, -1, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.178437, -0.5, 0.009731311, 0.61207813, 0.9194964, 0.00830208, -0.027759388, 0.82049423, 0.013371273, 0.78044474, -0.016763678, 1.0, 0.017605027, 1.0, 0.31324527, 1.0, 0.5405575, -0.0037243653, -0.018215295, 0.0052631986, 1.0, 0.42503887, 0.012906845, 0.41560978, -0.013107161, 0.42850766, -0.010987437, -0.009927853, 0.007677184, 0.00057011825, 0.017524337], "split_indices": [142, 1, 0, 141, 142, 0, 0, 141, 0, 140, 0, 40, 0, 69, 141, 53, 142, 0, 0, 0, 124, 142, 0, 142, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1924.0, 129.0, 193.0, 1731.0, 100.0, 93.0, 1588.0, 143.0, 1462.0, 126.0, 1350.0, 112.0, 1168.0, 182.0, 452.0, 716.0, 91.0, 91.0, 118.0, 334.0, 556.0, 160.0, 179.0, 155.0, 438.0, 118.0, 91.0, 88.0, 348.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011560903, 0.00953312, -0.013735506, 0.0001719298, 0.016693978, 0.010244763, -0.012041426, -0.00015228268, 0.016740015, -0.078850806, 0.016021952, 0.00050158595, -0.019566512, 0.001650791, 0.015157071, 0.012889041, -0.010232742, 0.062462304, -0.020602182, 0.0002980431, 0.11935369, 0.005148526, -0.01421641, 0.0122089, -0.010284022, 0.024416883, 0.0033953835, -0.0031236669, 0.012504074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, -1, -1, 15, -1, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.0209088, 2.83504, 0.0, 2.2057946, 0.0, 2.738504, 0.0, 2.0009806, 0.0, 2.6255503, 2.5401826, 0.0, 0.0, 1.3777002, 0.0, 1.7665266, 0.0, 1.5172057, 1.9877445, 2.5750685, 2.387658, 2.2858465, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, -1, -1, 16, -1, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.638525, 1.0129824, -0.013735506, 1.486252, 0.016693978, 1.4485375, -0.012041426, -1.0, 0.016740015, 0.43578738, 0.7927253, 0.00050158595, -0.019566512, 0.67203194, 0.015157071, 1.0, -0.010232742, 0.44437784, 0.42396438, 0.33947557, 0.52861816, 1.0, -0.01421641, 0.0122089, -0.010284022, 0.024416883, 0.0033953835, -0.0031236669, 0.012504074], "split_indices": [138, 139, 0, 138, 0, 138, 0, 0, 0, 142, 143, 0, 0, 143, 0, 137, 0, 141, 141, 141, 141, 93, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1924.0, 151.0, 1816.0, 108.0, 1676.0, 140.0, 1572.0, 104.0, 268.0, 1304.0, 156.0, 112.0, 1179.0, 125.0, 1064.0, 115.0, 429.0, 635.0, 205.0, 224.0, 524.0, 111.0, 94.0, 111.0, 91.0, 133.0, 402.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012373396, 0.010523003, -0.094810985, -0.0039266245, 0.027860973, 0.0013677845, -0.026790557, 0.0068810084, -0.08928488, 0.01692347, -0.01283637, -0.0015095362, -0.016500404, 0.0024604436, 0.016656453, 0.01567031, -0.013793821, -0.005191045, 0.10869301, 0.019442007, -0.01738782, 7.436902e-06, 0.020431451, 0.00321215, 0.010688942, 0.00876826, -0.001213354], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1], "loss_changes": [2.2768483, 7.11996, 4.33791, 1.6088763, 0.0, 0.0, 0.0, 2.1024778, 1.101043, 3.1187017, 0.0, 0.0, 0.0, 2.4370067, 0.0, 2.3306365, 0.0, 4.0763297, 2.284981, 1.2148854, 0.0, 0.0, 0.0, 0.93589795, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1], "split_conditions": [1.0, 1.3539915, 1.4838048, 1.0, 0.027860973, 0.0013677845, -0.026790557, 1.0278977, 0.281656, 0.8048927, -0.01283637, -0.0015095362, -0.016500404, 1.4645756, 0.016656453, 0.64346665, -0.013793821, 0.54409826, 0.61207813, 1.0, -0.01738782, 7.436902e-06, 0.020431451, -1.0, 0.010688942, 0.00876826, -0.001213354], "split_indices": [119, 139, 138, 40, 0, 0, 0, 142, 140, 142, 0, 0, 0, 138, 0, 143, 0, 139, 141, 121, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1838.0, 231.0, 1744.0, 94.0, 142.0, 89.0, 1548.0, 196.0, 1441.0, 107.0, 99.0, 97.0, 1314.0, 127.0, 1201.0, 113.0, 981.0, 220.0, 856.0, 125.0, 103.0, 117.0, 722.0, 134.0, 111.0, 611.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0014946493, 0.007396757, -0.01074716, -1.3653828e-06, 0.0149257695, 0.012222504, -0.07455765, -0.001280759, 0.0137041155, -0.00037748227, -0.020983147, -0.08517088, 0.016398853, 0.0017759994, -0.019400766, 0.018082885, -0.0005008962, 0.022329055, -0.037083838, -0.016977016, 0.09666881, 0.009379535, -0.11591455, -0.06822497, 0.0060635465, 0.024349974, 5.4205808e-05, -0.023460893, -0.003498656, -0.0007993956, -0.016739314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3267778, 2.053881, 0.0, 1.6951416, 0.0, 2.6933637, 2.508666, 2.138695, 0.0, 0.0, 0.0, 2.811869, 3.3095822, 0.0, 0.0, 0.0, 0.90200174, 1.9431326, 4.2816796, 1.7302039, 3.2463076, 0.0, 2.4878752, 1.5649258, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.6902589, 1.0, -0.01074716, 1.0, 0.0149257695, 0.9492228, 0.6400806, -1.0, 0.0137041155, -0.00037748227, -0.020983147, 0.40983817, 0.0, 0.0017759994, -0.019400766, 0.018082885, 0.49431247, 0.36073053, 1.0, 0.26845157, 0.3730132, 0.009379535, 1.0, 0.213057, 0.0060635465, 0.024349974, 5.4205808e-05, -0.023460893, -0.003498656, -0.0007993956, -0.016739314], "split_indices": [138, 102, 0, 64, 0, 140, 139, 0, 0, 0, 0, 142, 0, 0, 0, 0, 143, 142, 39, 139, 139, 0, 111, 143, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1957.0, 106.0, 1860.0, 97.0, 1598.0, 262.0, 1442.0, 156.0, 172.0, 90.0, 251.0, 1191.0, 129.0, 122.0, 111.0, 1080.0, 665.0, 415.0, 435.0, 230.0, 156.0, 259.0, 262.0, 173.0, 91.0, 139.0, 105.0, 154.0, 163.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024922523, 0.005914905, -0.009925101, -0.004549109, 0.020030826, 0.006267141, -0.01170929, -0.004553453, 0.015864892, 0.0032225582, -0.0119830845, 0.037067305, -0.025812207, 0.015369971, 0.012378685, -0.00348044, -0.015510136, 0.014819644, -0.018309437, -0.036477048, 0.009963394, -0.08046589, 0.007829944, -0.010444068, -0.014603247, -0.0014610685, -0.02051704, -0.009510568, 0.0026182577], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, 17, 19, -1, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [1.6789943, 3.8628225, 0.0, 2.193578, 0.0, 2.710729, 0.0, 1.3759718, 0.0, 1.4130856, 0.0, 1.911983, 2.2347353, 0.0, 2.2840602, 2.2456002, 0.0, 0.0, 2.6841743, 1.4260268, 0.0, 2.2337832, 0.0, 1.2527521, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 18, 18, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, 18, 20, -1, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.1391696, 1.1472435, -0.009925101, 1.4929663, 0.020030826, 1.4485375, -0.01170929, 0.7982205, 0.015864892, 1.0, -0.0119830845, 1.0, 0.500368, 0.015369971, 0.33947557, 1.0, -0.015510136, 0.014819644, 0.6520932, 1.0, 0.009963394, 1.0, 0.007829944, 1.0, -0.014603247, -0.0014610685, -0.02051704, -0.009510568, 0.0026182577], "split_indices": [143, 139, 0, 138, 0, 138, 0, 142, 0, 137, 0, 89, 140, 0, 141, 93, 0, 0, 143, 0, 0, 23, 0, 124, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1899.0, 165.0, 1802.0, 97.0, 1644.0, 158.0, 1535.0, 109.0, 1438.0, 97.0, 664.0, 774.0, 116.0, 548.0, 660.0, 114.0, 101.0, 447.0, 500.0, 160.0, 272.0, 175.0, 404.0, 96.0, 178.0, 94.0, 122.0, 282.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0039818645, -0.006520235, 0.055269115, 0.0002466141, -0.012219776, 0.0017039805, 0.01812747, -0.009940246, 0.009217991, 0.010122886, -0.015220043, 0.0007371002, -0.016532974, -0.011497126, 0.017241962, 0.009128125, -0.016883438, -0.017540008, 0.12861691, -0.0037518605, -0.0117380945, 0.021785168, 0.0026368708, -0.037907824, 0.09093163, 0.0094208, -0.006264665, -0.0012496555, 0.021441225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [1.1154901, 1.3455862, 2.3758252, 1.5208951, 0.0, 3.7833776, 0.0, 2.4256742, 0.0, 0.0, 0.0, 2.8733518, 0.0, 4.144019, 0.0, 3.5976074, 0.0, 1.2706213, 1.8795631, 2.6227787, 0.0, 0.0, 0.0, 1.9479606, 2.7458465, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.5964612, 1.0, -0.012219776, 0.36772123, 0.01812747, 1.1306531, 0.009217991, 0.010122886, -0.015220043, 0.957012, -0.016532974, 0.74083877, 0.017241962, 0.58333963, -0.016883438, 0.51875687, 1.0, 1.0, -0.0117380945, 0.021785168, 0.0026368708, 0.1497886, 0.26923078, 0.0094208, -0.006264665, -0.0012496555, 0.021441225], "split_indices": [62, 52, 139, 125, 0, 139, 0, 143, 0, 0, 0, 142, 0, 142, 0, 142, 0, 142, 106, 71, 0, 0, 0, 141, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1719.0, 352.0, 1624.0, 95.0, 247.0, 105.0, 1462.0, 162.0, 150.0, 97.0, 1368.0, 94.0, 1277.0, 91.0, 1129.0, 148.0, 923.0, 206.0, 811.0, 112.0, 110.0, 96.0, 596.0, 215.0, 94.0, 502.0, 117.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.00020082499, 0.005582079, -0.012768176, -0.0045683174, 0.018364092, 0.0033687793, -0.0144135505, -0.0085417675, 0.099382386, 0.0023978392, -0.01721939, 0.024825053, -0.006549305, -0.006394766, 0.013213555, 0.006788905, -0.078261115, -0.015585182, 0.08299539, -0.0012078036, -0.015580893, 0.009810973, -0.070359476, -0.0033542167, 0.015780965, -0.0012546703, 0.012724526, -0.002087292, -0.0142263], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5289739, 3.5858185, 0.0, 2.0792627, 0.0, 2.0309887, 0.0, 2.8286583, 4.810762, 1.6894252, 0.0, 0.0, 0.0, 1.3141305, 0.0, 1.9983196, 1.1034555, 1.2602974, 2.3191671, 0.0, 0.0, 1.6252205, 1.0212195, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4291958, 1.1185367, -0.012768176, 1.1319219, 0.018364092, 1.4726204, -0.0144135505, 0.7982205, 1.0, 0.6907201, -0.01721939, 0.024825053, -0.006549305, 1.3717041, 0.013213555, 0.4294398, 1.0, 0.32285196, 1.0, -0.0012078036, -0.015580893, 0.36073053, 0.35322598, -0.0033542167, 0.015780965, -0.0012546703, 0.012724526, -0.002087292, -0.0142263], "split_indices": [143, 142, 0, 140, 0, 138, 0, 142, 69, 142, 0, 0, 0, 138, 0, 139, 106, 143, 69, 0, 0, 142, 139, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1984.0, 90.0, 1877.0, 107.0, 1776.0, 101.0, 1580.0, 196.0, 1481.0, 99.0, 103.0, 93.0, 1387.0, 94.0, 1172.0, 215.0, 906.0, 266.0, 116.0, 99.0, 619.0, 287.0, 104.0, 162.0, 520.0, 99.0, 170.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0009251723, 0.006556082, -0.011112421, 0.014477645, -0.008492029, 0.005197151, 0.017865624, -0.0099019725, 0.08466043, 0.005097463, -0.020489467, -0.005815524, 0.027272848, -0.031600397, 0.03462391, 0.008207881, -0.009660751, 0.011270386, -0.015071607, 0.0061201425, 0.016278528, 0.082154326, -0.08605133, 0.016541356, -0.024549311, 0.0011837374, 0.02102874, -0.0014608841, -0.015827034, -0.05652198, 0.009700713, -0.010109048, 0.0022779205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, -1, -1, 21, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [1.3054146, 1.4275323, 0.0, 2.762392, 0.0, 2.0589015, 0.0, 4.217534, 4.662285, 1.4508834, 0.0, 1.4763187, 0.0, 3.0486302, 2.7105863, 0.0, 0.0, 3.0284617, 0.0, 2.9654636, 0.0, 2.2885208, 0.9545078, 0.0, 1.97822, 0.0, 0.0, 0.0, 0.0, 1.4243375, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, -1, -1, 22, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.638525, -0.011112421, 1.0355495, -0.008492029, 1.0, 0.017865624, 0.8762413, 0.6844134, 1.0, -0.020489467, 0.45108533, 0.027272848, 1.3191732, 0.69646746, 0.008207881, -0.009660751, 1.0, -0.015071607, 0.21849972, 0.016278528, 1.2614771, 1.0, 0.016541356, 1.0, 0.0011837374, 0.02102874, -0.0014608841, -0.015827034, 0.65384614, 0.009700713, -0.010109048, 0.0022779205], "split_indices": [117, 138, 0, 139, 0, 42, 0, 142, 142, 39, 0, 139, 0, 138, 139, 0, 0, 12, 0, 143, 0, 138, 106, 0, 105, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1970.0, 99.0, 1813.0, 157.0, 1716.0, 97.0, 1442.0, 274.0, 1339.0, 103.0, 185.0, 89.0, 597.0, 742.0, 94.0, 91.0, 439.0, 158.0, 607.0, 135.0, 254.0, 185.0, 98.0, 509.0, 164.0, 90.0, 93.0, 92.0, 403.0, 106.0, 258.0, 145.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0030897807, -0.0028918197, 0.011720481, 0.0052419133, -0.013874717, -0.0058727507, 0.015897997, 0.0015482883, -0.014262001, -0.014956872, 0.048883848, 0.0036526166, -0.11063597, 0.019316943, -0.006755682, -0.017283417, 0.020944942, -0.020924978, -0.0026768125, 0.012467311, -0.06325779, -0.031547092, 0.011489327, -0.020800522, 0.008422068, 0.009312562, -0.04950407, -0.007129208, 0.0029876425], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, 21, -1, -1, -1, -1, 23, 25, -1, -1, -1, -1, 27, -1, -1], "loss_changes": [1.4115975, 2.1713476, 0.0, 3.1680171, 0.0, 1.7546015, 0.0, 1.2813009, 0.0, 2.165136, 3.403865, 4.386123, 1.6375635, 0.0, 2.272357, 1.7420404, 0.0, 0.0, 0.0, 0.0, 4.5682864, 1.8671132, 0.0, 0.0, 0.0, 0.0, 1.2608371, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 20, 20, 21, 21, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, 22, -1, -1, -1, -1, 24, 26, -1, -1, -1, -1, 28, -1, -1], "split_conditions": [1.3443526, 1.0969085, 0.011720481, 1.0, -0.013874717, 0.97168744, 0.015897997, 0.5676343, -0.014262001, 0.51125157, 0.0, 0.457961, 1.0, 0.019316943, 1.3613431, 0.49446788, 0.020944942, -0.020924978, -0.0026768125, 0.012467311, 1.0, 0.09178938, 0.011489327, -0.020800522, 0.008422068, 0.009312562, 1.0, -0.007129208, 0.0029876425], "split_indices": [140, 141, 0, 125, 0, 140, 0, 140, 0, 141, 0, 141, 121, 0, 138, 139, 0, 0, 0, 0, 53, 139, 0, 0, 0, 0, 62, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1965.0, 103.0, 1854.0, 111.0, 1729.0, 125.0, 1640.0, 89.0, 1216.0, 424.0, 1018.0, 198.0, 118.0, 306.0, 924.0, 94.0, 91.0, 107.0, 92.0, 214.0, 834.0, 90.0, 108.0, 106.0, 105.0, 729.0, 572.0, 157.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.002032954, 0.0068770302, -0.072927125, -0.00395777, 0.021731295, 0.0043814736, -0.02626327, 0.005818376, -0.010443134, -0.0076747504, 0.012923323, 0.004698086, -0.08413889, -0.006046502, 0.014486616, -0.021696655, 0.0036038482, -0.017888907, 0.009635544, -0.006022492, -0.010693603, -0.024018688, 0.010670565, -0.0008787038, -0.013302015], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.3069171, 4.1906977, 5.115861, 1.7169633, 0.0, 0.0, 0.0, 2.6527474, 0.0, 1.3585688, 0.0, 1.8614761, 3.1925764, 1.3921626, 0.0, 0.0, 0.0, 1.0873133, 0.0, 1.8420391, 0.0, 1.2999926, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.1882018, 1.4838048, 0.98746014, 0.021731295, 0.0043814736, -0.02626327, 0.85294276, -0.010443134, 0.67203194, 0.012923323, 0.6242293, 1.0, 3.0384614, 0.014486616, -0.021696655, 0.0036038482, 0.53141505, 0.009635544, 0.4940717, -0.010693603, 1.0, 0.010670565, -0.0008787038, -0.013302015], "split_indices": [119, 141, 138, 140, 0, 0, 0, 143, 0, 143, 0, 143, 13, 1, 0, 0, 0, 143, 0, 141, 0, 42, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1838.0, 231.0, 1748.0, 90.0, 143.0, 88.0, 1593.0, 155.0, 1436.0, 157.0, 1236.0, 200.0, 1148.0, 88.0, 95.0, 105.0, 1029.0, 119.0, 908.0, 121.0, 783.0, 125.0, 687.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.007598203, 0.01602959, -0.063738324, 0.02637173, -0.06535852, -0.0010533094, -0.01021643, 0.014166885, 0.023507454, 0.0023947377, -0.01686185, 0.023876695, -0.0110748485, 0.01018392, 0.016536867, 0.024769718, -0.0095424615, 0.007283352, 0.09063501, 0.023773164, -0.0077633555, 0.0013746882, 0.014746535, 0.00096286327, 0.012700224, 0.0029718678, -0.008835154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1], "loss_changes": [1.2348094, 1.5454109, 0.44364816, 4.1493645, 1.9088976, 0.0, 0.0, 1.8666598, 0.0, 0.0, 0.0, 2.7666323, 0.0, 2.0055807, 0.0, 1.3175955, 0.0, 1.2658383, 1.0486983, 1.782497, 0.0, 0.0, 0.0, 1.592351, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.3539915, 0.80717355, -0.0010533094, -0.01021643, 1.0278977, 0.023507454, 0.0023947377, -0.01686185, 0.8048927, -0.0110748485, 0.7270401, 0.016536867, 0.5405575, -0.0095424615, 0.45182768, -0.1923077, 0.3898465, -0.0077633555, 0.0013746882, 0.014746535, 0.32514927, 0.012700224, 0.0029718678, -0.008835154], "split_indices": [40, 119, 126, 139, 141, 0, 0, 142, 0, 0, 0, 142, 0, 139, 0, 142, 0, 142, 1, 142, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1836.0, 217.0, 1629.0, 207.0, 91.0, 126.0, 1539.0, 90.0, 111.0, 96.0, 1428.0, 111.0, 1302.0, 126.0, 1144.0, 158.0, 904.0, 240.0, 757.0, 147.0, 102.0, 138.0, 620.0, 137.0, 469.0, 151.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021548436, -0.011908121, 0.045689303, -0.0049854154, -0.012716757, -0.002967594, 0.11809076, -0.022047993, 0.06859694, 0.005767763, 0.017850386, 0.002892375, -0.1191107, -0.012057225, 0.14566588, -0.042708155, 0.068676755, -0.02086426, -0.0005298963, 0.0018063514, 0.032094385, 0.020103503, -0.09679071, 0.021843849, 0.024159823, -0.024709225, 0.009928719, -0.04993398, -0.021149205, 0.08567823, -0.014494917, 2.8776218e-05, -0.0050829705, -0.011425385, 0.0050041433, -0.0048963455, 0.024316093], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, 17, -1, 19, 21, 23, -1, -1, -1, -1, 25, 27, 29, -1, 31, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9617393, 1.3660169, 1.9043374, 2.0276396, 0.0, 0.0, 0.6496539, 3.1736426, 4.432038, 0.0, 0.0, 3.1287942, 2.73086, 0.0, 4.83103, 2.0925617, 3.458022, 0.0, 0.0, 0.0, 0.0, 1.0113047, 1.7789686, 3.577435, 0.0, 0.11883403, 0.0, 1.5111454, 0.0, 5.1525083, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 21, 21, 22, 22, 23, 23, 25, 25, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, 18, -1, 20, 22, 24, -1, -1, -1, -1, 26, 28, 30, -1, 32, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.6902589, 0.43214852, 1.0, -0.012716757, -0.002967594, -0.15384616, 0.74083877, -0.42307693, 0.005767763, 0.017850386, 0.4125711, 0.9952312, -0.012057225, 0.58333963, 0.22602426, 1.1538461, -0.02086426, -0.0005298963, 0.0018063514, 0.032094385, 0.20446515, 0.96153843, 0.63661104, 0.024159823, 0.46153846, 0.009928719, 1.0, -0.021149205, 0.5326129, -0.014494917, 2.8776218e-05, -0.0050829705, -0.011425385, 0.0050041433, -0.0048963455, 0.024316093], "split_indices": [62, 138, 142, 42, 0, 0, 1, 142, 1, 0, 0, 139, 142, 0, 142, 139, 1, 0, 0, 0, 0, 142, 1, 141, 0, 1, 0, 71, 0, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1712.0, 349.0, 1615.0, 97.0, 171.0, 178.0, 1311.0, 304.0, 89.0, 89.0, 1043.0, 268.0, 88.0, 216.0, 616.0, 427.0, 150.0, 118.0, 125.0, 91.0, 285.0, 331.0, 336.0, 91.0, 182.0, 103.0, 235.0, 96.0, 243.0, 93.0, 93.0, 89.0, 143.0, 92.0, 131.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.006015699, 0.010109434, -0.011893691, -0.006037517, -0.011374729, -0.011723361, 0.008109552, -0.05043797, 0.010329701, -0.02589577, -0.018968262, 0.016251864, -0.0075479127, -0.045739803, 0.0058104186, -0.021323912, 0.011188864, 0.002062962, -0.08856688, 0.006979259, -0.0146200955, -0.014198485, -0.0007168024, -0.11188217, 0.044877112, -0.0045779925, -0.017798437, -0.016058065, 0.022081673, -0.008091776, 0.011902562], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, 11, 13, -1, -1, 15, 17, -1, 19, -1, -1, 21, 23, -1, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1], "loss_changes": [1.3070323, 0.0, 1.1738571, 0.92198545, 0.0, 1.4915462, 0.0, 2.166612, 3.0282233, 0.8984579, 0.0, 0.0, 1.6387763, 1.2392902, 0.0, 3.1562343, 0.0, 0.0, 1.1522627, 3.2793436, 0.0, 0.0, 0.0, 0.76903224, 5.917944, 0.0, 0.0, 3.592209, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 15, 15, 18, 18, 19, 19, 23, 23, 24, 24, 27, 27], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, 12, 14, -1, -1, 16, 18, -1, 20, -1, -1, 22, 24, -1, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1], "split_conditions": [-0.5769231, 0.010109434, 1.0, 3.1923077, -0.011374729, 0.3898465, 0.008109552, 0.31750953, 0.45182768, 1.0, -0.018968262, 0.016251864, 0.84615386, 1.0, 0.0058104186, 0.15384616, 0.011188864, 0.002062962, 1.2404901, 0.55677325, -0.0146200955, -0.014198485, -0.0007168024, 1.3656291, -0.115384616, -0.0045779925, -0.017798437, 1.0, 0.022081673, -0.008091776, 0.011902562], "split_indices": [1, 0, 43, 1, 0, 142, 0, 142, 142, 62, 0, 0, 1, 53, 0, 1, 0, 0, 138, 143, 0, 0, 0, 138, 1, 0, 0, 61, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 108.0, 1968.0, 1861.0, 107.0, 1747.0, 114.0, 634.0, 1113.0, 539.0, 95.0, 117.0, 996.0, 436.0, 103.0, 893.0, 103.0, 171.0, 265.0, 728.0, 165.0, 160.0, 105.0, 176.0, 552.0, 88.0, 88.0, 410.0, 142.0, 277.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0013883951, 0.0068229837, -0.010680929, 0.011699992, 0.0014284754, 0.012897615, -0.08245862, 0.0034248866, 0.011856031, 0.005714209, -0.020577261, -0.06562296, 0.023674216, -0.016951434, -0.018272376, -0.008526153, 0.12216948, 0.0074335793, -0.015435286, 0.024821453, -0.017848397, 0.022506468, 0.00031488723, -0.009131683, 0.01753637, 0.03872352, -0.07183851, -0.002085833, 0.010893146, -0.013662422, -0.0023140511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [1.2171803, 1.1714642, 0.0, 0.0, 1.8078098, 1.6545104, 3.8905275, 2.121028, 0.0, 0.0, 0.0, 1.9606186, 3.7202682, 3.047948, 0.0, 5.010235, 3.539281, 0.0, 0.0, 3.7773113, 0.0, 0.0, 0.0, 1.8095117, 0.0, 1.4306269, 0.8234376, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, 0.09178938, -0.010680929, 0.011699992, 1.3461539, 0.88461536, 0.33157113, 1.0, 0.011856031, 0.005714209, -0.020577261, 1.0, 0.8080213, 0.87744504, -0.018272376, 0.67203194, 0.9800058, 0.0074335793, -0.015435286, 0.55677325, -0.017848397, 0.022506468, 0.00031488723, 0.31759897, 0.01753637, 0.22482131, 0.44043657, -0.002085833, 0.010893146, -0.013662422, -0.0023140511], "split_indices": [117, 139, 0, 0, 1, 1, 143, 89, 0, 0, 0, 106, 143, 139, 0, 143, 142, 0, 0, 143, 0, 0, 0, 143, 0, 143, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1971.0, 99.0, 92.0, 1879.0, 1653.0, 226.0, 1517.0, 136.0, 106.0, 120.0, 344.0, 1173.0, 243.0, 101.0, 884.0, 289.0, 146.0, 97.0, 739.0, 145.0, 155.0, 134.0, 603.0, 136.0, 342.0, 261.0, 185.0, 157.0, 112.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010465864, -0.0072791553, 0.009210372, 0.004834978, -0.07857949, -0.021893311, 0.035686065, 0.006281964, -0.022829624, 0.0024890825, -0.017544195, 0.00018795115, 0.017652597, -0.008974761, 0.023981132, 0.009547049, -0.022618005, 0.050293416, -0.013038424, -0.054644246, 0.009717242, 0.014351473, 0.02041249, -0.14487562, 0.019913124, 0.011205704, -0.023276228, -0.0052371896, -0.023737932, -0.0058272243, 0.01207975, 0.02989728, -0.010154766, -0.0061263363, 0.011717877], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, 15, -1, -1, 17, -1, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.1942234, 1.6652943, 0.0, 1.3589355, 5.92755, 3.3058496, 3.8246558, 0.0, 0.0, 1.5105547, 0.0, 1.3277091, 0.0, 0.0, 2.510134, 0.0, 1.8913631, 2.9193125, 0.0, 2.6169646, 0.0, 1.5735149, 0.0, 1.5060196, 1.6800766, 0.0, 1.2860479, 0.0, 0.0, 0.0, 0.0, 1.4640214, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 11, 11, 14, 14, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, 16, -1, -1, 18, -1, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.178437, 0.8248565, 0.009210372, 1.0, 1.0, 0.65766674, 0.6904789, 0.006281964, -0.022829624, -1.0, -0.017544195, 1.2155514, 0.017652597, -0.008974761, 1.0, 0.009547049, 1.0, 0.52527964, -0.013038424, 1.0, 0.009717242, -0.115384616, 0.02041249, 0.4427616, 0.4294398, 0.011205704, 0.28287697, -0.0052371896, -0.023737932, -0.0058272243, 0.01207975, 0.2149753, -0.010154766, -0.0061263363, 0.011717877], "split_indices": [142, 142, 0, 39, 69, 139, 139, 0, 0, 0, 0, 138, 0, 0, 43, 0, 105, 142, 0, 69, 0, 1, 0, 141, 139, 0, 142, 0, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1928.0, 129.0, 1648.0, 280.0, 883.0, 765.0, 144.0, 136.0, 762.0, 121.0, 611.0, 154.0, 144.0, 618.0, 118.0, 493.0, 528.0, 90.0, 389.0, 104.0, 428.0, 100.0, 176.0, 213.0, 119.0, 309.0, 88.0, 88.0, 120.0, 93.0, 184.0, 125.0, 90.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0012303534, 0.008645991, -0.008701572, -0.0012296832, 0.019463789, 0.009527605, -0.0110529065, -0.0011720905, 0.01589343, 0.010566425, -0.069568515, -0.00034334025, 0.011317019, 0.0014737931, -0.019144636, 0.016509667, -0.010988791, -0.031644057, 0.052461177, 0.049596094, -0.13910459, 0.00483864, 0.028768761, -0.003945647, 0.016293576, -0.004021584, -0.023289595, 0.0064409333, -0.006448477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3506832, 3.4972591, 0.0, 2.1257834, 0.0, 2.6313062, 0.0, 1.2332125, 0.0, 1.4675114, 2.3118954, 2.1876945, 0.0, 0.0, 0.0, 1.7779415, 0.0, 3.8325186, 6.586822, 2.5232973, 1.7529569, 2.0193956, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1391696, 1.1472435, -0.008701572, 1.4929663, 0.019463789, 0.7633737, -0.0110529065, 2.0, 0.01589343, 0.7082679, 0.51125157, 0.5529485, 0.011317019, 0.0014737931, -0.019144636, 1.0, -0.010988791, 0.3688509, 0.44437784, 0.26866648, 0.44740093, 0.3025744, 0.028768761, -0.003945647, 0.016293576, -0.004021584, -0.023289595, 0.0064409333, -0.006448477], "split_indices": [143, 139, 0, 138, 0, 139, 0, 0, 0, 141, 141, 141, 0, 0, 0, 124, 0, 141, 141, 143, 139, 141, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1904.0, 160.0, 1808.0, 96.0, 1646.0, 162.0, 1536.0, 110.0, 1311.0, 225.0, 1185.0, 126.0, 133.0, 92.0, 1027.0, 158.0, 439.0, 588.0, 250.0, 189.0, 489.0, 99.0, 140.0, 110.0, 92.0, 97.0, 263.0, 226.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033690077, 0.0043052696, -0.06449746, -0.0058163214, 0.01634903, 0.0034891614, -0.021740377, 0.008006791, -0.089601316, -0.004569572, 0.014020487, -0.023704754, 0.0007035713, 0.0086441375, -0.012408026, -0.017161936, 0.06468019, 0.04678372, -0.039999675, 0.13918488, -0.0021171, -0.0023627903, 0.013443904, 0.0069533084, -0.07737043, 0.023149049, 0.005277107, 0.0070138895, -0.009283855, 0.008299482, -0.007309036, -0.01788394, -0.022768276, 0.0070217797, -0.009836673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.9715409, 2.9646192, 3.5105572, 2.0036335, 0.0, 0.0, 0.0, 2.468918, 3.4909482, 2.1413677, 0.0, 0.0, 0.0, 1.7656522, 0.0, 1.2208728, 1.9160343, 1.35783, 1.0808759, 1.4517181, 1.3306993, 0.0, 0.0, 1.6616533, 1.9003649, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5676016, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.6541643, 1.4758557, 1.0, 0.01634903, 0.0034891614, -0.021740377, 0.9492228, 1.0, 0.74297655, 0.014020487, -0.023704754, 0.0007035713, 0.4581304, -0.012408026, 0.16432898, 1.0, 0.11709295, 0.2940117, 0.51125157, 1.0, -0.0023627903, 0.013443904, 1.0, 1.0, 0.023149049, 0.005277107, 0.0070138895, -0.009283855, 0.008299482, -0.007309036, -0.01788394, 0.3643141, 0.0070217797, -0.009836673], "split_indices": [119, 138, 138, 64, 0, 0, 0, 140, 124, 140, 0, 0, 0, 140, 0, 143, 115, 143, 140, 141, 62, 0, 0, 12, 59, 0, 0, 0, 0, 0, 0, 0, 142, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1840.0, 231.0, 1730.0, 110.0, 140.0, 91.0, 1485.0, 245.0, 1356.0, 129.0, 97.0, 148.0, 1221.0, 135.0, 836.0, 385.0, 220.0, 616.0, 182.0, 203.0, 122.0, 98.0, 273.0, 343.0, 88.0, 94.0, 113.0, 90.0, 140.0, 133.0, 120.0, 223.0, 100.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046241917, 0.0018897728, -0.012332313, -0.011459269, 0.064909674, -0.000117803735, -0.013965216, -0.02664385, 0.022531148, -0.009826762, 0.015478417, 0.005755355, -0.014962885, 0.010914439, -0.018363625, -0.005678813, -0.012786338, 0.008520067, -0.014792317, 0.0028352793, -0.09563931, -0.019392662, 0.009187408, -5.0327468e-05, -0.020240305, 0.00012906283, -0.014226372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [1.6051651, 1.6555907, 0.0, 2.3611252, 5.051761, 2.2438731, 0.0, 2.267749, 0.0, 1.4259592, 0.0, 0.0, 0.0, 0.0, 1.8195686, 0.9723423, 0.0, 0.0, 1.5206228, 1.7337348, 1.9400012, 1.7815062, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [1.6902589, 1.0, -0.012332313, 0.9457361, 0.7132902, 1.5125633, -0.013965216, 1.3483206, 0.022531148, -0.5, 0.015478417, 0.005755355, -0.014962885, 0.010914439, 0.7147042, 0.1010785, -0.012786338, 0.008520067, 1.3461539, 0.5676343, 0.37428665, 0.48414886, 0.009187408, -5.0327468e-05, -0.020240305, 0.00012906283, -0.014226372], "split_indices": [138, 42, 0, 141, 142, 138, 0, 138, 0, 1, 0, 0, 0, 0, 139, 139, 0, 0, 1, 140, 142, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1968.0, 108.0, 1624.0, 344.0, 1492.0, 132.0, 219.0, 125.0, 1404.0, 88.0, 130.0, 89.0, 94.0, 1310.0, 1174.0, 136.0, 107.0, 1067.0, 876.0, 191.0, 701.0, 175.0, 101.0, 90.0, 600.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0036638586, -0.009415314, 0.008494944, 0.013893967, 0.002303166, -0.0062164557, 0.08013108, 0.0023615917, -0.013838504, 0.024972765, -0.010280452, -0.0113581, 0.020421768, 0.002195014, -0.018428583, -0.013276455, 0.01288548, 0.010253158, -0.10003069, -0.0069781654, 0.01821748, -0.02320203, -0.00014784228, 0.0011765392, -0.006812222], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [0.9739505, 0.0, 1.5862925, 0.0, 1.243246, 1.9160352, 5.7396717, 4.395044, 0.0, 0.0, 0.0, 3.4827523, 0.0, 2.700347, 0.0, 2.5067089, 0.0, 2.861715, 3.4080632, 1.0062382, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [0.09120955, -0.009415314, 0.13833226, 0.013893967, 1.0, 1.1306531, 1.0029843, 0.9884085, -0.013838504, 0.024972765, -0.010280452, 0.82049423, 0.020421768, 1.4540515, -0.018428583, 0.54409826, 0.01288548, 0.49648446, 1.0, 1.2307693, 0.01821748, -0.02320203, -0.00014784228, 0.0011765392, -0.006812222], "split_indices": [143, 0, 143, 0, 125, 143, 141, 141, 0, 0, 0, 141, 0, 138, 0, 139, 0, 139, 111, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 97.0, 1964.0, 89.0, 1875.0, 1690.0, 185.0, 1587.0, 103.0, 96.0, 89.0, 1486.0, 101.0, 1378.0, 108.0, 1228.0, 150.0, 966.0, 262.0, 878.0, 88.0, 112.0, 150.0, 672.0, 206.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020537178, 0.010301202, -0.007682241, -0.041142497, 0.009696339, -0.076632604, 0.006661059, 0.15079802, -0.014826336, -0.026262414, -0.027515039, 0.003997537, 0.027516564, 0.009496207, -0.01486723, -0.01292167, 0.036998637, -0.009606574, 0.019227512, -0.0020605808, 0.013198473, 0.020496806, -0.012517588, 0.06237607, -0.058207314, -0.02477273, 0.17951156, 0.0011502745, -0.012327007, 0.0046053426, -0.007522426, 0.027128953, 0.008161504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, -1, 19, 21, -1, -1, -1, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2211682, 0.0, 1.1397239, 2.5621932, 4.4636464, 5.039686, 0.0, 2.6325045, 3.5777664, 2.6182246, 0.0, 0.0, 0.0, 3.2471755, 0.0, 0.0, 1.362434, 2.9293406, 0.0, 0.0, 0.0, 2.2017753, 0.0, 4.4507837, 1.052243, 0.89332205, 1.6711617, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, -1, 20, 22, -1, -1, -1, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010301202, -0.1923077, 1.0, -0.03846154, 0.78798014, 0.006661059, 0.5665408, 1.0, 0.40983817, -0.027515039, 0.003997537, 0.027516564, 0.91670126, -0.01486723, -0.01292167, 1.0, 0.61223036, 0.019227512, -0.0020605808, 0.013198473, 1.0, -0.012517588, 0.33120996, 0.27718738, 1.0, 1.0, 0.0011502745, -0.012327007, 0.0046053426, -0.007522426, 0.027128953, 0.008161504], "split_indices": [1, 0, 1, 61, 1, 140, 0, 143, 64, 142, 0, 0, 0, 140, 0, 0, 97, 143, 0, 0, 0, 109, 0, 140, 143, 23, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 105.0, 1960.0, 670.0, 1290.0, 504.0, 166.0, 191.0, 1099.0, 402.0, 102.0, 101.0, 90.0, 930.0, 169.0, 153.0, 249.0, 842.0, 88.0, 155.0, 94.0, 668.0, 174.0, 436.0, 232.0, 250.0, 186.0, 112.0, 120.0, 104.0, 146.0, 96.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0038859965, 0.012363324, -0.048302542, 0.0040024556, 0.014126003, -0.022997474, 0.034275718, 0.012507654, -0.014660641, 0.015171095, -0.0090499185, 0.006592083, 0.011134418, 0.015495434, -0.008480215, 0.0056963325, 0.015322722, 0.019855684, -0.06667367, 0.0023734125, 0.01855045, -0.016181977, 0.005701623, -0.01398307, 0.06972365, -0.0062741055, 0.0020320113, 0.00017062575, 0.014147823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.9118262, 1.910741, 4.320627, 2.132795, 0.0, 0.0, 2.9012883, 0.9214467, 0.0, 0.0, 0.0, 1.2099941, 0.0, 1.8287725, 0.0, 1.296261, 0.0, 3.0638804, 2.436103, 1.0542438, 0.0, 0.0, 0.0, 1.2878668, 0.9126641, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.0078171, 1.0, 0.9194964, 0.89404505, 0.014126003, -0.022997474, 1.0, 0.8048927, -0.014660641, 0.015171095, -0.0090499185, 0.68092453, 0.011134418, 0.6847645, -0.008480215, 0.51125157, 0.015322722, 0.5302295, 1.0, 1.0, 0.01855045, -0.016181977, 0.005701623, 1.0, 1.0, -0.0062741055, 0.0020320113, 0.00017062575, 0.014147823], "split_indices": [140, 125, 142, 141, 0, 0, 69, 142, 0, 0, 0, 141, 0, 140, 0, 141, 0, 139, 121, 62, 0, 0, 0, 108, 74, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1773.0, 288.0, 1665.0, 108.0, 90.0, 198.0, 1576.0, 89.0, 102.0, 96.0, 1487.0, 89.0, 1355.0, 132.0, 1265.0, 90.0, 1058.0, 207.0, 957.0, 101.0, 117.0, 90.0, 770.0, 187.0, 318.0, 452.0, 96.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015695902, 0.009755111, -0.092117764, -0.0016995575, 0.022142282, 0.0019578503, -0.017567683, 0.007461535, -0.0103044165, -0.0034184773, 0.016323404, 0.009197717, -0.018648474, -0.0014903853, 0.015199959, 0.017100742, -0.080052905, -0.00383859, 0.10320737, -0.015966421, 0.0035816978, 0.011781199, -0.011605847, 0.017163482, 0.0015772287, -0.0032684344, 0.0053004473], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [2.1123884, 4.439413, 2.1373112, 1.6126786, 0.0, 0.0, 0.0, 2.699828, 0.0, 3.438994, 0.0, 2.1261098, 0.0, 1.8928932, 0.0, 1.8895605, 2.2876892, 1.4776535, 1.2265058, 0.0, 0.0, 1.3564312, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3539915, 0.6844134, 1.5272014, 0.022142282, 0.0019578503, -0.017567683, 1.4726204, -0.0103044165, 0.8248565, 0.016323404, 0.6907201, -0.018648474, 0.5361265, 0.015199959, 0.4268482, 0.67974854, 0.47014588, 1.0, -0.015966421, 0.0035816978, 0.26347196, -0.011605847, 0.017163482, 0.0015772287, -0.0032684344, 0.0053004473], "split_indices": [119, 139, 142, 138, 0, 0, 0, 138, 0, 142, 0, 142, 0, 140, 0, 140, 140, 139, 97, 0, 0, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1831.0, 229.0, 1737.0, 94.0, 98.0, 131.0, 1593.0, 144.0, 1489.0, 104.0, 1393.0, 96.0, 1296.0, 97.0, 1048.0, 248.0, 843.0, 205.0, 147.0, 101.0, 740.0, 103.0, 115.0, 90.0, 356.0, 384.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.00010009766, 0.0063419733, -0.009233682, -0.0020935258, 0.01599349, 0.00397606, -0.011109582, -0.006047573, 0.01471206, 0.010752397, -0.04256213, 0.000702846, 0.008432408, -0.13336791, 0.024008315, 0.049468134, -0.023124801, -0.0028556613, -0.020764362, -0.0055670845, 0.010925947, -0.014052554, 0.017533323, -0.011900576, -0.0022117586, 0.0032615904, -0.0073629343, 0.052690335, -0.056508325, 0.0154154925, -0.00027069822, -0.010263062, 0.001352921], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, 23, -1, -1, -1, -1, 25, -1, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [1.1949248, 2.5135288, 0.0, 1.2166805, 0.0, 2.4994717, 0.0, 0.998686, 0.0, 0.82438874, 3.1010756, 1.1398848, 0.0, 1.6893299, 2.010651, 2.5744023, 1.3214017, 0.0, 0.0, 0.0, 0.0, 0.5949965, 0.0, 0.0, 1.6127185, 0.0, 0.0, 1.5120136, 0.8786397, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 21, 21, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, 24, -1, -1, -1, -1, 26, -1, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.1665344, 1.1185367, -0.009233682, 0.9502548, 0.01599349, 1.5125633, -0.011109582, 1.3483206, 0.01471206, 1.0, 1.0, 0.21849972, 0.008432408, 0.5592704, 0.68688065, 0.26347196, 1.2140391, -0.0028556613, -0.020764362, -0.0055670845, 0.010925947, 0.15892702, 0.017533323, -0.011900576, 1.0, 0.0032615904, -0.0073629343, 1.2712117, 1.0, 0.0154154925, -0.00027069822, -0.010263062, 0.001352921], "split_indices": [141, 142, 0, 142, 0, 138, 0, 138, 0, 42, 39, 143, 0, 142, 143, 141, 138, 0, 0, 0, 0, 142, 0, 0, 97, 0, 0, 138, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1940.0, 131.0, 1839.0, 101.0, 1742.0, 97.0, 1628.0, 114.0, 1115.0, 513.0, 981.0, 134.0, 217.0, 296.0, 322.0, 659.0, 90.0, 127.0, 153.0, 143.0, 214.0, 108.0, 118.0, 541.0, 120.0, 94.0, 269.0, 272.0, 95.0, 174.0, 164.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0024526825, 0.008764568, -0.007520385, -0.00047257534, 0.01689047, 0.008654169, -0.009483969, -0.0041883034, 0.012694851, 0.0032955492, -0.011231441, 0.008410201, -0.0034593635, -0.06002238, 0.011028315, 0.0013704859, -0.009752528, 0.01268988, -0.0008847722, -0.0111335125, 0.012820742, 0.0045382297, -0.0031090828], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, -1, -1], "loss_changes": [1.0107079, 2.8209062, 0.0, 1.5528601, 0.0, 2.497552, 0.0, 1.2000439, 0.0, 0.7570812, 0.0, 0.0, 1.0489177, 0.7216614, 1.4066029, 0.0, 0.0, 0.0, 1.3987321, 0.0, 1.1753194, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 18, 18, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, -1, -1], "split_conditions": [1.638525, 1.0129824, -0.007520385, 0.8762413, 0.01689047, 0.71048903, -0.009483969, 0.6601035, 0.012694851, 0.09178938, -0.011231441, 0.008410201, 0.21691436, 0.17867137, 0.2563116, 0.0013704859, -0.009752528, 0.01268988, 1.2345582, -0.0111335125, 1.0, 0.0045382297, -0.0031090828], "split_indices": [138, 139, 0, 142, 0, 142, 0, 142, 0, 139, 0, 0, 139, 140, 139, 0, 0, 0, 138, 0, 115, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1907.0, 155.0, 1803.0, 104.0, 1644.0, 159.0, 1483.0, 161.0, 1387.0, 96.0, 107.0, 1280.0, 261.0, 1019.0, 88.0, 173.0, 95.0, 924.0, 102.0, 822.0, 472.0, 350.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003758062, -0.026328912, 0.025349861, -0.062113937, 0.010166125, 0.041595764, -0.007347002, -0.0918722, 0.007259817, -0.034727957, 0.009995431, 0.015879864, 0.01603475, -0.03354077, -0.15947682, 0.036508366, -0.011993221, 0.015635775, -0.0040303827, -0.007629393, 0.0009975814, -0.027117983, -0.0059703216, -0.006379091, 0.013790987, -0.0446238, 0.10100793, -0.009300444, -0.013976894, 0.015434164, 0.004827344, 0.04223788, -0.013341318, -0.0040408825, 0.017159797], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.3653666, 1.3294835, 1.6487644, 2.060522, 2.031603, 2.6934576, 0.0, 1.6602018, 0.0, 2.0393987, 0.0, 2.0277889, 0.0, 0.4204668, 2.1732764, 1.8612014, 0.0, 0.0, 2.7075536, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5392671, 0.49781644, 2.1364524, 0.0, 0.0, 0.0, 2.5231202, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 25, 25, 26, 26, 27, 27, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.638525, 1.3076923, 0.43578738, 0.9613828, -0.007347002, 0.2287025, 0.007259817, 0.26255798, 0.009995431, 1.2622243, 0.01603475, 1.2024587, 1.3015523, 0.2369247, -0.011993221, 0.015635775, 0.77939034, -0.007629393, 0.0009975814, -0.027117983, -0.0059703216, -0.006379091, 0.013790987, 0.6600532, 0.751305, 0.5769231, -0.013976894, 0.015434164, 0.004827344, 0.58005387, -0.013341318, -0.0040408825, 0.017159797], "split_indices": [71, 115, 138, 1, 142, 140, 0, 142, 0, 142, 0, 138, 0, 138, 138, 140, 0, 0, 142, 0, 0, 0, 0, 0, 0, 141, 140, 1, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1018.0, 1027.0, 514.0, 504.0, 882.0, 145.0, 421.0, 93.0, 336.0, 168.0, 725.0, 157.0, 226.0, 195.0, 183.0, 153.0, 90.0, 635.0, 114.0, 112.0, 92.0, 103.0, 92.0, 91.0, 458.0, 177.0, 334.0, 124.0, 88.0, 89.0, 236.0, 98.0, 144.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-2.513984e-05, -0.0067938482, 0.06733961, 0.00853243, -0.062126227, 0.02176831, -0.009456874, -0.0017261939, 0.016805982, -0.13358311, 0.0033970946, 0.010752389, -0.016547581, -0.0018515673, -0.024104277, -0.0015903625, 0.014675339, 0.008394969, -0.012538581, -0.0024558315, 0.009075855, -0.020785538, 0.040781826, 0.0072635245, -0.08024439, 0.09871579, -0.0059637013, -0.0029293231, 0.008830194, 0.001581754, -0.014577503, -0.00064650096, 0.020862384], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.94386256, 1.5951624, 4.6006136, 2.4106116, 2.8016567, 0.0, 0.0, 2.828015, 0.0, 2.8934345, 0.0, 2.1587143, 0.0, 0.0, 0.0, 1.4574078, 0.0, 0.97503865, 0.0, 0.76400226, 0.0, 1.1290773, 1.6696689, 1.362751, 1.3660157, 2.1039605, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.74297655, 1.0, 0.7783057, -0.07692308, 0.02176831, -0.009456874, 0.72378874, 0.016805982, 1.0, 0.0033970946, 0.5981277, -0.016547581, -0.0018515673, -0.024104277, 0.5352736, 0.014675339, 0.52807486, -0.012538581, 1.0, 0.009075855, 0.34762505, 0.37509307, 0.25325468, 0.37428665, 0.2563116, -0.0059637013, -0.0029293231, 0.008830194, 0.001581754, -0.014577503, -0.00064650096, 0.020862384], "split_indices": [125, 140, 15, 142, 1, 0, 0, 139, 0, 39, 0, 139, 0, 0, 0, 139, 0, 142, 0, 93, 0, 141, 139, 142, 142, 139, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1881.0, 189.0, 1473.0, 408.0, 98.0, 91.0, 1384.0, 89.0, 234.0, 174.0, 1286.0, 98.0, 113.0, 121.0, 1179.0, 107.0, 1091.0, 88.0, 964.0, 127.0, 677.0, 287.0, 460.0, 217.0, 182.0, 105.0, 317.0, 143.0, 88.0, 129.0, 93.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036334477, 0.0052445955, -0.07419661, -0.0049079102, 0.019338999, -0.00068489386, -0.018363653, 0.009390523, -0.06993391, 0.028574564, -0.046685897, 0.0059712357, -0.019431634, 0.074637875, -0.016801227, -0.00036695483, -0.011352193, -0.00781025, 0.008106626, 0.1684331, 0.008678635, -0.07662807, 0.027825296, -0.0071143964, 0.0062338966, 0.035914183, 0.003434101, 0.07027862, -0.010491396, 0.00025430386, -0.018830102, -0.04453948, 0.020507839, 0.013608098, 0.0019034276, -0.012156192, 0.0049773687], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [1.2948987, 3.5070307, 1.7025905, 1.6196594, 0.0, 0.0, 0.0, 1.5362029, 2.964558, 2.2239294, 1.1268616, 1.2311362, 0.0, 3.2665572, 1.4310472, 0.95419955, 0.0, 0.0, 0.0, 5.574811, 2.1691637, 2.024652, 3.9378529, 0.0, 0.0, 0.0, 0.0, 0.6777714, 0.0, 0.0, 0.0, 1.5836027, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 27, 27, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3539915, 0.8565531, 2.0, 0.019338999, -0.00068489386, -0.018363653, 1.0, 1.0, 1.0, 0.3120459, 1.0, -0.019431634, -0.07692308, -0.07692308, 0.14816526, -0.011352193, -0.00781025, 0.008106626, 0.6458431, 0.5590813, 0.4798773, 0.49998784, -0.0071143964, 0.0062338966, 0.035914183, 0.003434101, 0.35942933, -0.010491396, 0.00025430386, -0.018830102, 0.33120996, 0.020507839, 0.013608098, 0.0019034276, -0.012156192, 0.0049773687], "split_indices": [119, 139, 140, 0, 0, 0, 0, 80, 12, 122, 140, 69, 0, 1, 1, 143, 0, 0, 0, 141, 139, 140, 141, 0, 0, 0, 0, 141, 0, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1836.0, 231.0, 1742.0, 94.0, 143.0, 88.0, 1428.0, 314.0, 1064.0, 364.0, 195.0, 119.0, 528.0, 536.0, 215.0, 149.0, 92.0, 103.0, 218.0, 310.0, 229.0, 307.0, 101.0, 114.0, 90.0, 128.0, 201.0, 109.0, 134.0, 95.0, 218.0, 89.0, 88.0, 113.0, 120.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0006375757, 0.008747116, -0.0040394347, -0.007697079, 0.0028118133, 0.013637421, -0.004551742, -0.075932, 0.0049877716, -0.0023289204, -0.013830224, -0.006279689, 0.08338308, -0.0555619, 0.047304932, -0.0015365289, 0.0183182, -0.020455845, -0.019100651, -0.023354696, 0.09567089, -0.07950689, 0.05661076, 0.005652826, -0.01706944, 0.020248808, 0.010422536, 0.0033699328, -0.017384542, -0.008341803, 0.015957315, -0.008114572, 0.008026276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, -1, 3, -1, 5, -1, 7, 9, 11, -1, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.84229636, 0.0, 0.98335236, 0.0, 1.7693063, 0.0, 1.160991, 0.65995157, 1.3285075, 0.0, 0.0, 3.4726107, 1.8625919, 3.2571235, 2.153038, 0.0, 0.0, 2.4756699, 0.0, 3.0131025, 3.4056396, 3.2893507, 3.402577, 0.0, 0.0, 0.0, 1.3301907, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 26, 26], "right_children": [2, -1, 4, -1, 6, -1, 8, 10, 12, -1, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [0.09178938, 0.008747116, 1.197011, -0.007697079, 1.218573, 0.013637421, 0.28544348, 0.2696515, 1.2692307, -0.0023289204, -0.013830224, 1.0, 0.4666329, 1.0, 0.5501886, -0.0015365289, 0.0183182, 1.0, -0.019100651, 0.500368, 0.79743034, -0.34615386, 1.0, 0.005652826, -0.01706944, 0.020248808, 1.0979586, 0.0033699328, -0.017384542, -0.008341803, 0.015957315, -0.008114572, 0.008026276], "split_indices": [139, 0, 138, 0, 138, 0, 139, 140, 1, 0, 0, 124, 139, 50, 143, 0, 0, 115, 0, 140, 140, 1, 81, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 106.0, 1968.0, 169.0, 1799.0, 94.0, 1705.0, 201.0, 1504.0, 109.0, 92.0, 1315.0, 189.0, 685.0, 630.0, 95.0, 94.0, 544.0, 141.0, 256.0, 374.0, 308.0, 236.0, 166.0, 90.0, 166.0, 208.0, 140.0, 168.0, 100.0, 136.0, 90.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0008690289, 0.0066912062, -0.010621547, 0.0004588544, 0.013983686, -0.008475979, 0.011413343, 0.0059783887, -0.09439598, -0.0019360639, 0.012926801, -0.021081088, -5.9402282e-05, 0.004726045, -0.009905484, 0.015645774, -0.006716741, -0.045282394, 0.034760498, -0.010311466, 0.001977888, 0.104524665, 0.0023531495, 0.0005431332, 0.019869845, 0.0037625965, -0.011046981], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [1.2936895, 1.6330668, 0.0, 1.9094477, 0.0, 2.1646655, 0.0, 1.455849, 2.7409058, 0.9071164, 0.0, 0.0, 0.0, 1.0299947, 0.0, 1.3265079, 0.0, 1.0234385, 1.960175, 0.0, 0.0, 2.5662978, 2.3559134, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.6902589, 1.1314709, -0.010621547, 1.0, 0.013983686, 0.74138474, 0.011413343, 0.78044474, 0.8048927, 1.0, 0.012926801, -0.021081088, -5.9402282e-05, 1.0, -0.009905484, 1.0, -0.006716741, -0.15384616, -0.03846154, -0.010311466, 0.001977888, 0.4061306, 0.47502586, 0.0005431332, 0.019869845, 0.0037625965, -0.011046981], "split_indices": [138, 139, 0, 125, 0, 141, 0, 140, 142, 84, 0, 0, 0, 40, 0, 5, 0, 1, 1, 0, 0, 140, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1968.0, 107.0, 1880.0, 88.0, 1743.0, 137.0, 1492.0, 251.0, 1402.0, 90.0, 112.0, 139.0, 1312.0, 90.0, 1139.0, 173.0, 272.0, 867.0, 144.0, 128.0, 275.0, 592.0, 134.0, 141.0, 451.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0032297599, 0.008270206, -0.0060927416, 0.00037892562, 0.016890155, 0.008671297, -0.008452883, 0.015622966, -0.011474034, 0.0043966654, 0.01671265, 0.009026887, -0.004209821, 0.010356136, -0.012922126, -0.011733113, -0.004827697, -0.04056562, 0.023740232, -0.011264245, -0.0004208274, 0.01118448, -0.0019488638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1], "loss_changes": [0.6703682, 2.4363017, 0.0, 1.2898864, 0.0, 1.4318631, 0.0, 2.687302, 0.0, 1.0871544, 0.0, 0.0, 1.2553562, 0.0, 1.0454271, 0.0, 1.1720603, 1.3364662, 2.4299257, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1], "split_conditions": [1.638525, 1.559381, -0.0060927416, 0.9504357, 0.016890155, 1.0, -0.008452883, 0.8080213, -0.011474034, -0.46153846, 0.01671265, 0.009026887, 0.1010785, 0.010356136, -0.3846154, -0.011733113, 1.0, 1.2370167, 1.2726866, -0.011264245, -0.0004208274, 0.01118448, -0.0019488638], "split_indices": [138, 138, 0, 143, 0, 117, 0, 143, 0, 1, 0, 0, 139, 0, 1, 0, 17, 138, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1922.0, 151.0, 1832.0, 90.0, 1669.0, 163.0, 1580.0, 89.0, 1471.0, 109.0, 134.0, 1337.0, 100.0, 1237.0, 89.0, 1148.0, 510.0, 638.0, 171.0, 339.0, 210.0, 428.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.00736721, 0.015469678, -0.055963606, 0.004586765, 0.017471364, -0.013659574, 0.006628508, 0.015394111, -0.01924955, 0.0078970315, 0.014160866, 0.017773682, -0.062284663, 0.003429899, 0.07347971, -0.0007628132, -0.012240681, 0.026760643, -0.10438268, -0.0014277356, 0.013943727, 0.0015993787, 0.077255495, -0.0033887231, -0.01691815, 0.02367905, -0.007145833, 0.016380195, -0.00035593665, -0.003283433, 0.0137843685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.0585994, 3.1697261, 2.3065784, 3.6464503, 0.0, 0.0, 0.0, 1.535748, 0.0, 1.0619214, 0.0, 1.0731041, 0.6210665, 2.686391, 1.5917672, 0.0, 0.0, 1.1155114, 0.86792326, 0.0, 0.0, 0.9452708, 2.0423183, 0.0, 0.0, 2.9033234, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 22, 22, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1264962, 1.0, 1.1248903, 0.017471364, -0.013659574, 0.006628508, 0.88646895, -0.01924955, 1.0, 0.014160866, 1.0, 0.281656, 0.6190295, 0.4450249, -0.0007628132, -0.012240681, 0.3898465, 1.0, -0.0014277356, 0.013943727, 0.32514927, 0.47502586, -0.0033887231, -0.01691815, 0.30265966, -0.007145833, 0.016380195, -0.00035593665, -0.003283433, 0.0137843685], "split_indices": [119, 141, 58, 140, 0, 0, 0, 140, 0, 40, 0, 105, 140, 142, 140, 0, 0, 142, 13, 0, 0, 143, 141, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1829.0, 234.0, 1712.0, 117.0, 141.0, 93.0, 1623.0, 89.0, 1532.0, 91.0, 1343.0, 189.0, 1068.0, 275.0, 99.0, 90.0, 878.0, 190.0, 118.0, 157.0, 586.0, 292.0, 91.0, 99.0, 450.0, 136.0, 141.0, 151.0, 301.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0032665276, 0.0076074325, -0.00067707174, 0.007695877, -0.06232918, -0.000152966, 0.015179713, 0.0041632927, -0.0149919335, -0.03161306, 0.034376398, -0.013383625, -0.014774709, 0.061790165, -0.006236867, -0.046607245, 0.009449765, 0.12646434, 0.003400926, -0.005345519, -0.12018725, 0.003097896, 0.023170734, -0.01712269, 0.08083771, -0.07246245, 0.010751033, 0.0040399716, -0.032588296, 0.020869259, -0.0005367446, 0.0009445236, -0.013811863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5923387, 0.0, 1.010223, 1.948762, 2.1308177, 1.7750093, 0.0, 0.0, 0.0, 1.8100839, 2.066022, 2.6487286, 0.0, 2.2921996, 0.0, 1.7153614, 0.0, 2.8941593, 4.3137145, 2.7419827, 6.7055073, 0.0, 0.0, 0.0, 2.4358072, 1.2207487, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 24, 24, 25, 25], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.09178938, 0.0076074325, 1.3461539, 1.1153846, 0.33157113, 1.0, 0.015179713, 0.0041632927, -0.0149919335, 1.0078171, 1.0, 0.7082679, -0.014774709, 0.63661104, -0.006236867, 0.47075045, 0.009449765, 0.4268482, 0.83529425, 0.15384616, -0.07692308, 0.003097896, 0.023170734, -0.01712269, 1.0, 0.281656, 0.010751033, 0.0040399716, -0.032588296, 0.020869259, -0.0005367446, 0.0009445236, -0.013811863], "split_indices": [139, 0, 1, 1, 143, 124, 0, 0, 0, 140, 80, 141, 0, 141, 0, 140, 0, 140, 142, 1, 1, 0, 0, 0, 69, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 106.0, 1957.0, 1723.0, 234.0, 1634.0, 89.0, 107.0, 127.0, 855.0, 779.0, 739.0, 116.0, 607.0, 172.0, 565.0, 174.0, 288.0, 319.0, 362.0, 203.0, 151.0, 137.0, 98.0, 221.0, 227.0, 135.0, 114.0, 89.0, 89.0, 132.0, 101.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0016036398, 0.0075267954, -0.061102375, -0.0010183499, 0.013280854, -0.016286552, 0.0039530043, 0.007506477, -0.013123706, -0.00072835875, 0.012985264, 0.0059137233, -0.010090741, -0.0019233517, 0.010727325, 0.012346514, -0.010653987, -0.0032923014, 0.07556165, 0.046534967, -0.02335453, -0.0043008714, 0.016514817, -0.0012113872, 0.015765911, -0.00026933287, -0.008980195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1], "loss_changes": [0.77031976, 2.028694, 1.8330808, 1.9693035, 0.0, 0.0, 0.0, 1.6774888, 0.0, 1.0380205, 0.0, 1.1621522, 0.0, 2.027309, 0.0, 1.1813891, 0.0, 0.95766073, 2.517487, 1.7922585, 0.9376792, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0969085, 1.5634443, 1.2393746, 1.0052764, 0.013280854, -0.016286552, 0.0039530043, 0.8273459, -0.013123706, 1.0, 0.012985264, 0.78044474, -0.010090741, 0.6017302, 0.010727325, 0.49934718, -0.010653987, 1.204987, 0.44131443, 0.18284848, 0.43833455, -0.0043008714, 0.016514817, -0.0012113872, 0.015765911, -0.00026933287, -0.008980195], "split_indices": [141, 138, 141, 143, 0, 0, 0, 141, 0, 84, 0, 140, 0, 140, 0, 139, 0, 138, 140, 139, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1895.0, 179.0, 1774.0, 121.0, 89.0, 90.0, 1665.0, 109.0, 1560.0, 105.0, 1463.0, 97.0, 1358.0, 105.0, 1195.0, 163.0, 958.0, 237.0, 275.0, 683.0, 102.0, 135.0, 180.0, 95.0, 521.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0008015455, 0.0066050696, -0.007129026, -0.004582891, 0.10573964, 0.0034150172, -0.010657224, -0.0006365807, 0.02250008, -0.0061244573, 0.011552295, 0.0027624446, -0.013761242, -0.00767713, 0.0079913465, 0.0062599764, -0.010487013, -0.0023022962, 0.010083421, -0.015668353, 0.011424969, -0.0031192242, 0.0069828853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [0.86480486, 2.1217346, 0.0, 1.4021909, 2.5937464, 1.7047049, 0.0, 0.0, 0.0, 1.7165573, 0.0, 1.1082633, 0.0, 1.6417625, 0.0, 0.85835695, 0.0, 1.5142208, 0.0, 1.157361, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.638525, 0.9492228, -0.007129026, 0.79743034, 0.9026607, 0.6968186, -0.010657224, -0.0006365807, 0.02250008, 0.76807743, 0.011552295, 0.5981277, -0.013761242, 0.5338411, 0.0079913465, 2.1923077, -0.010487013, 1.0, 0.010083421, 0.470975, 0.011424969, -0.0031192242, 0.0069828853], "split_indices": [138, 140, 0, 140, 139, 140, 0, 0, 0, 139, 0, 139, 0, 140, 0, 1, 0, 73, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1913.0, 154.0, 1719.0, 194.0, 1594.0, 125.0, 100.0, 94.0, 1469.0, 125.0, 1376.0, 93.0, 1212.0, 164.0, 1060.0, 152.0, 972.0, 88.0, 872.0, 100.0, 738.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019588305, 0.0029133386, -0.009143374, -0.0058836895, 0.045716796, -0.0001559026, -0.010054611, -0.00427341, 0.112402305, -0.01069614, 0.009183767, 0.018974008, 0.004053283, 0.0030155727, -0.013204482, -0.01151137, 0.010768655, 0.0071868827, -0.087434985, -0.013022355, 0.016518641, -0.018430736, -0.0017686853, -0.008028696, 0.0027478104, 0.0033634303, -0.007335922], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, 25, -1, -1], "loss_changes": [0.90326154, 0.7399075, 0.0, 0.88379574, 1.9759619, 1.4903275, 0.0, 0.0, 1.0616205, 2.2945163, 0.0, 0.0, 0.0, 1.8839607, 0.0, 1.5445673, 0.0, 2.787533, 1.4526829, 0.82103926, 0.0, 0.0, 0.0, 0.0, 1.4738758, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, 26, -1, -1], "split_conditions": [1.0, 1.0, -0.009143374, 1.6902589, -0.1923077, 1.0054001, -0.010054611, -0.00427341, 0.3846154, 0.8762413, 0.009183767, 0.018974008, 0.004053283, 1.4334207, -0.013204482, 0.54409826, 0.010768655, 0.48200512, 1.3698684, -1.0, 0.016518641, -0.018430736, -0.0017686853, -0.008028696, 0.96153843, 0.0033634303, -0.007335922], "split_indices": [43, 62, 0, 138, 1, 139, 0, 0, 1, 142, 0, 0, 0, 138, 0, 139, 0, 139, 138, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1965.0, 107.0, 1630.0, 335.0, 1537.0, 93.0, 144.0, 191.0, 1379.0, 158.0, 92.0, 99.0, 1239.0, 140.0, 1088.0, 151.0, 873.0, 215.0, 774.0, 99.0, 90.0, 125.0, 147.0, 627.0, 446.0, 181.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.001762411, -0.0039828042, 0.0065159053, 0.006313732, -0.07524681, -0.0048384205, 0.02065456, -0.015861174, 0.0019184221, 0.006031798, -0.0126083195, -0.0009651112, 0.009658798, 0.012507444, -0.077711776, -0.001160643, 0.016894765, -0.022331508, 0.0035678379, 0.010331819, -0.0114426585, -0.0039024232, 0.012209701, 0.0014108282, -0.009050186], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [0.7583216, 1.4007717, 0.0, 3.7246718, 1.8972092, 2.0823727, 0.0, 0.0, 0.0, 0.9187398, 0.0, 1.3917288, 0.0, 2.4482837, 3.318507, 1.3706951, 0.0, 0.0, 0.0, 1.5208938, 0.0, 1.3226403, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.1185367, 0.87951106, 0.0065159053, 0.7897939, 1.0570605, 1.486252, 0.02065456, -0.015861174, 0.0019184221, 1.423565, -0.0126083195, 0.54409826, 0.009658798, 0.49446788, 1.0, 0.58005387, 0.016894765, -0.022331508, 0.0035678379, 0.4679038, -0.0114426585, 0.37509307, 0.012209701, 0.0014108282, -0.009050186], "split_indices": [142, 143, 0, 143, 143, 138, 0, 0, 0, 138, 0, 139, 0, 139, 111, 143, 0, 0, 0, 142, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2082.0, 1909.0, 173.0, 1668.0, 241.0, 1580.0, 88.0, 128.0, 113.0, 1450.0, 130.0, 1346.0, 104.0, 1145.0, 201.0, 1053.0, 92.0, 88.0, 113.0, 956.0, 97.0, 848.0, 108.0, 702.0, 146.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011404816, -0.039854556, 0.009194408, 0.013318056, -0.015113725, -0.002454268, 0.01156342, -0.008988911, 0.014272399, 0.008140753, -0.014522483, -0.005211836, 0.09688298, 0.009825709, -0.017545884, 0.003073211, 0.01615638, -0.0058833216, -0.011401775, -0.0226116, 0.008926614, 0.05118308, -0.04729169, -0.0050925654, 0.014641914, -0.003292851, -0.13042408, -0.0096101295, 0.0039994656, -0.021203019, -0.0021310286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, -1, 11, -1, 13, 15, -1, 17, -1, -1, 19, -1, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.8222172, 2.5621445, 2.0110898, 3.913197, 0.0, 2.211505, 0.0, 0.0, 0.0, 1.6127014, 0.0, 1.5097334, 0.7616066, 0.0, 1.1892399, 0.0, 0.0, 1.5009604, 0.0, 1.4606497, 0.0, 1.9546113, 2.1982946, 0.0, 0.0, 1.5788567, 1.8521042, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 9, 9, 11, 11, 12, 12, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, -1, 12, -1, 14, 16, -1, 18, -1, -1, 20, -1, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, 0.82117075, 1.0570605, 1.0, -0.015113725, 0.87239504, 0.01156342, -0.008988911, 0.014272399, 0.7633737, -0.014522483, 1.176989, 1.0, 0.009825709, 0.6400806, 0.003073211, 0.01615638, 0.5302295, -0.011401775, 0.0, 0.008926614, 1.0, 0.3925791, -0.0050925654, 0.014641914, 0.20606698, 1.0, -0.0096101295, 0.0039994656, -0.021203019, -0.0021310286], "split_indices": [89, 143, 143, 97, 0, 143, 0, 0, 0, 139, 0, 138, 109, 0, 139, 0, 0, 139, 0, 0, 0, 13, 140, 0, 0, 139, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 433.0, 1622.0, 293.0, 140.0, 1462.0, 160.0, 163.0, 130.0, 1361.0, 101.0, 1183.0, 178.0, 126.0, 1057.0, 88.0, 90.0, 943.0, 114.0, 802.0, 141.0, 201.0, 601.0, 97.0, 104.0, 393.0, 208.0, 125.0, 268.0, 119.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [6.32723e-05, 0.0073195635, -0.047913294, -0.0029026738, 0.020211002, -0.015399222, 0.0083381105, -0.010696912, 0.014120213, 0.0023218677, -0.01230128, -0.008290022, 0.011548708, -0.08089023, 0.005132947, 0.001193249, -0.015450828, -0.0072117886, 0.008791034, 0.01402069, -0.055267975, -0.0073633925, 0.013498753, 0.0051659048, -0.09965279, 0.008115276, -0.002659792, -0.0181889, 0.0005574142], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1], "loss_changes": [0.723418, 3.5941062, 3.8022268, 1.9262656, 0.0, 0.0, 0.0, 2.3790255, 0.0, 1.7509079, 0.0, 1.2990226, 0.0, 1.4213536, 1.1495985, 0.0, 0.0, 0.9989247, 0.0, 1.7564138, 1.4237812, 0.9823806, 0.0, 0.0, 1.8345344, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 21, 21, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1], "split_conditions": [3.0, 1.178437, 1.0, 1.0, 0.020211002, -0.015399222, 0.0083381105, 0.85667837, 0.014120213, 0.78044474, -0.01230128, -0.34615386, 0.011548708, 0.45182768, 0.5981277, 0.001193249, -0.015450828, 0.41560978, 0.008791034, 0.3985726, 0.36537135, 0.1010785, 0.013498753, 0.0051659048, 0.5405575, 0.008115276, -0.002659792, -0.0181889, 0.0005574142], "split_indices": [0, 142, 122, 102, 0, 0, 0, 139, 0, 140, 0, 1, 0, 142, 139, 0, 0, 142, 0, 143, 140, 139, 0, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1805.0, 273.0, 1715.0, 90.0, 151.0, 122.0, 1627.0, 88.0, 1458.0, 169.0, 1333.0, 125.0, 208.0, 1125.0, 92.0, 116.0, 979.0, 146.0, 679.0, 300.0, 577.0, 102.0, 88.0, 212.0, 103.0, 474.0, 119.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003650707, -0.0049988464, 0.007780989, 0.0018760244, -0.009852789, 0.014091697, -0.05359574, 0.002792451, 0.012971778, -0.026058791, -0.011665532, -0.030378548, 0.04653071, 0.0019110981, -0.008131505, -0.0057607195, -0.013808153, 0.092392534, -0.049296353, -0.04644204, 0.08043533, 0.021172935, 0.008395787, -0.018278362, 0.008419089, -0.0029732387, -0.01676161, 0.022053754, -0.0027713734, -0.011667604, 0.012617178, 0.008117441, -0.069430776, 0.0016090311, -0.01549519], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.7509342, 1.2583516, 0.0, 1.2353101, 0.0, 1.9518919, 0.5712976, 1.9745957, 0.0, 0.5715642, 0.0, 2.0521936, 2.5797498, 0.0, 0.0, 2.2091382, 0.0, 3.9794898, 3.3855817, 2.2544003, 3.060689, 0.0, 3.4321969, 0.0, 0.0, 1.7615575, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2872398, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 25, 25, 32, 32], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [3.5, 1.3461539, 0.007780989, 1.0, -0.009852789, 0.84615386, 0.34762505, 1.0, 0.012971778, 1.0, -0.011665532, 1.0, 1.0, 0.0019110981, -0.008131505, 0.74382067, -0.013808153, 0.5494912, 0.7478913, 0.5665408, 0.8248565, 0.021172935, 0.8666826, -0.018278362, 0.008419089, 1.0, -0.01676161, 0.022053754, -0.0027713734, -0.011667604, 0.012617178, 0.008117441, 0.37428665, 0.0016090311, -0.01549519], "split_indices": [1, 1, 0, 80, 0, 1, 141, 124, 0, 116, 0, 50, 0, 0, 0, 143, 0, 141, 143, 143, 142, 0, 142, 0, 0, 13, 0, 0, 0, 0, 0, 0, 142, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1957.0, 116.0, 1823.0, 134.0, 1494.0, 329.0, 1361.0, 133.0, 229.0, 100.0, 774.0, 587.0, 126.0, 103.0, 630.0, 144.0, 397.0, 190.0, 428.0, 202.0, 164.0, 233.0, 95.0, 95.0, 315.0, 113.0, 88.0, 114.0, 113.0, 120.0, 139.0, 176.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-1.8301144E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}