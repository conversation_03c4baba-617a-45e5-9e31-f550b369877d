{"learner": {"attributes": {"best_iteration": "11", "best_score": "0.674107"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "62"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.0025553266, -0.116938844, 0.5356459, -0.2288695, 0.06984542, 0.42624974, 0.08044478, -0.32965603, -0.1732025, -0.011773284, 0.11628293, 0.028296446, 0.055679854, -0.261164, -0.3893757, -0.0033558325, -0.21081881, 0.027237982, 0.07611093, -0.030246636, -0.021939224, -0.035379063, -0.04275025, -0.037370086, -0.16597834, -0.013472654, 0.15266418, -0.101358935, -0.027300421, -0.0077320184, 0.0057548718, 0.03336395, -0.0006655946, -0.017390406, 0.0022263848], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, -1, -1, 19, 21, -1, 23, -1, 25, -1, -1, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [127.49355, 35.708958, 10.674347, 5.991997, 5.5748267, 4.826069, 0.0, 1.5543213, 3.6139927, 0.0, 3.2168798, 0.0, 0.0, 0.30537128, 0.27541542, 0.0, 3.9586067, 0.0, 2.79803, 0.0, 0.0, 0.0, 0.0, 0.0, 2.9392776, 0.85249335, 6.343264, 2.3765812, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 13, 13, 14, 14, 16, 16, 18, 18, 24, 24, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, -1, -1, 20, 22, -1, 24, -1, 26, -1, -1, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.87650514, 0.48451114, 1.3539915, 0.22636361, 0.4390901, 0.95503044, 0.08044478, 1.0, 1.2186605, -0.011773284, 0.54138494, 0.028296446, 0.055679854, 0.22419262, 0.15677577, -0.0033558325, 0.28457788, 0.027237982, 0.69123185, -0.030246636, -0.021939224, -0.035379063, -0.04275025, -0.037370086, 1.0, 0.6762858, 0.8192118, 0.42053598, -0.027300421, -0.0077320184, 0.0057548718, 0.03336395, -0.0006655946, -0.017390406, 0.0022263848], "split_indices": [139, 139, 139, 142, 140, 140, 0, 23, 138, 0, 140, 0, 0, 140, 142, 0, 139, 0, 142, 0, 0, 0, 0, 0, 12, 140, 142, 139, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1708.0, 363.0, 1068.0, 640.0, 258.0, 105.0, 380.0, 688.0, 127.0, 513.0, 123.0, 135.0, 177.0, 203.0, 146.0, 542.0, 105.0, 408.0, 89.0, 88.0, 105.0, 98.0, 117.0, 425.0, 188.0, 220.0, 265.0, 160.0, 99.0, 89.0, 103.0, 117.0, 167.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012425769, -0.16926116, 0.2878384, -0.22071055, 0.04662048, 0.1484695, 0.56936353, -0.28163883, -0.13703755, 0.0129308505, -0.0063887793, 0.033554964, 0.10455826, 0.034718726, 0.0698732, -0.016892781, -0.32130104, -0.22286986, -0.06989452, -0.0033887792, 0.19629285, -0.2666924, -0.3639911, -0.033069365, -0.010332606, 0.005200522, -0.028020507, 0.0069169314, 0.036700156, -0.0205389, -0.03204058, -0.04509033, -0.030489082], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, -1, -1, 21, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [99.76477, 14.427963, 29.623066, 5.3478584, 2.2844281, 4.148534, 7.185646, 2.7135086, 2.5472631, 0.0, 0.0, 0.0, 5.1944194, 0.0, 0.0, 0.0, 1.0467339, 2.5005932, 6.3579283, 0.0, 5.3384686, 0.6486826, 1.2944069, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 12, 12, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, -1, -1, 22, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.5973865, 0.5286266, 1.0076025, 0.33120996, 0.6520626, 0.6569235, 0.97360194, -0.03846154, 1.0, 0.0129308505, -0.0063887793, 0.033554964, 0.7609559, 0.034718726, 0.0698732, -0.016892781, 1.0, 0.15384616, 1.0, -0.0033887792, 0.83982414, 1.2271738, 0.15360467, -0.033069365, -0.010332606, 0.005200522, -0.028020507, 0.0069169314, 0.036700156, -0.0205389, -0.03204058, -0.04509033, -0.030489082], "split_indices": [139, 142, 139, 140, 142, 139, 141, 1, 108, 0, 0, 0, 139, 0, 0, 0, 23, 1, 80, 0, 141, 138, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1299.0, 755.0, 1049.0, 250.0, 505.0, 250.0, 607.0, 442.0, 143.0, 107.0, 96.0, 409.0, 92.0, 158.0, 158.0, 449.0, 194.0, 248.0, 163.0, 246.0, 197.0, 252.0, 102.0, 92.0, 157.0, 91.0, 141.0, 105.0, 92.0, 105.0, 102.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00053775153, -0.07520042, 0.5243453, -0.18214342, 0.08887855, 0.038723584, 0.06635641, -0.20772752, 0.0013262727, 0.026398635, 0.0521385, -0.037961483, -0.18884745, -0.019912193, 0.0996606, -0.0023093966, -0.21271248, -0.012971245, 0.15889111, -0.23922737, -0.008691624, 0.03219529, 0.08858549, -0.17686027, -0.2820839, 0.026010772, -0.0035768114, -0.006480049, -0.030886287, -0.03571057, -0.21888821, -0.012944936, -0.028732834], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [82.28019, 31.795341, 5.0011215, 5.4842224, 4.5999303, 0.0, 0.0, 3.1478844, 0.0, 0.0, 7.0567894, 0.0, 3.4572906, 0.0, 6.7521853, 0.0, 2.548298, 0.0, 4.5283413, 1.6865616, 0.0, 0.0, 5.886918, 3.80159, 1.7731514, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2426043, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 19, 19, 22, 22, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0076025, 0.49792826, 1.0, 0.5286266, 0.46736357, 0.038723584, 0.06635641, 0.09323163, 0.0013262727, 0.026398635, 0.5000637, -0.037961483, 1.1741587, -0.019912193, 0.5791361, -0.0023093966, 0.446167, -0.012971245, 0.7016775, 1.0, -0.008691624, 0.03219529, 1.0, 1.0, 0.27046308, 0.026010772, -0.0035768114, -0.006480049, -0.030886287, -0.03571057, 1.2624887, -0.012944936, -0.028732834], "split_indices": [139, 139, 113, 142, 141, 0, 0, 143, 0, 0, 140, 0, 138, 0, 143, 0, 141, 0, 143, 126, 0, 0, 69, 23, 143, 0, 0, 0, 0, 0, 138, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1812.0, 262.0, 1097.0, 715.0, 132.0, 130.0, 970.0, 127.0, 124.0, 591.0, 96.0, 874.0, 94.0, 497.0, 110.0, 764.0, 102.0, 395.0, 631.0, 133.0, 119.0, 276.0, 257.0, 374.0, 116.0, 160.0, 139.0, 118.0, 171.0, 203.0, 88.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.010734276, -0.12450174, 0.26938227, -0.18027915, 0.022989238, 0.19290093, 0.062220644, -0.25823227, -0.13510571, -0.06638904, 0.014621493, 0.078157924, 0.3139114, -0.3110513, -0.0128755495, -0.06441657, -0.18577828, -0.020179348, 0.0048597245, 0.022197954, -0.0064522224, 0.04573076, 0.022825861, -0.026268933, -0.036734764, -0.019406956, 0.005044377, -0.28195786, -0.08352417, -0.03461544, -0.022146493, 0.00044742046, -0.016968926], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [65.648384, 12.052063, 16.055752, 3.7432594, 4.427508, 6.789814, 0.0, 2.6671467, 2.4106846, 3.6277297, 0.0, 5.1506433, 2.9231796, 0.754158, 0.0, 4.1846485, 3.8552246, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.78445053, 1.4406544, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.4202282, 0.48451114, 1.3539915, 0.26228148, 1.0, 0.82843477, 0.062220644, 1.0, 0.03846154, 1.0, 0.014621493, 0.6558667, 1.0, 0.19100232, -0.0128755495, 1.0, 1.0, -0.020179348, 0.0048597245, 0.022197954, -0.0064522224, 0.04573076, 0.022825861, -0.026268933, -0.036734764, -0.019406956, 0.005044377, 1.0, 1.2548681, -0.03461544, -0.022146493, 0.00044742046, -0.016968926], "split_indices": [138, 139, 139, 141, 105, 141, 0, 83, 1, 69, 0, 141, 39, 141, 0, 5, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1465.0, 595.0, 1063.0, 402.0, 489.0, 106.0, 390.0, 673.0, 233.0, 169.0, 251.0, 238.0, 277.0, 113.0, 281.0, 392.0, 107.0, 126.0, 125.0, 126.0, 89.0, 149.0, 149.0, 128.0, 132.0, 149.0, 202.0, 190.0, 98.0, 104.0, 94.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056490353, -0.1067058, 0.25413653, -0.15548898, 0.017691327, 0.1798961, 0.064793356, -0.22893816, -0.11493226, 0.02107256, -0.0862502, 0.03143263, 0.3548709, -0.013664076, -0.27189195, -0.02561714, -0.18404241, -0.018272748, 0.001237101, -0.006152785, 0.019955268, 0.049506504, 0.022199117, -0.031268932, -0.019984549, 0.008295919, -0.014558262, -0.24989323, -0.0072004544, -0.0291663, -0.018042827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [54.370144, 9.048117, 16.95668, 3.1903572, 8.426998, 12.676954, 0.0, 1.5104809, 4.259081, 0.0, 2.5975163, 4.12593, 4.17288, 0.0, 0.7642174, 3.9206493, 2.8699589, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7108736, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [0.6985237, 0.49792826, 1.354651, 0.22636361, 0.5082023, 0.90417325, 0.064793356, 1.0, 1.2624887, 0.02107256, 1.0, 1.0, 1.0, -0.013664076, 0.23830709, 0.3219062, 0.44982666, -0.018272748, 0.001237101, -0.006152785, 0.019955268, 0.049506504, 0.022199117, -0.031268932, -0.019984549, 0.008295919, -0.014558262, 0.3952318, -0.0072004544, -0.0291663, -0.018042827], "split_indices": [140, 139, 140, 142, 141, 139, 0, 53, 138, 0, 111, 97, 69, 0, 141, 143, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1491.0, 580.0, 1071.0, 420.0, 488.0, 92.0, 381.0, 690.0, 147.0, 273.0, 264.0, 224.0, 121.0, 260.0, 301.0, 389.0, 138.0, 135.0, 170.0, 94.0, 109.0, 115.0, 166.0, 94.0, 158.0, 143.0, 245.0, 144.0, 153.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0060429457, -0.087199934, 0.24763411, -0.16012637, 0.014313655, 0.18670678, 0.057731874, -0.22626223, -0.106140725, -0.06051023, 0.13848947, 0.038376566, 0.3183642, -0.27652076, -0.015596191, 0.010454033, -0.15488443, 0.014274045, -0.018147005, 0.032906864, -2.4091785e-05, 0.024599083, -0.010881263, 0.04650257, 0.017836912, -0.034768626, -0.022476397, -0.2017289, -0.0024811362, -0.00675236, 0.014444667, -0.27187908, -0.0067155086, -0.035410687, -0.019636368], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [46.675236, 11.067522, 11.590084, 3.1062336, 5.807074, 9.510511, 0.0, 1.3814754, 4.9190307, 3.5278986, 6.213195, 6.9979153, 5.297226, 0.83979034, 0.0, 0.0, 2.3702555, 2.5661235, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.6999474, 0.0, 0.0, 0.0, 1.1673775, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 27, 27, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.6985237, 0.4078803, 1.354651, 0.26228148, 0.5973865, 0.84577227, 0.057731874, 0.2019489, 0.20441829, 0.5325959, 0.6157239, 1.0, 1.0, 1.0, -0.015596191, 0.010454033, 0.4728955, 0.42307693, -0.018147005, 0.032906864, -2.4091785e-05, 0.024599083, -0.010881263, 0.04650257, 0.017836912, -0.034768626, -0.022476397, 0.34024993, -0.0024811362, -0.00675236, 0.014444667, 1.2671068, -0.0067155086, -0.035410687, -0.019636368], "split_indices": [140, 140, 140, 141, 139, 139, 0, 143, 143, 140, 141, 93, 121, 116, 0, 0, 142, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1495.0, 577.0, 870.0, 625.0, 487.0, 90.0, 391.0, 479.0, 390.0, 235.0, 229.0, 258.0, 228.0, 163.0, 90.0, 389.0, 241.0, 149.0, 99.0, 136.0, 95.0, 134.0, 126.0, 132.0, 96.0, 132.0, 286.0, 103.0, 148.0, 93.0, 188.0, 98.0, 90.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025295895, -0.076262444, 0.24090627, -0.14825733, 0.016469521, 0.0011331801, 0.3481287, -0.16486858, -0.0010772699, 0.0826433, -0.06819726, 0.046701234, 0.23904318, -0.18717402, -0.08968167, 0.025188772, -0.0053637745, 0.006294654, -0.13782516, 0.03941338, 0.008050605, -0.21839893, -0.13085248, -0.00028539132, -0.017650938, -0.011598177, 0.016620697, -0.027100647, -0.002493813, -0.17745678, -0.033089038, -0.0030302317, -0.025680473, -0.22951901, -0.0071093054, -0.015823528, -0.029786317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, 19, 21, 23, -1, 25, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [38.71008, 10.341477, 12.603155, 1.9914665, 3.7930422, 0.0, 4.526001, 1.3047638, 0.0, 5.659989, 2.711987, 0.0, 4.474947, 1.0551796, 1.3419514, 0.0, 4.7447033, 0.0, 2.9166813, 0.0, 0.0, 1.7777767, 2.7102063, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5671206, 0.0, 0.0, 0.0, 0.9256449, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 16, 16, 18, 18, 21, 21, 22, 22, 29, 29, 33, 33], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, 20, 22, 24, -1, 26, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [0.7609559, 0.41844594, 0.8001296, 0.5083688, 1.0, 0.0011331801, 1.0, 0.33120996, -0.0010772699, 0.5193469, 0.4980591, 0.046701234, -0.30769232, 1.0, 0.31215942, 0.025188772, 0.72102535, 0.006294654, 0.52396023, 0.03941338, 0.008050605, 0.31853303, 1.0, -0.00028539132, -0.017650938, -0.011598177, 0.016620697, -0.027100647, -0.002493813, 0.22636361, -0.033089038, -0.0030302317, -0.025680473, 0.16385265, -0.0071093054, -0.015823528, -0.029786317], "split_indices": [139, 140, 142, 142, 122, 0, 69, 140, 0, 141, 140, 0, 1, 109, 142, 0, 140, 0, 139, 0, 0, 141, 59, 0, 0, 0, 0, 0, 0, 142, 0, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1549.0, 512.0, 872.0, 677.0, 163.0, 349.0, 778.0, 94.0, 380.0, 297.0, 167.0, 182.0, 600.0, 178.0, 130.0, 250.0, 103.0, 194.0, 92.0, 90.0, 386.0, 214.0, 89.0, 89.0, 152.0, 98.0, 89.0, 105.0, 283.0, 103.0, 119.0, 95.0, 190.0, 93.0, 93.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038070201, -0.06826636, 0.1938852, -0.12284866, 0.011282599, -0.0014829347, 0.29160705, -0.08558473, -0.19088097, 0.038896598, -0.015562864, 0.4040276, 0.012010416, -0.03480679, -0.18268204, -0.23007149, -0.008444304, -0.05529816, 0.110533394, 0.017995756, 0.059438795, -0.0153876245, 0.035860434, -0.0101099415, -0.026834374, -0.016036615, -0.030523201, 0.01006795, -0.019492336, 0.20261073, -0.0030601523, 0.024189165, -0.008509981, 0.042175498, 0.0007815839], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, 19, -1, 21, 23, 25, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [26.327267, 6.7647824, 10.361156, 2.3424788, 2.9221606, 0.0, 6.671034, 2.943449, 1.3640337, 3.6708095, 0.0, 8.914696, 0.0, 3.2984092, 1.4326434, 1.252141, 0.0, 5.1179276, 4.0155573, 0.0, 0.0, 0.0, 6.1307116, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.9826875, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 22, 22, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, 20, -1, 22, 24, 26, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.7609559, 0.42774072, 0.8001296, 1.0, 0.8001296, -0.0014829347, 1.145815, 1.269133, 0.28457788, 0.5233413, -0.015562864, 0.98838335, 0.012010416, 0.23308125, 1.0, 0.16646369, -0.008444304, 1.3467784, 0.6198556, 0.017995756, 0.059438795, -0.0153876245, 0.29891056, -0.0101099415, -0.026834374, -0.016036615, -0.030523201, 0.01006795, -0.019492336, 1.0, -0.0030601523, 0.024189165, -0.008509981, 0.042175498, 0.0007815839], "split_indices": [139, 139, 142, 80, 142, 0, 143, 138, 139, 143, 0, 141, 0, 142, 124, 143, 0, 138, 139, 0, 0, 0, 143, 0, 0, 0, 0, 0, 0, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1558.0, 508.0, 924.0, 634.0, 162.0, 346.0, 597.0, 327.0, 544.0, 90.0, 209.0, 137.0, 392.0, 205.0, 239.0, 88.0, 235.0, 309.0, 96.0, 113.0, 146.0, 246.0, 105.0, 100.0, 124.0, 115.0, 111.0, 124.0, 187.0, 122.0, 91.0, 155.0, 88.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028818273, -0.045759168, 0.3055968, -0.12706277, 0.03315835, 0.016346429, 0.038732298, -0.14708899, -0.0031712085, 0.002192164, 0.16314633, -0.001053439, -0.16557638, -0.033520587, 0.016903345, 0.02293915, 0.0096148355, -0.19329691, -0.10625175, -0.021327695, 0.027974999, -0.16716774, -0.02817739, 0.0030896834, -0.020767257, 0.096715674, -0.015206008, -0.00882187, -0.19979128, -0.018838288, 0.039564878, -0.025830938, -0.010765639, -0.018969884, 0.009667305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, 17, 19, -1, -1, -1, 21, 23, -1, 25, 27, -1, -1, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [27.313225, 11.632714, 2.9272137, 1.7051945, 3.7032132, 0.0, 0.0, 1.8631077, 0.0, 4.427063, 0.7855768, 0.0, 1.0689316, 6.7651844, 0.0, 0.0, 0.0, 1.0241394, 2.879313, 0.0, 5.6433344, 0.88085365, 0.0, 0.0, 0.0, 11.399161, 0.0, 0.0, 1.3047562, 4.697247, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 17, 17, 18, 18, 20, 20, 21, 21, 25, 25, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, 18, 20, -1, -1, -1, 22, 24, -1, 26, 28, -1, -1, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0076025, 0.41844594, 0.97360194, 0.44982666, 1.0, 0.016346429, 0.038732298, 1.1550102, -0.0031712085, 1.0, -0.23076923, -0.001053439, 0.33236158, 1.0, 0.016903345, 0.02293915, 0.0096148355, 1.0, 0.303171, -0.021327695, 0.7860664, 0.1772107, -0.02817739, 0.0030896834, -0.020767257, 1.4115218, -0.015206008, -0.00882187, 0.27221572, 1.0, 0.039564878, -0.025830938, -0.010765639, -0.018969884, 0.009667305], "split_indices": [139, 140, 141, 142, 42, 0, 0, 138, 0, 116, 1, 0, 141, 39, 0, 0, 0, 74, 142, 0, 141, 141, 0, 0, 0, 138, 0, 0, 139, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1813.0, 252.0, 893.0, 920.0, 92.0, 160.0, 738.0, 155.0, 743.0, 177.0, 88.0, 650.0, 612.0, 131.0, 89.0, 88.0, 443.0, 207.0, 156.0, 456.0, 342.0, 101.0, 88.0, 119.0, 330.0, 126.0, 100.0, 242.0, 238.0, 92.0, 148.0, 94.0, 96.0, 142.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0032782978, -0.064688094, 0.1695694, -0.08259651, 0.015424636, -0.008086018, 0.212612, -0.107123636, -0.009518636, 0.32059896, 0.106299244, 0.00013840324, -0.12665753, 0.014930422, -0.11233152, 0.011809933, 0.04679966, 0.02591391, -0.0069466564, -0.17353483, -0.073382586, -0.021475116, -0.0028778631, -0.22887306, -0.0073382813, 0.013323781, -0.16679308, -0.030459693, -0.19345988, 0.015435087, -0.009543437, -0.009628046, -0.024180645, -0.012434701, -0.025063505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, -1, 23, 25, -1, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [23.372963, 5.7556906, 6.4674835, 2.432273, 0.0, 0.0, 5.87796, 2.153492, 5.568201, 7.5813847, 6.9309177, 0.0, 2.1502466, 0.0, 1.7713923, 0.0, 0.0, 0.0, 0.0, 2.5383453, 3.264012, 0.0, 0.0, 0.79107666, 0.0, 3.20561, 1.0261402, 0.0, 0.7942586, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 19, 19, 20, 20, 23, 23, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, -1, 24, 26, -1, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4202282, 0.808956, 0.56020635, 0.47112516, 0.015424636, -0.008086018, 1.0, 1.176989, 0.5427232, 1.0, 1.0, 0.00013840324, 1.0, 0.014930422, 0.5639369, 0.011809933, 0.04679966, 0.02591391, -0.0069466564, 0.32566002, 1.2634009, -0.021475116, -0.0028778631, 1.0, -0.0073382813, 1.0, 0.3924582, -0.030459693, 1.0, 0.015435087, -0.009543437, -0.009628046, -0.024180645, -0.012434701, -0.025063505], "split_indices": [138, 143, 143, 139, 0, 0, 121, 138, 139, 109, 69, 0, 17, 0, 140, 0, 0, 0, 0, 139, 138, 0, 0, 53, 0, 69, 140, 0, 106, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1468.0, 600.0, 1357.0, 111.0, 88.0, 512.0, 1016.0, 341.0, 254.0, 258.0, 155.0, 861.0, 134.0, 207.0, 107.0, 147.0, 138.0, 120.0, 458.0, 403.0, 93.0, 114.0, 295.0, 163.0, 209.0, 194.0, 94.0, 201.0, 91.0, 118.0, 100.0, 94.0, 91.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004219732, -0.02404762, 0.04252392, -0.0898343, 0.052187108, -0.04935702, -0.1428613, 0.029529005, 0.025685696, -0.0005281834, -0.15232766, -0.108778566, -0.024451671, 0.06222806, -0.020927316, 0.016416874, -0.05488894, -0.008321355, -0.022664389, -0.16908947, -0.00022617173, -0.010615982, 0.109699115, -0.14696892, 0.008210807, -0.02085132, -0.01176311, -0.003240398, 0.14769973, -0.0077951434, -0.021828694, 0.062795706, 0.030344723, 0.023294874, -0.045483474, -0.00631557, -0.0027811213], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, 19, -1, 21, -1, -1, 23, -1, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1, 35, -1, -1], "loss_changes": [17.558475, 9.885017, 0.0, 2.2708788, 4.2339725, 3.0167613, 1.5868292, 6.4186726, 0.0, 3.6438909, 0.9913049, 2.2034755, 0.0, 5.779337, 0.0, 0.0, 3.8600926, 0.0, 0.0, 0.4442792, 0.0, 0.0, 3.0456033, 0.90075994, 0.0, 0.0, 0.0, 0.0, 5.8844967, 0.0, 0.0, 5.306122, 0.0, 0.0, 0.054965973, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 16, 16, 19, 19, 22, 22, 23, 23, 28, 28, 31, 31, 34, 34], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, 20, -1, 22, -1, -1, 24, -1, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1, 36, -1, -1], "split_conditions": [1.354651, 0.48451114, 0.04252392, 1.0, 1.0769231, 1.0, 0.35341257, 0.46153846, 0.025685696, 1.0, 0.29488516, 0.25490266, -0.024451671, 0.53773886, -0.020927316, 0.016416874, 1.0, -0.008321355, -0.022664389, 0.14816526, -0.00022617173, -0.010615982, -0.5, 0.35062885, 0.008210807, -0.02085132, -0.01176311, -0.003240398, 0.89932793, -0.0077951434, -0.021828694, 0.6139384, 0.030344723, 0.023294874, 0.77644604, -0.00631557, -0.0027811213], "split_indices": [140, 139, 0, 23, 1, 80, 143, 1, 0, 59, 140, 142, 0, 143, 0, 0, 62, 0, 0, 143, 0, 0, 1, 141, 0, 0, 0, 0, 139, 0, 0, 140, 0, 0, 142, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1971.0, 91.0, 1058.0, 913.0, 600.0, 458.0, 822.0, 91.0, 407.0, 193.0, 343.0, 115.0, 723.0, 99.0, 101.0, 306.0, 100.0, 93.0, 219.0, 124.0, 159.0, 564.0, 183.0, 123.0, 124.0, 95.0, 119.0, 445.0, 93.0, 90.0, 288.0, 157.0, 112.0, 176.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.005019043, -0.044051614, 0.13294712, -0.0848696, 0.017244074, 0.1851147, -0.0014452543, -0.1312241, -0.049346447, 0.09565408, -0.0541065, 0.074015185, 0.04105108, -0.019213784, -0.098219015, 0.013789713, -0.0899535, -0.0035575365, 0.031549804, -0.12009506, 0.0050925273, -0.007349742, 0.019146156, -0.0007228449, -0.01615435, -0.123728365, 0.0022277539, -0.015100323, 0.012545742, 0.0008225585, -0.025817929, -0.000876278, -0.19031732, -0.024160616, -0.013254985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [12.90657, 3.7179222, 4.383006, 1.4688187, 3.323192, 10.542426, 0.0, 0.77804804, 3.839722, 6.172535, 2.155509, 4.885599, 0.0, 0.0, 1.4462445, 0.0, 1.5730946, 3.7094266, 0.0, 3.3843396, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4420843, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.59848976, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 16, 16, 17, 17, 19, 19, 25, 25, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [0.6985237, 0.41844594, 1.145815, 0.26228148, 0.5127808, 0.98838335, -0.0014452543, 1.0, 0.20441829, 0.49153346, 0.69685656, -0.15384616, 0.04105108, -0.019213784, 0.17247613, 0.013789713, 0.5142005, 1.0, 0.031549804, 1.0, 0.0050925273, -0.007349742, 0.019146156, -0.0007228449, -0.01615435, 0.28194562, 0.0022277539, -0.015100323, 0.012545742, 0.0008225585, -0.025817929, -0.000876278, 0.38830712, -0.024160616, -0.013254985], "split_indices": [140, 140, 143, 141, 141, 141, 0, 69, 143, 139, 142, 1, 0, 0, 141, 0, 142, 108, 0, 124, 0, 0, 0, 0, 0, 142, 0, 0, 0, 0, 0, 0, 142, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1486.0, 570.0, 892.0, 594.0, 421.0, 149.0, 387.0, 505.0, 283.0, 311.0, 282.0, 139.0, 136.0, 251.0, 90.0, 415.0, 195.0, 88.0, 191.0, 120.0, 125.0, 157.0, 103.0, 148.0, 319.0, 96.0, 91.0, 104.0, 99.0, 92.0, 117.0, 202.0, 107.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0020865966, -0.025383731, 0.1983963, -0.07905287, 0.024505086, 0.006139495, 0.029559318, -0.047183692, -0.11745725, 0.053687572, -0.09822346, 0.0026353074, -0.08803746, -0.16897875, -0.055917658, 0.033631984, 0.018247588, -0.014596896, -0.0050477935, -0.12369047, 2.847435e-05, -0.012649623, -0.02279822, -0.012011907, 0.001120197, 0.13969526, -0.023766957, -0.0053352076, -0.020823862, 0.025966251, -0.0010851857, 0.039919764, -0.015910126, -0.016716484, 0.13357612, 0.027554113, 0.0011512736], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, 35, -1, -1], "loss_changes": [11.114338, 4.8409023, 3.3689728, 1.06603, 3.355888, 0.0, 0.0, 1.430025, 1.2523913, 1.9552736, 0.4103335, 0.0, 0.9635799, 0.5389209, 0.7756518, 3.9875884, 0.0, 0.0, 0.0, 1.2964416, 0.0, 0.0, 0.0, 0.0, 0.0, 4.153967, 3.6630747, 0.0, 0.0, 0.0, 0.0, 5.6050944, 0.0, 0.0, 3.4484167, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 25, 25, 26, 26, 31, 31, 34, 34], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, 36, -1, -1], "split_conditions": [1.0152625, 0.4078803, 1.0, 1.0, 1.0, 0.006139495, 0.029559318, 0.24084663, 1.0, 0.9206867, -0.23076923, 0.0026353074, 0.46297458, 0.24269977, 0.23689131, 1.0, 0.018247588, -0.014596896, -0.0050477935, 0.28430787, 2.847435e-05, -0.012649623, -0.02279822, -0.012011907, 0.001120197, 0.6418912, 0.1923077, -0.0053352076, -0.020823862, 0.025966251, -0.0010851857, 1.0, -0.015910126, -0.016716484, 1.0, 0.027554113, 0.0011512736], "split_indices": [139, 140, 39, 23, 113, 0, 0, 139, 111, 140, 1, 0, 142, 140, 142, 53, 0, 0, 0, 142, 0, 0, 0, 0, 0, 139, 1, 0, 0, 0, 0, 5, 0, 0, 109, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1808.0, 253.0, 871.0, 937.0, 105.0, 148.0, 476.0, 395.0, 757.0, 180.0, 170.0, 306.0, 215.0, 180.0, 655.0, 102.0, 90.0, 90.0, 218.0, 88.0, 125.0, 90.0, 92.0, 88.0, 230.0, 425.0, 119.0, 99.0, 128.0, 102.0, 289.0, 136.0, 90.0, 199.0, 92.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.002725308, -0.041216098, 0.13846551, -0.068203904, 0.021479499, 0.032471906, 0.060262892, -0.10428311, 0.016619457, 0.10548283, -0.103033036, -0.009453725, 0.018956657, -0.07857288, -0.027751511, -0.051837232, 0.021187663, -0.0073823305, 0.18796366, 0.006719244, -0.026157642, -0.10527966, 0.009618236, -0.01595408, 0.014083243, 0.037742153, 0.0032952668, -0.069688745, -0.02332439, -0.15185206, 0.022467384, -0.008893612, -0.022058373, 0.014937021, -0.006087176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, -1, 23, -1, -1, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [11.238641, 2.749527, 6.4525213, 3.4765687, 5.11468, 0.0, 6.245064, 3.5497055, 4.531298, 4.3184824, 5.316661, 0.0, 0.0, 3.2390022, 0.0, 5.208553, 0.0, 0.0, 5.8736095, 0.0, 0.0, 2.7417269, 0.0, 0.0, 0.0, 0.0, 0.0, 3.5663433, 0.0, 1.0767536, 2.347866, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 18, 18, 21, 21, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, -1, 24, -1, -1, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.808956, 0.5469401, 0.95005274, 1.0, 0.15384616, 0.032471906, 0.96667093, 0.54792327, 0.39586842, 0.55506134, 1.0, -0.009453725, 0.018956657, 0.45159537, -0.027751511, 0.3283406, 0.021187663, -0.0073823305, 0.75114214, 0.006719244, -0.026157642, 0.35341257, 0.009618236, -0.01595408, 0.014083243, 0.037742153, 0.0032952668, 0.26005286, -0.02332439, 0.16508219, 0.22351408, -0.008893612, -0.022058373, 0.014937021, -0.006087176], "split_indices": [143, 143, 143, 83, 1, 0, 139, 141, 142, 140, 115, 0, 0, 141, 0, 140, 0, 0, 140, 0, 0, 143, 0, 0, 0, 0, 0, 141, 0, 142, 143, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1625.0, 443.0, 1136.0, 489.0, 131.0, 312.0, 797.0, 339.0, 292.0, 197.0, 142.0, 170.0, 694.0, 103.0, 251.0, 88.0, 92.0, 200.0, 95.0, 102.0, 602.0, 92.0, 161.0, 90.0, 90.0, 110.0, 471.0, 131.0, 249.0, 222.0, 130.0, 119.0, 88.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015097451, -0.054056007, 0.086406805, -0.017142935, -0.04434032, 0.05624034, 0.030730313, 0.0092416825, -0.058609534, 0.091158934, -0.014663668, -0.019893255, -0.04558257, 0.042028543, 0.027222818, 0.005102757, -0.08032099, -0.09471424, 0.13170406, 0.058351673, -0.010545986, -0.018375456, -0.050086554, 0.0037837368, -0.022580927, 0.041336484, 0.031541836, -0.005010113, 0.016212309, -0.020106684, -0.017000599, -0.011938569, 0.019359963, -0.06803414, 0.010818396, 0.0007972554, -0.018204423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, 11, 13, -1, -1, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [9.558131, 1.4767694, 5.1576715, 0.0, 2.3338945, 4.8243275, 0.0, 0.0, 1.9797046, 5.168578, 0.0, 0.0, 1.7448819, 5.603954, 0.0, 2.3725977, 1.8388257, 3.1452115, 4.5821004, 3.0611699, 0.0, 0.0, 1.6358012, 0.0, 0.0, 4.5273323, 0.0, 0.0, 0.0, 2.2381067, 0.0, 0.0, 0.0, 2.2963657, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 8, 8, 9, 9, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 22, 22, 25, 25, 29, 29, 33, 33], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, 12, 14, -1, -1, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [0.5973865, 0.09323163, 1.354651, -0.017142935, 1.1741587, 1.1919701, 0.030730313, 0.0092416825, 1.197011, 0.98838335, -0.014663668, -0.019893255, 1.0, 1.0, 0.027222818, 1.3148105, 0.27221572, 0.69032747, 0.77116394, 1.0, -0.010545986, -0.018375456, 1.0, 0.0037837368, -0.022580927, 1.0, 0.031541836, -0.005010113, 0.016212309, 0.50444955, -0.017000599, -0.011938569, 0.019359963, 0.39349177, 0.010818396, 0.0007972554, -0.018204423], "split_indices": [139, 143, 140, 0, 138, 143, 0, 0, 138, 141, 0, 0, 126, 39, 0, 138, 139, 143, 143, 17, 0, 0, 0, 0, 0, 69, 0, 0, 0, 141, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1295.0, 774.0, 99.0, 1196.0, 681.0, 93.0, 113.0, 1083.0, 581.0, 100.0, 92.0, 991.0, 457.0, 124.0, 403.0, 588.0, 181.0, 276.0, 272.0, 131.0, 133.0, 455.0, 90.0, 91.0, 185.0, 91.0, 133.0, 139.0, 364.0, 91.0, 90.0, 95.0, 265.0, 99.0, 159.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011577337, -0.0147168785, 0.02906586, -0.028453425, 0.013638511, -0.1001697, -0.005366317, -0.0015175155, -0.12251112, 0.0068131373, -0.013025567, -0.1615986, -0.002056129, -0.0126922, 0.018569138, -0.009875597, -0.01993837, 0.013220278, -0.1253071, -0.022109399, 0.13827336, -0.021005156, -0.0006857446, 0.011741549, -0.04940775, -0.0030893886, 0.034462024, -0.08791326, 0.0048336987, -0.0018077636, -0.01791501], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, 11, 13, -1, 15, -1, 17, -1, -1, -1, 19, 21, 23, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1], "loss_changes": [8.198447, 4.1097264, 0.0, 3.0051343, 0.0, 0.83931255, 2.0884488, 0.0, 1.3947358, 4.36484, 0.0, 0.6007519, 0.0, 3.2916522, 0.0, 0.0, 0.0, 4.051384, 2.1180067, 2.7232914, 7.0512414, 0.0, 0.0, 0.0, 2.250699, 0.0, 0.0, 2.733408, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 24, 24, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, 12, 14, -1, 16, -1, 18, -1, -1, -1, 20, 22, 24, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1], "split_conditions": [1.354651, 1.0, 0.02906586, 0.24562961, 0.013638511, 0.08975307, 1.0358227, -0.0015175155, 0.28457788, 0.808956, -0.013025567, 0.16832787, -0.002056129, 0.6758105, 0.018569138, -0.009875597, -0.01993837, 0.56020635, 0.7113641, 0.28788704, 1.0, -0.021005156, -0.0006857446, 0.011741549, 1.0, -0.0030893886, 0.034462024, 1.0, 0.0048336987, -0.0018077636, -0.01791501], "split_indices": [140, 125, 0, 142, 0, 139, 143, 0, 139, 143, 0, 140, 0, 143, 0, 0, 0, 143, 141, 142, 93, 0, 0, 0, 83, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1980.0, 92.0, 1815.0, 165.0, 442.0, 1373.0, 92.0, 350.0, 1251.0, 122.0, 253.0, 97.0, 1128.0, 123.0, 95.0, 158.0, 917.0, 211.0, 715.0, 202.0, 123.0, 88.0, 117.0, 598.0, 111.0, 91.0, 429.0, 169.0, 243.0, 186.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0016320366, -0.03513086, 0.099710196, -0.04535303, 0.008191295, 0.04295086, 0.030782772, -0.054809634, 0.0076436535, -0.061978694, 0.13200873, -0.036498424, -0.019009572, 0.0070126215, -0.017246647, -0.0036595948, 0.026972404, -0.061134204, 0.091716506, -0.037504088, -0.14608733, 0.016162602, 0.0024086612, -0.06750722, 0.009628765, -0.02309744, -0.007832658, -0.04469322, -0.017186014, -0.08274368, 0.005965726, 0.0012970783, -0.013139854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, -1, -1, 19, 21, 23, 25, -1, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [7.4059787, 1.7874838, 6.6150627, 1.5824575, 0.0, 4.1117134, 0.0, 3.158495, 0.0, 2.9483876, 5.526228, 3.547192, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8910198, 0.8557627, 2.9584446, 1.1791615, 0.0, 0.0, 1.4331856, 0.0, 0.0, 0.0, 1.9614685, 0.0, 1.6858251, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, -1, -1, 20, 22, 24, 26, -1, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [0.71047145, 3.1923077, 1.0, 1.0, 0.008191295, 1.0, 0.030782772, 0.66180456, 0.0076436535, -0.34615386, 1.0076025, 0.54172677, -0.019009572, 0.0070126215, -0.017246647, -0.0036595948, 0.026972404, 1.3148105, 0.5325959, 0.3872172, 0.38830712, 0.016162602, 0.0024086612, 0.36615553, 0.009628765, -0.02309744, -0.007832658, 0.30265966, -0.017186014, 0.1463435, 0.005965726, 0.0012970783, -0.013139854], "split_indices": [142, 1, 115, 119, 0, 124, 0, 139, 0, 1, 139, 142, 0, 0, 0, 0, 0, 138, 140, 141, 142, 0, 0, 143, 0, 0, 0, 140, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1494.0, 560.0, 1374.0, 120.0, 440.0, 120.0, 1275.0, 99.0, 202.0, 238.0, 1123.0, 152.0, 92.0, 110.0, 107.0, 131.0, 942.0, 181.0, 737.0, 205.0, 89.0, 92.0, 602.0, 135.0, 91.0, 114.0, 494.0, 108.0, 362.0, 132.0, 122.0, 240.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0015831179, -0.032369398, 0.09083483, -0.022609405, -0.01890838, 0.17604461, 0.006515733, -0.043578777, 0.11074267, 0.06973469, 0.03572777, 0.013950984, -0.012372675, -0.024022952, -0.016447976, -0.0066925087, 0.027419705, -0.018931625, 0.032590736, -0.0437197, 0.012736526, -0.026877223, -0.011956067, -0.050363507, 0.0347463, -0.07685308, 0.005346613, 0.014869421, -0.0061209803, 0.00369373, -0.10743836, -0.0027690828, -0.01471395], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, -1, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [6.2788243, 2.295826, 4.102527, 3.9511847, 0.0, 5.4717903, 4.971266, 2.8868325, 5.5757904, 11.878757, 0.0, 0.0, 0.0, 3.1339302, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1879351, 0.0, 1.1014013, 0.0, 1.5154719, 2.2961397, 1.5278571, 0.0, 0.0, 0.0, 0.0, 1.095459, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, -1, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [0.6985237, 0.7446223, 1.0, 0.6011969, -0.01890838, -0.07692308, 1.0, 0.5139595, 0.640117, 1.0, 0.03572777, 0.013950984, -0.012372675, 0.44571885, -0.016447976, -0.0066925087, 0.027419705, -0.018931625, 0.032590736, 1.0, 0.012736526, 1.0, -0.011956067, 0.38830712, 0.3176839, 1.1741587, 0.005346613, 0.014869421, -0.0061209803, 0.00369373, 1.0, -0.0027690828, -0.01471395], "split_indices": [140, 141, 121, 139, 0, 1, 69, 140, 143, 61, 0, 0, 0, 140, 0, 0, 0, 0, 0, 58, 0, 93, 0, 142, 142, 138, 0, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1501.0, 571.0, 1413.0, 88.0, 284.0, 287.0, 1221.0, 192.0, 179.0, 105.0, 142.0, 145.0, 1051.0, 170.0, 92.0, 100.0, 89.0, 90.0, 930.0, 121.0, 761.0, 169.0, 551.0, 210.0, 439.0, 112.0, 96.0, 114.0, 93.0, 346.0, 115.0, 231.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00059098256, -0.012270604, 0.02008202, 0.0010578916, -0.12304381, -0.10860632, 0.013814464, -0.0058557363, -0.01767825, 0.00047340454, -0.02257672, -0.013557977, 0.15412518, 0.06281459, -0.029685026, 0.00519851, 0.03250754, -0.012632454, 0.018479565, 0.01692063, -0.047637694, 0.0032461847, -0.09610333, -0.035810657, 0.016194033, -0.12966521, -0.033909556, 0.009525871, -0.08871503, -0.06007612, -0.024122864, 0.0069952602, -0.013895201, -0.026229247, 0.00043767784, -0.010722356, -0.0009040207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, 23, 25, 27, -1, 29, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.3333755, 2.8731532, 0.0, 2.4299579, 0.72427106, 2.4035099, 5.9760475, 0.0, 0.0, 0.0, 0.0, 1.6036265, 4.435058, 5.2372065, 3.838427, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4315941, 2.9812827, 1.0541067, 2.6765792, 0.0, 2.5464597, 1.9310591, 0.0, 4.443627, 0.48605442, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 11, 11, 12, 12, 13, 13, 14, 14, 20, 20, 21, 21, 22, 22, 23, 23, 25, 25, 26, 26, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, 24, 26, 28, -1, 30, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1794761, 1.0, 0.02008202, -0.5, 1.0, 1.4079863, 0.7926152, -0.0058557363, -0.01767825, 0.00047340454, -0.02257672, 1.0, 0.93678445, 0.3837031, 1.176989, 0.00519851, 0.03250754, -0.012632454, 0.018479565, 0.01692063, 1.0, 1.4448426, 1.3220984, 0.28788704, 0.016194033, 0.35297304, 0.53793645, 0.009525871, -0.115384616, 0.25373527, -0.024122864, 0.0069952602, -0.013895201, -0.026229247, 0.00043767784, -0.010722356, -0.0009040207], "split_indices": [142, 40, 0, 1, 108, 138, 143, 0, 0, 0, 0, 89, 140, 142, 138, 0, 0, 0, 0, 0, 97, 138, 138, 142, 0, 140, 142, 0, 1, 141, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1946.0, 125.0, 1737.0, 209.0, 181.0, 1556.0, 95.0, 114.0, 92.0, 89.0, 1302.0, 254.0, 227.0, 1075.0, 159.0, 95.0, 89.0, 138.0, 89.0, 986.0, 481.0, 505.0, 386.0, 95.0, 328.0, 177.0, 111.0, 275.0, 202.0, 126.0, 89.0, 88.0, 96.0, 179.0, 105.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014687567, -0.009544564, 0.017792303, 0.00058737735, -0.016916562, -0.024044428, 0.15766402, -0.012538144, -0.01903323, 0.0016184436, 0.025229603, -0.027258214, 0.015558693, -0.014302739, -0.11004853, -0.047111798, 0.029464029, -0.0032084526, -0.0197758, 0.001419, -0.1028154, 0.0009269788, 0.014007904, 0.013166237, -0.057735346, -0.16366808, 0.00031521873, 0.011080225, -0.037274677, -0.011705271, 0.0041337237, -0.007315186, -0.026164953, -0.009053589, 0.007701495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [2.9930832, 3.1973457, 0.0, 7.1926217, 0.0, 3.0747616, 3.3739014, 3.719643, 0.0, 0.0, 0.0, 1.4823163, 0.0, 1.7159562, 1.2787395, 1.8463817, 1.6161921, 0.0, 0.0, 2.8121288, 2.0505953, 1.7083491, 0.0, 0.0, 1.475058, 1.7915192, 0.0, 0.0, 1.8383355, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.354651, 1.145815, 0.017792303, 0.808956, -0.016916562, 0.89354455, 0.70514965, 0.6985237, -0.01903323, 0.0016184436, 0.025229603, 2.0, 0.015558693, 1.0, 0.36615553, 0.3342976, 1.0, -0.0032084526, -0.0197758, 1.0, 0.49877736, 0.23177461, 0.014007904, 0.013166237, 0.24562961, 0.03846154, 0.00031521873, 0.011080225, 1.0, -0.011705271, 0.0041337237, -0.007315186, -0.026164953, -0.009053589, 0.007701495], "split_indices": [140, 143, 0, 143, 0, 140, 140, 140, 0, 0, 0, 0, 0, 39, 143, 141, 121, 0, 0, 53, 143, 143, 0, 0, 142, 1, 0, 0, 74, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1977.0, 89.0, 1859.0, 118.0, 1607.0, 252.0, 1503.0, 104.0, 101.0, 151.0, 1382.0, 121.0, 1195.0, 187.0, 683.0, 512.0, 99.0, 88.0, 365.0, 318.0, 407.0, 105.0, 114.0, 251.0, 202.0, 116.0, 105.0, 302.0, 157.0, 94.0, 105.0, 97.0, 206.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00042123397, -0.03463556, 0.040181212, -0.018297367, -0.021561243, 0.08116429, -0.05672669, -0.033676155, 0.012780108, 0.04436524, 0.031675823, 0.0050658025, -0.015147793, -0.051177733, 0.0075500333, 0.12562317, -0.04349668, -0.0320197, -0.015750479, 0.02426607, -0.0007783213, -0.01655463, 0.011077399, -0.07036188, 0.047315028, -0.11715812, -0.016141225, 0.015404589, -0.0029768343, -0.0039637866, -0.020351838, -0.012363843, 0.009557152], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [2.882497, 3.2495608, 3.8484654, 2.2647913, 0.0, 5.904019, 2.9303527, 1.7426139, 0.0, 4.205153, 0.0, 0.0, 0.0, 1.6010947, 0.0, 4.777748, 5.328514, 2.0258832, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1392574, 1.7852972, 1.6134145, 2.497832, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [0.5233413, 0.59734994, 0.15384616, 3.0384614, -0.021561243, 0.0, 1.0, 0.446167, 0.012780108, 0.8020324, 0.031675823, 0.0050658025, -0.015147793, 1.3148105, 0.0075500333, 1.0, 1.1592654, 1.0, -0.015750479, 0.02426607, -0.0007783213, -0.01655463, 0.011077399, 1.0, 1.2114619, 0.22031887, 0.26005286, 0.015404589, -0.0029768343, -0.0039637866, -0.020351838, -0.012363843, 0.009557152], "split_indices": [143, 141, 1, 1, 0, 1, 59, 141, 0, 140, 0, 0, 0, 138, 0, 106, 140, 83, 0, 0, 0, 0, 0, 13, 138, 143, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1099.0, 969.0, 1008.0, 91.0, 681.0, 288.0, 912.0, 96.0, 589.0, 92.0, 135.0, 153.0, 786.0, 126.0, 306.0, 283.0, 666.0, 120.0, 163.0, 143.0, 158.0, 125.0, 449.0, 217.0, 241.0, 208.0, 91.0, 126.0, 127.0, 114.0, 106.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0021505123, -0.009407956, 0.118724175, -0.026521306, 0.075617224, 0.030965704, -0.009151648, -0.009903757, -0.01598437, -0.020136775, 0.025002632, -0.05818745, 0.031684596, 0.007768903, -0.011420008, -0.02233279, -0.027968956, -0.0054865316, 0.018811312, -0.05551586, 0.013274724, -0.033597335, 0.012268839, -0.023570795, -0.01415614, -0.10363074, 0.049029782, 0.020928226, -0.011668915, -0.19398238, 0.006735728, 0.018286198, -0.009453566, -0.004596492, 0.0076944465, -0.012575532, -0.026220938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, -1, 23, -1, 25, -1, 27, -1, 29, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.7931876, 2.7442536, 7.506527, 3.4783206, 5.277317, 0.0, 0.0, 2.8032227, 0.0, 1.8771712, 0.0, 5.130456, 4.3609686, 0.0, 0.0, 2.8611932, 0.0, 2.1834784, 0.0, 1.2589179, 0.0, 2.8759694, 0.0, 1.383988, 0.0, 4.155795, 4.380719, 0.8468452, 0.0, 0.81926537, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23, 25, 25, 26, 26, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, -1, 24, -1, 26, -1, 28, -1, 30, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1347722, 1.0, 1.145815, 0.8192118, 0.71047145, 0.030965704, -0.009151648, 1.0, -0.01598437, 0.40404245, 0.025002632, 0.54824436, 0.6720697, 0.007768903, -0.011420008, 0.42053598, -0.027968956, 1.0, 0.018811312, 1.0, 0.013274724, 1.0, 0.012268839, 0.3342976, -0.01415614, 0.53846157, 1.2820895, 0.20268412, -0.011668915, 0.37027234, 0.006735728, 0.018286198, -0.009453566, -0.004596492, 0.0076944465, -0.012575532, -0.026220938], "split_indices": [139, 42, 143, 142, 142, 0, 0, 39, 0, 142, 0, 139, 139, 0, 0, 139, 0, 105, 0, 109, 0, 12, 0, 141, 0, 1, 138, 141, 0, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1886.0, 187.0, 1570.0, 316.0, 98.0, 89.0, 1396.0, 174.0, 204.0, 112.0, 646.0, 750.0, 100.0, 104.0, 556.0, 90.0, 606.0, 144.0, 458.0, 98.0, 497.0, 109.0, 334.0, 124.0, 269.0, 228.0, 226.0, 108.0, 176.0, 93.0, 118.0, 110.0, 103.0, 123.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [5.4151806e-05, -0.014283592, 0.09980943, -0.0012517286, -0.017019715, 0.01980289, -0.003971729, -0.013692416, 0.016621072, 0.024555396, -0.03518993, -0.032368764, 0.13106814, -0.012463305, -0.12232711, 0.013192303, -0.087334044, 0.029460987, -0.0013420151, -0.10762434, 0.020014858, -0.026590968, 0.0017113615, -9.3038754e-05, -0.018171338, -0.0037023425, -0.01653887, -0.0321938, 0.07707147, 0.01935649, -0.015591452, -0.00222656, 0.019334977, -0.008356327, 0.012038784], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [2.9477775, 3.6613827, 3.5493994, 3.4646075, 0.0, 0.0, 0.0, 1.2728165, 0.0, 3.3771741, 1.9625106, 3.278015, 4.584195, 2.4292557, 4.1043587, 0.0, 2.2180839, 0.0, 0.0, 0.81564236, 1.7456058, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9516191, 3.234209, 2.2459946, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 19, 19, 20, 20, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0076025, 1.4860486, 1.145815, 0.76394844, -0.017019715, 0.01980289, -0.003971729, 1.0, 0.016621072, 0.49792826, 0.55228823, 1.2091086, 0.5761435, 0.18284848, -0.115384616, 0.013192303, 1.0, 0.029460987, -0.0013420151, 0.10245847, 1.0, -0.026590968, 0.0017113615, -9.3038754e-05, -0.018171338, -0.0037023425, -0.01653887, 0.5083503, 0.36329922, 1.0, -0.015591452, -0.00222656, 0.019334977, -0.008356327, 0.012038784], "split_indices": [139, 138, 143, 139, 0, 0, 0, 53, 0, 139, 141, 138, 140, 139, 1, 0, 122, 0, 0, 139, 124, 0, 0, 0, 0, 0, 0, 143, 140, 17, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1802.0, 259.0, 1663.0, 139.0, 152.0, 107.0, 1548.0, 115.0, 557.0, 991.0, 363.0, 194.0, 786.0, 205.0, 91.0, 272.0, 91.0, 103.0, 200.0, 586.0, 101.0, 104.0, 142.0, 130.0, 90.0, 110.0, 306.0, 280.0, 216.0, 90.0, 151.0, 129.0, 107.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.003891346, -0.006544324, 0.016643738, 0.0041572494, -0.09698822, 0.012022178, -0.014012046, -0.0030130453, -0.014886061, -0.036379095, 0.03396466, -0.013256047, -0.014857839, 0.017645894, 0.018379344, -0.06504793, 0.0064280317, -0.010629907, 0.032927427, 0.0027745315, -0.011799133, 0.14432612, 0.0045824754, 0.0022375914, 0.026114155, -0.012075484, 0.043736327, 0.024006544, 0.0068984497, -0.008029106, 0.010902322], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1], "loss_changes": [3.5146859, 1.8844862, 0.0, 1.9755713, 0.71442235, 1.7534348, 0.0, 0.0, 0.0, 1.3361102, 2.522849, 1.7147269, 0.0, 0.0, 1.8573642, 1.2576743, 0.0, 0.0, 2.8955102, 0.0, 0.0, 2.6496933, 3.5873375, 0.0, 0.0, 0.0, 4.0284166, 0.0, 4.176074, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 18, 18, 21, 21, 22, 22, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1], "split_conditions": [1.1794761, 1.0, 0.016643738, 1.0, 1.0, 0.33120996, -0.014012046, -0.0030130453, -0.014886061, 1.3148105, 0.2981182, 1.0, -0.014857839, 0.017645894, 0.4078803, 0.16508219, 0.0064280317, -0.010629907, 0.5139595, 0.0027745315, -0.011799133, 1.0, 0.5560235, 0.0022375914, 0.026114155, -0.012075484, 1.0, 0.024006544, 1.0, -0.008029106, 0.010902322], "split_indices": [142, 40, 0, 117, 108, 140, 0, 0, 0, 138, 142, 17, 0, 0, 140, 142, 0, 0, 140, 0, 0, 124, 143, 0, 0, 0, 81, 0, 53, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1947.0, 125.0, 1741.0, 206.0, 1651.0, 90.0, 90.0, 116.0, 515.0, 1136.0, 427.0, 88.0, 112.0, 1024.0, 256.0, 171.0, 107.0, 917.0, 93.0, 163.0, 186.0, 731.0, 91.0, 95.0, 174.0, 557.0, 88.0, 469.0, 253.0, 216.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [7.128922e-05, -0.008439043, 0.013933784, -0.109362245, 0.0025749756, 0.0059945537, -0.027514279, -0.01806715, 0.11910057, -0.004667539, -0.10425558, 0.0012561188, 0.030951133, -0.041711837, 0.052555952, 0.0008008724, -0.021318533, -0.025302397, -0.015811628, -0.010496183, 0.021767093, 0.012472894, -0.06717099, 0.015295342, -0.06578062, -0.008426349, 0.057090092, -0.019482613, -0.015459967, 0.0008669814, -0.017711477, -0.0034566347, 0.011924071, -0.009878877, 0.0057578995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, 23, -1, 25, 27, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.4284842, 2.146442, 0.0, 5.332909, 4.1876903, 0.0, 0.0, 1.708084, 5.3149953, 2.7133493, 2.4335563, 0.0, 0.0, 1.4841715, 5.236659, 0.0, 0.0, 1.0770687, 0.0, 3.2891834, 0.0, 1.5451659, 1.3466938, 0.0, 2.2545748, 0.0, 1.3956442, 1.2772952, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22, 24, 24, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, 24, -1, 26, 28, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1794761, -0.5, 0.013933784, 0.61207813, 0.7926152, 0.0059945537, -0.027514279, 1.0, 0.93678445, 0.44571885, 0.42958724, 0.0012561188, 0.030951133, 0.3995319, 0.65384614, 0.0008008724, -0.021318533, 1.0, -0.015811628, 0.0, 0.021767093, 0.2090219, 1.2634009, 0.015295342, -0.03846154, -0.008426349, 1.0, 1.0, -0.015459967, 0.0008669814, -0.017711477, -0.0034566347, 0.011924071, -0.009878877, 0.0057578995], "split_indices": [142, 1, 0, 141, 143, 0, 0, 64, 140, 140, 140, 0, 0, 140, 1, 0, 0, 12, 0, 0, 0, 140, 138, 0, 1, 0, 81, 111, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1931.0, 118.0, 190.0, 1741.0, 94.0, 96.0, 1479.0, 262.0, 1280.0, 199.0, 168.0, 94.0, 777.0, 503.0, 98.0, 101.0, 681.0, 96.0, 364.0, 139.0, 358.0, 323.0, 92.0, 272.0, 113.0, 245.0, 209.0, 114.0, 163.0, 109.0, 99.0, 146.0, 103.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0028568052, -0.0054127765, 0.018892242, -0.013026299, 0.01105374, -0.0016294969, -0.100119054, -0.018252019, 0.023462577, -0.020674778, 0.003975945, -0.032125626, 0.00924123, -0.013587519, -0.019420164, 0.005421148, -0.019689722, -0.023941237, 0.11287762, -0.05210613, 0.039429758, 0.020931715, 0.0028587931, -0.008451759, 0.0020558264, 0.010909335, -0.007949089], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1], "loss_changes": [3.1820009, 1.7479224, 0.0, 1.8442113, 0.0, 6.45232, 3.2067397, 2.3567057, 0.0, 0.0, 0.0, 4.0982494, 0.0, 4.2649956, 0.0, 3.499093, 0.0, 1.5545933, 1.9346676, 1.4201612, 2.2202296, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2553585, 5.0, 0.018892242, 0.95005274, 0.01105374, 0.808956, 1.0, 0.7609559, 0.023462577, -0.020674778, 0.003975945, 2.0, 0.00924123, 0.6758105, -0.019420164, 0.48451114, -0.019689722, 1.0, 0.46906352, 1.0, 1.0, 0.020931715, 0.0028587931, -0.008451759, 0.0020558264, 0.010909335, -0.007949089], "split_indices": [142, 0, 0, 143, 0, 143, 125, 139, 0, 0, 0, 0, 0, 143, 0, 139, 0, 83, 141, 71, 108, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1980.0, 88.0, 1858.0, 122.0, 1643.0, 215.0, 1535.0, 108.0, 122.0, 93.0, 1364.0, 171.0, 1224.0, 140.0, 1109.0, 115.0, 871.0, 238.0, 603.0, 268.0, 111.0, 127.0, 417.0, 186.0, 169.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0005263802, 0.0077247843, -0.012480295, 0.014619726, -0.013312898, 0.0015467387, 0.024953326, 0.016300015, -0.07391012, 0.0016222156, 0.024184885, -0.01561108, -0.020640789, 0.014087209, -0.013713281, -0.012935558, 0.008525665, 0.0018769186, 0.017215004, 0.013001814, -0.010613567, -0.0074635646, 0.098638505, 0.0017668657, -0.015704084, 0.027822694, -0.0061178706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, -1, 17, -1, -1, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [1.8602766, 1.8937975, 0.0, 5.709028, 0.0, 1.9604083, 0.0, 4.8764553, 2.2246532, 2.3920095, 0.0, 2.2946303, 0.0, 2.4491606, 0.0, 0.0, 0.0, 1.4155183, 0.0, 1.8717636, 0.0, 3.2404387, 5.9124737, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, -1, 18, -1, -1, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, 1.4291958, -0.012480295, 1.1228671, -0.013312898, 1.0, 0.024953326, 0.9238216, 0.7181928, 0.82173043, 0.024184885, 1.2889981, -0.020640789, 0.78604263, -0.013713281, -0.012935558, 0.008525665, 0.6401608, 0.017215004, 0.54172677, -0.010613567, 0.45213273, 0.6336577, 0.0017668657, -0.015704084, 0.027822694, -0.0061178706], "split_indices": [43, 143, 0, 142, 0, 64, 0, 142, 140, 141, 0, 138, 0, 140, 0, 0, 0, 140, 0, 142, 0, 142, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1950.0, 112.0, 1859.0, 91.0, 1761.0, 98.0, 1473.0, 288.0, 1383.0, 90.0, 200.0, 88.0, 1269.0, 114.0, 94.0, 106.0, 1178.0, 91.0, 1068.0, 110.0, 862.0, 206.0, 738.0, 124.0, 97.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0011457896, 0.008414634, -0.014063261, 0.001781302, 0.014382108, 0.015162332, -0.015349251, -0.0015145956, 0.028117571, 0.011102425, -0.09052082, -0.033369955, 0.06367927, -0.025200233, 0.0036663709, 4.151915e-05, -0.01685445, 0.0018633917, 0.2016939, 0.017275497, -0.039789014, -0.056411773, 0.014575824, 0.0057426966, 0.03176227, -0.06371127, 0.0068518296, -0.015624436, 0.025836641, -0.0023482686, -0.017553931, 0.009209611, -0.0040422804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, 23, -1, 25, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [2.1343, 1.7694453, 0.0, 3.9019642, 0.0, 7.6703396, 0.0, 1.8271105, 0.0, 3.33196, 4.1486664, 3.4866464, 5.571067, 0.0, 0.0, 4.2582684, 0.0, 3.781859, 3.3783865, 0.0, 1.3032503, 2.6357546, 0.0, 0.0, 0.0, 1.8534569, 0.0, 0.0, 0.7726958, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, 24, -1, 26, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.354651, -0.014063261, 1.0510639, 0.014382108, 1.0, -0.015349251, 0.7446223, 0.028117571, 1.0, 0.79505837, 0.50444955, 1.0, -0.025200233, 0.0036663709, -0.26923078, -0.01685445, 0.4593882, 1.0, 0.017275497, 1.0, 1.0, 0.014575824, 0.0057426966, 0.03176227, 1.0, 0.0068518296, -0.015624436, 1.2522725, -0.0023482686, -0.017553931, 0.009209611, -0.0040422804], "split_indices": [117, 140, 0, 141, 0, 125, 0, 141, 0, 39, 142, 141, 71, 0, 0, 1, 0, 139, 93, 0, 42, 115, 0, 0, 0, 109, 0, 0, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1970.0, 101.0, 1878.0, 92.0, 1729.0, 149.0, 1627.0, 102.0, 1425.0, 202.0, 772.0, 653.0, 89.0, 113.0, 619.0, 153.0, 451.0, 202.0, 116.0, 503.0, 321.0, 130.0, 90.0, 112.0, 412.0, 91.0, 145.0, 176.0, 303.0, 109.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003794766, -0.012004229, 0.013670224, -0.0011965233, -0.01551077, -0.011509666, 0.018336574, -0.0021809808, -0.016086096, -0.01696985, 0.017282061, 0.0099476, -0.095946655, -0.012011134, 0.018813735, -0.051191565, -0.019616744, -0.026774673, 0.010609715, 0.004440613, -0.015758257, -0.008015921, -0.01894192, -0.0024929242, 0.010216225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, -1, -1], "loss_changes": [2.381781, 3.0174558, 0.0, 3.4527977, 0.0, 2.3936052, 0.0, 4.1849184, 0.0, 3.1696486, 0.0, 4.3510575, 1.6999617, 1.7262592, 0.0, 2.6647332, 0.0, 2.6848865, 0.0, 0.0, 0.0, 1.4702846, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, -1, -1], "split_conditions": [1.2066275, 1.0782044, 0.013670224, 0.90889996, -0.01551077, 0.8020324, 0.018336574, 0.6985237, -0.016086096, 0.5127808, 0.017282061, 0.52396023, 1.4322847, 0.44446307, 0.018813735, 1.0, -0.019616744, 0.39833245, 0.010609715, 0.004440613, -0.015758257, 1.3846154, -0.01894192, -0.0024929242, 0.010216225], "split_indices": [141, 140, 0, 140, 0, 140, 0, 140, 0, 141, 0, 139, 138, 141, 0, 15, 0, 141, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1951.0, 114.0, 1814.0, 137.0, 1718.0, 96.0, 1617.0, 101.0, 1491.0, 126.0, 1112.0, 379.0, 990.0, 122.0, 262.0, 117.0, 880.0, 110.0, 138.0, 124.0, 789.0, 91.0, 684.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019313, 0.0044731395, -0.01424732, -0.007183401, 0.0189583, 0.0051957658, -0.10755176, -0.010250506, 0.022200529, -0.018003149, -0.0015744057, 0.0024230343, -0.021744156, -0.0083084, 0.016141161, -0.016359096, 0.010833516, 0.055303168, -0.04200183, 0.0013728217, 0.020050023, -0.0131163895, -0.1487335, 0.009276803, -0.0068324218, -0.0042824657, 0.0123496605, -0.023032619, -0.004488824], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8586906, 4.2615385, 0.0, 2.308522, 0.0, 5.539079, 1.3574557, 4.0543046, 0.0, 0.0, 0.0, 2.4824858, 0.0, 1.279941, 0.0, 2.3429606, 0.0, 2.6310568, 2.8949277, 1.560644, 0.0, 2.9992573, 1.694602, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4291958, 1.168409, -0.01424732, 1.4860486, 0.0189583, 0.76394844, 0.84633076, 0.8001296, 0.022200529, -0.018003149, -0.0015744057, 0.78902584, -0.021744156, 1.0, 0.016141161, 0.0, 0.010833516, 0.49877736, 1.3498533, 0.21749559, 0.020050023, 0.42774072, 1.3946381, 0.009276803, -0.0068324218, -0.0042824657, 0.0123496605, -0.023032619, -0.004488824], "split_indices": [143, 139, 0, 138, 0, 139, 143, 142, 0, 0, 0, 143, 0, 88, 0, 0, 0, 143, 138, 143, 0, 139, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1975.0, 90.0, 1858.0, 117.0, 1654.0, 204.0, 1544.0, 110.0, 114.0, 90.0, 1455.0, 89.0, 1363.0, 92.0, 1275.0, 88.0, 336.0, 939.0, 245.0, 91.0, 739.0, 200.0, 106.0, 139.0, 607.0, 132.0, 112.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0010683889, -0.030188583, 0.024871778, -0.014673938, -0.015019889, 0.03643796, -0.011392233, -0.02963227, 0.008170849, 0.0180666, 0.022474432, -0.053076413, 0.005226797, -0.013646386, 0.040553574, 0.0020902778, -0.08060356, 0.0054544825, 0.17020921, -0.13305476, -0.025654664, 0.01779235, -0.020357203, 0.025758058, 0.00837875, -0.009194045, -0.01709757, -0.00590309, 0.0010288942, -0.046825018, 0.009727751, 0.009623211, -0.09996053, -0.17267261, -0.0008484013, -0.01118328, -0.024220383], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, 17, -1, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, 33, 35, -1, -1, -1], "loss_changes": [1.533429, 1.658968, 1.8782213, 1.1375171, 0.0, 3.7361984, 0.0, 1.3114152, 0.0, 3.4193227, 0.0, 1.0813478, 0.0, 0.0, 3.909132, 0.0, 1.115386, 3.0093606, 1.3817925, 0.30869937, 0.22673634, 0.0, 1.8307579, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.6486783, 0.0, 0.0, 2.3280063, 0.8249011, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 29, 29, 32, 32, 33, 33], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, 18, -1, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, 34, 36, -1, -1, -1], "split_conditions": [0.41844594, 1.3467784, 1.4291958, 1.0, -0.015019889, 1.1228671, -0.011392233, 1.0, 0.008170849, -0.5, 0.022474432, 0.1772107, 0.005226797, -0.013646386, 0.808956, 0.0020902778, 1.0, 0.37602052, 0.9664031, 1.0, 1.2606692, 0.01779235, 1.0, 0.025758058, 0.00837875, -0.009194045, -0.01709757, -0.00590309, 0.0010288942, 0.0, 0.009727751, 0.009623211, 1.3946381, 0.1923077, -0.0008484013, -0.01118328, -0.024220383], "split_indices": [140, 138, 143, 113, 0, 142, 0, 62, 0, 1, 0, 141, 0, 0, 143, 0, 13, 141, 143, 126, 138, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 138, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 891.0, 1170.0, 789.0, 102.0, 1080.0, 90.0, 683.0, 106.0, 984.0, 96.0, 531.0, 152.0, 125.0, 859.0, 144.0, 387.0, 676.0, 183.0, 198.0, 189.0, 88.0, 588.0, 91.0, 92.0, 95.0, 103.0, 98.0, 91.0, 480.0, 108.0, 130.0, 350.0, 195.0, 155.0, 104.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0021886271, 0.01051903, -0.009496531, -0.0007712523, 0.022515327, 0.0102478815, -0.011259507, -0.0020925833, 0.013315893, -0.03590041, 0.02034557, 2.1043696e-05, -0.020583656, -0.024895405, 0.08117875, 0.0632175, -0.05151554, 0.016272334, -0.022465253, -0.0065760924, 0.02601708, -0.0032578663, 0.020800037, -0.014797893, 0.0066296286, 0.015523291, -0.03433949, 0.0060782726, -0.009950634, 0.009116253, -0.014201321, 0.009259031, -0.005283713], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [1.6704599, 4.606658, 0.0, 2.2253563, 0.0, 2.4935858, 0.0, 1.1333264, 0.0, 3.638195, 2.471433, 1.6024094, 0.0, 4.235129, 6.0159416, 3.0651917, 3.0797877, 3.003112, 0.0, 1.6087357, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4645405, 0.0, 0.0, 1.1532447, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.145815, 1.069748, -0.009496531, 0.83869666, 0.022515327, 0.71047145, -0.011259507, 1.0, 0.013315893, 2.0, 1.0, 0.35825115, -0.020583656, 0.53585416, 0.49792826, 0.2772888, 0.52396023, -0.07692308, -0.022465253, 1.0, 0.02601708, -0.0032578663, 0.020800037, -0.014797893, 0.0066296286, 0.015523291, 1.0, 0.0060782726, -0.009950634, 1.0, -0.014201321, 0.009259031, -0.005283713], "split_indices": [143, 142, 0, 142, 0, 142, 0, 69, 0, 0, 39, 141, 0, 139, 139, 141, 139, 1, 0, 80, 0, 0, 0, 0, 0, 0, 115, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1901.0, 163.0, 1806.0, 95.0, 1644.0, 162.0, 1494.0, 150.0, 596.0, 898.0, 492.0, 104.0, 515.0, 383.0, 221.0, 271.0, 427.0, 88.0, 257.0, 126.0, 133.0, 88.0, 149.0, 122.0, 114.0, 313.0, 149.0, 108.0, 223.0, 90.0, 95.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00021747987, 0.006991789, -0.014079826, 0.00039797733, 0.01416939, 0.01089194, -0.018817207, -0.00081956683, 0.016685497, 0.009819101, -0.009928542, -0.073616944, 0.007965122, -0.0033490963, -0.011498136, 0.02243891, -0.009131144, 0.059793677, -0.01957549, -0.04418439, 0.12943332, 0.024658173, -0.0148320645, 0.0059203478, -0.018733686, 0.031885024, 0.0026606969, -0.0045700395, 0.011271703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, -1, 11, 13, 15, -1, -1, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.0776439, 1.7319907, 0.0, 3.6786766, 0.0, 3.2165763, 0.0, 1.4772872, 0.0, 0.0, 1.7094285, 0.54606974, 1.6826186, 0.0, 0.0, 1.6039658, 0.0, 3.9173794, 2.7392328, 3.2116501, 6.310563, 2.2180593, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, -1, 12, 14, 16, -1, -1, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.354651, -0.014079826, 1.6286973, 0.01416939, 1.0, -0.018817207, 1.176989, 0.016685497, 0.009819101, 0.25646102, 1.0, 1.3461539, -0.0033490963, -0.011498136, 1.0, -0.009131144, 0.5233413, 1.0, 1.3413831, 0.6699145, 0.5286266, -0.0148320645, 0.0059203478, -0.018733686, 0.031885024, 0.0026606969, -0.0045700395, 0.011271703], "split_indices": [117, 140, 0, 138, 0, 125, 0, 138, 0, 0, 143, 111, 1, 0, 0, 106, 0, 143, 50, 138, 143, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1950.0, 100.0, 1859.0, 91.0, 1761.0, 98.0, 1638.0, 123.0, 138.0, 1500.0, 329.0, 1171.0, 167.0, 162.0, 1022.0, 149.0, 541.0, 481.0, 217.0, 324.0, 358.0, 123.0, 126.0, 91.0, 114.0, 210.0, 199.0, 159.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0054350304, -0.00030319873, 0.011124372, -0.009961519, 0.017326793, -0.018826587, 0.012158705, -0.0021246418, -0.100262575, -0.030845726, 0.06827953, -0.0015679365, -0.017420636, -0.004156397, -0.1571752, -0.00010813738, 0.020122899, -0.023125533, 0.012934334, -0.0060097016, -0.025866598, 0.009601731, -0.045373578, 0.009670977, -0.02168679, -0.0014858805, -0.0077546723, -0.020997053, 0.013186394, 0.0011568773, -0.012024531], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, 21, -1, 23, -1, -1, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.2628816, 3.307548, 0.0, 2.179603, 0.0, 2.3816037, 0.0, 2.9380882, 1.8638113, 3.4795408, 3.8277762, 0.0, 0.0, 2.157583, 1.7734566, 1.2096226, 0.0, 4.7401395, 0.0, 0.0, 0.0, 0.0, 0.18555167, 2.3908525, 0.0, 0.0, 0.0, 1.6483722, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 17, 17, 22, 22, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, 22, -1, 24, -1, -1, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.3539915, 1.0, 0.011124372, 1.0, 0.017326793, 0.74348426, 0.012158705, 1.0, 1.0, 1.0, 0.49792826, -0.0015679365, -0.017420636, 0.59701616, 0.29891056, 0.24401098, 0.020122899, 0.54824436, 0.012934334, -0.0060097016, -0.025866598, 0.009601731, 1.0, 1.0, -0.02168679, -0.0014858805, -0.0077546723, 1.0, 0.013186394, 0.0011568773, -0.012024531], "split_indices": [139, 102, 0, 125, 0, 140, 0, 50, 50, 58, 139, 0, 0, 140, 143, 139, 0, 139, 0, 0, 0, 0, 39, 121, 0, 0, 0, 109, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1973.0, 107.0, 1869.0, 104.0, 1751.0, 118.0, 1453.0, 298.0, 1032.0, 421.0, 139.0, 159.0, 852.0, 180.0, 278.0, 143.0, 746.0, 106.0, 92.0, 88.0, 89.0, 189.0, 638.0, 108.0, 97.0, 92.0, 510.0, 128.0, 384.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0072568594, 0.003503807, -0.104522794, 0.008865766, -0.010031783, -0.0043751574, -0.016470397, 0.016563918, -0.0070385705, 0.005254671, 0.013051431, 0.021374257, -0.017009495, 0.0067286086, 0.021300098, 0.012123912, -0.0029638135, 0.08715707, -0.022364177, -0.010451949, 0.029661808, -0.03975521, 0.012931431, -0.0023123554, -0.017606245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, 17, 19, 21, -1, -1, 23, -1, -1, -1], "loss_changes": [2.1539984, 1.0315415, 0.7497425, 1.0749781, 0.0, 0.0, 0.0, 2.0696406, 0.0, 4.1296086, 0.0, 3.755093, 0.0, 1.3795862, 0.0, 0.0, 2.0036407, 8.150201, 2.4874883, 0.0, 0.0, 1.9178941, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, 18, 20, 22, -1, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.145815, -0.010031783, -0.0043751574, -0.016470397, 0.98838335, -0.0070385705, 0.8315363, 0.013051431, 0.808956, -0.017009495, 1.176989, 0.021300098, 0.012123912, 1.0, 1.0, 1.4448426, -0.010451949, 0.029661808, 0.61696106, 0.012931431, -0.0023123554, -0.017606245], "split_indices": [40, 117, 13, 143, 0, 0, 0, 141, 0, 142, 0, 143, 0, 138, 0, 0, 89, 97, 138, 0, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1853.0, 205.0, 1762.0, 91.0, 102.0, 103.0, 1606.0, 156.0, 1461.0, 145.0, 1338.0, 123.0, 1243.0, 95.0, 97.0, 1146.0, 203.0, 943.0, 106.0, 97.0, 846.0, 97.0, 754.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029758844, 0.009065679, -0.06321882, -0.006795739, 0.10339939, 0.029381309, -0.019429367, 0.005173776, -0.015181926, -0.00453343, 0.032203794, 0.012973832, -0.005532366, -0.0056529837, 0.015232461, 0.010720871, -0.014373103, -0.02859313, 0.063383736, -0.0509249, 0.0069480524, 0.012730524, 0.024573525, -0.015946543, -0.017216342, 0.011286893, -0.04290191, 0.0074930065, -0.04158572, 0.0025967476, -0.008787786, -0.012209865, 0.0035469588], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, 25, -1, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [1.4936382, 2.5675936, 4.1631794, 2.5499804, 8.032174, 1.7086483, 0.0, 2.1619277, 0.0, 0.0, 0.0, 0.0, 0.0, 2.8577402, 0.0, 2.339538, 0.0, 1.4170325, 4.4613204, 2.234861, 0.0, 2.1058168, 0.0, 0.95297056, 0.0, 0.0, 0.7526845, 0.0, 1.97906, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, 26, -1, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 0.9206867, 1.0, 0.83869666, -0.07692308, 0.6503386, -0.019429367, 0.71985584, -0.015181926, -0.00453343, 0.032203794, 0.012973832, -0.005532366, 0.6401608, 0.015232461, 0.42307693, -0.014373103, 0.5665408, 0.44883975, 0.45213273, 0.0069480524, 1.0, 0.024573525, 0.16832787, -0.017216342, 0.011286893, 0.21850841, 0.0074930065, 0.31853303, 0.0025967476, -0.008787786, -0.012209865, 0.0035469588], "split_indices": [64, 140, 126, 142, 1, 140, 0, 142, 0, 0, 0, 0, 0, 140, 0, 1, 0, 143, 140, 142, 0, 108, 0, 140, 0, 0, 140, 0, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1716.0, 343.0, 1469.0, 247.0, 201.0, 142.0, 1357.0, 112.0, 147.0, 100.0, 92.0, 109.0, 1264.0, 93.0, 1130.0, 134.0, 647.0, 483.0, 527.0, 120.0, 378.0, 105.0, 409.0, 118.0, 135.0, 243.0, 90.0, 319.0, 96.0, 147.0, 156.0, 163.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0020589698, -0.006339146, 0.0137053495, -0.00024838382, -0.011991029, 0.006766157, -0.013098751, -0.005485795, 0.017642457, 0.0044082017, -0.009477458, -0.0036042077, 0.012735421, 0.013943616, -0.074178375, -0.006476321, 0.013556427, -0.020417497, 0.008181752, 0.0155969225, -0.045524027, -0.0031447478, 0.008149643, -0.014823533, 6.089987e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [2.342224, 1.3454238, 0.0, 1.6929208, 0.0, 3.6417897, 0.0, 1.4435134, 0.0, 1.4490731, 0.0, 1.7102624, 0.0, 2.746736, 5.576708, 0.81622845, 0.0, 0.0, 0.0, 1.8756229, 1.6205274, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.1794761, 1.0484753, 0.0137053495, 1.1362028, -0.011991029, 1.5122256, -0.013098751, 0.7446223, 0.017642457, 0.6985237, -0.009477458, 0.5139595, 0.012735421, 0.44216755, 0.6001431, 0.3219062, 0.013556427, -0.020417497, 0.008181752, 0.22636361, 0.39476404, -0.0031447478, 0.008149643, -0.014823533, 6.089987e-05], "split_indices": [142, 142, 0, 140, 0, 138, 0, 141, 0, 140, 0, 140, 0, 140, 143, 143, 0, 0, 0, 142, 143, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1945.0, 121.0, 1846.0, 99.0, 1752.0, 94.0, 1634.0, 118.0, 1471.0, 163.0, 1381.0, 90.0, 1106.0, 275.0, 947.0, 159.0, 150.0, 125.0, 605.0, 342.0, 353.0, 252.0, 106.0, 236.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0055920137, 0.012734117, -0.007660709, 0.0011943504, 0.018690633, 0.010855312, -0.017822348, -0.001930505, 0.01524126, -0.053403404, 0.013360605, -0.09549225, 0.0072390176, 0.1163108, -0.008842283, -0.0026028168, -0.015258598, 0.0022604852, 0.018678384, -0.024764078, 0.009751031, 0.0031559689, -0.014419985, 0.01892269, -0.029698655, 0.00019674776, -0.018835247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, -1, -1, -1, -1, 21, -1, 23, -1, -1, 25, -1, -1], "loss_changes": [1.2117215, 3.816812, 0.0, 3.0870938, 0.0, 3.058775, 0.0, 1.2199707, 0.0, 1.8795499, 2.7315216, 1.0549462, 0.0, 1.399993, 1.6645374, 0.0, 0.0, 0.0, 0.0, 2.851128, 0.0, 4.2365108, 0.0, 0.0, 2.9591088, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 19, 19, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, -1, -1, -1, -1, 22, -1, 24, -1, -1, 26, -1, -1], "split_conditions": [1.145815, 1.0248694, -0.007660709, 1.0594158, 0.018690633, 0.78604263, -0.017822348, 1.0, 0.01524126, 0.23076923, -0.30769232, 1.0, 0.0072390176, 0.43736437, 1.0, -0.0026028168, -0.015258598, 0.0022604852, 0.018678384, 1.0, 0.009751031, 0.07692308, -0.014419985, 0.01892269, 0.62704825, 0.00019674776, -0.018835247], "split_indices": [143, 142, 0, 140, 0, 140, 0, 5, 0, 1, 1, 106, 0, 140, 113, 0, 0, 0, 0, 15, 0, 1, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1899.0, 165.0, 1781.0, 118.0, 1690.0, 91.0, 1550.0, 140.0, 355.0, 1195.0, 266.0, 89.0, 212.0, 983.0, 120.0, 146.0, 91.0, 121.0, 855.0, 128.0, 693.0, 162.0, 104.0, 589.0, 491.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0031602795, 0.008702597, -0.010740327, 0.00045410375, 0.017573455, 0.011577324, -0.08805034, -0.0040072575, 0.13616928, -0.020482287, 0.004302082, 0.0054063206, -0.015018217, 0.028353876, -0.0011200168, -0.011415102, 0.06505598, 0.00473682, -0.014666991, 0.020746937, -0.015693868, -0.010956653, 0.015885029, -0.012198539, 0.008842841, -0.02339758, 0.009227653, -0.00031805536, -0.011581825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [1.2580335, 2.693524, 0.0, 1.8340384, 0.0, 3.2135363, 3.183547, 2.0241392, 3.9960694, 0.0, 0.0, 1.3866881, 0.0, 0.0, 0.0, 2.355026, 3.495958, 2.3290887, 0.0, 0.0, 2.1470594, 1.1224924, 0.0, 0.0, 0.0, 1.457407, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.6909655, 1.1347722, -0.010740327, 1.4860486, 0.017573455, 1.4242606, 0.9206867, 0.8001296, 1.0, -0.020482287, 0.004302082, 0.5286266, -0.015018217, 0.028353876, -0.0011200168, 0.5325959, 1.3283987, 2.3076923, -0.014666991, 0.020746937, 1.0, 0.446167, 0.015885029, -0.012198539, 0.008842841, 0.36290243, 0.009227653, -0.00031805536, -0.011581825], "split_indices": [138, 139, 0, 138, 0, 138, 140, 142, 59, 0, 0, 142, 0, 0, 0, 140, 138, 1, 0, 0, 111, 141, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1955.0, 98.0, 1863.0, 92.0, 1655.0, 208.0, 1471.0, 184.0, 110.0, 98.0, 1382.0, 89.0, 92.0, 92.0, 1078.0, 304.0, 963.0, 115.0, 110.0, 194.0, 874.0, 89.0, 96.0, 98.0, 780.0, 94.0, 640.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019377831, -0.008562376, 0.010504114, -0.11205377, 0.0028451968, 0.0006260463, -0.022795346, -0.010704933, 0.07962927, -0.02210463, 0.015741058, -0.0024919526, 0.027838692, -0.005334427, -0.024507985, -0.016376501, 0.011442186, 0.00031868825, -0.10734274, -0.015513281, 0.015608487, -0.01946831, -0.002459922, -0.044908483, 0.047519717, 0.008793817, -0.0067589628, -0.004399396, 0.011547002], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [1.4705352, 2.3068643, 0.0, 2.6602416, 1.8311645, 0.0, 0.0, 2.867033, 5.4858875, 5.2388144, 0.0, 0.0, 0.0, 1.7230321, 0.0, 1.8118078, 0.0, 2.4858148, 1.336966, 1.6953739, 0.0, 0.0, 0.0, 1.8801829, 1.8095493, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.1794761, -0.5, 0.010504114, 0.61207813, 1.0, 0.0006260463, -0.022795346, 1.0076025, 0.7066865, 0.81106013, 0.015741058, -0.0024919526, 0.027838692, 1.4224669, -0.024507985, 0.6000145, 0.011442186, 0.52538157, 1.0, 1.0, 0.015608487, -0.01946831, -0.002459922, 0.098104544, 0.15384616, 0.008793817, -0.0067589628, -0.004399396, 0.011547002], "split_indices": [142, 1, 0, 141, 42, 0, 0, 139, 142, 139, 0, 0, 0, 138, 0, 142, 0, 142, 111, 93, 0, 0, 0, 139, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1954.0, 121.0, 194.0, 1760.0, 96.0, 98.0, 1496.0, 264.0, 1401.0, 95.0, 173.0, 91.0, 1303.0, 98.0, 1193.0, 110.0, 1008.0, 185.0, 915.0, 93.0, 90.0, 95.0, 624.0, 291.0, 91.0, 533.0, 124.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00044915715, 0.007973917, -0.008695733, -0.0014412163, 0.014802404, 0.008938583, -0.018207116, 0.019220302, -0.066698216, 0.0073582795, 0.011483977, -0.0009879902, -0.013197879, 0.018390939, -0.01218511, 0.06297265, -0.005849303, -0.01024129, 0.026527435, 0.022013212, -0.013171774, 0.05162242, -0.015568686, -0.01263883, 0.100417845, 0.015619745, -0.0034803194, -0.0059184036, 0.0042040474, 0.00046441072, 0.02042567], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3608075, 2.5119154, 0.0, 3.3467011, 0.0, 1.3127176, 0.0, 1.6854805, 0.7492444, 1.884541, 0.0, 0.0, 0.0, 1.3162581, 0.0, 6.35405, 2.7670321, 2.8343081, 0.0, 1.7551051, 0.0, 1.9973898, 0.0, 1.1401865, 1.9691164, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.145815, 1.0248694, -0.008695733, 1.5460447, 0.014802404, 1.0, -0.018207116, 0.7609559, 1.0, 0.7446223, 0.011483977, -0.0009879902, -0.013197879, 1.0, -0.01218511, 0.49792826, 0.5427232, 0.393348, 0.026527435, 0.39610526, -0.013171774, 1.0, -0.015568686, 1.0, 1.0, 0.015619745, -0.0034803194, -0.0059184036, 0.0042040474, 0.00046441072, 0.02042567], "split_indices": [143, 142, 0, 138, 0, 40, 0, 139, 12, 141, 0, 0, 0, 53, 0, 139, 139, 143, 0, 143, 0, 80, 0, 12, 69, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1905.0, 164.0, 1785.0, 120.0, 1688.0, 97.0, 1486.0, 202.0, 1322.0, 164.0, 108.0, 94.0, 1218.0, 104.0, 429.0, 789.0, 315.0, 114.0, 646.0, 143.0, 221.0, 94.0, 448.0, 198.0, 100.0, 121.0, 242.0, 206.0, 103.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.005364679, 0.010102524, -0.010784443, 0.0029304745, -0.06321789, -0.008943722, 0.014897297, -0.020570694, 0.0029011136, -0.0012895021, -0.012232708, -0.07941131, 0.017769946, 0.016573748, -0.0746371, -0.028156659, 0.012926509, 0.07238988, -0.025355976, -0.017226657, 0.003067969, -0.0019856119, 0.19340764, -0.013932458, 0.016751887, 0.013078268, -0.0852471, 0.028614199, 0.010171522, -0.023107227, 0.011987946, -0.0017144344, -0.015258463, -0.06943872, 0.0033310018, -0.0020420393, -0.0120128095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [1.1895425, 0.0, 1.4116335, 2.698318, 3.8344426, 1.2488519, 0.0, 0.0, 3.999885, 1.7661854, 0.0, 7.9729733, 0.0, 2.5369449, 2.7144542, 0.0, 0.0, 4.185352, 2.9705656, 0.0, 0.0, 3.1836917, 1.5050364, 0.0, 1.8579797, 0.0, 0.8116987, 0.0, 0.0, 0.85213006, 0.0, 0.0, 0.0, 0.4447624, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 22, 22, 24, 24, 26, 26, 29, 29, 33, 33], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [-0.5769231, 0.010102524, 1.4635866, 1.4242606, 0.8001296, 1.0, 0.014897297, -0.020570694, -0.07692308, 1.0, -0.012232708, 1.0, 0.017769946, 1.0, 1.0, -0.028156659, 0.012926509, 0.44536373, -1.0, -0.017226657, 0.003067969, 0.26084006, 0.63754064, -0.013932458, 0.44982666, 0.013078268, 0.39171878, 0.028614199, 0.010171522, 1.0, 0.011987946, -0.0017144344, -0.015258463, 1.2350001, 0.0033310018, -0.0020420393, -0.0120128095], "split_indices": [1, 0, 138, 138, 142, 117, 0, 0, 1, 0, 0, 61, 0, 122, 106, 0, 0, 143, 0, 0, 0, 142, 143, 0, 142, 0, 141, 0, 0, 13, 0, 0, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 100.0, 1963.0, 1556.0, 407.0, 1439.0, 117.0, 129.0, 278.0, 1348.0, 91.0, 189.0, 89.0, 1084.0, 264.0, 96.0, 93.0, 465.0, 619.0, 137.0, 127.0, 288.0, 177.0, 167.0, 452.0, 111.0, 177.0, 88.0, 89.0, 326.0, 126.0, 88.0, 89.0, 179.0, 147.0, 91.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0020162652, 0.0085967695, -0.007322346, -0.0003802831, 0.01700042, 0.012062218, -0.011800534, 0.00020308132, 0.019498039, 0.011019951, -0.014334788, 0.0026884084, 0.013269707, 0.018516017, -0.06949607, 0.0005936405, 0.018664494, 0.00046409937, -0.015498749, 0.021680016, -0.06450287, -0.0012102729, 0.022279788, 0.0058951634, -0.01355094], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [1.0219178, 2.7501326, 0.0, 2.6314628, 0.0, 3.5272026, 0.0, 2.3710825, 0.0, 1.4395361, 0.0, 1.5183926, 0.0, 3.2844641, 1.5148025, 1.3520597, 0.0, 0.0, 0.0, 5.05497, 2.112625, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.145815, 1.1347722, -0.007322346, 1.4860486, 0.01700042, 0.76394844, -0.011800534, 0.76154757, 0.019498039, 0.72102535, -0.014334788, 0.5127808, 0.013269707, 0.52396023, 1.0, 0.3731999, 0.018664494, 0.00046409937, -0.015498749, 0.38830712, 0.3807156, -0.0012102729, 0.022279788, 0.0058951634, -0.01355094], "split_indices": [143, 139, 0, 138, 0, 139, 0, 142, 0, 140, 0, 141, 0, 139, 124, 139, 0, 0, 0, 142, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1898.0, 166.0, 1798.0, 100.0, 1626.0, 172.0, 1527.0, 99.0, 1420.0, 107.0, 1329.0, 91.0, 1090.0, 239.0, 985.0, 105.0, 128.0, 111.0, 744.0, 241.0, 637.0, 107.0, 88.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0027395505, -0.0033787426, 0.010680377, -0.012854815, 0.01075036, 0.0040344624, -0.16227418, 0.011228272, -0.011675825, -0.0057092304, -0.026631275, -0.00020320324, 0.012109747, 0.008902408, -0.011587154, -0.0033171328, 0.010787205, -0.017182639, 0.007651206, 0.0041064317, -0.017601764, -0.0013012862, 0.013686831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [1.3185964, 2.0552258, 0.0, 4.547501, 0.0, 1.4068456, 2.002563, 1.9191179, 0.0, 0.0, 0.0, 1.457672, 0.0, 1.5516137, 0.0, 1.2640479, 0.0, 3.2901511, 0.0, 1.9500538, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.3144982, 1.0, 0.010680377, 0.8784966, 0.01075036, 0.8597126, 0.8020324, 0.69123185, -0.011675825, -0.0057092304, -0.026631275, 0.6770848, 0.012109747, 0.59235704, -0.011587154, 0.47274625, 0.010787205, 0.4733392, 0.007651206, 0.44444776, -0.017601764, -0.0013012862, 0.013686831], "split_indices": [139, 125, 0, 143, 0, 142, 140, 142, 0, 0, 0, 141, 0, 139, 0, 141, 0, 140, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1956.0, 115.0, 1802.0, 154.0, 1619.0, 183.0, 1528.0, 91.0, 91.0, 92.0, 1384.0, 144.0, 1283.0, 101.0, 1142.0, 141.0, 973.0, 169.0, 858.0, 115.0, 760.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.0037556572, -0.0018099867, 0.010606991, -0.009947076, 0.013963846, 0.00029261207, -0.01624922, -0.0714804, 0.015758347, 0.002875631, -0.019248044, 0.0013102223, 0.018796435, 0.0145439925, -0.01821782, 0.028508563, -0.008125116, -0.0006347207, 0.11518565, 0.017202001, -0.013100832, 0.00060675066, 0.02086087, -0.0008670388, 0.018043814], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [1.1810286, 2.263975, 0.0, 2.9053478, 0.0, 1.9347688, 0.0, 3.7477522, 3.5678682, 0.0, 0.0, 3.212566, 0.0, 1.6507689, 0.0, 2.7205615, 0.0, 1.8743029, 2.7626143, 2.9943264, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.3539915, 1.0, 0.010606991, 1.0510639, 0.013963846, -1.0, -0.01624922, 0.428107, 0.84553057, 0.002875631, -0.019248044, 0.8185113, 0.018796435, 1.0, -0.01821782, 0.54172677, -0.008125116, 0.5421654, 1.0, 0.44444776, -0.013100832, 0.00060675066, 0.02086087, -0.0008670388, 0.018043814], "split_indices": [139, 102, 0, 141, 0, 0, 0, 142, 141, 0, 0, 139, 0, 64, 0, 142, 0, 143, 111, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1967.0, 107.0, 1860.0, 107.0, 1743.0, 117.0, 309.0, 1434.0, 169.0, 140.0, 1323.0, 111.0, 1234.0, 89.0, 1077.0, 157.0, 806.0, 271.0, 709.0, 97.0, 125.0, 146.0, 612.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044269417, -0.010367484, 0.008875331, -0.0046433224, -0.0119421575, -0.0129989665, 0.012127257, -0.001021209, -0.022912506, -0.013018434, 0.014713961, 0.0009815018, -0.0694354, -0.018405719, 0.049006425, 0.001942904, -0.01442116, -0.00014356799, -0.010430989, -0.021596422, 0.018095275, -0.022866663, 0.008544673, 0.006635733, -0.010802058, 0.007953312, -0.0046789763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [1.1452765, 1.2141526, 0.0, 1.9442958, 0.0, 4.486226, 0.0, 2.9186857, 0.0, 1.199758, 0.0, 1.1331118, 2.0067742, 1.3601449, 3.2605257, 0.0, 0.0, 1.3905864, 0.0, 1.733103, 0.0, 1.384092, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.1794761, 1.0484753, 0.008875331, 0.90361995, -0.0119421575, 0.97938603, 0.012127257, 0.7681918, -0.022912506, 1.0, 0.014713961, 1.0, 0.37159565, 0.55853724, 0.48451114, 0.001942904, -0.01442116, 1.0, -0.010430989, 0.28788704, 0.018095275, 0.15389337, 0.008544673, 0.006635733, -0.010802058, 0.007953312, -0.0046789763], "split_indices": [142, 142, 0, 142, 0, 140, 0, 140, 0, 0, 0, 50, 143, 139, 139, 0, 0, 121, 0, 142, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1945.0, 124.0, 1848.0, 97.0, 1733.0, 115.0, 1642.0, 91.0, 1519.0, 123.0, 1217.0, 302.0, 867.0, 350.0, 138.0, 164.0, 715.0, 152.0, 228.0, 122.0, 565.0, 150.0, 113.0, 115.0, 107.0, 458.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.002345654, 0.010415237, -0.06812502, -0.004158587, 0.12332559, -0.005448796, -0.023435323, 0.009233031, -0.02070064, 0.025255457, -0.003174914, 0.014678321, -0.010938654, -0.0025585934, 0.0142468335, 0.012053593, -0.011873316, -0.009280256, -0.0012168844, 0.05763893, -0.024728876, -0.0068807104, 0.12240398, 0.004904984, -0.092535175, -0.0015023065, 0.022995561, 0.013436119, -0.021332238, -0.017974181, -0.00038377914, 0.0026225187, -0.017149044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, 15, -1, -1, 17, -1, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.7358862, 2.8500683, 3.5006342, 4.1670513, 3.9679496, 3.8607266, 0.0, 2.260757, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5157934, 0.0, 0.0, 1.05991, 0.0, 1.5028255, 2.5386786, 1.5592656, 0.0, 3.0300038, 1.834149, 1.8254597, 0.0, 0.0, 0.0, 3.206371, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 13, 13, 16, 16, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, 16, -1, -1, 18, -1, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 0.96261597, 0.9334718, 0.8315363, 1.1919701, 0.0, -0.023435323, 0.81395763, -0.02070064, 0.025255457, -0.003174914, 0.014678321, -0.010938654, 0.08975307, 0.0142468335, 0.012053593, 0.18284848, -0.009280256, 1.2634009, 1.0, 1.0, -0.0068807104, 1.0, 0.36290243, 1.0, -0.0015023065, 0.022995561, 0.013436119, 0.46153846, -0.017974181, -0.00038377914, 0.0026225187, -0.017149044], "split_indices": [64, 142, 142, 142, 143, 1, 0, 140, 0, 0, 0, 0, 0, 139, 0, 0, 139, 0, 138, 53, 137, 0, 17, 141, 13, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1732.0, 336.0, 1534.0, 198.0, 244.0, 92.0, 1439.0, 95.0, 108.0, 90.0, 99.0, 145.0, 1322.0, 117.0, 93.0, 1229.0, 143.0, 1086.0, 310.0, 776.0, 105.0, 205.0, 540.0, 236.0, 90.0, 115.0, 91.0, 449.0, 119.0, 117.0, 341.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [3.2956603e-05, 0.008728356, -0.0691538, -0.00076684094, 0.019101528, 0.0044421284, -0.021029567, 0.009117307, -0.014369341, -0.0002686156, 0.016643358, 0.008748953, -0.014239336, -0.0016002536, 0.015471593, 0.018440746, -0.071710475, 0.004539734, 0.016539427, -0.01644791, -0.019340565, -0.021940162, 0.08786011, -0.010235138, 0.009522657, -0.0072559244, 0.0032120438, -0.0032828196, 0.022404969], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2447237, 3.1813035, 3.702977, 2.4680002, 0.0, 0.0, 0.0, 2.4126968, 0.0, 1.9762582, 0.0, 2.1904318, 0.0, 1.902477, 0.0, 2.1510706, 2.024281, 2.1224751, 0.0, 1.9857976, 0.0, 1.9976435, 3.8132665, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2066275, 1.4635866, 1.0594158, 0.019101528, 0.0044421284, -0.021029567, 0.8806386, -0.014369341, 0.77610457, 0.016643358, 0.68689543, -0.014239336, 0.5127808, 0.015471593, 3.5, 1.4265472, 1.0, 0.016539427, 1.3588839, -0.019340565, 1.0, 1.0, -0.010235138, 0.009522657, -0.0072559244, 0.0032120438, -0.0032828196, 0.022404969], "split_indices": [119, 141, 138, 140, 0, 0, 0, 140, 0, 140, 0, 140, 0, 141, 0, 1, 138, 71, 0, 138, 0, 115, 108, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1838.0, 231.0, 1747.0, 91.0, 128.0, 103.0, 1634.0, 113.0, 1542.0, 92.0, 1450.0, 92.0, 1354.0, 96.0, 1053.0, 301.0, 962.0, 91.0, 207.0, 94.0, 730.0, 232.0, 117.0, 90.0, 377.0, 353.0, 123.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00042228895, -0.0095412005, 0.05151864, 0.00203172, -0.10781514, 0.105124384, -0.007445482, 0.013522955, -0.018718187, -0.023307638, 0.0013361466, 0.00015956552, 0.022686647, -0.015257073, 0.15155198, 0.010712578, -0.08876862, 0.027422791, -0.0020666148, -0.01882068, 0.12110697, -0.049230836, -0.018229883, -0.041144848, 0.005761838, 0.0006984335, 0.024677007, -0.013816587, 0.0018359757, -0.017961703, -0.015578672, -0.06837569, 0.014724276, -0.005569508, -0.008091378, -0.007243318, 0.006510433], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [1.0451783, 1.9539099, 2.262222, 3.341895, 2.7473514, 2.9618933, 0.0, 5.756123, 0.0, 0.0, 0.0, 0.0, 0.0, 2.2889743, 5.2817564, 2.8886313, 1.1574657, 0.0, 0.0, 1.1928003, 2.681767, 1.322458, 0.0, 1.4378474, 0.0, 0.0, 0.0, 0.0, 0.0, 0.741524, 0.0, 0.028140962, 1.1987423, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 23, 23, 29, 29, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0796752, 1.0, 1.0782044, 1.2165418, 0.43234557, -0.007445482, 1.4333211, -0.018718187, -0.023307638, 0.0013361466, 0.00015956552, 0.022686647, 1.3482019, 1.0, 0.42053598, 0.6788181, 0.027422791, -0.0020666148, 1.0, 0.47978002, 0.5233413, -0.018229883, 0.3713269, 0.005761838, 0.0006984335, 0.024677007, -0.013816587, 0.0018359757, 0.14987536, -0.015578672, 0.1292752, 1.0, -0.005569508, -0.008091378, -0.007243318, 0.006510433], "split_indices": [62, 141, 108, 140, 140, 142, 0, 138, 0, 0, 0, 0, 0, 138, 126, 139, 140, 0, 0, 71, 141, 143, 0, 141, 0, 0, 0, 0, 0, 143, 0, 139, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1718.0, 335.0, 1537.0, 181.0, 235.0, 100.0, 1449.0, 88.0, 89.0, 92.0, 127.0, 108.0, 1199.0, 250.0, 886.0, 313.0, 146.0, 104.0, 699.0, 187.0, 220.0, 93.0, 541.0, 158.0, 98.0, 89.0, 95.0, 125.0, 450.0, 91.0, 177.0, 273.0, 88.0, 89.0, 100.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.002575777, -0.0032686377, 0.013056849, 0.005634455, -0.013474953, -0.006259368, 0.023239281, 0.0033395092, -0.013354206, -0.0080943415, 0.013426147, 0.007519714, -0.057179417, -0.0041183685, 0.009048264, -0.013451502, 0.010539308, 0.010117811, -0.011561413, -0.012369309, 0.012303887, 0.011558387, -0.002415447, 0.004769324, -0.0033862262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, 19, 21, -1, -1, -1, -1, 23, -1, -1], "loss_changes": [1.5417161, 2.307227, 0.0, 4.9787064, 0.0, 2.1429868, 0.0, 2.441513, 0.0, 1.1496258, 0.0, 1.0987726, 1.8958194, 1.5840995, 0.0, 0.0, 2.9145095, 1.1698228, 0.0, 0.0, 0.0, 0.0, 1.246425, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, 20, 22, -1, -1, -1, -1, 24, -1, -1], "split_conditions": [1.354651, 1.0796752, 0.013056849, 1.5601431, -0.013474953, 1.4860486, 0.023239281, 1.4333211, -0.013354206, 1.3498533, 0.013426147, 1.0, 1.0, 1.0, 0.009048264, -0.013451502, 1.0, 0.10245847, -0.011561413, -0.012369309, 0.012303887, 0.011558387, 1.0, 0.004769324, -0.0033862262], "split_indices": [140, 141, 0, 138, 0, 138, 0, 138, 0, 138, 0, 42, 39, 64, 0, 0, 111, 139, 0, 0, 0, 0, 126, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1971.0, 90.0, 1846.0, 125.0, 1754.0, 92.0, 1631.0, 123.0, 1500.0, 131.0, 1138.0, 362.0, 998.0, 140.0, 169.0, 193.0, 885.0, 113.0, 88.0, 105.0, 94.0, 791.0, 305.0, 486.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0023763217, -0.0021535167, 0.010470952, 0.0046586758, -0.013076774, -0.005925047, 0.017739672, 0.0037841687, -0.015916582, -0.0031869235, 0.0127853975, 0.009624318, -0.012129832, -0.0032431842, 0.015391633, -0.015545364, 0.007884326, 0.0009648825, -0.015602007, -0.0011396284, 0.012145375], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [0.9623361, 1.7417763, 0.0, 3.451664, 0.0, 2.6468813, 0.0, 1.4469815, 0.0, 2.3968358, 0.0, 2.6531932, 0.0, 1.3249129, 0.0, 2.6462898, 0.0, 1.5206603, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.354651, 1.6286973, 0.010470952, 1.5412097, -0.013076774, 1.4860486, 0.017739672, 1.0, -0.015916582, 0.74348426, 0.0127853975, 0.62681913, -0.012129832, 1.0, 0.015391633, 0.6190295, 0.007884326, 0.5286266, -0.015602007, -0.0011396284, 0.012145375], "split_indices": [140, 138, 0, 138, 0, 138, 0, 125, 0, 140, 0, 140, 0, 42, 0, 142, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1988.0, 88.0, 1888.0, 100.0, 1779.0, 109.0, 1673.0, 106.0, 1584.0, 89.0, 1429.0, 155.0, 1312.0, 117.0, 1141.0, 171.0, 1021.0, 120.0, 926.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [-0.00031881031, -0.005612846, 0.008647666, 0.0022794039, -0.012189627, -0.0059669698, 0.016523903, -0.035586216, 0.025879016, -0.079431795, 0.019577894, 0.08667021, -0.02103173, 0.0026990096, -0.13588732, -0.042468958, 0.022662556, -0.0265868, 0.023340574, -0.117415085, 0.050192725, -0.032503434, -0.028556248, 0.017696962, -0.016163256, 0.0062485905, -0.011061768, -0.018617107, -0.0047971513, 0.015725147, -0.00408436, -0.013725397, 0.006596205, -0.0061968504, 0.0083136475], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, 19, -1, 21, 23, -1, 25, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.95116127, 1.7905068, 0.0, 2.4551706, 0.0, 1.6403189, 0.0, 2.1792512, 2.3897753, 3.0160685, 5.125817, 6.0658727, 3.2470753, 0.0, 5.075471, 2.2010636, 0.0, 1.541881, 0.0, 0.95970607, 2.6509762, 2.000977, 0.0, 1.0635071, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, 20, -1, 22, 24, -1, 26, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.1923077, 1.3461539, 0.008647666, 1.1923077, -0.012189627, 1.0, 0.016523903, 1.0, 1.3190187, -0.34615386, 0.6882759, 0.25325468, 0.71047145, 0.0026990096, 1.3659331, 0.5501886, 0.022662556, 0.16646369, 0.023340574, -0.1923077, 0.8628813, 1.0, -0.028556248, 0.2659622, -0.016163256, 0.0062485905, -0.011061768, -0.018617107, -0.0047971513, 0.015725147, -0.00408436, -0.013725397, 0.006596205, -0.0061968504, 0.0083136475], "split_indices": [1, 1, 0, 1, 0, 124, 0, 115, 138, 1, 142, 142, 142, 0, 138, 143, 0, 143, 0, 1, 143, 71, 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1951.0, 119.0, 1827.0, 124.0, 1739.0, 88.0, 901.0, 838.0, 502.0, 399.0, 365.0, 473.0, 174.0, 328.0, 307.0, 92.0, 206.0, 159.0, 201.0, 272.0, 194.0, 134.0, 204.0, 103.0, 100.0, 106.0, 101.0, 100.0, 125.0, 147.0, 94.0, 100.0, 92.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.006730472, -0.012331119, 0.008613493, -0.0065325885, -0.009798906, -0.07057482, 0.006487916, -0.13516094, 0.003430859, 0.011742919, -0.0040511005, -0.0075569237, -0.02013739, -0.01376222, 0.0109096775, 0.01003983, -0.10068766, -0.033746243, 0.06304914, -0.19448832, 0.0075065107, -0.011302392, -0.0037168108, 0.015636653, 0.009724905, -0.024694365, -0.014319861, 0.036927514, -0.014597197, -0.045568217, 0.013273661, -0.0032856741, 0.015084884, 0.006201278, -0.014888862], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, 13, -1, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, 27, -1, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.0698587, 0.9635787, 0.0, 1.5151278, 0.0, 2.0796223, 1.7655104, 0.74969053, 0.0, 0.0, 1.5152346, 0.0, 0.0, 2.627635, 0.0, 2.3141062, 4.500603, 1.2998421, 2.2442107, 0.4788928, 0.0, 0.0, 2.289619, 0.0, 1.9520885, 0.0, 0.0, 2.4485743, 0.0, 2.2008321, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 22, 22, 24, 24, 27, 27, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, 14, -1, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, 28, -1, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [3.1923077, 1.3461539, 0.008613493, -1.0, -0.009798906, 1.3613431, 0.0, 0.35062885, 0.003430859, 0.011742919, 1.1794761, -0.0075569237, -0.02013739, 1.4635866, 0.0109096775, 0.44444776, 2.0, -0.1923077, -0.26923078, 0.87664425, 0.0075065107, -0.011302392, 1.2947173, 0.015636653, 0.3846154, -0.024694365, -0.014319861, 0.21236956, -0.014597197, -0.03846154, 0.013273661, -0.0032856741, 0.015084884, 0.006201278, -0.014888862], "split_indices": [1, 1, 0, 0, 0, 138, 0, 141, 0, 0, 142, 0, 0, 138, 0, 142, 0, 1, 1, 142, 0, 0, 138, 0, 1, 0, 0, 142, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1940.0, 117.0, 1817.0, 123.0, 307.0, 1510.0, 190.0, 117.0, 131.0, 1379.0, 100.0, 90.0, 1270.0, 109.0, 997.0, 273.0, 546.0, 451.0, 178.0, 95.0, 150.0, 396.0, 164.0, 287.0, 88.0, 90.0, 308.0, 88.0, 198.0, 89.0, 191.0, 117.0, 97.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00147148, 0.0057602017, -0.05857684, -0.0066862134, 0.023849482, 0.001418117, -0.016732274, 0.001006782, -0.015101733, -0.0074244644, 0.014554247, 0.008367774, -0.111807324, -0.009230819, 0.095357314, -0.023209734, 0.0022123794, -0.02042913, 0.009751941, 0.0003658109, 0.019372553, -0.029507374, 0.0072248722, -0.011378908, -0.020275777, -0.0028372898, 0.011038381], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [0.8523657, 5.306776, 1.8356156, 1.9308794, 0.0, 0.0, 0.0, 2.0119355, 0.0, 2.571565, 0.0, 2.074361, 3.3026686, 1.3472409, 2.0566251, 0.0, 0.0, 0.858179, 0.0, 0.0, 0.0, 2.9177692, 0.0, 1.7402257, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0, 1.3144982, 1.4836318, 1.0524075, 0.023849482, 0.001418117, -0.016732274, 0.9652778, -0.015101733, 0.71545964, 0.014554247, 1.0, 1.0, 1.0, 0.44571885, -0.023209734, 0.0022123794, 0.6011969, 0.009751941, 0.0003658109, 0.019372553, 0.50727594, 0.0072248722, 0.5560235, -0.020275777, -0.0028372898, 0.011038381], "split_indices": [119, 139, 138, 139, 0, 0, 0, 141, 0, 139, 0, 105, 111, 41, 140, 0, 0, 139, 0, 0, 0, 139, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1832.0, 232.0, 1739.0, 93.0, 139.0, 93.0, 1651.0, 88.0, 1560.0, 91.0, 1355.0, 205.0, 1127.0, 228.0, 108.0, 97.0, 1020.0, 107.0, 118.0, 110.0, 929.0, 91.0, 841.0, 88.0, 738.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.006180914, 0.010963108, -0.00890765, 0.0040058875, 0.012435357, 0.013477158, -0.012618648, 0.0035485933, 0.015846996, 0.013735152, -0.05713355, -0.0017370733, 0.053328186, 0.0041788556, -0.015863625, 0.01739786, -0.016622394, -0.013131233, 0.1127887, 0.0031272785, 0.07440045, 0.01780854, 0.0026411933, 0.03270726, -0.07199414, 0.0020948339, 0.012725862, 0.0006451043, 0.015951281, -0.001545389, -0.0139596565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, -1, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.943422, 1.5556759, 0.0, 2.2910752, 0.0, 2.4933355, 0.0, 1.0020084, 0.0, 0.8502784, 2.3395212, 3.141151, 4.2817397, 0.0, 0.0, 0.72723305, 0.0, 0.0, 1.6638339, 1.5887942, 0.50574267, 0.0, 0.0, 1.7079992, 0.7720958, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, -1, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.6909655, 1.1592654, -0.00890765, 0.94960475, 0.012435357, 1.5122256, -0.012618648, 2.0, 0.015846996, 0.54172677, 0.3986941, 0.48035455, -1.0, 0.0041788556, -0.015863625, 0.43249872, -0.016622394, -0.013131233, 1.3975399, 0.34807172, 0.42307693, 0.01780854, 0.0026411933, 0.31351322, 1.0, 0.0020948339, 0.012725862, 0.0006451043, 0.015951281, -0.001545389, -0.0139596565], "split_indices": [138, 140, 0, 141, 0, 138, 0, 0, 0, 142, 143, 142, 0, 0, 0, 143, 0, 0, 138, 141, 1, 0, 0, 141, 17, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1972.0, 99.0, 1858.0, 114.0, 1732.0, 126.0, 1621.0, 111.0, 1388.0, 233.0, 998.0, 390.0, 118.0, 115.0, 894.0, 104.0, 95.0, 295.0, 715.0, 179.0, 168.0, 127.0, 513.0, 202.0, 89.0, 90.0, 425.0, 88.0, 110.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00060428376, 0.007054952, -0.013100261, -0.0028779928, 0.011998014, 0.0060734753, -0.08591931, -0.004737271, 0.010978691, -0.0029286244, -0.014191602, 0.013540594, -0.06433673, -0.001302226, 0.009633437, -0.015715389, -0.009239788, -0.013220682, 0.011179352, 0.011320017, -0.013976544, 0.006378391, -0.086610705, -0.019716186, 0.014225828, -0.014054318, -0.0031492866, -0.004079131, 0.005079831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1], "loss_changes": [1.7624247, 2.2198043, 0.0, 1.3521388, 0.0, 1.8410431, 0.5613136, 1.619865, 0.0, 0.0, 0.0, 1.3984805, 1.7847651, 1.3007491, 0.0, 0.0, 3.4999607, 1.2557029, 0.0, 0.0, 0.0, 2.4430072, 0.54696536, 0.8589669, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1], "split_conditions": [1.6909655, 1.0076025, -0.013100261, 0.8597126, 0.011998014, 0.71047145, 0.8624959, 0.5325959, 0.010978691, -0.0029286244, -0.014191602, 0.49792826, 1.0, 1.0, 0.009633437, -0.015715389, 1.0, 0.37499917, 0.011179352, 0.011320017, -0.013976544, 0.38830712, 0.43865556, 1.0, 0.014225828, -0.014054318, -0.0031492866, -0.004079131, 0.005079831], "split_indices": [138, 139, 0, 142, 0, 142, 140, 140, 0, 0, 0, 139, 39, 41, 0, 0, 124, 139, 0, 0, 0, 142, 139, 93, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1979.0, 97.0, 1819.0, 160.0, 1642.0, 177.0, 1487.0, 155.0, 88.0, 89.0, 1138.0, 349.0, 965.0, 173.0, 130.0, 219.0, 873.0, 92.0, 113.0, 106.0, 689.0, 184.0, 578.0, 111.0, 93.0, 91.0, 445.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0005105625, -0.0044291564, 0.008333604, 0.0024926374, -0.014545122, -0.008469512, 0.016523842, 0.0007484565, -0.017520335, -0.010308774, 0.015365702, 0.0019950697, -0.015539162, -0.0055801207, 0.009848684, 0.011117323, -0.07940767, -0.010406564, 0.01601902, -0.0018141015, -0.015416421, -0.0023814018, 0.011864017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1], "loss_changes": [0.8432267, 1.8985648, 0.0, 3.307617, 0.0, 2.6696775, 0.0, 2.7829673, 0.0, 2.7400932, 0.0, 1.0342853, 0.0, 1.6173439, 0.0, 3.433232, 1.1083797, 1.6177257, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1], "split_conditions": [1.2066275, 1.0665853, 0.008333604, 1.0, -0.014545122, 1.0358227, 0.016523842, 0.87650514, -0.017520335, 0.7478956, 0.015365702, 0.72102535, -0.015539162, 0.5127808, 0.009848684, 0.49792826, 1.0, 0.446167, 0.01601902, -0.0018141015, -0.015416421, -0.0023814018, 0.011864017], "split_indices": [141, 141, 0, 125, 0, 143, 0, 139, 0, 142, 0, 140, 0, 141, 0, 139, 15, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1945.0, 116.0, 1854.0, 91.0, 1737.0, 117.0, 1646.0, 91.0, 1535.0, 111.0, 1415.0, 120.0, 1312.0, 103.0, 1070.0, 242.0, 935.0, 135.0, 133.0, 109.0, 847.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.00049356715, -0.0050994917, 0.010176827, 0.002968749, -0.017222738, -0.010638806, 0.103362285, 0.002410704, -0.07523155, 0.018110862, 0.0028996207, -0.007237335, 0.01360309, 0.0031429369, -0.021050883, 0.0053420146, -0.013804033, -0.006356179, 0.012709412, -0.034325954, 0.025761412, 0.007347462, -0.06918065, -0.0059972755, 0.05472256, -0.014292545, -0.0029693176, 0.014764912, -4.7739726e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1], "loss_changes": [0.972636, 2.6644945, 0.0, 2.5751185, 0.0, 1.3992205, 1.3008795, 1.7803483, 4.025636, 0.0, 0.0, 2.1192973, 0.0, 0.0, 0.0, 1.673529, 0.0, 0.96300095, 0.0, 2.152965, 1.2389969, 0.0, 1.2608936, 0.0, 1.9133198, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1], "split_conditions": [1.2553585, 1.2514719, 0.010176827, 0.87650514, -0.017222738, 0.6569235, 1.0, 0.5973865, 1.0, 0.018110862, 0.0028996207, 0.5427232, 0.01360309, 0.0031429369, -0.021050883, 0.49153346, -0.013804033, 1.0, 0.012709412, 0.14650463, 1.0, 0.007347462, 1.2377071, -0.0059972755, 0.03846154, -0.014292545, -0.0029693176, 0.014764912, -4.7739726e-05], "split_indices": [142, 140, 0, 139, 0, 139, 124, 139, 105, 0, 0, 139, 0, 0, 0, 139, 0, 17, 0, 139, 5, 0, 138, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1976.0, 89.0, 1885.0, 91.0, 1660.0, 225.0, 1381.0, 279.0, 110.0, 115.0, 1288.0, 93.0, 156.0, 123.0, 1175.0, 113.0, 1072.0, 103.0, 573.0, 499.0, 140.0, 433.0, 126.0, 373.0, 151.0, 282.0, 139.0, 234.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0016220217, 0.006468843, -0.00827213, 0.013054859, -0.04895549, 0.047854144, -0.0019162585, -1.4216619e-05, -0.010118571, 0.02264035, 0.016944062, 0.012666539, -0.015179503, -0.0076971543, 0.011769789, -0.0010479246, 0.014532763, 0.007823363, -0.050858747, -0.014123501, 0.022370797, 0.001461426, -0.010195966, 0.039954152, -0.013228277, 0.09606279, -0.009345178, 0.00052712864, 0.028872633, 0.09849509, -0.09809869, 0.015888505, -0.018950237, 0.018786792, 0.0014708039, -0.00033650748, -0.018793225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, 21, -1, 23, -1, -1, 25, -1, 27, 29, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8425305, 0.71143466, 0.0, 0.90755445, 0.5277525, 1.6064037, 2.6621244, 0.0, 0.0, 1.2515733, 0.0, 2.0195074, 0.0, 1.2202312, 0.0, 3.3027003, 0.0, 0.0, 0.73271525, 0.0, 2.3440611, 0.0, 0.0, 2.140976, 0.0, 6.6630588, 3.9433353, 7.2824273, 0.0, 1.3928206, 1.9233179, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 11, 11, 13, 13, 15, 15, 18, 18, 20, 20, 23, 23, 25, 25, 26, 26, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, 22, -1, 24, -1, -1, 26, -1, 28, 30, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.00827213, 0.36290243, 0.30816057, 0.36964688, 1.3461539, -1.4216619e-05, -0.010118571, 0.3283406, 0.016944062, 0.65384614, -0.015179503, 0.16508219, 0.011769789, 0.42053598, 0.014532763, 0.007823363, 1.0, -0.014123501, 0.30769232, 0.001461426, -0.010195966, 1.0, -0.013228277, -0.1923077, -0.30769232, 0.82579786, 0.028872633, 0.8256487, 0.73645115, 0.015888505, -0.018950237, 0.018786792, 0.0014708039, -0.00033650748, -0.018793225], "split_indices": [43, 40, 0, 141, 140, 142, 1, 0, 0, 140, 0, 1, 0, 142, 0, 139, 0, 0, 69, 0, 1, 0, 0, 69, 0, 1, 1, 140, 0, 139, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1949.0, 112.0, 1742.0, 207.0, 524.0, 1218.0, 107.0, 100.0, 434.0, 90.0, 1110.0, 108.0, 329.0, 105.0, 1006.0, 104.0, 110.0, 219.0, 144.0, 862.0, 96.0, 123.0, 774.0, 88.0, 362.0, 412.0, 242.0, 120.0, 186.0, 226.0, 132.0, 110.0, 90.0, 96.0, 110.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044194427, 0.0033141295, -0.07634168, -0.0059561045, 0.014268734, -0.025307884, 0.006252321, -0.014556189, 0.013542925, -0.0017906883, -0.1164709, 0.00931564, -0.0099586025, 0.0041683223, -0.028351013, -0.001797975, 0.01332119, 0.026636882, -0.03363701, 0.00740357, 0.01335106, -0.07710779, 0.008533563, 0.09735785, -0.03621062, -0.1618754, -0.012454518, 0.014578714, 0.0048928526, -0.00937211, 0.0008613709, -0.023132224, -0.009544794, 0.008371488, -0.011364148], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, -1, 25, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1458049, 2.4031618, 4.9085183, 2.1205745, 0.0, 0.0, 0.0, 2.1388311, 0.0, 1.5868613, 4.834485, 1.8065394, 0.0, 0.0, 0.0, 1.0900275, 0.0, 1.3073202, 2.9376016, 2.1146498, 0.0, 2.2798886, 0.0, 0.41278934, 0.93576646, 0.8303709, 2.2965372, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, -1, 26, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0665853, 1.0, 1.1592654, 5.0, 0.014268734, -0.025307884, 0.006252321, 0.76303107, 0.013542925, 2.0, 0.80745256, 0.6985237, -0.0099586025, 0.0041683223, -0.028351013, 0.36290243, 0.01332119, 0.38830712, 0.6011969, 1.0, 0.01335106, 0.44446307, 0.008533563, 1.0, 0.15384616, 1.0, 0.4980591, 0.014578714, 0.0048928526, -0.00937211, 0.0008613709, -0.023132224, -0.009544794, 0.008371488, -0.011364148], "split_indices": [141, 125, 140, 0, 0, 0, 0, 141, 0, 0, 140, 140, 0, 0, 0, 141, 0, 142, 139, 53, 0, 141, 0, 59, 1, 108, 140, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1860.0, 200.0, 1744.0, 116.0, 88.0, 112.0, 1644.0, 100.0, 1461.0, 183.0, 1312.0, 149.0, 94.0, 89.0, 1204.0, 108.0, 636.0, 568.0, 539.0, 97.0, 416.0, 152.0, 176.0, 363.0, 180.0, 236.0, 88.0, 88.0, 159.0, 204.0, 88.0, 92.0, 121.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0030344569, -0.007234209, 0.057405334, 0.0006063721, -0.014166753, -0.0036126475, 0.11181568, -0.009367075, 0.082344085, 0.020873426, -0.00034663657, 0.002720948, -0.08775422, 0.022139719, -0.005516396, 0.03192869, -0.027658848, -0.016124448, -0.00029577213, 0.07219976, -0.009862035, -0.107130215, 0.009916259, 0.018480783, 0.009521669, -0.0071240733, 0.027837554, -0.0033419149, -0.016504603, 0.0938165, -0.103349045, 0.009665745, -0.008444827, 0.008412238, -0.0029021576, 0.020303426, 0.0030697116, -0.014802364, -0.006061679], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, -1, -1, 23, 25, 27, 29, -1, 31, -1, 33, -1, -1, 35, 37, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1562734, 1.8361303, 1.6743134, 1.3418305, 0.0, 0.0, 2.323979, 1.3900498, 3.4226456, 0.0, 0.0, 1.1277905, 1.2214161, 0.0, 0.0, 1.0905565, 1.8603688, 0.0, 0.0, 2.3291588, 0.7358367, 0.85380673, 4.0197635, 0.0, 1.7358868, 0.0, 0.63046056, 0.0, 0.0, 1.6751826, 0.3436278, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 26, 26, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, -1, -1, 24, 26, 28, 30, -1, 32, -1, 34, -1, -1, 36, 38, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.90361995, 1.521938, 1.5412097, 0.71047145, -0.014166753, -0.0036126475, 1.1919701, 2.0, 1.0, 0.020873426, -0.00034663657, 0.36290243, 1.0, 0.022139719, -0.005516396, 1.0, 0.44446307, -0.016124448, -0.00029577213, 1.2177447, 0.14816526, 0.3807156, 1.0, 0.018480783, 1.0, -0.0071240733, 0.2799348, -0.0033419149, -0.016504603, 0.5170228, 0.5745219, 0.009665745, -0.008444827, 0.008412238, -0.0029021576, 0.020303426, 0.0030697116, -0.014802364, -0.006061679], "split_indices": [142, 138, 138, 142, 0, 0, 143, 0, 105, 0, 0, 141, 106, 0, 0, 23, 141, 0, 0, 138, 143, 142, 115, 0, 108, 0, 143, 0, 0, 141, 141, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1742.0, 329.0, 1646.0, 96.0, 121.0, 208.0, 1467.0, 179.0, 113.0, 95.0, 1271.0, 196.0, 89.0, 90.0, 648.0, 623.0, 105.0, 91.0, 330.0, 318.0, 200.0, 423.0, 118.0, 212.0, 121.0, 197.0, 88.0, 112.0, 243.0, 180.0, 110.0, 102.0, 99.0, 98.0, 89.0, 154.0, 88.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.00045083725, 0.009006045, -0.0049671857, -0.009547631, 0.0020867623, 0.03137447, -0.019157916, -0.016857298, 0.16695134, 0.005899018, -0.017533472, 0.039607875, -0.01984729, 0.030864561, 0.0037402262, -0.022528403, 0.101246506, -0.016103454, 0.01823394, 0.018869981, -0.015701046, 0.022588968, -0.0024595133, 0.008007192, -0.009530672, -0.03829034, 0.014299922, 0.0037632152, -0.013161975, 0.00655097, -0.006801044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, 15, -1, 17, -1, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [0.8429051, 0.0, 1.2539097, 0.0, 1.1336628, 5.0089607, 4.1324587, 5.7940507, 3.6896281, 2.46654, 0.0, 3.42721, 0.0, 0.0, 0.0, 3.9027052, 3.2782283, 2.3613954, 0.0, 3.8030632, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4404142, 0.0, 1.121238, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 25, 25, 27, 27], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, 16, -1, 18, -1, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [-0.5769231, 0.009006045, -0.5, -0.009547631, 1.0, 0.7860664, 0.80339915, 0.56194913, 1.0, 0.56194913, -0.017533472, 1.0, -0.01984729, 0.030864561, 0.0037402262, 0.45255446, 1.0, 1.0, 0.01823394, 0.33120996, -0.015701046, 0.022588968, -0.0024595133, 0.008007192, -0.009530672, 0.31571105, 0.014299922, 1.0, -0.013161975, 0.00655097, -0.006801044], "split_indices": [1, 0, 1, 0, 126, 141, 141, 141, 69, 141, 0, 121, 0, 0, 0, 140, 124, 97, 0, 140, 0, 0, 0, 0, 0, 141, 0, 106, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 98.0, 1964.0, 142.0, 1822.0, 766.0, 1056.0, 565.0, 201.0, 910.0, 146.0, 431.0, 134.0, 96.0, 105.0, 701.0, 209.0, 310.0, 121.0, 536.0, 165.0, 105.0, 104.0, 140.0, 170.0, 367.0, 169.0, 253.0, 114.0, 136.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.14592025E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}