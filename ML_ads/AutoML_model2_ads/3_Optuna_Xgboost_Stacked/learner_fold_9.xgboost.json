{"learner": {"attributes": {"best_iteration": "15", "best_score": "1.253308"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "66"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.0035375208, -0.15520686, 0.3913303, -0.2433339, 0.052464716, 0.14213878, 0.5550642, -0.26794255, -0.003066098, 0.10551782, -0.00984622, 0.034461394, 0.0002655858, 0.45997164, 0.078994274, -0.30429062, -0.18372767, 0.19622868, -0.0007407927, 0.028936122, 0.056479108, -0.23031633, -0.354805, -0.010569646, -0.026985098, 0.01066435, 0.027921284, -0.018442541, -0.030455157, -0.38552463, -0.02837333, -0.029858634, -0.43192425, -0.045655146, -0.040784426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1], "loss_changes": [124.090706, 27.39732, 23.460632, 5.5005035, 3.5711854, 6.439135, 7.750313, 2.8835068, 0.0, 3.380386, 0.0, 0.0, 0.0, 4.4171715, 0.0, 2.458786, 1.9085665, 1.3604484, 0.0, 0.0, 0.0, 0.9095936, 0.8536682, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1012535, 0.0, 0.0, 0.10555267, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 29, 29, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1], "split_conditions": [0.70048, 0.49791622, 0.8417005, 0.52959335, 1.0, 1.0, 1.3561559, 1.0, -0.003066098, 1.0, -0.00984622, 0.034461394, 0.0002655858, 1.0, 0.078994274, 1.0, 1.0, 1.0, -0.0007407927, 0.028936122, 0.056479108, 1.278068, 0.3323314, -0.010569646, -0.026985098, 0.01066435, 0.027921284, -0.018442541, -0.030455157, 1.2027056, -0.02837333, -0.029858634, 1.2552593, -0.045655146, -0.040784426], "split_indices": [140, 139, 139, 142, 0, 93, 139, 93, 0, 106, 0, 0, 0, 109, 0, 122, 80, 111, 0, 0, 0, 138, 140, 0, 0, 0, 0, 0, 0, 138, 0, 0, 138, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1497.0, 575.0, 1051.0, 446.0, 228.0, 347.0, 942.0, 109.0, 330.0, 116.0, 93.0, 135.0, 247.0, 100.0, 658.0, 284.0, 183.0, 147.0, 94.0, 153.0, 267.0, 391.0, 149.0, 135.0, 88.0, 95.0, 165.0, 102.0, 273.0, 118.0, 95.0, 178.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00011802546, -0.12912945, 0.37968963, -0.20969541, 0.054774866, 0.0095397625, 0.51225275, -0.33688816, -0.14618848, 0.19382992, -0.054027747, 0.02942234, 0.6422882, -0.019548401, -0.38420007, 0.004605722, -0.17875643, 0.029608915, 0.0077723064, -0.017190946, 0.00966558, 0.08225706, 0.0466792, -0.4235211, -0.030730551, -0.038116608, -0.13698936, -0.039179835, -0.045524385, 0.0003469005, -0.18136394, -0.10333313, -0.26754078, -0.020097133, 0.0003458601, -0.022935646, -0.03057251], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, 21, -1, 23, -1, 25, -1, -1, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, 31, 33, 35, -1, -1, -1, -1], "loss_changes": [100.81557, 22.713585, 19.672432, 8.610744, 7.065502, 0.0, 10.09314, 2.3749847, 4.451606, 2.4339628, 4.6538625, 0.0, 7.0554733, 0.0, 0.80426407, 0.0, 5.140068, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.17711258, 0.0, 0.0, 3.141323, 0.0, 0.0, 0.0, 2.5754614, 2.0958176, 0.26536274, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 23, 23, 26, 26, 30, 30, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, 22, -1, 24, -1, 26, -1, -1, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, 32, 34, 36, -1, -1, -1, -1], "split_conditions": [0.76225835, 0.49791622, 0.7991742, 0.26278603, 1.0, 0.0095397625, 1.0129824, 1.1840668, 0.20380725, 1.0, 1.0, 0.02942234, 1.145815, -0.019548401, 0.23177461, 0.004605722, 0.22756366, 0.029608915, 0.0077723064, -0.017190946, 0.00966558, 0.08225706, 0.0466792, 0.16507967, -0.030730551, -0.038116608, 1.2630832, -0.039179835, -0.045524385, 0.0003469005, 0.1923077, 0.41348258, 0.403525, -0.020097133, 0.0003458601, -0.022935646, -0.03057251], "split_indices": [139, 139, 142, 141, 93, 0, 139, 138, 143, 115, 111, 0, 143, 0, 143, 0, 139, 0, 0, 0, 0, 0, 0, 142, 0, 0, 138, 0, 0, 0, 1, 139, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1533.0, 522.0, 1066.0, 467.0, 166.0, 356.0, 355.0, 711.0, 205.0, 262.0, 133.0, 223.0, 89.0, 266.0, 103.0, 608.0, 109.0, 96.0, 147.0, 115.0, 110.0, 113.0, 176.0, 90.0, 104.0, 504.0, 88.0, 88.0, 121.0, 383.0, 201.0, 182.0, 105.0, 96.0, 91.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0061375652, -0.12106392, 0.34288973, -0.18781771, 0.03531433, 0.22204089, 0.56333923, -0.24055816, -0.08117125, 0.027610002, -0.032820072, 0.09373187, 0.039372195, 0.077646315, 0.035021532, -0.267414, -0.006474922, 0.008383284, -0.14242278, -0.16838811, 0.11040716, -0.0109704, 0.027682418, -0.0118198395, -0.2943172, -0.00523441, -0.026658526, -0.023637874, -0.010254451, 0.0051200893, 0.016894815, -0.3370643, -0.020564748, -0.36661735, -0.029053394, -0.03882022, -0.03343613], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, -1, -1, -1, 23, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1], "loss_changes": [83.2333, 16.295029, 13.693508, 6.1532936, 7.661504, 7.3133698, 8.266762, 3.4561386, 3.6586444, 0.0, 7.0678015, 7.077035, 0.0, 0.0, 0.0, 2.5491257, 0.0, 0.0, 2.9526787, 0.83715105, 0.6134801, 0.0, 0.0, 0.0, 2.0392227, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.49916458, 0.0, 0.1545639, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 15, 15, 18, 18, 19, 19, 20, 20, 24, 24, 31, 31, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, -1, -1, -1, 24, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1], "split_conditions": [0.76225835, 0.49791622, 1.1429962, 0.38830712, 0.4672373, 1.0, 1.1919701, 0.3801079, 1.2695129, 0.027610002, 0.6505282, 0.838765, 0.039372195, 0.077646315, 0.035021532, 1.1776886, -0.006474922, 0.008383284, 0.1923077, 0.6308827, 1.0, -0.0109704, 0.027682418, -0.0118198395, 0.30363682, -0.00523441, -0.026658526, -0.023637874, -0.010254451, 0.0051200893, 0.016894815, 1.0, -0.020564748, 0.22351408, -0.029053394, -0.03882022, -0.03343613], "split_indices": [139, 139, 139, 142, 141, 53, 143, 141, 138, 0, 140, 141, 0, 0, 0, 138, 0, 0, 1, 142, 109, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 17, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1561.0, 514.0, 1094.0, 467.0, 332.0, 182.0, 732.0, 362.0, 103.0, 364.0, 190.0, 142.0, 91.0, 91.0, 635.0, 97.0, 98.0, 264.0, 187.0, 177.0, 90.0, 100.0, 97.0, 538.0, 153.0, 111.0, 92.0, 95.0, 88.0, 89.0, 363.0, 175.0, 222.0, 141.0, 133.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0009399587, -0.12783296, 0.28110537, -0.19176987, -0.015098796, 0.20362636, 0.06486165, -0.22562987, -0.07076758, -0.08894071, 0.09160099, 0.15776667, 0.04278853, -0.28176013, -0.18394896, 0.0056085624, -0.017530406, -0.024297656, 0.012621368, -0.011220324, 0.026474444, 0.1125721, 0.03157194, -0.017466782, -0.32765684, -0.087803684, -0.26277077, 0.013594692, -0.011624687, -0.0028961531, 0.17423613, -0.038149465, -0.028568154, 0.0009792421, -0.017917028, -0.020710547, -0.031008625, 0.02574429, 0.0029569815], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, 23, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1, 31, 33, 35, -1, -1, -1, 37, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [74.35618, 10.177515, 18.479874, 3.6915207, 4.0261264, 5.5124607, 0.0, 1.647049, 2.6123755, 4.7245493, 7.37506, 3.1766796, 0.0, 1.4745522, 3.0616493, 0.0, 0.0, 0.0, 2.8924801, 0.0, 0.0, 3.0197272, 0.0, 0.0, 0.47456932, 1.6228989, 0.58470917, 0.0, 0.0, 0.0, 2.900968, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 18, 18, 21, 21, 24, 24, 25, 25, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, 24, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1, 32, 34, 36, -1, -1, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4193542, 0.42784345, 1.3188831, 1.0, 1.0, 1.0, 0.06486165, 0.25091603, 1.2679365, 1.0, 1.0, 1.0, 0.04278853, 1.1840668, 1.0, 0.0056085624, -0.017530406, -0.024297656, 0.53385, -0.011220324, 0.026474444, -0.34615386, 0.03157194, -0.017466782, 1.225907, 1.2542967, 1.0, 0.013594692, -0.011624687, -0.0028961531, 0.1923077, -0.038149465, -0.028568154, 0.0009792421, -0.017917028, -0.020710547, -0.031008625, 0.02574429, 0.0029569815], "split_indices": [138, 139, 139, 71, 97, 125, 0, 141, 138, 69, 13, 58, 0, 138, 69, 0, 0, 0, 139, 0, 0, 1, 0, 0, 138, 138, 39, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1412.0, 649.0, 901.0, 511.0, 536.0, 113.0, 704.0, 197.0, 302.0, 209.0, 445.0, 91.0, 300.0, 404.0, 89.0, 108.0, 120.0, 182.0, 96.0, 113.0, 346.0, 99.0, 90.0, 210.0, 182.0, 222.0, 93.0, 89.0, 105.0, 241.0, 92.0, 118.0, 88.0, 94.0, 102.0, 120.0, 153.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.009499221, -0.09772491, 0.2866495, -0.15609287, 0.042997822, 0.16352886, 0.461285, -0.18823631, -0.0242581, 0.030014757, -0.03308197, 0.25098205, -2.6053967e-05, 0.024479212, 0.05897374, -0.20995954, -0.0039190883, 0.009708433, -0.015922062, -0.01992767, 0.06078725, 0.012912444, 0.04317146, -0.009069048, -0.23030874, -0.004576011, 0.021576526, -0.28732058, -0.14905049, -0.024407577, -0.32068083, 0.003090727, -0.024376513, -0.03818797, -0.027478168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, -1, -1, -1, 23, -1, -1, -1, -1, 25, -1, -1, -1, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [61.574028, 12.271265, 12.427715, 4.4749317, 8.568989, 4.8558035, 6.6463547, 2.748846, 3.3899734, 0.0, 5.2729926, 4.867223, 0.0, 0.0, 0.0, 1.7984314, 0.0, 0.0, 0.0, 0.0, 3.5667005, 0.0, 0.0, 0.0, 2.9324837, 0.0, 0.0, 0.53666687, 4.4486504, 0.0, 0.5898819, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 15, 15, 20, 20, 24, 24, 27, 27, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, -1, -1, -1, 24, -1, -1, -1, -1, 26, -1, -1, -1, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [0.70048, 0.49791622, 1.0129824, 0.44700637, 0.4672373, 1.0, 1.019717, 1.0, 0.46040413, 0.030014757, 0.5227802, 0.7835469, -2.6053967e-05, 0.024479212, 0.05897374, 1.1840668, -0.0039190883, 0.009708433, -0.015922062, -0.01992767, 1.0, 0.012912444, 0.04317146, -0.009069048, 0.28457788, -0.004576011, 0.021576526, 0.18474929, 1.2736244, -0.024407577, 0.27657035, 0.003090727, -0.024376513, -0.03818797, -0.027478168], "split_indices": [140, 139, 139, 142, 141, 50, 141, 121, 140, 0, 140, 139, 0, 0, 0, 138, 0, 0, 0, 0, 105, 0, 0, 0, 139, 0, 0, 143, 138, 0, 143, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1494.0, 578.0, 1056.0, 438.0, 339.0, 239.0, 849.0, 207.0, 100.0, 338.0, 221.0, 118.0, 89.0, 150.0, 741.0, 108.0, 109.0, 98.0, 122.0, 216.0, 132.0, 89.0, 108.0, 633.0, 128.0, 88.0, 372.0, 261.0, 162.0, 210.0, 90.0, 171.0, 90.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00019436456, -0.1030491, 0.22370465, -0.15846571, -0.02390961, 0.12701286, 0.36966848, -0.22460467, -0.10992715, -0.073220946, 0.07446017, 0.028912023, 0.22412278, 0.01824828, 0.04840597, -0.013950444, -0.25372642, 0.011144414, -0.16018747, 0.009537334, -0.14904875, -0.010471176, 0.025923124, 0.014387271, -0.008145021, -0.0012328181, 0.041328356, -0.017359791, -0.029471165, -0.029692153, -0.11943891, 0.0128141865, -0.010407329, -0.004194224, -0.024514427, -0.0029147, -0.17654242, -0.024938503, -0.010753361], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, -1, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, 37, -1, -1], "loss_changes": [47.8365, 6.2188425, 9.244339, 2.6773796, 2.8328354, 3.7534661, 5.5886345, 0.87482643, 5.3517, 2.4411225, 6.45563, 2.4867144, 8.855998, 0.0, 0.0, 0.0, 0.8637123, 0.0, 2.1841106, 2.5063007, 2.0893679, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5571074, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9299536, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 16, 16, 18, 18, 19, 19, 20, 20, 30, 30, 36, 36], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, -1, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, 38, -1, -1], "split_conditions": [1.4193542, 0.4080126, 1.0129824, 0.26278603, 1.0, 1.0, 0.9789495, 1.1840668, 0.20380725, 1.0, 0.52685916, 0.73487437, 0.70048, 0.01824828, 0.04840597, -0.013950444, 1.0, 0.011144414, 0.23054773, 0.51408464, 0.5251513, -0.010471176, 0.025923124, 0.014387271, -0.008145021, -0.0012328181, 0.041328356, -0.017359791, -0.029471165, -0.029692153, 0.3209992, 0.0128141865, -0.010407329, -0.004194224, -0.024514427, -0.0029147, 1.0, -0.024938503, -0.010753361], "split_indices": [138, 140, 139, 141, 105, 53, 141, 138, 143, 122, 139, 140, 140, 0, 0, 0, 122, 0, 139, 141, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 0, 0, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1418.0, 655.0, 834.0, 584.0, 394.0, 261.0, 353.0, 481.0, 389.0, 195.0, 196.0, 198.0, 99.0, 162.0, 90.0, 263.0, 89.0, 392.0, 186.0, 203.0, 99.0, 96.0, 96.0, 100.0, 88.0, 110.0, 89.0, 174.0, 90.0, 302.0, 91.0, 95.0, 96.0, 107.0, 117.0, 185.0, 90.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.0021137185, -0.0828385, 0.22340533, -0.14164773, 0.021225411, 0.17330888, 0.045986053, -0.16843489, -0.032789238, -0.069518216, 0.1455482, 0.032380816, 0.119682685, -0.18756106, -0.004839529, -0.015044172, 0.0075258925, 0.0024685112, -0.01759809, 0.034000322, -0.0010324465, -0.006833955, 0.22118144, -0.00950887, -0.2069039, 0.009834672, 0.03627338, -0.17393166, -0.25751248, -0.13299718, -0.027670328, -0.018889716, -0.032300886, -0.004892343, -0.018767452], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, 25, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [38.76398, 9.118677, 6.7756615, 2.776041, 6.0694475, 3.809372, 0.0, 1.7540665, 2.3898816, 3.119063, 6.880421, 0.0, 6.6412406, 1.1787376, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.9295816, 0.0, 0.90942955, 0.0, 0.0, 1.3882761, 0.96622086, 1.0848742, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 22, 22, 24, 24, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, 26, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [0.70048, 0.4400628, 1.3561559, 0.44700637, 0.5982285, 0.79961056, 0.045986053, 1.0, 1.0, 0.53437847, 0.6349509, 0.032380816, 0.9291086, 0.10037874, -0.004839529, -0.015044172, 0.0075258925, 0.0024685112, -0.01759809, 0.034000322, -0.0010324465, -0.006833955, -0.115384616, -0.00950887, 0.5769231, 0.009834672, 0.03627338, 1.0, 1.0, 1.0, -0.027670328, -0.018889716, -0.032300886, -0.004892343, -0.018767452], "split_indices": [140, 140, 139, 142, 139, 140, 0, 113, 122, 140, 141, 0, 140, 139, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 7, 12, 97, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1490.0, 572.0, 952.0, 538.0, 472.0, 100.0, 764.0, 188.0, 311.0, 227.0, 124.0, 348.0, 659.0, 105.0, 90.0, 98.0, 165.0, 146.0, 101.0, 126.0, 122.0, 226.0, 114.0, 545.0, 121.0, 105.0, 330.0, 215.0, 236.0, 94.0, 105.0, 110.0, 93.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00017808209, -0.07031107, 0.21370663, -0.12522848, 0.014675865, 0.002574546, 0.3020322, -0.18597683, -0.08676151, 0.017219862, -0.025922779, 0.04420003, 0.17211702, -0.027458165, -0.15491611, 0.012071463, -0.12654561, 0.020776464, -0.018893776, 0.0036348086, 0.030058652, -0.004293711, -0.21152769, -0.1690279, 0.0011708166, 0.012916927, -0.070167735, -0.030457918, -0.012052126, -0.23999661, -0.004920388, -0.018847875, 0.0064164535, -0.02938506, -0.016580997], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [31.11134, 7.2529254, 8.51671, 2.205943, 3.9010787, 0.0, 6.3462048, 1.0072784, 4.770957, 0.0, 3.6921482, 0.0, 3.1570325, 0.0, 1.7179527, 0.0, 2.848569, 3.716352, 0.0, 0.0, 0.0, 0.0, 1.5242891, 3.154892, 0.0, 0.0, 3.2580628, 0.0, 0.0, 0.930892, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 17, 17, 22, 22, 23, 23, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.76225835, 0.4400628, 0.7991742, 0.26278603, 0.4980591, 0.002574546, 1.0, 0.13580762, 0.20380725, 0.017219862, 1.0, 0.04420003, 1.1037354, -0.027458165, 0.17637321, 0.012071463, 0.50696194, 1.0, -0.018893776, 0.0036348086, 0.030058652, -0.004293711, 0.22351408, 1.0, 0.0011708166, 0.012916927, 1.0, -0.030457918, -0.012052126, 0.33910778, -0.004920388, -0.018847875, 0.0064164535, -0.02938506, -0.016580997], "split_indices": [139, 140, 142, 141, 140, 0, 69, 142, 143, 0, 137, 0, 142, 0, 142, 0, 142, 93, 0, 0, 0, 0, 143, 93, 0, 0, 111, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1554.0, 513.0, 944.0, 610.0, 164.0, 349.0, 366.0, 578.0, 125.0, 485.0, 168.0, 181.0, 95.0, 271.0, 93.0, 485.0, 377.0, 108.0, 88.0, 93.0, 91.0, 180.0, 371.0, 114.0, 172.0, 205.0, 89.0, 91.0, 233.0, 138.0, 109.0, 96.0, 135.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0020477637, -0.10255308, 0.13623735, -0.1238554, 0.010514451, 0.06101153, 0.2887017, -0.08186337, -0.18281679, 0.14496042, -0.09845117, 0.43448132, 0.006819475, -0.041507263, -0.02069673, -0.22613105, -0.0048825913, 0.05319775, 0.031528246, 0.0068875626, -0.024336815, 0.022095522, 0.06433145, -0.08522999, 0.010129354, -0.029292613, -0.20194188, 0.01649225, -0.015895369, -0.015858538, -0.018935524, -0.014729366, -0.024282973, 0.0071043796, -0.0113858795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, 21, -1, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [28.999086, 5.136774, 10.379679, 2.607132, 0.0, 8.11235, 9.611481, 3.1049542, 2.5420284, 6.204793, 5.067936, 8.02644, 0.0, 2.9032927, 0.0, 0.5348015, 0.0, 6.1152616, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7312477, 0.0, 0.0, 0.5429697, 0.0, 0.0, 0.0, 1.5971919, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 23, 23, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, 22, -1, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [0.56020635, 3.0384614, 0.9291086, 1.0, 0.010514451, 0.15384616, 1.1919701, 0.50722474, 1.0, 0.76225835, 1.0, 0.996009, 0.006819475, 0.40983817, -0.02069673, 0.112523064, -0.0048825913, 0.7441023, 0.031528246, 0.0068875626, -0.024336815, 0.022095522, 0.06433145, 0.2748725, 0.010129354, -0.029292613, 0.25308752, 0.01649225, -0.015895369, -0.015858538, 0.31376776, -0.014729366, -0.024282973, 0.0071043796, -0.0113858795], "split_indices": [143, 1, 140, 23, 0, 1, 143, 140, 105, 139, 111, 139, 0, 142, 0, 143, 0, 141, 0, 0, 0, 0, 0, 140, 0, 0, 140, 0, 0, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1161.0, 905.0, 1053.0, 108.0, 606.0, 299.0, 615.0, 438.0, 397.0, 209.0, 180.0, 119.0, 465.0, 150.0, 331.0, 107.0, 258.0, 139.0, 97.0, 112.0, 89.0, 91.0, 356.0, 109.0, 88.0, 243.0, 169.0, 89.0, 169.0, 187.0, 104.0, 139.0, 96.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0016382216, -0.061380517, 0.16696584, -0.102226384, 0.0098379105, 0.057484053, 0.23712227, -0.022163082, -0.087424174, 0.12022313, -0.069306195, 0.017507486, -0.011628899, 0.15271884, 0.04126515, 0.0015199803, -0.10915286, 0.029250218, -0.0043221056, -0.15971623, 0.011672982, 0.030867053, 0.0017808223, -0.019404378, -0.08165978, -0.0031754721, -0.030288106, -0.014899692, -0.12269579, -0.011959869, 0.006663579, -0.01996201, -0.07384883, 0.0035601594, -0.017692354], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, -1, -1, 21, -1, -1, 23, -1, -1, 25, -1, -1, -1, -1, 27, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [21.55637, 4.357649, 4.385766, 1.6826115, 4.7700415, 4.5568066, 5.155712, 0.0, 1.8887119, 6.420027, 5.348609, 0.0, 0.0, 4.9442906, 0.0, 0.0, 1.6314039, 0.0, 0.0, 3.920391, 0.0, 0.0, 0.0, 0.0, 1.446491, 0.0, 0.0, 1.7158735, 1.2287078, 0.0, 0.0, 0.0, 2.2563148, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 13, 13, 16, 16, 19, 19, 24, 24, 27, 27, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, -1, -1, 22, -1, -1, 24, -1, -1, 26, -1, -1, -1, -1, 28, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [0.70048, 0.4400628, 0.8417005, 0.09421818, 0.5110199, 0.7934809, 2.0, -0.022163082, 0.16433378, 0.50722474, 0.7257812, 0.017507486, -0.011628899, -0.3846154, 0.04126515, 0.0015199803, 0.2243912, 0.029250218, -0.0043221056, 1.0, 0.011672982, 0.030867053, 0.0017808223, -0.019404378, 0.3215633, -0.0031754721, -0.030288106, 0.27213097, 1.0, -0.011959869, 0.006663579, -0.01996201, 1.3079672, 0.0035601594, -0.017692354], "split_indices": [140, 140, 139, 143, 141, 142, 0, 0, 143, 140, 142, 0, 0, 1, 0, 0, 142, 0, 0, 124, 0, 0, 0, 0, 143, 0, 0, 139, 53, 0, 0, 0, 138, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1498.0, 571.0, 952.0, 546.0, 223.0, 348.0, 105.0, 847.0, 228.0, 318.0, 133.0, 90.0, 235.0, 113.0, 148.0, 699.0, 111.0, 117.0, 214.0, 104.0, 109.0, 126.0, 171.0, 528.0, 113.0, 101.0, 201.0, 327.0, 88.0, 113.0, 127.0, 200.0, 97.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.001066933, -0.0719605, 0.1167536, -0.09310878, 0.01455517, 0.08369675, 0.0353371, -0.109277904, 0.005641555, 0.009723678, -0.013780193, -0.044810332, 0.16424426, -0.09271261, -0.020817516, -0.018023657, 0.012829969, 0.031981196, 0.108684376, -0.06925526, -0.019271495, 0.00031892248, 0.026107326, -0.12037606, -0.015213272, -0.011959173, 0.012292422, -0.04961404, -0.020882858, -0.08229358, 0.013781364, -0.013588197, 0.0037591555, 0.0001847373, -0.014211255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, 25, -1, 27, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [17.231659, 2.3565984, 6.0619135, 2.5022964, 3.187074, 7.038631, 0.0, 1.5301361, 0.0, 0.0, 0.0, 6.1422334, 3.6129084, 1.8766298, 0.0, 0.0, 0.0, 0.0, 5.086216, 1.7902098, 0.0, 2.6463027, 0.0, 2.084272, 3.233504, 0.0, 0.0, 1.3917638, 0.0, 1.102277, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, 26, -1, 28, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.5982285, 0.5272047, 1.3494815, 3.1538463, 1.0, 1.0, 0.0353371, 1.0, 0.005641555, 0.009723678, -0.013780193, 0.8130224, 0.6414547, 1.0384616, -0.020817516, -0.018023657, 0.012829969, 0.031981196, 0.9291086, 1.0, -0.019271495, 1.0, 0.026107326, 0.31376776, 0.3283229, -0.011959173, 0.012292422, 0.23922633, -0.020882858, 0.21802229, 0.013781364, -0.013588197, 0.0037591555, 0.0001847373, -0.014211255], "split_indices": [139, 142, 140, 1, 124, 39, 0, 40, 0, 0, 0, 142, 141, 1, 0, 0, 0, 0, 140, 124, 0, 59, 0, 139, 142, 0, 0, 140, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1288.0, 775.0, 1035.0, 253.0, 680.0, 95.0, 934.0, 101.0, 164.0, 89.0, 262.0, 418.0, 800.0, 134.0, 147.0, 115.0, 110.0, 308.0, 648.0, 152.0, 180.0, 128.0, 333.0, 315.0, 91.0, 89.0, 185.0, 148.0, 219.0, 96.0, 93.0, 92.0, 91.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0032975657, -0.07383314, 0.10090019, -0.091621794, 0.010490866, 0.07380781, 0.029150575, -0.042784255, -0.14036596, 0.042559166, 0.02513211, 0.025918856, -0.1440481, -0.11284131, -0.027642524, 0.18394156, -0.01110843, -0.027039427, 0.015596089, -0.02133963, -0.0072699427, -0.15095036, 0.0035312914, 0.003277431, 0.034869682, 0.05937406, -0.13574697, 0.008625582, -0.015220373, -0.09185947, -0.027279258, 0.020491239, -0.008340086, -0.020856246, -0.006374961, -0.014795578, -0.0013208946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, 31, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.485422, 3.6533337, 4.6888714, 2.487669, 0.0, 4.409905, 0.0, 3.6316278, 1.9586239, 5.1292543, 0.0, 2.1417964, 1.0440068, 2.4560175, 0.0, 4.6324415, 4.3045697, 3.1338956, 0.0, 0.0, 0.0, 2.491118, 0.0, 0.0, 0.0, 6.5038967, 0.9279256, 0.0, 0.0, 1.0279963, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, 32, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.56020635, 3.0384614, 1.3188831, 1.0, 0.010490866, 1.0, 0.029150575, 1.0, 0.49998152, 0.66754913, 0.02513211, 0.26923078, 1.0, 0.42035607, -0.027642524, 0.57810056, 0.03846154, 1.0, 0.015596089, -0.02133963, -0.0072699427, 1.2630832, 0.0035312914, 0.003277431, 0.034869682, 0.73935336, 0.5769231, 0.008625582, -0.015220373, 0.26005286, -0.027279258, 0.020491239, -0.008340086, -0.020856246, -0.006374961, -0.014795578, -0.0013208946], "split_indices": [143, 1, 139, 108, 0, 125, 0, 23, 140, 143, 0, 1, 26, 139, 0, 140, 1, 69, 0, 0, 0, 138, 0, 0, 0, 140, 1, 0, 0, 141, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1149.0, 908.0, 1045.0, 104.0, 795.0, 113.0, 522.0, 523.0, 676.0, 119.0, 311.0, 211.0, 435.0, 88.0, 186.0, 490.0, 221.0, 90.0, 107.0, 104.0, 346.0, 89.0, 97.0, 89.0, 313.0, 177.0, 116.0, 105.0, 233.0, 113.0, 155.0, 158.0, 88.0, 89.0, 136.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032402133, -0.05235297, 0.1225529, -0.03265759, -0.18092279, 0.165389, -0.00580533, -0.05679978, 0.061249085, -0.024807332, -0.012558575, 0.103309385, 0.041571, 0.023423204, -0.0738139, -0.0026530195, 0.021146933, 0.02420531, 0.042708676, -0.011836882, 0.016680844, -0.054461345, -0.022822253, 0.022168038, -0.0068453862, 0.007709741, -0.07194953, 0.0048498404, -0.1164895, -0.009522487, 0.012584929, -0.022005418, -0.06885695, 0.011172185, -0.15135488, -0.024343098, -0.0037306063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, 17, -1, 19, 21, -1, -1, -1, 23, -1, -1, 25, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, 33, -1, 35, -1, -1], "loss_changes": [12.739131, 3.7552986, 4.479413, 2.9155073, 0.73203325, 7.2726374, 0.0, 1.3963172, 3.467978, 0.0, 0.0, 3.1529875, 0.0, 3.6392283, 2.5220408, 0.0, 0.0, 0.0, 5.192583, 0.0, 0.0, 1.7255423, 0.0, 0.0, 0.0, 0.0, 2.2644646, 2.9424834, 2.066947, 0.0, 0.0, 0.0, 4.275548, 0.0, 2.068729, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 18, 18, 21, 21, 26, 26, 27, 27, 28, 28, 32, 32, 34, 34], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, 18, -1, 20, 22, -1, -1, -1, 24, -1, -1, 26, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, 34, -1, 36, -1, -1], "split_conditions": [0.70048, 1.0, 1.0, 0.5982285, 0.2604705, 1.3188831, -0.00580533, 1.0, 1.0, -0.024807332, -0.012558575, 0.8071234, 0.041571, 0.36287326, 1.0, -0.0026530195, 0.021146933, 0.02420531, 1.0, -0.011836882, 0.016680844, 0.1535796, -0.022822253, 0.022168038, -0.0068453862, 0.007709741, 1.0, 0.36704683, 0.27213097, -0.009522487, 0.012584929, -0.022005418, 0.3464803, 0.011172185, 0.48844358, -0.024343098, -0.0037306063], "split_indices": [140, 40, 119, 139, 142, 139, 0, 89, 50, 0, 0, 140, 0, 142, 64, 0, 0, 0, 108, 0, 0, 142, 0, 0, 0, 0, 126, 142, 139, 0, 0, 0, 142, 0, 142, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1483.0, 579.0, 1286.0, 197.0, 468.0, 111.0, 1023.0, 263.0, 89.0, 108.0, 375.0, 93.0, 179.0, 844.0, 166.0, 97.0, 114.0, 261.0, 90.0, 89.0, 750.0, 94.0, 100.0, 161.0, 88.0, 662.0, 243.0, 419.0, 133.0, 110.0, 132.0, 287.0, 90.0, 197.0, 109.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0024313051, -0.06148628, 0.08448004, -0.077616125, 0.009935562, 0.12738134, -0.023858238, -0.035551604, -0.119284555, -0.0025853633, 0.18090208, 0.008047233, -0.012578121, -0.08781239, 0.04894965, -0.096218124, -0.023081452, 0.08287282, 0.3021848, 0.0018489568, -0.13106948, 0.017441006, -0.008556457, -0.14866152, 0.0004153683, 0.021232253, -0.014266321, 0.007150553, 0.046827383, -0.021296388, -0.005661999, -0.12592348, -0.019583084, -0.016124709, -0.009236604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, 17, -1, -1, 19, 21, 23, -1, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [10.850576, 3.0172343, 4.2109547, 1.8526692, 0.0, 5.322612, 2.7328568, 2.3228698, 1.3660479, 0.0, 5.71873, 0.0, 0.0, 1.494452, 3.3921185, 2.31609, 0.0, 7.7660227, 8.23736, 0.0, 1.4084055, 0.0, 0.0, 0.30996227, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.23114657, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 23, 23, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, 18, -1, -1, 20, 22, 24, -1, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.56020635, 3.0384614, 0.15384616, 1.0, 0.009935562, -0.5, 1.0, 1.0, 0.49998152, -0.0025853633, 0.8301077, 0.008047233, -0.012578121, -0.30769232, 1.2642441, 0.34196812, -0.023081452, 0.7520928, 1.0, 0.0018489568, 1.0, 0.017441006, -0.008556457, 0.29029372, 0.0004153683, 0.021232253, -0.014266321, 0.007150553, 0.046827383, -0.021296388, -0.005661999, 0.16250928, -0.019583084, -0.016124709, -0.009236604], "split_indices": [143, 1, 1, 108, 0, 1, 59, 83, 140, 0, 139, 0, 0, 1, 138, 141, 0, 141, 15, 0, 13, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1163.0, 906.0, 1057.0, 106.0, 649.0, 257.0, 526.0, 531.0, 168.0, 481.0, 127.0, 130.0, 325.0, 201.0, 440.0, 91.0, 266.0, 215.0, 94.0, 231.0, 104.0, 97.0, 289.0, 151.0, 169.0, 97.0, 90.0, 125.0, 110.0, 121.0, 195.0, 94.0, 95.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00086472376, -0.06340693, 0.07179869, -0.044774763, -0.017045507, 0.04944438, 0.02994061, -0.086754836, 0.017072879, -0.011963895, 0.08789347, 0.004979675, -0.12599747, 0.07718808, -0.007894444, 0.12424947, -0.0038571854, -0.08204992, -0.023497311, 0.020603918, -0.0011870746, 0.054351702, 0.039897807, 0.006012825, -0.1406326, -0.017339543, 0.017669907, -0.0076352693, -0.018832413, 0.008738838, -0.014045483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, 15, -1, 17, 19, -1, 21, -1, 23, -1, -1, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [9.43722, 2.1660686, 5.0065956, 2.401641, 0.0, 5.824986, 0.0, 2.9526138, 2.1587656, 0.0, 3.3563752, 0.0, 2.0497823, 2.639326, 0.0, 10.88805, 0.0, 2.5403998, 0.0, 0.0, 0.0, 3.9645987, 0.0, 0.0, 0.6621704, 3.6746788, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, 16, -1, 18, 20, -1, 22, -1, 24, -1, -1, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [0.49791622, 1.3288147, 1.0769231, 0.28457788, -0.017045507, 0.5380342, 0.02994061, 0.10037874, 1.0, -0.011963895, 1.145815, 0.004979675, 0.31646383, 1.0, -0.007894444, 1.019717, -0.0038571854, 0.0, -0.023497311, 0.020603918, -0.0011870746, 1.0, 0.039897807, 0.006012825, 0.21611509, 1.0, 0.017669907, -0.0076352693, -0.018832413, 0.008738838, -0.014045483], "split_indices": [139, 138, 1, 139, 0, 143, 0, 139, 12, 0, 143, 0, 140, 126, 0, 141, 0, 1, 0, 0, 0, 97, 0, 0, 141, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1086.0, 984.0, 925.0, 161.0, 896.0, 88.0, 551.0, 374.0, 166.0, 730.0, 123.0, 428.0, 230.0, 144.0, 567.0, 163.0, 305.0, 123.0, 94.0, 136.0, 452.0, 115.0, 89.0, 216.0, 285.0, 167.0, 92.0, 124.0, 154.0, 131.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029098047, -0.03947071, 0.091139965, -0.023365596, -0.14596164, 0.023436094, 0.049765013, -0.010272151, -0.017188266, -0.009371094, -0.020877985, -0.022452138, 0.022562206, -0.049840912, 0.058996018, -0.11594406, 0.015766988, 0.0073989565, -0.08218931, -0.057302743, 0.15789968, 0.0009455235, -0.02487198, -0.1270546, 0.010517016, 0.0019525079, -0.015225852, -0.0043252436, 0.033330437, -0.0894357, -0.023735465, -0.008174706, 0.010468348, -0.0033996433, -0.014712247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [7.124665, 2.5588522, 3.4369411, 2.5202012, 0.64332914, 0.0, 5.7149534, 3.264359, 0.0, 0.0, 0.0, 5.3719444, 0.0, 3.036334, 4.980528, 3.4964976, 0.0, 0.0, 2.4997368, 1.4517543, 8.2562275, 0.0, 0.0, 1.6804924, 1.702884, 0.0, 0.0, 0.0, 0.0, 0.96582913, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.70048, 1.0, 0.79961056, 0.63536245, 0.29532394, 0.023436094, 0.0, 0.44133672, -0.017188266, -0.009371094, -0.020877985, 1.2284123, 0.022562206, 1.2046952, 1.0, 0.96076936, 0.015766988, 0.0073989565, 1.0, 1.0, 1.0, 0.0009455235, -0.02487198, 0.39251393, 1.0, 0.0019525079, -0.015225852, -0.0043252436, 0.033330437, 1.2642441, -0.023735465, -0.008174706, 0.010468348, -0.0033996433, -0.014712247], "split_indices": [140, 40, 140, 140, 143, 0, 1, 139, 0, 0, 0, 140, 0, 138, 69, 143, 0, 0, 93, 124, 59, 0, 0, 143, 106, 0, 0, 0, 0, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1492.0, 580.0, 1296.0, 196.0, 130.0, 450.0, 1191.0, 105.0, 107.0, 89.0, 319.0, 131.0, 758.0, 433.0, 210.0, 109.0, 157.0, 601.0, 199.0, 234.0, 108.0, 102.0, 405.0, 196.0, 110.0, 89.0, 109.0, 125.0, 302.0, 103.0, 99.0, 97.0, 154.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0003260154, -0.024354972, 0.17071977, -0.055452827, 0.029899843, 0.026061926, 0.004419455, -0.0435185, -0.01915301, 0.08786956, -0.052027825, -0.060317714, 0.00865354, 0.18584523, -0.015841788, -0.11909533, 0.0074258833, -0.04180737, -0.020875987, 0.03358641, 0.0046391073, -0.016519768, -0.007246908, -0.06811266, 0.006854247, -0.09004794, 0.0046688817, -0.11442232, 0.0037240442, -0.08701992, -0.020099446, -0.0020631612, -0.011816504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1, -1, -1, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [8.642275, 3.0285392, 2.9573822, 1.8529732, 3.1060576, 0.0, 0.0, 2.2918587, 0.0, 9.241858, 2.2952976, 2.5526273, 0.0, 5.7322845, 0.0, 0.3804748, 0.0, 2.3976994, 0.0, 0.0, 0.0, 0.0, 0.0, 1.679642, 0.0, 1.737442, 0.0, 1.1149731, 0.0, 0.73815846, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 23, 23, 25, 25, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1, -1, -1, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [1.0129824, 0.56020635, 1.1919701, 0.58333963, 1.0, 0.026061926, 0.004419455, 0.511963, -0.01915301, 0.9706581, 1.0, 1.3322752, 0.00865354, 0.6633684, -0.015841788, 0.65398246, 0.0074258833, 1.0, -0.020875987, 0.03358641, 0.0046391073, -0.016519768, -0.007246908, 0.36748597, 0.006854247, 1.0, 0.0046688817, 0.31974006, 0.0037240442, 1.0, -0.020099446, -0.0020631612, -0.011816504], "split_indices": [139, 143, 143, 142, 106, 0, 0, 139, 0, 143, 97, 138, 0, 141, 0, 139, 0, 62, 0, 0, 0, 0, 0, 139, 0, 71, 0, 140, 0, 53, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1795.0, 260.0, 1141.0, 654.0, 152.0, 108.0, 1049.0, 92.0, 383.0, 271.0, 929.0, 120.0, 274.0, 109.0, 177.0, 94.0, 826.0, 103.0, 132.0, 142.0, 89.0, 88.0, 667.0, 159.0, 560.0, 107.0, 470.0, 90.0, 357.0, 113.0, 114.0, 243.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003686545, -0.034806665, 0.088335976, -0.014480084, -0.025967102, -0.005244701, 0.12784424, 0.008129346, -0.04140965, 0.1661792, -0.0031754305, -0.11233155, -0.017796343, 0.08645422, 0.045608815, -0.06833983, -0.020457221, 0.030957174, -0.058579545, 0.02151613, -0.0025694037, -0.00015755689, -0.014623145, -0.050169814, 0.09989609, -0.015395206, -0.010893274, 0.004005007, -0.013744778, -0.0013462481, 0.023545895, -0.11517694, 0.014678359, -0.01927871, -0.0031596709], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, 11, 13, -1, 15, 17, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [6.754722, 1.3855278, 3.6042356, 0.0, 2.18476, 0.0, 3.0958118, 0.0, 1.9309294, 9.430098, 0.0, 1.1686523, 1.7199008, 4.618969, 0.0, 1.014073, 0.0, 2.2035658, 2.1420887, 0.0, 0.0, 0.0, 0.0, 1.4252319, 3.2732167, 0.0, 5.16314, 0.0, 0.0, 0.0, 0.0, 1.2259812, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 23, 23, 24, 24, 26, 26, 31, 31], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, 12, 14, -1, 16, 18, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.4222897, 0.09421818, -0.5, -0.014480084, 1.2046952, -0.005244701, 1.145815, 0.008129346, 0.28457788, 1.019717, -0.0031754305, 0.3139467, 1.3288147, 0.68592155, 0.045608815, 0.42307693, -0.020457221, 1.0, 0.49791622, 0.02151613, -0.0025694037, -0.00015755689, -0.014623145, 0.39349177, 1.0, -0.015395206, 1.0, 0.004005007, -0.013744778, -0.0013462481, 0.023545895, -0.115384616, 0.014678359, -0.01927871, -0.0031596709], "split_indices": [138, 143, 1, 0, 138, 0, 143, 0, 139, 141, 0, 140, 138, 141, 0, 1, 0, 13, 139, 0, 0, 0, 0, 141, 108, 0, 97, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1425.0, 648.0, 106.0, 1319.0, 142.0, 506.0, 166.0, 1153.0, 408.0, 98.0, 288.0, 865.0, 320.0, 88.0, 195.0, 93.0, 394.0, 471.0, 149.0, 171.0, 105.0, 90.0, 181.0, 213.0, 157.0, 314.0, 89.0, 92.0, 116.0, 97.0, 189.0, 125.0, 98.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.008532596, -0.021063074, 0.02493864, -0.044792928, 0.04625598, -0.024501989, -0.021576043, 0.09686263, -0.012781342, -0.060880546, 0.054156724, -0.0080917915, 0.15553021, -0.02075713, -0.09388, 0.1554936, -0.0613568, 0.0023363722, 0.23204765, -0.09644975, 0.011836801, -0.002873816, -0.11789877, 0.028619993, 0.0014922626, 0.0014946013, -0.015051968, 0.034745883, 0.010649039, -0.005599495, -0.017202461, -0.15774003, -0.003583255, -0.008437663, -0.020801703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, -1, 17, 19, 21, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [6.696389, 3.1566029, 0.0, 5.068342, 4.53667, 3.7371063, 0.0, 4.1615524, 0.0, 1.1823769, 4.834488, 0.0, 3.0339122, 4.243891, 0.7666669, 4.0421724, 1.3130527, 0.0, 2.753232, 0.7979715, 0.0, 0.0, 1.1705241, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.88892555, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 22, 22, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, -1, 18, 20, 22, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.3494815, 0.7153027, 0.02493864, 0.68592155, 1.145815, 0.4400628, -0.021576043, 0.0, -0.012781342, 1.2488443, 1.0, -0.0080917915, 1.0, 0.2243912, 0.0, 1.0, 1.0, 0.0023363722, 0.8379644, 0.16433378, 0.011836801, -0.002873816, 1.0, 0.028619993, 0.0014922626, 0.0014946013, -0.015051968, 0.034745883, 0.010649039, -0.005599495, -0.017202461, 0.32404217, -0.003583255, -0.008437663, -0.020801703], "split_indices": [140, 143, 0, 141, 143, 140, 0, 0, 0, 138, 97, 0, 15, 142, 0, 111, 137, 0, 142, 143, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1976.0, 96.0, 1461.0, 515.0, 1306.0, 155.0, 399.0, 116.0, 893.0, 413.0, 99.0, 300.0, 403.0, 490.0, 220.0, 193.0, 110.0, 190.0, 261.0, 142.0, 132.0, 358.0, 114.0, 106.0, 104.0, 89.0, 99.0, 91.0, 170.0, 91.0, 241.0, 117.0, 98.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00063007197, -0.03027376, 0.07513585, -0.017520566, -0.019497212, 0.12165544, -0.003943524, -0.038772993, 0.10677392, 0.043630224, 0.037658926, -0.053535156, 0.010927038, 0.02647533, -0.0027582373, 0.10989492, -0.011788993, -0.03940729, -0.018967636, -0.006049798, 0.023053808, -0.056260083, 0.011849598, -0.07675812, 0.007298173, 0.0020573903, -0.1081658, 0.007008556, -0.0058682016, -0.070334174, -0.17796211, -0.011513887, 0.003493508, -0.023685528, -0.011843565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, -1, -1, 19, -1, 21, -1, -1, -1, 23, -1, 25, -1, 27, 29, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [4.7592964, 3.0687275, 3.229859, 3.5819545, 0.0, 8.573135, 0.0, 2.53074, 4.202655, 3.5320177, 0.0, 2.0253236, 0.0, 0.0, 0.0, 4.810277, 0.0, 2.5386992, 0.0, 0.0, 0.0, 2.283613, 0.0, 1.8417072, 0.0, 0.8759821, 1.4047499, 0.0, 0.0, 1.6272124, 0.65556526, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 17, 17, 21, 21, 23, 23, 25, 25, 26, 26, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, -1, -1, 20, -1, 22, -1, -1, -1, 24, -1, 26, -1, 28, 30, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.7153027, 0.6725496, 1.145815, 0.56020635, -0.019497212, 1.019717, -0.003943524, 3.0384614, 1.0, 0.8293257, 0.037658926, 0.5957538, 0.010927038, 0.02647533, -0.0027582373, 1.0, -0.011788993, 0.4940717, -0.018967636, -0.006049798, 0.023053808, 0.43847784, 0.011849598, 0.0, 0.007298173, 1.0, 0.27044263, 0.007008556, -0.0058682016, 0.25908482, 0.34371495, -0.011513887, 0.003493508, -0.023685528, -0.011843565], "split_indices": [143, 143, 143, 143, 0, 141, 0, 1, 111, 142, 0, 141, 0, 0, 0, 39, 0, 141, 0, 0, 0, 142, 0, 0, 0, 59, 142, 0, 0, 143, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1461.0, 606.0, 1356.0, 105.0, 431.0, 175.0, 1158.0, 198.0, 330.0, 101.0, 1053.0, 105.0, 91.0, 107.0, 234.0, 96.0, 954.0, 99.0, 97.0, 137.0, 862.0, 92.0, 744.0, 118.0, 212.0, 532.0, 100.0, 112.0, 345.0, 187.0, 242.0, 103.0, 94.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013541866, -0.030883173, 0.0739118, -0.016937159, -0.122670695, 0.115040965, -0.009166853, -0.026277702, 0.01076034, -0.0066196844, -0.017688556, 0.059073478, 0.03401145, 0.0026276757, -0.12214375, 0.117331654, -0.009017839, -0.0336779, 0.12421711, -0.00059368386, -0.026031101, -0.00025451244, 0.031992342, 0.021493083, -0.07404692, 0.031091189, -0.006965822, 0.013579718, -0.0065915906, -0.120523155, -0.020757383, -0.007277779, -0.0180574, -0.008134245, 0.0037953889], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [4.5984106, 1.9021837, 3.970335, 1.5006266, 0.60009646, 5.8827033, 0.0, 3.3252537, 0.0, 0.0, 0.0, 3.2519832, 0.0, 4.0700526, 4.463565, 6.5329485, 0.0, 1.581311, 7.6734495, 0.0, 0.0, 0.0, 0.0, 2.9973617, 1.0154455, 0.0, 0.0, 0.0, 0.0, 0.62790465, 0.67939216, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 23, 23, 24, 24, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.70048, 1.0, 1.0, 0.77544206, 0.2285557, 1.3188831, -0.009166853, 0.51408464, 0.01076034, -0.0066196844, -0.017688556, 1.5493003, 0.03401145, 0.4400628, 1.0, 0.7987627, -0.009017839, 1.2488443, 0.51887953, -0.00059368386, -0.026031101, -0.00025451244, 0.031992342, 1.0, 0.36233214, 0.031091189, -0.006965822, 0.013579718, -0.0065915906, 0.2788484, 1.0, -0.007277779, -0.0180574, -0.008134245, 0.0037953889], "split_indices": [140, 40, 119, 142, 143, 139, 0, 141, 0, 0, 0, 138, 0, 140, 124, 139, 0, 138, 140, 0, 0, 0, 0, 23, 139, 0, 0, 0, 0, 140, 108, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1486.0, 583.0, 1290.0, 196.0, 467.0, 116.0, 1200.0, 90.0, 96.0, 100.0, 374.0, 93.0, 922.0, 278.0, 269.0, 105.0, 710.0, 212.0, 151.0, 127.0, 169.0, 100.0, 300.0, 410.0, 108.0, 104.0, 130.0, 170.0, 219.0, 191.0, 122.0, 97.0, 94.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0045117843, -0.01236851, 0.11266625, 3.5571e-05, -0.024721913, 0.029846324, 0.018768832, -0.020779053, 0.15835287, -0.00765633, 0.010814275, -0.05457474, 0.021617327, 0.0033184427, 0.026480451, -0.035599817, -0.019303529, 0.014714335, -0.01889899, -0.01812462, -0.014196324, 0.046465565, -0.10495234, -0.046292577, 0.009985754, -0.033229407, 0.021811632, -0.017834736, -0.00044330402, -0.004875884, -0.11568178, -0.009280106, 0.0032747765, 0.038959626, -0.01056524, -0.0048258994, -0.01732857, -0.004589738, 0.0114468], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, -1, -1, 21, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, 33, 35, -1, -1, 37, -1, -1, -1, -1, -1], "loss_changes": [3.786459, 5.2261133, 4.884839, 5.6152167, 0.0, 0.0, 1.5847583, 2.1578193, 2.638228, 0.0, 0.0, 2.2016585, 3.3973498, 0.0, 0.0, 1.3698773, 0.0, 0.0, 2.840544, 2.1036594, 0.0, 3.926075, 1.60832, 1.4685478, 0.0, 0.7703526, 0.0, 0.0, 0.0, 1.413629, 0.7418082, 0.0, 0.0, 1.4288538, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25, 29, 29, 30, 30, 33, 33], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, -1, -1, 22, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, 34, 36, -1, -1, 38, -1, -1, -1, -1, -1], "split_conditions": [0.9867292, 1.0230856, 1.0199403, 0.7995418, -0.024721913, 0.029846324, 1.2005786, 1.0, 0.645011, -0.00765633, 0.010814275, 0.6276796, 0.0, 0.0033184427, 0.026480451, 0.50344044, -0.019303529, 0.014714335, 0.49558082, 0.39487335, -0.014196324, 0.38830712, 0.6472997, 1.0, 0.009985754, 1.0, 0.021811632, -0.017834736, -0.00044330402, 0.33421066, 0.15510692, -0.009280106, 0.0032747765, 0.42307693, -0.01056524, -0.0048258994, -0.01732857, -0.004589738, 0.0114468], "split_indices": [142, 140, 143, 143, 0, 0, 140, 39, 140, 0, 0, 143, 0, 0, 0, 141, 0, 0, 143, 141, 0, 142, 143, 80, 0, 12, 0, 0, 0, 141, 142, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1794.0, 280.0, 1704.0, 90.0, 94.0, 186.0, 1506.0, 198.0, 90.0, 96.0, 838.0, 668.0, 91.0, 107.0, 737.0, 101.0, 163.0, 505.0, 633.0, 104.0, 287.0, 218.0, 511.0, 122.0, 196.0, 91.0, 126.0, 92.0, 320.0, 191.0, 103.0, 93.0, 223.0, 97.0, 88.0, 103.0, 105.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015264894, -0.015436006, 0.13712473, -0.01755813, -0.0049660155, 0.026611418, -0.00063578924, -0.018161865, 0.021629771, 0.070924915, -0.038134124, 0.016361367, -0.002677401, -0.012133877, -0.083358794, -0.06342689, 0.0215025, -0.11969728, 0.0038088222, -0.031848196, -0.0151496325, 0.015228978, -0.024357967, -0.005097074, -0.1548787, -0.010242143, 0.0016463659, -0.07930179, 0.03144883, -0.020148696, -0.008855156, 0.004828056, -0.020688416, 0.015504189, -0.008102083], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, -1, 11, 13, -1, -1, 15, 17, 19, 21, 23, -1, 25, -1, -1, 27, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [3.9767137, 3.142172, 3.4794576, 0.0, 5.135862, 0.0, 0.0, 2.9535787, 0.0, 2.7528996, 1.5944561, 0.0, 0.0, 1.4854932, 2.184535, 0.9483607, 3.1189427, 0.9212184, 0.0, 0.85579073, 0.0, 0.0, 1.1805022, 0.0, 0.7790303, 0.0, 0.0, 3.1577888, 2.6549897, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 22, 22, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, -1, 12, 14, -1, -1, 16, 18, 20, 22, 24, -1, 26, -1, -1, 28, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 0.9907159, -0.01755813, 1.1810409, 0.026611418, -0.00063578924, -0.30769232, 0.021629771, 1.0, 1.0, 0.016361367, -0.002677401, 0.33038563, 1.0, 0.30228853, 0.40108702, 0.115384616, 0.0038088222, 0.34615386, -0.0151496325, 0.015228978, 0.59568846, -0.005097074, 1.0, -0.010242143, 0.0016463659, 1.3242663, 1.4581172, -0.020148696, -0.008855156, 0.004828056, -0.020688416, 0.015504189, -0.008102083], "split_indices": [125, 1, 141, 0, 139, 0, 0, 1, 0, 126, 109, 0, 0, 140, 42, 139, 141, 1, 0, 1, 0, 0, 139, 0, 122, 0, 0, 138, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1874.0, 188.0, 115.0, 1759.0, 99.0, 89.0, 1660.0, 99.0, 304.0, 1356.0, 156.0, 148.0, 861.0, 495.0, 341.0, 520.0, 381.0, 114.0, 251.0, 90.0, 135.0, 385.0, 129.0, 252.0, 102.0, 149.0, 194.0, 191.0, 148.0, 104.0, 97.0, 97.0, 91.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002577428, -0.008236254, 0.016053364, 0.0020864801, -0.01597183, -0.010534625, 0.022759216, -0.022550814, 0.06622701, -0.011875846, -0.012899104, -0.007269831, 0.021360869, -0.023496203, 0.01317427, -0.010878493, -0.016991157, -0.029813329, 0.020626778, 0.0079216575, -0.043855928, -0.014261822, -0.0023829814], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, -1, 21, -1, -1], "loss_changes": [2.6594007, 3.0883255, 0.0, 5.262497, 0.0, 1.6150913, 0.0, 1.720276, 4.8525863, 2.2964044, 0.0, 0.0, 0.0, 2.3517735, 0.0, 4.818829, 0.0, 1.6504858, 0.0, 0.0, 1.8888222, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, -1, 22, -1, -1], "split_conditions": [1.3494815, 1.0738561, 0.016053364, 0.9291086, -0.01597183, 0.76225835, 0.022759216, 0.6579766, 0.7991742, 0.59568846, -0.012899104, -0.007269831, 0.021360869, 0.54411215, 0.01317427, 0.49791622, -0.016991157, 0.10037874, 0.020626778, 0.0079216575, 0.18234658, -0.014261822, -0.0023829814], "split_indices": [140, 140, 0, 140, 0, 139, 0, 139, 142, 139, 0, 0, 0, 139, 0, 139, 0, 139, 0, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1975.0, 98.0, 1849.0, 126.0, 1751.0, 98.0, 1514.0, 237.0, 1376.0, 138.0, 122.0, 115.0, 1273.0, 103.0, 1172.0, 101.0, 1078.0, 94.0, 123.0, 955.0, 161.0, 794.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012956496, -0.010463409, 0.020070406, -0.04533061, 0.027100088, -0.022007613, -0.028266132, -0.0046576373, 0.020068001, -0.044251174, 0.016809454, 0.02014242, -0.019497115, -0.0157663, -0.01946599, -0.016800066, 0.017497493, -0.04816732, 0.009210291, 0.0438767, -0.015489203, -0.11813225, -0.01129121, -0.015010437, 0.022053814, -0.008047031, -0.015703576, 0.030507777, -0.013433995, 0.010648639, -0.06616701, 0.009525183, -0.0076090964, -0.0018347785, -0.012581246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [3.7963634, 2.568389, 0.0, 5.629363, 5.203804, 3.9156358, 0.0, 3.7663894, 0.0, 3.5517452, 0.0, 4.0382485, 0.0, 2.4360654, 0.0, 4.7760153, 0.0, 1.3828981, 0.0, 4.1196227, 0.0, 0.27105784, 1.8053031, 1.8459622, 0.0, 0.0, 0.0, 1.808228, 0.0, 0.0, 0.5961091, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.3865964, 1.0, 0.020070406, 0.91750056, 0.9291086, 0.73416793, -0.028266132, 0.77610457, 0.020068001, 0.55845666, 0.016809454, 0.6731954, -0.019497115, 0.41348258, -0.01946599, 0.4980591, 0.017497493, 1.0, 0.009210291, 0.4080126, -0.015489203, 1.0, 1.2743453, 1.2291423, 0.022053814, -0.008047031, -0.015703576, 1.0, -0.013433995, 0.010648639, 0.34615386, 0.009525183, -0.0076090964, -0.0018347785, -0.012581246], "split_indices": [140, 39, 0, 140, 140, 142, 0, 140, 0, 139, 0, 139, 0, 139, 0, 140, 0, 26, 0, 140, 0, 12, 138, 138, 0, 0, 0, 80, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1961.0, 89.0, 1017.0, 944.0, 926.0, 91.0, 798.0, 146.0, 829.0, 97.0, 706.0, 92.0, 697.0, 132.0, 570.0, 136.0, 536.0, 161.0, 396.0, 174.0, 185.0, 351.0, 297.0, 99.0, 94.0, 91.0, 262.0, 89.0, 88.0, 209.0, 163.0, 99.0, 116.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028991387, 0.008724029, -0.102610864, -0.0029123256, 0.116877496, -0.0022720944, -0.01689601, 0.007447192, -0.01560573, 0.027572623, -0.003848004, -0.0038971694, 0.019362079, 0.013824949, -0.01906717, -0.016770499, 0.1098346, -0.002225311, -0.015722243, 0.18519336, -0.0009782454, -0.020249506, 0.012504116, 0.0013455331, 0.03604008, -0.037371863, 0.011375151, -0.00093637494, -0.01644856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [2.397901, 2.3320231, 1.1449366, 2.6542284, 4.442104, 0.0, 0.0, 3.309537, 0.0, 0.0, 0.0, 4.8889303, 0.0, 3.9626315, 0.0, 2.089886, 2.9386272, 2.1264224, 0.0, 6.017955, 0.0, 1.8630632, 0.0, 0.0, 0.0, 2.563355, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.07692308, 1.1314721, 0.9907159, -0.0022720944, -0.01689601, 0.9867292, -0.01560573, 0.027572623, -0.003848004, 0.79864544, 0.019362079, 0.6252839, -0.01906717, 0.5957538, 0.34615386, 0.48462236, -0.015722243, 1.0, -0.0009782454, 0.48910156, 0.012504116, 0.0013455331, 0.03604008, 0.40108702, 0.011375151, -0.00093637494, -0.01644856], "split_indices": [40, 125, 1, 143, 141, 0, 0, 142, 0, 0, 0, 141, 0, 143, 0, 141, 1, 141, 0, 111, 0, 139, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1853.0, 216.0, 1673.0, 180.0, 98.0, 118.0, 1567.0, 106.0, 89.0, 91.0, 1477.0, 90.0, 1349.0, 128.0, 1023.0, 326.0, 927.0, 96.0, 200.0, 126.0, 812.0, 115.0, 101.0, 99.0, 720.0, 92.0, 590.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.004803395, -0.0051324097, 0.013526189, 0.0059595425, -0.095778614, 0.010003614, -0.003429942, -0.0040466483, -0.016612118, -0.07884187, 0.013374446, -0.002502647, -0.016100643, 0.024277497, -0.011015816, 0.0050027347, 0.018073434, 0.021930523, -0.014718457, 0.00085049716, 0.019408405, 0.040933624, -0.06574127, 0.00035702373, 0.016822241, -0.0007387233, -0.020169372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, 13, -1, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [2.6740818, 1.9274353, 0.0, 1.5087293, 0.81317616, 0.0, 1.9680417, 0.0, 0.0, 1.2513458, 1.7105404, 0.0, 0.0, 3.5192847, 0.0, 2.676666, 0.0, 3.3931153, 0.0, 2.2234488, 0.0, 2.4730887, 2.4831452, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, 14, -1, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.157584, 1.0, 0.013526189, 1.2046952, 0.36011788, 0.010003614, 0.28890142, -0.0040466483, -0.016612118, 0.28313357, 1.03513, -0.002502647, -0.016100643, 0.9291086, -0.011015816, 0.8379644, 0.018073434, 0.71985584, -0.014718457, 0.51408464, 0.019408405, 0.457961, 1.433935, 0.00035702373, 0.016822241, -0.0007387233, -0.020169372], "split_indices": [142, 40, 0, 138, 142, 0, 139, 0, 0, 140, 141, 0, 0, 140, 0, 142, 0, 142, 0, 141, 0, 141, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1917.0, 146.0, 1708.0, 209.0, 155.0, 1553.0, 117.0, 92.0, 283.0, 1270.0, 171.0, 112.0, 1167.0, 103.0, 1039.0, 128.0, 935.0, 104.0, 833.0, 102.0, 520.0, 313.0, 402.0, 118.0, 219.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.00058871775, -0.011406453, 0.08238222, 0.0011177936, -0.013944939, 0.019371307, 0.0005797907, -0.017026363, 0.12586974, -0.0007731764, -0.07491866, -0.0015357257, 0.028278867, -0.017620709, 0.08567397, 0.0011632092, -0.016656065, -0.032262214, 0.012049746, 0.026483849, -0.008396047, -0.059858978, 0.05332774, 0.0149919465, -0.12718053, -0.0030672434, 0.014496433, -0.010306131, 0.06783485, -0.06791285, -0.023625296, 0.015569471, -0.0012037733, 0.0034334536, -0.016038008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, -1, -1, 23, 25, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [2.0328953, 2.8977804, 2.2594416, 3.7257526, 0.0, 0.0, 0.0, 1.3521225, 4.631689, 1.6341041, 2.4984808, 0.0, 0.0, 1.8989, 5.561824, 0.0, 0.0, 2.005343, 0.0, 0.0, 0.0, 3.23509, 1.5933807, 1.896436, 2.1849904, 0.0, 0.0, 0.0, 1.473695, 2.070543, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 21, 21, 22, 22, 23, 23, 24, 24, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, -1, -1, 24, 26, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0129824, 0.8764639, 1.2172625, 0.69042635, -0.013944939, 0.019371307, 0.0005797907, 0.53437847, 0.7014005, 0.48910156, 1.0, -0.0015357257, 0.028278867, 0.457961, 0.46890086, 0.0011632092, -0.016656065, 1.0, 0.012049746, 0.026483849, -0.008396047, 0.25308752, 0.2934114, 1.0, 1.2804871, -0.0030672434, 0.014496433, -0.010306131, 1.0, 1.0, -0.023625296, 0.015569471, -0.0012037733, 0.0034334536, -0.016038008], "split_indices": [139, 142, 139, 142, 0, 0, 0, 140, 139, 139, 124, 0, 0, 141, 141, 0, 0, 74, 0, 0, 0, 140, 140, 69, 138, 0, 0, 0, 106, 126, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1807.0, 265.0, 1646.0, 161.0, 108.0, 157.0, 1437.0, 209.0, 1122.0, 315.0, 110.0, 99.0, 939.0, 183.0, 162.0, 153.0, 849.0, 90.0, 89.0, 94.0, 642.0, 207.0, 304.0, 338.0, 108.0, 99.0, 94.0, 210.0, 219.0, 119.0, 100.0, 110.0, 104.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020192857, -0.0079925135, 0.0116660865, 0.0056453343, -0.015498729, -0.0056237923, 0.018756127, -0.019055197, 0.076601215, -0.007025669, -0.020395845, -0.004106838, 0.024470067, -0.024010945, 0.05167588, 0.007901727, -0.03714951, 0.1264542, -0.011519784, -0.09954141, -0.009494198, -0.0015965345, 0.023356317, -0.0033951707, -0.016290015, -0.05079579, 0.049684193, 0.0071286145, -0.009795381, 0.014591237, -0.0074507683], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, -1, 19, 21, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [1.4645942, 3.9432304, 0.0, 3.690061, 0.0, 1.8719534, 0.0, 3.2408028, 4.7076864, 1.3639808, 0.0, 0.0, 0.0, 1.4362149, 3.8309112, 0.0, 1.6236646, 3.2339356, 0.0, 1.2009912, 1.5935936, 0.0, 0.0, 0.0, 0.0, 2.2107425, 3.2028031, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 20, 20, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, -1, 20, 22, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.3494815, 1.0093737, 0.0116660865, 1.0, -0.015498729, 0.76225835, 0.018756127, 0.74770635, 1.0, 0.5272047, -0.020395845, -0.004106838, 0.024470067, 0.10037874, 0.1923077, 0.007901727, 0.23054773, 0.59329486, -0.011519784, 1.0, 0.53846157, -0.0015965345, 0.023356317, -0.0033951707, -0.016290015, 0.31376776, 1.0, 0.0071286145, -0.009795381, 0.014591237, -0.0074507683], "split_indices": [140, 140, 0, 125, 0, 139, 0, 141, 53, 142, 0, 0, 0, 139, 1, 0, 139, 143, 0, 23, 1, 0, 0, 0, 0, 139, 12, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1967.0, 99.0, 1800.0, 167.0, 1695.0, 105.0, 1457.0, 238.0, 1368.0, 89.0, 140.0, 98.0, 1061.0, 307.0, 120.0, 941.0, 212.0, 95.0, 289.0, 652.0, 91.0, 121.0, 142.0, 147.0, 384.0, 268.0, 107.0, 277.0, 151.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.005963227, 0.003352395, -0.08574982, 0.00986412, -0.012251706, -0.00351906, -0.015929047, -0.0023077817, 0.020356124, 0.009479458, -0.012790002, -0.0071809827, 0.019610329, 0.0038052027, -0.016027883, -0.011834246, 0.017938018, 0.08954343, -0.032933157, -0.0065980116, 0.024355708, -0.014575058, -0.018047161, 0.0042503793, -0.0055345674], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, -1, -1, 23, -1, -1, -1], "loss_changes": [1.5355788, 1.5163101, 0.8031213, 4.1471286, 0.0, 0.0, 0.0, 2.450039, 0.0, 4.7042737, 0.0, 2.3362446, 0.0, 3.5586815, 0.0, 2.5453606, 0.0, 4.910314, 2.6678972, 0.0, 0.0, 2.0385745, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, -1, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.4310507, 1.2743453, 1.1155719, -0.012251706, -0.00351906, -0.015929047, 0.9504357, 0.020356124, 0.7995418, -0.012790002, 0.8961227, 0.019610329, 0.76225835, -0.016027883, 1.0, 0.017938018, 1.0, 0.66575265, -0.0065980116, 0.024355708, 1.0, -0.018047161, 0.0042503793, -0.0055345674], "split_indices": [40, 143, 138, 142, 0, 0, 0, 143, 0, 143, 0, 140, 0, 139, 0, 89, 0, 97, 143, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1850.0, 216.0, 1759.0, 91.0, 128.0, 88.0, 1655.0, 104.0, 1513.0, 142.0, 1389.0, 124.0, 1296.0, 93.0, 1190.0, 106.0, 205.0, 985.0, 102.0, 103.0, 876.0, 109.0, 365.0, 511.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034818035, -0.011255944, 0.010084388, -0.002970339, -0.016357712, -0.013276118, 0.015880201, -0.005798617, -0.015118499, -0.016029464, 0.014061828, 0.0037941218, -0.07138683, -0.012351268, 0.014724522, -0.11062826, 0.006186151, -0.026222365, 0.011166456, -0.04700701, -0.024967676, -0.0036624558, -0.09930375, -0.014790093, 0.0046549137, -0.0029345653, 0.0131758, -0.0038053624, -0.016113168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6723691, 2.4219177, 0.0, 3.0342853, 0.0, 1.7644064, 0.0, 2.4312031, 0.0, 1.6647282, 0.0, 2.587055, 2.0915422, 1.7271163, 0.0, 2.7335486, 0.0, 1.4887842, 0.0, 2.0011203, 0.0, 2.3998404, 0.8066237, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.157584, 1.1454756, 0.010084388, 0.9291086, -0.016357712, 0.8913477, 0.015880201, 0.778775, -0.015118499, 0.51408464, 0.014061828, 0.5272047, 0.708655, 3.0384614, 0.014724522, 1.4275644, 0.006186151, 1.3154383, 0.011166456, 0.6110355, -0.024967676, 0.3801079, 1.0, -0.014790093, 0.0046549137, -0.0029345653, 0.0131758, -0.0038053624, -0.016113168], "split_indices": [142, 140, 0, 140, 0, 142, 0, 142, 0, 141, 0, 142, 140, 1, 0, 138, 0, 138, 0, 141, 0, 141, 111, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1919.0, 143.0, 1820.0, 99.0, 1711.0, 109.0, 1623.0, 88.0, 1517.0, 106.0, 1117.0, 400.0, 1004.0, 113.0, 309.0, 91.0, 903.0, 101.0, 212.0, 97.0, 690.0, 213.0, 102.0, 110.0, 580.0, 110.0, 107.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015553201, -0.011651634, 0.10043915, 0.00084460835, -0.02088022, 0.023555895, -0.0040619955, -0.010844832, 0.022387384, 0.0028454612, -0.018252555, -0.01108017, 0.017559534, -0.04669001, 0.03400693, -0.01649942, -0.14097753, 0.0051450473, 0.020654386, 0.009208829, -0.037832964, -0.004195712, -0.022411728, -0.025618916, 0.016242152, 1.6207741e-05, -0.10944534, 0.008170091, -0.05821463, -0.0040119323, 0.009910083, -0.01336052, -0.008528545, -0.011937125, -0.0017248469], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [2.126471, 4.6291823, 3.5451384, 4.606724, 0.0, 0.0, 0.0, 3.9462543, 0.0, 3.740788, 0.0, 2.3103786, 0.0, 2.2886624, 3.1621346, 1.4107854, 1.6053431, 2.6321163, 0.0, 0.0, 1.379629, 0.0, 0.0, 1.5916657, 0.0, 1.3242792, 0.10273051, 0.0, 0.87436736, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 23, 23, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1314721, 0.9907159, 0.96802163, -0.02088022, 0.023555895, -0.0040619955, 0.79864544, 0.022387384, 0.7995418, -0.018252555, 1.0, 0.017559534, 0.48037297, 0.7014005, -0.26923078, 0.54411215, 1.0, 0.020654386, 0.009208829, 0.33421066, -0.004195712, -0.022411728, 0.23177461, 0.016242152, 0.24481331, 0.39616552, 0.008170091, 1.0, -0.0040119323, 0.009910083, -0.01336052, -0.008528545, -0.011937125, -0.0017248469], "split_indices": [125, 143, 141, 141, 0, 0, 0, 141, 0, 143, 0, 39, 0, 142, 139, 1, 139, 105, 0, 0, 141, 0, 0, 143, 0, 142, 141, 0, 115, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1879.0, 186.0, 1767.0, 112.0, 95.0, 91.0, 1679.0, 88.0, 1555.0, 124.0, 1439.0, 116.0, 804.0, 635.0, 609.0, 195.0, 544.0, 91.0, 100.0, 509.0, 89.0, 106.0, 455.0, 89.0, 333.0, 176.0, 106.0, 349.0, 237.0, 96.0, 88.0, 88.0, 140.0, 209.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008445412, -0.009811632, 0.011615755, -0.115080155, 0.002398775, 0.0012993878, -0.025967991, 0.010865579, -0.01366123, 0.0032395367, 0.012973747, 0.013612475, -0.00760097, 0.023440642, -0.012256219, 0.00039739956, 0.016459694, 0.03838023, -0.033558976, -0.015679488, 0.01679059, -0.0070238407, -0.017577535, -0.011443966, 0.0049548144, -0.0037785682, 0.012346041], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [2.151844, 2.4486332, 0.0, 3.666857, 2.0091038, 0.0, 0.0, 1.4585936, 0.0, 1.2429355, 0.0, 1.7893702, 0.0, 4.056115, 0.0, 1.382622, 0.0, 3.5430732, 2.135931, 2.2997558, 0.0, 1.9146472, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.157584, -0.5, 0.011615755, 0.67420596, 1.0, 0.0012993878, -0.025967991, 5.0, -0.01366123, 2.0, 0.012973747, 0.9824233, -0.00760097, 1.4328352, -0.012256219, 1.0, 0.016459694, 0.51852506, 0.5288287, 1.0, 0.01679059, 1.0, -0.017577535, -0.011443966, 0.0049548144, -0.0037785682, 0.012346041], "split_indices": [142, 1, 0, 142, 84, 0, 0, 0, 0, 0, 0, 140, 0, 138, 0, 106, 0, 143, 140, 97, 0, 121, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 1905.0, 146.0, 198.0, 1707.0, 105.0, 93.0, 1609.0, 98.0, 1512.0, 97.0, 1337.0, 175.0, 1247.0, 90.0, 1072.0, 175.0, 506.0, 566.0, 357.0, 149.0, 477.0, 89.0, 142.0, 215.0, 386.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0005720435, -0.005838872, 0.01289199, -0.012720485, 0.010905946, -0.004280734, -0.01315576, -0.022125183, 0.0853107, -0.012831166, -0.016660487, 0.0042918054, 0.023692027, -0.020965684, 0.010406822, -0.012639453, 0.013089423, 0.0007029185, -0.14671275, -0.015599731, 0.08545819, -0.005703341, -0.024436355, 0.0066444343, -0.095122635, -0.003547702, 0.020639343, -0.002140288, 0.01228693, -0.015541117, -0.0039471663], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, -1, 17, -1, -1, -1, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.7123039, 1.5671389, 0.0, 1.8755267, 0.0, 2.791347, 0.0, 1.9551111, 3.562139, 1.3008585, 0.0, 3.1270442, 0.0, 3.4849725, 0.0, 0.0, 0.0, 1.5074736, 1.6463637, 1.6185627, 2.5740585, 0.0, 0.0, 2.330754, 0.67102265, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, -1, 18, -1, -1, -1, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3494815, 1.0, 0.01289199, 1.0423194, 0.010905946, 0.70048, -0.01315576, 0.77860993, 1.0, 0.6731954, -0.016660487, 0.7922069, 0.023692027, 0.53437847, 0.010406822, -0.012639453, 0.013089423, 0.4400628, 0.5001129, 1.3154383, 0.4373057, -0.005703341, -0.024436355, 1.0, 1.0, -0.003547702, 0.020639343, -0.002140288, 0.01228693, -0.015541117, -0.0039471663], "split_indices": [140, 102, 0, 140, 0, 140, 0, 139, 97, 139, 0, 139, 0, 140, 0, 0, 0, 140, 139, 138, 139, 0, 0, 62, 122, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2081.0, 1982.0, 99.0, 1870.0, 112.0, 1746.0, 124.0, 1456.0, 290.0, 1368.0, 88.0, 189.0, 101.0, 1279.0, 89.0, 93.0, 96.0, 1091.0, 188.0, 915.0, 176.0, 98.0, 90.0, 715.0, 200.0, 88.0, 88.0, 576.0, 139.0, 96.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0016910557, 0.01113885, -0.010329059, -0.0010612983, 0.025788343, 0.012516445, -0.106124036, -0.0047469195, 0.017870512, -0.020655952, -0.0014988108, 0.004420247, -0.01379173, -0.0062952084, 0.012296, 0.010476052, -0.013585912, -0.008936933, 0.11070378, 0.0023244834, -0.008689314, -0.0024681909, 0.024308093, -0.0019047986, 0.009239419], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [2.0422084, 5.6864963, 0.0, 2.567727, 0.0, 4.5731473, 1.8855753, 1.7628284, 0.0, 0.0, 0.0, 1.7160506, 0.0, 2.6922855, 0.0, 2.1344543, 0.0, 0.8067879, 3.1901114, 1.5457848, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.145815, 1.1429962, -0.010329059, 1.4857019, 0.025788343, 0.7014005, 0.71855736, 0.743004, 0.017870512, -0.020655952, -0.0014988108, 0.6505282, -0.01379173, 0.53437847, 0.012296, 0.4400628, -0.013585912, 1.3495234, 0.4373057, 0.3801079, -0.008689314, -0.0024681909, 0.024308093, -0.0019047986, 0.009239419], "split_indices": [143, 139, 0, 138, 0, 139, 143, 142, 0, 0, 0, 140, 0, 140, 0, 140, 0, 138, 139, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1889.0, 170.0, 1800.0, 89.0, 1594.0, 206.0, 1444.0, 150.0, 98.0, 108.0, 1351.0, 93.0, 1239.0, 112.0, 1097.0, 142.0, 919.0, 178.0, 803.0, 116.0, 88.0, 90.0, 649.0, 154.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.003529343, -0.013897764, 0.049084418, -0.005752798, -0.014211229, 0.018224135, 0.007357812, -0.01746513, 0.010408282, -0.058293052, 0.016430443, -0.00045252117, -0.012352658, -0.01760072, 0.005789234, -0.010692277, 0.01162574, -0.02667073, 0.0073494175, -0.007082003, -0.09491867, 0.009561771, -0.028953243, -0.0027292771, -0.015442942, 0.012849443, -0.08239719, -0.003668512, 0.010903309, -0.0025602018, -0.012457594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [1.6354198, 1.5560107, 3.1670256, 1.8022901, 0.0, 0.0, 4.471798, 2.2843473, 0.0, 4.1850605, 0.0, 1.3038331, 0.0, 0.0, 0.0, 1.3492044, 0.0, 1.126998, 0.0, 1.4712412, 0.75659955, 0.0, 1.2064146, 0.0, 0.0, 1.4436179, 0.56774473, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [0.70048, 0.7987627, 0.8071234, 0.64677393, -0.014211229, 0.018224135, 0.0, 0.53437847, 0.010408282, 1.1010596, 0.016430443, 2.2692308, -0.012352658, -0.01760072, 0.005789234, 0.50696194, 0.01162574, 0.37458783, 0.0073494175, -0.30769232, 0.36233214, 0.009561771, 1.0, -0.0027292771, -0.015442942, 0.32770875, 0.1535796, -0.003668512, 0.010903309, -0.0025602018, -0.012457594], "split_indices": [140, 139, 140, 139, 0, 0, 1, 140, 0, 140, 0, 1, 0, 0, 0, 142, 0, 143, 0, 1, 139, 0, 80, 0, 0, 140, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1490.0, 570.0, 1401.0, 89.0, 136.0, 434.0, 1266.0, 135.0, 306.0, 128.0, 1091.0, 175.0, 152.0, 154.0, 1003.0, 88.0, 843.0, 160.0, 655.0, 188.0, 115.0, 540.0, 88.0, 100.0, 303.0, 237.0, 200.0, 103.0, 101.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0025858246, -0.005339519, 0.08669244, -0.10172772, 0.005502732, 0.024379987, -0.0066923685, 0.005395317, -0.024339736, -0.0059990846, 0.021344012, 0.005465039, -0.015634004, -0.009411239, 0.013885568, 0.003674354, -0.010046841, -0.013977837, 0.013979238, -0.027241433, 0.009387611, -0.012938091, -0.015105471, -0.0040327013, 0.005863034], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.3778082, 1.9741281, 4.2958946, 4.212554, 4.061035, 0.0, 0.0, 0.0, 0.0, 2.7731557, 0.0, 2.9666133, 0.0, 1.6026176, 0.0, 2.8256714, 0.0, 1.489183, 0.0, 1.6416643, 0.0, 1.6289113, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.1155719, -0.5, 1.2768567, -0.5769231, 0.96802163, 0.024379987, -0.0066923685, 0.005395317, -0.024339736, 0.8293257, 0.021344012, 0.69042635, -0.015634004, 0.59896654, 0.013885568, 0.5272047, -0.010046841, 0.52685916, 0.013979238, 0.45263797, 0.009387611, 1.0, -0.015105471, -0.0040327013, 0.005863034], "split_indices": [142, 1, 143, 1, 141, 0, 0, 0, 0, 142, 0, 142, 0, 142, 0, 142, 0, 139, 0, 142, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1889.0, 178.0, 191.0, 1698.0, 88.0, 90.0, 91.0, 100.0, 1609.0, 89.0, 1495.0, 114.0, 1345.0, 150.0, 1176.0, 169.0, 1041.0, 135.0, 927.0, 114.0, 831.0, 96.0, 601.0, 230.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0036671262, 0.009411471, -0.01128779, 0.00019804543, 0.102638826, 0.009492068, -0.01352522, 0.025708301, -0.0053560366, 0.00059498637, 0.016627839, 0.00962535, -0.013956375, -0.020856312, 0.044681463, 0.003068903, -0.019150658, -0.0065325093, 0.1419666, -0.019921556, 0.01118025, 0.049000196, -0.011056848, -0.002827555, 0.024664253, 0.006455407, -0.013578302, -0.0263143, 0.021600192, -0.003348957, 0.008842045, -0.01353088, 0.009157768], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.3824651, 1.6904, 0.0, 2.2546494, 4.2699585, 2.33792, 0.0, 0.0, 0.0, 2.007375, 0.0, 1.5921673, 0.0, 3.2540278, 3.452775, 1.7473851, 0.0, 2.622939, 4.2590413, 1.7633548, 0.0, 3.7229855, 0.0, 0.0, 0.0, 1.5388235, 0.0, 2.621314, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01128779, 1.1314721, 1.0, 0.9867292, -0.01352522, 0.025708301, -0.0053560366, 0.8293257, 0.016627839, 1.0, -0.013956375, 0.6579766, 1.0, 0.52453405, -0.019150658, 0.47044897, 1.0, 0.37458783, 0.01118025, 1.304276, -0.011056848, -0.002827555, 0.024664253, 0.26694217, -0.013578302, 1.0, 0.021600192, -0.003348957, 0.008842045, -0.01353088, 0.009157768], "split_indices": [117, 125, 0, 143, 15, 142, 0, 0, 0, 142, 0, 39, 0, 139, 71, 143, 0, 143, 69, 143, 0, 138, 0, 0, 0, 143, 0, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1968.0, 97.0, 1791.0, 177.0, 1676.0, 115.0, 89.0, 88.0, 1586.0, 90.0, 1490.0, 96.0, 797.0, 693.0, 699.0, 98.0, 454.0, 239.0, 577.0, 122.0, 296.0, 158.0, 91.0, 148.0, 470.0, 107.0, 204.0, 92.0, 316.0, 154.0, 106.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0025537324, -0.0029177775, 0.012058205, 0.005158186, -0.011055801, -0.006255387, 0.110829934, 0.0018033854, -0.012969027, 0.01642844, 0.0058563305, 0.013830758, -0.091887094, -0.00029922792, 0.01758118, -0.00081491284, -0.017562503, -0.029502321, 0.04024218, -0.003328798, -0.018604487, -0.006424037, 0.017342826, 0.0575952, -0.05695652, 0.04688263, -0.015352996, 0.016828468, -0.0007621835, -0.011777137, 0.00031343332, -0.0016864727, 0.018870262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3264593, 1.7064337, 0.0, 2.2023246, 0.0, 1.6393214, 0.49731112, 1.7432373, 0.0, 0.0, 0.0, 3.1379318, 1.234119, 1.4929415, 0.0, 0.0, 0.0, 3.0032985, 3.281674, 2.0518112, 0.0, 3.0661154, 0.0, 2.1223397, 1.2205753, 2.5946672, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3865964, 1.03513, 0.012058205, 0.8273459, -0.011055801, 1.0, 0.8509382, 0.68592155, -0.012969027, 0.01642844, 0.0058563305, 0.69640046, 0.73935336, 1.0, 0.01758118, -0.00081491284, -0.017562503, 0.50344044, 1.0, 1.0, -0.018604487, 0.46040413, 0.017342826, 1.0, 1.0, 0.31750953, -0.015352996, 0.016828468, -0.0007621835, -0.011777137, 0.00031343332, -0.0016864727, 0.018870262], "split_indices": [140, 141, 0, 141, 0, 84, 140, 141, 0, 0, 0, 140, 140, 39, 0, 0, 0, 141, 71, 23, 0, 140, 0, 137, 108, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1963.0, 91.0, 1826.0, 137.0, 1648.0, 178.0, 1547.0, 101.0, 88.0, 90.0, 1371.0, 176.0, 1261.0, 110.0, 88.0, 88.0, 733.0, 528.0, 628.0, 105.0, 391.0, 137.0, 294.0, 334.0, 287.0, 104.0, 109.0, 185.0, 166.0, 168.0, 198.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.006240943, 0.0020419487, -0.070512004, -0.009864207, 0.02361518, 0.007933704, -0.016728953, 0.014346901, -0.03983397, -0.007717368, 0.014060358, -0.017014949, -0.017365772, -0.027269326, 0.012616696, 0.006377653, -0.044036057, 0.027787391, -0.055201672, 0.008629531, -0.020458119, 0.0140822325, -0.003914117, -0.015150777, -0.008843138, -0.09372099, 0.014363177, 0.010033656, -0.07284504, 0.004083685, -0.020855917, -0.012227613, -0.0009528794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.1051593, 5.125934, 3.4369793, 1.2698022, 0.0, 0.0, 0.0, 2.6966174, 2.2896605, 2.156985, 0.0, 0.0, 1.443447, 1.1057239, 0.0, 0.0, 4.244513, 1.8307946, 2.1296182, 5.223034, 0.0, 0.0, 0.0, 0.0, 2.2500422, 3.3222618, 0.0, 0.0, 0.6353471, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.3561559, 0.6349509, 1.3430321, 0.02361518, 0.007933704, -0.016728953, 0.42784345, 1.0, 1.0, 0.014060358, -0.017014949, 0.0, 0.16433378, 0.012616696, 0.006377653, 1.5272014, 1.0, 1.2331591, 0.7035378, -0.020458119, 0.0140822325, -0.003914117, -0.015150777, 1.2630832, 1.0, 0.014363177, 0.010033656, 0.42307693, 0.004083685, -0.020855917, -0.012227613, -0.0009528794], "split_indices": [119, 139, 141, 138, 0, 0, 0, 139, 127, 121, 0, 0, 0, 143, 0, 0, 138, 23, 138, 141, 0, 0, 0, 0, 138, 13, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1839.0, 237.0, 1750.0, 89.0, 93.0, 144.0, 968.0, 782.0, 824.0, 144.0, 115.0, 667.0, 719.0, 105.0, 165.0, 502.0, 242.0, 477.0, 378.0, 124.0, 90.0, 152.0, 155.0, 322.0, 215.0, 163.0, 119.0, 203.0, 99.0, 116.0, 114.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023224417, 0.008844343, -0.008056497, -0.011387905, 0.0016770397, 0.0122985635, -0.0693315, -0.0015792662, 0.024026923, 0.0064063254, -0.015085054, 0.07746741, -0.01780567, 0.018088585, -0.00218794, -0.031906586, 0.0072388607, -0.00057859253, -0.11122864, -0.040054534, 0.11140416, -0.019653302, -0.005175929, 0.014194244, -0.13366027, 0.0033033376, 0.01821661, -0.010527163, 0.0062936335, -0.0051519363, -0.02297826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, 17, -1, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0773448, 0.0, 2.0054643, 0.0, 1.3447721, 4.906959, 2.5228171, 1.8752242, 0.0, 0.0, 0.0, 2.558299, 1.5427201, 0.0, 0.0, 2.6067657, 0.0, 3.3243096, 1.5066788, 2.8233669, 1.0869505, 0.0, 0.0, 2.0497017, 1.6106963, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, 18, -1, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [0.10037874, 0.008844343, 0.18234658, -0.011387905, 1.0, 1.3561559, 0.6349509, 1.2630832, 0.024026923, 0.0064063254, -0.015085054, 1.0, 1.2692307, 0.018088585, -0.00218794, 0.1923077, 0.0072388607, -0.115384616, 1.0, -0.30769232, 1.0, -0.019653302, -0.005175929, 1.0, 1.3923972, 0.0033033376, 0.01821661, -0.010527163, 0.0062936335, -0.0051519363, -0.02297826], "split_indices": [139, 0, 139, 0, 119, 139, 141, 138, 0, 0, 0, 111, 1, 0, 0, 1, 0, 1, 69, 1, 124, 0, 0, 15, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 123.0, 1947.0, 164.0, 1783.0, 1551.0, 232.0, 1462.0, 89.0, 88.0, 144.0, 249.0, 1213.0, 122.0, 127.0, 1049.0, 164.0, 752.0, 297.0, 556.0, 196.0, 122.0, 175.0, 352.0, 204.0, 93.0, 103.0, 102.0, 250.0, 110.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0058394345, 0.01127406, -0.010318579, -0.012173749, 0.03462665, -0.024699094, 0.009695795, 0.018848179, 0.014489467, -0.0066076657, -0.0117939515, -0.09475187, 0.04854513, -0.030577092, 0.0649119, -0.019305173, 0.0030553408, 0.11059808, -0.016180862, 0.0018106141, -0.014969801, 0.017876782, -0.0060588326, 0.012901121, 0.029597178, 0.009850681, -0.10406654, -0.028599419, 0.009474597, 0.012885426, -0.01009814, 0.00044651653, -0.020355398, -0.01014522, 0.023274796, -0.0057833767, 0.010024521], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [1.2229432, 1.076517, 0.0, 1.3409408, 3.0517352, 1.4844297, 0.0, 0.0, 3.240369, 1.2634265, 0.0, 2.5497208, 2.666916, 2.129646, 2.643455, 0.0, 0.0, 6.1394396, 3.2758064, 1.2265569, 0.0, 0.0, 0.0, 2.9315183, 0.0, 0.0, 1.9867482, 1.2357924, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1924053, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 23, 23, 26, 26, 27, 27, 34, 34], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, -0.010318579, 3.1538463, 0.40983817, 2.0, 0.009695795, 0.018848179, 0.53184193, 0.4426461, -0.0117939515, 1.0, 0.86809504, 0.3566001, 1.0, -0.019305173, 0.0030553408, 0.71855736, 1.0, 0.29866442, -0.014969801, 0.017876782, -0.0060588326, 1.0, 0.029597178, 0.009850681, -0.3846154, 0.26923078, 0.009474597, 0.012885426, -0.01009814, 0.00044651653, -0.020355398, -0.01014522, 0.20166701, -0.0057833767, 0.010024521], "split_indices": [117, 71, 0, 1, 142, 0, 0, 0, 140, 142, 0, 108, 143, 141, 93, 0, 0, 143, 69, 141, 0, 0, 0, 108, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1966.0, 98.0, 981.0, 985.0, 880.0, 101.0, 114.0, 871.0, 737.0, 143.0, 207.0, 664.0, 552.0, 185.0, 116.0, 91.0, 339.0, 325.0, 434.0, 118.0, 97.0, 88.0, 222.0, 117.0, 141.0, 184.0, 327.0, 107.0, 110.0, 112.0, 88.0, 96.0, 136.0, 191.0, 93.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0036194837, -0.008980518, 0.052240677, 0.0025851985, -0.0181568, -0.0083574485, 0.015464385, -0.009412758, 0.020014249, -0.017293459, 0.012599121, 0.0046892883, -0.019179022, -0.008878932, 0.012147292, 0.008171531, -0.013550547, -0.006333752, 0.014322561, 0.010400095, -0.013417433, -0.015959235, 0.013507262, -0.004433779, 0.005805904], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.2650752, 3.2736013, 2.637313, 3.6431267, 0.0, 5.9035616, 0.0, 3.7266767, 0.0, 0.0, 0.0, 2.1312149, 0.0, 2.6016452, 0.0, 2.0804558, 0.0, 2.0515552, 0.0, 2.7867694, 0.0, 1.4703722, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.03513, 1.0, 1.0, -0.0181568, -0.1923077, 0.015464385, 0.79864544, 0.020014249, -0.017293459, 0.012599121, 0.73786306, -0.019179022, 0.6932683, 0.012147292, 1.3908919, -0.013550547, 1.3516413, 0.014322561, 0.45061553, -0.013417433, 1.0, 0.013507262, -0.004433779, 0.005805904], "split_indices": [113, 141, 61, 125, 0, 1, 0, 141, 0, 0, 0, 139, 0, 143, 0, 138, 0, 138, 0, 142, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1640.0, 425.0, 1537.0, 103.0, 267.0, 158.0, 1449.0, 88.0, 120.0, 147.0, 1345.0, 104.0, 1205.0, 140.0, 1062.0, 143.0, 959.0, 103.0, 848.0, 111.0, 700.0, 148.0, 506.0, 194.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054010926, -0.016455103, 0.051108044, 0.0069188536, -0.0587328, 0.1300486, -0.006247547, -0.037042323, 0.07758543, -0.028149156, -0.019367639, -0.0026104364, 0.025781015, -0.020402515, -0.014483931, 0.025298694, 0.012372032, -0.059633024, 0.0103628645, 0.004849077, -0.04677698, -0.011905252, 0.10749147, -0.004520438, -0.023519782, -0.012320473, -0.0051389113, -0.0051641758, 0.026487602, -0.0856193, 0.01928201, 0.0078579215, -0.009191247, 0.0023717713, -0.017255122], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, -1, 21, 23, -1, -1, 25, -1, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.2942799, 1.7125456, 3.0395913, 3.46695, 2.5463996, 3.9900694, 0.0, 1.2340797, 4.89569, 2.0868838, 0.0, 0.0, 0.0, 1.0829461, 0.0, 0.0, 3.9003212, 3.9283857, 0.0, 0.0, 1.3715725, 0.0, 4.533166, 4.9452643, 0.0, 0.0, 2.026801, 0.0, 0.0, 2.0815687, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 16, 16, 17, 17, 20, 20, 22, 22, 23, 23, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, -1, 22, 24, -1, -1, 26, -1, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.9453911, -0.15384616, -0.006247547, 1.0, 0.5245163, 0.8417005, -0.019367639, -0.0026104364, 0.025781015, 0.17894638, -0.014483931, 0.025298694, 1.0, 0.65398246, 0.0103628645, 0.004849077, 0.28457788, -0.011905252, 1.0, 0.52685916, -0.023519782, -0.012320473, 0.45524624, -0.0051641758, 0.026487602, 0.3628846, 0.01928201, 0.0078579215, -0.009191247, 0.0023717713, -0.017255122], "split_indices": [62, 121, 124, 71, 141, 1, 0, 40, 141, 139, 0, 0, 0, 143, 0, 0, 109, 139, 0, 0, 139, 0, 61, 139, 0, 0, 140, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1733.0, 339.0, 1116.0, 617.0, 200.0, 139.0, 688.0, 428.0, 503.0, 114.0, 90.0, 110.0, 596.0, 92.0, 116.0, 312.0, 406.0, 97.0, 165.0, 431.0, 131.0, 181.0, 309.0, 97.0, 152.0, 279.0, 90.0, 91.0, 219.0, 90.0, 142.0, 137.0, 97.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056730267, 0.00017969002, -0.012632437, -0.008767781, 0.015518637, -0.10269095, 0.0016619278, 0.0057667526, -0.024523186, -0.007819445, 0.01736196, 0.0021774839, -0.014693561, -0.009503579, 0.013424109, 0.009155832, -0.019132378, 0.059642844, -0.034330655, -0.005115261, 0.018197952, 0.0006923542, -0.09631583, -0.011036581, 0.0030083938, 0.003962442, -0.016188702], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, 11, -1, 13, -1, 15, -1, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [1.465237, 2.744711, 0.0, 1.8328158, 0.0, 4.2743793, 2.7455852, 0.0, 0.0, 2.2196126, 0.0, 2.296996, 0.0, 1.331207, 0.0, 0.0, 1.4953625, 2.7379787, 2.2729402, 0.0, 0.0, 2.1837332, 3.3694034, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, 12, -1, 14, -1, 16, -1, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1155719, -0.012632437, -0.5, 0.015518637, 0.5878983, 0.959083, 0.0057667526, -0.024523186, 0.8293257, 0.01736196, 0.7787005, -0.014693561, 0.10037874, 0.013424109, 0.009155832, -0.30769232, 1.0, 1.0, -0.005115261, 0.018197952, 0.22473024, 0.25240347, -0.011036581, 0.0030083938, 0.003962442, -0.016188702], "split_indices": [143, 142, 0, 1, 0, 139, 141, 0, 0, 142, 0, 140, 0, 139, 0, 0, 1, 69, 109, 0, 0, 139, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1979.0, 96.0, 1871.0, 108.0, 187.0, 1684.0, 88.0, 99.0, 1596.0, 88.0, 1489.0, 107.0, 1368.0, 121.0, 119.0, 1249.0, 202.0, 1047.0, 106.0, 96.0, 669.0, 378.0, 140.0, 529.0, 123.0, 255.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0035855756, -0.0044030137, 0.08402684, 0.005334662, -0.019182712, -0.0045226994, 0.020654874, -0.005493969, 0.020519355, 0.007012888, -0.014687585, -0.025413634, 0.037269607, 0.010542497, -0.017490251, 0.002271123, 0.022121511, -0.03320191, 0.015782585, 0.013804403, -0.01851044, -0.105802715, 0.058338214, -0.018739915, 0.01444345, -0.0031407736, -0.021811048, -0.00043433244, 0.011806311, 0.014297242, -0.025657581, 0.0033913206, -0.012230392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [1.3302076, 3.4366174, 2.961412, 3.8739154, 0.0, 0.0, 0.0, 3.0024767, 0.0, 1.5305476, 0.0, 4.0474067, 5.195316, 3.910794, 0.0, 1.913027, 0.0, 3.1102748, 0.0, 0.0, 3.2725382, 2.180689, 0.7749356, 0.0, 2.535839, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1589873, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 20, 20, 21, 21, 22, 22, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.1919701, 1.0, 0.9928109, -0.019182712, -0.0045226994, 0.020654874, 0.79864544, 0.020519355, 1.0, -0.014687585, 0.5957538, 0.625384, 0.4581304, -0.017490251, 0.14794195, 0.022121511, 1.0, 0.015782585, 0.013804403, 1.2386552, 0.28835878, 0.25436574, -0.018739915, 0.0, -0.0031407736, -0.021811048, -0.00043433244, 0.011806311, 0.014297242, 0.46040413, 0.0033913206, -0.012230392], "split_indices": [125, 143, 16, 142, 0, 0, 0, 141, 0, 111, 0, 141, 141, 140, 0, 139, 0, 17, 0, 0, 138, 142, 142, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1883.0, 187.0, 1790.0, 93.0, 91.0, 96.0, 1698.0, 92.0, 1560.0, 138.0, 753.0, 807.0, 607.0, 146.0, 678.0, 129.0, 468.0, 139.0, 90.0, 588.0, 261.0, 207.0, 96.0, 492.0, 157.0, 104.0, 101.0, 106.0, 117.0, 375.0, 232.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0052023227, -0.004136528, 0.07075527, 0.0067486125, -0.011645759, 0.01833385, -0.00065077026, -0.015632464, 0.041215476, -0.00044227846, -0.010677355, 0.111388944, -0.013577503, -0.018958906, 0.015756624, 0.0037813212, 0.022844123, -0.08734167, 0.007988764, 0.001972203, -0.017087828, 0.0028074805, -0.017845998, -0.029103134, 0.020925885, -0.009086257, -0.013964074, -0.030552594, 0.0061419527, 0.0067735463, -0.007199496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [1.2666197, 2.2141843, 2.244217, 1.2735904, 0.0, 0.0, 0.0, 1.385834, 2.499259, 2.5103233, 0.0, 2.454478, 2.5164483, 2.4421184, 0.0, 0.0, 0.0, 2.1453774, 0.0, 4.348015, 0.0, 0.0, 0.0, 1.2988061, 0.0, 0.7522098, 0.0, 1.5519234, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.0129824, 0.8764639, 1.2172625, 1.0, -0.011645759, 0.01833385, -0.00065077026, 1.377572, 1.0, 0.462621, -0.010677355, 1.4038054, 0.53184193, 0.46040413, 0.015756624, 0.0037813212, 0.022844123, 0.45263797, 0.007988764, 0.38246974, -0.017087828, 0.0028074805, -0.017845998, 0.28835878, 0.020925885, 0.30363682, -0.013964074, 1.0, 0.0061419527, 0.0067735463, -0.007199496], "split_indices": [139, 142, 139, 71, 0, 0, 0, 138, 121, 139, 0, 138, 140, 140, 0, 0, 0, 142, 0, 142, 0, 0, 0, 142, 0, 140, 0, 53, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1811.0, 258.0, 1651.0, 160.0, 105.0, 153.0, 1001.0, 650.0, 858.0, 143.0, 285.0, 365.0, 768.0, 90.0, 175.0, 110.0, 204.0, 161.0, 675.0, 93.0, 90.0, 114.0, 587.0, 88.0, 497.0, 90.0, 381.0, 116.0, 113.0, 268.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.004596894, 0.000990538, -0.009958327, -0.0049389238, 0.011334782, 0.009131039, -0.06616557, 0.01286517, 0.0014662162, -0.024501214, -0.013320443, 0.075984016, -0.0132486895, 0.004244051, -0.009800588, 0.0009316085, 0.016077524, -0.012646789, -0.0011973436, -0.035713375, 0.07803021, -0.058603898, 0.013326495, 0.14752667, -0.010343272, -0.02004757, -0.16000064, 0.029341891, 0.0039458326, 0.0021566728, -0.012302019, -0.00546342, -0.027135376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, -1, -1, -1, 19, 21, 23, 25, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0986112, 1.3024569, 0.0, 1.5997251, 0.0, 1.3833177, 0.969216, 0.0, 1.5559655, 1.0529933, 0.0, 1.3227679, 1.6168653, 0.0, 0.0, 0.0, 0.0, 0.0, 2.9287786, 2.8855298, 4.098585, 2.5685315, 0.0, 3.7050877, 0.0, 2.0397236, 2.123651, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, -1, -1, -1, 20, 22, 24, 26, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.009958327, 1.0, 0.011334782, -0.5769231, 0.30764693, 0.01286517, 1.2642441, 1.0, -0.013320443, 0.34615386, -0.5, 0.004244051, -0.009800588, 0.0009316085, 0.016077524, -0.012646789, 0.7995418, 1.2307693, 1.1395987, 0.30769232, 0.013326495, 0.9504357, -0.010343272, 0.74522334, 0.46040413, 0.029341891, 0.0039458326, 0.0021566728, -0.012302019, -0.00546342, -0.027135376], "split_indices": [43, 1, 0, 80, 0, 1, 140, 0, 138, 116, 0, 1, 1, 0, 0, 0, 0, 0, 143, 1, 142, 1, 0, 143, 0, 140, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1955.0, 115.0, 1857.0, 98.0, 1510.0, 347.0, 91.0, 1419.0, 214.0, 133.0, 234.0, 1185.0, 112.0, 102.0, 131.0, 103.0, 114.0, 1071.0, 746.0, 325.0, 657.0, 89.0, 235.0, 90.0, 476.0, 181.0, 100.0, 135.0, 339.0, 137.0, 93.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017382168, -0.007459842, 0.00896659, 0.0045662196, -0.11224976, -0.007559433, 0.01985767, 0.0024843288, -0.022093616, 0.0006849133, -0.015049234, -0.01859618, 0.061594464, -0.00041828136, -0.11812266, 0.011283225, 0.021639824, -0.018508619, 0.00875714, -0.024226347, 0.00019700397, -0.010286245, 0.012707129, 0.0036720936, -0.09541784, -0.033712137, 0.09116295, -0.020935804, 0.0028880518, 0.0059686275, -0.006028238, 0.002228715, 0.016374173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.074201, 2.4359863, 0.0, 4.0792418, 2.965131, 1.9231306, 0.0, 0.0, 0.0, 1.8121035, 0.0, 2.1203613, 2.8894844, 1.5774369, 2.6984224, 3.7006779, 0.0, 1.4022512, 0.0, 0.0, 0.0, 0.0, 0.0, 2.086757, 2.6059155, 1.1092837, 0.9547931, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.00896659, 1.6541643, 0.6349509, 1.5493003, 0.01985767, 0.0024843288, -0.022093616, 0.5942927, -0.015049234, 0.54411215, 1.0, 1.0, 1.0, 0.69042635, 0.021639824, 0.39616552, 0.00875714, -0.024226347, 0.00019700397, -0.010286245, 0.012707129, 0.28457788, 0.45589396, 0.13291834, 0.3245149, -0.020935804, 0.0028880518, 0.0059686275, -0.006028238, 0.002228715, 0.016374173], "split_indices": [102, 119, 0, 138, 141, 138, 0, 0, 0, 140, 0, 139, 61, 121, 69, 142, 0, 141, 0, 0, 0, 0, 0, 139, 141, 140, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1933.0, 121.0, 1734.0, 199.0, 1632.0, 102.0, 88.0, 111.0, 1543.0, 89.0, 1172.0, 371.0, 991.0, 181.0, 280.0, 91.0, 822.0, 169.0, 89.0, 92.0, 141.0, 139.0, 638.0, 184.0, 447.0, 191.0, 96.0, 88.0, 99.0, 348.0, 98.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0045834435, -0.067603104, 0.00411132, 0.0059799673, -0.016934203, -0.006805199, 0.06512475, -0.017556485, 0.014445059, -0.0017435303, 0.020299204, -0.0031053163, -0.0146122025, -0.015125523, 0.010705267, -0.018923128, 0.06333925, 0.01419329, -0.08744451, 0.007292775, 0.017989963, 0.045354173, -0.009137761, -0.003023426, -0.019016286, 0.012878425, -0.00877875, 0.0006539155, 0.013578621, 0.0070454217, -0.0041481615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, -1, -1, -1, 13, -1, -1, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.1298546, 3.2404556, 1.2068906, 0.0, 0.0, 2.4994602, 3.1301417, 2.666118, 0.0, 0.0, 0.0, 2.639388, 0.0, 0.0, 1.8401635, 1.7132331, 2.776439, 1.6744484, 1.4456289, 3.315264, 0.0, 1.5886375, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7735022, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 11, 11, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 27, 27], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, -1, -1, -1, 14, -1, -1, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [-0.5, -0.5769231, 1.0, 0.0059799673, -0.016934203, 1.1211865, 1.4383249, 0.8212249, 0.014445059, -0.0017435303, 0.020299204, -0.3846154, -0.0146122025, -0.015125523, 1.0, 1.0, 1.0, 1.3351715, 0.34886172, 1.0, 0.017989963, 0.28457788, -0.009137761, -0.003023426, -0.019016286, 0.012878425, -0.00877875, 0.16507967, 0.013578621, 0.0070454217, -0.0041481615], "split_indices": [1, 1, 42, 0, 0, 139, 138, 139, 0, 0, 0, 1, 0, 0, 71, 109, 116, 138, 140, 121, 0, 139, 0, 0, 0, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 250.0, 1812.0, 111.0, 139.0, 1537.0, 275.0, 1435.0, 102.0, 172.0, 103.0, 1290.0, 145.0, 110.0, 1180.0, 755.0, 425.0, 509.0, 246.0, 287.0, 138.0, 393.0, 116.0, 158.0, 88.0, 126.0, 161.0, 263.0, 130.0, 99.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0015482443, -0.009103374, 0.056936666, 0.004525138, -0.08821264, 0.13729617, -0.0057779425, -0.016625598, 0.088141054, -0.030283803, 0.003015651, 0.0015575999, 0.02820079, -0.0066070645, -0.010400032, 0.024267308, 0.01542008, -0.018698158, 0.012113793, 0.011176393, -0.009732269, -0.09140256, 0.004614664, -0.000998773, -0.018154038, -0.012814012, 0.0064928466, 0.03921597, -0.054800242, 0.010834009, -0.0015791229, -0.0026319143, -0.013665783, -0.0072710253, 0.006965939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, -1, 17, -1, -1, 19, 21, -1, -1, -1, 23, 25, -1, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [1.2253809, 1.8781228, 3.0882072, 2.628048, 6.5036874, 3.470024, 0.0, 1.0381843, 3.371315, 0.0, 0.0, 0.0, 0.0, 1.6434292, 0.0, 0.0, 2.2158628, 1.6474866, 0.0, 0.0, 0.0, 1.7318985, 0.77367574, 0.0, 0.0, 1.247374, 0.0, 0.96959305, 1.3494377, 0.0, 0.0, 0.9777485, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 13, 13, 16, 16, 17, 17, 21, 21, 22, 22, 25, 25, 27, 27, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, -1, 18, -1, -1, 20, 22, -1, -1, -1, 24, 26, -1, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0093737, 1.0, 1.4328352, 0.9867292, 0.42682585, -0.0057779425, 1.3968375, 0.6505282, -0.030283803, 0.003015651, 0.0015575999, 0.02820079, 0.645011, -0.010400032, 0.024267308, 1.0, 1.0, 0.012113793, 0.011176393, -0.009732269, 0.35454825, 0.4477922, -0.000998773, -0.018154038, 0.25052792, 0.0064928466, 1.0, 0.5769231, 0.010834009, -0.0015791229, 0.30742753, -0.013665783, -0.0072710253, 0.006965939], "split_indices": [62, 140, 124, 138, 142, 140, 0, 138, 140, 0, 0, 0, 0, 140, 0, 0, 50, 69, 0, 0, 0, 140, 140, 0, 0, 140, 0, 106, 1, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1742.0, 335.0, 1486.0, 256.0, 197.0, 138.0, 1186.0, 300.0, 91.0, 165.0, 107.0, 90.0, 1064.0, 122.0, 96.0, 204.0, 972.0, 92.0, 110.0, 94.0, 236.0, 736.0, 124.0, 112.0, 571.0, 165.0, 255.0, 316.0, 113.0, 142.0, 193.0, 123.0, 98.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019391429, -0.046124313, 0.0101607945, 0.003687491, -0.019422552, 0.024319323, -0.056979634, 0.008966617, -0.007679254, 0.012659888, 0.018651498, 0.03517835, -0.02108661, 0.032070182, -0.069610514, -0.0095758485, 0.016760314, 0.0021543927, 0.022017376, 0.0045707203, -0.018589733, 0.021493051, -0.012296836, -0.0010806116, 0.018878901, 0.012386732, -0.02575615, 0.009969803, -0.050577197, 0.00033960186, -0.010205587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, 13, -1, 15, -1, 17, 19, -1, -1, 21, -1, -1, -1, 23, -1, 25, -1, -1, 27, -1, 29, -1, -1], "loss_changes": [1.1067003, 3.2828493, 1.5447404, 2.304216, 0.0, 2.5378683, 4.013469, 0.0, 0.0, 1.9993097, 0.0, 3.0690534, 0.0, 5.7004213, 3.2049735, 0.0, 0.0, 2.114823, 0.0, 0.0, 0.0, 2.858798, 0.0, 2.0564659, 0.0, 0.0, 1.7344444, 0.0, 1.2919886, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 21, 21, 23, 23, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, 14, -1, 16, -1, 18, 20, -1, -1, 22, -1, -1, -1, 24, -1, 26, -1, -1, 28, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.019422552, 5.0, 1.4757286, 0.008966617, -0.007679254, 0.8784966, 0.018651498, 1.0, -0.02108661, 1.4465061, 1.0, -0.0095758485, 0.016760314, 1.3923972, 0.022017376, 0.0045707203, -0.018589733, 0.49791622, -0.012296836, -0.23076923, 0.018878901, 0.012386732, 1.1905649, 0.009969803, 0.8076923, 0.00033960186, -0.010205587], "split_indices": [5, 0, 64, 69, 0, 0, 138, 0, 0, 143, 0, 13, 0, 138, 105, 0, 0, 138, 0, 0, 0, 139, 0, 1, 0, 0, 138, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 445.0, 1625.0, 333.0, 112.0, 1342.0, 283.0, 161.0, 172.0, 1252.0, 90.0, 177.0, 106.0, 1013.0, 239.0, 89.0, 88.0, 874.0, 139.0, 120.0, 119.0, 757.0, 117.0, 667.0, 90.0, 110.0, 557.0, 92.0, 465.0, 227.0, 238.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0010270654, 0.010810021, -0.04638691, 0.017724447, 0.0014319787, -0.020106966, 0.017598314, -0.00873436, 0.008815061, -0.0068805413, 0.0072737555, 0.10658603, -0.0058091218, 0.027581608, -0.008496004, -0.021022137, 0.04772796, -0.07220315, 0.028956966, -0.040968206, 0.020379914, 0.016039388, -0.13322511, -0.033316523, 0.095516495, 0.0077231154, -0.016580801, 0.010083846, -0.008926052, -0.049746398, -0.031454444, 0.0021873363, -0.012382797, 0.020244656, -0.00213816, -0.01462462, 0.007526466], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, 11, -1, -1, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, 27, 29, 31, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.95460075, 2.6627743, 3.483889, 0.0, 1.0585313, 0.0, 1.1862948, 0.0, 2.1318626, 0.0, 0.0, 6.288579, 1.0563552, 0.0, 0.0, 2.583561, 3.9729168, 2.6869829, 2.1180413, 2.7003458, 0.0, 1.821584, 4.465209, 1.318764, 3.0874817, 0.0, 0.0, 0.0, 0.0, 2.4368358, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 29, 29], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, 12, -1, -1, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, 28, 30, 32, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.34615386, 0.017724447, -0.5, -0.020106966, 1.0, -0.00873436, -0.34615386, -0.0068805413, 0.0072737555, 1.0, 1.2692307, 0.027581608, -0.008496004, 1.0, 1.321129, 0.446167, 0.36287326, 0.2844921, 0.020379914, 1.0, 0.23076923, 1.2679365, 0.69378763, 0.0077231154, -0.016580801, 0.010083846, -0.008926052, 0.76225835, -0.031454444, 0.0021873363, -0.012382797, 0.020244656, -0.00213816, -0.01462462, 0.007526466], "split_indices": [7, 1, 1, 0, 1, 0, 13, 0, 1, 0, 0, 124, 1, 0, 0, 124, 138, 141, 142, 142, 0, 23, 1, 138, 141, 0, 0, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1706.0, 352.0, 91.0, 1615.0, 103.0, 249.0, 124.0, 1491.0, 97.0, 152.0, 194.0, 1297.0, 103.0, 91.0, 1010.0, 287.0, 499.0, 511.0, 183.0, 104.0, 204.0, 295.0, 264.0, 247.0, 94.0, 89.0, 113.0, 91.0, 202.0, 93.0, 164.0, 100.0, 129.0, 118.0, 114.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0038877432, -0.0027721645, 0.07156682, -0.014998808, 0.00664232, 0.019311888, -0.0059545483, -0.003487315, 0.017731136, 0.006321985, -0.017547708, 0.013729528, -0.0116149355, -0.0012164518, 0.102707006, 0.019748427, -0.053028844, -0.0006846958, 0.020824067, -0.0053666336, 0.024223512, -0.010241261, -0.016571691, -0.02635364, 0.008617535, -0.013819085, 0.008720377, 0.0026621034, -0.0061625727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [0.930771, 2.6056087, 2.94834, 0.0, 3.0548165, 0.0, 0.0, 2.814082, 0.0, 1.4315797, 0.0, 1.9788254, 0.0, 1.3838705, 2.4741898, 5.0681057, 1.7695448, 0.0, 0.0, 1.5657715, 0.0, 3.3165023, 0.0, 1.2388337, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 1.0, -0.014998808, 1.1810409, 0.019311888, -0.0059545483, 0.94329643, 0.017731136, 1.0, -0.017547708, 0.70048, -0.0116149355, 0.47377545, 1.4465061, 0.5001129, 0.34615386, -0.0006846958, 0.020824067, 1.0, 0.024223512, 1.0, -0.016571691, 0.20894125, 0.008617535, -0.013819085, 0.008720377, 0.0026621034, -0.0061625727], "split_indices": [125, 1, 15, 0, 139, 0, 0, 139, 0, 84, 0, 140, 0, 141, 138, 139, 1, 0, 0, 62, 0, 111, 0, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1880.0, 185.0, 113.0, 1767.0, 96.0, 89.0, 1668.0, 99.0, 1578.0, 90.0, 1488.0, 90.0, 1274.0, 214.0, 907.0, 367.0, 105.0, 109.0, 815.0, 92.0, 266.0, 101.0, 663.0, 152.0, 115.0, 151.0, 265.0, 398.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0030679263, -0.0071108923, 0.04182302, 0.0013928303, -0.013541709, 0.01700868, 0.008915683, -0.009558617, 0.017981533, -0.015286602, 0.07179441, 0.00029651402, -0.0137106795, 0.0203257, -0.0019148861, -0.031097302, 0.03716777, 0.035663232, -0.08100723, -0.008190759, 0.11659561, 0.0115645435, -0.0045877043, -0.0055674557, -0.020171085, 0.04537615, -0.01001781, -0.004228359, 0.02460053, 0.009061187, -0.0087807765, -0.008139207, 0.012094954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8173648, 1.7904633, 1.8191724, 3.0071828, 0.0, 0.0, 3.4892113, 1.8226563, 0.0, 0.0, 2.9530435, 1.5580345, 0.0, 0.0, 0.0, 2.4223738, 2.2300904, 2.0282712, 3.7880344, 1.9414265, 4.6261153, 0.0, 0.0, 2.0249133, 0.0, 2.385496, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.03513, 0.2983946, 1.0, -0.013541709, 0.01700868, 0.5878993, 0.79864544, 0.017981533, -0.015286602, 0.9877956, 1.0, -0.0137106795, 0.0203257, -0.0019148861, 0.07692308, 1.0, 1.306339, 0.3387323, 0.47044897, 1.0, 0.0115645435, -0.0045877043, 1.0, -0.020171085, 1.0, -0.01001781, -0.004228359, 0.02460053, 0.009061187, -0.0087807765, -0.008139207, 0.012094954], "split_indices": [113, 141, 142, 125, 0, 0, 143, 141, 0, 0, 143, 39, 0, 0, 0, 1, 71, 138, 141, 143, 93, 0, 0, 12, 0, 115, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1641.0, 431.0, 1539.0, 102.0, 88.0, 343.0, 1450.0, 89.0, 96.0, 247.0, 1346.0, 104.0, 101.0, 146.0, 727.0, 619.0, 311.0, 416.0, 394.0, 225.0, 157.0, 154.0, 256.0, 160.0, 249.0, 145.0, 101.0, 124.0, 118.0, 138.0, 93.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016233164, -0.010132741, 0.04957696, -0.038359538, 0.019225711, -0.020190312, 0.020926652, 0.012441432, -0.09764755, -0.0013580623, 0.010372753, 0.0065350826, -0.011446017, -0.037480064, 0.014745641, -0.047087207, -0.026325842, 0.037097316, -0.012264042, 0.022884902, -0.01321532, -0.102020256, 0.0077913464, -0.014753123, 0.018264893, 0.01484389, -0.0077765803, 0.00023404979, -0.020922724, 0.007861808, -0.04791601, 0.0026541061, -0.010047397], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, -1, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.90491736, 1.475906, 3.297768, 2.734795, 1.5184673, 1.6611739, 0.0, 3.295934, 3.5084298, 3.2740998, 0.0, 0.0, 0.0, 2.040234, 0.0, 2.2042003, 0.0, 4.022506, 0.0, 2.7548878, 0.0, 2.4949698, 0.0, 1.2169081, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1348605, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, -1, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.2692307, 1.0, 0.46564206, 1.0, 0.9867292, 0.31750953, 0.020926652, 1.0, 0.8043532, 0.6517612, 0.010372753, 0.0065350826, -0.011446017, 0.67420596, 0.014745641, 0.557428, -0.026325842, 0.45589396, -0.012264042, -0.26923078, -0.01321532, 0.33494726, 0.0077913464, 1.2146873, 0.018264893, 0.01484389, -0.0077765803, 0.00023404979, -0.020922724, 0.007861808, 0.29029372, 0.0026541061, -0.010047397], "split_indices": [1, 124, 139, 16, 142, 142, 0, 62, 139, 141, 0, 0, 0, 142, 0, 141, 0, 141, 0, 1, 0, 141, 0, 138, 0, 0, 0, 0, 0, 0, 140, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1781.0, 296.0, 908.0, 873.0, 206.0, 90.0, 489.0, 419.0, 702.0, 171.0, 108.0, 98.0, 357.0, 132.0, 321.0, 98.0, 533.0, 169.0, 218.0, 139.0, 223.0, 98.0, 393.0, 140.0, 97.0, 121.0, 113.0, 110.0, 103.0, 290.0, 120.0, 170.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0043414305, 0.0018099329, -0.012015271, -0.007990871, 0.016806396, -0.01827593, 0.013146478, -0.009955543, -0.015615659, 0.0007725213, -0.014203386, -0.06689125, 0.022102611, 0.0019565437, -0.1345158, 0.005970477, 0.016616954, -0.0061745085, -0.019537857, 0.02102436, -0.0074409805, -0.00096567837, 0.020356536, 0.003704148, -0.006242133], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.4689635, 3.1904109, 0.0, 2.6520383, 0.0, 1.975513, 0.0, 2.3011189, 0.0, 2.167798, 0.0, 2.1047745, 2.6541295, 0.0, 0.89466286, 1.2427062, 0.0, 0.0, 0.0, 3.4721832, 0.0, 1.803203, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.6937072, 1.0, -0.012015271, 1.0663754, 0.016806396, 1.0, 0.013146478, 0.8784966, -0.015615659, 1.0, -0.014203386, 0.37693453, 3.0, 0.0019565437, 1.0, 0.66834235, 0.016616954, -0.0061745085, -0.019537857, 0.557428, -0.0074409805, 0.3621228, 0.020356536, 0.003704148, -0.006242133], "split_indices": [138, 102, 0, 143, 0, 84, 0, 143, 0, 5, 0, 140, 0, 0, 69, 141, 0, 0, 0, 141, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1958.0, 104.0, 1849.0, 109.0, 1722.0, 127.0, 1624.0, 98.0, 1502.0, 122.0, 360.0, 1142.0, 158.0, 202.0, 1027.0, 115.0, 92.0, 110.0, 865.0, 162.0, 772.0, 93.0, 477.0, 295.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0011854124, -0.0057780114, 0.07156842, -0.013460425, 0.002693554, 0.017584197, -0.0037288547, -0.005654737, 0.014015108, 0.0033996112, -0.016578449, 0.017729035, -0.047082774, -0.0025884663, 0.014837675, -0.01489719, 0.051352456, 0.0073488303, -0.015603811, 0.014417538, -0.004252524, -0.015874991, 0.0019568605, -0.020113904, 0.010710831, -0.00372017, 0.009231598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, 19, -1, 21, -1, -1, -1, 23, 25, -1, -1, -1], "loss_changes": [1.0125607, 2.051757, 2.1112685, 0.0, 2.024253, 0.0, 0.0, 2.411135, 0.0, 1.1386056, 0.0, 3.2543378, 3.490259, 1.0505651, 0.0, 0.0, 1.5423787, 0.0, 2.2774506, 0.0, 0.0, 0.0, 1.872864, 1.2814262, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 16, 16, 18, 18, 22, 22, 23, 23], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, 20, -1, 22, -1, -1, -1, 24, 26, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 1.0, -0.013460425, 1.1810409, 0.017584197, -0.0037288547, 0.94329643, 0.014015108, 0.66754913, -0.016578449, 0.56020635, 1.0, -0.30769232, 0.014837675, -0.01489719, 0.03846154, 0.0073488303, -0.1923077, 0.014417538, -0.004252524, -0.015874991, 1.0, 3.0384614, 0.010710831, -0.00372017, 0.009231598], "split_indices": [125, 1, 15, 0, 139, 0, 0, 139, 0, 143, 0, 143, 111, 1, 0, 0, 1, 0, 1, 0, 0, 0, 62, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1880.0, 186.0, 116.0, 1764.0, 95.0, 91.0, 1663.0, 101.0, 1574.0, 89.0, 1226.0, 348.0, 1061.0, 165.0, 171.0, 177.0, 155.0, 906.0, 89.0, 88.0, 99.0, 807.0, 667.0, 140.0, 579.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0018901711, 0.0070192185, -0.008657453, -0.004018341, 0.04744627, -0.014787626, 0.015518941, -0.015321207, 0.015616121, 0.00027679474, -0.012459247, -0.015522516, 0.010403738, -0.01731663, 0.018255103, 0.0045198835, -0.094346225, -0.014156309, 0.012189142, 0.0021907778, -0.018034237, 0.0039226166, -0.014318277, -0.018889926, 0.0107888, 0.004128814, -0.0055414196], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [0.9356109, 0.86967504, 0.0, 2.6249814, 2.852333, 2.3720465, 0.0, 4.425166, 0.0, 4.0438104, 0.0, 0.0, 0.0, 1.9343665, 0.0, 1.9640802, 2.5393388, 1.8031466, 0.0, 0.0, 0.0, 1.608023, 0.0, 1.2220659, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008657453, 1.0, 1.0, 0.8293257, 0.015518941, -0.1923077, 0.015616121, 0.71047145, -0.012459247, -0.015522516, 0.010403738, 0.55845666, 0.018255103, 0.52959335, 1.0, 0.45263797, 0.012189142, 0.0021907778, -0.018034237, 0.39430043, -0.014318277, 1.0, 0.0107888, 0.004128814, -0.0055414196], "split_indices": [43, 113, 0, 102, 61, 142, 0, 1, 0, 142, 0, 0, 0, 139, 0, 142, 115, 142, 0, 0, 0, 142, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1949.0, 113.0, 1531.0, 418.0, 1434.0, 97.0, 265.0, 153.0, 1261.0, 173.0, 122.0, 143.0, 1150.0, 111.0, 896.0, 254.0, 773.0, 123.0, 108.0, 146.0, 678.0, 95.0, 556.0, 122.0, 210.0, 346.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.002912933, 0.007706453, -0.00790122, -0.009049829, -1.9284167e-05, 0.011903579, -0.006318871, 0.009984665, -0.028638646, -0.096178934, 0.044217013, -0.002417297, -0.021397587, -0.017747326, -0.0012800117, 0.010460324, 0.021383053, -0.0403683, 0.020298757, 0.070856825, -0.13831295, 0.011543158, -0.07390856, 0.036350895, 0.019639963, -0.024590796, -0.0029495235, -0.018384663, -0.11471526, 0.0126921935, -0.03525238, -0.009368252, 0.0062902756, 0.0026841785, -0.020203944, -0.014453798, 0.007990875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, -1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, 21, -1, 23, 25, -1, 27, 29, -1, -1, -1, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.82223684, 0.0, 1.2629879, 0.0, 1.3282456, 0.0, 0.6120652, 3.532471, 3.4504519, 1.6064386, 4.208308, 4.848689, 0.0, 0.0, 0.0, 5.508041, 0.0, 2.7434235, 0.0, 1.8887391, 2.0723584, 0.0, 0.97880244, 2.217933, 0.0, 0.0, 0.0, 1.1201007, 3.0779774, 0.0, 2.4038217, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22, 23, 23, 27, 27, 28, 28, 30, 30], "right_children": [2, -1, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, 22, -1, 24, 26, -1, 28, 30, -1, -1, -1, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [0.10037874, 0.007706453, 0.18635547, -0.009049829, 1.2192684, 0.011903579, 1.0, 1.0, 0.94329643, 1.0, 1.5681577, 0.73371637, -0.021397587, -0.017747326, -0.0012800117, 0.68592155, 0.021383053, 1.0, 0.020298757, 0.5889432, -0.07692308, 0.011543158, 0.36704683, 0.40779537, 0.019639963, -0.024590796, -0.0029495235, 1.0, -0.115384616, 0.0126921935, 1.0, -0.009368252, 0.0062902756, 0.0026841785, -0.020203944, -0.014453798, 0.007990875], "split_indices": [139, 0, 139, 0, 138, 0, 97, 89, 139, 50, 138, 141, 0, 0, 0, 141, 0, 89, 0, 140, 1, 0, 142, 142, 0, 0, 0, 81, 1, 0, 115, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 121.0, 1940.0, 169.0, 1771.0, 89.0, 1682.0, 972.0, 710.0, 237.0, 735.0, 622.0, 88.0, 120.0, 117.0, 613.0, 122.0, 525.0, 97.0, 436.0, 177.0, 93.0, 432.0, 342.0, 94.0, 89.0, 88.0, 183.0, 249.0, 151.0, 191.0, 95.0, 88.0, 95.0, 154.0, 98.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0008249738, 0.013171283, -0.0474485, 0.0025570886, 0.010934621, -0.079752535, -0.0048497515, 0.014297356, -0.016743217, -0.0015793771, -0.013958491, -0.0090921195, 0.008313436, -0.00019136062, 0.022357885, 0.012203983, -0.013563042, 0.023700042, -0.012675934, 0.002356603, 0.12093125, 0.03741635, -0.051665027, -0.0026816574, 0.026867911, 0.0068898527, 0.017059565, -0.100828744, 0.0048343195, 0.006480382, -0.0052340324, -0.018720206, -0.00052830327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.2349104, 1.6843516, 0.5807188, 2.9656382, 0.0, 0.9184327, 1.3782715, 4.214787, 0.0, 0.0, 0.0, 0.0, 0.0, 2.182458, 0.0, 1.9026587, 0.0, 2.2827728, 0.0, 1.7083744, 4.3222265, 2.2238264, 1.7454557, 0.0, 0.0, 1.526463, 0.0, 1.964118, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.1919701, 0.010934621, 0.31107146, 0.15482669, 0.9907159, -0.016743217, -0.0015793771, -0.013958491, -0.0090921195, 0.008313436, 1.5248721, 0.022357885, 0.79864544, -0.013563042, 0.6444585, -0.012675934, 1.3242663, 1.4120544, 0.44330555, 1.0, -0.0026816574, 0.026867911, 1.0, 0.017059565, 1.0, 0.0048343195, 0.006480382, -0.0052340324, -0.018720206, -0.00052830327], "split_indices": [80, 125, 23, 143, 0, 141, 143, 141, 0, 0, 0, 0, 0, 138, 0, 141, 0, 142, 0, 138, 138, 141, 109, 0, 0, 23, 0, 97, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1650.0, 422.0, 1486.0, 164.0, 240.0, 182.0, 1390.0, 96.0, 116.0, 124.0, 92.0, 90.0, 1300.0, 90.0, 1191.0, 109.0, 1100.0, 91.0, 902.0, 198.0, 547.0, 355.0, 99.0, 99.0, 445.0, 102.0, 238.0, 117.0, 225.0, 220.0, 125.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0020470708, -0.006016434, 0.05134568, -0.03118297, 0.020341586, -0.031044044, 0.019650856, -0.07336645, 0.004875697, 0.091338, -0.002816504, 0.008987914, -0.015595374, -0.004052175, -0.018513568, -0.034468252, 0.011018734, -0.0010237361, 0.02301577, 0.021284217, -0.010361875, -0.0065980474, 0.010768798, 0.004229085, -0.09166132, 0.014021893, -0.008308536, -0.0024280704, -0.017872613, 0.07338174, -0.012704442, -0.0016544437, 0.016925348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, -1, -1, -1, 25, -1, -1, -1, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.820083, 1.176102, 3.4683797, 1.3796198, 1.4238268, 2.7943287, 0.0, 3.2383301, 2.0261111, 3.0034413, 1.586403, 0.0, 0.0, 1.7853284, 0.0, 1.5628717, 0.0, 0.0, 0.0, 1.8548326, 0.0, 0.0, 0.0, 0.0, 1.1967614, 0.0, 4.0932174, 0.0, 0.0, 2.155345, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 19, 19, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, -1, -1, -1, 26, -1, -1, -1, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.321129, 1.0, 0.0, 0.28835878, 0.019650856, 0.61962706, 1.0, 0.5647175, 0.6923077, 0.008987914, -0.015595374, 0.419542, -0.018513568, -0.34615386, 0.011018734, -0.0010237361, 0.02301577, 1.0, -0.010361875, -0.0065980474, 0.010768798, 0.004229085, 1.3516413, 0.014021893, 1.4857019, -0.0024280704, -0.017872613, 0.46161503, -0.012704442, -0.0016544437, 0.016925348], "split_indices": [1, 124, 138, 15, 0, 142, 0, 139, 62, 140, 1, 0, 0, 141, 0, 1, 0, 0, 0, 81, 0, 0, 0, 0, 138, 0, 138, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1773.0, 290.0, 907.0, 866.0, 185.0, 105.0, 418.0, 489.0, 213.0, 653.0, 94.0, 91.0, 258.0, 160.0, 356.0, 133.0, 123.0, 90.0, 527.0, 126.0, 166.0, 92.0, 152.0, 204.0, 105.0, 422.0, 115.0, 89.0, 250.0, 172.0, 129.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004046783, 0.009807854, -0.0012446258, 0.0074670888, -0.010282437, -0.0016614978, 0.012967238, 0.00600058, -0.012378566, -0.012224237, 0.018967057, 0.001842472, -0.01980455, 0.021233324, -0.030724714, -0.01571601, 0.09676212, 0.02536682, -0.0908228, 0.048616376, -0.091105536, 0.02115882, -0.0009920237, -0.0028535791, 0.012418831, -0.019430421, 0.0005928098, -0.0022335357, 0.013891861, -0.0015189624, -0.015400727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.018009, 0.0, 1.714117, 1.9901625, 0.0, 1.5533036, 0.0, 5.2285643, 0.0, 3.7143438, 0.0, 0.83421874, 0.0, 2.3107321, 1.6619003, 2.6965935, 3.331978, 1.3583181, 2.382838, 1.9221303, 1.2224617, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009807854, 1.1765813, 0.9291086, -0.010282437, 0.8071234, 0.012967238, 0.789198, -0.012378566, 1.453637, 0.018967057, 1.0, -0.01980455, 0.43847784, 0.35454825, 1.2642441, 1.0, 0.31571105, 1.3340551, 0.2243912, 0.3401452, 0.02115882, -0.0009920237, -0.0028535791, 0.012418831, -0.019430421, 0.0005928098, -0.0022335357, 0.013891861, -0.0015189624, -0.015400727], "split_indices": [1, 0, 140, 140, 0, 140, 0, 142, 0, 138, 0, 109, 0, 142, 140, 138, 93, 141, 138, 142, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2046.0, 109.0, 1937.0, 1784.0, 153.0, 1660.0, 124.0, 1562.0, 98.0, 1421.0, 141.0, 1321.0, 100.0, 828.0, 493.0, 556.0, 272.0, 255.0, 238.0, 300.0, 256.0, 131.0, 141.0, 165.0, 90.0, 115.0, 123.0, 168.0, 132.0, 116.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.004699942, 0.010348781, -0.00062907045, -0.008684568, 0.0062910076, -0.004558407, 0.06592405, 0.007996783, -0.010840346, -0.0188671, 0.021163315, -0.0022265173, 0.01487046, 0.0027989803, -0.006519756, 0.010371667, -0.012186991, -0.0024999776, -0.009854916, -0.016696116, 0.05118722, 0.005160222, -0.01968895, 0.013393037, -0.0037800658, -0.0017709406, 0.017512318], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, 13, -1, 15, -1, -1, -1, -1, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [1.0902612, 0.0, 1.1723697, 0.0, 1.1768633, 2.0065398, 3.4593563, 1.9750587, 0.0, 0.38424972, 0.0, 1.3507125, 0.0, 0.0, 0.0, 0.0, 0.9788116, 0.8017827, 0.0, 3.2767227, 1.6198903, 2.8841465, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, 14, -1, 16, -1, -1, -1, -1, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010348781, -0.5, -0.008684568, 1.0, 0.9061094, 1.4383249, 1.5072216, -0.010840346, 0.39957437, 0.021163315, 0.10037874, 0.01487046, 0.0027989803, -0.006519756, 0.010371667, 1.4615384, 0.5942927, -0.009854916, 0.6228613, -0.03846154, 0.52959335, -0.01968895, 0.013393037, -0.0037800658, -0.0017709406, 0.017512318], "split_indices": [1, 0, 1, 0, 42, 141, 138, 138, 0, 140, 0, 139, 0, 0, 0, 0, 1, 140, 0, 142, 1, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 106.0, 1965.0, 146.0, 1819.0, 1539.0, 280.0, 1373.0, 166.0, 177.0, 103.0, 1280.0, 93.0, 88.0, 89.0, 110.0, 1170.0, 1052.0, 118.0, 832.0, 220.0, 742.0, 90.0, 114.0, 106.0, 654.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0034437333, -0.003649824, 0.075644664, 0.0023983885, -0.011875892, 0.017333394, -0.002747176, -0.0059984485, 0.015907787, 0.0039793053, -0.012634553, -0.024762763, 0.03809298, 0.0010033159, -0.012647887, 0.00594085, 0.01964621, -0.021333888, 0.0145336045, -0.023778887, 0.014332457, -0.0034877039, -0.010636567, 0.009043106, -0.055686112, -0.06510957, 0.053732593, -0.01250848, -0.012424517, -0.0019984671, -0.012151567, 0.010283332, -0.0030213788, -0.008684413, 0.004753182], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, 25, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0591501, 1.310953, 1.8635734, 2.3536305, 0.0, 0.0, 0.0, 2.038948, 0.0, 1.53742, 0.0, 2.2303226, 3.650895, 2.1890895, 0.0, 2.4334733, 0.0, 0.89228547, 0.0, 1.7856202, 0.0, 1.7136467, 0.0, 0.0, 1.1337632, 0.59560424, 1.038701, 1.0488368, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 24, 24, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, 26, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1919701, 1.0, 0.9907159, -0.011875892, 0.017333394, -0.002747176, 0.79864544, 0.015907787, 1.0, -0.012634553, 0.54411215, 0.7014005, 0.52453405, -0.012647887, 1.0, 0.01964621, 0.37458783, 0.0145336045, 0.23177461, 0.014332457, 0.19926971, -0.010636567, 0.009043106, 0.48414886, 0.11933984, 1.0, 0.32287902, -0.012424517, -0.0019984671, -0.012151567, 0.010283332, -0.0030213788, -0.008684413, 0.004753182], "split_indices": [125, 143, 15, 141, 0, 0, 0, 141, 0, 39, 0, 139, 139, 143, 0, 105, 0, 143, 0, 143, 0, 143, 0, 0, 140, 143, 115, 143, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1883.0, 185.0, 1789.0, 94.0, 95.0, 90.0, 1698.0, 91.0, 1568.0, 130.0, 851.0, 717.0, 679.0, 172.0, 596.0, 121.0, 588.0, 91.0, 490.0, 106.0, 486.0, 102.0, 107.0, 383.0, 234.0, 252.0, 235.0, 148.0, 130.0, 104.0, 159.0, 93.0, 105.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00091380964, 0.008861047, -0.06609584, 0.000652344, 0.085720174, -0.0025531107, -0.011567492, 0.009623101, -0.010514779, 0.019589616, -0.002569371, -0.00042186756, 0.014066431, 0.013703641, -0.015521391, 0.0016873634, 0.012709531, 0.0067696944, -0.008650711, -0.008130089, 0.005743845, -0.034352235, 0.058376715, 0.0023646338, -0.011346681, -0.002162616, 0.011772159], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1], "loss_changes": [1.1050237, 1.1703447, 0.4424553, 1.5907035, 2.1972494, 0.0, 0.0, 2.0336914, 0.0, 0.0, 0.0, 3.1376514, 0.0, 1.7917473, 0.0, 0.811388, 0.0, 0.0, 1.0750488, 0.0, 1.810699, 2.2346156, 1.7614195, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 13, 13, 15, 15, 18, 18, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.36011788, 1.6152333, 0.9907159, -0.0025531107, -0.011567492, 0.9057718, -0.010514779, 0.019589616, -0.002569371, 0.79864544, 0.014066431, 1.4446453, -0.015521391, 0.17656, 0.012709531, 0.0067696944, -1.0, -0.008130089, 0.43657884, 0.2983946, 1.0, 0.0023646338, -0.011346681, -0.002162616, 0.011772159], "split_indices": [40, 125, 142, 138, 141, 0, 0, 139, 0, 0, 0, 141, 0, 138, 0, 140, 0, 0, 0, 0, 142, 142, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1855.0, 220.0, 1676.0, 179.0, 121.0, 99.0, 1545.0, 131.0, 90.0, 89.0, 1435.0, 110.0, 1315.0, 120.0, 1189.0, 126.0, 161.0, 1028.0, 170.0, 858.0, 487.0, 371.0, 281.0, 206.0, 158.0, 213.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "3.3844025E-9", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}