# Summary of 3_Optuna_Xgboost_Stacked

[<< Go back](../README.md)


## Extreme Gradient Boosting (Xgboost)
- **n_jobs**: -1
- **objective**: reg:squarederror
- **eta**: 0.1
- **max_depth**: 10
- **min_child_weight**: 88
- **subsample**: 0.970697557159987
- **colsample_bytree**: 0.8613105322932351
- **eval_metric**: mse
- **lambda**: 2.840098794801191e-06
- **alpha**: 3.0773599420974e-06
- **max_rounds**: 1000
- **early_stopping_rounds**: 50
- **explain_level**: 0

## Validation
 - **validation_type**: kfold
 - **k_folds**: 10
 - **shuffle**: True

## Optimized metric
mse

## Training time

15.2 seconds

### Metric details:
| Metric   |      Score |
|:---------|-----------:|
| MAE      |  2.08959   |
| MSE      | 74.182     |
| RMSE     |  8.6129    |
| R2       | -0.0116029 |
| MAPE     |  1.678e+15 |



## Learning curves
![Learning curves](learning_curves.png)
## True vs Predicted

![True vs Predicted](true_vs_predicted.png)


## Predicted vs Residuals

![Predicted vs Residuals](predicted_vs_residuals.png)



[<< Go back](../README.md)
