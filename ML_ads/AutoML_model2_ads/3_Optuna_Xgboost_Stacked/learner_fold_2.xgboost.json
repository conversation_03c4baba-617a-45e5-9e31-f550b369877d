{"learner": {"attributes": {"best_iteration": "23", "best_score": "0.610892"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "74"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.001560326, -0.14107186, 0.42918155, -0.24779911, 0.012400912, 0.31484303, 0.08532735, -0.34288406, -0.18595105, -0.04076985, 0.01865441, 0.0139139695, 0.42937028, -0.02445211, -0.3826885, -0.09871708, -0.29632872, -0.1219375, 0.020543834, 0.0322164, 0.05274349, -0.04585113, -0.031627435, -0.021487737, -0.014823521, -0.03644749, -0.01768815, -0.0024876385, -0.025135228, 0.009217757, -0.0097481245, -0.013596053, 0.010104663], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [126.315575, 25.437717, 25.117844, 5.3868294, 5.8981905, 8.21011, 0.0, 1.413414, 5.343918, 2.4286237, 0.0, 0.0, 2.5967445, 0.0, 1.294178, 3.0209801, 1.9942665, 2.6378396, 2.3503726, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5265095, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7612845, 0.4292953, 1.3316327, 0.22394206, 0.71983254, 0.80103934, 0.08532735, 1.176989, 1.2629046, 1.0, 0.01865441, 0.0139139695, 0.99982387, -0.02445211, 0.20015146, 0.22945473, 0.42746797, 1.0, 0.54138494, 0.0322164, 0.05274349, -0.04585113, -0.031627435, -0.021487737, 1.0, -0.03644749, -0.01768815, -0.0024876385, -0.025135228, 0.009217757, -0.0097481245, -0.013596053, 0.010104663], "split_indices": [139, 139, 139, 142, 140, 142, 0, 138, 138, 69, 0, 0, 139, 0, 139, 139, 142, 12, 140, 0, 0, 0, 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1553.0, 518.0, 916.0, 637.0, 408.0, 110.0, 361.0, 555.0, 488.0, 149.0, 161.0, 247.0, 104.0, 257.0, 310.0, 245.0, 210.0, 278.0, 118.0, 129.0, 120.0, 137.0, 130.0, 180.0, 156.0, 89.0, 120.0, 90.0, 173.0, 105.0, 88.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0056592347, -0.16558975, 0.2865252, -0.2134721, 0.048750527, 0.207953, 0.07349285, -0.28675908, -0.11537273, 0.014150432, -0.0041645095, -0.0042432286, 0.26452154, -0.018293537, -0.32843477, -0.00086644135, -0.17054164, 0.3132847, 0.004181965, -0.042385302, -0.29315147, -0.03306591, -0.0038270678, 0.20603894, 0.0492243, -0.25266722, -0.039574225, -0.00010065029, 0.30733538, -0.036240917, -0.017415948, 0.051533043, 0.012056427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, -1, -1, 25, -1, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [98.79332, 13.095741, 27.410515, 7.4985466, 1.9535974, 9.376522, 0.0, 2.5831718, 2.6255941, 0.0, 0.0, 0.0, 5.864212, 0.0, 1.4342041, 0.0, 6.226591, 8.502281, 0.0, 0.0, 1.2916775, 0.0, 0.0, 5.809512, 0.0, 1.9212761, 0.0, 0.0, 7.225626, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 12, 12, 14, 14, 16, 16, 17, 17, 20, 20, 23, 23, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, -1, -1, 26, -1, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [0.5968419, 0.5289018, 1.3316327, 0.33220515, 1.3280674, 0.5594404, 0.07349285, 0.0, 0.30261472, 0.014150432, -0.0041645095, -0.0042432286, 0.42307693, -0.018293537, 0.34615386, -0.00086644135, 1.0, -0.1923077, 0.004181965, -0.042385302, 0.30387777, -0.03306591, -0.0038270678, 0.7612845, 0.0492243, 0.14816526, -0.039574225, -0.00010065029, 1.007507, -0.036240917, -0.017415948, 0.051533043, 0.012056427], "split_indices": [139, 142, 139, 140, 138, 143, 0, 1, 143, 0, 0, 0, 1, 0, 1, 0, 13, 1, 0, 0, 141, 0, 0, 139, 0, 143, 0, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1276.0, 778.0, 1043.0, 233.0, 662.0, 116.0, 597.0, 446.0, 115.0, 118.0, 122.0, 540.0, 171.0, 426.0, 152.0, 294.0, 443.0, 97.0, 115.0, 311.0, 133.0, 161.0, 277.0, 166.0, 223.0, 88.0, 91.0, 186.0, 93.0, 130.0, 88.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00035049228, -0.11572726, 0.34813645, -0.21003655, 0.00097199733, 0.25518906, 0.068208896, -0.2983719, -0.1470404, 0.10088557, -0.06523866, 0.1220019, 0.3832289, -0.35557514, -0.020717835, 0.0045671733, -0.19143978, -0.015329311, 0.030983761, -0.12112262, 0.010851904, -0.0040667327, 0.030008195, 0.052140046, 0.02382171, -0.030272132, -0.041561294, -0.23948489, -0.0019233089, 0.007177899, -0.010441738, 0.0006619156, -0.027843429, -0.0055756313, 0.007821707, -0.31859073, -0.010290897, -0.024175415, -0.039542727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1, 37, -1, -1, -1], "loss_changes": [83.72782, 17.11406, 16.109768, 4.785717, 4.597664, 6.923622, 0.0, 1.8675308, 4.2952623, 6.7264853, 1.7774376, 5.764661, 4.1475563, 0.69810677, 0.0, 0.0, 3.3756647, 1.3813355, 0.0, 4.8429613, 0.7942121, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.4464588, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1925774, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 19, 19, 20, 20, 27, 27, 35, 35], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, 38, -1, -1, -1], "split_conditions": [0.7612845, 0.4080126, 1.3316327, 0.26289493, 1.0, 0.84796643, 0.068208896, 0.20106938, 0.20498092, 0.5968419, 0.5987796, 1.0, 1.0651944, 0.17896864, -0.020717835, 0.0045671733, 0.48850685, 1.0, 0.030983761, 0.50326043, 0.6427007, -0.0040667327, 0.030008195, 0.052140046, 0.02382171, -0.030272132, -0.041561294, 0.33220515, -0.0019233089, 0.007177899, -0.010441738, 0.0006619156, -0.027843429, -0.0055756313, 0.007821707, 0.26760083, -0.010290897, -0.024175415, -0.039542727], "split_indices": [139, 140, 139, 141, 53, 141, 0, 143, 143, 139, 140, 53, 141, 141, 0, 0, 142, 137, 0, 140, 142, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1555.0, 519.0, 860.0, 695.0, 406.0, 113.0, 358.0, 502.0, 277.0, 418.0, 199.0, 207.0, 220.0, 138.0, 94.0, 408.0, 178.0, 99.0, 241.0, 177.0, 104.0, 95.0, 106.0, 101.0, 117.0, 103.0, 319.0, 89.0, 90.0, 88.0, 133.0, 108.0, 89.0, 88.0, 202.0, 117.0, 101.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.0025838441, -0.099698745, 0.31022757, -0.17223907, 0.04679869, 0.2328911, 0.058158364, -0.2587728, -0.1183083, 0.019908408, 0.0089130495, 0.11257984, 0.3461939, -0.014680822, -0.30332208, -0.045165718, -0.17422892, -0.08546105, 0.13438773, -0.0046831477, 0.02893185, 0.023574863, 0.046556406, -0.34381333, -0.022647128, -0.015423389, 0.0045122486, -0.11933823, -0.027786255, -0.023951298, 0.014397795, 0.006152775, 0.020724766, -0.03076793, -0.038153563, -0.0017304849, -0.0284859], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, 21, -1, 23, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [64.82119, 16.429295, 10.786655, 4.8255024, 2.9539473, 5.4526424, 0.0, 1.9802094, 2.605444, 0.0, 4.85504, 5.465785, 2.7158756, 0.0, 0.8837452, 2.7179296, 2.0535555, 8.270853, 0.93430805, 0.0, 0.0, 0.0, 0.0, 0.2535286, 0.0, 0.0, 0.0, 3.9857192, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, 22, -1, 24, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7612845, 0.47360003, 1.3316327, 0.27548242, 1.3350061, 0.84796643, 0.058158364, 1.176989, 0.03846154, 0.019908408, 1.0, 1.0, -0.26923078, -0.014680822, 0.26911485, 1.0, 0.37509307, 1.0, 1.0, -0.0046831477, 0.02893185, 0.023574863, 0.046556406, 0.20452946, -0.022647128, -0.015423389, 0.0045122486, 1.0, -0.027786255, -0.023951298, 0.014397795, 0.006152775, 0.020724766, -0.03076793, -0.038153563, -0.0017304849, -0.0284859], "split_indices": [139, 139, 139, 141, 138, 141, 0, 138, 1, 0, 97, 53, 1, 0, 139, 5, 139, 12, 121, 0, 0, 0, 0, 141, 0, 0, 0, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1546.0, 514.0, 1034.0, 512.0, 400.0, 114.0, 397.0, 637.0, 102.0, 410.0, 194.0, 206.0, 113.0, 284.0, 276.0, 361.0, 234.0, 176.0, 102.0, 92.0, 107.0, 99.0, 186.0, 98.0, 125.0, 151.0, 236.0, 125.0, 140.0, 94.0, 88.0, 88.0, 95.0, 91.0, 146.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022489168, -0.09432822, 0.27381122, -0.16696823, 0.011811372, 0.113969706, 0.38633123, -0.2440095, -0.117166534, -0.027032508, 0.013528863, -0.0060532033, 0.026704142, 0.54173255, 0.015765123, -0.014884481, -0.2844732, 0.0036804993, -0.17096382, -0.07219868, 0.006767075, 0.035392534, 0.07358702, -0.03309594, -0.024284376, -0.069080494, -0.25699863, 0.011077106, -0.026509264, 0.004123861, -0.01642578, -0.037217524, -0.018301655, -0.013941578, 0.0109941065], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [52.643623, 11.973599, 9.3164215, 3.5375156, 3.026488, 5.716215, 10.803295, 1.3939571, 4.63862, 2.0531445, 0.0, 0.0, 0.0, 6.5993423, 0.0, 0.0, 0.491539, 0.0, 3.6376858, 5.220605, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9949766, 1.9172249, 3.3773794, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 16, 16, 18, 18, 19, 19, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7612845, 0.4292953, 0.84796643, 0.22394206, 0.71983254, 1.0, 1.1919701, 1.0, 1.2195088, 0.5968419, 0.013528863, -0.0060532033, 0.026704142, 1.5618138, 0.015765123, -0.014884481, 0.20452946, 0.0036804993, 1.0, 0.5338411, 0.006767075, 0.035392534, 0.07358702, -0.03309594, -0.024284376, 1.2703108, 0.3453729, 1.0, -0.026509264, 0.004123861, -0.01642578, -0.037217524, -0.018301655, -0.013941578, 0.0109941065], "split_indices": [139, 139, 141, 142, 140, 39, 143, 53, 138, 139, 0, 0, 0, 138, 0, 0, 141, 0, 97, 140, 0, 0, 0, 0, 0, 138, 141, 69, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1553.0, 518.0, 922.0, 631.0, 214.0, 304.0, 362.0, 560.0, 480.0, 151.0, 100.0, 114.0, 181.0, 123.0, 108.0, 254.0, 145.0, 415.0, 325.0, 155.0, 92.0, 89.0, 120.0, 134.0, 190.0, 225.0, 227.0, 98.0, 88.0, 102.0, 88.0, 137.0, 90.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0032354053, -0.081047416, 0.26003465, -0.15810111, 0.012885746, 0.0091376705, 0.34167486, -0.18069133, -0.004141204, 0.043965917, -0.0130828945, 0.020097792, 0.04831898, -0.20427862, -0.009061638, 0.18092263, -0.026848964, -0.008988785, -0.22520666, 0.0326889, 0.0063078175, -0.07132855, 0.0121247815, -0.17053218, -0.26902813, -0.016514845, -0.01970277, -0.024736423, -0.008773258, -0.02300618, -0.033957765, 0.00855669, -0.01216828], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, 21, -1, 23, -1, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [44.84588, 11.29112, 7.0498695, 2.259077, 3.1400738, 0.0, 6.86919, 1.5254803, 0.0, 5.605776, 0.0, 0.0, 0.0, 1.3621712, 0.0, 3.388661, 2.509755, 0.0, 1.1524334, 0.0, 0.0, 1.4191526, 0.0, 1.3613944, 0.73399734, 0.0, 2.0289915, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 16, 16, 18, 18, 21, 21, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, 22, -1, 24, -1, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4481664, 0.4080126, 0.80103934, 0.450694, 1.0, 0.0091376705, 1.0, 0.33220515, -0.004141204, 1.0, -0.0130828945, 0.020097792, 0.04831898, 0.091577284, -0.009061638, 0.57594323, 0.68101007, -0.008988785, 1.0, 0.0326889, 0.0063078175, 0.4423092, 0.0121247815, 0.25195393, 0.31571105, -0.016514845, 0.57097316, -0.024736423, -0.008773258, -0.02300618, -0.033957765, 0.00855669, -0.01216828], "split_indices": [138, 140, 142, 142, 113, 0, 113, 140, 0, 53, 0, 0, 0, 139, 0, 140, 142, 0, 126, 0, 0, 141, 0, 141, 141, 0, 143, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1560.0, 512.0, 857.0, 703.0, 167.0, 345.0, 718.0, 139.0, 578.0, 125.0, 173.0, 172.0, 569.0, 149.0, 197.0, 381.0, 88.0, 481.0, 88.0, 109.0, 293.0, 88.0, 214.0, 267.0, 104.0, 189.0, 111.0, 103.0, 172.0, 95.0, 93.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.004867062, -0.08640123, 0.19841559, -0.13615784, 0.004056656, 0.0556359, 0.29427615, -0.09225124, -0.1948802, -0.0693063, 0.11305305, 0.022559216, -0.009664488, 0.17660327, 0.046506524, -0.06285969, -0.022406785, -0.16265556, -0.030486425, 0.004496263, -0.018654326, -0.0047881757, 0.025387106, 0.03308546, 0.0037777058, 0.0042151413, -0.10776799, -0.02429717, -0.009634804, -0.15935473, 0.0013155726, -0.00606567, -0.027368804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [34.16001, 6.6207905, 8.0752945, 2.446806, 4.174068, 6.1338143, 7.094326, 2.1037407, 1.4389448, 4.179722, 4.7591286, 0.0, 0.0, 4.4755507, 0.0, 2.0938466, 0.0, 1.672226, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.940037, 0.0, 0.0, 2.4600143, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [0.6985078, 0.44331938, 0.831369, 1.0, 0.6003935, 0.65763235, 1.0, 1.0, 1.0, 0.5338411, 1.0, 0.022559216, -0.009664488, 1.068056, 0.046506524, 0.24130851, -0.022406785, 1.0, -0.030486425, 0.004496263, -0.018654326, -0.0047881757, 0.025387106, 0.03308546, 0.0037777058, 0.0042151413, 0.48850685, -0.02429717, -0.009634804, 0.30028036, 0.0013155726, -0.00606567, -0.027368804], "split_indices": [140, 140, 139, 23, 139, 139, 0, 40, 44, 140, 126, 0, 0, 140, 0, 139, 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 142, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1471.0, 590.0, 949.0, 522.0, 237.0, 353.0, 543.0, 406.0, 312.0, 210.0, 112.0, 125.0, 209.0, 144.0, 444.0, 99.0, 314.0, 92.0, 158.0, 154.0, 98.0, 112.0, 99.0, 110.0, 133.0, 311.0, 142.0, 172.0, 218.0, 93.0, 117.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007726524, -0.074776694, 0.18524429, -0.12458985, -0.0042894376, 0.044109903, 0.2671933, -0.18601513, -0.08047089, -0.06352103, 0.09408649, 0.017936386, -0.012182019, 0.37771982, 0.008403501, -0.02615948, -0.15756498, 0.012091349, -0.124250114, 0.0155382, -0.018056978, 0.021783842, -0.0031836492, 0.019199437, 0.05984878, -0.0035786927, -0.022199405, -0.15682358, -0.0014871804, -0.01188273, 0.012239583, -0.22403234, -0.004234084, -0.015949633, -0.028792936], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, 23, -1, -1, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [28.440567, 5.189542, 6.800701, 2.3468761, 3.5661008, 4.8476243, 7.530712, 0.77838993, 4.443491, 3.5349457, 3.584139, 0.0, 0.0, 9.512516, 0.0, 0.0, 2.06351, 0.0, 1.4750113, 3.2736194, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.454463, 0.0, 0.0, 0.0, 0.82885456, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 16, 16, 18, 18, 19, 19, 27, 27, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, 24, -1, -1, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.6985078, 0.4080126, 0.80736697, 0.26289493, 0.5968419, 1.0, 1.1489253, 0.13796628, 0.20498092, 0.5338411, 0.64012194, 0.017936386, -0.012182019, 0.9850181, 0.008403501, -0.02615948, 0.1785376, 0.012091349, 0.49296728, 1.0, -0.018056978, 0.021783842, -0.0031836492, 0.019199437, 0.05984878, -0.0035786927, -0.022199405, 0.33220515, -0.0014871804, -0.01188273, 0.012239583, 1.0, -0.004234084, -0.015949633, -0.028792936], "split_indices": [140, 140, 139, 141, 139, 108, 143, 142, 143, 140, 141, 0, 0, 141, 0, 0, 142, 0, 142, 124, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1478.0, 588.0, 866.0, 612.0, 216.0, 372.0, 362.0, 504.0, 382.0, 230.0, 119.0, 97.0, 232.0, 140.0, 99.0, 263.0, 90.0, 414.0, 228.0, 154.0, 116.0, 114.0, 126.0, 106.0, 91.0, 172.0, 319.0, 95.0, 101.0, 127.0, 201.0, 118.0, 100.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0035720742, -0.06730042, 0.1803336, -0.16795075, -0.033201803, 0.06826663, 0.25536147, -0.00580679, -0.21879849, -0.047398735, 0.013022423, 0.02061028, -0.0059483955, 0.35538802, 0.009692522, -0.026310844, -0.015446383, 0.008895066, -0.068543, 0.017462794, 0.059640147, -0.123161286, -0.011505715, -0.0530459, -0.028232832, -0.06510079, 0.016482206, -0.14599761, 0.013466238, -0.011890724, -0.021082377, -0.022280896, -0.0070649288, -0.017318368, 0.008769955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, 19, -1, -1, -1, -1, 21, -1, -1, 23, 25, 27, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [25.869349, 5.0588226, 4.9692154, 2.0840597, 2.5544834, 4.173251, 5.610132, 0.0, 0.7269182, 2.9204865, 0.0, 0.0, 0.0, 9.453732, 0.0, 0.0, 0.0, 0.0, 2.732099, 0.0, 0.0, 4.999705, 4.0541787, 5.4262676, 0.0, 2.5510426, 0.0, 1.2038212, 0.0, 3.8712335, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 13, 13, 18, 18, 21, 21, 22, 22, 23, 23, 25, 25, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, 20, -1, -1, -1, -1, 22, -1, -1, 24, 26, 28, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.6985078, 0.22394206, 0.831369, 1.0, 0.778775, 0.65763235, 1.1489253, -0.00580679, 0.46153846, 1.2195088, 0.013022423, 0.02061028, -0.0059483955, -0.15384616, 0.009692522, -0.026310844, -0.015446383, 0.008895066, 1.0, 0.017462794, 0.059640147, 1.3556404, 0.6287483, 0.4113523, -0.028232832, 0.5275487, 0.016482206, 0.33910778, 0.013466238, -0.15384616, -0.021082377, -0.022280896, -0.0070649288, -0.017318368, 0.008769955], "split_indices": [140, 142, 139, 53, 142, 139, 143, 0, 1, 138, 0, 0, 0, 1, 0, 0, 0, 0, 39, 0, 0, 138, 139, 139, 0, 140, 0, 140, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1474.0, 591.0, 373.0, 1101.0, 237.0, 354.0, 118.0, 255.0, 1013.0, 88.0, 114.0, 123.0, 217.0, 137.0, 151.0, 104.0, 136.0, 877.0, 124.0, 93.0, 448.0, 429.0, 311.0, 137.0, 329.0, 100.0, 208.0, 103.0, 241.0, 88.0, 103.0, 105.0, 92.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056390576, -0.066908464, 0.14675954, -0.10144268, 0.010018947, 0.102381036, 0.034162153, -0.03934719, -0.13811782, 0.016770298, -0.044868108, 0.044951912, 0.030178769, -0.1129053, 0.005663715, -0.17124292, 0.0034582913, -0.02030985, 0.023231035, 0.12540403, -0.011403677, -0.0025086142, -0.020072443, -0.13686773, -0.032522965, -0.0070540956, 0.01658842, -0.0013274912, 0.03320357, -0.17629786, -0.0029604414, -0.00519936, -0.24110101, -0.02073999, -0.028641025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, -1, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1], "loss_changes": [19.309685, 3.9185266, 5.1280766, 2.3183527, 3.955249, 5.5311937, 0.0, 2.6688416, 3.6612663, 0.0, 3.6528459, 4.796616, 0.0, 1.6504104, 0.0, 2.8425121, 0.0, 0.0, 3.1703186, 7.13521, 0.0, 0.0, 0.0, 1.8567085, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5857525, 0.0, 0.0, 0.32219124, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 15, 15, 18, 18, 19, 19, 23, 23, 29, 29, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, -1, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1], "split_conditions": [0.6985078, 0.48706603, 1.3316327, 0.03846154, 0.46798277, 1.0, 0.034162153, 1.0, 1.0, 0.016770298, 0.5000298, 0.8833981, 0.030178769, 0.32231632, 0.005663715, 0.40435973, 0.0034582913, -0.02030985, 0.69685656, 1.0, -0.011403677, -0.0025086142, -0.020072443, 0.31032252, -0.032522965, -0.0070540956, 0.01658842, -0.0013274912, 0.03320357, 1.1851524, -0.0029604414, -0.00519936, 0.96153843, -0.02073999, -0.028641025], "split_indices": [140, 139, 139, 1, 141, 125, 0, 124, 62, 0, 140, 143, 0, 140, 0, 142, 0, 0, 142, 109, 0, 0, 0, 139, 0, 0, 0, 0, 0, 138, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1475.0, 593.0, 1018.0, 457.0, 483.0, 110.0, 378.0, 640.0, 118.0, 339.0, 375.0, 108.0, 214.0, 164.0, 537.0, 103.0, 102.0, 237.0, 249.0, 126.0, 107.0, 107.0, 439.0, 98.0, 143.0, 94.0, 149.0, 100.0, 321.0, 118.0, 110.0, 211.0, 121.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00205091, -0.056013104, 0.14162461, -0.10001931, 0.0016221486, 0.08173104, 0.038551558, -0.14714117, -0.065896586, -0.04014807, 0.017330764, -0.009374666, 0.18455483, -0.020321237, -0.10211428, 0.011199479, -0.10725634, 0.0061853323, -0.024575256, -0.0037516512, 0.31606078, -0.017845213, -0.003585875, -0.15233773, -0.0035243195, -0.07736734, 0.097110316, 0.049101815, 0.01468712, -0.022116942, -0.010108005, 0.01132944, -0.020257806, 0.02214942, -0.002973658], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, 19, -1, 21, -1, 23, 25, -1, -1, 27, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [15.986781, 3.8019285, 8.224024, 1.3667364, 4.6542, 8.155562, 0.0, 0.9013219, 3.6272683, 4.972758, 0.0, 0.0, 8.323059, 0.0, 1.0014448, 0.0, 1.2985811, 3.2363331, 0.0, 0.0, 5.2985706, 0.0, 0.0, 0.8679247, 0.0, 5.2997828, 3.2186534, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 14, 14, 16, 16, 17, 17, 20, 20, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, 20, -1, 22, -1, 24, 26, -1, -1, 28, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7106174, 0.4080126, 1.0, 0.26289493, 1.2692307, 0.9301657, 0.038551558, 0.30769232, 0.20498092, 0.68233967, 0.017330764, -0.009374666, 1.0, -0.020321237, 0.15563406, 0.011199479, 1.0, 1.0, -0.024575256, -0.0037516512, 1.1489253, -0.017845213, -0.003585875, 1.0, -0.0035243195, -0.26923078, 0.49153346, 0.049101815, 0.01468712, -0.022116942, -0.010108005, 0.01132944, -0.020257806, 0.02214942, -0.002973658], "split_indices": [142, 140, 115, 141, 1, 142, 0, 1, 143, 141, 0, 0, 50, 0, 143, 0, 93, 124, 0, 0, 143, 0, 0, 111, 0, 1, 139, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1499.0, 563.0, 850.0, 649.0, 452.0, 111.0, 357.0, 493.0, 522.0, 127.0, 167.0, 285.0, 159.0, 198.0, 93.0, 400.0, 426.0, 96.0, 106.0, 179.0, 92.0, 106.0, 246.0, 154.0, 222.0, 204.0, 88.0, 91.0, 105.0, 141.0, 88.0, 134.0, 103.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0064997096, -0.03934691, 0.22241217, -0.08442494, 0.04007628, 0.032446004, 0.009146576, -0.07018012, -0.023521857, -0.0012951015, 0.021946533, -0.08608224, 0.008751593, 0.033179697, -0.017173234, -0.045360226, -0.14084633, 0.11480501, -0.010155124, -0.08612491, 0.007516142, -0.18176809, -0.0010487275, -0.002774883, 0.021042041, -0.015695756, -0.1395539, -0.012578353, -0.20662104, -0.008292521, 0.005153367, -0.023464998, -0.0022512572, -0.02733253, -0.015631944], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, 25, -1, 27, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [15.459299, 6.437273, 3.4476023, 2.463787, 4.8314447, 0.0, 0.0, 2.6280718, 0.0, 3.108293, 0.0, 2.1230583, 0.0, 4.83888, 0.0, 2.6825128, 2.1658163, 3.7347145, 0.0, 1.5352871, 0.0, 0.42993546, 0.0, 0.0, 0.0, 0.79548454, 2.5821981, 0.0, 0.7180395, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, 26, -1, 28, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0194759, 0.56020635, 1.1489253, 0.5907737, 1.0, 0.032446004, 0.009146576, 3.0384614, -0.023521857, 0.91372913, 0.021946533, 1.0, 0.008751593, 0.15384616, -0.017173234, 1.0, 1.0, 0.5710635, -0.010155124, 0.2638264, 0.007516142, 0.13752887, -0.0010487275, -0.002774883, 0.021042041, 0.2490458, 0.4292953, -0.012578353, 0.24535228, -0.008292521, 0.005153367, -0.023464998, -0.0022512572, -0.02733253, -0.015631944], "split_indices": [139, 143, 143, 141, 42, 0, 0, 1, 0, 141, 0, 23, 0, 1, 0, 62, 105, 140, 0, 143, 0, 139, 0, 0, 0, 140, 139, 0, 139, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1798.0, 258.0, 1147.0, 651.0, 145.0, 113.0, 1048.0, 99.0, 529.0, 122.0, 952.0, 96.0, 440.0, 89.0, 546.0, 406.0, 274.0, 166.0, 408.0, 138.0, 309.0, 97.0, 110.0, 164.0, 176.0, 232.0, 95.0, 214.0, 88.0, 88.0, 128.0, 104.0, 92.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026255744, -0.052936457, 0.124010175, -0.083968386, 0.017297303, 0.07973366, 0.031997472, -0.058881164, -0.15992144, 0.018610055, -0.03252141, 0.022573426, 0.029044196, -0.08949444, 0.008751011, -0.004780496, -0.025121624, 0.009062413, -0.08845002, -0.059910703, 0.013763092, -0.12672064, -0.011228598, 0.0019499923, -0.015322002, 0.0039109588, -0.016940432, -0.0010840197, -0.15687665, 0.011582439, -0.012312851, -0.008907626, -0.19544199, -0.025773425, -0.015080737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, -1, 21, -1, -1, -1, -1, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [13.130953, 3.214747, 5.084502, 1.949276, 3.8011217, 5.757097, 0.0, 3.4462862, 2.5998545, 0.0, 2.4036884, 3.5683973, 0.0, 1.8530107, 0.0, 0.0, 0.0, 0.0, 1.6780605, 2.3744183, 0.0, 1.5061255, 2.9145296, 0.0, 0.0, 0.0, 0.0, 0.0, 0.89424133, 0.0, 0.0, 0.0, 0.6061249, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 18, 18, 19, 19, 21, 21, 22, 22, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, -1, 22, -1, -1, -1, -1, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [0.6985078, 0.48706603, 1.3316327, 0.3730132, 1.0, 1.0, 0.031997472, 0.3935085, 0.03846154, 0.018610055, 0.0, 1.0, 0.029044196, 0.30363175, 0.008751011, -0.004780496, -0.025121624, 0.009062413, -0.23076923, 1.0, 0.013763092, 0.091577284, 1.0, 0.0019499923, -0.015322002, 0.0039109588, -0.016940432, -0.0010840197, 0.16967288, 0.011582439, -0.012312851, -0.008907626, 1.0, -0.025773425, -0.015080737], "split_indices": [140, 139, 139, 139, 17, 125, 0, 142, 1, 0, 0, 53, 0, 140, 0, 0, 0, 0, 1, 15, 0, 139, 81, 0, 0, 0, 0, 0, 140, 0, 0, 0, 81, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1475.0, 586.0, 1023.0, 452.0, 478.0, 108.0, 769.0, 254.0, 103.0, 349.0, 376.0, 102.0, 636.0, 133.0, 114.0, 140.0, 109.0, 240.0, 219.0, 157.0, 431.0, 205.0, 90.0, 150.0, 115.0, 104.0, 89.0, 342.0, 96.0, 109.0, 124.0, 218.0, 91.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0037351279, -0.059100073, 0.08528126, -0.07358554, 0.009067682, 0.03219309, 0.18732084, -0.04617674, -0.14546372, 0.08641097, -0.013494752, 0.0470655, 0.04327677, -0.06900257, 0.010353384, -0.026400594, -0.072514646, 0.13383862, -0.010171869, -0.016995655, 0.023904657, -0.102708854, 0.005702209, 0.0048573357, -0.019904485, 0.051254213, 0.03374934, 0.0010235211, -0.15046608, 0.008463241, -0.009778402, 0.013544952, -0.006256532, -0.12110497, -0.022799255, -0.017585387, -0.0032898362], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, 23, 25, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [10.596364, 2.534079, 4.8753843, 2.0981503, 0.0, 5.3647103, 10.602971, 2.634714, 2.5423765, 3.9883795, 0.0, 8.166169, 0.0, 1.684556, 0.0, 0.0, 2.788475, 6.0042777, 0.0, 0.0, 0.0, 2.4865856, 1.6989833, 0.0, 0.0, 2.4341, 0.0, 0.0, 0.7375088, 0.0, 0.0, 0.0, 0.0, 1.1348648, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 21, 21, 22, 22, 25, 25, 28, 28, 33, 33], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, 24, 26, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [0.56020635, 3.5, 0.9271665, 1.3148584, 0.009067682, 0.46153846, -0.07692308, 0.4349057, 0.50496, 0.79649323, -0.013494752, 1.0, 0.04327677, 0.2840416, 0.010353384, -0.026400594, 0.65763235, 0.7612845, -0.010171869, -0.016995655, 0.023904657, 1.176989, 0.30028036, 0.0048573357, -0.019904485, 0.63716984, 0.03374934, 0.0010235211, 0.30768904, 0.008463241, -0.009778402, 0.013544952, -0.006256532, 0.20505655, -0.022799255, -0.017585387, -0.0032898362], "split_indices": [143, 1, 140, 138, 0, 1, 1, 142, 139, 140, 0, 61, 0, 139, 0, 0, 139, 139, 0, 0, 0, 138, 142, 0, 0, 141, 0, 0, 140, 0, 0, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1168.0, 900.0, 1065.0, 103.0, 592.0, 308.0, 771.0, 294.0, 447.0, 145.0, 196.0, 112.0, 669.0, 102.0, 112.0, 182.0, 357.0, 90.0, 92.0, 104.0, 461.0, 208.0, 93.0, 89.0, 254.0, 103.0, 137.0, 324.0, 118.0, 90.0, 146.0, 108.0, 235.0, 89.0, 145.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016032533, -0.056452546, 0.06978014, -0.0688245, 0.0067733848, 0.020288454, 0.16243869, -0.0463126, -0.12669066, 0.07579828, -0.014853797, 0.279471, -0.0028353508, -0.06759712, 0.01096196, -0.023727804, -0.053171646, 0.11343657, -0.0075182565, 0.009378361, 0.05031399, -0.021095611, -0.118431054, 0.0062474855, -0.016500566, 0.03986698, 0.029447544, 0.0057040593, -0.09325419, -0.074556194, -0.017693086, 0.0112908315, -0.00704254, -0.0011595959, -0.017763434, -0.018138645, 0.0029976591], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, 23, 25, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.100815, 1.7976215, 4.122658, 1.3860483, 0.0, 5.4917135, 6.9889336, 2.5423093, 2.422821, 2.5060537, 0.0, 8.057303, 0.0, 1.5932374, 0.0, 0.0, 2.3150454, 4.70159, 0.0, 0.0, 0.0, 1.9846457, 0.82646656, 0.0, 0.0, 2.0220315, 0.0, 0.0, 1.2609305, 2.0547771, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 21, 21, 22, 22, 25, 25, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, 24, 26, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.56020635, 3.5, 0.9271665, 1.3148584, 0.0067733848, 0.46153846, 1.1919701, 0.4113523, 0.5122769, 0.79649323, -0.014853797, -0.07692308, -0.0028353508, 1.0, 0.01096196, -0.023727804, 0.65763235, 0.7612845, -0.0075182565, 0.009378361, 0.05031399, 1.0, 0.22211815, 0.0062474855, -0.016500566, 1.3781409, 0.029447544, 0.0057040593, 0.23213835, 0.11508339, -0.017693086, 0.0112908315, -0.00704254, -0.0011595959, -0.017763434, -0.018138645, 0.0029976591], "split_indices": [143, 1, 140, 138, 0, 1, 143, 139, 139, 140, 0, 1, 0, 23, 0, 0, 139, 139, 0, 0, 0, 69, 143, 0, 0, 138, 0, 0, 143, 143, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1170.0, 899.0, 1064.0, 106.0, 586.0, 313.0, 766.0, 298.0, 441.0, 145.0, 194.0, 119.0, 674.0, 92.0, 119.0, 179.0, 353.0, 88.0, 106.0, 88.0, 352.0, 322.0, 88.0, 91.0, 251.0, 102.0, 169.0, 183.0, 184.0, 138.0, 151.0, 100.0, 93.0, 90.0, 91.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00083941966, -0.014396092, 0.02577417, -0.029577976, 0.015602054, -0.07080955, 0.0071398253, -0.04024129, -0.10975943, 0.056002207, -0.07366053, -0.0021196902, -0.016345574, -0.0037909949, -0.15776646, 0.013110251, 0.018702831, -0.13532983, 0.0076954844, -0.06163875, 0.015821734, -0.025315544, -0.0082981475, 0.013681232, -0.054091204, -0.020090796, -0.0046677873, 0.0024868231, -0.0108686425, 0.008041009, -0.014427707], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [8.109886, 5.0606523, 0.0, 2.7190235, 0.0, 1.0072722, 3.7506928, 2.22644, 1.2831316, 3.3270187, 3.3252273, 3.4546056, 0.0, 0.0, 1.590806, 3.70758, 0.0, 1.4766612, 0.0, 1.0744678, 0.0, 0.0, 0.0, 0.0, 3.5056036, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 17, 17, 19, 19, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3316327, 1.0, 0.02577417, 0.4080126, 0.015602054, 1.0, 0.6887748, 1.3181741, 0.19545476, 1.4138024, 0.9850181, 1.0, -0.016345574, -0.0037909949, 0.24535228, 0.48414886, 0.018702831, 1.0, 0.0076954844, 1.0, 0.015821734, -0.025315544, -0.0082981475, 0.013681232, 1.0, -0.020090796, -0.0046677873, 0.0024868231, -0.0108686425, 0.008041009, -0.014427707], "split_indices": [139, 125, 0, 140, 0, 23, 141, 138, 140, 138, 141, 62, 0, 0, 139, 140, 0, 12, 0, 53, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1956.0, 116.0, 1796.0, 160.0, 846.0, 950.0, 474.0, 372.0, 592.0, 358.0, 362.0, 112.0, 149.0, 223.0, 446.0, 146.0, 254.0, 104.0, 264.0, 98.0, 98.0, 125.0, 157.0, 289.0, 146.0, 108.0, 93.0, 171.0, 116.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0029093365, -0.05749402, 0.051052347, -0.03535291, -0.018366575, 0.026591009, 0.18455319, -0.060010068, 0.009409715, 0.0020025831, 0.021407774, 0.02237234, 0.014493781, -0.1123502, 0.00085053925, 0.016096836, -0.024745418, -0.0038627055, -0.16636516, -0.012333171, 0.012421041, -0.020361556, 0.009338952, -0.020083947, -0.012170525, -0.031487808, 0.024704145, 0.01344159, -0.08792053, -0.16745496, 0.0062766466, -0.0039982772, -0.025791908, -0.010278505, 0.011412659], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, 19, -1, 21, -1, 23, -1, -1, -1, 25, -1, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1], "loss_changes": [5.9730306, 2.544953, 3.732591, 2.4737, 0.0, 4.453262, 0.27465725, 2.0737298, 0.0, 3.6312227, 0.0, 0.0, 0.0, 1.3937526, 4.611051, 0.0, 4.4566708, 0.0, 0.31100225, 0.0, 0.0, 0.0, 5.9586396, 0.0, 0.0, 4.9058967, 0.0, 0.0, 2.9293408, 2.4447098, 2.1054516, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 16, 16, 18, 18, 22, 22, 25, 25, 28, 28, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, 20, -1, 22, -1, 24, -1, -1, -1, 26, -1, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1], "split_conditions": [0.4292953, 0.3730132, 1.1185367, 1.0, -0.018366575, 1.2692307, 1.2121096, 1.0, 0.009409715, 1.3243612, 0.021407774, 0.02237234, 0.014493781, 0.19545476, 0.22765444, 0.016096836, -0.5, -0.0038627055, 0.25646102, -0.012333171, 0.012421041, -0.020361556, 0.9648798, -0.020083947, -0.012170525, -0.30769232, 0.024704145, 0.01344159, 1.0, 0.6287483, 0.6398453, -0.0039982772, -0.025791908, -0.010278505, 0.011412659], "split_indices": [139, 139, 142, 71, 0, 1, 140, 115, 0, 138, 0, 0, 0, 140, 139, 0, 1, 0, 143, 0, 0, 0, 141, 0, 0, 1, 0, 0, 124, 139, 143, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 911.0, 1143.0, 775.0, 136.0, 966.0, 177.0, 651.0, 124.0, 854.0, 112.0, 89.0, 88.0, 350.0, 301.0, 123.0, 731.0, 148.0, 202.0, 150.0, 151.0, 117.0, 614.0, 114.0, 88.0, 524.0, 90.0, 133.0, 391.0, 212.0, 179.0, 88.0, 124.0, 89.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0038484319, -0.009869129, 0.023728555, -0.0006967627, -0.017668027, 0.014312954, -0.12490218, 0.0010594914, 0.01635371, -0.0060620666, -0.020036306, 0.0115219625, -0.015947752, -0.027852627, 0.084522456, -0.007688334, -0.018916693, 0.14305702, -0.018640663, -0.043149594, 0.08066428, 0.05633538, 0.029689366, 0.013053477, -0.015979594, 0.027951824, -0.08753888, 0.02129828, -0.0036834541, -0.0072543505, 0.022622121, 0.011913084, -0.004705025, -0.002853768, -0.0159571], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.6349344, 2.994313, 0.0, 3.4582548, 0.0, 3.2731535, 0.97014713, 2.5530126, 0.0, 0.0, 0.0, 4.1017184, 0.0, 3.0153344, 3.0193045, 2.5816703, 0.0, 4.2557673, 3.8112988, 1.8558114, 3.6691566, 4.4665184, 0.0, 0.0, 0.0, 1.5455273, 1.5384932, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3316327, 1.0, 0.023728555, 1.0, -0.017668027, 1.0, 0.3567492, 1.0716654, 0.01635371, -0.0060620666, -0.020036306, 0.56020635, -0.015947752, 0.5907737, 0.15384616, 0.41328812, -0.018916693, -0.1923077, 1.0, 1.2195088, 0.5000298, 1.0, 0.029689366, 0.013053477, -0.015979594, 0.16432898, 0.30665743, 0.02129828, -0.0036834541, -0.0072543505, 0.022622121, 0.011913084, -0.004705025, -0.002853768, -0.0159571], "split_indices": [139, 117, 0, 40, 0, 125, 142, 143, 0, 0, 0, 143, 0, 141, 1, 140, 0, 1, 115, 138, 140, 97, 0, 0, 0, 143, 142, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1957.0, 115.0, 1855.0, 102.0, 1655.0, 200.0, 1520.0, 135.0, 108.0, 92.0, 1427.0, 93.0, 927.0, 500.0, 824.0, 103.0, 319.0, 181.0, 588.0, 236.0, 204.0, 115.0, 88.0, 93.0, 226.0, 362.0, 111.0, 125.0, 116.0, 88.0, 102.0, 124.0, 199.0, 163.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002812195, -0.02416748, 0.09371208, -0.01400957, -0.019628124, 0.17343128, -0.0043734806, -0.05550116, 0.036783967, 0.049302902, 0.04412872, -0.08614507, 0.027401555, 0.017556233, -0.0053833006, 0.016328836, -0.0051087933, -0.13185912, -0.0024071857, 0.011458595, -0.0042908415, -0.06537917, 0.0151272565, -0.08426551, -0.024104442, 0.014420635, -0.013101557, -0.022395773, -0.018731164, -0.0015627852, -0.015546425, 0.0063065044, -0.010543219], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, -1, 21, -1, -1, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [5.0790224, 2.7920604, 5.1936917, 3.1781173, 0.0, 9.974555, 0.0, 2.1085854, 3.9675915, 2.3458347, 0.0, 2.3197665, 1.3731053, 0.0, 4.8873267, 0.0, 0.0, 2.037036, 4.0351257, 0.0, 0.0, 1.9706426, 0.0, 1.3341272, 0.0, 0.0, 0.0, 1.9727882, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 17, 17, 18, 18, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, -1, 22, -1, -1, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.793871, 0.8589071, 1.1489253, 1.0, -0.019628124, 0.95420814, -0.0043734806, 1.0, 0.0, 1.0, 0.04412872, 1.0, 1.0, 0.017556233, 0.88461536, 0.016328836, -0.0051087933, 0.40963307, 1.0, 0.011458595, -0.0042908415, 2.0, 0.0151272565, 1.0, -0.024104442, 0.014420635, -0.013101557, 1.0, -0.018731164, -0.0015627852, -0.015546425, 0.0063065044, -0.010543219], "split_indices": [143, 141, 143, 39, 0, 140, 0, 42, 0, 69, 0, 116, 69, 0, 1, 0, 0, 140, 122, 0, 0, 0, 0, 23, 0, 0, 0, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1597.0, 474.0, 1508.0, 89.0, 300.0, 174.0, 830.0, 678.0, 205.0, 95.0, 606.0, 224.0, 158.0, 520.0, 96.0, 109.0, 392.0, 214.0, 100.0, 124.0, 376.0, 144.0, 273.0, 119.0, 100.0, 114.0, 278.0, 98.0, 139.0, 134.0, 137.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00017623091, -0.023782132, 0.08335635, -0.0118897855, -0.095553465, 0.16510022, -0.005705073, -0.038752332, 0.14243817, 0.0011107738, -0.018822635, 0.04685481, 0.037885148, -0.011118179, -0.0822989, 0.02621678, -0.0009219328, -0.0032210548, 0.012262581, -0.07803041, 0.055607658, 0.005416273, -0.11879004, -0.0010655824, -0.13125631, 0.012539989, -0.010034739, -0.00053885987, -0.15561411, -0.01768826, -0.0077695004, 0.007821051, -0.01011892, -0.21706003, -0.0027834491, -0.014970583, -0.028224146], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, 17, -1, 19, 21, -1, -1, -1, -1, 23, 25, -1, 27, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [4.1172476, 1.369061, 5.3025675, 5.7044034, 2.2536898, 7.3803306, 0.0, 1.4103521, 3.7042117, 0.0, 0.0, 1.1262822, 0.0, 3.2012434, 2.2657366, 0.0, 0.0, 0.0, 0.0, 1.2838137, 1.6446975, 0.0, 1.4991488, 0.0, 0.48875976, 0.0, 1.4881307, 0.0, 2.1277666, 0.0, 0.0, 0.0, 0.0, 0.80341244, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 19, 19, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, 18, -1, 20, 22, -1, -1, -1, -1, 24, 26, -1, 28, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [0.793871, 0.67325324, 1.1489253, 0.56020635, -0.03846154, 0.9271665, -0.005705073, 0.3250815, 1.0, 0.0011107738, -0.018822635, 1.0, 0.037885148, 0.22394206, 1.0, 0.02621678, -0.0009219328, -0.0032210548, 0.012262581, 0.15303391, 0.34726515, 0.005416273, 0.0, -0.0010655824, 0.25195393, 0.012539989, 1.0, -0.00053885987, 1.0, -0.01768826, -0.0077695004, 0.007821051, -0.01011892, 0.4292335, -0.0027834491, -0.014970583, -0.028224146], "split_indices": [143, 143, 143, 143, 1, 140, 0, 143, 106, 0, 0, 105, 0, 142, 89, 0, 0, 0, 0, 142, 141, 0, 0, 0, 141, 0, 97, 0, 121, 0, 0, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1604.0, 462.0, 1376.0, 228.0, 292.0, 170.0, 1172.0, 204.0, 106.0, 122.0, 188.0, 104.0, 717.0, 455.0, 114.0, 90.0, 92.0, 96.0, 358.0, 359.0, 96.0, 359.0, 158.0, 200.0, 174.0, 185.0, 88.0, 271.0, 108.0, 92.0, 94.0, 91.0, 183.0, 88.0, 90.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0041497876, -0.050649732, 0.047467507, 0.0053395927, -0.071104325, 0.030170092, 0.021179292, -0.11516318, -0.019114865, 0.010232482, 0.02067872, -0.15172628, -0.0034667656, 0.04653264, -0.009105197, 0.038750306, -0.0964933, -0.1091883, -0.023635441, 0.016277099, -0.0065956055, -0.015081754, 0.15538645, 0.0045309574, -0.026665676, -0.014144137, -0.0075894794, 0.026301583, -0.01856313, 0.027040157, 0.0011617494, -0.0054098754, 0.08315372, 0.018997455, -0.0035956192], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, -1, 7, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [4.908998, 1.9430571, 3.282977, 0.0, 1.7477243, 3.6797817, 0.0, 1.2155275, 1.652872, 2.8579278, 0.0, 1.0223732, 0.0, 2.392817, 0.0, 4.6525645, 4.777675, 0.20295072, 0.0, 0.0, 0.0, 3.5783608, 3.869331, 0.0, 0.0, 0.0, 0.0, 1.8649402, 0.0, 0.0, 0.0, 0.0, 3.0408978, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 27, 27, 32, 32], "right_children": [2, 4, 6, -1, 8, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [0.4292953, 1.176989, 1.2692307, 0.0053395927, 1.0, 1.249012, 0.021179292, 0.32566002, 0.3339218, 0.991673, 0.02067872, 0.29398257, -0.0034667656, 1.0, -0.009105197, 0.6985078, 1.0, 1.0, -0.023635441, 0.016277099, -0.0065956055, 0.46153846, 1.0, 0.0045309574, -0.026665676, -0.014144137, -0.0075894794, 0.5485319, -0.01856313, 0.027040157, 0.0011617494, -0.0054098754, 0.64012194, 0.018997455, -0.0035956192], "split_indices": [139, 138, 1, 0, 17, 142, 0, 139, 140, 140, 0, 142, 0, 108, 0, 140, 69, 59, 0, 0, 0, 1, 126, 0, 0, 0, 0, 143, 0, 0, 0, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 913.0, 1155.0, 150.0, 763.0, 1045.0, 110.0, 413.0, 350.0, 939.0, 106.0, 284.0, 129.0, 183.0, 167.0, 741.0, 198.0, 189.0, 95.0, 90.0, 93.0, 507.0, 234.0, 108.0, 90.0, 96.0, 93.0, 408.0, 99.0, 130.0, 104.0, 169.0, 239.0, 126.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004206799, -0.014361989, 0.12561291, -0.004331553, -0.018114254, -0.0012462579, 0.025306726, 0.008507746, -0.01470312, -0.004383729, 0.020676509, -0.033953074, 0.05025903, 0.009543368, -0.055810027, 0.019395888, 0.006857291, 0.0033505552, -0.07396468, -0.08122224, 0.10738286, -0.116073765, 0.013522227, 0.0033874426, -0.017449027, 0.0028357492, 0.018470872, -0.07471813, -0.022895431, -0.009364455, 0.009562584, 0.0029770497, -0.012487269], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [4.673298, 3.0078409, 4.839539, 3.1073496, 0.0, 0.0, 0.0, 3.9768715, 0.0, 2.3606114, 0.0, 2.680944, 3.19949, 0.0, 1.3150313, 0.0, 3.4885728, 0.0, 2.4830124, 2.2543163, 1.1243689, 2.1240516, 1.9269328, 0.0, 0.0, 0.0, 0.0, 1.7451136, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 16, 16, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.99982387, 1.0, 1.0, 0.9504941, -0.018114254, -0.0012462579, 0.025306726, 0.8080213, -0.01470312, 0.47360003, 0.020676509, 1.176989, 0.54264086, 0.009543368, 1.0, 0.019395888, 0.6444585, 0.0033505552, 1.0, 0.5289018, 0.68522966, 0.39957437, 1.0, 0.0033874426, -0.017449027, 0.0028357492, 0.018470872, 0.07692308, -0.022895431, -0.009364455, 0.009562584, 0.0029770497, -0.012487269], "split_indices": [139, 117, 113, 143, 0, 0, 0, 143, 0, 139, 0, 138, 139, 0, 89, 0, 142, 0, 93, 142, 140, 140, 124, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1798.0, 275.0, 1696.0, 102.0, 132.0, 143.0, 1556.0, 140.0, 1461.0, 95.0, 948.0, 513.0, 137.0, 811.0, 119.0, 394.0, 137.0, 674.0, 210.0, 184.0, 455.0, 219.0, 94.0, 116.0, 91.0, 93.0, 333.0, 122.0, 95.0, 124.0, 108.0, 225.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0012575426, 0.02061791, -0.07576304, 0.010265615, 0.015982902, -0.04479375, -0.018725244, -0.0051416983, 0.017112259, -0.10469605, 0.010177568, 0.015652217, -0.14506982, -0.015480297, -0.0063337944, -0.028166223, 0.050090175, -0.00614001, -0.02296692, 0.0007226241, -0.013392455, 0.08491386, -0.007236925, 0.06079222, -0.06757984, 0.022734264, 0.02831715, 0.014679426, -0.0034945875, 0.00043218173, -0.014964156, -0.084963515, 0.14041165, -0.0170601, -0.0010348649, 0.035128858, -0.0039755013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, -1, -1, 19, 21, -1, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [3.0732536, 2.373581, 1.4294369, 3.7993476, 0.0, 2.84467, 0.0, 4.0706058, 0.0, 0.47663498, 0.0, 1.8379836, 1.2811909, 0.0, 0.0, 1.6376071, 2.908381, 0.0, 0.0, 1.7273216, 0.0, 4.2803907, 0.0, 1.8443428, 1.1623741, 0.0, 4.8252935, 0.0, 0.0, 0.0, 0.0, 1.2076772, 7.256662, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, -1, -1, 20, 22, -1, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.3316327, 0.35341257, 1.0, 0.015982902, 0.28644016, -0.018725244, 0.8833981, 0.017112259, 1.0, 0.010177568, 0.4080126, -0.26923078, -0.015480297, -0.0063337944, 1.3181741, 1.0, -0.00614001, -0.02296692, 1.0, -0.013392455, 1.3280674, -0.007236925, 1.0, 1.0, 0.022734264, 1.4212835, 0.014679426, -0.0034945875, 0.00043218173, -0.014964156, 0.5968419, 1.4917217, -0.0170601, -0.0010348649, 0.035128858, -0.0039755013], "split_indices": [80, 139, 143, 125, 0, 139, 0, 143, 0, 12, 0, 140, 1, 0, 0, 138, 7, 0, 0, 23, 0, 138, 0, 69, 115, 0, 138, 0, 0, 0, 0, 139, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1647.0, 414.0, 1533.0, 114.0, 324.0, 90.0, 1399.0, 134.0, 230.0, 94.0, 1218.0, 181.0, 104.0, 126.0, 536.0, 682.0, 91.0, 90.0, 421.0, 115.0, 531.0, 151.0, 224.0, 197.0, 151.0, 380.0, 118.0, 106.0, 105.0, 92.0, 189.0, 191.0, 88.0, 101.0, 88.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00046304835, -0.009598915, 0.017169146, 0.0016718352, -0.10336085, 0.010432562, -0.015238645, -0.0025811752, -0.01650024, 0.019132614, -0.010701813, 0.028639622, -0.008841063, 0.008604933, 0.12813678, 0.023512056, -0.015552551, 0.031104846, 0.0012082449, -0.00024317279, 0.119532034, 0.0204918, -0.017446205, 0.023891792, -0.0011558322, -0.0018597109, 0.01116431], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [3.5698373, 2.0680938, 0.0, 2.3578598, 1.0038517, 1.6890801, 0.0, 0.0, 0.0, 1.5734954, 0.0, 2.8186598, 0.0, 2.8797805, 5.0309625, 2.461174, 0.0, 0.0, 0.0, 3.124747, 3.349173, 2.7542033, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [1.3316327, 1.0, 0.017169146, 1.0, 1.0, 1.0, -0.015238645, -0.0025811752, -0.01650024, 1.040707, -0.010701813, 0.8080213, -0.008841063, 0.773966, 0.9504941, 0.60624427, -0.015552551, 0.031104846, 0.0012082449, 0.5907737, 1.0, 0.4270486, -0.017446205, 0.023891792, -0.0011558322, -0.0018597109, 0.01116431], "split_indices": [139, 40, 0, 117, 106, 43, 0, 0, 0, 141, 0, 143, 0, 141, 143, 143, 0, 0, 0, 141, 115, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1957.0, 115.0, 1747.0, 210.0, 1653.0, 94.0, 93.0, 117.0, 1539.0, 114.0, 1414.0, 125.0, 1177.0, 237.0, 1079.0, 98.0, 92.0, 145.0, 865.0, 214.0, 773.0, 92.0, 112.0, 102.0, 541.0, 232.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0048415456, -0.016082482, 0.10083465, -0.023243818, 0.012746972, -0.0051797256, 0.028229708, -0.010055224, -0.12936126, -0.022194846, 0.018943917, -0.026090607, 8.4123596e-05, -0.010990492, -0.019929998, -0.022519592, 0.013626233, -0.007069772, -0.085845515, -0.025874296, 0.014669463, 0.0011129937, -0.019679204, 0.0028014886, -0.08239603, -0.0057961405, 0.0074122483, -0.017390197, -0.0011282825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [2.4340057, 1.9039031, 5.456301, 2.4687877, 0.0, 0.0, 0.0, 3.799782, 3.339856, 2.934851, 0.0, 0.0, 0.0, 2.3614898, 0.0, 1.2621024, 0.0, 2.9984505, 2.7220507, 1.497624, 0.0, 0.0, 0.0, 2.6565397, 2.023764, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.137993, 1.0, -0.34615386, 1.486009, 0.012746972, -0.0051797256, 0.028229708, 1.449413, 0.84749436, 0.8202487, 0.018943917, -0.026090607, 8.4123596e-05, 0.69135696, -0.019929998, 0.5361265, 0.013626233, 0.5289018, 0.15384616, 0.3250815, 0.014669463, 0.0011129937, -0.019679204, 0.22394206, 0.26923078, -0.0057961405, 0.0074122483, -0.017390197, -0.0011282825], "split_indices": [139, 114, 1, 138, 0, 0, 0, 138, 143, 142, 0, 0, 0, 142, 0, 140, 0, 142, 1, 143, 0, 0, 0, 142, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1852.0, 197.0, 1764.0, 88.0, 107.0, 90.0, 1569.0, 195.0, 1479.0, 90.0, 97.0, 98.0, 1391.0, 88.0, 1290.0, 101.0, 1037.0, 253.0, 924.0, 113.0, 135.0, 118.0, 613.0, 311.0, 331.0, 282.0, 136.0, 175.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.001662464, -0.008590074, 0.10985826, -0.01787256, 0.0142995445, 0.015948573, 0.006186683, -0.0074232705, -0.012742154, -0.023225376, 0.017583214, -0.01386758, -0.015818985, -0.028170003, 0.015373078, -0.015141441, -0.016375927, -0.00075747, -0.014913945, -0.018591484, 0.017869726, -0.00022287003, -0.016518176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [2.2939944, 2.6579938, 0.42632222, 2.037582, 0.0, 0.0, 0.0, 4.705709, 0.0, 1.8894029, 0.0, 3.3534906, 0.0, 2.277061, 0.0, 2.2666495, 0.0, 3.3988225, 0.0, 2.317071, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.1185367, 5.0, 1.2121096, 0.9504941, 0.0142995445, 0.015948573, 0.006186683, 0.793871, -0.012742154, 1.5003616, 0.017583214, 1.4230363, -0.015818985, 0.66754913, 0.015373078, 2.0, -0.016375927, 0.5653381, -0.014913945, 0.48442143, 0.017869726, -0.00022287003, -0.016518176], "split_indices": [142, 0, 140, 143, 0, 0, 0, 143, 0, 138, 0, 138, 0, 143, 0, 0, 0, 142, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1889.0, 179.0, 1780.0, 109.0, 88.0, 91.0, 1625.0, 155.0, 1496.0, 129.0, 1399.0, 97.0, 1289.0, 110.0, 1176.0, 113.0, 1062.0, 114.0, 966.0, 96.0, 869.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.0022424464, -0.0060399584, 0.012236848, -0.01630604, 0.011571576, -0.0038138994, -0.108149655, -0.019116497, 0.023050709, -0.0015961136, -0.017959574, -0.010210859, -0.014657842, -0.02118058, 0.015009659, 0.010762269, -0.030641923, -0.0505575, 0.05184104, -0.022058696, -0.15056965, -0.0034117003, 0.01859734, -0.004756738, 0.013147018, -0.020485913, -0.007857705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1], "loss_changes": [2.0515513, 2.4111614, 0.0, 2.0410883, 0.0, 5.6152368, 1.4029255, 1.6686403, 0.0, 0.0, 0.0, 2.416217, 0.0, 1.5671862, 0.0, 0.0, 1.9679496, 2.7504685, 2.6864333, 2.9411557, 0.8364053, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.1185367, 0.012236848, 0.8833981, 0.011571576, 0.7699487, 0.7651399, 1.5003616, 0.023050709, -0.0015961136, -0.017959574, 0.70246446, -0.014657842, 1.1550102, 0.015009659, 0.010762269, 1.0, 0.5275487, 0.4292953, 0.44635215, 1.0, -0.0034117003, 0.01859734, -0.004756738, 0.013147018, -0.020485913, -0.007857705], "split_indices": [0, 142, 0, 143, 0, 143, 139, 138, 0, 0, 0, 142, 0, 138, 0, 0, 61, 140, 139, 140, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1929.0, 133.0, 1779.0, 150.0, 1566.0, 213.0, 1470.0, 96.0, 93.0, 120.0, 1374.0, 96.0, 1286.0, 88.0, 88.0, 1198.0, 965.0, 233.0, 751.0, 214.0, 142.0, 91.0, 644.0, 107.0, 122.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020552664, -0.014394047, 0.077525415, 0.00025662978, -0.016921863, -0.004662772, 0.019305682, -0.015621524, 0.015109912, -0.003564061, -0.09663259, 0.061000854, -0.017425459, -0.0023401903, -0.017962733, 0.020384783, -0.0063112066, -0.062215984, 0.026529418, 0.012366144, -0.11459244, -0.005540442, 0.01833546, -0.008135857, 0.009704727, -0.0030970692, -0.15825698, 0.10208087, -0.113646545, -0.02359186, -0.0058780275, -0.001325496, 0.026330302, -0.018880716, 8.025196e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, -1, -1, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [2.0335739, 4.067035, 3.987517, 3.9231749, 0.0, 0.0, 0.0, 1.4475994, 0.0, 1.1544981, 1.1669297, 4.0422482, 2.0908256, 0.0, 0.0, 0.0, 0.0, 2.0547395, 2.6957386, 1.7222673, 1.128253, 5.177361, 0.0, 0.0, 0.0, 0.0, 1.5682797, 4.1466165, 1.9096577, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, -1, -1, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [0.99982387, 0.8678284, 1.0, 0.7106174, -0.016921863, -0.004662772, 0.019305682, 1.0, 0.015109912, 1.2046952, 1.0, 1.0, 0.4292953, -0.0023401903, -0.017962733, 0.020384783, -0.0063112066, 1.0, 1.2692307, 1.0, 0.22842844, 1.3761382, 0.01833546, -0.008135857, 0.009704727, -0.0030970692, 1.0, 0.5203532, 1.0, -0.02359186, -0.0058780275, -0.001325496, 0.026330302, -0.018880716, 8.025196e-05], "split_indices": [139, 142, 113, 142, 0, 0, 0, 40, 0, 138, 12, 23, 139, 0, 0, 0, 0, 97, 1, 106, 142, 138, 0, 0, 0, 0, 23, 142, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1793.0, 278.0, 1638.0, 155.0, 134.0, 144.0, 1482.0, 156.0, 1290.0, 192.0, 228.0, 1062.0, 102.0, 90.0, 106.0, 122.0, 526.0, 536.0, 217.0, 309.0, 445.0, 91.0, 103.0, 114.0, 106.0, 203.0, 223.0, 222.0, 114.0, 89.0, 130.0, 93.0, 134.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005764097, 0.0024419469, -0.016369024, 0.010326456, -0.015432554, -0.0016544458, 0.02245305, 0.0107503375, -0.09958368, 0.024034493, -0.088773094, -0.003929224, -0.016693046, -0.006419178, 0.13522105, 0.010304722, -0.026279569, 0.017283747, -0.01976228, -0.0044473275, 0.02820445, -0.015371714, 0.084345914, 0.0759332, -0.065927185, 0.024361417, 0.016190568, 0.019073525, -0.0025801926, -0.0007126627, -0.013155811, -0.014381929, 0.013810286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, -1, -1, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [2.6761353, 2.4263358, 0.0, 4.7965226, 0.0, 2.1501803, 0.0, 2.076995, 0.80802596, 4.6930504, 6.175498, 0.0, 0.0, 4.930909, 7.8622355, 0.0, 0.0, 2.119868, 0.0, 0.0, 0.0, 3.0049922, 3.4410288, 2.709622, 1.6169769, 0.0, 4.3305917, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 21, 21, 22, 22, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, -1, -1, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.4310507, -0.016369024, 1.1185367, -0.015432554, 1.0, 0.02245305, 1.0, 0.29874414, 0.6987224, 0.6088955, -0.003929224, -0.016693046, 0.6887748, 0.7106174, 0.010304722, -0.026279569, 0.44331938, -0.01976228, -0.0044473275, 0.02820445, 1.2195088, 0.50326043, 1.0, 0.31108832, 0.024361417, 0.5908567, 0.019073525, -0.0025801926, -0.0007126627, -0.013155811, -0.014381929, 0.013810286], "split_indices": [117, 143, 0, 142, 0, 40, 0, 119, 143, 143, 140, 0, 0, 141, 142, 0, 0, 140, 0, 0, 0, 138, 140, 23, 142, 0, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1963.0, 102.0, 1869.0, 94.0, 1770.0, 99.0, 1571.0, 199.0, 1386.0, 185.0, 105.0, 94.0, 1088.0, 298.0, 88.0, 97.0, 968.0, 120.0, 134.0, 164.0, 651.0, 317.0, 232.0, 419.0, 95.0, 222.0, 109.0, 123.0, 221.0, 198.0, 96.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00027908676, 0.006518304, -0.013969644, -0.004894547, 0.018554535, 0.0028073555, -0.014266433, 0.0163865, -0.05552962, -0.0106751425, 0.026406089, -0.0024236713, -0.015395872, 0.036194377, -0.010220985, -0.006368308, 0.004253281, -0.004654363, 0.076643944, 0.012432922, -0.0468979, 0.16795464, 0.015189596, -0.01192014, -0.012722279, 0.0065791183, 0.033141613, 0.1016762, -0.010178907, 0.012737723, -0.0079120165, -0.00994938, 0.026960945], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, 13, 15, -1, 17, -1, -1, -1, 19, 21, -1, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.9569476, 4.023079, 0.0, 1.964077, 0.0, 1.388667, 0.0, 1.7544515, 1.730193, 0.0, 1.6554921, 0.59211165, 0.0, 2.0191278, 0.0, 0.0, 0.0, 3.3128233, 3.4454236, 0.0, 1.1317254, 4.1248465, 3.7129714, 0.0, 2.8930187, 0.0, 0.0, 7.1282415, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22, 24, 24, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, 14, 16, -1, 18, -1, -1, -1, 20, 22, -1, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1740773, -0.013969644, 1.0, 0.018554535, 1.0, -0.014266433, 0.13796628, 0.49905074, -0.0106751425, 1.0, 1.0, -0.015395872, 0.48169166, -0.010220985, -0.006368308, 0.004253281, 1.2046952, 0.67325324, 0.012432922, 0.2840416, 0.56020635, 0.8420044, -0.01192014, 0.35278645, 0.0065791183, 0.033141613, 0.70317, -0.010178907, 0.012737723, -0.0079120165, -0.00994938, 0.026960945], "split_indices": [143, 139, 0, 117, 0, 7, 0, 142, 139, 0, 41, 13, 0, 139, 0, 0, 0, 138, 143, 0, 139, 143, 142, 0, 139, 0, 0, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1969.0, 96.0, 1851.0, 118.0, 1753.0, 98.0, 1422.0, 331.0, 107.0, 1315.0, 215.0, 116.0, 1222.0, 93.0, 91.0, 124.0, 608.0, 614.0, 150.0, 458.0, 247.0, 367.0, 147.0, 311.0, 152.0, 95.0, 211.0, 156.0, 100.0, 211.0, 96.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.001989952, -0.009773231, 0.014300138, 0.0042438786, -0.020829465, -0.009240808, 0.021270987, 0.032086056, -0.034440115, 0.0056563565, 0.018095534, -0.017180294, -0.10521363, 0.087136194, -0.055195663, -0.037206344, 0.0134416865, -0.0015950011, -0.01919509, -0.00095702626, 0.019240078, -0.12951489, 0.009849357, -0.017597178, -0.013266799, -0.022781476, -0.001196036, -0.011970044, 0.020915449, 0.014875318, -0.010520051, -0.006694724, 0.00654396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, 25, -1, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [2.3258548, 5.4429545, 0.0, 5.1358776, 0.0, 1.7870568, 0.0, 2.55747, 1.3021593, 2.7369335, 0.0, 2.6017594, 1.6181781, 2.4024253, 3.6093729, 1.4170455, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4613416, 0.0, 2.4694626, 0.0, 0.0, 0.0, 0.0, 1.8325015, 0.0, 1.5687453, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 21, 21, 23, 23, 28, 28, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, 26, -1, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [1.1936536, 1.115853, 0.014300138, 0.9271665, -0.020829465, 1.0, 0.021270987, 1.0, 0.61227083, 0.35341257, 0.018095534, 0.5684248, 1.0, 0.24863185, 0.7114859, 0.50025445, 0.0134416865, -0.0015950011, -0.01919509, -0.00095702626, 0.019240078, 1.0, 0.009849357, 1.1971636, -0.013266799, -0.022781476, -0.001196036, -0.011970044, 1.2246107, 0.014875318, 0.3576206, -0.006694724, 0.00654396], "split_indices": [141, 140, 0, 140, 0, 53, 0, 119, 141, 143, 0, 142, 121, 142, 141, 143, 0, 0, 0, 0, 0, 39, 0, 138, 0, 0, 0, 0, 138, 0, 140, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1956.0, 105.0, 1827.0, 129.0, 1716.0, 111.0, 650.0, 1066.0, 552.0, 98.0, 857.0, 209.0, 236.0, 316.0, 757.0, 100.0, 103.0, 106.0, 123.0, 113.0, 213.0, 103.0, 628.0, 129.0, 116.0, 97.0, 172.0, 456.0, 90.0, 366.0, 210.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00018917883, 0.013413543, -0.054897945, 0.023177976, -0.008371344, -0.02409471, -0.016321696, 0.0082419, 0.024946751, -0.06583578, 0.0077789384, -0.04109938, 0.03614714, 6.4784117e-06, -0.013115831, 0.008075577, -0.02194704, 0.020721909, 0.0035620045, -0.010579511, 0.078897595, -0.057734177, 0.0692245, 0.017581457, -0.008618073, 0.008197795, -0.1305802, -0.020657595, 0.15478073, -0.0017241307, -0.021951972, -0.015455953, 0.0110268695, 0.00028602334, 0.029274931], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, -1, 21, -1, 23, 25, 27, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5360044, 1.5676881, 1.3713324, 5.076575, 0.0, 1.3608805, 0.0, 1.9400243, 0.0, 0.97718793, 0.0, 4.464637, 5.016961, 0.0, 0.0, 3.217756, 0.0, 0.0, 3.0427942, 0.0, 3.935728, 3.9793916, 2.8068407, 0.0, 0.0, 0.0, 2.5906386, 3.1205688, 3.9195662, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 18, 18, 20, 20, 21, 21, 22, 22, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, -1, 22, -1, 24, 26, 28, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1919701, 0.35341257, 1.0942689, -0.008371344, 0.28644016, -0.016321696, -0.1923077, 0.024946751, 0.16330443, 0.0077789384, 1.0, -0.03846154, 6.4784117e-06, -0.013115831, 1.0, -0.02194704, 0.020721909, 1.0, -0.010579511, -0.34615386, 1.0, 0.53846157, 0.017581457, -0.008618073, 0.008197795, 0.33817554, 1.0, 1.0, -0.0017241307, -0.021951972, -0.015455953, 0.0110268695, 0.00028602334, 0.029274931], "split_indices": [80, 143, 143, 142, 0, 139, 0, 1, 0, 142, 0, 50, 1, 0, 0, 5, 0, 0, 39, 0, 1, 126, 1, 0, 0, 0, 141, 12, 81, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1653.0, 411.0, 1502.0, 151.0, 320.0, 91.0, 1409.0, 93.0, 227.0, 93.0, 509.0, 900.0, 113.0, 114.0, 399.0, 110.0, 144.0, 756.0, 153.0, 246.0, 391.0, 365.0, 155.0, 91.0, 134.0, 257.0, 178.0, 187.0, 113.0, 144.0, 88.0, 90.0, 89.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00532815, -0.0024467332, 0.011561633, 0.0049166596, -0.0148354, -0.003959224, 0.012976589, -0.0163476, 0.065695256, 0.0012994347, -0.01621619, -0.0022130925, 0.018686289, -0.035100058, 0.04194255, 0.00894793, -0.054103695, -0.026630417, 0.09991789, -0.004874315, -0.08762455, 0.01038603, -0.012087373, -0.0009836456, 0.16770735, -0.006755832, 0.01026857, -0.01870188, -0.05335066, 0.0076041482, 0.027711496, -0.013686518, 0.0011415684], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.7578297, 2.0574236, 0.0, 2.0201528, 0.0, 1.4686657, 0.0, 3.7182603, 2.7349153, 1.9069324, 0.0, 0.0, 0.0, 1.6098734, 2.421105, 0.0, 0.9736242, 3.431108, 2.455263, 1.6114076, 1.1957252, 0.0, 0.0, 0.0, 2.0459032, 0.0, 0.0, 0.0, 1.4117312, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.1794761, 1.6264299, 0.011561633, 1.0, -0.0148354, 1.0, 0.012976589, 0.7707177, 0.5485319, 0.40209216, -0.01621619, -0.0022130925, 0.018686289, 1.1628685, 1.0, 0.00894793, 1.0, 1.3297317, 1.323436, 0.24481331, 1.0, 0.01038603, -0.012087373, -0.0009836456, 1.0, -0.006755832, 0.01026857, -0.01870188, 0.15384616, 0.0076041482, 0.027711496, -0.013686518, 0.0011415684], "split_indices": [142, 138, 0, 125, 0, 42, 0, 142, 143, 142, 0, 0, 0, 138, 111, 0, 59, 138, 138, 142, 53, 0, 0, 0, 106, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1915.0, 135.0, 1823.0, 92.0, 1702.0, 121.0, 1445.0, 257.0, 1289.0, 156.0, 149.0, 108.0, 680.0, 609.0, 90.0, 590.0, 279.0, 330.0, 239.0, 351.0, 117.0, 162.0, 126.0, 204.0, 151.0, 88.0, 90.0, 261.0, 111.0, 93.0, 114.0, 147.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0020728952, 0.009916769, -0.008549408, -0.00012834427, 0.02017256, 0.009815839, -0.010222609, -0.002903135, 0.018822838, 0.008290241, -0.07021604, 0.05119137, -0.027859036, 0.0026449456, -0.019507566, 0.018306313, 0.023456128, 0.0050106402, -0.014067103, -0.052148007, 0.07880514, -0.022283884, 0.01294737, 0.0035432675, -0.017855315, 0.012718506, 0.019696001, 0.009110531, -0.049398698, 0.014039362, -0.011640742, -0.0017931439, -0.010983579], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.4286776, 3.6781502, 0.0, 1.8417158, 0.0, 3.7510288, 0.0, 1.1625869, 0.0, 2.0517676, 2.6553164, 3.648228, 2.662412, 0.0, 0.0, 2.186613, 0.0, 1.8888212, 0.0, 2.6237442, 2.1551337, 1.4019842, 0.0, 0.0, 0.0, 2.9180517, 0.0, 0.0, 0.69985825, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.1489253, 1.137993, -0.008549408, 1.4917217, 0.02017256, 0.7612845, -0.010222609, 2.0, 0.018822838, 1.0, 0.4211421, 1.0, 0.49827978, 0.0026449456, -0.019507566, 1.0, 0.023456128, 1.3221555, -0.014067103, 1.0, 0.46111313, 0.1496468, 0.01294737, 0.0035432675, -0.017855315, 0.3173981, 0.019696001, 0.009110531, 1.0, 0.014039362, -0.011640742, -0.0017931439, -0.010983579], "split_indices": [143, 139, 0, 138, 0, 139, 0, 0, 0, 122, 143, 74, 140, 0, 0, 97, 0, 138, 0, 15, 143, 140, 0, 0, 0, 140, 0, 0, 80, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1909.0, 171.0, 1814.0, 95.0, 1653.0, 161.0, 1543.0, 110.0, 1323.0, 220.0, 605.0, 718.0, 124.0, 96.0, 513.0, 92.0, 556.0, 162.0, 237.0, 276.0, 456.0, 100.0, 140.0, 97.0, 177.0, 99.0, 88.0, 368.0, 89.0, 88.0, 242.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00957603, 0.00090512837, -0.012523656, -0.006986297, 0.01531333, 0.0005104062, -0.013672519, -0.0128177395, 0.013024895, -0.005271643, -0.013715679, -0.075405, 0.010339827, 0.0006161907, -0.018440152, -0.00845236, 0.023646561, 0.08265474, -0.02721561, -0.008593994, 0.024592541, 0.015867867, -0.047668528, -0.010597315, 0.00024204336], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, 19, 21, -1, -1, -1, 23, -1, -1], "loss_changes": [2.494824, 2.2668483, 0.0, 1.7448698, 0.0, 2.93268, 0.0, 1.4430658, 0.0, 1.5875832, 0.0, 2.347095, 5.0397873, 0.0, 0.0, 1.8718643, 0.0, 5.147468, 3.452289, 0.0, 0.0, 0.0, 2.3889022, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, 20, 22, -1, -1, -1, 24, -1, -1], "split_conditions": [1.1489253, 1.0749121, -0.012523656, 1.068056, 0.01531333, 1.4716803, -0.013672519, 1.0, 0.013024895, -1.0, -0.013715679, 0.4349057, 0.7898809, 0.0006161907, -0.018440152, 1.0, 0.023646561, 0.3935085, 1.1784285, -0.008593994, 0.024592541, 0.015867867, 1.0, -0.010597315, 0.00024204336], "split_indices": [143, 142, 0, 140, 0, 138, 0, 117, 0, 0, 0, 142, 143, 0, 0, 89, 0, 142, 138, 0, 0, 0, 17, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1887.0, 171.0, 1794.0, 93.0, 1696.0, 98.0, 1538.0, 158.0, 1450.0, 88.0, 264.0, 1186.0, 151.0, 113.0, 1095.0, 91.0, 187.0, 908.0, 92.0, 95.0, 90.0, 818.0, 378.0, 440.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.003758169, -0.009916735, 0.010859246, -0.0011192511, -0.015582812, -0.0134492265, 0.018223597, -0.022623636, 0.014939651, -0.0063264007, -0.015840288, -0.020671854, 0.01068627, -0.007985826, -0.0136233615, 0.0048176306, -0.011856115, -0.013021598, 0.0153424805, 0.00015847744, -0.014495015], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [1.4246603, 2.50569, 0.0, 4.1620684, 0.0, 2.5771728, 0.0, 3.6135447, 0.0, 2.3674254, 0.0, 1.8970289, 0.0, 1.6507608, 0.0, 2.770334, 0.0, 1.7978891, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.249012, 1.0745199, 0.010859246, 1.0, -0.015582812, 5.0, 0.018223597, 0.8359072, 0.014939651, 0.73751384, -0.015840288, 2.0, 0.01068627, 0.66754913, -0.0136233615, 0.5653381, -0.011856115, 0.48442143, 0.0153424805, 0.00015847744, -0.014495015], "split_indices": [142, 141, 0, 125, 0, 0, 0, 143, 0, 139, 0, 0, 0, 143, 0, 142, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1952.0, 107.0, 1841.0, 111.0, 1725.0, 116.0, 1633.0, 92.0, 1458.0, 175.0, 1294.0, 164.0, 1166.0, 128.0, 1045.0, 121.0, 933.0, 112.0, 840.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.0034120746, -0.0031341503, 0.012326308, 0.0068870783, -0.012312344, -0.0052605327, 0.024151344, 0.0043945224, -0.014119357, -0.009477268, 0.016446201, 0.0026601865, -0.020105598, -0.007964116, 0.013814637, 0.009415358, -0.10597003, -0.0136044165, 0.10086666, -0.00012404799, -0.01929153, -0.00020015879, -0.010874758, -0.001869499, 0.026413906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6209253, 2.3555808, 0.0, 5.1530714, 0.0, 2.2560863, 0.0, 3.5637784, 0.0, 3.4344358, 0.0, 1.9993912, 0.0, 2.1938396, 0.0, 2.3030765, 1.7665129, 0.9648343, 4.2946467, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1936536, 1.068056, 0.012326308, 0.9271665, -0.012312344, 0.79649323, 0.024151344, 0.6985078, -0.014119357, 0.798084, 0.016446201, 0.6731954, -0.020105598, 0.5338411, 0.013814637, 0.42138514, 1.3221555, 0.38100478, 1.3181741, -0.00012404799, -0.01929153, -0.00020015879, -0.010874758, -0.001869499, 0.026413906], "split_indices": [141, 140, 0, 140, 0, 140, 0, 140, 0, 139, 0, 139, 0, 140, 0, 140, 138, 140, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1959.0, 107.0, 1808.0, 151.0, 1719.0, 89.0, 1605.0, 114.0, 1477.0, 128.0, 1389.0, 88.0, 1288.0, 101.0, 1094.0, 194.0, 874.0, 220.0, 88.0, 106.0, 779.0, 95.0, 127.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.00024980278, -0.0070901113, 0.0123635745, -5.002771e-05, -0.014500161, -0.010731849, 0.020074544, 8.859549e-05, -0.013086845, -0.011289961, 0.011167432, 0.0021186846, -0.011255643, -0.012946662, 0.07251137, 0.0028397713, -0.01178137, 0.01722829, -0.0053931754, -0.026037032, 0.05583928, 0.007960057, -0.004484921, -0.0016152445, 0.014448218], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [1.7490652, 1.8990965, 0.0, 3.9915864, 0.0, 2.296979, 0.0, 2.0581589, 0.0, 1.9973922, 0.0, 1.3775768, 0.0, 1.7713603, 2.8889332, 1.4233247, 0.0, 0.0, 0.0, 1.1963387, 2.0931506, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.3399042, 1.6290085, 0.0123635745, 0.99982387, -0.014500161, 0.8678284, 0.020074544, 0.7106174, -0.013086845, 0.65763235, 0.011167432, 0.5289018, -0.011255643, 0.5139595, 0.5445301, 0.33220515, -0.01178137, 0.01722829, -0.0053931754, -0.30769232, 1.0, 0.007960057, -0.004484921, -0.0016152445, 0.014448218], "split_indices": [140, 138, 0, 139, 0, 142, 0, 142, 0, 139, 0, 142, 0, 140, 139, 140, 0, 0, 0, 1, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1956.0, 108.0, 1861.0, 95.0, 1767.0, 94.0, 1621.0, 146.0, 1471.0, 150.0, 1299.0, 172.0, 1070.0, 229.0, 930.0, 140.0, 128.0, 101.0, 602.0, 328.0, 91.0, 511.0, 181.0, 147.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011612189, -0.0076995343, 0.011108948, 0.0021832348, -0.016599724, -0.024030358, 0.03580217, -0.0027346357, -0.111981675, 0.017010156, 0.012102275, -0.028684026, 0.08717647, -0.026444355, 0.0028752347, 0.09021093, -0.024012474, 0.042556502, -0.07605436, 0.021015853, -0.0026838114, -0.004902329, 0.019837638, -0.011968991, 0.02570226, 0.01357878, -0.0043664905, -0.11171449, 0.004429856, -0.008091812, 0.013658747, -0.15011281, -0.004548618, -0.009012005, -0.02029064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.506759, 3.0349748, 0.0, 1.6092045, 0.0, 1.921684, 2.5463047, 1.9271723, 4.291316, 0.0, 1.918195, 2.1631749, 2.5940242, 0.0, 0.0, 3.2379725, 2.2118092, 2.0578651, 1.6523435, 0.0, 0.0, 0.0, 0.0, 0.0, 3.617723, 0.0, 0.0, 0.75528646, 0.0, 0.0, 0.0, 0.59543896, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 24, 24, 27, 27, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.3316327, 1.0651944, 0.011108948, 1.0, -0.016599724, 1.0, 0.4108042, 0.46250355, 1.0, 0.017010156, 1.0, 1.2182355, 1.3761382, -0.026444355, 0.0028752347, 0.52206093, 0.5676343, 1.0, 1.0, 0.021015853, -0.0026838114, -0.004902329, 0.019837638, -0.011968991, 1.0, 0.01357878, -0.0043664905, 1.0, 0.004429856, -0.008091812, 0.013658747, 1.0, -0.004548618, -0.009012005, -0.02029064], "split_indices": [139, 141, 0, 71, 0, 0, 142, 139, 106, 0, 17, 138, 138, 0, 0, 139, 140, 23, 62, 0, 0, 0, 0, 0, 53, 0, 0, 23, 0, 0, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1940.0, 113.0, 1826.0, 114.0, 1026.0, 800.0, 826.0, 200.0, 120.0, 680.0, 641.0, 185.0, 96.0, 104.0, 215.0, 465.0, 256.0, 385.0, 89.0, 96.0, 94.0, 121.0, 159.0, 306.0, 123.0, 133.0, 297.0, 88.0, 156.0, 150.0, 188.0, 109.0, 88.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004771123, 0.015872085, -0.03936651, 0.02937438, -0.064294234, -0.010190976, -0.014388542, 0.012652382, 0.025727618, 0.0068171597, -0.024221405, -0.053316843, 0.009467294, 0.0993609, -0.008983766, -0.0002760617, -0.010520346, -0.0016374238, 0.020186918, 0.009719316, -0.017182073, -0.00646876, 0.015871927, 0.02032741, -0.10103515, 0.09550765, -0.014944374, -0.022346998, 0.002534915, 0.022332849, -0.0041, -0.08570367, 0.08337379, 0.0020827504, -0.017584546, 0.0246279, -0.005717184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, -1, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [1.0166881, 1.7946676, 1.2715971, 5.40777, 5.6328235, 1.4742851, 0.0, 2.480123, 0.0, 0.0, 0.0, 0.605957, 0.0, 3.1320462, 3.2221954, 0.0, 0.0, 0.0, 0.0, 2.28901, 0.0, 2.1691186, 0.0, 1.7687117, 2.9245563, 3.7165358, 3.1584437, 0.0, 0.0, 0.0, 0.0, 2.5351684, 4.3501673, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 26, 26, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, -1, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.35341257, 1.3316327, 1.4758557, 0.28644016, -0.014388542, 0.28096482, 0.025727618, 0.0068171597, -0.024221405, 0.16330443, 0.009467294, 0.26289493, 1.3461539, -0.0002760617, -0.010520346, -0.0016374238, 0.020186918, 0.84615386, -0.017182073, 2.0, 0.015871927, 1.3243612, 0.7196737, 1.0, 0.7392852, -0.022346998, 0.002534915, 0.022332849, -0.0041, 1.3908919, 1.0, 0.0020827504, -0.017584546, 0.0246279, -0.005717184], "split_indices": [80, 119, 143, 139, 138, 139, 0, 143, 0, 0, 0, 142, 0, 141, 1, 0, 0, 0, 0, 1, 0, 0, 0, 138, 143, 97, 142, 0, 0, 0, 0, 138, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1658.0, 417.0, 1419.0, 239.0, 326.0, 91.0, 1322.0, 97.0, 137.0, 102.0, 231.0, 95.0, 264.0, 1058.0, 117.0, 114.0, 124.0, 140.0, 949.0, 109.0, 856.0, 93.0, 667.0, 189.0, 213.0, 454.0, 96.0, 93.0, 110.0, 103.0, 264.0, 190.0, 121.0, 143.0, 88.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00024485408, 0.006879532, -0.014072418, -0.0032618723, 0.0180586, -0.010618083, 0.011423684, -0.08144295, 0.005213937, 0.010279444, -0.021796003, -0.004934038, 0.01263497, 0.009884374, -0.006786549, 0.0059146886, -0.014865053, -0.026462987, 0.048942115, 0.013524072, -0.113674596, 0.09406806, -0.037819345, -0.023257492, 0.017372824, -0.01939419, 0.00076384493, 0.16320753, 0.00079782354, -0.013032063, 0.0059937644, 0.0048443596, -0.008048682, 0.0017803574, 0.033311773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, 21, 23, 25, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.9351025, 3.4809766, 0.0, 1.6137323, 0.0, 1.9701258, 0.0, 4.019458, 1.7652504, 1.3288034, 0.0, 2.0658622, 0.0, 0.0, 0.0, 1.7163337, 0.0, 2.4515975, 2.0711372, 2.8402145, 2.1519818, 2.071367, 1.6367193, 1.6085346, 0.0, 0.0, 0.0, 4.768183, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, 22, 24, 26, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.014072418, 5.0, 0.0180586, -0.3846154, 0.011423684, 0.63716984, 0.87650514, 1.0, -0.021796003, 0.7707177, 0.01263497, 0.009884374, -0.006786549, 0.3875664, -0.014865053, 0.34849122, 1.3781409, 0.33220515, 1.0, 0.46153846, 0.67886037, 1.0, 0.017372824, -0.01939419, 0.00076384493, 0.5238575, 0.00079782354, -0.013032063, 0.0059937644, 0.0048443596, -0.008048682, 0.0017803574, 0.033311773], "split_indices": [143, 142, 0, 0, 0, 1, 0, 141, 139, 59, 0, 142, 0, 0, 0, 143, 0, 141, 138, 140, 71, 1, 143, 106, 0, 0, 0, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1976.0, 93.0, 1867.0, 109.0, 1757.0, 110.0, 321.0, 1436.0, 192.0, 129.0, 1325.0, 111.0, 90.0, 102.0, 1232.0, 93.0, 703.0, 529.0, 482.0, 221.0, 348.0, 181.0, 392.0, 90.0, 133.0, 88.0, 193.0, 155.0, 93.0, 88.0, 174.0, 218.0, 104.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0017460374, -0.0036727493, 0.009983614, 0.0037107104, -0.014984957, -0.006472959, 0.019301979, 0.00086173724, -0.014147976, 0.008557929, -0.012713804, 0.043652207, -0.022516, 0.004096503, 0.020651257, -0.045423273, 0.061818585, 0.010746105, -0.031522352, -0.019712502, -0.01955583, 0.0003460448, 0.011952827, -0.0036580341, -0.013961138, 0.011191658, -0.043912828, 0.006846241, -0.009266746, -0.012357974, 0.000557309], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, -1, 23, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [1.0965453, 2.1100137, 0.0, 3.58775, 0.0, 1.7487535, 0.0, 1.65006, 0.0, 1.7230171, 0.0, 4.780005, 1.6189116, 2.1979914, 0.0, 2.5860057, 0.60284114, 0.0, 1.3372508, 0.0, 1.8028799, 0.0, 0.0, 2.266048, 0.0, 0.0, 1.8726358, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 18, 18, 20, 20, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, -1, 24, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.3399042, 1.6290085, 0.009983614, 0.99982387, -0.014984957, 0.991673, 0.019301979, 1.0, -0.014147976, 1.0, -0.012713804, 0.68522966, 0.88461536, 0.26170823, 0.020651257, 1.0, 0.31940445, 0.010746105, 1.0, -0.019712502, 0.16967288, 0.0003460448, 0.011952827, 1.0, -0.013961138, 0.011191658, 1.0, 0.006846241, -0.009266746, -0.012357974, 0.000557309], "split_indices": [140, 138, 0, 139, 0, 140, 0, 117, 0, 122, 0, 140, 1, 142, 0, 89, 143, 0, 0, 0, 140, 0, 0, 109, 0, 0, 81, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1955.0, 108.0, 1861.0, 94.0, 1766.0, 95.0, 1675.0, 91.0, 1580.0, 95.0, 742.0, 838.0, 597.0, 145.0, 659.0, 179.0, 153.0, 444.0, 96.0, 563.0, 89.0, 90.0, 353.0, 91.0, 88.0, 475.0, 195.0, 158.0, 182.0, 293.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0058698007, 0.0044035343, -0.08431683, -0.007558219, 0.022223338, 0.0050816755, -0.028560957, -0.00013476933, -0.011278885, -0.013950877, 0.08270205, -0.0024387299, -0.017959785, -0.004601409, 0.020384667, -0.01758759, 0.05728939, -0.0037524942, -0.012089716, 0.014138884, -0.0015997243, -0.031058745, 0.01779832, -0.008313947, -0.017666792, -0.0036161914, 0.012549787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [1.6634037, 4.75527, 6.5011377, 1.3514311, 0.0, 0.0, 0.0, 1.8494834, 0.0, 2.641128, 3.6020458, 1.1717324, 0.0, 0.0, 0.0, 1.476464, 1.614802, 4.520856, 0.0, 0.0, 0.0, 2.6229858, 0.0, 2.5525746, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0, 1.3316327, 1.4836318, 1.0268755, 0.022223338, 0.0050816755, -0.028560957, 0.7106174, -0.011278885, 0.66142523, 1.0, 1.0, -0.017959785, -0.004601409, 0.020384667, 0.55072904, 1.3297317, 1.0, -0.012089716, 0.014138884, -0.0015997243, 0.45846936, 0.01779832, 1.3846154, -0.017666792, -0.0036161914, 0.012549787], "split_indices": [119, 139, 138, 142, 0, 0, 0, 142, 0, 142, 59, 121, 0, 0, 0, 141, 138, 71, 0, 0, 0, 143, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1825.0, 239.0, 1730.0, 95.0, 143.0, 96.0, 1616.0, 114.0, 1385.0, 231.0, 1295.0, 90.0, 112.0, 119.0, 1033.0, 262.0, 911.0, 122.0, 122.0, 140.0, 792.0, 119.0, 685.0, 107.0, 567.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.00039474675, 0.005869183, -0.009021179, 0.013894262, -0.057671834, 0.020014634, -0.00966087, 0.0006084632, -0.010836348, 0.028404992, -0.0063497364, 0.011492299, 0.01574498, 0.027435988, -0.014941836, 0.044697016, -0.030413456, 0.02260562, 0.016451214, 0.00754003, -0.014584667, 0.011582825, 0.004332266, -0.009498729, 0.0025338635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, -1, -1], "loss_changes": [1.0272566, 0.99587697, 0.0, 1.1727375, 0.7077908, 1.1512427, 0.0, 0.0, 0.0, 3.2584648, 0.0, 3.3864725, 0.0, 1.1992476, 0.0, 2.4483664, 3.3711805, 1.3304256, 0.0, 0.0, 0.0, 0.0, 1.362382, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, -1, -1], "split_conditions": [1.0, 1.0, -0.009021179, 1.0, 0.07692308, 1.235102, -0.00966087, 0.0006084632, -0.010836348, 0.95420814, -0.0063497364, 0.79649323, 0.01574498, 1.0, -0.014941836, 0.69157404, 0.5127808, 0.17627749, 0.016451214, 0.00754003, -0.014584667, 0.011582825, 0.25646102, -0.009498729, 0.0025338635], "split_indices": [43, 40, 0, 117, 1, 139, 0, 0, 0, 140, 0, 140, 0, 0, 0, 139, 141, 143, 0, 0, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1953.0, 118.0, 1734.0, 219.0, 1643.0, 91.0, 97.0, 122.0, 1493.0, 150.0, 1320.0, 173.0, 1201.0, 119.0, 925.0, 276.0, 781.0, 144.0, 144.0, 132.0, 128.0, 653.0, 114.0, 539.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0051185936, 0.0034505236, -0.07807402, 0.010976858, -0.013606392, 0.0013437079, -0.01514342, -6.520619e-06, 0.01790836, 0.011724144, -0.019904533, 0.000995838, 0.014536552, -0.007617487, 0.0109800035, 0.005400465, -0.011237684, -0.016209532, 0.06575706, 0.016049547, -0.08951469, 0.019340467, 0.0025410848, -0.0014901982, 0.010670759, -0.017542226, -0.004460844, -0.01136299, 0.009488216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2965896, 1.94886, 1.4634933, 3.251476, 0.0, 0.0, 0.0, 3.8595204, 0.0, 2.2380764, 0.0, 1.354205, 0.0, 1.8260646, 0.0, 1.5534284, 0.0, 2.073892, 2.5337806, 1.7088574, 1.033886, 0.0, 2.2527444, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.7004119, 0.07692308, 1.1577346, -0.013606392, 0.0013437079, -0.01514342, 1.0178082, 0.01790836, 1.5178967, -0.019904533, 1.0, 0.014536552, 0.743426, 0.0109800035, 0.56020635, -0.011237684, 1.0, 1.0, 1.0, 1.0, 0.019340467, 1.0, -0.0014901982, 0.010670759, -0.017542226, -0.004460844, -0.01136299, 0.009488216], "split_indices": [40, 138, 1, 140, 0, 0, 0, 141, 0, 138, 0, 88, 0, 140, 0, 143, 0, 127, 53, 93, 126, 0, 122, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1856.0, 218.0, 1761.0, 95.0, 97.0, 121.0, 1653.0, 108.0, 1561.0, 92.0, 1445.0, 116.0, 1339.0, 106.0, 1191.0, 148.0, 877.0, 314.0, 609.0, 268.0, 104.0, 210.0, 454.0, 155.0, 92.0, 176.0, 93.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.00094285153, 0.010132346, -0.005647724, -0.08540156, 0.0026302952, -0.0012003182, -0.016369313, -0.0046955007, 0.012275916, 0.006029353, -0.018874787, -0.0062775565, 0.019832484, 0.01456112, -0.084670685, 0.02641424, -0.009679578, -0.14921744, 0.0048182807, 0.015063371, 0.014183396, -0.0067986026, -0.024371108, -0.028574536, 0.054803435, 0.003334362, -0.0141722625, -0.0010694462, 0.021516038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, -1, 3, 5, 7, -1, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, -1, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.995499, 0.0, 1.3058833, 1.0688437, 1.5770313, 0.0, 0.0, 3.3339763, 0.0, 3.7770357, 0.0, 2.4504137, 0.0, 1.5641128, 2.7012074, 1.6271796, 0.0, 1.6272802, 0.0, 0.0, 1.6934084, 0.0, 0.0, 3.3278127, 5.2515216, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 23, 23, 24, 24], "right_children": [2, -1, 4, 6, 8, -1, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, -1, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [0.08515571, 0.010132346, 0.18286213, 0.20436072, 1.0, -0.0012003182, -0.016369313, 1.7004119, 0.012275916, 1.137993, -0.018874787, 0.7456034, 0.019832484, 1.0, 1.0, 1.2103893, -0.009679578, 1.0, 0.0048182807, 0.015063371, 1.0, -0.0067986026, -0.024371108, 0.5139595, 0.56254256, 0.003334362, -0.0141722625, -0.0010694462, 0.021516038], "split_indices": [139, 0, 139, 140, 84, 0, 0, 138, 0, 139, 0, 141, 0, 40, 97, 138, 0, 108, 0, 0, 111, 0, 0, 140, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 91.0, 1978.0, 186.0, 1792.0, 96.0, 90.0, 1689.0, 103.0, 1596.0, 93.0, 1500.0, 96.0, 1185.0, 315.0, 1071.0, 114.0, 212.0, 103.0, 96.0, 975.0, 114.0, 98.0, 475.0, 500.0, 307.0, 168.0, 355.0, 145.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0053756814, 0.013453996, -0.055578865, 0.0007879735, 0.023678955, 0.0051325047, -0.02126622, 0.010754277, -0.078017734, 0.020556586, -0.011490951, 0.00070338114, -0.01648412, 0.003099613, 0.11653006, -0.035513524, 0.02188181, -0.00035885238, 0.022318712, 0.01860631, -0.02209804, -7.522847e-05, 0.015193506, -0.048806556, 0.011972564, 0.05662694, -0.056613997, 0.0030595106, -0.012564691, -0.0040526288, 0.123449415, -0.121676855, 0.0051990906, 0.02268301, 0.0019055167, -0.001004715, -0.02038363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, -1, 29, 31, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1], "loss_changes": [1.0183038, 5.1653385, 4.0638633, 1.3571738, 0.0, 0.0, 0.0, 1.8895736, 1.432588, 2.3841028, 0.0, 0.0, 0.0, 0.8731883, 2.805718, 3.954751, 2.3130233, 0.0, 0.0, 2.079107, 0.0, 2.2216682, 0.0, 1.1165293, 0.0, 2.246239, 2.4519525, 0.0, 0.0, 0.0, 2.2124307, 1.9902003, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 25, 25, 26, 26, 30, 30, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, -1, 30, 32, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3316327, 1.4836318, 1.0, 0.023678955, 0.0051325047, -0.02126622, 1.0268755, 0.32258317, 0.7106174, -0.011490951, 0.00070338114, -0.01648412, -0.15384616, 1.0, 0.58609116, 0.61682194, -0.00035885238, 0.022318712, 0.41878033, -0.02209804, 1.2607766, 0.015193506, 0.25648886, 0.011972564, 0.22819103, 1.0, 0.0030595106, -0.012564691, -0.0040526288, 0.7307692, 0.39455435, 0.0051990906, 0.02268301, 0.0019055167, -0.001004715, -0.02038363], "split_indices": [119, 139, 138, 40, 0, 0, 0, 142, 141, 142, 0, 0, 0, 1, 59, 141, 140, 0, 0, 139, 0, 138, 0, 139, 0, 140, 23, 0, 0, 0, 1, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1826.0, 242.0, 1728.0, 98.0, 144.0, 98.0, 1534.0, 194.0, 1423.0, 111.0, 98.0, 96.0, 1204.0, 219.0, 394.0, 810.0, 103.0, 116.0, 305.0, 89.0, 693.0, 117.0, 183.0, 122.0, 346.0, 347.0, 90.0, 93.0, 141.0, 205.0, 217.0, 130.0, 103.0, 102.0, 92.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0043904744, -0.009256526, 0.009262871, 0.013444396, 0.0032701571, -0.011071665, 0.04818686, -0.023706129, 0.016383667, -0.06232004, 0.12599698, 0.0005303228, -0.16989426, 0.0027627654, -0.015037413, 0.0017381692, 0.026314674, -0.013898561, 0.014275792, -0.035475336, 0.00091879535, 0.0033577327, -0.015194893, -0.03608772, 0.03812459, 0.00733496, -0.064971484, 0.009472867, 0.016868524, -0.018036498, -0.0024217411, -0.013010524, 0.0064915055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, 21, -1, -1, -1, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [0.9774094, 0.0, 1.477843, 0.0, 1.2110724, 3.1490686, 3.912342, 4.708756, 0.0, 1.4890096, 3.9773812, 2.3394918, 6.256842, 0.0, 0.0, 0.0, 0.0, 2.4656167, 0.0, 0.0, 0.0, 1.261683, 0.0, 1.3623744, 1.8292446, 0.0, 1.6036388, 3.1031451, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 21, 21, 23, 23, 24, 24, 26, 26, 27, 27], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, 22, -1, -1, -1, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [0.0919324, -0.009256526, 0.1384309, 0.013444396, 1.0, 5.0, 1.0, 0.8883714, 0.016383667, 0.46695456, 0.8883714, 0.778775, -0.115384616, 0.0027627654, -0.015037413, 0.0017381692, 0.026314674, 2.0, 0.014275792, -0.035475336, 0.00091879535, 1.2858242, -0.015194893, 0.22115828, 1.0769231, 0.00733496, 1.206961, 0.41259032, 0.016868524, -0.018036498, -0.0024217411, -0.013010524, 0.0064915055], "split_indices": [143, 0, 143, 0, 61, 0, 17, 140, 0, 142, 140, 142, 1, 0, 0, 0, 0, 0, 0, 0, 0, 138, 0, 141, 1, 0, 138, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 99.0, 1970.0, 90.0, 1880.0, 1425.0, 455.0, 1329.0, 96.0, 188.0, 267.0, 1140.0, 189.0, 93.0, 95.0, 149.0, 118.0, 1035.0, 105.0, 93.0, 96.0, 920.0, 115.0, 431.0, 489.0, 90.0, 341.0, 401.0, 88.0, 89.0, 252.0, 114.0, 287.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0061705173, 0.009727222, -0.010913181, -0.10863925, -0.0012882283, -0.0041597164, -0.01756813, 0.010368381, -0.0072476123, -0.090858035, 0.005191708, -0.0051217745, -0.014557883, -0.002587415, 0.012753613, 0.011217006, -0.01920311, -0.0011751805, 0.016888782, 0.014965363, -0.093484595, -0.0026058217, 0.015569036, -0.016219577, -0.0023211788, 0.0021640258, -0.016916586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, -1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [1.0071895, 0.0, 1.8464148, 0.79105544, 1.117891, 0.0, 0.0, 0.0, 1.7587365, 0.4750427, 1.4009504, 0.0, 0.0, 3.6193824, 0.0, 2.5205138, 0.0, 1.7819494, 0.0, 2.517214, 0.8594773, 3.6547782, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21], "right_children": [2, -1, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [0.08515571, 0.009727222, 1.1971636, 0.24167094, 1.2195088, -0.0041597164, -0.01756813, 0.010368381, 0.25500777, 0.32187057, 1.0, -0.0051217745, -0.014557883, 1.7004119, 0.012753613, 1.137993, -0.01920311, 1.4917217, 0.016888782, 1.4481664, 0.8961227, 0.760985, 0.015569036, -0.016219577, -0.0023211788, 0.0021640258, -0.016916586], "split_indices": [139, 0, 138, 141, 138, 0, 0, 0, 143, 139, 84, 0, 0, 138, 0, 139, 0, 138, 0, 138, 140, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 90.0, 1963.0, 176.0, 1787.0, 88.0, 88.0, 96.0, 1691.0, 219.0, 1472.0, 127.0, 92.0, 1384.0, 88.0, 1290.0, 94.0, 1196.0, 94.0, 1018.0, 178.0, 905.0, 113.0, 90.0, 88.0, 790.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0016531595, -0.008451819, 0.054089814, -0.001668932, -0.01188772, 0.12378083, -0.01059925, -0.011613847, 0.015359347, 0.00028514848, 0.025560468, -0.020342948, 0.013142434, -0.0013573301, -0.07935521, -0.015570483, 0.012669141, -0.1484203, 0.008245438, 0.0046282266, -0.095549226, -0.005540814, -0.030144027, -0.014524963, 0.0156112565, 0.0029203591, -0.019741165, 0.0060847015, -0.009018596, -0.0024267968, 0.012530783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, -1, 23, 25, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.0920649, 1.2942765, 3.7150478, 2.513748, 0.0, 3.6983974, 0.0, 1.9103497, 0.0, 0.0, 0.0, 1.6155941, 0.0, 1.9855939, 3.9225636, 1.5863893, 0.0, 3.5012484, 0.0, 2.2747045, 2.5161102, 0.0, 0.0, 1.0853063, 0.0, 0.0, 0.0, 1.9794513, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, -1, 24, 26, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.6902589, 1.0, 1.0, -0.01188772, 0.43165883, -0.01059925, 1.1053776, 0.015359347, 0.00028514848, 0.025560468, 0.65763235, 0.013142434, 0.6700471, 0.95420814, 0.48414886, 0.012669141, 0.71682304, 0.008245438, 0.49448743, 1.0, -0.005540814, -0.030144027, 0.3730132, 0.0156112565, 0.0029203591, -0.019741165, 0.35067207, -0.009018596, -0.0024267968, 0.012530783], "split_indices": [62, 138, 108, 102, 0, 142, 0, 139, 0, 0, 0, 139, 0, 140, 140, 140, 0, 140, 0, 139, 39, 0, 0, 139, 0, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1728.0, 333.0, 1628.0, 100.0, 232.0, 101.0, 1530.0, 98.0, 121.0, 111.0, 1442.0, 88.0, 1091.0, 351.0, 982.0, 109.0, 246.0, 105.0, 784.0, 198.0, 153.0, 93.0, 696.0, 88.0, 89.0, 109.0, 547.0, 149.0, 436.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00076408044, 0.0065212734, -0.0117978, -0.004017133, 0.0189187, 0.0025853529, -0.012226167, -0.004929399, 0.01310263, 0.016503599, -0.035829164, -0.049240414, 0.03620089, 0.04338136, -0.06751338, 0.0051569296, -0.01149859, 0.017110892, 0.015319821, 0.009628306, -0.0021543417, -0.11486192, 0.009605429, 0.03680821, -0.009935377, -0.00310165, -0.17650169, 0.1142109, -0.012510312, -0.010938328, -0.02406237, 0.027594322, 8.709169e-05, -0.0121386675, 0.005028348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, -1, 25, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.419197, 3.8115118, 0.0, 1.4614855, 0.0, 1.7113034, 0.0, 1.1093099, 0.0, 1.2807341, 1.7216703, 1.5111346, 2.1437547, 0.67318785, 3.7948985, 0.0, 0.0, 0.0, 1.6238751, 0.0, 0.0, 1.9639201, 0.0, 2.11865, 0.0, 0.0, 0.94252396, 3.9594386, 2.3176618, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 21, 21, 23, 23, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, -1, 26, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.0117978, 1.1362028, 0.0189187, 0.9271665, -0.012226167, 1.0, 0.01310263, 0.2314219, -0.30769232, 0.14923225, 0.2967913, 0.6398453, 1.0, 0.0051569296, -0.01149859, 0.017110892, 1.3461539, 0.009628306, -0.0021543417, 0.3173981, 0.009605429, 1.0, -0.009935377, -0.00310165, 1.0, 1.3315015, 1.0, -0.010938328, -0.02406237, 0.027594322, 8.709169e-05, -0.0121386675, 0.005028348], "split_indices": [143, 142, 0, 140, 0, 140, 0, 109, 0, 142, 1, 142, 142, 143, 42, 0, 0, 0, 1, 0, 0, 140, 0, 15, 0, 0, 53, 138, 111, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1980.0, 96.0, 1872.0, 108.0, 1773.0, 99.0, 1675.0, 98.0, 989.0, 686.0, 228.0, 761.0, 196.0, 490.0, 90.0, 138.0, 102.0, 659.0, 108.0, 88.0, 380.0, 110.0, 555.0, 104.0, 161.0, 219.0, 216.0, 339.0, 107.0, 112.0, 89.0, 127.0, 124.0, 215.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0004412369, 0.0045637693, -0.008526402, -0.00365284, 0.01554433, 0.006939892, -0.08046182, -0.0019204363, 0.010300118, 0.006744987, -0.024058644, -0.019124754, 0.04489229, -0.0013919543, -0.013254324, 0.018130451, -0.011866633, 0.049093433, -0.036668736, 0.0486451, -0.012270498, -0.006755289, 0.01940952, -0.0718585, 0.021261575, -0.004203945, 0.014860424, 0.010255075, -0.071233585, 0.001474013, -0.10331818, 0.013450197, -0.006894685, -0.012824686, -0.0014220265, -0.016501855, -0.0040646135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, 19, 21, 23, 25, -1, 27, -1, 29, 31, -1, -1, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7313777, 2.448444, 0.0, 1.5239047, 0.0, 1.4009677, 5.3763375, 1.2137091, 0.0, 0.0, 0.0, 2.216372, 3.135758, 1.6972569, 0.0, 0.0, 1.9182084, 3.1744802, 1.1436291, 1.6769793, 0.0, 1.9945462, 0.0, 0.9508035, 2.1656303, 0.0, 0.0, 0.0, 0.57859164, 0.0, 0.9899225, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 28, 28, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, 20, 22, 24, 26, -1, 28, -1, 30, 32, -1, -1, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.008526402, 0.8202487, 0.01554433, 0.7106174, 1.0, 0.88461536, 0.010300118, 0.006744987, -0.024058644, 0.65763235, 1.0, 1.0, -0.013254324, 0.018130451, 0.39172632, 0.5277829, 1.0, 1.4230769, -0.012270498, 0.25952345, 0.01940952, 0.1490376, 0.3754838, -0.004203945, 0.014860424, 0.010255075, 0.40043047, 0.001474013, 1.0, 0.013450197, -0.006894685, -0.012824686, -0.0014220265, -0.016501855, -0.0040646135], "split_indices": [143, 142, 0, 142, 0, 142, 69, 1, 0, 0, 0, 139, 108, 122, 0, 0, 143, 143, 93, 1, 0, 142, 0, 143, 142, 0, 0, 0, 139, 0, 2, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1975.0, 95.0, 1873.0, 102.0, 1646.0, 227.0, 1507.0, 139.0, 118.0, 109.0, 1102.0, 405.0, 953.0, 149.0, 119.0, 286.0, 392.0, 561.0, 185.0, 101.0, 283.0, 109.0, 349.0, 212.0, 97.0, 88.0, 105.0, 178.0, 93.0, 256.0, 94.0, 118.0, 89.0, 89.0, 129.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00024636273, 0.004811768, -0.007903676, 0.01558651, -0.03303732, 0.005925552, 0.012293906, 0.0290232, -0.0129249515, 0.024623899, -0.117299594, -0.0050123613, 0.017527279, 0.009317669, 0.013840375, -0.0037236572, -0.02018605, 0.04164729, -0.055159424, 0.01921424, 0.016627531, -0.014892719, 0.0051818476, -0.009687901, 0.042432897, -0.011676705, 0.014403458, 0.08786055, 0.0024702155, 0.0015973019, 0.020082664, 0.005040527, -0.0077101937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, -1, -1, 19, 21, 23, -1, -1, 25, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.7445506, 0.79319865, 0.0, 1.5702121, 2.5734923, 3.2004037, 0.0, 3.0326998, 0.0, 2.1002972, 1.2389462, 0.0, 0.0, 2.2158446, 0.0, 0.0, 0.0, 1.9794164, 2.0086136, 1.6173176, 0.0, 0.0, 3.6575146, 0.0, 0.9077052, 0.0, 0.0, 1.9002789, 1.0146024, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 17, 17, 18, 18, 19, 19, 22, 22, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, -1, -1, 20, 22, 24, -1, -1, 26, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.007903676, 1.1740773, 0.69458735, 1.0, 0.012293906, 0.43557197, -0.0129249515, 1.4716803, 0.07692308, -0.0050123613, 0.017527279, 1.3495234, 0.013840375, -0.0037236572, -0.02018605, 0.45909482, 1.0, -1.0, 0.016627531, -0.014892719, 0.66891193, -0.009687901, 1.0, -0.011676705, 0.014403458, 0.26923078, 1.2595882, 0.0015973019, 0.020082664, 0.005040527, -0.0077101937], "split_indices": [43, 58, 0, 139, 142, 64, 0, 139, 0, 138, 1, 0, 0, 138, 0, 0, 0, 139, 39, 0, 0, 0, 139, 0, 108, 0, 0, 1, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1945.0, 112.0, 1514.0, 431.0, 1389.0, 125.0, 262.0, 169.0, 1206.0, 183.0, 170.0, 92.0, 1063.0, 143.0, 94.0, 89.0, 708.0, 355.0, 600.0, 108.0, 139.0, 216.0, 100.0, 500.0, 115.0, 101.0, 234.0, 266.0, 143.0, 91.0, 166.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024113692, -0.009440238, 0.06731945, -0.019158622, 0.010651012, 0.020072756, -0.007039211, -0.0054129395, -0.025312155, 0.0084575005, -0.10600867, -0.009017443, 0.09382691, -0.024936821, 0.0015913896, 0.00068289024, -0.012499972, -0.00022053071, 0.025528772, -0.009125416, 0.010688684, 0.038732152, -0.03728137, -0.019433284, 0.011875595, -0.011749899, -0.01736194, -0.012785921, 0.006101174, -0.004690624, 0.011634142], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.0116258, 2.1128438, 3.4722786, 5.5636454, 0.0, 0.0, 0.0, 2.2799318, 0.0, 2.142262, 3.4607964, 1.3410801, 3.783328, 0.0, 0.0, 1.1458492, 0.0, 0.0, 0.0, 1.3569078, 0.0, 1.7361734, 2.2068968, 1.8840227, 0.0, 2.4047203, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0248215, 1.0, 0.8589071, 0.010651012, 0.020072756, -0.007039211, 0.743426, -0.025312155, 0.5968419, 1.4554129, 0.5394974, 1.0, -0.024936821, 0.0015913896, 0.48706603, -0.012499972, -0.00022053071, 0.025528772, 0.03846154, 0.010688684, 1.0, 0.40043047, 1.2505668, 0.011875595, 0.32566002, -0.01736194, -0.012785921, 0.006101174, -0.004690624, 0.011634142], "split_indices": [125, 141, 15, 141, 0, 0, 0, 140, 0, 139, 138, 139, 50, 0, 0, 139, 0, 0, 0, 1, 0, 124, 139, 138, 0, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1875.0, 189.0, 1730.0, 145.0, 96.0, 93.0, 1634.0, 96.0, 1436.0, 198.0, 1192.0, 244.0, 91.0, 107.0, 1100.0, 92.0, 153.0, 91.0, 1007.0, 93.0, 373.0, 634.0, 216.0, 157.0, 534.0, 100.0, 92.0, 124.0, 419.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019306893, 0.0062015806, -0.06516255, -0.0056162514, 0.012046355, 0.005101734, -0.023164716, 0.0049530896, -0.018404495, -0.0064903814, 0.010867791, 0.00600324, -0.0094945235, -0.0052926163, 0.015196097, 0.009547378, -0.012698059, -0.01174151, 0.012213035, 0.005782304, -0.013539088, -0.0013848486, 0.013289656], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [1.0649468, 2.4778533, 4.5647507, 3.1362088, 0.0, 0.0, 0.0, 1.8635464, 0.0, 1.5626421, 0.0, 2.0427613, 0.0, 2.0767267, 0.0, 2.4566858, 0.0, 1.8677893, 0.0, 1.8839922, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.0, 1.0248215, 1.4758557, 0.991673, 0.012046355, 0.005101734, -0.023164716, 0.84749436, -0.018404495, 0.67325324, 0.010867791, 0.6985078, -0.0094945235, 0.54134214, 0.015196097, 0.44330555, -0.012698059, 0.45268637, 0.012213035, 0.3935085, -0.013539088, -0.0013848486, 0.013289656], "split_indices": [119, 141, 138, 140, 0, 0, 0, 143, 0, 143, 0, 140, 0, 141, 0, 141, 0, 143, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1835.0, 236.0, 1663.0, 172.0, 139.0, 97.0, 1570.0, 93.0, 1414.0, 156.0, 1239.0, 175.0, 1150.0, 89.0, 1025.0, 125.0, 862.0, 163.0, 755.0, 107.0, 654.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.00393236, 0.009017706, -0.008246698, -0.015435619, 0.060874574, -0.02283151, 0.011014658, 0.022402326, -0.011877227, -0.005015231, -0.11664637, 0.003799788, -0.013914379, -0.024509525, -0.047313146, -0.0060473634, 0.013274567, 0.001951962, -0.011414594, -0.015534409, 0.011274692, 0.006695778, -0.107630916, -0.014722017, 0.07947569, -0.0011568748, -0.023113938, -0.002865637, 0.008360124, -0.0024753509, 0.021692082], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.84289694, 0.0, 0.986361, 1.6699682, 5.480811, 2.838091, 0.0, 0.0, 0.0, 1.687208, 2.4134645, 1.7001952, 0.0, 0.0, 0.78612566, 1.4019961, 0.0, 0.0, 0.0, 2.3585157, 0.0, 1.446553, 2.6576455, 0.9823408, 3.0227437, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [0.08515571, 0.009017706, 1.0, 1.2514719, 0.9850181, 0.7456034, 0.011014658, 0.022402326, -0.011877227, 1.0, 0.79696983, 0.78044474, -0.013914379, -0.024509525, 0.87970835, 0.69135696, 0.013274567, 0.001951962, -0.011414594, 0.5275487, 0.011274692, 1.3221555, 0.1923077, 0.43702146, 0.4080126, -0.0011568748, -0.023113938, -0.002865637, 0.008360124, -0.0024753509, 0.021692082], "split_indices": [139, 0, 125, 140, 141, 141, 0, 0, 0, 84, 142, 140, 0, 0, 140, 142, 0, 0, 0, 140, 0, 138, 1, 141, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 91.0, 1985.0, 1798.0, 187.0, 1698.0, 100.0, 98.0, 89.0, 1427.0, 271.0, 1339.0, 88.0, 95.0, 176.0, 1244.0, 95.0, 88.0, 88.0, 1152.0, 92.0, 928.0, 224.0, 717.0, 211.0, 126.0, 98.0, 628.0, 89.0, 120.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032052856, 0.0053912676, -0.07000235, -0.0065267053, 0.022747567, 0.0027506175, -0.01886684, 0.004949279, -0.017104968, 0.048402883, -0.010702772, 0.0023437068, 0.019052831, 0.0020723876, -0.011868332, 0.010681037, -0.059107255, 0.020534126, -0.085745595, 0.002073456, -0.015071526, -0.0039407136, 0.012900906, -0.0016718794, -0.015704798, 0.025850652, -0.018593354, 0.055803552, -0.0062663998, 0.0011037302, 0.024555137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, -1, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [1.1834768, 4.83305, 2.7191744, 3.2720141, 0.0, 0.0, 0.0, 1.1018237, 0.0, 2.808311, 1.6429477, 2.079943, 0.0, 1.7266552, 0.0, 0.0, 1.4920869, 2.336318, 0.91052806, 0.0, 0.0, 3.8928633, 0.0, 0.0, 0.0, 1.6358339, 0.0, 3.9158711, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, -1, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.3316327, 1.4632982, 1.0268755, 0.022747567, 0.0027506175, -0.01886684, 0.0, -0.017104968, 0.5968419, 1.0, 1.0, 0.019052831, 1.0, -0.011868332, 0.010681037, 0.31969234, 0.7155382, 1.0, 0.002073456, -0.015071526, 0.63804257, 0.012900906, -0.0016718794, -0.015704798, 0.45846936, -0.018593354, 0.43165883, -0.0062663998, 0.0011037302, 0.024555137], "split_indices": [119, 139, 138, 142, 0, 0, 0, 0, 0, 139, 40, 122, 0, 58, 0, 0, 140, 143, 39, 0, 0, 139, 0, 0, 0, 143, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1826.0, 235.0, 1733.0, 93.0, 129.0, 106.0, 1620.0, 113.0, 429.0, 1191.0, 324.0, 105.0, 1065.0, 126.0, 120.0, 204.0, 880.0, 185.0, 109.0, 95.0, 718.0, 162.0, 94.0, 91.0, 617.0, 101.0, 461.0, 156.0, 373.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020390567, -0.008349775, 0.060237754, -0.012241536, -0.0005504172, 0.020961331, -0.009231598, -0.010072599, 0.014610896, -0.00067890633, -0.01689485, -0.012267794, 0.07360033, 0.0035421122, -0.11736775, -0.0036006228, 0.020628199, -0.009661181, 0.013206228, -0.006233243, -0.017240304, 0.01884486, -0.06959055, 0.03910516, -0.0075596827, 0.00817469, -0.01691301, -0.00039769514, 0.013693251, -0.0068850345, 0.009731607], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [0.81156844, 1.6680714, 4.3296824, 0.0, 2.4508874, 0.0, 0.0, 2.4595263, 0.0, 1.339426, 0.0, 2.2365415, 3.0539846, 1.9853609, 0.53308296, 0.0, 0.0, 1.8125585, 0.0, 0.0, 0.0, 1.3757467, 2.647326, 2.4950488, 0.0, 1.3182948, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 21, 21, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 1.0, -0.012241536, 1.1740773, 0.020961331, -0.009231598, 1.529458, 0.014610896, 1.0, -0.01689485, 0.7478913, 0.5271865, 0.71983254, 0.70742667, -0.0036006228, 0.020628199, 1.0, 0.013206228, -0.006233243, -0.017240304, 1.0, 0.3250815, 0.44171807, -0.0075596827, 1.0, -0.01691301, -0.00039769514, 0.013693251, -0.0068850345, 0.009731607], "split_indices": [125, 1, 15, 0, 139, 0, 0, 138, 0, 42, 0, 143, 139, 140, 142, 0, 0, 109, 0, 0, 0, 0, 143, 143, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1875.0, 190.0, 120.0, 1755.0, 96.0, 94.0, 1648.0, 107.0, 1556.0, 92.0, 1346.0, 210.0, 1170.0, 176.0, 115.0, 95.0, 1061.0, 109.0, 88.0, 88.0, 719.0, 342.0, 592.0, 127.0, 192.0, 150.0, 411.0, 181.0, 103.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.004756988, 0.010405759, -0.0063278503, 0.0016287391, 0.013853584, 0.010967295, -0.009703248, 0.0016245125, 0.012936152, -0.04307198, 0.015436973, 0.004332431, -0.11855507, 0.013341853, 0.0010769938, -0.0015453411, -0.021134652, -0.010565135, 0.01237772, 0.0032297706, -0.013590597, -0.017031536, 0.0112671545, 0.0021000889, -0.0109369885], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, -1, 15, -1, 17, -1, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [0.79207706, 2.1401138, 0.0, 1.6409298, 0.0, 1.7996755, 0.0, 0.9309917, 0.0, 2.3216395, 1.9517326, 0.0, 1.8177204, 0.0, 1.4670613, 0.0, 0.0, 1.6218634, 0.0, 1.8737313, 0.0, 2.5039504, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, -1, 16, -1, 18, -1, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.235102, 0.99982387, -0.0063278503, 0.8678284, 0.013853584, 0.81395763, -0.009703248, 1.0, 0.012936152, 0.3758132, -0.42307693, 0.004332431, 0.49770924, 0.013341853, 0.69135696, -0.0015453411, -0.021134652, 0.6004196, 0.01237772, 0.49905074, -0.013590597, 0.35278645, 0.0112671545, 0.0021000889, -0.0109369885], "split_indices": [139, 139, 0, 142, 0, 140, 0, 5, 0, 140, 1, 0, 142, 0, 142, 0, 0, 142, 0, 139, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1903.0, 158.0, 1781.0, 122.0, 1627.0, 154.0, 1508.0, 119.0, 356.0, 1152.0, 166.0, 190.0, 125.0, 1027.0, 90.0, 100.0, 938.0, 89.0, 845.0, 93.0, 713.0, 132.0, 505.0, 208.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.011583226, 0.004653389, 0.08018493, -0.0048071994, 0.012162683, 0.019405372, -0.0050505367, 0.0068163644, -0.019849375, 0.014343517, -0.010981645, 0.021352168, -0.0072137103, 0.011198218, 0.015973896, 0.0021435088, 0.011614657, 0.013740378, -0.01348216, -0.008209987, 0.085079074, 0.0013353381, -0.008558444, 0.021136764, 0.0018649809], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [0.9793209, 2.0705192, 2.812613, 3.8970497, 0.0, 0.0, 0.0, 1.4336317, 0.0, 0.9297763, 0.0, 1.9939393, 0.0, 1.2562656, 0.0, 1.9330416, 0.0, 1.7569517, 0.0, 1.4315336, 2.2147632, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.0, 1.0248215, 0.9850181, 0.8589071, 0.012162683, 0.019405372, -0.0050505367, 1.0, -0.019849375, 0.7456034, -0.010981645, 0.78044474, -0.0072137103, 3.1923077, 0.015973896, 1.3461539, 0.011614657, 1.0, -0.01348216, 0.54264086, -0.03846154, 0.0013353381, -0.008558444, 0.021136764, 0.0018649809], "split_indices": [125, 141, 141, 141, 0, 0, 0, 84, 0, 141, 0, 140, 0, 1, 0, 1, 0, 50, 0, 139, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1871.0, 189.0, 1731.0, 140.0, 101.0, 88.0, 1633.0, 98.0, 1534.0, 99.0, 1419.0, 115.0, 1322.0, 97.0, 1217.0, 105.0, 1122.0, 95.0, 858.0, 264.0, 671.0, 187.0, 91.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0001712467, 0.0069375713, -0.07223223, -0.0035347966, 0.012610355, -0.0039382577, -0.010471276, 0.005230282, -0.010455392, -0.025404382, 0.040470432, 0.012380845, -0.13964705, -0.015974224, 0.10248006, -0.0144747645, 0.017351454, -0.027308855, -0.0025931678, -0.09569546, 0.079961486, 0.1690284, 0.0015621496, 0.061280724, -0.05143365, -0.001955048, -0.018849714, 0.016623795, -0.0005345546, 0.0062219365, 0.029813817, 0.017603816, -0.0047306935, -0.011579724, -0.009804641, 0.0070417686, -0.009195345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, -1, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [1.014594, 2.3636177, 0.18885404, 1.5415524, 0.0, 0.0, 0.0, 1.7294714, 0.0, 3.6994, 2.6075835, 2.7868097, 3.2321358, 2.9827645, 2.0520039, 1.5455108, 0.0, 0.0, 0.0, 1.5051384, 1.3027186, 2.7718072, 0.0, 2.2554853, 0.68920684, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3180878, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 23, 23, 24, 24, 33, 33], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, -1, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.1489253, 0.9648798, 1.0, 0.83529425, 0.012610355, -0.0039382577, -0.010471276, 1.0, -0.010455392, 1.3495234, 1.321129, 0.49558082, 1.0, 1.0, 1.0, 1.0, 0.017351454, -0.027308855, -0.0025931678, 1.0, 0.28946778, 0.6248974, 0.0015621496, 0.27905032, 1.0, -0.001955048, -0.018849714, 0.016623795, -0.0005345546, 0.0062219365, 0.029813817, 0.017603816, -0.0047306935, 1.0, -0.009804641, 0.0070417686, -0.009195345], "split_indices": [143, 141, 39, 142, 0, 0, 0, 39, 0, 138, 138, 143, 53, 12, 108, 53, 0, 0, 0, 15, 143, 141, 0, 142, 12, 0, 0, 0, 0, 0, 0, 0, 0, 124, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1894.0, 177.0, 1741.0, 153.0, 88.0, 89.0, 1602.0, 139.0, 857.0, 745.0, 644.0, 213.0, 390.0, 355.0, 552.0, 92.0, 98.0, 115.0, 213.0, 177.0, 201.0, 154.0, 181.0, 371.0, 117.0, 96.0, 88.0, 89.0, 110.0, 91.0, 88.0, 93.0, 200.0, 171.0, 99.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00091130426, -0.006120168, 0.008264573, 0.0020583244, -0.015058068, -0.00440641, 0.01167744, 0.0047219926, -0.08249941, -0.0057486165, 0.015100852, -0.015080884, -0.0017127974, 0.012112462, -0.071067505, 0.0031557025, 0.01192882, -0.12412974, 0.0055820416, -0.011502832, 0.074728675, -0.0058535775, -0.019597074, 0.01914613, -0.054042928, 0.020421213, -0.00562096, -0.0028939385, 0.009080299, -0.01005382, 0.003894759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.897459, 2.2932322, 0.0, 1.362336, 0.0, 1.2396712, 0.0, 2.3848715, 0.81271815, 1.6951658, 0.0, 0.0, 0.0, 1.0952996, 2.1006827, 1.1047603, 0.0, 1.0367129, 0.0, 1.13953, 3.0348275, 0.0, 0.0, 1.7503939, 1.5824448, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.6902589, 0.008264573, 1.5856481, -0.015058068, 1.486009, 0.01167744, 1.4451531, 0.87269676, 0.57594323, 0.015100852, -0.015080884, -0.0017127974, 1.8076923, 0.81395763, 0.5289018, 0.01192882, -0.03846154, 0.0055820416, 0.31376776, 0.63227344, -0.0058535775, -0.019597074, 0.22394206, 0.53846157, 0.020421213, -0.00562096, -0.0028939385, 0.009080299, -0.01005382, 0.003894759], "split_indices": [84, 138, 0, 138, 0, 138, 0, 138, 140, 140, 0, 0, 0, 1, 140, 142, 0, 1, 0, 139, 142, 0, 0, 142, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1941.0, 121.0, 1837.0, 104.0, 1739.0, 98.0, 1557.0, 182.0, 1453.0, 104.0, 89.0, 93.0, 1141.0, 312.0, 1053.0, 88.0, 220.0, 92.0, 874.0, 179.0, 115.0, 105.0, 508.0, 366.0, 90.0, 89.0, 304.0, 204.0, 244.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.000106645675, 0.006060113, -0.007248723, -0.0024753816, 0.013084766, 0.011435724, -0.0098476345, -0.0007704495, -0.016859746, 0.007416263, -0.008874709, -0.0009292865, 0.012969675, -0.033914667, 0.028535178, -0.0031330206, -0.014603174, -0.0055710888, 0.12025101, 0.012327882, -0.03363539, 0.046507355, -0.016631717, 0.02471066, 0.0009406276, -0.012567269, 0.00041483343, -0.002304513, 0.01986181], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, 5, -1, -1, 7, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.88381875, 2.0130837, 0.0, 1.523674, 0.0, 0.0, 2.397826, 1.1336571, 0.0, 1.4695172, 0.0, 1.3101166, 0.0, 2.1949294, 2.2271967, 1.9240743, 0.0, 4.3447604, 2.7138255, 0.0, 1.3979603, 4.147235, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 6, 6, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, -1, 8, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.638525, 1.0977916, -0.007248723, 1.0, 0.013084766, 0.011435724, 1.521886, 1.0, -0.016859746, 1.0, -0.008874709, 1.0, 0.012969675, 0.59589326, 0.56254256, -0.30769232, -0.014603174, 0.45494753, 1.0, 0.012327882, 0.15563406, 0.33220515, -0.016631717, 0.02471066, 0.0009406276, -0.012567269, 0.00041483343, -0.002304513, 0.01986181], "split_indices": [138, 143, 0, 104, 0, 0, 138, 119, 0, 90, 0, 111, 0, 141, 141, 1, 0, 140, 124, 0, 143, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1890.0, 155.0, 1769.0, 121.0, 105.0, 1664.0, 1574.0, 90.0, 1440.0, 134.0, 1348.0, 92.0, 636.0, 712.0, 499.0, 137.0, 519.0, 193.0, 97.0, 402.0, 392.0, 127.0, 90.0, 103.0, 117.0, 285.0, 269.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0013395614, 0.008546793, -0.06997408, 0.020279532, -0.022480434, 0.008919372, -0.021023089, 0.004109979, 0.025579257, 0.010724892, -0.009092626, 0.019602219, -0.006075553, 0.009573099, 0.015068987, 0.009902219, 0.0018373654, 0.038928535, -0.023910128, -0.0007628523, 0.02052946, -0.001433513, -0.014378537, -0.0074410248, 0.0048543443, -0.007939274, 0.0049163974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [1.0639259, 5.147154, 4.241631, 6.8165708, 0.0, 0.0, 0.0, 1.0529994, 0.0, 0.9937132, 0.0, 1.8313677, 0.0, 0.8953887, 0.0, 0.0, 1.1374106, 3.222411, 1.8941557, 1.4307245, 0.0, 2.3351684, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.0745199, 1.115853, 1.0, 0.86948144, -0.022480434, 0.008919372, -0.021023089, 1.0, 0.025579257, 0.743426, -0.009092626, 0.7075807, -0.006075553, 0.10083316, 0.015068987, 0.009902219, -0.03846154, -0.1923077, 0.5361265, 1.0, 0.02052946, 0.26911485, -0.014378537, -0.0074410248, 0.0048543443, -0.007939274, 0.0049163974], "split_indices": [141, 140, 69, 141, 0, 0, 0, 84, 0, 140, 0, 141, 0, 139, 0, 0, 1, 1, 140, 5, 0, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1880.0, 190.0, 1790.0, 90.0, 89.0, 101.0, 1675.0, 115.0, 1566.0, 109.0, 1393.0, 173.0, 1294.0, 99.0, 103.0, 1191.0, 488.0, 703.0, 394.0, 94.0, 592.0, 111.0, 158.0, 236.0, 233.0, 359.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.003864291, -0.018189985, 0.026458586, -0.0018474472, -0.08951048, 0.047329113, -0.008926579, -0.049504753, 0.021646762, -0.0029805154, -0.015635444, 0.022825072, 0.01965627, 0.013379006, -0.017662464, -0.0052095754, 0.053781416, 0.045082346, -0.013273648, -0.0017105686, 0.004078447, 0.021491146, -0.0011987585, 0.016612668, 0.01747775, 0.009686351, -0.010331879, 0.053678535, -0.0121154245, -0.0060376716, 0.13330016, 0.006909316, -0.012905413, 0.02706076, 0.00019625764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, 17, -1, 19, -1, -1, 21, 23, -1, -1, -1, -1, 25, 27, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [1.0299878, 1.2191733, 2.465948, 0.95283985, 0.77823305, 3.1631546, 0.0, 2.2462518, 1.3507235, 0.0, 0.0, 2.5725446, 0.0, 0.15706411, 0.0, 0.0, 3.5170074, 2.4000459, 0.0, 0.0, 0.0, 0.0, 2.9641805, 2.7217376, 0.0, 0.0, 0.0, 1.996975, 0.0, 2.2181592, 3.246052, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 16, 16, 17, 17, 22, 22, 23, 23, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, 18, -1, 20, -1, -1, 22, 24, -1, -1, -1, -1, 26, 28, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.1489253, 0.22765444, 1.0, 1.058468, -0.008926579, 0.2783235, 1.0, -0.0029805154, -0.015635444, 0.94488615, 0.01965627, 0.1612983, -0.017662464, -0.0052095754, 0.33634365, 0.7612845, -0.013273648, -0.0017105686, 0.004078447, 0.021491146, 1.0, 0.65763235, 0.01747775, 0.009686351, -0.010331879, 0.5485319, -0.0121154245, 0.37846237, 1.0, 0.006909316, -0.012905413, 0.02706076, 0.00019625764], "split_indices": [71, 58, 143, 139, 93, 140, 0, 141, 17, 0, 0, 139, 0, 140, 0, 0, 141, 139, 0, 0, 0, 0, 122, 139, 0, 0, 0, 143, 0, 143, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1046.0, 1021.0, 851.0, 195.0, 865.0, 156.0, 281.0, 570.0, 103.0, 92.0, 743.0, 122.0, 188.0, 93.0, 173.0, 397.0, 650.0, 93.0, 89.0, 99.0, 101.0, 296.0, 533.0, 117.0, 151.0, 145.0, 420.0, 113.0, 240.0, 180.0, 149.0, 91.0, 88.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072066765, -0.0070840735, 0.06311646, 0.0007343048, -0.013092163, -0.008647394, 0.022257, -0.007677746, 0.015452857, 0.015631646, -0.09388344, -0.0030276293, 0.12564364, -0.028891254, -0.02817956, -0.01364605, 0.009562713, 0.023948785, 0.0006954966, -0.01583775, 0.006383758, 0.00034257586, -0.009130088, -0.020843314, 0.050545726, -0.001113798, -0.012126652, 0.016014941, -0.0008522111, -0.0027860764, 0.0074501666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, -1, -1, -1, -1, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [0.8425033, 1.8260423, 4.4843116, 2.295069, 0.0, 0.0, 0.0, 3.379815, 0.0, 2.7178335, 4.3721895, 1.1858351, 2.5943067, 3.193891, 0.0, 1.110183, 0.0, 0.0, 0.0, 0.0, 0.0, 0.92107636, 0.0, 1.2066121, 1.6638322, 1.0294443, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, -1, -1, -1, -1, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1314721, 1.0, 0.9850181, -0.013092163, -0.008647394, 0.022257, 0.67325324, 0.015452857, 0.56020635, 0.79587597, 3.0384614, 1.0, 0.6097397, -0.02817956, 1.0, 0.009562713, 0.023948785, 0.0006954966, -0.01583775, 0.006383758, 0.41786185, -0.009130088, 1.2985203, 0.3648895, 1.0, -0.012126652, 0.016014941, -0.0008522111, -0.0027860764, 0.0074501666], "split_indices": [125, 143, 16, 141, 0, 0, 0, 143, 0, 143, 141, 1, 115, 141, 0, 113, 0, 0, 0, 0, 0, 142, 0, 138, 143, 93, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1886.0, 188.0, 1774.0, 112.0, 97.0, 91.0, 1682.0, 92.0, 1324.0, 358.0, 1132.0, 192.0, 266.0, 92.0, 1022.0, 110.0, 98.0, 94.0, 111.0, 155.0, 866.0, 156.0, 609.0, 257.0, 509.0, 100.0, 90.0, 167.0, 376.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.001238744, -0.006125775, 0.007499892, 0.016143702, -0.05213536, 0.00048925943, 0.01563221, -0.021964272, -0.015430088, 0.010718372, -0.010334585, -0.05982594, 0.014224008, -0.0024803753, 0.01243219, 0.03692801, -0.16841681, -0.014136288, 0.010668411, 0.012756297, -0.007374201, -0.004850094, -0.029354637, 0.008434246, -0.103264265, -0.012844322, 0.011688031, -0.014602256, -0.006050594, 0.013939286, -0.011155491, 0.0057288436, -0.005856278], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.7730946, 1.9979885, 0.0, 2.8834608, 1.9604348, 1.2554504, 0.0, 3.0525713, 0.0, 1.6133808, 0.0, 4.192132, 0.0, 1.2266046, 0.0, 2.1164513, 2.8209434, 1.7521616, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6037663, 0.32177544, 1.536063, 0.0, 0.0, 0.0, 1.4363067, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 23, 23, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 0.6599491, 0.007499892, 1.4138024, 3.0, 0.55072904, 0.01563221, 1.1794761, -0.015430088, 0.5260858, -0.010334585, 1.0, 0.014224008, 3.0384614, 0.01243219, 0.83529425, 0.87269676, 1.3148584, 0.010668411, 0.012756297, -0.007374201, -0.004850094, -0.029354637, 0.38551757, 0.4000699, 0.31032252, 0.011688031, -0.014602256, -0.006050594, 0.53846157, -0.011155491, 0.0057288436, -0.005856278], "split_indices": [84, 141, 0, 138, 0, 141, 0, 142, 0, 142, 0, 108, 0, 1, 0, 142, 140, 138, 0, 0, 0, 0, 0, 141, 142, 139, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1950.0, 125.0, 1314.0, 636.0, 1182.0, 132.0, 491.0, 145.0, 1076.0, 106.0, 399.0, 92.0, 964.0, 112.0, 211.0, 188.0, 871.0, 93.0, 116.0, 95.0, 96.0, 92.0, 695.0, 176.0, 581.0, 114.0, 88.0, 88.0, 457.0, 124.0, 286.0, 171.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.005519691, -0.015384112, 0.040183663, -0.007501362, -0.015851354, 0.13914555, -0.056649998, -0.0010567618, -0.011304031, -0.0051512835, 0.03216036, 0.007761041, -0.0196812, -0.009042606, 0.0075418763, 0.00047552178, -0.009533105, -0.009359037, 0.010696976, 0.0048727216, -0.08552679, -0.007856556, 0.0078021274, -0.017057167, -0.0003285539, 0.007789588, -0.0259231, -0.0002895744, -0.011786192], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, 27, -1, -1], "loss_changes": [0.9345852, 1.9236717, 3.5264862, 1.0991324, 0.0, 6.3312645, 3.5001874, 0.93012875, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1325791, 0.0, 1.3007766, 0.0, 1.2325094, 0.0, 0.89202106, 1.251961, 1.2641879, 0.0, 0.0, 0.0, 0.0, 1.4269303, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, 28, -1, -1], "split_conditions": [0.87650514, 0.89908534, 1.0, 0.77706695, -0.015851354, -0.3846154, 1.6290085, 0.69919735, -0.011304031, -0.0051512835, 0.03216036, 0.007761041, -0.0196812, 0.6932683, 0.0075418763, 0.60124767, -0.009533105, 0.5139595, 0.010696976, 0.42824298, 1.0, -0.30769232, 0.0078021274, -0.017057167, -0.0003285539, 0.007789588, 0.32285196, -0.0002895744, -0.011786192], "split_indices": [139, 141, 69, 139, 0, 1, 138, 141, 0, 0, 0, 0, 0, 143, 0, 143, 0, 140, 0, 143, 97, 1, 0, 0, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1705.0, 368.0, 1616.0, 89.0, 182.0, 186.0, 1523.0, 93.0, 89.0, 93.0, 95.0, 91.0, 1379.0, 144.0, 1242.0, 137.0, 1137.0, 105.0, 958.0, 179.0, 816.0, 142.0, 88.0, 91.0, 142.0, 674.0, 539.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0041134977, 0.00891636, -0.0004932003, -0.009380524, 0.059364494, 0.00049789145, -0.018863495, 0.01989366, -0.0024555426, -0.0068592667, 0.012229088, 0.00846548, -0.06516099, -0.0051926677, 0.017384788, 0.0032623687, -0.01623307, 0.025489835, -0.058491275, 0.014577135, 0.0083900355, -0.0044269357, -0.015411526, -0.05468739, 0.06048939, 0.005014164, -0.006908384, -0.011679, 0.000830876, 0.011560164, -0.0045459135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.80828404, 0.0, 1.0410744, 3.017358, 2.9633594, 1.4471216, 0.0, 0.0, 0.0, 1.3607385, 0.0, 2.724134, 3.012042, 1.821763, 0.0, 0.0, 0.0, 1.4541498, 2.1041272, 0.0, 2.0342152, 0.91734123, 0.0, 1.0954236, 1.9794421, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 20, 20, 21, 21, 23, 23, 24, 24], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.00891636, 0.96261597, 1.0230856, 1.0202819, 0.82570606, -0.018863495, 0.01989366, -0.0024555426, 1.0, 0.012229088, 0.7075807, 0.46161503, 1.0, 0.017384788, 0.0032623687, -0.01623307, 0.13268687, 0.42029098, 0.014577135, 0.3576206, 1.0, -0.015411526, 0.2916033, 0.5326464, 0.005014164, -0.006908384, -0.011679, 0.000830876, 0.011560164, -0.0045459135], "split_indices": [1, 0, 142, 140, 143, 140, 0, 0, 0, 0, 0, 141, 141, 109, 0, 0, 0, 143, 142, 0, 140, 59, 0, 143, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 106.0, 1957.0, 1704.0, 253.0, 1615.0, 89.0, 95.0, 158.0, 1523.0, 92.0, 1206.0, 317.0, 1114.0, 92.0, 158.0, 159.0, 707.0, 407.0, 88.0, 619.0, 260.0, 147.0, 280.0, 339.0, 141.0, 119.0, 141.0, 139.0, 223.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039419765, -0.013654533, 0.050361443, -0.005946608, -0.015582289, 0.015447618, -0.0084633725, -0.014551839, 0.010350336, -0.0154410945, 0.010855763, -0.022098212, 0.0073884143, -0.0061322334, -0.12655646, -0.021246988, 0.067282274, -0.02148706, -0.0038242277, -0.04474114, 0.03357268, -0.0068289945, 0.021359293, -0.07424956, 0.020230308, -0.02209546, 0.01120341, -0.0471476, -0.0193379, 0.012068837, -0.007243356, 0.008448581, -0.0125163095, -0.011010065, 0.0016438123], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0880778, 1.9176899, 1.9169785, 1.5634578, 0.0, 0.0, 3.4157867, 1.0270836, 0.0, 0.0, 0.0, 2.3649087, 0.0, 1.3648599, 1.4662845, 1.3137002, 4.1654887, 0.0, 0.0, 1.3688843, 1.3365476, 0.0, 0.0, 1.5852618, 2.07587, 1.9663293, 0.0, 1.6011666, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.94474304, 1.0106246, 1.0199403, 1.0, -0.015582289, 0.015447618, 1.0, 0.85294276, 0.010350336, -0.0154410945, 0.010855763, 0.67325324, 0.0073884143, 0.5485319, 1.0, 0.88461536, 1.0, -0.02148706, -0.0038242277, 1.0, 0.32566002, -0.0068289945, 0.021359293, 0.40235823, 1.0, 0.23533852, 0.01120341, 1.0, -0.0193379, 0.012068837, -0.007243356, 0.008448581, -0.0125163095, -0.011010065, 0.0016438123], "split_indices": [142, 140, 143, 90, 0, 0, 61, 143, 0, 0, 0, 143, 0, 143, 111, 1, 93, 0, 0, 83, 139, 0, 0, 143, 13, 140, 0, 13, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1750.0, 313.0, 1660.0, 90.0, 113.0, 200.0, 1539.0, 121.0, 89.0, 111.0, 1418.0, 121.0, 1230.0, 188.0, 1020.0, 210.0, 94.0, 94.0, 714.0, 306.0, 109.0, 101.0, 491.0, 223.0, 179.0, 127.0, 400.0, 91.0, 107.0, 116.0, 88.0, 91.0, 201.0, 199.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0031781907, -0.008600849, 0.0075067673, 0.010927655, 0.002711895, -0.0128380405, 0.052148312, -0.07419034, 0.025204532, -0.051110297, 0.12435171, -0.037232917, -0.026821677, 0.01869869, -0.003838844, 0.002670082, -0.012250404, 0.0028540734, 0.025609178, -0.0015581575, -0.01448776, -0.03683086, 0.05896062, -0.069035664, 0.009585653, -0.0048665963, -0.01839391, 0.014415989, -0.00010151583, 0.0036415712, -0.020647794, -0.056974802, 0.008371733, -0.0003937184, -0.013398132], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, 21, -1, -1, -1, -1, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.8006703, 0.0, 0.9652106, 0.0, 1.4521369, 3.353957, 3.3699417, 3.9438932, 4.167753, 1.0332725, 3.3574896, 1.7741712, 0.0, 0.0, 1.5580544, 0.0, 0.0, 0.0, 0.0, 2.280935, 0.0, 2.3181872, 1.3234627, 2.9711635, 0.0, 1.8694595, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0414813, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25, 31, 31], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, 22, -1, -1, -1, -1, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.0919324, -0.008600849, 0.1384309, 0.010927655, 1.0, -0.1923077, 1.0, 0.90278953, -0.03846154, 0.37551925, 0.90278953, 1.0, -0.026821677, 0.01869869, 0.5271865, 0.002670082, -0.012250404, 0.0028540734, 0.025609178, 1.0, -0.01448776, 0.50326043, 0.67325324, 0.6065063, 0.009585653, 0.33220515, -0.01839391, 0.014415989, -0.00010151583, 0.0036415712, -0.020647794, 0.3020134, 0.008371733, -0.0003937184, -0.013398132], "split_indices": [143, 0, 143, 0, 61, 1, 17, 140, 1, 143, 140, 137, 0, 0, 139, 0, 0, 0, 0, 97, 0, 140, 143, 141, 0, 140, 0, 0, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 96.0, 1978.0, 89.0, 1889.0, 1437.0, 452.0, 550.0, 887.0, 186.0, 266.0, 462.0, 88.0, 135.0, 752.0, 89.0, 97.0, 154.0, 112.0, 347.0, 115.0, 493.0, 259.0, 205.0, 142.0, 405.0, 88.0, 107.0, 152.0, 116.0, 89.0, 255.0, 150.0, 151.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016141247, 0.0032490802, -0.010368972, -0.00449448, 0.013963804, 0.0010297005, -0.011617084, -0.0059242416, 0.013153205, -0.012102405, 0.010010098, -0.00077719416, -0.016509568, -0.013824798, 0.01043992, -0.0015531157, -0.015905952, 0.044152427, -0.025665944, 0.0006744154, 0.01857087, -0.0008619154, -0.015976733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, -1, -1, -1, -1], "loss_changes": [1.0260892, 2.083757, 0.0, 1.1517907, 0.0, 1.6144533, 0.0, 1.1063641, 0.0, 2.7653587, 0.0, 2.0392373, 0.0, 2.3561664, 0.0, 1.3434476, 0.0, 2.229353, 1.8242261, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1185367, -0.010368972, 1.040707, 0.013963804, 1.0, -0.011617084, 5.0, 0.013153205, 0.8833981, 0.010010098, 1.4451531, -0.016509568, 0.63804257, 0.01043992, 1.0, -0.015905952, 0.49448743, 0.54264086, 0.0006744154, 0.01857087, -0.0008619154, -0.015976733], "split_indices": [143, 142, 0, 141, 0, 125, 0, 0, 0, 143, 0, 138, 0, 139, 0, 53, 0, 139, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1973.0, 94.0, 1867.0, 106.0, 1779.0, 88.0, 1689.0, 90.0, 1596.0, 93.0, 1486.0, 110.0, 1322.0, 164.0, 1219.0, 103.0, 421.0, 798.0, 333.0, 88.0, 708.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.0032502236, -0.0015442844, 0.010302649, 0.005440273, -0.013264445, -0.005599992, 0.012809408, 0.0040839547, -0.07765996, -0.010210818, 0.06673765, -0.00022187173, -0.016421882, 0.001402297, -0.014811659, 0.1486271, -0.009968282, -0.008896841, 0.012452384, 0.027818058, 0.0033324473, -0.02013931, 0.009358256, 0.012098264, -0.06262355, -0.0011832456, 0.013309632, -0.0124013275, -0.0013888718], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.99119973, 1.8102931, 0.0, 2.5417032, 0.0, 1.2016547, 0.0, 1.3595518, 1.3321418, 1.979474, 3.8431191, 0.0, 0.0, 1.4455725, 0.0, 2.8232536, 0.0, 1.2120316, 0.0, 0.0, 0.0, 1.2983704, 0.0, 1.5607128, 1.2236533, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.3654754, 1.6290085, 0.010302649, 0.95420814, -0.013264445, 0.743426, 0.012809408, 0.5676343, 1.0, 0.6526074, 0.23076923, -0.00022187173, -0.016421882, 3.0384614, -0.014811659, 1.0, -0.009968282, 0.54929537, 0.012452384, 0.027818058, 0.0033324473, 0.31376776, 0.009358256, 0.3159845, 1.0, -0.0011832456, 0.013309632, -0.0124013275, -0.0013888718], "split_indices": [140, 138, 0, 140, 0, 140, 0, 140, 126, 142, 1, 0, 0, 1, 0, 109, 0, 142, 0, 0, 0, 139, 0, 142, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1977.0, 95.0, 1877.0, 100.0, 1722.0, 155.0, 1518.0, 204.0, 1236.0, 282.0, 109.0, 95.0, 1140.0, 96.0, 189.0, 93.0, 1052.0, 88.0, 89.0, 100.0, 948.0, 104.0, 539.0, 409.0, 450.0, 89.0, 181.0, 228.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011165192, 0.00616375, -0.057523325, -0.0041573197, 0.019004732, 0.0035445709, -0.0199944, 0.004864748, -0.0130932955, -0.00333868, 0.0099519715, 0.009476136, -0.01591399, -0.00037526223, 0.014644238, 0.010927083, -0.056109317, -0.0020773783, 0.013913271, 0.0011450752, -0.010184231, 0.011158241, -0.011674243, -0.0009030243, 0.011364285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, -1, -1], "loss_changes": [0.85498756, 3.4996824, 3.1512902, 1.9970374, 0.0, 0.0, 0.0, 1.2656875, 0.0, 2.994846, 0.0, 1.8701427, 0.0, 0.81449383, 0.0, 1.7922889, 0.67356, 1.4812393, 0.0, 0.0, 0.0, 1.8103831, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.3316327, 1.4836318, 1.0268755, 0.019004732, 0.0035445709, -0.0199944, 0.8090817, -0.0130932955, 1.4632982, 0.0099519715, 0.69135696, -0.01591399, 0.54264086, 0.014644238, 0.5289018, 1.0, 0.45285252, 0.013913271, 0.0011450752, -0.010184231, 1.0, -0.011674243, -0.0009030243, 0.011364285], "split_indices": [119, 139, 138, 142, 0, 0, 0, 142, 0, 138, 0, 142, 0, 139, 0, 142, 13, 142, 0, 0, 0, 71, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2082.0, 1844.0, 238.0, 1746.0, 98.0, 144.0, 94.0, 1630.0, 116.0, 1500.0, 130.0, 1386.0, 114.0, 1293.0, 93.0, 1075.0, 218.0, 976.0, 99.0, 88.0, 130.0, 875.0, 101.0, 731.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-2.1938986E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}