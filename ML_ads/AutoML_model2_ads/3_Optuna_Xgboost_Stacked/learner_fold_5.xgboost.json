{"learner": {"attributes": {"best_iteration": "92", "best_score": "1.168841"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "143"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.0036250432, -0.199992, 0.31938982, -0.25390017, 0.03906911, 0.15724118, 0.55775344, -0.3367241, -0.1415493, -0.005370666, 0.012952551, 0.040502775, 0.23618658, 0.02480839, 0.7316565, -0.36125222, -0.019636871, -0.07256361, -0.032068953, 0.016783936, -0.0073971497, 0.0043365, 0.037631154, 0.05874722, 0.091634184, -0.04427081, -0.33938354, -0.13252841, 0.008688821, -0.36821032, -0.0255675, -0.0203179, -0.0027304103, -0.33339173, -0.042898457, -0.038562115, -0.028633347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, 21, -1, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [131.36238, 16.598904, 30.263214, 9.779915, 1.9889427, 4.2946367, 17.071236, 2.0827942, 5.511718, 0.0, 0.0, 2.7404315, 7.5113134, 0.0, 5.4056244, 0.9173889, 0.0, 3.0788026, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.97969437, 1.7395926, 0.0, 0.63905334, 0.0, 0.0, 0.0, 0.47190094, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 15, 15, 17, 17, 26, 26, 27, 27, 29, 29, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, 22, -1, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [0.59135294, 0.52807486, 0.90107125, 0.33186364, 0.47900635, 1.0, 0.99982387, 0.36718732, 0.5139595, -0.005370666, 0.012952551, 1.0, 1.0, 0.02480839, 1.3466678, 0.1411145, -0.019636871, 1.0, -0.032068953, 0.016783936, -0.0073971497, 0.0043365, 0.037631154, 0.05874722, 0.091634184, -0.04427081, 1.0, 1.0, 0.008688821, 0.2382665, -0.0255675, -0.0203179, -0.0027304103, 1.0, -0.042898457, -0.038562115, -0.028633347], "split_indices": [139, 142, 142, 140, 139, 39, 139, 142, 140, 0, 0, 69, 111, 0, 139, 142, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 93, 93, 0, 139, 0, 0, 0, 69, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1288.0, 783.0, 1051.0, 237.0, 466.0, 317.0, 605.0, 446.0, 117.0, 120.0, 188.0, 278.0, 114.0, 203.0, 515.0, 90.0, 322.0, 124.0, 89.0, 99.0, 117.0, 161.0, 114.0, 89.0, 109.0, 406.0, 234.0, 88.0, 302.0, 104.0, 140.0, 94.0, 192.0, 110.0, 91.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0061378875, -0.12581995, 0.4066779, -0.2336499, 0.022653583, 0.010257605, 0.5486505, -0.3120289, -0.1000977, -0.02314933, 0.07329541, 0.069437794, 0.040376056, -0.020494204, -0.3529739, -0.046331123, -0.023769386, 0.016784696, 0.033254553, -0.39001903, -0.024183853, 0.012156428, -0.017442912, 0.027088558, -0.053111244, -0.032961305, -0.42845914, -0.015848594, 0.046965264, -0.046173606, -0.0392594, -0.005333204, 0.015068183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, -1, -1, 19, 21, -1, 23, -1, 25, -1, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [108.56293, 24.735281, 21.975624, 9.368584, 8.365799, 0.0, 7.326706, 2.47295, 2.4487624, 0.0, 7.9405212, 0.0, 0.0, 0.0, 1.6797409, 5.1186814, 0.0, 7.9034767, 0.0, 0.71053696, 0.0, 0.0, 0.0, 0.0, 3.680391, 0.0, 0.22317886, 0.0, 1.8620464, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 14, 14, 15, 15, 17, 17, 19, 19, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, -1, -1, 20, 22, -1, 24, -1, 26, -1, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [0.761162, 0.43565965, 0.8071148, 0.33186364, -1.0, 0.010257605, 1.0, 0.0, 1.0, -0.02314933, 0.8141592, 0.069437794, 0.040376056, -0.020494204, 0.30455607, 1.2628604, -0.023769386, 1.0, 0.033254553, 0.17621006, -0.024183853, 0.012156428, -0.017442912, 0.027088558, 0.5325338, -0.032961305, 0.25546637, -0.015848594, 0.6316315, -0.046173606, -0.0392594, -0.005333204, 0.015068183], "split_indices": [139, 142, 142, 140, 0, 0, 69, 1, 80, 0, 143, 0, 0, 0, 143, 138, 0, 89, 0, 141, 0, 0, 0, 0, 139, 0, 141, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1545.0, 509.0, 895.0, 650.0, 162.0, 347.0, 564.0, 331.0, 108.0, 542.0, 173.0, 174.0, 156.0, 408.0, 238.0, 93.0, 445.0, 97.0, 306.0, 102.0, 103.0, 135.0, 96.0, 349.0, 119.0, 187.0, 170.0, 179.0, 97.0, 90.0, 91.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00074452214, -0.11785608, 0.3701948, -0.19779912, 0.05524638, 0.012366479, 0.4880376, -0.29801473, -0.14534584, 0.0134875085, 0.024362525, 0.39353538, 0.07292151, -0.33195943, -0.018962303, -0.21979661, -0.07440103, 0.01356887, -0.07816337, 0.06008267, 0.027104506, -0.027210979, -0.038574323, -0.30223766, -0.01216025, 0.015581661, -0.019264754, 0.011666682, -0.01972263, -0.024122698, -0.036660053, -0.015040116, 0.014042348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, 19, -1, 21, -1, 23, 25, -1, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [90.87651, 21.72619, 14.64209, 5.6456223, 3.9017782, 0.0, 7.772011, 1.3576698, 3.7237368, 4.5471377, 0.0, 6.2208366, 0.0, 0.90452003, 0.0, 2.7847538, 3.8410907, 0.0, 5.3817163, 0.0, 0.0, 0.0, 0.0, 0.73431396, 0.0, 4.247927, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 18, 18, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, 20, -1, 22, -1, 24, 26, -1, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.761162, 0.48429644, 0.8071148, 0.22425285, 0.8039851, 0.012366479, 1.3466678, 0.2891638, 1.0, 1.0, 0.024362525, 1.0, 0.07292151, 0.17621006, -0.018962303, 0.32621422, 0.1923077, 0.01356887, 0.5030815, 0.06008267, 0.027104506, -0.027210979, -0.038574323, 0.31451914, -0.01216025, 1.0, -0.019264754, 0.011666682, -0.01972263, -0.024122698, -0.036660053, -0.015040116, 0.014042348], "split_indices": [139, 139, 142, 142, 140, 0, 139, 140, 17, 53, 0, 39, 0, 141, 0, 139, 1, 0, 141, 0, 0, 0, 0, 143, 0, 5, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1570.0, 504.0, 1074.0, 496.0, 163.0, 341.0, 369.0, 705.0, 406.0, 90.0, 245.0, 96.0, 281.0, 88.0, 344.0, 361.0, 174.0, 232.0, 91.0, 154.0, 133.0, 148.0, 187.0, 157.0, 205.0, 156.0, 88.0, 144.0, 96.0, 91.0, 88.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022410988, -0.18118316, 0.18743749, -0.27005655, -0.12796637, 0.096680336, 0.44845223, -0.017194148, -0.30533764, -0.066000305, -0.19895662, 0.069873594, 0.029088024, 0.030346248, 0.06374567, -0.020938955, -0.3530656, 0.005002101, -0.13498595, -0.029100997, -0.008338598, 0.10844316, -0.014054054, -0.032167107, -0.03822843, -0.023666933, -0.0010932209, 0.05717581, 0.03314312, -0.00897795, 0.15209208, 0.016557602, -0.010549605, 0.0009806118, 0.030070186], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, -1, -1, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [69.91945, 5.0133286, 23.688953, 1.3742542, 2.9165268, 3.8627524, 7.070156, 0.0, 1.3371868, 2.8333478, 3.2873468, 5.291359, 0.0, 0.0, 0.0, 0.0, 0.17887306, 0.0, 2.8003526, 0.0, 0.0, 6.299035, 0.0, 0.0, 0.0, 0.0, 0.0, 2.8130236, 0.0, 4.447771, 3.8906941, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 16, 16, 18, 18, 21, 21, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, -1, -1, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.48429644, 0.23047675, 0.9782575, 1.1749831, 1.290134, 1.0769231, 1.0, -0.017194148, 0.07692308, 1.0, 0.41258156, 0.46153846, 0.029088024, 0.030346248, 0.06374567, -0.020938955, 1.2231779, 0.005002101, 0.3595268, -0.029100997, -0.008338598, 0.03846154, -0.014054054, -0.032167107, -0.03822843, -0.023666933, -0.0010932209, 0.761162, 0.03314312, 0.55102646, 1.0, 0.016557602, -0.010549605, 0.0009806118, 0.030070186], "split_indices": [139, 139, 141, 138, 138, 1, 0, 0, 1, 69, 139, 1, 0, 0, 0, 0, 138, 0, 141, 0, 0, 1, 0, 0, 0, 0, 0, 139, 0, 141, 121, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1060.0, 1000.0, 397.0, 663.0, 742.0, 258.0, 105.0, 292.0, 354.0, 309.0, 652.0, 90.0, 146.0, 112.0, 97.0, 195.0, 132.0, 222.0, 172.0, 137.0, 551.0, 101.0, 94.0, 101.0, 122.0, 100.0, 448.0, 103.0, 264.0, 184.0, 94.0, 170.0, 94.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0036845608, -0.12310178, 0.2228462, -0.17694148, 0.0014365176, 0.105352774, 0.40460613, -0.23090786, -0.08762319, -0.045513865, 0.013856885, 0.1593494, -0.009994688, 0.015154036, 0.52121484, -0.033285256, -0.2103043, 0.0041731615, -0.15062791, 0.011052157, -0.13302581, 0.06424507, 0.035352074, 0.07067957, 0.033195913, -0.011790868, -0.23847988, -0.0060979156, -0.023007272, -0.022357304, -0.0054133157, 0.02783223, -0.006673639, -0.29632574, -0.017024253, -0.034335367, -0.025340704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, -1, -1, 23, -1, 25, -1, 27, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [57.54626, 8.797096, 16.208893, 4.415287, 2.549613, 5.110411, 8.79388, 1.1993427, 2.811738, 4.028215, 0.0, 6.740283, 0.0, 0.0, 7.1649284, 0.0, 1.2365646, 0.0, 1.6523328, 0.0, 1.3501232, 6.8698378, 0.0, 0.0, 0.0, 0.0, 1.4367981, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.39761734, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 14, 14, 16, 16, 18, 18, 20, 20, 21, 21, 26, 26, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, -1, -1, 24, -1, 26, -1, 28, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [0.5981277, 0.44982666, 0.9476538, 0.33186364, 0.62643254, 0.8105995, 0.90107125, 0.097535886, 0.28601104, 0.44998744, 0.013856885, 0.78377116, -0.009994688, 0.015154036, 1.1951114, -0.033285256, 0.17247613, 0.0041731615, 1.0, 0.011052157, 1.0, 0.5804528, 0.035352074, 0.07067957, 0.033195913, -0.011790868, 1.0, -0.0060979156, -0.023007272, -0.022357304, -0.0054133157, 0.02783223, -0.006673639, 1.0, -0.017024253, -0.034335367, -0.025340704], "split_indices": [139, 142, 140, 140, 140, 140, 142, 143, 142, 143, 0, 142, 0, 0, 143, 0, 141, 0, 97, 0, 69, 141, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1312.0, 759.0, 916.0, 396.0, 461.0, 298.0, 571.0, 345.0, 295.0, 101.0, 365.0, 96.0, 94.0, 204.0, 96.0, 475.0, 113.0, 232.0, 106.0, 189.0, 245.0, 120.0, 103.0, 101.0, 111.0, 364.0, 109.0, 123.0, 88.0, 101.0, 93.0, 152.0, 197.0, 167.0, 94.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002637776, -0.09090934, 0.27449456, -0.16855463, 0.01146228, 0.20527305, 0.05377951, -0.22438872, -0.11297205, -0.02024067, 0.017169071, 0.007233733, 0.28875667, -0.25654, -0.012119837, -0.047943495, -0.20070899, 0.023437887, -0.024583325, 0.0421533, 0.013782287, -0.015283494, -0.2954823, -0.015266478, 0.007173796, -0.030172516, -0.008959117, -0.09743553, 0.14227125, -0.02604757, -0.03244803, 0.001602402, -0.022430396, 0.02672317, -0.00095498125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [51.604454, 12.384035, 9.368191, 2.7496128, 3.4135683, 4.5168686, 0.0, 1.466425, 2.5331998, 5.527847, 0.0, 0.0, 5.010105, 1.3609772, 0.0, 3.1959655, 2.1214662, 6.750984, 0.0, 0.0, 0.0, 0.0, 0.248703, 0.0, 0.0, 0.0, 0.0, 3.3539042, 4.4962754, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 22, 22, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [0.761162, 0.4208793, 1.3466678, 0.28365853, 0.8141592, 0.8071148, 0.05377951, 0.2910317, 0.39922574, 0.6940378, 0.017169071, 0.007233733, 1.0516728, 1.1786883, -0.012119837, 0.30246237, 0.4512219, 0.47262976, -0.024583325, 0.0421533, 0.013782287, -0.015283494, 0.17297588, -0.015266478, 0.007173796, -0.030172516, -0.008959117, 0.5139595, 1.0, -0.02604757, -0.03244803, 0.001602402, -0.022430396, 0.02672317, -0.00095498125], "split_indices": [139, 140, 139, 141, 143, 142, 0, 143, 141, 143, 0, 0, 142, 138, 0, 140, 142, 139, 0, 0, 0, 0, 140, 0, 0, 0, 0, 140, 108, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1558.0, 514.0, 886.0, 672.0, 407.0, 107.0, 442.0, 444.0, 561.0, 111.0, 157.0, 250.0, 337.0, 105.0, 255.0, 189.0, 470.0, 91.0, 133.0, 117.0, 92.0, 245.0, 136.0, 119.0, 99.0, 90.0, 233.0, 237.0, 111.0, 134.0, 123.0, 110.0, 130.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.007554876, -0.081489615, 0.23329717, -0.11615228, 0.08165242, 0.17345804, 0.050257325, -0.20049243, -0.07968085, 0.02681289, -0.0015973498, 0.038499907, 0.28996485, -0.012593714, -0.23269334, -0.056932162, -0.16388871, 0.021549223, -0.013375153, 0.046105687, 0.014135204, -0.028967682, -0.01734486, -0.018044861, -0.019444674, -0.021932662, -0.010783478, -0.069689855, 0.009910766, 0.003858568, -0.12030648, -0.023006618, -0.0005058766], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [41.428383, 8.358, 9.394016, 3.7496471, 4.7150803, 7.500128, 0.0, 0.8834753, 1.6301899, 0.0, 0.0, 6.7376676, 6.5091743, 0.0, 0.8676233, 3.1023152, 0.562459, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0617354, 0.0, 0.0, 1.9784759, 0.0, 0.0, 3.11179, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 24, 24, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [0.6960004, 0.5981277, 1.3494815, 0.22425285, 0.5804528, 0.8316313, 0.050257325, 1.0, 0.5139595, 0.02681289, -0.0015973498, 0.65399134, 1.0, -0.012593714, 1.0, 0.23047675, 0.5076032, 0.021549223, -0.013375153, 0.046105687, 0.014135204, -0.028967682, -0.01734486, -0.018044861, 0.4208793, -0.021932662, -0.010783478, 0.3188401, 0.009910766, 0.003858568, 0.44982666, -0.023006618, -0.0005058766], "split_indices": [140, 139, 140, 142, 141, 139, 0, 53, 140, 0, 0, 139, 69, 0, 59, 139, 142, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 142, 0, 0, 142, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1478.0, 583.0, 1219.0, 259.0, 477.0, 106.0, 368.0, 851.0, 89.0, 170.0, 221.0, 256.0, 111.0, 257.0, 670.0, 181.0, 109.0, 112.0, 119.0, 137.0, 131.0, 126.0, 156.0, 514.0, 91.0, 90.0, 361.0, 153.0, 115.0, 246.0, 126.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.000299061, -0.07309986, 0.22009009, -0.116884954, 0.020763319, 0.0046400614, 0.30318072, -0.1851193, -0.08391457, 0.013473823, -0.013040252, 0.044928923, 0.015957693, -0.0058348044, -0.2413442, -0.061945517, -0.023623329, -0.015915466, 0.04779548, -0.018035602, -0.02916245, -0.08913122, 0.00894359, 0.013569503, -0.0036896036, -0.12937307, 0.0029492092, -0.00509419, -0.18986705, -0.030020645, -0.011794207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, -1, -1, 19, 21, -1, -1, 23, -1, -1, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1], "loss_changes": [33.147953, 6.382531, 7.403599, 2.3824463, 1.9032631, 0.0, 7.2806606, 2.459056, 2.389255, 0.0, 3.3867006, 0.0, 0.0, 0.0, 0.732893, 2.5680163, 0.0, 0.0, 2.0025294, 0.0, 0.0, 2.5252461, 0.0, 0.0, 0.0, 1.8741212, 0.0, 0.0, 1.769762, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 14, 14, 15, 15, 18, 18, 21, 21, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, -1, -1, 20, 22, -1, -1, 24, -1, -1, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1], "split_conditions": [0.761162, 0.48429644, 0.8071148, 0.22819103, 1.3493615, 0.0046400614, 1.0, 0.07692308, 0.52989423, 0.013473823, 0.5377607, 0.044928923, 0.015957693, -0.0058348044, 0.17621006, 0.4557361, -0.023623329, -0.015915466, 1.0, -0.018035602, -0.02916245, 1.0, 0.00894359, 0.013569503, -0.0036896036, 0.03846154, 0.0029492092, -0.00509419, 0.26691094, -0.030020645, -0.011794207], "split_indices": [139, 139, 142, 140, 138, 0, 69, 1, 141, 0, 143, 0, 0, 0, 141, 141, 0, 0, 15, 0, 0, 74, 0, 0, 0, 1, 0, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1553.0, 513.0, 1059.0, 494.0, 166.0, 347.0, 345.0, 714.0, 113.0, 381.0, 172.0, 175.0, 106.0, 239.0, 624.0, 90.0, 112.0, 269.0, 108.0, 131.0, 529.0, 95.0, 132.0, 137.0, 395.0, 134.0, 172.0, 223.0, 88.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.009128231, -0.09372545, 0.13643694, -0.11809316, 0.010230354, 0.051243093, 0.27193293, -0.023253303, -0.104408816, 0.011621826, -0.005901506, 0.023004232, -0.004003849, 0.009699266, 0.051713612, -0.06658024, -0.15313715, -0.05252381, 0.0129298335, -0.13371983, 5.6407283e-05, -0.24029675, -0.074412316, 0.0057657114, -0.019459924, -0.004541877, -0.020038417, 0.011975882, -0.0069356808, -0.016642464, -0.030174175, -0.015419218, -9.865739e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, -1, 19, 21, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [25.429253, 3.3083153, 8.761459, 1.6568623, 1.820116, 4.6032, 12.5685005, 0.0, 1.7419405, 0.0, 0.0, 0.0, 2.3025424, 0.0, 0.0, 2.380146, 2.83385, 4.085695, 0.0, 1.5599298, 2.218484, 0.88965607, 1.2711626, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, -1, 20, 22, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.5981277, 0.52807486, 0.9476538, 0.10044436, 0.602346, 0.0, 1.0, -0.023253303, 0.3215633, 0.011621826, -0.005901506, 0.023004232, 1.0, 0.009699266, 0.051713612, 0.23527001, 1.0, 1.0, 0.0129298335, 0.16922487, 1.2484895, 0.34024993, 1.0, 0.0057657114, -0.019459924, -0.004541877, -0.020038417, 0.011975882, -0.0069356808, -0.016642464, -0.030174175, -0.015419218, -9.865739e-05], "split_indices": [139, 142, 140, 143, 142, 0, 0, 0, 143, 0, 0, 0, 42, 0, 0, 142, 13, 0, 0, 143, 138, 140, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1306.0, 759.0, 1058.0, 248.0, 466.0, 293.0, 113.0, 945.0, 98.0, 150.0, 110.0, 356.0, 171.0, 122.0, 532.0, 413.0, 261.0, 95.0, 265.0, 267.0, 196.0, 217.0, 147.0, 114.0, 114.0, 151.0, 98.0, 169.0, 89.0, 107.0, 104.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0005868995, -0.08878229, 0.114088714, -0.15023501, -0.059545208, 0.077670924, 0.037927642, -0.00863299, -0.17836313, 0.0071586766, -0.083946474, 0.049372297, 0.026525036, -0.011974361, -0.022095995, -0.0077135446, -0.15505262, 0.01803602, 0.026587743, 0.008046485, -0.07845555, -0.0063283863, -0.23572843, -0.055075027, 0.12344513, -0.015566292, -0.00021156366, -0.018358734, -0.029142458, 0.0182149, -0.026470004, 0.031990897, -0.0029515978, -0.011021546, 0.009937029], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, -1, -1, -1, 19, 21, 23, -1, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [20.976892, 2.0787802, 8.798029, 0.6704769, 2.5086322, 4.251899, 0.0, 0.0, 0.64672375, 0.0, 3.583036, 4.7219863, 0.0, 0.0, 0.0, 1.9898955, 2.5320024, 4.685595, 0.0, 0.0, 1.0432384, 0.0, 0.52853775, 5.515462, 7.4827824, 0.0, 0.0, 0.0, 0.0, 2.7724693, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 20, 20, 22, 22, 23, 23, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, -1, -1, -1, 20, 22, 24, -1, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.5325338, 0.22425285, 1.3494815, 1.0, 1.2186605, 1.1144832, 0.037927642, -0.00863299, 0.13452224, 0.0071586766, 1.0, 0.7692308, 0.026525036, -0.011974361, -0.022095995, 1.290134, 0.115384616, 0.76931804, 0.026587743, 0.008046485, 0.44982666, -0.0063283863, 0.37522858, 0.8163307, 0.95287216, -0.015566292, -0.00021156366, -0.018358734, -0.029142458, 0.5377607, -0.026470004, 0.031990897, -0.0029515978, -0.011021546, 0.009937029], "split_indices": [139, 142, 140, 53, 138, 142, 0, 0, 143, 0, 97, 1, 0, 0, 0, 138, 1, 143, 0, 0, 142, 0, 143, 140, 143, 0, 0, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1157.0, 911.0, 373.0, 784.0, 801.0, 110.0, 114.0, 259.0, 123.0, 661.0, 696.0, 105.0, 109.0, 150.0, 319.0, 342.0, 608.0, 88.0, 142.0, 177.0, 160.0, 182.0, 359.0, 249.0, 88.0, 89.0, 94.0, 88.0, 266.0, 93.0, 109.0, 140.0, 103.0, 163.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005092176, -0.091030374, 0.09184327, -0.12916441, -0.047277365, 0.047721174, 0.2763538, -0.106802195, -0.02112784, -0.12728474, 0.031792406, 0.0038856654, 0.02008935, 0.04504952, 0.011820497, -0.15258624, 0.0027028036, -0.004744099, -0.022942714, -0.004302043, 0.01674421, 0.0185611, -0.05047661, -0.005895443, -0.20652159, -0.14515103, 0.10517456, -0.024869015, -0.01631707, -0.0671123, -0.031129802, 0.023800077, -0.002916101, -0.019657822, 0.009144708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, -1, -1, -1, -1, -1, 25, -1, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [17.177402, 1.8236475, 7.8886194, 1.0723686, 3.220018, 5.2506504, 5.1500263, 2.8124256, 0.0, 2.063324, 2.597975, 6.006435, 0.0, 0.0, 0.0, 1.7271214, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.896536, 0.0, 0.39668465, 3.773076, 3.1582618, 0.0, 0.0, 4.0645514, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 22, 22, 24, 24, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, 26, -1, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.4967865, 1.0, 1.1314709, 0.41837215, 1.0, 0.9271665, 1.1106075, 0.34024993, -0.02112784, 1.0, 0.4140956, 1.0, 0.02008935, 0.04504952, 0.011820497, 0.17621006, 0.0027028036, -0.004744099, -0.022942714, -0.004302043, 0.01674421, 0.0185611, 1.0, -0.005895443, 1.0, 0.7650251, -0.1923077, -0.024869015, -0.01631707, 1.0, -0.031129802, 0.023800077, -0.002916101, -0.019657822, 0.009144708], "split_indices": [139, 17, 139, 142, 115, 140, 143, 140, 0, 111, 142, 17, 0, 0, 0, 141, 0, 0, 0, 0, 0, 0, 97, 0, 13, 141, 1, 0, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1093.0, 969.0, 584.0, 509.0, 782.0, 187.0, 459.0, 125.0, 253.0, 256.0, 608.0, 174.0, 89.0, 98.0, 342.0, 117.0, 142.0, 111.0, 165.0, 91.0, 140.0, 468.0, 125.0, 217.0, 291.0, 177.0, 110.0, 107.0, 198.0, 93.0, 89.0, 88.0, 109.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005364683, -0.052839097, 0.15312816, -0.09292996, 0.01045119, 0.10738573, 0.035810597, -0.14128503, -0.057955578, 0.09626727, -0.06156808, 0.008013093, 0.19311897, -0.17492184, -0.0044836584, -0.0035008732, -0.14002845, 0.023652578, -0.005633396, -0.015675267, 0.0040596686, 0.020039372, -0.01741526, 0.034840837, 0.0026466933, -0.010763477, -0.025087466, -0.07018371, 0.014198892, -0.007484021, -0.021977996, 0.0016111011, -0.018431546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [17.682407, 3.7426095, 5.4475603, 1.5271425, 3.5351958, 4.0467806, 0.0, 1.229558, 2.3418884, 5.5863457, 3.024324, 7.7099338, 6.5992184, 1.4360895, 0.0, 3.0560262, 1.0865612, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1273773, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.6960004, 0.4244175, 1.3494815, 0.2622024, 0.50989574, 0.8316313, 0.035810597, 0.26206392, 0.39922574, 1.0, 0.647933, 0.65399134, 1.0052764, 0.17621006, -0.0044836584, 0.33186364, 1.0, 0.023652578, -0.005633396, -0.015675267, 0.0040596686, 0.020039372, -0.01741526, 0.034840837, 0.0026466933, -0.010763477, -0.025087466, 0.29735926, 0.014198892, -0.007484021, -0.021977996, 0.0016111011, -0.018431546], "split_indices": [140, 140, 140, 141, 141, 139, 0, 143, 141, 97, 139, 139, 143, 141, 0, 140, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1475.0, 581.0, 903.0, 572.0, 475.0, 106.0, 379.0, 524.0, 261.0, 311.0, 220.0, 255.0, 281.0, 98.0, 315.0, 209.0, 136.0, 125.0, 161.0, 150.0, 107.0, 113.0, 132.0, 123.0, 149.0, 132.0, 216.0, 99.0, 115.0, 94.0, 123.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0038091508, -0.059329208, 0.1101085, -0.08995326, 0.0078168595, 0.052291352, 0.19236569, -0.11478992, -0.025498603, -0.03886917, 0.017357852, -0.07469113, 0.14060624, -0.00014355672, 0.2967926, -0.048631977, -0.15709038, -0.010642644, 0.0062973336, 0.0033879422, -0.089714974, 0.0015938331, -0.016055066, 0.0004457759, 0.023810612, 0.008859362, 0.0471531, -0.013882915, 0.00444984, -0.09971307, -0.20454732, -0.016338063, -0.002065339, -0.014957144, -0.0049288096, -0.024272986, -0.017629841], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, 23, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [13.832538, 2.6587768, 3.6525126, 1.4215441, 3.1341953, 5.0577145, 6.415468, 1.7938461, 1.7684814, 1.1688718, 0.0, 1.4395598, 3.5310059, 0.0, 7.4943523, 2.1000242, 1.0646725, 0.0, 0.0, 0.0, 0.9462681, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.44499624, 0.23082256, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 20, 20, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, 24, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [0.59135294, 0.43565965, 0.90107125, 1.0, 1.0, 1.0, 0.99982387, 1.0, 0.30109692, -0.23076923, 0.017357852, 1.0, 1.0, -0.00014355672, -0.42307693, 0.30246237, 0.20523074, -0.010642644, 0.0062973336, 0.0033879422, 1.0, 0.0015938331, -0.016055066, 0.0004457759, 0.023810612, 0.008859362, 0.0471531, -0.013882915, 0.00444984, 0.19729245, 1.0, -0.016338063, -0.002065339, -0.014957144, -0.0049288096, -0.024272986, -0.017629841], "split_indices": [139, 142, 142, 93, 74, 39, 139, 122, 139, 1, 0, 108, 111, 0, 1, 140, 139, 0, 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 140, 17, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1293.0, 768.0, 888.0, 405.0, 451.0, 317.0, 641.0, 247.0, 316.0, 89.0, 185.0, 266.0, 111.0, 206.0, 250.0, 391.0, 129.0, 118.0, 130.0, 186.0, 90.0, 95.0, 111.0, 155.0, 94.0, 112.0, 127.0, 123.0, 177.0, 214.0, 90.0, 96.0, 89.0, 88.0, 91.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.005324065, -0.041355353, 0.1274087, -0.0769629, 0.014880021, 0.061857853, 0.2438728, -0.056483492, -0.14667167, 0.09097147, -0.049579605, 0.021139788, -0.009294245, 0.004943862, 0.043456785, -0.11379621, 0.0018089204, -0.02252736, -0.008433216, 0.020441921, -0.005799031, -0.12126206, 0.006788321, 0.013105164, -0.014739463, -0.0717661, -0.018630615, -0.042216465, 0.013338483, 0.0024137397, -0.025658434, -0.012568804, -0.0013886555, -0.009519088, 0.0063130343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.785201, 2.9955966, 4.36683, 1.3076758, 2.8447995, 3.8942723, 7.637991, 2.3653555, 1.0192003, 4.495235, 2.6438873, 0.0, 4.8066916, 0.0, 0.0, 1.0879922, 2.033231, 0.0, 0.0, 0.0, 0.0, 3.8367796, 0.0, 0.0, 0.0, 0.7053406, 0.0, 1.4677203, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 16, 16, 21, 21, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6960004, 0.4244175, 1.0, 1.3148105, 0.50989574, 0.8509382, -0.23076923, 0.2622024, 1.0, 0.5139595, 0.6967313, 0.021139788, 1.0, 0.004943862, 0.043456785, 0.2038502, 1.0, -0.02252736, -0.008433216, 0.020441921, -0.005799031, 1.0, 0.006788321, 0.013105164, -0.014739463, 0.15482669, -0.018630615, 1.273555, 0.013338483, 0.0024137397, -0.025658434, -0.012568804, -0.0013886555, -0.009519088, 0.0063130343], "split_indices": [140, 140, 0, 138, 141, 140, 1, 141, 69, 140, 142, 0, 105, 0, 0, 141, 62, 0, 0, 0, 0, 124, 0, 0, 0, 143, 0, 138, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1496.0, 572.0, 916.0, 580.0, 366.0, 206.0, 708.0, 208.0, 266.0, 314.0, 118.0, 248.0, 102.0, 104.0, 357.0, 351.0, 92.0, 116.0, 151.0, 115.0, 195.0, 119.0, 123.0, 125.0, 226.0, 131.0, 263.0, 88.0, 94.0, 101.0, 117.0, 109.0, 175.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004887156, -0.053725433, 0.080422185, -0.040471934, -0.021593016, 0.026265606, 0.19625708, -0.059080757, 0.010254585, 0.07913969, -0.01257058, 0.0073365406, 0.03811289, -0.039138667, -0.015431933, 0.12543973, -0.0074174553, -0.09794385, 0.017326007, 0.046008237, 0.032762896, -0.051437937, -0.017439192, 0.066601045, -0.008815334, -0.01146241, 0.016263174, -0.013630259, 0.0033426695, -0.006950892, 0.13653602, 0.003983756, 0.023872865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [9.160092, 2.5044932, 5.670993, 2.86632, 0.0, 4.949775, 6.543129, 1.8099909, 0.0, 3.2439942, 0.0, 0.0, 0.0, 2.6164877, 0.0, 5.637127, 0.0, 1.3723404, 2.0893946, 4.720844, 0.0, 1.7284825, 0.0, 2.6081643, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7886181, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [0.55767125, 0.6348765, 0.9476538, 0.52329415, -0.021593016, 0.46153846, 1.0, 1.3148105, 0.010254585, 0.79743034, -0.01257058, 0.0073365406, 0.03811289, 0.23047675, -0.015431933, 0.761162, -0.0074174553, 0.21846965, 1.0, 1.0, 0.032762896, 0.12942436, -0.017439192, -0.1923077, -0.008815334, -0.01146241, 0.016263174, -0.013630259, 0.0033426695, -0.006950892, 0.30109692, 0.003983756, 0.023872865], "split_indices": [143, 141, 140, 139, 0, 1, 0, 138, 0, 140, 0, 0, 0, 139, 0, 139, 0, 143, 50, 69, 0, 143, 0, 1, 0, 0, 0, 0, 0, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1165.0, 904.0, 1077.0, 88.0, 616.0, 288.0, 953.0, 124.0, 457.0, 159.0, 173.0, 115.0, 788.0, 165.0, 351.0, 106.0, 386.0, 402.0, 252.0, 99.0, 240.0, 146.0, 274.0, 128.0, 106.0, 146.0, 120.0, 120.0, 93.0, 181.0, 93.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0027006762, -0.051563542, 0.07145119, -0.037083562, -0.018814493, 0.023619073, 0.17385463, -0.054782733, 0.01152267, 0.12938124, -0.030158296, 0.006067029, 0.034460686, -0.070934705, 0.009374513, -0.00030717623, 0.029601572, 0.03535864, -0.018462915, -0.10484407, -0.0012797116, -0.0049028536, 0.014993312, -0.083084896, -0.020689456, 0.009857089, -0.015517793, -0.117439434, -0.0003972672, -0.07683776, -0.019031418, 0.00054512284, -0.013736441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, 25, -1, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [7.7299953, 2.2901704, 4.4769306, 2.8224664, 0.0, 3.5433822, 5.6240034, 2.2502785, 0.0, 4.634961, 4.1797495, 0.0, 0.0, 1.9982147, 0.0, 0.0, 0.0, 2.8038993, 0.0, 1.263483, 4.2566123, 0.0, 0.0, 1.2746778, 0.0, 0.0, 0.0, 0.96753883, 0.0, 1.0459424, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 17, 17, 19, 19, 20, 20, 23, 23, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, 26, -1, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [0.55767125, 0.59144264, 0.9476538, 0.48379704, -0.018814493, 0.67325324, 1.0, 3.0384614, 0.01152267, 1.0, 0.15384616, 0.006067029, 0.034460686, 1.0, 0.009374513, -0.00030717623, 0.029601572, 0.761162, -0.018462915, 1.0, 1.2634896, -0.0049028536, 0.014993312, 1.0, -0.020689456, 0.009857089, -0.015517793, 1.0, -0.0003972672, 0.23500222, -0.019031418, 0.00054512284, -0.013736441], "split_indices": [143, 141, 140, 141, 0, 143, 0, 1, 0, 93, 1, 0, 0, 83, 0, 0, 0, 139, 0, 0, 138, 0, 0, 71, 0, 0, 0, 116, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1158.0, 914.0, 1047.0, 111.0, 623.0, 291.0, 938.0, 109.0, 210.0, 413.0, 175.0, 116.0, 846.0, 92.0, 117.0, 93.0, 290.0, 123.0, 569.0, 277.0, 167.0, 123.0, 469.0, 100.0, 168.0, 109.0, 327.0, 142.0, 210.0, 117.0, 89.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0029926363, -0.04767655, 0.06669104, -0.08252953, 0.00452682, 0.030971186, 0.19030975, -0.05739698, -0.02511153, -0.05941864, 0.021687403, -0.003276653, 0.01965496, 0.0379768, 0.0046582767, -0.09411525, 0.014677271, -0.11509662, 0.0029011068, -0.07146023, 0.09404761, -0.054962453, -0.02017129, -0.005732925, -0.017847246, 0.0080259945, -0.1476516, 0.024764838, -0.009196429, -0.09530831, 0.0060611586, -0.00051699317, -0.024508389, -0.0052726897, -0.014493072], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [6.62938, 2.0814435, 4.0182347, 2.9065742, 6.219018, 4.003517, 5.554972, 4.475565, 0.0, 1.7331032, 0.0, 3.8820112, 0.0, 0.0, 0.0, 2.1316504, 0.0, 0.790787, 0.0, 3.9765604, 6.885749, 1.7299483, 0.0, 0.0, 0.0, 0.0, 3.1790495, 0.0, 0.0, 0.58107257, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.55767125, 1.0, 1.0, 0.5458753, 0.43214852, 1.1314709, 1.4891474, 0.4534852, -0.02511153, 0.32742807, 0.021687403, 1.0, 0.01965496, 0.0379768, 0.0046582767, 0.35825115, 0.014677271, 1.0, 0.0029011068, 0.54411215, 1.0, 0.2785644, -0.02017129, -0.005732925, -0.017847246, 0.0080259945, 1.0, 0.024764838, -0.009196429, 0.16922487, 0.0060611586, -0.00051699317, -0.024508389, -0.0052726897, -0.014493072], "split_indices": [143, 115, 42, 141, 142, 139, 138, 141, 0, 140, 0, 12, 0, 0, 0, 141, 0, 74, 0, 139, 105, 142, 0, 0, 0, 0, 93, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1144.0, 910.0, 686.0, 458.0, 706.0, 204.0, 597.0, 89.0, 352.0, 106.0, 585.0, 121.0, 88.0, 116.0, 506.0, 91.0, 216.0, 136.0, 344.0, 241.0, 371.0, 135.0, 113.0, 103.0, 115.0, 229.0, 132.0, 109.0, 275.0, 96.0, 93.0, 136.0, 148.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0024412726, -0.029567083, 0.08399682, -0.012371142, -0.13780035, 0.16935773, 0.015020959, -0.00046758784, -0.014885749, -0.01875421, -0.009270733, 0.005556476, 0.033842154, -0.085325904, 0.0169887, -0.02916593, 0.07140599, 0.009413389, -0.029239494, -0.0004168979, -0.11487057, 0.021701798, 0.015078711, -0.057134513, 0.08020468, -0.0015460727, -0.025494805, 0.010311592, -0.006940859, -0.013691845, 0.012575683, -0.0018200744, 0.01652551, -0.009467062, 0.01466336], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, 17, -1, 19, 21, -1, -1, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [5.408871, 2.7694247, 3.4384995, 2.0860791, 0.45757246, 5.0211883, 5.019525, 2.4359927, 0.0, 0.0, 0.0, 0.0, 0.0, 7.283471, 0.0, 2.079553, 2.7640488, 0.0, 0.0, 2.8899233, 2.9521165, 0.0, 1.8074406, 2.063411, 2.18442, 0.0, 0.0, 0.0, 0.0, 0.0, 2.846689, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 13, 13, 15, 15, 16, 16, 19, 19, 20, 20, 22, 22, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, 18, -1, 20, 22, -1, -1, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [0.6960004, 1.0, 1.0, 1.0, 0.2785644, 1.0219027, 0.9640368, 1.0, -0.014885749, -0.01875421, -0.009270733, 0.005556476, 0.033842154, 1.0, 0.0169887, 0.52989423, -0.07692308, 0.009413389, -0.029239494, 0.36718732, 1.0, 0.021701798, 1.0, 0.15384616, 1.0, -0.0015460727, -0.025494805, 0.010311592, -0.006940859, -0.013691845, 0.25063372, -0.0018200744, 0.01652551, -0.009467062, 0.01466336], "split_indices": [140, 40, 69, 43, 142, 141, 142, 50, 0, 0, 0, 0, 0, 108, 0, 141, 1, 0, 0, 142, 124, 0, 17, 1, 15, 0, 0, 0, 0, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1488.0, 584.0, 1284.0, 204.0, 261.0, 323.0, 1181.0, 103.0, 97.0, 107.0, 156.0, 105.0, 196.0, 127.0, 844.0, 337.0, 105.0, 91.0, 632.0, 212.0, 94.0, 243.0, 371.0, 261.0, 124.0, 88.0, 119.0, 124.0, 173.0, 198.0, 121.0, 140.0, 110.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047519584, -0.027923021, 0.07708194, -0.061174687, 0.012748432, 0.025033418, -0.0058996943, -0.09221122, 0.015868928, -0.107034944, 0.061171506, -0.08017392, 0.012658943, -0.055483133, -0.019638542, -0.0056033167, 0.015231954, -0.0030683537, -0.018712904, 0.0008258006, 0.12463027, 0.0021606516, -0.016327908, 0.028052758, -0.108166054, 0.011028312, -0.011457104, 0.028512765, 0.00042572268, -0.007476845, 0.013201646, -0.016703276, -0.0042806673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.9269855, 2.182763, 6.5701776, 2.1233563, 4.211003, 0.0, 3.0407228, 2.4219322, 2.501827, 1.2780967, 1.9798328, 1.6747793, 0.0, 2.0596285, 0.0, 0.0, 0.0, 0.0, 0.0, 3.3472228, 4.8685274, 0.0, 0.0, 1.9348313, 1.1042299, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 19, 19, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7922416, 1.0, 0.95287216, 1.0, -0.1923077, 0.025033418, 1.0, 1.319978, 0.5233413, 1.0, 1.0, 1.1106075, 0.012658943, 1.0, -0.019638542, -0.0056033167, 0.015231954, -0.0030683537, -0.018712904, 1.2634896, 0.61088866, 0.0021606516, -0.016327908, 1.0, 1.0, 0.011028312, -0.011457104, 0.028512765, 0.00042572268, -0.007476845, 0.013201646, -0.016703276, -0.0042806673], "split_indices": [143, 39, 143, 42, 1, 0, 125, 138, 143, 126, 2, 143, 0, 23, 0, 0, 0, 0, 0, 138, 143, 0, 0, 13, 116, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1614.0, 457.0, 888.0, 726.0, 148.0, 309.0, 633.0, 255.0, 209.0, 517.0, 198.0, 111.0, 468.0, 165.0, 167.0, 88.0, 107.0, 102.0, 265.0, 252.0, 89.0, 109.0, 181.0, 287.0, 136.0, 129.0, 108.0, 144.0, 91.0, 90.0, 151.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006964672, -0.011689221, 0.020460626, -0.052675277, 0.021882845, -0.025619727, -0.10446477, -0.07932055, 0.04887835, -0.06752339, 0.0076287985, -0.0610061, -0.017073922, 0.002198918, -0.015223289, 0.120396495, -0.010489398, -0.036932826, -0.016927725, -0.014026522, 0.0014037065, 0.024905644, 0.23082444, -0.019069513, 0.05869673, -0.09072894, 0.009815516, 0.01457716, -0.010442089, 0.015734097, 0.0306813, 0.017859232, -0.007008, -0.0035316662, -0.016181333], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, 25, -1, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.662635, 2.6983097, 0.0, 1.2372534, 2.9451358, 2.476778, 0.87269974, 1.6767877, 3.6132364, 1.2793223, 0.0, 1.0884578, 0.0, 0.0, 0.0, 4.0703154, 5.797498, 2.2964377, 0.0, 0.0, 0.0, 3.2356534, 0.99951744, 0.0, 5.1877604, 0.89020205, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, 26, -1, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3494815, 0.40912068, 0.020460626, 0.3222687, 1.0, 0.2560708, 0.5769231, -0.26923078, 0.6555352, 0.20518488, 0.0076287985, 1.0, -0.017073922, 0.002198918, -0.015223289, 1.0, -0.42307693, 0.2585062, -0.016927725, -0.014026522, 0.0014037065, 0.44998744, 0.58099, -0.019069513, 1.0, 0.17621006, 0.009815516, 0.01457716, -0.010442089, 0.015734097, 0.0306813, 0.017859232, -0.007008, -0.0035316662, -0.016181333], "split_indices": [140, 140, 0, 143, 5, 143, 1, 1, 141, 143, 0, 69, 0, 0, 0, 71, 1, 141, 0, 0, 0, 143, 142, 0, 126, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1961.0, 105.0, 883.0, 1078.0, 580.0, 303.0, 227.0, 851.0, 411.0, 169.0, 183.0, 120.0, 95.0, 132.0, 386.0, 465.0, 316.0, 95.0, 89.0, 94.0, 207.0, 179.0, 129.0, 336.0, 226.0, 90.0, 107.0, 100.0, 91.0, 88.0, 174.0, 162.0, 127.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.000927427, -0.010654888, 0.019254094, -0.029453658, 0.0426999, -0.015030509, -0.12217388, -0.014892456, 0.09083971, -0.00010703372, -0.095556654, -0.021571027, -0.00097199896, 0.17220716, 0.013346885, -0.08804766, 0.022974767, 0.0046184296, -0.021236172, 0.030153904, 0.001090558, 0.012847358, -0.008547865, -0.002778117, -0.017743163, 0.001027734, 0.020189075, 0.011265159, -0.029968321, -0.06906137, 0.01021895, -0.016876912, -0.031897563, 0.010215439, -0.008293828], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, 27, -1, -1, 29, 31, -1, -1, 33, -1, -1], "loss_changes": [3.8918843, 1.9749156, 0.0, 1.9471341, 4.7323046, 1.5141795, 2.0616314, 0.0, 2.5852103, 2.1577075, 3.2615452, 0.0, 0.0, 4.172286, 2.3892663, 1.1904951, 3.3062596, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5949242, 0.0, 0.0, 3.0327072, 1.6785998, 0.0, 0.0, 2.2578955, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 25, 25, 28, 28, 29, 29, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, 28, -1, -1, 30, 32, -1, -1, 34, -1, -1], "split_conditions": [1.2604058, 1.4242606, 0.019254094, 2.0, -0.5, 1.3461539, 4.0, -0.014892456, 1.0, -1.0, 0.30186823, -0.021571027, -0.00097199896, 1.5248721, 0.7908306, 0.428107, 0.69867027, 0.0046184296, -0.021236172, 0.030153904, 0.001090558, 0.012847358, -0.008547865, -0.002778117, -0.017743163, 1.2146873, 0.020189075, 0.011265159, 0.5405575, 0.22376525, 0.01021895, -0.016876912, 1.268324, 0.010215439, -0.008293828], "split_indices": [141, 138, 0, 0, 1, 1, 0, 0, 121, 0, 143, 0, 0, 138, 140, 142, 141, 0, 0, 0, 0, 0, 0, 0, 0, 138, 0, 0, 142, 143, 0, 0, 138, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1969.0, 99.0, 1456.0, 513.0, 1260.0, 196.0, 103.0, 410.0, 1063.0, 197.0, 107.0, 89.0, 200.0, 210.0, 221.0, 842.0, 89.0, 108.0, 111.0, 89.0, 97.0, 113.0, 132.0, 89.0, 750.0, 92.0, 163.0, 587.0, 453.0, 134.0, 123.0, 330.0, 91.0, 239.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005400212, -0.014391803, 0.015505879, -0.00051895546, -0.1248678, -0.10107581, 0.014299948, -0.007015699, -0.01936793, 0.004112268, -0.024327433, -0.027257722, 0.074298, -0.0042573484, -0.01401411, 0.036310386, 0.019814802, -0.022150561, 0.012294002, -0.0043602665, 0.018543614, 0.0071892682, -0.019913603, 0.015677016, -0.06642533, -0.024772443, 0.014931031, -0.019003417, 0.007484189, 0.00286022, -0.04708074, -0.0008994965, -0.014568055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [2.9908862, 3.008526, 0.0, 2.5988085, 0.8244753, 4.529373, 3.7899365, 0.0, 0.0, 0.0, 0.0, 2.3315306, 2.9263644, 1.697873, 0.0, 2.8869605, 0.0, 3.3960414, 0.0, 3.7402132, 0.0, 2.5483046, 0.0, 0.0, 4.714707, 0.54533947, 0.0, 0.0, 0.0, 0.0, 1.2129452, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 24, 24, 25, 25, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.3466678, 1.0, 0.015505879, -0.46153846, 1.0, 0.6348765, 0.5325338, -0.007015699, -0.01936793, 0.004112268, -0.024327433, 0.5139595, 1.0, 0.457961, -0.01401411, 0.5769231, 0.019814802, 0.39922574, 0.012294002, -0.30769232, 0.018543614, 0.36329922, -0.019913603, 0.015677016, 1.0, 0.16432898, 0.014931031, -0.019003417, 0.007484189, 0.00286022, 0.29444343, -0.0008994965, -0.014568055], "split_indices": [139, 40, 0, 1, 115, 141, 139, 0, 0, 0, 0, 140, 42, 141, 0, 1, 0, 141, 0, 1, 0, 140, 0, 0, 12, 143, 0, 0, 0, 0, 140, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1963.0, 110.0, 1744.0, 219.0, 224.0, 1520.0, 122.0, 97.0, 112.0, 112.0, 898.0, 622.0, 746.0, 152.0, 476.0, 146.0, 654.0, 92.0, 374.0, 102.0, 561.0, 93.0, 104.0, 270.0, 458.0, 103.0, 144.0, 126.0, 135.0, 323.0, 233.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002228831, -0.041036256, 0.028695306, -0.025790501, -0.015481474, 0.04300942, -0.0131653175, -0.06724593, 0.018209497, 0.01668203, 0.027681705, -0.030738523, -0.11490836, -0.042938426, 0.07720651, 0.06615463, -0.022855563, -0.007117267, 0.0034405347, -0.013778925, -0.009253032, -0.014046212, 0.0052574435, 0.02166163, -0.0035588674, 0.022206526, 0.00263548, -0.0189289, 0.067984045, -0.018788356, 0.069317155, -0.01531101, 0.026971234, -0.006712781, 0.021586917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [2.4733775, 1.5854592, 2.632647, 1.4701755, 0.0, 1.9983171, 0.0, 0.72211254, 1.41055, 0.0, 1.8218248, 0.61899877, 0.092164755, 1.7884355, 3.129227, 5.2685604, 6.1230936, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.8021603, 0.0, 11.685449, 0.0, 5.5989604, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 26, 26, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [0.4244175, 0.47377545, 1.0, 0.2711477, -0.015481474, 0.48259154, -0.0131653175, 0.2038502, 1.0, 0.01668203, 1.0, 0.19248241, 0.26923078, 1.0, 1.0, 0.6846022, 0.5964612, -0.007117267, 0.0034405347, -0.013778925, -0.009253032, -0.014046212, 0.0052574435, 0.02166163, -0.0035588674, 0.022206526, 0.7869461, -0.0189289, -0.115384616, -0.018788356, 1.0, -0.01531101, 0.026971234, -0.006712781, 0.021586917], "split_indices": [140, 141, 80, 141, 0, 140, 0, 141, 17, 0, 59, 143, 1, 111, 111, 139, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 141, 0, 1, 0, 113, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 914.0, 1147.0, 806.0, 108.0, 1053.0, 94.0, 415.0, 391.0, 116.0, 937.0, 235.0, 180.0, 192.0, 199.0, 532.0, 405.0, 145.0, 90.0, 89.0, 91.0, 95.0, 97.0, 89.0, 110.0, 154.0, 378.0, 143.0, 262.0, 98.0, 280.0, 125.0, 137.0, 145.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00019716562, 0.011694285, -0.0987949, 0.0045237807, 0.014120686, -0.005343968, -0.016458487, 0.015092229, -0.018400365, -0.0029584854, 0.10878083, 0.008279841, -0.01673664, 0.026297752, 0.0018452583, 0.0194573, -0.012047584, 0.004386141, 0.020725405, 0.019892154, -0.01538065, 0.0002920083, 0.01924876, 0.008183881, -0.0025821954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [2.4293582, 1.7171113, 0.66541314, 3.490759, 0.0, 0.0, 0.0, 2.8056118, 0.0, 2.5701094, 3.7327878, 1.8737878, 0.0, 0.0, 0.0, 3.3907166, 0.0, 2.720307, 0.0, 3.416724, 0.0, 1.9314656, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.2604058, 1.2742993, 1.0723042, 0.014120686, -0.005343968, -0.016458487, 0.8026405, -0.018400365, 1.0, 0.95287216, 0.7811377, -0.01673664, 0.026297752, 0.0018452583, 0.78044474, -0.012047584, 0.6318365, 0.020725405, 0.5981277, -0.01538065, 1.2186605, 0.01924876, 0.008183881, -0.0025821954], "split_indices": [40, 141, 138, 141, 0, 0, 0, 143, 0, 84, 143, 141, 0, 0, 0, 140, 0, 140, 0, 139, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1849.0, 223.0, 1752.0, 97.0, 132.0, 91.0, 1659.0, 93.0, 1391.0, 268.0, 1302.0, 89.0, 99.0, 169.0, 1198.0, 104.0, 1109.0, 89.0, 1010.0, 99.0, 907.0, 103.0, 220.0, 687.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026513562, -0.016536515, 0.09042096, -0.00553671, -0.014079638, -0.003228657, 0.02059631, -0.017960776, 0.01046333, -0.009073635, -0.011718824, -0.021339359, 0.016485153, -0.00251162, -0.014065466, -0.02459358, 0.020684233, -0.05189528, 0.035790298, -0.028896023, -0.018051244, -0.007212692, 0.09027793, -0.0067549, 0.011954437, 0.019565014, 0.00037221672], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1], "loss_changes": [2.6479716, 2.4370651, 3.7713199, 2.2420275, 0.0, 0.0, 0.0, 1.2980804, 0.0, 2.882112, 0.0, 2.8350031, 0.0, 5.03901, 0.0, 1.6255023, 0.0, 2.0085487, 1.8052075, 3.304894, 0.0, 0.0, 1.8605957, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 22, 22], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1], "split_conditions": [0.99982387, 0.87664425, 1.0, 0.71048903, -0.014079638, -0.003228657, 0.02059631, 0.6809079, 0.01046333, 0.67889345, -0.011718824, 0.5138817, 0.016485153, 0.52329415, -0.014065466, 1.0, 0.020684233, 0.4564035, 0.26953244, 0.35120344, -0.018051244, -0.007212692, 0.3779377, -0.0067549, 0.011954437, 0.019565014, 0.00037221672], "split_indices": [139, 142, 113, 142, 0, 0, 0, 141, 0, 140, 0, 141, 0, 139, 0, 93, 0, 143, 139, 142, 0, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1783.0, 266.0, 1638.0, 145.0, 129.0, 137.0, 1472.0, 166.0, 1351.0, 121.0, 1262.0, 89.0, 1090.0, 172.0, 986.0, 104.0, 679.0, 307.0, 576.0, 103.0, 103.0, 204.0, 457.0, 119.0, 92.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.00014537056, -0.014843359, 0.076985754, -0.0042524757, -0.020865649, 0.021739954, -0.023413727, 0.0042994455, -0.013113341, 0.010428223, -0.0142171, 0.017064815, -0.079964556, 0.010516853, 0.004948682, -0.0010017636, -0.016605303, -0.09208809, 0.027733002, -0.0027080616, -0.019069491, 0.006400712, 0.013685584, -0.022479754, 0.017792558, 0.01514967, -0.017809017, -0.0029408704, 0.015212543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, -1, 17, -1, -1, 19, 21, -1, -1, 23, -1, 25, -1, 27, -1, -1, -1], "loss_changes": [2.3444345, 3.5654562, 4.6662636, 1.7871201, 0.0, 0.0, 2.9268107, 1.6597449, 0.0, 0.0, 0.0, 1.4304179, 1.222389, 0.0, 2.6044598, 0.0, 0.0, 1.4358797, 2.2207592, 0.0, 0.0, 3.9530678, 0.0, 3.9993281, 0.0, 3.3568797, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 17, 17, 18, 18, 21, 21, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, -1, 18, -1, -1, 20, 22, -1, -1, 24, -1, 26, -1, 28, -1, -1, -1], "split_conditions": [0.90107125, 0.88207287, 1.0199403, 1.0, -0.020865649, 0.021739954, 1.6290085, 1.0, -0.013113341, 0.010428223, -0.0142171, 1.204987, 1.0, 0.010516853, 0.28644016, -0.0010017636, -0.016605303, 0.28313357, 1.2692307, -0.0027080616, -0.019069491, 0.8141592, 0.013685584, 0.66754913, 0.017792558, 0.56020635, -0.017809017, -0.0029408704, 0.015212543], "split_indices": [142, 141, 143, 43, 0, 0, 138, 40, 0, 0, 0, 138, 12, 0, 139, 0, 0, 140, 1, 0, 0, 143, 0, 143, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1737.0, 331.0, 1647.0, 90.0, 138.0, 193.0, 1543.0, 104.0, 93.0, 100.0, 1340.0, 203.0, 162.0, 1178.0, 112.0, 91.0, 224.0, 954.0, 135.0, 89.0, 798.0, 156.0, 683.0, 115.0, 550.0, 133.0, 415.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.00015990528, -0.0079973135, 0.014739434, -0.01859311, 0.017712919, -0.009525076, -0.0155559825, -0.02429425, 0.088719204, -0.0036866628, -0.16491699, 0.026642738, -0.0032385592, -0.024360193, 0.10346627, -0.026237836, -0.0060119787, -0.009043927, -0.015234184, 0.023285879, -0.0027146905, 0.003880193, -0.011302977, -0.020650057, 0.014611763, 0.007516538, -0.0036994168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [2.3845851, 3.8407393, 0.0, 2.3002183, 0.0, 2.5203643, 0.0, 4.3758216, 4.885338, 2.917457, 1.9712391, 0.0, 0.0, 2.1640615, 3.5997796, 0.0, 0.0, 1.3251109, 0.0, 0.0, 0.0, 3.0599585, 0.0, 1.1713814, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.3466678, 1.0, 0.014739434, 1.0558407, 0.017712919, 0.8026405, -0.0155559825, 0.67325324, 0.95287216, 0.55767125, 1.0, 0.026642738, -0.0032385592, 0.5458753, 1.0, -0.026237836, -0.0060119787, 0.4564035, -0.015234184, 0.023285879, -0.0027146905, 0.43214852, -0.011302977, 1.1749831, 0.014611763, 0.007516538, -0.0036994168], "split_indices": [139, 102, 0, 141, 0, 143, 0, 143, 143, 143, 111, 0, 0, 141, 115, 0, 0, 143, 0, 0, 0, 142, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1958.0, 104.0, 1852.0, 106.0, 1737.0, 115.0, 1510.0, 227.0, 1317.0, 193.0, 92.0, 135.0, 1104.0, 213.0, 100.0, 93.0, 986.0, 118.0, 107.0, 106.0, 877.0, 109.0, 748.0, 129.0, 109.0, 639.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.006015902, -0.0013002106, 0.01416401, -0.012072889, 0.012362141, -0.00616117, -0.012240353, -0.0157538, 0.012623364, -0.0042882254, -0.014335173, -0.025535425, 0.07450347, -0.0051357904, -0.1261736, -0.007080792, 0.17294022, -0.0232059, 0.010949213, -0.008460762, -0.017176209, 0.007741163, 0.026059015, -0.009994347, -0.007818564, -0.003317621, 0.00818745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, 21, 23, -1, -1, -1, -1, -1, 25, -1, -1, -1], "loss_changes": [2.0549333, 2.6443799, 0.0, 1.1799086, 0.0, 2.1806145, 0.0, 2.3422363, 0.0, 2.4592566, 0.0, 2.3753, 4.4628425, 1.992628, 0.3695104, 0.0, 1.5573907, 0.60361135, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4268929, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, 22, 24, -1, -1, -1, -1, -1, 26, -1, -1, -1], "split_conditions": [1.3494815, 1.0, 0.01416401, 1.5976542, 0.012362141, 0.8711886, -0.012240353, 0.7650251, 0.012623364, 0.5653381, -0.014335173, 0.4991331, 1.0, 0.4244175, 1.0, -0.007080792, 0.5981277, 1.3148105, 0.010949213, -0.008460762, -0.017176209, 0.007741163, 0.026059015, 1.0, -0.007818564, -0.003317621, 0.00818745], "split_indices": [140, 125, 0, 138, 0, 139, 0, 141, 0, 142, 0, 140, 126, 140, 122, 0, 139, 138, 0, 0, 0, 0, 0, 62, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1965.0, 106.0, 1809.0, 156.0, 1717.0, 92.0, 1601.0, 116.0, 1469.0, 132.0, 1157.0, 312.0, 962.0, 195.0, 126.0, 186.0, 831.0, 131.0, 102.0, 93.0, 89.0, 97.0, 670.0, 161.0, 535.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014667986, -0.0151737295, 0.05815479, -0.0069006816, -0.015788376, -0.0072011873, 0.02731034, -0.021181518, 0.021738358, 0.01497595, -0.013181849, -0.0094828345, -0.019716492, -0.044584412, 0.026688598, -0.022103421, -0.016335562, 0.052849796, -0.012386651, -0.058283567, 0.012632792, 0.02122485, 0.019326761, -0.020095749, -0.018981935, -0.016832046, 0.016584529, 0.009457199, -0.05942224, 0.08097383, -0.12140111, -0.0024146896, -0.013152346, -0.0041937917, 0.022088403, -0.005339764, -0.018519815], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, -1, 23, -1, -1, 25, 27, -1, 29, -1, -1, 31, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6875778, 1.9823058, 5.422597, 5.083108, 0.0, 5.789765, 0.0, 3.0716906, 0.0, 0.0, 0.0, 1.7762744, 0.0, 1.8957664, 2.7137656, 3.2060497, 0.0, 3.1366506, 0.0, 2.4110706, 0.0, 0.0, 2.5694993, 1.6775264, 0.0, 3.9784858, 0.0, 0.0, 0.7045199, 3.4565184, 0.8156223, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 22, 22, 23, 23, 25, 25, 28, 28, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, -1, 24, -1, -1, 26, 28, -1, 30, -1, -1, 32, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1665344, 0.9782575, 1.0444515, -0.015788376, 1.3493615, 0.02731034, 0.87664425, 0.021738358, 0.01497595, -0.013181849, 0.40307778, -0.019716492, 1.3148105, 1.0, 0.30554494, -0.016335562, 0.39922574, -0.012386651, 0.32365677, 0.012632792, 0.02122485, 0.73786306, 0.0, -0.018981935, 1.0, 0.016584529, 0.009457199, 0.23518711, 0.56483954, 0.5401446, -0.0024146896, -0.013152346, -0.0041937917, 0.022088403, -0.005339764, -0.018519815], "split_indices": [42, 141, 141, 139, 0, 138, 0, 142, 0, 0, 0, 142, 0, 138, 113, 139, 0, 141, 0, 140, 0, 0, 139, 1, 0, 122, 0, 0, 141, 140, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1679.0, 386.0, 1587.0, 92.0, 296.0, 90.0, 1492.0, 95.0, 131.0, 165.0, 1399.0, 93.0, 710.0, 689.0, 597.0, 113.0, 587.0, 102.0, 480.0, 117.0, 102.0, 485.0, 372.0, 108.0, 389.0, 96.0, 95.0, 277.0, 201.0, 188.0, 186.0, 91.0, 107.0, 94.0, 91.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011185051, 0.007956722, -0.0105621135, -0.006909777, 0.017770689, 0.0039145546, -0.017618174, 0.011865463, -0.013160035, -0.023086853, 0.055708274, -0.0071668886, -0.012165795, 0.121112846, -0.019068908, -0.023137724, 0.0104457205, 0.024384959, 0.04628648, -0.012359401, 0.06385043, -0.0009919088, -0.013067459, 0.018152185, -0.007330179, -0.004743577, 0.017893052, 0.07522241, -0.048511088, 0.013735306, -0.0008795137, -0.011973654, -0.0010764868], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [1.9584155, 4.7948227, 0.0, 3.2009513, 0.0, 1.7691998, 0.0, 2.3767648, 0.0, 1.354261, 3.3648493, 1.3245683, 0.0, 3.3705068, 2.7821562, 1.5479692, 0.0, 0.0, 3.687345, 0.0, 2.292422, 1.9520649, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0805538, 0.89257884, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 20, 20, 21, 21, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.1510894, 0.9677354, -0.0105621135, 0.9144915, 0.017770689, 1.0, -0.017618174, 0.4244175, -0.013160035, 0.44801942, 1.0, 0.38895512, -0.012165795, 1.3507949, 1.0, 1.0, 0.0104457205, 0.024384959, 1.0, -0.012359401, 0.56409985, 1.2186605, -0.013067459, 0.018152185, -0.007330179, -0.004743577, 0.017893052, 1.0, 1.0, 0.013735306, -0.0008795137, -0.011973654, -0.0010764868], "split_indices": [143, 141, 0, 139, 0, 117, 0, 140, 0, 141, 122, 141, 0, 138, 126, 0, 0, 0, 13, 0, 139, 138, 0, 0, 0, 0, 0, 13, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1900.0, 165.0, 1747.0, 153.0, 1642.0, 105.0, 1551.0, 91.0, 863.0, 688.0, 743.0, 120.0, 367.0, 321.0, 650.0, 93.0, 139.0, 228.0, 142.0, 179.0, 539.0, 111.0, 107.0, 121.0, 91.0, 88.0, 207.0, 332.0, 119.0, 88.0, 115.0, 217.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0010546014, 0.007386058, -0.012181324, -0.053585257, 0.020046154, -0.012928109, -0.01018986, 0.014608852, 0.009892274, -0.009526444, 0.0063341297, -0.011167615, 0.019216878, 0.007875432, 0.094330706, -0.02597018, 0.0672591, -0.00020276684, 0.018964175, 0.005862995, -0.025557546, 0.023622125, -0.016647266, -0.024686715, 0.018453253, -0.09019784, 0.011189436, 0.044530783, -0.07802865, -0.016058719, -0.001594092, -0.0024869372, 0.013857776, 0.0008111476, -0.012050873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, -1, 23, -1, -1, 25, 27, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6033185, 1.5129292, 0.0, 0.6619629, 2.0771458, 1.1491932, 0.0, 0.0, 1.7026333, 0.0, 0.0, 0.0, 1.1883997, 2.4359705, 1.6806755, 5.6425986, 6.2378807, 0.0, 0.0, 3.7007296, 0.0, 0.0, 2.7795672, 2.1377814, 0.0, 0.9774287, 0.0, 1.6447728, 1.1965716, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 22, 22, 23, 23, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, -1, 24, -1, -1, 26, 28, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.21544637, -0.012181324, 0.16242528, 1.2186605, 0.15793334, -0.01018986, 0.014608852, 0.28365853, -0.009526444, 0.0063341297, -0.011167615, 1.2692307, 0.76931804, 1.3275425, 0.8961227, 0.95287216, -0.00020276684, 0.018964175, 0.68873906, -0.025557546, 0.023622125, 1.0, 1.3243612, 0.018453253, 0.9782575, 0.011189436, 1.0, 0.4802205, -0.016058719, -0.001594092, -0.0024869372, 0.013857776, 0.0008111476, -0.012050873], "split_indices": [117, 142, 0, 143, 138, 140, 0, 0, 141, 0, 0, 0, 1, 143, 138, 140, 143, 0, 0, 140, 0, 0, 125, 138, 0, 141, 0, 93, 142, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 337.0, 1623.0, 183.0, 154.0, 121.0, 1502.0, 88.0, 95.0, 107.0, 1395.0, 1212.0, 183.0, 772.0, 440.0, 91.0, 92.0, 678.0, 94.0, 146.0, 294.0, 579.0, 99.0, 187.0, 107.0, 252.0, 327.0, 96.0, 91.0, 145.0, 107.0, 108.0, 219.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.000213805, -0.012834592, 0.058225814, -0.004142809, -0.016508402, -0.01534784, 0.019120341, -0.01825628, 0.021130363, 0.009711088, -0.018288841, -0.00807419, -0.01718635, -0.04712798, 0.02544482, -0.019213805, -0.020961782, 0.090765595, -0.045847096, -0.055499464, 0.014925529, 0.019920764, 0.1547655, -0.10618487, 0.006947589, -0.012457502, -0.013719692, 0.012890497, -0.008790401, 0.026150933, 0.0013239931, -0.021343255, -0.0022322002, 0.026684608, -0.010010183, 0.009036547, -0.0020806175], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [1.5623733, 2.2297919, 3.7080026, 4.8468714, 0.0, 4.5973015, 0.0, 2.3398077, 0.0, 0.0, 0.0, 1.8365893, 0.0, 2.9391785, 3.5159168, 3.3804955, 0.0, 1.7864201, 2.5119581, 1.5999702, 0.0, 2.197474, 3.1271434, 2.1316009, 0.0, 1.0223142, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6229965, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25, 33, 33], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.0, 1.1665344, 0.8071148, 1.0444515, -0.016508402, 0.5030815, 0.019120341, 0.87664425, 0.021130363, 0.009711088, -0.018288841, 1.0, -0.01718635, 0.535755, 1.0, 0.41258156, -0.020961782, 0.44982666, 0.5, 0.33421066, 0.014925529, 0.26866648, 0.675519, 1.0, 0.006947589, 1.0, -0.013719692, 0.012890497, -0.008790401, 0.026150933, 0.0013239931, -0.021343255, -0.0022322002, 1.0, -0.010010183, 0.009036547, -0.0020806175], "split_indices": [42, 141, 142, 139, 0, 141, 0, 142, 0, 0, 0, 39, 0, 139, 13, 139, 0, 142, 1, 141, 0, 143, 141, 106, 0, 109, 0, 0, 0, 0, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1685.0, 379.0, 1594.0, 91.0, 244.0, 135.0, 1496.0, 98.0, 146.0, 98.0, 1403.0, 93.0, 648.0, 755.0, 553.0, 95.0, 394.0, 361.0, 455.0, 98.0, 187.0, 207.0, 237.0, 124.0, 298.0, 157.0, 93.0, 94.0, 118.0, 89.0, 104.0, 133.0, 206.0, 92.0, 88.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.008235275, -0.0021409246, 0.08799133, 0.005912443, -0.01564077, 0.025357846, -0.006413343, -0.010139149, 0.093504846, 0.0066194055, -0.10694904, -0.0032496161, 0.026788129, 0.08490801, -0.010513426, -0.0043202885, -0.019719636, -0.0064377435, 0.025572503, 0.02491655, -0.069347546, -0.014519024, 0.055157766, -0.028206319, -0.020328507, -0.018481327, 0.12252971, 0.0022712587, -0.009476176, 0.011084742, -0.011159805, 0.064917505, 0.02495384, 0.015092345, -0.001265645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, -1, 21, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.6965078, 2.253654, 5.9448175, 2.42394, 0.0, 0.0, 0.0, 2.3638284, 5.866419, 1.6659015, 1.2368772, 0.0, 0.0, 5.686611, 2.1240969, 0.0, 0.0, 0.0, 0.0, 3.2717338, 2.1104653, 0.0, 2.6790533, 0.99295676, 0.0, 3.1070087, 2.0634632, 0.0, 0.0, 0.0, 0.0, 1.2943339, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 19, 19, 20, 20, 22, 22, 23, 23, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, -1, 22, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.562096, 1.5248721, 1.0, 1.0, -0.01564077, 0.025357846, -0.006413343, 2.0, 0.5338557, 1.0, 0.537529, -0.0032496161, 0.026788129, 1.0, 1.0, -0.0043202885, -0.019719636, -0.0064377435, 0.025572503, -0.3846154, 0.428107, -0.014519024, 1.0, 0.26820892, -0.020328507, 1.0, 0.5653381, 0.0022712587, -0.009476176, 0.011084742, -0.011159805, 0.3222687, 0.02495384, 0.015092345, -0.001265645], "split_indices": [138, 138, 69, 42, 0, 0, 0, 0, 143, 89, 142, 0, 0, 97, 23, 0, 0, 0, 0, 1, 142, 0, 115, 140, 0, 39, 142, 0, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1814.0, 236.0, 1724.0, 90.0, 113.0, 123.0, 1457.0, 267.0, 1242.0, 215.0, 155.0, 112.0, 223.0, 1019.0, 126.0, 89.0, 119.0, 104.0, 636.0, 383.0, 96.0, 540.0, 293.0, 90.0, 258.0, 282.0, 166.0, 127.0, 108.0, 150.0, 194.0, 88.0, 92.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00042931733, -0.010901291, 0.058028143, -0.0011216667, -0.09914015, -0.056914397, 0.023366038, -0.009518878, 0.013542426, -0.0038060513, -0.016021974, 0.008026957, -0.021748202, -0.08846901, 0.0067037507, -0.0024540073, -0.016387237, 0.09173288, -0.012640164, -0.0039374908, 0.020983828, 0.011278418, -0.024896618, 0.027865108, -0.07191631, -0.009812116, 0.061290048, -0.028390743, -0.14550096, 0.018997185, -0.0387217, 0.005974845, -0.06929746, -0.007193339, -0.021826006, 0.005963271, -0.012831189, -0.015948491, 0.004539741], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, 15, 17, -1, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, 29, 31, 33, -1, 35, -1, 37, -1, -1, -1, -1, -1, -1], "loss_changes": [1.273303, 1.5222309, 6.379287, 1.8208082, 0.65660596, 4.2072153, 0.0, 1.9160453, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2292156, 2.0411925, 0.0, 0.0, 3.5614438, 1.5541675, 0.0, 0.0, 0.0, 2.2848542, 1.8276101, 1.5597699, 0.0, 4.4143047, 1.1032785, 0.96884036, 0.0, 1.7006371, 0.0, 2.1619048, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 23, 23, 24, 24, 26, 26, 27, 27, 28, 28, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, 16, 18, -1, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, 30, 32, 34, -1, 36, -1, 38, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9476538, 1.486252, 1.0, 1.4446453, 0.76061165, 1.0953796, 0.023366038, -1.0, 0.013542426, -0.0038060513, -0.016021974, 0.008026957, -0.021748202, 1.0, 1.0, -0.0024540073, -0.016387237, 1.0, 1.1786883, -0.0039374908, 0.020983828, 0.011278418, 1.0, 0.26953244, 1.0, -0.009812116, 1.3243612, 0.31252044, 0.28565422, 0.018997185, 1.0, 0.005974845, 0.4967865, -0.007193339, -0.021826006, 0.005963271, -0.012831189, -0.015948491, 0.004539741], "split_indices": [140, 138, 0, 138, 143, 139, 0, 0, 0, 0, 0, 0, 0, 127, 89, 0, 0, 97, 138, 0, 0, 0, 97, 139, 23, 0, 138, 139, 140, 0, 13, 0, 139, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1764.0, 316.0, 1588.0, 176.0, 191.0, 125.0, 1496.0, 92.0, 88.0, 88.0, 103.0, 88.0, 255.0, 1241.0, 138.0, 117.0, 230.0, 1011.0, 109.0, 121.0, 90.0, 921.0, 434.0, 487.0, 91.0, 343.0, 306.0, 181.0, 150.0, 193.0, 97.0, 209.0, 90.0, 91.0, 92.0, 101.0, 117.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056872997, 0.00057285046, -0.013362914, -0.008404745, 0.08629443, 0.0023407151, -0.09971244, 0.021759598, -0.003160898, 0.011230837, -0.010269174, 0.003154513, -0.022957368, -0.0019283985, 0.019478878, 0.017352484, -0.051825915, -0.0106678195, 0.13322812, -0.019490944, -0.013682066, 0.012367766, -0.013279301, 0.004407247, 0.023640824, -0.006324044, 0.002300854, -0.05706103, 0.11995592, 0.004516781, -0.01732791, 0.02340789, 0.0012595762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [1.6483251, 1.5099037, 0.0, 1.7425108, 2.8794484, 1.4837307, 3.1874666, 0.0, 0.0, 3.538682, 0.0, 0.0, 0.0, 1.3151473, 0.0, 3.201415, 1.0471028, 2.2337012, 1.7662246, 0.5131755, 0.0, 4.9897704, 0.0, 0.0, 0.0, 0.0, 0.0, 4.823621, 3.210091, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0769875, -0.013362914, 0.84173065, 1.1951114, 1.4891474, 1.0, 0.021759598, -0.003160898, 0.71048903, -0.010269174, 0.003154513, -0.022957368, 1.0, 0.019478878, 1.0, 0.37346566, 0.6151461, 0.4208793, 0.20523074, -0.013682066, 0.43840483, -0.013279301, 0.004407247, 0.023640824, -0.006324044, 0.002300854, 0.2898547, 0.46382326, 0.004516781, -0.01732791, 0.02340789, 0.0012595762], "split_indices": [52, 142, 0, 142, 143, 138, 69, 0, 0, 142, 0, 0, 0, 80, 0, 116, 140, 139, 140, 139, 0, 142, 0, 0, 0, 0, 0, 142, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1962.0, 96.0, 1776.0, 186.0, 1589.0, 187.0, 88.0, 98.0, 1465.0, 124.0, 93.0, 94.0, 1367.0, 98.0, 986.0, 381.0, 794.0, 192.0, 276.0, 105.0, 668.0, 126.0, 103.0, 89.0, 136.0, 140.0, 406.0, 262.0, 216.0, 190.0, 127.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022482926, -0.008118118, 0.010378899, 0.0011014909, -0.012924242, -0.008525701, 0.018981637, 0.0026122024, -0.066498905, 0.017515091, -0.04480999, -0.12857713, 0.006317559, 0.033016, -0.009627563, -0.07553499, 0.0032943045, -0.004950177, -0.021468138, 0.057100423, -0.030307084, -0.0006349225, -0.012150404, 0.016348727, 0.021501323, 0.0056659034, -0.018776154, 0.05880984, -0.00883535, 0.011997818, -0.0013449442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, 25, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.2815632, 2.1787186, 0.0, 3.293849, 0.0, 1.1138326, 0.0, 1.0226349, 2.2378902, 1.9420087, 0.8265812, 1.2800395, 0.0, 1.4778221, 0.0, 0.7887397, 0.0, 0.0, 0.0, 4.51752, 3.6560857, 0.0, 0.0, 2.480741, 0.0, 0.0, 0.0, 1.7547325, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 19, 19, 20, 20, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, 26, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.3466678, 1.0516728, 0.010378899, 1.0, -0.012924242, 2.0, 0.018981637, 1.0, 5.0, 1.3461539, 0.34489173, 0.518582, 0.006317559, 0.67325324, -0.009627563, 0.16273141, 0.0032943045, -0.004950177, -0.021468138, 0.56020635, 0.03846154, -0.0006349225, -0.012150404, 0.4802205, 0.021501323, 0.0056659034, -0.018776154, 1.0, -0.00883535, 0.011997818, -0.0013449442], "split_indices": [139, 142, 0, 125, 0, 0, 0, 80, 0, 1, 139, 142, 0, 143, 0, 142, 0, 0, 0, 143, 1, 0, 0, 142, 0, 0, 0, 23, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1951.0, 108.0, 1813.0, 138.0, 1725.0, 88.0, 1447.0, 278.0, 1101.0, 346.0, 188.0, 90.0, 969.0, 132.0, 248.0, 98.0, 98.0, 90.0, 702.0, 267.0, 99.0, 149.0, 558.0, 144.0, 172.0, 95.0, 397.0, 161.0, 215.0, 182.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.003795926, 0.0015646736, -0.012013864, -0.008736385, 0.015666682, -0.00057523523, -0.013315809, -0.010676005, 0.017808888, -0.0017665598, -0.016461138, -0.011111522, 0.010153509, 0.005468063, -0.064068675, -0.020288449, 0.109811895, 0.01458123, -0.018497827, -0.0019046363, -0.009340348, 0.0011335834, 0.023420269, -0.0103744045, 0.015016231, -0.0028386302, 0.0075744973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.288496, 3.15549, 0.0, 1.8805653, 0.0, 3.1364727, 0.0, 2.256082, 0.0, 1.5011191, 0.0, 1.2520391, 0.0, 2.9186614, 3.2332397, 1.1707395, 2.6336448, 3.3047898, 0.0, 1.4311783, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1314709, -0.012013864, 1.5298582, 0.015666682, 0.8316313, -0.013315809, 0.8730146, 0.017808888, 0.7189062, -0.016461138, 0.5139595, 0.010153509, 0.40912068, 0.1923077, 0.39922574, 0.44350845, 0.5992283, -0.018497827, 0.3186285, -0.009340348, 0.0011335834, 0.023420269, -0.0103744045, 0.015016231, -0.0028386302, 0.0075744973], "split_indices": [143, 139, 0, 138, 0, 139, 0, 141, 0, 141, 0, 140, 0, 140, 1, 141, 139, 143, 0, 141, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1975.0, 91.0, 1852.0, 123.0, 1738.0, 114.0, 1645.0, 93.0, 1555.0, 90.0, 1426.0, 129.0, 1086.0, 340.0, 871.0, 215.0, 206.0, 134.0, 696.0, 175.0, 120.0, 95.0, 110.0, 96.0, 519.0, 177.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0059288787, -0.015749518, 0.04880397, 0.012329323, -0.03838066, -0.030140024, 0.024226015, -0.030907806, 0.14588906, -0.018326141, -0.022505833, -0.010411757, 0.006251869, 0.013102392, -0.01672172, 0.032373082, -0.0041514076, -0.043736756, 0.00987907, -0.026796142, 0.014430721, -0.08483565, 0.04708445, 0.024551637, -0.013085494, -0.01834433, -0.1532345, -0.0069469013, 0.01745308, -0.009268519, 0.0106183225, 0.0055810115, -0.006051969, -0.024931718, -0.0023892405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1094238, 1.1120485, 4.7954736, 4.5100718, 3.6276746, 1.5285902, 0.0, 3.5394125, 6.3656697, 2.6040096, 0.0, 0.0, 0.0, 2.3347569, 0.0, 0.0, 0.0, 2.6837761, 0.0, 1.8273709, 0.0, 2.2512248, 3.3273664, 2.1915822, 0.0, 0.78499997, 3.0323167, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 17, 17, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 0.46594483, 0.8316313, 1.6103358, 3.1923077, 0.024226015, 0.6095173, 1.0199403, 0.7960763, -0.022505833, -0.010411757, 0.006251869, 0.52807486, -0.01672172, 0.032373082, -0.0041514076, 1.0, 0.00987907, 0.37522858, 0.014430721, 0.40912068, 1.0, 0.20523074, -0.013085494, 0.22962083, 1.0, -0.0069469013, 0.01745308, -0.009268519, 0.0106183225, 0.0055810115, -0.006051969, -0.024931718, -0.0023892405], "split_indices": [1, 69, 139, 139, 138, 1, 0, 139, 143, 143, 0, 0, 0, 142, 0, 0, 0, 116, 0, 143, 0, 140, 71, 139, 0, 140, 12, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1750.0, 314.0, 781.0, 969.0, 223.0, 91.0, 590.0, 191.0, 875.0, 94.0, 124.0, 99.0, 446.0, 144.0, 98.0, 93.0, 719.0, 156.0, 342.0, 104.0, 495.0, 224.0, 229.0, 113.0, 251.0, 244.0, 117.0, 107.0, 94.0, 135.0, 91.0, 160.0, 140.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0047112093, -0.001960598, 0.012974347, -0.06954002, 0.010968714, 0.004347449, -0.025845686, 0.02614003, -0.100494, -0.00498104, 0.005708012, 0.015371037, 0.015895758, -0.0060466137, -0.01430499, 0.028359283, -0.011344679, 0.0076949312, 0.017005768, 0.023953086, -0.009527336, 0.0073228693, 0.013371249, 0.05911552, -0.031837422, -0.0021199936, 0.016385123, 0.0018971516, -0.01298261], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [1.7125947, 1.7029495, 0.0, 4.369038, 2.7665346, 0.6425748, 0.0, 2.0596473, 0.33387005, 0.0, 0.0, 2.2285926, 0.0, 0.0, 0.0, 3.543007, 0.0, 1.7678223, 0.0, 1.6646936, 0.0, 1.6063465, 0.0, 2.8684568, 2.2453942, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.3466678, -0.42307693, 0.012974347, 1.4817663, 1.0, 0.46812254, -0.025845686, 0.99982387, 0.32253915, -0.00498104, 0.005708012, 0.84173065, 0.015895758, -0.0060466137, -0.01430499, 0.6960004, -0.011344679, 2.0, 0.017005768, 0.5964612, -0.009527336, 1.2609842, 0.013371249, 0.2891638, 0.46382326, -0.0021199936, 0.016385123, 0.0018971516, -0.01298261], "split_indices": [139, 1, 0, 138, 40, 140, 0, 139, 141, 0, 0, 142, 0, 0, 0, 140, 0, 0, 0, 139, 0, 138, 0, 140, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1949.0, 104.0, 313.0, 1636.0, 225.0, 88.0, 1440.0, 196.0, 111.0, 114.0, 1332.0, 108.0, 101.0, 95.0, 1210.0, 122.0, 1056.0, 154.0, 912.0, 144.0, 792.0, 120.0, 341.0, 451.0, 193.0, 148.0, 297.0, 154.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0023753184, 0.007365568, -0.0098295305, -0.0010631367, 0.009812468, 0.008397824, -0.015155434, -0.002796809, 0.02137176, 0.011481788, -0.012328814, 0.022151815, -0.014892424, 0.010270864, 0.016633809, 0.06933327, -0.017015275, 0.021419028, 0.020166783, 0.0118283285, -0.016818104, 0.010187648, -0.00486568, -0.002373143, 0.014424167], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1], "loss_changes": [1.0424207, 1.5123694, 0.0, 2.575639, 0.0, 3.9120126, 0.0, 2.776802, 0.0, 2.4697473, 0.0, 2.3177834, 0.0, 2.0144815, 0.0, 2.50458, 3.7279418, 1.6350557, 0.0, 3.3807657, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0098295305, 1.1106075, 0.009812468, 1.0081195, -0.015155434, 0.7446223, 0.02137176, 1.0, -0.012328814, 0.76394844, -0.014892424, 1.0, 0.016633809, 0.4967865, 0.54411215, 0.7307692, 0.020166783, 0.44982666, -0.016818104, 0.010187648, -0.00486568, -0.002373143, 0.014424167], "split_indices": [52, 125, 0, 143, 0, 141, 0, 141, 0, 84, 0, 139, 0, 53, 0, 139, 139, 1, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1977.0, 98.0, 1809.0, 168.0, 1702.0, 107.0, 1614.0, 88.0, 1443.0, 171.0, 1353.0, 90.0, 1250.0, 103.0, 395.0, 855.0, 290.0, 105.0, 718.0, 137.0, 135.0, 155.0, 566.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0058610467, -0.03428214, 0.011622029, -0.008333318, -0.017457454, 0.1251047, -0.007412436, -0.04237931, 0.020641828, 0.0018031228, 0.023693696, 0.015805067, -0.12809101, -0.10405265, 0.026096225, -0.0045015365, 0.020779481, -0.018773748, -0.00691147, 0.017363783, -0.029603591, -0.015907718, 0.012873522, 0.024899581, -0.012425733, -0.0046128123, 0.008584947, -0.034928367, 0.104322754, -0.0040139114, -0.011012566, 0.20724876, -0.010045707, -0.004416779, 0.006876496, 0.0074586244, 0.032546286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, 27, -1, -1, -1, 29, 31, 33, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [1.0280616, 2.8686528, 2.7670655, 4.8621016, 0.0, 2.203265, 3.0736353, 2.4240682, 0.0, 0.0, 0.0, 3.586768, 0.6226373, 7.0395975, 5.169636, 2.9294338, 0.0, 0.0, 0.0, 0.8044332, 0.0, 0.0, 0.0, 3.1741529, 0.0, 0.0, 0.0, 0.8857041, 6.0491476, 0.7890355, 0.0, 2.9953718, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 23, 23, 27, 27, 28, 28, 29, 29, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, 28, -1, -1, -1, 30, 32, 34, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [-0.1923077, 1.0558407, -0.03846154, 0.9355003, -0.017457454, 1.0, 1.0, 1.0, 0.020641828, 0.0018031228, 0.023693696, 0.9016015, 0.5, 0.5887627, 0.3409893, 0.61088866, 0.020779481, -0.018773748, -0.00691147, 0.40477976, -0.029603591, -0.015907718, 0.012873522, 0.32742807, -0.012425733, -0.0046128123, 0.008584947, 0.29720545, 1.0, 0.1963199, -0.011012566, 1.0, -0.010045707, -0.004416779, 0.006876496, 0.0074586244, 0.032546286], "split_indices": [1, 141, 1, 142, 0, 124, 64, 97, 0, 0, 0, 140, 1, 141, 142, 143, 0, 0, 0, 140, 0, 0, 0, 140, 0, 0, 0, 141, 109, 142, 0, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 788.0, 1281.0, 665.0, 123.0, 184.0, 1097.0, 574.0, 91.0, 94.0, 90.0, 920.0, 177.0, 302.0, 272.0, 832.0, 88.0, 88.0, 89.0, 185.0, 117.0, 97.0, 175.0, 668.0, 164.0, 96.0, 89.0, 381.0, 287.0, 270.0, 111.0, 191.0, 96.0, 174.0, 96.0, 90.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0032981182, -0.0024671669, 0.012142643, 0.005529226, -0.013124389, -0.0042421464, 0.015733449, 0.004196855, -0.01589572, -0.006339979, 0.015316257, 0.0040356726, -0.015911618, -0.004701505, 0.01078677, 0.012725185, -0.07614426, -0.0029679588, 0.016165622, 0.0030704867, -0.017741175, -0.0013502697, 0.009980422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1], "loss_changes": [1.4049929, 2.025517, 0.0, 2.747157, 0.0, 2.271815, 0.0, 2.589885, 0.0, 2.442721, 0.0, 1.3090882, 0.0, 1.6571097, 0.0, 2.5008004, 2.8241103, 1.048032, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1], "split_conditions": [1.2604058, 1.0558407, 0.012142643, 1.0, -0.013124389, 1.0199403, 0.015733449, 0.8711886, -0.01589572, 0.7734109, 0.015316257, 0.7202307, -0.015911618, 0.50989574, 0.01078677, 0.52329415, 1.0, 3.0384614, 0.016165622, 0.0030704867, -0.017741175, -0.0013502697, 0.009980422], "split_indices": [141, 141, 0, 125, 0, 143, 0, 139, 0, 141, 0, 140, 0, 141, 0, 139, 124, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1967.0, 96.0, 1852.0, 115.0, 1740.0, 112.0, 1650.0, 90.0, 1541.0, 109.0, 1443.0, 98.0, 1331.0, 112.0, 1070.0, 261.0, 968.0, 102.0, 127.0, 134.0, 878.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0065557645, -0.011295812, 0.008653874, -0.018894346, 0.010975666, -0.0039088777, -0.09940247, -0.01977801, 0.11314729, -0.011911262, -0.025127396, -0.008710678, -0.011271326, -0.0040408927, 0.026343638, -0.009443872, 0.007617981, 0.0047346056, -0.012713675, -0.013997548, 0.06594084, 0.0049392325, -0.009065184, 0.021046722, -0.0010170675, -0.017650587, 0.00998165, 0.007729206, -0.004762044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, 17, -1, -1, -1, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [0.9107859, 1.8065288, 0.0, 2.2295227, 0.0, 2.894109, 3.8533514, 1.4111638, 4.292476, 1.3376675, 0.0, 1.9521261, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2623236, 0.0, 1.2236867, 2.8380322, 1.4488443, 0.0, 0.0, 0.0, 1.5535977, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 17, 17, 19, 19, 20, 20, 21, 21, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, 18, -1, -1, -1, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.2520225, 5.0, 0.008653874, 0.8362515, 0.010975666, 0.761162, 1.0, 0.67325324, 1.0, 0.8951914, -0.025127396, 2.0, -0.011271326, -0.0040408927, 0.026343638, -0.009443872, 0.007617981, 0.46594483, -0.012713675, 0.37509307, 1.3573998, 1.0, -0.009065184, 0.021046722, -0.0010170675, 0.17247613, 0.00998165, 0.007729206, -0.004762044], "split_indices": [142, 0, 0, 143, 0, 139, 97, 143, 13, 139, 0, 0, 0, 0, 0, 0, 0, 139, 0, 139, 138, 62, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1964.0, 100.0, 1848.0, 116.0, 1558.0, 290.0, 1372.0, 186.0, 184.0, 106.0, 1226.0, 146.0, 92.0, 94.0, 95.0, 89.0, 1101.0, 125.0, 843.0, 258.0, 676.0, 167.0, 89.0, 169.0, 546.0, 130.0, 131.0, 415.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0040827836, -0.0057794806, 0.048254255, -0.015233408, 0.011489258, -0.011481325, 0.017360106, -0.005340383, -0.017416697, 0.007655617, -0.01766753, 0.009327039, -0.08702164, -0.037330404, 0.04839068, 0.0030067922, -0.016508138, 0.014594564, -0.099440664, 0.021085339, 0.021506699, -0.043478616, 0.0093345335, -0.0006253038, -0.02048496, -0.015109896, 0.014804711, 0.0008157484, -0.009454101, 0.04862952, -0.07997745, -0.00175815, 0.014104909, -0.015758859, 0.0039491327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, 15, 17, 19, -1, -1, 21, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.9021913, 1.9314164, 2.8303378, 2.4685633, 0.0, 3.7230759, 0.0, 1.7707233, 0.0, 0.0, 0.0, 2.2837298, 2.0564957, 1.841517, 3.1038861, 0.0, 0.0, 1.4222989, 2.5539293, 2.6929111, 0.0, 0.47196293, 0.0, 0.0, 0.0, 1.8853868, 0.0, 0.0, 0.0, 1.4074152, 2.0954967, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, 16, 18, 20, -1, -1, 22, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.1314709, 0.84547085, 0.9453911, 0.011489258, 0.6095173, 0.017360106, 1.0, -0.017416697, 0.007655617, -0.01766753, 1.0, 0.38341978, 0.33421066, 0.761162, 0.0030067922, -0.016508138, 0.20321046, 0.03846154, 1.0, 0.021506699, 0.14724413, 0.0093345335, -0.0006253038, -0.02048496, 0.49346045, 0.014804711, 0.0008157484, -0.009454101, 0.30760387, 0.5962914, -0.00175815, 0.014104909, -0.015758859, 0.0039491327], "split_indices": [42, 139, 141, 141, 0, 139, 0, 113, 0, 0, 0, 39, 140, 141, 139, 0, 0, 143, 1, 50, 0, 142, 0, 0, 0, 143, 0, 0, 0, 142, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1693.0, 378.0, 1570.0, 123.0, 256.0, 122.0, 1478.0, 92.0, 167.0, 89.0, 1253.0, 225.0, 571.0, 682.0, 90.0, 135.0, 311.0, 260.0, 586.0, 96.0, 179.0, 132.0, 138.0, 122.0, 456.0, 130.0, 89.0, 90.0, 230.0, 226.0, 134.0, 96.0, 137.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025230583, -0.0025283557, 0.010437609, 0.005355747, -0.016968908, -0.0059713637, 0.08198856, 0.0039036197, -0.018057905, 0.020462545, -0.011240392, 0.015666442, -0.0740539, -0.014012321, 0.04534521, -0.0036929378, -0.011924893, 0.020979509, -0.015958903, 0.003842527, 0.018484777, -0.008888333, 0.013909327, 0.047802225, -0.0123115685, -0.058418747, 0.06194844, -0.03443288, 0.019618299, -0.012375256, 0.000898913, 0.018760834, -0.0062315194, 0.005250709, -0.00836442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, -1, 25, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0670772, 2.604195, 0.0, 1.6379699, 0.0, 2.8346646, 5.7930446, 1.4268525, 0.0, 0.0, 0.0, 1.190881, 0.3422798, 3.4435415, 3.9138565, 0.0, 0.0, 1.9226533, 0.0, 2.9077237, 0.0, 1.5262301, 0.0, 4.722216, 0.0, 1.1274277, 2.7950776, 1.0653293, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, -1, 26, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2604058, 1.2337983, 0.010437609, 0.82821655, -0.016968908, 0.8201793, 1.0, 1.0, -0.018057905, 0.020462545, -0.011240392, 1.0, 0.30769083, 0.6000145, 0.5724227, -0.0036929378, -0.011924893, 0.5311527, -0.015958903, 0.46382326, 0.018484777, 1.0, 0.013909327, 0.33186364, -0.0123115685, 0.26866648, 0.31233892, 0.23518711, 0.019618299, -0.012375256, 0.000898913, 0.018760834, -0.0062315194, 0.005250709, -0.00836442], "split_indices": [141, 140, 0, 141, 0, 142, 126, 40, 0, 0, 0, 111, 140, 142, 141, 0, 0, 143, 0, 140, 0, 17, 0, 140, 0, 143, 140, 141, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1976.0, 98.0, 1887.0, 89.0, 1644.0, 243.0, 1556.0, 88.0, 149.0, 94.0, 1352.0, 204.0, 676.0, 676.0, 112.0, 92.0, 545.0, 131.0, 521.0, 155.0, 435.0, 110.0, 387.0, 134.0, 256.0, 179.0, 249.0, 138.0, 130.0, 126.0, 89.0, 90.0, 90.0, 159.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0041544493, 0.014547621, -0.07580309, 0.0032492513, 0.022569347, 0.005689956, -0.026870077, 0.01362574, -0.07609732, 0.022945596, -0.012174748, -0.0024951922, -0.012985215, 0.0031360344, 0.11269814, 0.024855277, -0.091441385, 0.0015583093, 0.027860297, -0.0058094016, 0.14034367, 0.0035363666, -0.020721992, 0.025162525, -0.016545335, 0.025658745, 0.00498175, 0.0013971344, 0.014911532, 0.0025526858, -0.008056412], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.7193646, 4.36804, 6.092333, 1.4309626, 0.0, 0.0, 0.0, 1.9391694, 0.55261123, 2.5567038, 0.0, 0.0, 0.0, 2.4197884, 4.189081, 3.3926752, 3.2298875, 0.0, 0.0, 3.7429724, 2.1151433, 0.0, 0.0, 1.8676285, 0.0, 0.0, 0.0, 1.0521376, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.3131162, 1.4839479, 1.0, 0.022569347, 0.005689956, -0.026870077, 1.0374465, 0.28008294, 0.71645033, -0.012174748, -0.0024951922, -0.012985215, 0.59144264, 1.4762797, 0.45070726, 1.0, 0.0015583093, 0.027860297, 0.39922574, 0.0, 0.0035363666, -0.020721992, 0.38907775, -0.016545335, 0.025658745, 0.00498175, 0.28601104, 0.014911532, 0.0025526858, -0.008056412], "split_indices": [119, 139, 138, 40, 0, 0, 0, 142, 140, 143, 0, 0, 0, 141, 138, 141, 108, 0, 0, 141, 1, 0, 0, 142, 0, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1831.0, 238.0, 1738.0, 93.0, 141.0, 97.0, 1537.0, 201.0, 1438.0, 99.0, 103.0, 98.0, 1178.0, 260.0, 958.0, 220.0, 164.0, 96.0, 757.0, 201.0, 105.0, 115.0, 634.0, 123.0, 88.0, 113.0, 532.0, 102.0, 411.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0058047986, -0.0003439643, -0.010490879, -0.00739731, 0.01255311, -0.015279438, 0.00899668, -0.0057490715, -0.016776528, -0.014153083, 0.012442731, -0.0047858614, -0.014841655, -0.017112013, 0.010351424, 0.009548906, -0.090351135, -0.005739128, 0.0128968675, -0.036092885, -0.016600698, 0.0012348177, -0.01504376, -0.012144597, 0.0054594735], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1], "loss_changes": [1.1191815, 1.7401667, 0.0, 1.4243618, 0.0, 2.4952228, 0.0, 1.76791, 0.0, 1.9091513, 0.0, 1.8942561, 0.0, 2.487641, 0.0, 1.7051982, 1.395684, 2.1670465, 0.0, 1.532613, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1], "split_conditions": [1.6878304, 1.0, -0.010490879, 1.0183754, 0.01255311, 1.5248721, 0.00899668, 1.4726204, -0.016776528, 0.76379925, 0.012442731, 0.638004, -0.014841655, 1.3325276, 0.010351424, 0.4370801, 0.5217996, 0.48259154, 0.0128968675, 1.0, -0.016600698, 0.0012348177, -0.01504376, -0.012144597, 0.0054594735], "split_indices": [138, 102, 0, 142, 0, 138, 0, 138, 0, 142, 0, 142, 0, 138, 0, 139, 142, 140, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1960.0, 108.0, 1856.0, 104.0, 1717.0, 139.0, 1616.0, 101.0, 1518.0, 98.0, 1419.0, 99.0, 1274.0, 145.0, 934.0, 340.0, 828.0, 106.0, 198.0, 142.0, 736.0, 92.0, 102.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021957203, 0.0025992258, -0.009562599, 0.009617475, -0.0078920415, -0.004604692, 0.13856514, 0.004893275, -0.016544392, 0.025113238, 0.0023468256, -0.0034220035, 0.010800275, 0.0074507357, -0.008483812, -0.008318937, 0.015296767, 0.008566429, -0.06778773, -0.0083001675, 0.011492359, -0.00040393192, -0.015789364, 0.00072451454, -0.012309636], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [0.926898, 1.1259419, 0.0, 3.3230548, 0.0, 2.4931183, 2.3321033, 1.3212291, 0.0, 0.0, 0.0, 1.2623186, 0.0, 2.8868017, 0.0, 1.1397132, 0.0, 1.5857933, 1.4417707, 1.3616066, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.6397896, -0.009562599, 0.9476538, -0.0078920415, 0.8922892, 1.0, 0.78377116, -0.016544392, 0.025113238, 0.0023468256, 1.4242606, 0.010800275, 0.61693144, -0.008483812, 1.3325276, 0.015296767, 0.44610116, 0.47795698, 0.4564035, 0.011492359, -0.00040393192, -0.015789364, 0.00072451454, -0.012309636], "split_indices": [117, 138, 0, 140, 0, 141, 50, 142, 0, 0, 0, 138, 0, 141, 0, 138, 0, 141, 140, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1968.0, 101.0, 1812.0, 156.0, 1632.0, 180.0, 1541.0, 91.0, 91.0, 89.0, 1426.0, 115.0, 1258.0, 168.0, 1135.0, 123.0, 884.0, 251.0, 763.0, 121.0, 147.0, 104.0, 672.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.00788853, 0.0022885706, 0.011178109, 0.009972605, -0.015451635, 0.0010354167, 0.018751133, 0.009669774, -0.011245456, -0.0014640756, 0.07444015, 0.013033941, -0.08910788, -0.0015951963, 0.021829, 0.04976434, -0.017902838, 0.00018987745, -0.01774114, -0.0056808265, 0.1208538, 0.015062753, -0.01161966, 0.010708043, -0.060435105, 0.022531036, 0.0018965822, -0.012121565, 0.0126348585, -0.00043551205, -0.013541842, 0.00664353, -0.004501904], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, 23, 25, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.1944232, 2.3471344, 0.0, 2.9464958, 0.0, 1.732486, 0.0, 1.1848392, 0.0, 1.7814674, 3.133698, 1.3669934, 1.599206, 0.0, 0.0, 2.1678622, 2.115924, 0.0, 0.0, 1.9078157, 2.5649304, 1.4793376, 0.0, 0.0, 0.87465274, 0.0, 0.0, 1.0156386, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 24, 24, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, 24, 26, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.3494815, 1.6290085, 0.011178109, 0.99982387, -0.015451635, 1.4922987, 0.018751133, 1.0, -0.011245456, 2.0, 0.5338557, 1.0, 0.4534852, -0.0015951963, 0.021829, 0.47496155, 0.49731833, 0.00018987745, -0.01774114, 0.07692308, 1.0, 0.44164315, -0.01161966, 0.010708043, 0.32404217, 0.022531036, 0.0018965822, 1.2024587, 0.0126348585, -0.00043551205, -0.013541842, 0.00664353, -0.004501904], "split_indices": [140, 138, 0, 139, 0, 138, 0, 42, 0, 0, 143, 122, 141, 0, 0, 140, 140, 0, 0, 1, 109, 142, 0, 0, 141, 0, 0, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1948.0, 105.0, 1857.0, 91.0, 1768.0, 89.0, 1643.0, 125.0, 1402.0, 241.0, 1203.0, 199.0, 148.0, 93.0, 550.0, 653.0, 98.0, 101.0, 309.0, 241.0, 489.0, 164.0, 101.0, 208.0, 119.0, 122.0, 393.0, 96.0, 119.0, 89.0, 116.0, 277.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012612263, 0.0094402, -0.08481468, -0.005002692, 0.13031611, 0.0041489857, -0.026637748, 0.005716262, -0.018442867, 0.022817163, 0.0018481218, 0.052905794, -0.010729824, -0.009832795, 0.023497073, 0.0009815445, -0.014762275, 0.043250587, -0.011708292, 0.015490219, -0.010759948, -0.0056289556, 0.01626988, 0.035626743, -0.05780672, -0.0060980767, 0.14887984, -0.015519164, 0.0043553053, 0.048911463, -0.064960435, 0.025575427, 0.002039033, -0.0020969051, 0.016560517, -0.01127465, 0.002074279], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1, 25, 27, 29, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8428253, 3.189573, 5.3661375, 3.138759, 2.134013, 0.0, 0.0, 1.1951681, 0.0, 0.0, 0.0, 4.5461545, 1.830859, 1.6851875, 0.0, 1.6572853, 0.0, 2.354199, 0.0, 1.3696775, 0.0, 0.0, 0.0, 3.440139, 1.9741828, 1.7226114, 2.6915188, 0.0, 0.0, 2.24252, 1.0525229, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 23, 23, 24, 24, 25, 25, 26, 26, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1, 26, 28, 30, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.9782575, 1.4839479, 0.85589015, 1.1373166, 0.0041489857, -0.026637748, 0.0, -0.018442867, 0.022817163, 0.0018481218, 0.56483954, 0.81041974, 0.40983817, 0.023497073, 1.0, -0.014762275, 1.2596997, -0.011708292, 0.56263477, -0.010759948, -0.0056289556, 0.01626988, 0.44164315, 0.61693144, 1.2539469, 0.5030815, -0.015519164, 0.0043553053, 0.2891638, 1.0, 0.025575427, 0.002039033, -0.0020969051, 0.016560517, -0.01127465, 0.002074279], "split_indices": [119, 141, 138, 141, 142, 0, 0, 0, 0, 0, 0, 140, 139, 142, 0, 40, 0, 138, 0, 140, 0, 0, 0, 142, 141, 138, 141, 0, 0, 140, 80, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1827.0, 234.0, 1632.0, 195.0, 138.0, 96.0, 1540.0, 92.0, 104.0, 91.0, 398.0, 1142.0, 296.0, 102.0, 1052.0, 90.0, 198.0, 98.0, 928.0, 124.0, 108.0, 90.0, 728.0, 200.0, 532.0, 196.0, 102.0, 98.0, 275.0, 257.0, 107.0, 89.0, 172.0, 103.0, 165.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.005349405, 0.010225914, -0.008265142, 0.02062437, -0.032364644, 0.031042548, -0.008024892, 0.008019594, -0.01240848, 0.014766907, 0.02075189, -0.004111276, 0.0090561975, -0.02171002, 0.06152082, -0.0873766, 0.063861735, 0.023330873, 0.02837501, -0.037303038, -0.020845609, -0.02709552, 0.030365815, 0.069003984, -0.010584567, 0.0047757397, -0.08500891, 0.008859473, -0.010176088, 0.1283844, -0.0041931495, -0.013427637, -0.0036260027, 0.022685839, 0.0030724222], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, 15, 17, 19, 21, -1, 23, 25, -1, 27, -1, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.89088786, 0.87113714, 0.0, 1.6614978, 1.4297633, 4.1159563, 0.0, 1.0868745, 0.0, 2.2375362, 0.0, 0.0, 0.0, 4.141354, 3.2740777, 2.5282211, 6.9795938, 0.0, 2.6284657, 1.1970755, 0.0, 2.004029, 0.0, 2.4373362, 0.0, 0.0, 0.45392692, 0.0, 0.0, 2.3176923, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 23, 23, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, 16, 18, 20, 22, -1, 24, 26, -1, 28, -1, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008265142, 1.3461539, 0.37346566, 0.84615386, -0.008024892, 0.21544637, -0.01240848, 1.0, 0.02075189, -0.004111276, 0.0090561975, 1.0, 1.270702, 0.8509382, 0.68515503, 0.023330873, 1.0, -0.34615386, -0.020845609, 1.0, 0.030365815, 1.0, -0.010584567, 0.0047757397, 0.5338557, 0.008859473, -0.010176088, 1.0, -0.0041931495, -0.013427637, -0.0036260027, 0.022685839, 0.0030724222], "split_indices": [43, 80, 0, 1, 140, 1, 0, 142, 0, 124, 0, 0, 0, 115, 138, 140, 142, 0, 115, 1, 0, 127, 0, 97, 0, 0, 143, 0, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1967.0, 109.0, 1581.0, 386.0, 1433.0, 148.0, 268.0, 118.0, 1312.0, 121.0, 168.0, 100.0, 737.0, 575.0, 417.0, 320.0, 93.0, 482.0, 295.0, 122.0, 232.0, 88.0, 370.0, 112.0, 106.0, 189.0, 91.0, 141.0, 241.0, 129.0, 94.0, 95.0, 120.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0023230682, 0.0066111996, -0.008396461, -0.0019256129, 0.009888036, 0.0069018775, -0.013794924, 0.00014643591, 0.012460806, 0.008288724, -0.013056325, -0.0041917404, 0.016547514, 0.005127285, -0.01278115, 0.05395597, -0.017127218, 0.013833108, 0.019940129, 0.005712833, -0.029100807, -0.003997615, 0.013882756, -0.01052312, -0.0008941277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, 23, -1, -1, -1, -1], "loss_changes": [0.76592636, 1.553314, 0.0, 2.1673493, 0.0, 1.3477911, 0.0, 1.7060338, 0.0, 2.9602957, 0.0, 1.6105182, 0.0, 1.4126557, 0.0, 2.3751225, 0.79397106, 2.1455493, 0.0, 0.0, 1.1802248, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, 24, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008396461, 1.1106075, 0.009888036, 0.9548015, -0.013794924, 1.5248721, 0.012460806, 0.761162, -0.013056325, 0.7149361, 0.016547514, 1.0, -0.01278115, 1.0, 0.13452224, 0.48429644, 0.019940129, 0.005712833, 0.23527001, -0.003997615, 0.013882756, -0.01052312, -0.0008941277], "split_indices": [52, 125, 0, 143, 0, 142, 0, 138, 0, 139, 0, 142, 0, 53, 0, 83, 143, 139, 0, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1972.0, 98.0, 1805.0, 167.0, 1695.0, 110.0, 1603.0, 92.0, 1509.0, 94.0, 1398.0, 111.0, 1300.0, 98.0, 407.0, 893.0, 319.0, 88.0, 124.0, 769.0, 223.0, 96.0, 161.0, 608.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.00040529185, 0.0060816663, -0.011068133, 0.010993751, -0.008055812, 0.015738962, -0.007676765, 0.008717603, 0.014733023, 0.018296968, -0.012064545, 0.009801847, 0.014042484, 0.020169774, -0.014170365, 0.030962175, -0.032806817, 0.019553239, 0.015372704, -0.010595338, 0.0052760835, 0.003223737, -0.011334822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1], "loss_changes": [1.2970808, 0.83286357, 0.0, 0.7712584, 0.0, 1.6233786, 0.0, 2.067012, 0.0, 1.6112232, 0.0, 2.2807984, 0.0, 0.7770008, 0.0, 1.5812955, 1.4395655, 1.7413685, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.011068133, 1.0, -0.008055812, 1.2520225, -0.007676765, 1.0723042, 0.014733023, 0.90201825, -0.012064545, 0.9144915, 0.014042484, 1.0, -0.014170365, 0.761162, 1.0, 0.6592616, 0.015372704, -0.010595338, 0.0052760835, 0.003223737, -0.011334822], "split_indices": [117, 43, 0, 52, 0, 142, 0, 141, 0, 141, 0, 139, 0, 113, 0, 139, 109, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1957.0, 100.0, 1852.0, 105.0, 1757.0, 95.0, 1668.0, 89.0, 1553.0, 115.0, 1452.0, 101.0, 1359.0, 93.0, 1129.0, 230.0, 1033.0, 96.0, 124.0, 106.0, 943.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.004496066, 0.00893691, -0.0076725692, -0.00029207207, 0.011474691, 0.008541982, -0.009338943, 0.00017820584, 0.0148028135, 0.008581498, -0.008451494, 0.0017257615, 0.008658051, 0.013376784, -0.0075513646, -0.0014700277, 0.014927195, 0.012661241, -0.008369695, -0.00041150648, 0.008403681], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [0.74447054, 1.9110473, 0.0, 1.480369, 0.0, 1.917941, 0.0, 1.1038486, 0.0, 0.7545188, 0.0, 1.1671937, 0.0, 2.2738469, 0.0, 1.1805625, 0.0, 1.038162, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.6878304, 0.99982387, -0.0076725692, 1.486252, 0.011474691, 0.76394844, -0.009338943, 0.7457551, 0.0148028135, 0.62643254, -0.008451494, 0.5458753, 0.008658051, 0.5325338, -0.0075513646, 0.50325376, 0.014927195, 1.0, -0.008369695, -0.00041150648, 0.008403681], "split_indices": [138, 139, 0, 138, 0, 139, 0, 140, 0, 140, 0, 141, 0, 139, 0, 143, 0, 62, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1957.0, 107.0, 1800.0, 157.0, 1644.0, 156.0, 1551.0, 93.0, 1411.0, 140.0, 1297.0, 114.0, 1127.0, 170.0, 1016.0, 111.0, 867.0, 149.0, 702.0, 165.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.0031634031, -0.004678563, 0.044535726, 0.0060556848, -0.020631168, 0.014722027, -0.033987727, -0.0032646738, 0.010013503, -0.019849464, 0.012195108, 0.0687138, -0.01887166, -0.009506996, 0.023999913, -0.0026067377, -0.014934718, 0.048828114, -0.042787615, -0.011454985, 0.016790586, -0.013908924, -0.014492474, 0.008542793, -0.009693992, 0.006710963, -0.04552022, -0.0763433, 0.004348572, -0.0112166535, -0.0016264723], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, -1, -1, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1], "loss_changes": [0.6719161, 3.768186, 2.6608381, 1.4494386, 0.0, 0.0, 4.797114, 1.6895437, 0.0, 0.0, 0.0, 7.5184064, 2.6230068, 0.0, 0.0, 2.2713006, 0.0, 3.4599771, 1.819894, 2.6502497, 0.0, 1.2318898, 0.0, 0.0, 0.0, 0.0, 0.94922936, 0.55311716, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, -1, -1, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1], "split_conditions": [0.90107125, 0.99389625, 1.0, 0.8141592, -0.020631168, 0.014722027, 1.0675184, 1.0, 0.010013503, -0.019849464, 0.012195108, 1.0, 0.66754913, -0.009506996, 0.023999913, 1.0, -0.014934718, 0.45939127, 0.4512219, 0.28287697, 0.016790586, 1.204987, -0.014492474, 0.008542793, -0.009693992, 0.006710963, 0.36250836, 0.84615386, 0.004348572, -0.0112166535, -0.0016264723], "split_indices": [142, 140, 39, 143, 0, 0, 140, 89, 0, 0, 0, 97, 143, 0, 0, 97, 0, 140, 142, 142, 0, 138, 0, 0, 0, 0, 143, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1741.0, 330.0, 1653.0, 88.0, 143.0, 187.0, 1504.0, 149.0, 91.0, 96.0, 268.0, 1236.0, 137.0, 131.0, 1099.0, 137.0, 482.0, 617.0, 320.0, 162.0, 481.0, 136.0, 150.0, 170.0, 135.0, 346.0, 257.0, 89.0, 161.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.003539128, 0.015306691, -0.020488298, -0.016305547, 0.0859143, 0.0012139467, -0.015084091, 0.025533147, -0.025027731, 0.023868896, -0.0047999276, -0.02866373, 0.01698851, 0.12142002, -0.021295317, -0.017631823, -0.008413965, 0.0029012868, 0.020906387, -0.013237146, 0.06481416, 0.008929527, -0.036665075, -0.0009790139, 0.016893227, -0.05925966, 0.008015917, -0.01459867, -0.014010757, -0.0075222715, 0.0045985053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, -1, 21, -1, -1, -1, 23, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1], "loss_changes": [0.663118, 2.1941195, 3.0920367, 6.6467814, 6.2194147, 4.7220144, 0.0, 2.5863757, 0.0, 0.0, 0.0, 2.3800144, 0.0, 1.530695, 3.7015424, 0.0, 1.9322759, 0.0, 0.0, 0.0, 1.69335, 0.0, 1.4333, 0.0, 0.0, 1.7855579, 0.0, 0.0, 1.098066, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 16, 16, 20, 20, 22, 22, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, -1, 22, -1, -1, -1, 24, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1], "split_conditions": [1.0, 0.7869461, 0.8037921, 0.65096307, 1.0, 0.62448454, -0.015084091, -0.26923078, -0.025027731, 0.023868896, -0.0047999276, -0.34615386, 0.01698851, 1.0, 0.26923078, -0.017631823, -0.03846154, 0.0029012868, 0.020906387, -0.013237146, 0.33666655, 0.008929527, 3.1923077, -0.0009790139, 0.016893227, 1.0, 0.008015917, -0.01459867, 1.0, -0.0075222715, 0.0045985053], "split_indices": [126, 141, 141, 141, 39, 141, 0, 1, 0, 0, 0, 1, 0, 109, 1, 0, 1, 0, 0, 0, 139, 0, 1, 0, 0, 53, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 983.0, 1093.0, 679.0, 304.0, 937.0, 156.0, 576.0, 103.0, 142.0, 162.0, 796.0, 141.0, 189.0, 387.0, 96.0, 700.0, 92.0, 97.0, 169.0, 218.0, 157.0, 543.0, 127.0, 91.0, 455.0, 88.0, 156.0, 299.0, 148.0, 151.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.002705976, -0.033671554, 0.016939117, -0.058062017, 0.0131378425, 0.10467357, 0.002045416, -0.009905454, -0.023219103, -0.00015243381, 0.021931907, 0.022046903, -0.09840896, 0.05424233, -0.08989221, -0.03205163, 0.082652576, -0.004864602, -0.014653133, -0.0023643407, 0.013689661, -0.018082527, -0.00026257953, -0.0049035843, -0.014064378, 0.028499166, 0.02247474, -0.043985963, 0.0069382745, 0.013230301, -0.00656335, -0.0011396542, -0.009420024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.2537509, 3.220516, 1.6477368, 5.8446617, 0.0, 2.2280462, 2.1659572, 2.8015118, 0.0, 0.0, 0.0, 2.9475317, 0.42865252, 1.9505899, 1.928302, 1.4003301, 3.2626455, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1032488, 0.0, 2.9997997, 0.0, 0.40747684, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.1923077, 1.2135563, -0.03846154, 1.486252, 0.0131378425, 1.0, 1.0, 1.0, -0.023219103, -0.00015243381, 0.021931907, 1.0, 1.0, 0.45939127, 1.0, 0.46906352, 0.67845887, -0.004864602, -0.014653133, -0.0023643407, 0.013689661, -0.018082527, -0.00026257953, 0.32365677, -0.014064378, 1.2861747, 0.02247474, 0.23518711, 0.0069382745, 0.013230301, -0.00656335, -0.0011396542, -0.009420024], "split_indices": [1, 143, 1, 138, 0, 124, 64, 126, 0, 0, 0, 39, 59, 140, 97, 141, 139, 0, 0, 0, 0, 0, 0, 140, 0, 138, 0, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 800.0, 1261.0, 697.0, 103.0, 183.0, 1078.0, 546.0, 151.0, 95.0, 88.0, 899.0, 179.0, 303.0, 243.0, 475.0, 424.0, 88.0, 91.0, 156.0, 147.0, 119.0, 124.0, 380.0, 95.0, 307.0, 117.0, 249.0, 131.0, 146.0, 161.0, 151.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0019494253, 0.0073976195, -0.011102888, 0.00023385024, 0.011135686, -0.006028434, 0.012365012, 0.0019360167, -0.013917155, -0.0056179753, 0.010501795, 0.0055179964, -0.018961463, -0.008425465, 0.017588176, 0.011432362, -0.05613453, -0.0012927321, 0.0128558, -0.12216226, 0.0029471177, 0.0014669551, -0.012061478, -0.0043655797, -0.023817733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1], "loss_changes": [1.2710645, 1.4671376, 0.0, 1.4243957, 0.0, 1.8599625, 0.0, 1.288716, 0.0, 3.1595302, 0.0, 3.4539201, 0.0, 1.2733035, 0.0, 1.414423, 2.2326787, 1.6303827, 0.0, 2.0310688, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1314709, -0.011102888, 1.0, 0.011135686, 1.5270969, 0.012365012, 1.4726204, -0.013917155, 0.83346164, 0.010501795, 0.68852574, -0.018961463, 1.3325276, 0.017588176, 0.45099154, 0.5871718, 0.45463473, 0.0128558, 0.4802205, 0.0029471177, 0.0014669551, -0.012061478, -0.0043655797, -0.023817733], "split_indices": [143, 139, 0, 102, 0, 138, 0, 138, 0, 142, 0, 142, 0, 138, 0, 139, 143, 142, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1970.0, 95.0, 1843.0, 127.0, 1754.0, 89.0, 1655.0, 99.0, 1542.0, 113.0, 1454.0, 88.0, 1344.0, 110.0, 949.0, 395.0, 856.0, 93.0, 223.0, 172.0, 755.0, 101.0, 133.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.00010893252, -0.027911896, 0.017314548, -0.0045226016, -0.015191226, 0.10822132, 0.0017713355, -0.032614924, 0.014441967, 0.0031129026, 0.018959647, 0.02062478, -0.095281035, -0.00064074754, -0.014325569, -0.026004242, 0.07247104, -0.005827817, -0.013228387, -0.045060225, 0.0075982832, 0.00038715612, -0.013074513, 0.024051566, 0.01915673, 0.0075720907, -0.10254431, 0.0070844344, -0.04054232, 0.0800371, -0.0099705905, -0.01691741, -0.003938479, -0.008071799, 0.00268635, -0.0010326864, 0.020531446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, 21, 23, -1, -1, 25, -1, 27, -1, 29, -1, -1, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.99839866, 2.3028228, 1.7902496, 2.7950015, 0.0, 1.1605778, 1.9798125, 1.9881575, 0.0, 0.0, 0.0, 2.1902916, 0.2409805, 1.4839605, 0.0, 1.3185517, 2.4738615, 0.0, 0.0, 1.9162662, 0.0, 1.0987186, 0.0, 2.1132317, 0.0, 0.0, 0.7869525, 0.0, 0.65264606, 2.377318, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 26, 26, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, 22, 24, -1, -1, 26, -1, 28, -1, 30, -1, -1, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.1923077, 1.0558407, -0.03846154, 0.8711886, -0.015191226, 1.0, 1.0, 0.6592616, 0.014441967, 0.0031129026, 0.018959647, 1.0, 1.0, 0.4534852, -0.014325569, 0.46906352, 0.6720697, -0.005827817, -0.013228387, 0.2439953, 0.0075982832, 1.0, -0.013074513, 0.50101894, 0.01915673, 0.0075720907, 1.0, 0.0070844344, 0.30967447, 0.2960337, -0.0099705905, -0.01691741, -0.003938479, -0.008071799, 0.00268635, -0.0010326864, 0.020531446], "split_indices": [1, 141, 1, 139, 0, 124, 64, 139, 0, 0, 0, 39, 59, 141, 0, 141, 139, 0, 0, 140, 0, 124, 0, 140, 0, 0, 5, 0, 141, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 794.0, 1267.0, 668.0, 126.0, 185.0, 1082.0, 562.0, 106.0, 95.0, 90.0, 906.0, 176.0, 436.0, 126.0, 477.0, 429.0, 88.0, 88.0, 276.0, 160.0, 381.0, 96.0, 305.0, 124.0, 89.0, 187.0, 140.0, 241.0, 210.0, 95.0, 91.0, 96.0, 151.0, 90.0, 122.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0059205503, 0.0029840241, -0.08313535, -0.008479774, 0.018400785, -0.019097732, -0.00042265674, -0.0013030781, -0.014136957, -0.010947573, 0.061961252, 0.0046764803, -0.07656861, -0.0030476083, 0.019851645, -0.011160394, 0.018984612, -0.017493842, -0.0012952191, -0.0024263917, -0.010568167, -0.014422092, 0.011397141, 0.0022767056, -0.010614271, -0.0024370416, 0.006217481], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [1.4163841, 3.832933, 1.8125609, 1.6565931, 0.0, 0.0, 0.0, 1.0055313, 0.0, 1.4661314, 2.7517705, 3.3870478, 1.7209315, 0.0, 0.0, 0.87838393, 0.0, 0.0, 0.0, 1.3599696, 0.0, 1.3524244, 0.0, 1.1922958, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0558407, 1.0, 1.1503603, 1.0078171, 0.018400785, -0.019097732, -0.00042265674, 1.4491278, -0.014136957, 0.54411215, 0.7977335, 0.4967865, 1.0, -0.0030476083, 0.019851645, 1.3507949, 0.018984612, -0.017493842, -0.0012952191, 0.54883325, -0.010568167, 0.37509307, 0.011397141, 0.88461536, -0.010614271, -0.0024370416, 0.006217481], "split_indices": [141, 125, 140, 140, 0, 0, 0, 138, 0, 139, 142, 139, 59, 0, 0, 138, 0, 0, 0, 140, 0, 139, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1847.0, 213.0, 1737.0, 110.0, 90.0, 123.0, 1648.0, 89.0, 1430.0, 218.0, 1155.0, 275.0, 130.0, 88.0, 1064.0, 91.0, 108.0, 167.0, 974.0, 90.0, 883.0, 91.0, 747.0, 136.0, 517.0, 230.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.00013385442, 0.004749726, -0.010171236, -0.0020240129, 0.012920061, -0.009950388, 0.010609667, 0.003994575, -0.018045926, -0.00390593, 0.010061389, 0.0037258982, -0.008884883, 0.052604288, -0.01210994, -0.0029417789, 0.020613614, 0.02897078, -0.036682185, -0.006136944, 0.0060437354, 0.07176035, -0.008594975, -0.0028942137, -0.01378375, 0.014117076, -0.002470833, -0.0037565865, 0.01289267], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [1.0273546, 1.6657641, 0.0, 1.6060271, 0.0, 4.1515346, 0.0, 1.2320334, 0.0, 0.9672185, 0.0, 1.0596476, 0.0, 2.8569107, 1.0437667, 0.9109612, 0.0, 1.903034, 2.2113376, 0.0, 0.0, 1.8882527, 0.0, 2.2166672, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1144832, -0.010171236, 0.9677354, 0.012920061, 0.83346164, 0.010609667, 0.8141592, -0.018045926, 0.6940378, 0.010061389, 0.0, -0.008884883, 0.49346045, 1.0, 1.0, 0.020613614, 1.0, 0.49346045, -0.006136944, 0.0060437354, 1.0, -0.008594975, 0.36250836, -0.01378375, 0.014117076, -0.002470833, -0.0037565865, 0.01289267], "split_indices": [143, 142, 0, 141, 0, 142, 0, 143, 0, 143, 0, 0, 0, 143, 53, 13, 0, 113, 143, 0, 0, 116, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1976.0, 95.0, 1874.0, 102.0, 1746.0, 128.0, 1614.0, 132.0, 1492.0, 122.0, 1369.0, 123.0, 335.0, 1034.0, 246.0, 89.0, 387.0, 647.0, 128.0, 118.0, 282.0, 105.0, 485.0, 162.0, 164.0, 118.0, 384.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0007292585, 0.0097425245, -0.0041474095, -0.095592335, 0.005367387, 0.0006687939, -0.018838271, 0.0128563745, -0.013845518, -0.00061039, 0.022927685, -0.016099457, 0.06339443, -0.0020152815, -0.01981221, -0.013158294, 0.024479112, -0.013968494, 0.0126748, 0.007518063, -0.010999138, 0.0056457724, -0.07458101, -0.015879156, 0.010872291, -0.015291779, 0.000861384, -0.0065525933, 0.0017831611], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, 5, 7, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1], "loss_changes": [0.9723451, 0.0, 1.707967, 1.7557664, 1.9150577, 0.0, 0.0, 4.925478, 0.0, 1.5772773, 0.0, 3.2840207, 4.3047876, 1.8300309, 0.0, 1.8648006, 0.0, 1.2934909, 0.0, 0.0, 0.0, 1.8237948, 1.7335802, 1.1380692, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21, 22, 22, 23, 23], "right_children": [2, -1, 4, 6, 8, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0097425245, -0.46153846, 0.67845887, 1.1373166, 0.0006687939, -0.018838271, 1.562096, -0.013845518, 1.0, 0.022927685, 0.8730146, 0.5653381, 0.7189062, -0.01981221, 0.30769083, 0.024479112, 0.5139595, 0.0126748, 0.007518063, -0.010999138, 0.42792067, 1.0, 0.15384616, 0.010872291, -0.015291779, 0.000861384, -0.0065525933, 0.0017831611], "split_indices": [1, 0, 1, 139, 142, 0, 0, 138, 0, 61, 0, 141, 142, 141, 0, 140, 0, 140, 0, 0, 0, 140, 71, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 99.0, 1963.0, 185.0, 1778.0, 88.0, 97.0, 1690.0, 88.0, 1591.0, 99.0, 1281.0, 310.0, 1189.0, 92.0, 218.0, 92.0, 1088.0, 101.0, 114.0, 104.0, 822.0, 266.0, 680.0, 142.0, 137.0, 129.0, 275.0, 405.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0008368677, -0.003447695, 0.008976436, 0.0032874004, -0.010902178, -0.0054848604, 0.014921061, 0.0014105209, -0.009479119, 0.024983343, -0.023802673, 0.013157059, 0.0073185954, -0.006679434, -0.012531899, -0.00989415, 0.02994639, -0.02938802, 0.006255404, -0.0028618965, 0.013874449, -0.010122182, -0.011597839, -0.06850908, 0.08731659, 0.0057382714, -0.050466917, 0.0020901328, -0.020348445, -0.00074675644, 0.018411746, -0.009418389, 0.0017731522], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, -1, 15, 17, -1, -1, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7791768, 1.3872617, 0.0, 2.3476608, 0.0, 1.0653365, 0.0, 0.9545199, 0.0, 1.5627542, 1.3489113, 0.0, 1.7119553, 1.043937, 0.0, 0.0, 2.0952847, 0.8341176, 0.0, 2.6699038, 0.0, 1.1138982, 0.0, 3.1498015, 1.7432866, 0.0, 0.76324606, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, -1, 16, 18, -1, -1, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2520225, 1.1962737, 0.008976436, 1.562096, -0.010902178, 0.86804646, 0.014921061, 1.0, -0.009479119, 0.32961476, 0.50570595, 0.013157059, -1.0, 1.0, -0.012531899, -0.00989415, 0.70670706, 1.0, 0.006255404, 1.0, 0.013874449, 1.2157785, -0.011597839, 0.5369919, 0.5338557, 0.0057382714, 1.0, 0.0020901328, -0.020348445, -0.00074675644, 0.018411746, -0.009418389, 0.0017731522], "split_indices": [142, 140, 0, 138, 0, 142, 0, 137, 0, 141, 140, 0, 0, 93, 0, 0, 141, 0, 0, 105, 0, 138, 0, 141, 143, 0, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1951.0, 94.0, 1834.0, 117.0, 1730.0, 104.0, 1606.0, 124.0, 830.0, 776.0, 118.0, 712.0, 664.0, 112.0, 125.0, 587.0, 500.0, 164.0, 451.0, 136.0, 409.0, 91.0, 261.0, 190.0, 153.0, 256.0, 157.0, 104.0, 96.0, 94.0, 156.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020168747, -0.009896078, 0.03864543, 0.0005435747, -0.018802268, -0.0058938954, 0.097943135, -0.007964754, 0.01342459, 0.027843133, -0.0035918911, 0.0050532063, -0.07989451, -0.0052233045, 0.014705594, -0.02211511, 0.0029499666, -0.045585234, 0.0129131, 0.0058842953, -0.07833276, -0.0019082961, 0.011652175, -0.033914216, -0.017555997, 0.019309118, -0.066732794, 0.004171674, -0.012569115, -0.014779337, 0.012383036, 0.0026567238, -0.015900759, -0.0037978247, 0.0037599427], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.66320014, 3.224512, 1.9442742, 1.8633618, 0.0, 0.0, 5.049549, 1.4420234, 0.0, 0.0, 0.0, 1.902918, 3.6468258, 0.8901368, 0.0, 0.0, 0.0, 1.2892513, 1.2883897, 0.0, 1.2394636, 1.0095499, 0.0, 1.3674121, 0.0, 1.9703215, 1.5582728, 0.0, 0.0, 0.5067092, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 20, 20, 21, 21, 23, 23, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.90107125, 0.97098887, 0.99982387, 0.78377116, -0.018802268, -0.0058938954, 1.1373166, 0.6368173, 0.01342459, 0.027843133, -0.0035918911, 0.65205914, 1.0, -0.15384616, 0.014705594, -0.02211511, 0.0029499666, 0.23658822, 0.5132328, 0.0058842953, 1.0, 0.39592648, 0.011652175, 0.48244315, -0.017555997, 0.33186364, 0.40983817, 0.004171674, -0.012569115, 1.0, 0.012383036, 0.0026567238, -0.015900759, -0.0037978247, 0.0037599427], "split_indices": [142, 140, 139, 142, 0, 0, 142, 139, 0, 0, 0, 140, 39, 1, 0, 0, 0, 140, 142, 0, 83, 141, 0, 142, 0, 140, 142, 0, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1734.0, 336.0, 1638.0, 96.0, 127.0, 209.0, 1540.0, 98.0, 89.0, 120.0, 1304.0, 236.0, 1216.0, 88.0, 103.0, 133.0, 377.0, 839.0, 90.0, 287.0, 734.0, 105.0, 197.0, 90.0, 553.0, 181.0, 108.0, 89.0, 417.0, 136.0, 90.0, 91.0, 289.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008195624, 0.003950337, -0.010093673, -0.0026402227, 0.01039432, -0.010495544, 0.0079258755, -0.002952976, -0.01118367, 0.0050442736, -0.010149538, -0.009267157, 0.08797033, 0.0007830515, -0.013616487, 0.017974125, 0.0004393217, -0.009859905, 0.012023329, -0.018967142, 0.008546909, -0.0057860347, 0.0013617041], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [0.98709375, 1.3002248, 0.0, 1.1908275, 0.0, 1.2910249, 0.0, 1.2388434, 0.0, 1.7255937, 0.0, 1.5814326, 1.6413684, 1.4607282, 0.0, 0.0, 0.0, 0.9159337, 0.0, 1.2204133, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.4310507, 1.1314709, -0.010093673, 0.9271665, 0.01039432, 0.8256487, 0.0079258755, 0.7425969, -0.01118367, 0.5981277, -0.010149538, 0.54254174, 1.0, 0.49214, -0.013616487, 0.017974125, 0.0004393217, 0.54883325, 0.012023329, 1.0, 0.008546909, -0.0057860347, 0.0013617041], "split_indices": [143, 139, 0, 140, 0, 139, 0, 140, 0, 139, 0, 139, 53, 139, 0, 0, 0, 140, 0, 106, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1973.0, 94.0, 1851.0, 122.0, 1689.0, 162.0, 1572.0, 117.0, 1454.0, 118.0, 1240.0, 214.0, 1149.0, 91.0, 102.0, 112.0, 1055.0, 94.0, 963.0, 92.0, 439.0, 524.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.003300274, 0.0022982855, -0.011043391, -0.005136233, 0.061168917, 0.00406924, -0.009853954, -0.005308676, 0.020993937, -0.0017390789, 0.01010682, 0.010037837, -0.08616166, -0.0020472317, 0.009948275, -0.00055964565, -0.016672684, -0.018761806, 0.032893334, 0.023023233, -0.050947588, -0.03210647, 0.010835289, -0.030729257, 0.0129126, -0.0071886764, -0.011884932, 0.008364624, -0.014558956, 0.0068642534, -0.010487593, -0.010568122, 0.0059492593], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2439731, 0.8626572, 0.0, 1.5046881, 3.756529, 0.89749783, 0.0, 0.0, 0.0, 1.4943392, 0.0, 1.4257708, 1.1942973, 0.67862743, 0.0, 0.0, 0.0, 1.0570791, 1.8442264, 1.9505248, 1.3192594, 2.6534672, 0.0, 1.672556, 0.0, 1.7732543, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.6878304, 0.9476538, -0.011043391, 1.4922987, -0.07692308, 1.4491278, -0.009853954, -0.005308676, 0.020993937, 0.6151461, 0.01010682, 0.5325338, 1.0, 1.0, 0.009948275, -0.00055964565, -0.016672684, 0.24274075, 0.15384616, 0.18234658, 1.0, -0.23076923, 0.010835289, 0.102340475, 0.0129126, 0.33666655, -0.011884932, 0.008364624, -0.014558956, 0.0068642534, -0.010487593, -0.010568122, 0.0059492593], "split_indices": [138, 140, 0, 138, 1, 138, 0, 0, 0, 139, 0, 139, 109, 93, 0, 0, 0, 139, 1, 139, 12, 1, 0, 139, 0, 139, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1971.0, 103.0, 1750.0, 221.0, 1593.0, 157.0, 125.0, 96.0, 1503.0, 90.0, 1319.0, 184.0, 1162.0, 157.0, 92.0, 92.0, 786.0, 376.0, 342.0, 444.0, 202.0, 174.0, 227.0, 115.0, 270.0, 174.0, 100.0, 102.0, 97.0, 130.0, 109.0, 161.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003938533, 0.008626443, -0.009376736, 0.0033142576, 0.01176417, 0.010720734, -0.014465064, -0.00038600882, 0.018282432, 0.017248953, -0.059517678, -3.1941625e-05, 0.015290986, 0.005980842, -0.015385205, 0.011591667, -0.0069213435, -0.009447294, 0.010731582, 0.0014943916, 0.009650491, 0.07310532, -0.018403286, 0.017997965, -0.0038316834, -0.01254448, -5.0275525e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [0.9504255, 1.146636, 0.0, 2.0690558, 0.0, 3.4368958, 0.0, 1.7612635, 0.0, 3.0499895, 2.3973594, 0.92797613, 0.0, 2.3311012, 0.0, 0.8471035, 0.0, 0.0, 0.0, 1.258179, 0.0, 2.2863688, 1.3240247, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, 1.2520225, -0.009376736, 1.2521182, 0.01176417, 1.0, -0.014465064, 0.6555352, 0.018282432, 1.4060924, 1.0, 1.3556404, 0.015290986, 1.0, -0.015385205, 1.0, -0.0069213435, -0.009447294, 0.010731582, 0.17711858, 0.009650491, 0.42307693, -0.3846154, 0.017997965, -0.0038316834, -0.01254448, -5.0275525e-05], "split_indices": [52, 142, 0, 140, 0, 125, 0, 141, 0, 138, 105, 138, 0, 69, 0, 42, 0, 0, 0, 140, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1980.0, 95.0, 1888.0, 92.0, 1798.0, 90.0, 1689.0, 109.0, 1301.0, 388.0, 1154.0, 147.0, 229.0, 159.0, 988.0, 166.0, 115.0, 114.0, 883.0, 105.0, 192.0, 691.0, 98.0, 94.0, 99.0, 592.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041472874, 0.005137161, -0.007262682, -0.003281908, 0.013406309, 0.004249579, -0.0081344135, -0.004417635, 0.015083857, 0.003865409, -0.008441144, -0.0054657296, 0.012352357, 0.0045869905, -0.01097518, -0.0070577804, 0.0083741015, 0.024221767, -0.040709835, 0.0111172, -0.0019374447, -0.014000508, -0.00069919176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, -1, -1, -1, -1], "loss_changes": [0.8310941, 2.0894656, 0.0, 1.0623801, 0.0, 2.0938146, 0.0, 1.0309937, 0.0, 1.5743314, 0.0, 1.3712534, 0.0, 1.0996246, 0.0, 1.0947258, 0.0, 2.0431886, 1.6773621, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, -1, -1, -1, -1], "split_conditions": [1.2294465, 0.99982387, -0.007262682, 1.486252, 0.013406309, 0.76394844, -0.0081344135, 0.6592616, 0.015083857, 0.7202307, -0.008441144, 0.575291, 0.012352357, 1.0, -0.01097518, 0.31233892, 0.0083741015, 0.07692308, 0.28427187, 0.0111172, -0.0019374447, -0.014000508, -0.00069919176], "split_indices": [139, 139, 0, 138, 0, 139, 0, 139, 0, 140, 0, 140, 0, 42, 0, 140, 0, 1, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1925.0, 148.0, 1807.0, 118.0, 1648.0, 159.0, 1556.0, 92.0, 1410.0, 146.0, 1308.0, 102.0, 1193.0, 115.0, 1040.0, 153.0, 539.0, 501.0, 180.0, 359.0, 127.0, 374.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025860777, 0.0020281023, -0.008530268, -0.0073614498, 0.075657494, 0.00055029424, -0.01468804, 0.017784035, -0.0022003965, 0.01197478, -0.046575703, -0.0060330564, 0.117770836, 0.007965952, -0.014380216, 0.01112089, -0.041034516, 0.022572836, -0.00045809927, -0.008843527, 0.008192899, -0.004719729, 0.012562105, -0.011925792, 0.010299583, -0.021369055, 0.008509055, -0.004323674, 0.0068839327, 0.008428147, -0.0100118825, -0.0023031866, 0.009337021], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, 19, -1, 21, 23, -1, -1, -1, -1, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.78738403, 1.3508922, 0.0, 1.9129522, 2.205432, 0.88295996, 0.0, 0.0, 0.0, 2.514809, 1.6969256, 0.6772661, 2.536091, 1.461676, 0.0, 1.3730117, 1.4897614, 0.0, 0.0, 0.0, 0.0, 0.9943615, 0.0, 0.0, 0.7020167, 1.3163997, 0.0, 0.0, 0.0, 1.0876175, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 21, 21, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, 20, -1, 22, 24, -1, -1, -1, -1, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.6878304, 0.9476538, -0.008530268, 0.9061094, 1.0, 0.67325324, -0.01468804, 0.017784035, -0.0022003965, 0.56020635, 0.15384616, 0.35274446, 1.0, -0.3846154, -0.014380216, 0.4208829, -0.15384616, 0.022572836, -0.00045809927, -0.008843527, 0.008192899, 1.3846154, 0.012562105, -0.011925792, 1.0, 0.6923077, 0.008509055, -0.004323674, 0.0068839327, 1.0, -0.0100118825, -0.0023031866, 0.009337021], "split_indices": [138, 140, 0, 141, 50, 143, 0, 0, 0, 143, 1, 143, 106, 1, 0, 142, 1, 0, 0, 0, 0, 1, 0, 0, 39, 1, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1954.0, 109.0, 1733.0, 221.0, 1640.0, 93.0, 108.0, 113.0, 1320.0, 320.0, 1128.0, 192.0, 205.0, 115.0, 757.0, 371.0, 102.0, 90.0, 89.0, 116.0, 665.0, 92.0, 147.0, 224.0, 561.0, 104.0, 117.0, 107.0, 407.0, 154.0, 297.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019452357, 0.006411775, -0.07607873, -0.001393834, 0.012444618, -0.0175908, 0.0050878017, 0.0054542073, -0.009632995, -0.0043452526, 0.017226283, 0.0112688355, -0.108936116, 0.0010068001, 0.0132538425, -0.0047315126, -0.016085748, -0.04057145, 0.020772696, 0.0012634584, -0.06934827, -0.023268448, 0.065132216, -0.0022550342, -0.01271335, 0.042157523, -0.103310876, -0.016776534, 0.16212943, -0.0019162277, 0.01375439, -0.020069465, -0.00016929804, 0.004166978, -0.009973262, 0.029356888, 0.0048730667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, 27, -1, -1, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2780998, 1.708147, 2.6488662, 1.1305703, 0.0, 0.0, 0.0, 2.6513777, 0.0, 2.5018954, 0.0, 1.6588819, 0.6366892, 1.0100307, 0.0, 0.0, 0.0, 0.60631585, 1.6273855, 0.0, 0.69498646, 2.1890051, 3.297143, 0.0, 0.0, 1.3452874, 1.860435, 1.0909077, 2.831962, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22, 25, 25, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, 28, -1, -1, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0558407, 1.0, -0.26923078, 0.95287216, 0.012444618, -0.0175908, 0.0050878017, 0.8026405, -0.009632995, 0.67325324, 0.017226283, 0.62324387, -0.03846154, 0.23527001, 0.0132538425, -0.0047315126, -0.016085748, 0.13452224, 0.03846154, 0.0012634584, 1.0, 0.48244315, 1.0, -0.0022550342, -0.01271335, 0.42565113, 1.0, 1.0, 0.96153843, -0.0019162277, 0.01375439, -0.020069465, -0.00016929804, 0.004166978, -0.009973262, 0.029356888, 0.0048730667], "split_indices": [141, 125, 1, 143, 0, 0, 0, 143, 0, 143, 0, 143, 1, 142, 0, 0, 0, 143, 1, 0, 17, 142, 39, 0, 0, 140, 124, 115, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1854.0, 209.0, 1739.0, 115.0, 117.0, 92.0, 1622.0, 117.0, 1532.0, 90.0, 1333.0, 199.0, 1229.0, 104.0, 91.0, 108.0, 396.0, 833.0, 139.0, 257.0, 418.0, 415.0, 142.0, 115.0, 230.0, 188.0, 225.0, 190.0, 140.0, 90.0, 96.0, 92.0, 132.0, 93.0, 88.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0045274314, -0.0012130127, 0.011437616, 0.0059703263, -0.012759916, -0.007835722, 0.014727441, 0.0013990566, -0.012935004, 0.00826048, -0.008202439, 0.00048698214, 0.012511918, 0.009663959, -0.053421978, 0.07074726, -0.003479741, 0.0014735244, -0.013766127, -0.0053733564, 0.020920044, 0.012762915, -0.011637294, -0.0013112868, 0.014457943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, -1, -1, 23, -1, -1, -1], "loss_changes": [1.3078244, 1.7894208, 0.0, 3.6383379, 0.0, 1.9065465, 0.0, 0.9038249, 0.0, 1.3253562, 0.0, 0.67677855, 0.0, 0.9385441, 1.1425622, 3.5675976, 1.7640058, 0.0, 0.0, 0.0, 0.0, 2.86853, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, -1, -1, 24, -1, -1, -1], "split_conditions": [1.3466678, 1.0953796, 0.011437616, 0.9271665, -0.012759916, 1.486252, 0.014727441, 0.7425969, -0.012935004, 0.65205914, -0.008202439, 1.0, 0.012511918, 1.0, 1.0, 1.0, 1.0, 0.0014735244, -0.013766127, -0.0053733564, 0.020920044, 0.62324387, -0.011637294, -0.0013112868, 0.014457943], "split_indices": [139, 139, 0, 140, 0, 138, 0, 140, 0, 140, 0, 40, 0, 89, 12, 97, 64, 0, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1971.0, 103.0, 1865.0, 106.0, 1699.0, 166.0, 1579.0, 120.0, 1459.0, 120.0, 1368.0, 91.0, 1169.0, 199.0, 207.0, 962.0, 110.0, 89.0, 109.0, 98.0, 841.0, 121.0, 703.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031642073, -0.010272933, 0.049215868, -0.00042937687, -0.01778083, -0.011147267, 0.013973032, -0.009668523, 0.008213546, 0.0037155976, -0.08585953, -0.017783381, 0.034473844, -0.0010552006, -0.01956162, 0.0021293534, -0.014750617, -0.0016853339, 0.089301266, -0.01446318, 0.009157104, -0.0065545873, 0.0070522963, -0.0027426097, 0.022607274, 0.004518666, -0.009780255, 0.0075946744, -0.02858767, -0.007648378, 0.001900528], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1], "loss_changes": [0.769659, 3.001441, 3.5925255, 1.3113022, 0.0, 0.0, 0.0, 1.5765332, 0.0, 0.86957127, 1.9093307, 1.9993472, 1.0725405, 0.0, 0.0, 0.9958073, 0.0, 1.5032711, 3.4324703, 0.89537567, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0901369, 0.0, 0.0, 0.7180481, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1], "split_conditions": [1.0183754, 1.0675184, 1.0, 1.4726204, -0.01778083, -0.011147267, 0.013973032, 0.6151461, 0.008213546, 1.0, 0.67889345, 0.50570595, 0.48429644, -0.0010552006, -0.01956162, 0.33549544, -0.014750617, 1.0, 1.0, 0.27044263, 0.009157104, -0.0065545873, 0.0070522963, -0.0027426097, 0.022607274, 1.0, -0.009780255, 0.0075946744, 1.0, -0.007648378, 0.001900528], "split_indices": [142, 140, 50, 138, 0, 0, 0, 139, 0, 2, 140, 140, 139, 0, 0, 142, 0, 17, 105, 142, 0, 0, 0, 0, 0, 53, 0, 0, 12, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1820.0, 247.0, 1719.0, 101.0, 89.0, 158.0, 1546.0, 173.0, 1315.0, 231.0, 774.0, 541.0, 137.0, 94.0, 671.0, 103.0, 326.0, 215.0, 566.0, 105.0, 173.0, 153.0, 116.0, 99.0, 461.0, 105.0, 146.0, 315.0, 157.0, 158.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0009904404, -0.0038064292, 0.009268985, 0.0018750757, -0.01046667, -0.011180065, 0.0132812755, -0.0014214322, -0.015746748, 0.0048668636, -0.0077712513, -0.003134414, 0.00872185, 0.0049517076, -0.0062668505, -0.008408866, 0.014439461, 0.0025231168, -0.012661846, -0.00072865246, 0.010240314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [0.91141117, 1.1283123, 0.0, 3.1863403, 0.0, 2.419722, 0.0, 0.7623085, 0.0, 0.9672923, 0.0, 0.64411324, 0.0, 2.194658, 0.0, 1.3891857, 0.0, 0.9641108, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.3466678, 1.0953796, 0.009268985, 0.9271665, -0.01046667, 1.4922987, 0.0132812755, 0.7425969, -0.015746748, 0.62643254, -0.0077712513, 0.61964226, 0.00872185, 0.5405575, -0.0062668505, 0.48244315, 0.014439461, 0.44164315, -0.012661846, -0.00072865246, 0.010240314], "split_indices": [139, 139, 0, 140, 0, 138, 0, 140, 0, 140, 0, 142, 0, 142, 0, 142, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1969.0, 103.0, 1864.0, 105.0, 1695.0, 169.0, 1589.0, 106.0, 1468.0, 121.0, 1338.0, 130.0, 1178.0, 160.0, 1075.0, 103.0, 984.0, 91.0, 896.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [-0.00215185, 0.0018549672, -0.008595763, -0.0041703717, 0.012054163, -0.0109764, 0.010497131, -0.00253625, -0.07997678, 0.007795008, -0.008637383, 0.007926088, -0.022660159, -0.0038721703, 0.00990907, 0.007162076, -0.008877459, -0.0068654497, 0.0165829, 0.019357363, -0.054794934, -0.00059302384, 0.015719961, -0.013935509, 0.0018373693], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [0.69912416, 1.4209583, 0.0, 1.404675, 0.0, 1.0366248, 0.0, 1.3737105, 4.52955, 1.5040106, 0.0, 0.0, 0.0, 1.1729165, 0.0, 2.4660814, 0.0, 1.2794691, 0.0, 2.2935908, 2.2273748, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1254623, -0.008595763, 5.0, 0.012054163, 0.88695586, 0.010497131, 2.0, 1.0, 0.70670706, -0.008637383, 0.007926088, -0.022660159, 0.5804528, 0.00990907, 0.59135294, -0.008877459, 0.37374815, 0.0165829, 0.38907775, 0.44437784, -0.00059302384, 0.015719961, -0.013935509, 0.0018373693], "split_indices": [143, 142, 0, 0, 0, 140, 0, 0, 105, 141, 0, 0, 0, 141, 0, 139, 0, 141, 0, 142, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2082.0, 1987.0, 95.0, 1891.0, 96.0, 1780.0, 111.0, 1586.0, 194.0, 1412.0, 174.0, 93.0, 101.0, 1252.0, 160.0, 1108.0, 144.0, 1018.0, 90.0, 658.0, 360.0, 556.0, 102.0, 167.0, 193.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0070077265, -0.0008870112, -0.008418547, -0.009050994, 0.016366256, -0.0010499326, -0.083515406, -0.014022296, 0.0741467, 0.0010281136, -0.017731197, -0.0026951793, -0.013456168, -0.0030433577, 0.024653181, -0.020898005, 0.07677008, 0.00635503, -0.1167576, -0.007203389, 0.018459907, -0.010803569, 0.009516063, -0.0036334973, -0.01944307, 0.009354846, -0.009188522, -0.0015296274, 0.0075146705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [0.97074693, 2.5577953, 0.0, 1.0807707, 0.0, 1.597833, 1.5484123, 1.9074128, 4.3447685, 0.0, 0.0, 1.8471705, 0.0, 0.0, 0.0, 2.7143514, 3.8188014, 1.232738, 1.4367349, 0.0, 0.0, 1.108176, 0.0, 0.0, 0.0, 0.8806609, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.6397896, 1.562096, -0.008418547, 1.486252, 0.016366256, 1.0, 0.8037921, 0.76379925, 0.5338557, 0.0010281136, -0.017731197, 1.0, -0.013456168, -0.0030433577, 0.024653181, 0.5325959, 0.31643265, 0.5076032, 1.0, -0.007203389, 0.018459907, 1.3100263, 0.009516063, -0.0036334973, -0.01944307, 0.28427187, -0.009188522, -0.0015296274, 0.0075146705], "split_indices": [138, 138, 0, 138, 0, 42, 141, 142, 143, 0, 0, 61, 0, 0, 0, 140, 141, 142, 93, 0, 0, 138, 0, 0, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1904.0, 151.0, 1814.0, 90.0, 1638.0, 176.0, 1397.0, 241.0, 88.0, 88.0, 1277.0, 120.0, 150.0, 91.0, 1039.0, 238.0, 809.0, 230.0, 100.0, 138.0, 678.0, 131.0, 113.0, 117.0, 543.0, 135.0, 395.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0021566728, -0.00225862, 0.009136364, 0.0042856405, -0.010552486, -0.0075683724, 0.07211457, 0.0016973114, -0.015289334, -0.0036904283, 0.015066233, -0.004783462, 0.009904831, 0.0029233687, -0.008679701, 0.01332186, -0.06004206, -0.0062461053, 0.08579893, 0.0019553115, -0.013704176, 0.01832255, -0.017983401, -0.0009779464, 0.023494326, 0.008669997, -0.0021671779], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [0.8184724, 1.3380868, 0.0, 1.4971321, 0.0, 2.1342587, 2.3720033, 0.94005585, 0.0, 0.0, 0.0, 0.8829944, 0.0, 0.83610976, 0.0, 1.5543787, 1.1093141, 3.6805415, 3.3214102, 0.0, 0.0, 2.06744, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.2520225, 1.1962737, 0.009136364, 1.4726204, -0.010552486, 0.81649506, 1.0, 0.8141592, -0.015289334, -0.0036904283, 0.015066233, 0.67325324, 0.009904831, 1.0, -0.008679701, 1.0, 0.3021278, 0.54411215, 0.40912068, 0.0019553115, -0.013704176, 1.0, -0.017983401, -0.0009779464, 0.023494326, 0.008669997, -0.0021671779], "split_indices": [142, 140, 0, 138, 0, 142, 109, 143, 0, 0, 0, 143, 0, 113, 0, 50, 142, 139, 140, 0, 0, 137, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1980.0, 98.0, 1862.0, 118.0, 1585.0, 277.0, 1490.0, 95.0, 116.0, 161.0, 1397.0, 93.0, 1277.0, 120.0, 1096.0, 181.0, 863.0, 233.0, 89.0, 92.0, 756.0, 107.0, 142.0, 91.0, 279.0, 477.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.00013197638, -0.0082126325, 0.006217897, -0.0007945321, 0.014746829, -0.007180635, 0.010325071, -0.06396679, 0.004422113, -0.017663594, -0.012396811, 0.013734061, -0.00984788, -0.01659106, 0.012795863, -0.0024565635, 0.018430373, 0.008425556, -0.008233436, -0.0047525545, 0.015001707, 0.049986735, -0.030826848, -1.8115634e-05, 0.01563427, 0.0002984733, -0.01446592], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, 11, -1, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [1.0793154, 0.0, 1.9057384, 1.2179248, 0.0, 1.137878, 0.0, 1.7024308, 1.3740702, 0.0, 4.3308463, 3.6315434, 0.0, 0.0, 0.0, 1.0439572, 0.0, 1.9722658, 0.0, 1.3801876, 0.0, 1.6647247, 2.520998, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, 12, -1, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, -0.0082126325, 1.3466678, 5.0, 0.014746829, -0.3846154, 0.010325071, 1.0, 0.8922892, -0.017663594, 1.0, 0.72909546, -0.00984788, -0.01659106, 0.012795863, 0.6316315, 0.018430373, 0.5964612, -0.008233436, 0.115384616, 0.015001707, 0.3940059, 0.40983817, -1.8115634e-05, 0.01563427, 0.0002984733, -0.01446592], "split_indices": [104, 0, 139, 0, 0, 1, 0, 89, 141, 0, 69, 141, 0, 0, 0, 142, 0, 139, 0, 1, 0, 143, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 149.0, 1924.0, 1833.0, 91.0, 1727.0, 106.0, 293.0, 1434.0, 92.0, 201.0, 1315.0, 119.0, 96.0, 105.0, 1201.0, 114.0, 1057.0, 144.0, 967.0, 90.0, 312.0, 655.0, 212.0, 100.0, 505.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.00047957344, 0.0046376623, -0.007978781, 0.010197792, -0.006089242, -0.00048026003, 0.106595255, 0.00721694, -0.008012492, 0.024920247, -0.0040818914, -4.6901943e-05, 0.009745875, 0.047315203, -0.010279458, -0.009283281, 0.016340798, 0.037736002, -0.041868582, -0.00045759982, 0.018635897, -0.05882421, 0.0050587556, 0.008145536, -0.044944823, 0.007453074, -0.085717, -4.525449e-05, -0.011775034, -0.011244966, 3.433474e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [0.69121456, 0.7174164, 0.0, 1.8682474, 0.0, 1.001709, 3.805041, 0.97669864, 0.0, 0.0, 0.0, 0.6683121, 0.0, 3.9861925, 1.7200129, 0.0, 0.0, 2.5544014, 1.0722741, 1.3045805, 0.0, 2.072874, 0.0, 0.0, 0.75151324, 0.0, 1.1065993, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 21, 21, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.6397896, -0.007978781, 0.9476538, -0.006089242, 1.486252, 1.0, 0.78377116, -0.008012492, 0.024920247, -0.0040818914, 1.0, 0.009745875, 0.38907775, 0.292501, -0.009283281, 0.016340798, 0.25275725, 0.71645033, 1.0, 0.018635897, -1.0, 0.0050587556, 0.008145536, 0.25341877, 0.007453074, 1.0, -4.525449e-05, -0.011775034, -0.011244966, 3.433474e-05], "split_indices": [117, 138, 0, 140, 0, 138, 108, 142, 0, 0, 0, 89, 0, 142, 142, 0, 0, 142, 143, 124, 0, 0, 0, 0, 140, 0, 62, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1969.0, 102.0, 1815.0, 154.0, 1634.0, 181.0, 1490.0, 144.0, 92.0, 89.0, 1379.0, 111.0, 245.0, 1134.0, 111.0, 134.0, 450.0, 684.0, 358.0, 92.0, 578.0, 106.0, 126.0, 232.0, 97.0, 481.0, 144.0, 88.0, 367.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0026390103, 0.00927352, -0.048897397, -0.0031417592, 0.11467541, 0.0027086434, -0.017755186, 0.006326814, -0.016889503, 0.025145218, -0.0046690423, 0.042409647, -0.00622102, -0.0116001405, 0.0201262, 0.018352864, -0.046652656, 0.04098211, -0.013626146, -0.008471829, 0.0121845445, -0.13773991, 0.025395855, -0.0023737957, 0.012969955, 0.008635672, -0.026215222, -0.021049647, -0.007282962, 0.012554871, -0.009460709, 0.004501475, -0.012860425, -0.04359792, 0.007196558, 0.004568813, -0.014338824], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, -1, 19, 21, 23, -1, 25, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1], "loss_changes": [0.71050715, 2.409122, 2.3168316, 2.5848796, 4.281794, 0.0, 0.0, 0.70540243, 0.0, 0.0, 0.0, 3.4489925, 1.1485583, 1.9664922, 0.0, 1.9960572, 2.8679008, 1.2115196, 0.0, 0.9607531, 0.0, 0.91147065, 2.9325473, 0.0, 0.0, 0.0, 1.5127707, 0.0, 0.0, 0.0, 0.0, 1.2006438, 0.0, 1.9245353, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 22, 22, 26, 26, 31, 31, 33, 33], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, -1, 20, 22, 24, -1, 26, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1], "split_conditions": [1.0, 0.9782575, 0.85986227, 0.8635552, 1.0977916, 0.0027086434, -0.017755186, 0.0, -0.016889503, 0.025145218, -0.0046690423, 0.56483954, 0.47514603, 0.42291117, 0.0201262, 0.43214852, 0.6396571, 0.2785644, -0.013626146, 0.14039758, 0.0121845445, 1.0, 0.6725825, -0.0023737957, 0.012969955, 0.008635672, 0.38341978, -0.021049647, -0.007282962, 0.012554871, -0.009460709, 0.25908482, -0.012860425, 0.16432898, 0.007196558, 0.004568813, -0.014338824], "split_indices": [119, 141, 140, 141, 143, 0, 0, 0, 0, 0, 0, 140, 143, 142, 0, 142, 143, 142, 0, 141, 0, 97, 142, 0, 0, 0, 140, 0, 0, 0, 0, 143, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1841.0, 237.0, 1647.0, 194.0, 149.0, 88.0, 1558.0, 89.0, 105.0, 89.0, 402.0, 1156.0, 300.0, 102.0, 719.0, 437.0, 211.0, 89.0, 571.0, 148.0, 193.0, 244.0, 122.0, 89.0, 90.0, 481.0, 91.0, 102.0, 133.0, 111.0, 370.0, 111.0, 216.0, 154.0, 114.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017247633, 0.0048548165, -0.047833238, -0.0064614625, 0.016705483, -0.015662707, 0.00898439, 0.0022226772, -0.011617817, 0.008975213, -0.010265711, -0.030350456, 0.030925404, 0.021949854, -0.10768553, 0.0014959062, 0.01956891, -0.063291155, 0.017206458, -0.0023391827, -0.019607112, 0.024307445, -0.0100762695, 0.002051924, -0.01514217, -0.0101466, 0.081262104, -0.033810925, 0.006108064, 0.021144198, 0.00069927954, -0.0002599265, -0.010041722], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [0.62191767, 3.2928889, 3.8344772, 1.597838, 0.0, 0.0, 0.0, 1.1005497, 0.0, 1.2602807, 0.0, 2.1153512, 4.5434318, 3.9923303, 1.5720232, 1.8544773, 0.0, 1.4698653, 0.0, 0.0, 0.0, 1.2755072, 0.0, 0.0, 0.0, 0.6826455, 2.36875, 0.63198316, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 21, 21, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.1254623, 1.0, 1.0078171, 0.016705483, -0.015662707, 0.00898439, 2.0, -0.011617817, -0.15384616, -0.010265711, 0.55102646, 0.6292746, 1.0, 1.0, 0.4802205, 0.01956891, -0.34615386, 0.017206458, -0.0023391827, -0.019607112, 0.33186364, -0.0100762695, 0.002051924, -0.01514217, 0.30455607, 0.30699542, 0.21324573, 0.006108064, 0.021144198, 0.00069927954, -0.0002599265, -0.010041722], "split_indices": [0, 142, 122, 140, 0, 0, 0, 0, 0, 1, 0, 141, 141, 71, 108, 142, 0, 1, 0, 0, 0, 140, 0, 0, 0, 143, 143, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1794.0, 256.0, 1677.0, 117.0, 143.0, 113.0, 1554.0, 123.0, 1460.0, 94.0, 523.0, 937.0, 312.0, 211.0, 795.0, 142.0, 199.0, 113.0, 108.0, 103.0, 650.0, 145.0, 102.0, 97.0, 405.0, 245.0, 304.0, 101.0, 89.0, 156.0, 207.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015637549, 0.0034416749, -0.0108745275, 0.012351453, -0.06600685, -0.0023654061, 0.028000424, 0.0026676976, -0.019418662, 0.008999805, -0.019545048, -0.0025527426, 0.018553719, 0.0062420545, -0.011781535, -0.006297911, 0.055184018, 0.0076864623, -0.013280473, -0.0027874731, 0.014066786, 0.018577684, -0.00799475, 0.00025677765, 0.0152165685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, -1, -1], "loss_changes": [1.1062417, 1.2189786, 0.0, 6.87751, 2.6611638, 3.6318197, 0.0, 0.0, 0.0, 3.1876714, 0.0, 1.4871147, 0.0, 0.8365149, 0.0, 1.919494, 1.9738507, 0.9324885, 0.0, 0.0, 0.0, 1.8585572, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, -1, -1], "split_conditions": [1.4310507, 1.0, -0.0108745275, 1.1314709, 1.4762797, 1.5248721, 0.028000424, 0.0026676976, -0.019418662, 1.4726204, -0.019545048, 0.76379925, 0.018553719, 0.5405575, -0.011781535, 0.5786564, -0.115384616, 0.45463473, -0.013280473, -0.0027874731, 0.014066786, 0.43592757, -0.00799475, 0.00025677765, 0.0152165685], "split_indices": [143, 119, 0, 139, 138, 138, 0, 0, 0, 138, 0, 142, 0, 142, 0, 143, 1, 142, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1970.0, 92.0, 1746.0, 224.0, 1655.0, 91.0, 130.0, 94.0, 1563.0, 92.0, 1467.0, 96.0, 1363.0, 104.0, 1085.0, 278.0, 977.0, 108.0, 141.0, 137.0, 869.0, 108.0, 776.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0027109135, 0.008802577, -0.0015461396, -0.008912483, 0.004102566, -0.007768438, 0.11390937, -6.3758125e-05, -0.011775449, 0.022546653, 0.0007202499, -0.080189444, 0.016758896, 0.0019452529, -0.13340734, 0.00023904906, 0.1114669, -0.010481649, -0.016199814, 0.01405244, -0.011095302, 0.02703594, -0.0074509517, -0.01499617, 0.053345256, 0.014573528, -0.012005276, 0.14438197, -0.024627889, 0.0057931994, -0.0034046874, 0.023574363, 0.0057681603, 0.006338159, -0.014765193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.74889773, 0.0, 0.9716033, 0.0, 2.4049895, 1.4109329, 2.142704, 2.0973742, 0.0, 0.0, 0.0, 1.4317389, 2.012026, 0.0, 0.14386773, 1.6818534, 5.6440997, 0.0, 0.0, 1.1117253, 0.0, 0.0, 0.0, 1.7396358, 2.938746, 0.9212424, 0.0, 1.5129275, 2.4144843, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 23, 23, 24, 24, 25, 25, 27, 27, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008802577, 1.0, -0.008912483, 1.0183754, 1.5270969, 1.176247, -1.0, -0.011775449, 0.022546653, 0.0007202499, 1.0, 1.0, 0.0019452529, 0.37522858, 1.0, 1.0, -0.010481649, -0.016199814, 0.43565965, -0.011095302, 0.02703594, -0.0074509517, 0.3986846, 0.5866392, 0.5769231, -0.012005276, 1.0, 1.0, 0.0057931994, -0.0034046874, 0.023574363, 0.0057681603, 0.006338159, -0.014765193], "split_indices": [1, 0, 104, 0, 142, 138, 143, 0, 0, 0, 0, 122, 42, 0, 143, 64, 12, 0, 0, 142, 0, 0, 0, 140, 142, 1, 0, 93, 105, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 98.0, 1964.0, 119.0, 1845.0, 1665.0, 180.0, 1556.0, 109.0, 88.0, 92.0, 270.0, 1286.0, 94.0, 176.0, 1095.0, 191.0, 88.0, 88.0, 974.0, 121.0, 103.0, 88.0, 560.0, 414.0, 437.0, 123.0, 191.0, 223.0, 231.0, 206.0, 93.0, 98.0, 130.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018319632, 0.009049195, -0.006539966, -0.0006802208, -0.0099740205, -0.00862319, 0.012962942, -0.016431458, 0.0072901263, -0.008329674, -0.013078234, 0.002739825, -0.07216885, -0.009640163, 0.075802065, -0.020546144, 0.003380966, 0.0018112091, -0.06689704, -0.0026241806, 0.018357879, -0.012041749, 0.011919684, 0.0026998043, -0.01650601, 0.0012591084, -0.0053069293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [0.89583653, 0.0, 1.0709599, 1.9096588, 0.0, 1.1069858, 0.0, 1.4702696, 0.0, 1.0472817, 0.0, 1.1423959, 3.0936272, 0.7081236, 2.0126257, 0.0, 0.0, 1.4635249, 1.6590658, 0.0, 0.0, 0.81355304, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009049195, 1.289564, 1.0933275, -0.0099740205, 0.8711886, 0.012962942, 0.82049423, 0.0072901263, 0.6151461, -0.013078234, 0.50411034, 1.0, 1.0, 0.03846154, -0.020546144, 0.003380966, 0.55767125, 0.35969147, -0.0026241806, 0.018357879, 1.2634896, 0.011919684, 0.0026998043, -0.01650601, 0.0012591084, -0.0053069293], "split_indices": [1, 0, 140, 142, 0, 139, 0, 141, 0, 139, 0, 139, 59, 105, 1, 0, 0, 143, 140, 0, 0, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 100.0, 1961.0, 1845.0, 116.0, 1739.0, 106.0, 1587.0, 152.0, 1482.0, 105.0, 1263.0, 219.0, 1080.0, 183.0, 97.0, 122.0, 900.0, 180.0, 94.0, 89.0, 805.0, 95.0, 92.0, 88.0, 503.0, 302.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.003206271, -0.005631237, 0.04903924, 0.004904739, -0.01826791, 0.017388795, 0.0027989661, -0.0038114695, 0.014733133, -0.0065158904, 0.007969868, -0.0110445535, 0.0075752432, 0.01004908, -0.085448794, -0.0028249722, 0.013563423, -0.0287171, -0.022475658, 0.017983897, -0.0379751, 0.0053474735, -0.011951954, -0.002111194, 0.013149981, -0.009889251, 0.016104626, 0.030364089, -0.035706326, -0.0087216105, 0.012155571, -0.0022440806, 0.011334324, 0.00432093, -0.007654059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, -1, 23, 25, -1, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8344016, 3.2214983, 1.9224215, 2.023515, 0.0, 0.0, 1.2699037, 0.88395613, 0.0, 0.0, 0.0, 2.2097943, 0.0, 1.7736188, 2.457884, 0.7277773, 0.0, 1.6493717, 0.0, 1.425696, 1.2189269, 0.0, 0.0, 0.5793271, 0.0, 0.0, 2.1354754, 1.1830606, 0.8410627, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 23, 23, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, -1, 24, 26, -1, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [0.90107125, 0.97098887, 0.87239504, 0.78377116, -0.01826791, 0.017388795, 1.1427536, 0.7202307, 0.014733133, -0.0065158904, 0.007969868, 0.5138817, 0.0075752432, 0.5325338, 1.4322847, 0.3222687, 0.013563423, 1.0, -0.022475658, 0.29588273, 1.0, 0.0053474735, -0.011951954, 1.0, 0.013149981, -0.009889251, 0.38902214, 0.42307693, 0.17621006, -0.0087216105, 0.012155571, -0.0022440806, 0.011334324, 0.00432093, -0.007654059], "split_indices": [142, 140, 143, 142, 0, 0, 140, 140, 0, 0, 0, 141, 0, 139, 138, 143, 0, 124, 0, 143, 124, 0, 0, 80, 0, 0, 140, 1, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1727.0, 333.0, 1630.0, 97.0, 90.0, 243.0, 1536.0, 94.0, 129.0, 114.0, 1408.0, 128.0, 1097.0, 311.0, 995.0, 102.0, 221.0, 90.0, 625.0, 370.0, 116.0, 105.0, 531.0, 94.0, 174.0, 196.0, 270.0, 261.0, 99.0, 97.0, 165.0, 105.0, 89.0, 172.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004158575, 0.003908609, -0.07523392, -0.004076914, 0.0084815845, -0.018631903, 0.0005803558, 0.007476878, -0.059933532, -0.008372123, 0.012879023, 0.010376494, -0.13124774, 0.0024424477, -0.012058573, -0.00083552126, -0.023610096, 0.0116137825, -0.0059432634, 3.934431e-05, 0.010463401, 0.033418857, -0.02459791, -0.009375222, 0.0734126, 0.022363093, -0.012373782, 0.015139152, 0.002157953, -0.0027432658, 0.015971184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, -1, 19, -1, 21, -1, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [1.1868926, 1.2010752, 1.8994349, 1.0919422, 0.0, 0.0, 0.0, 2.6956196, 3.385468, 1.5047923, 0.0, 0.0, 2.6029062, 0.64181644, 0.0, 0.0, 0.0, 1.0605067, 0.0, 0.72040445, 0.0, 1.8920095, 2.346478, 0.0, 1.1438534, 2.3390698, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 17, 17, 19, 19, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, -1, 20, -1, 22, -1, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0558407, 0.85070765, 1.1503603, 0.6555352, 0.0084815845, -0.018631903, 0.0005803558, 1.4060924, 1.3955984, 0.658762, 0.012879023, 0.010376494, 1.0, 1.3507949, -0.012058573, -0.00083552126, -0.023610096, 1.3217473, -0.0059432634, 1.0, 0.010463401, 1.1945249, 0.36517957, -0.009375222, 0.24274075, 0.33186364, -0.012373782, 0.015139152, 0.002157953, -0.0027432658, 0.015971184], "split_indices": [141, 141, 140, 141, 0, 0, 0, 138, 138, 143, 0, 0, 69, 138, 0, 0, 0, 138, 0, 126, 0, 138, 142, 0, 139, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1859.0, 211.0, 1692.0, 167.0, 89.0, 122.0, 1402.0, 290.0, 1240.0, 162.0, 88.0, 202.0, 1131.0, 109.0, 93.0, 109.0, 985.0, 146.0, 876.0, 109.0, 372.0, 504.0, 89.0, 283.0, 342.0, 162.0, 113.0, 170.0, 251.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0010302449, -0.0037425682, 0.005494328, 0.008408348, -0.007986772, -0.0009843974, -0.010299496, -0.054262273, 0.024654124, -0.018719612, -0.018329106, 0.003624949, 0.026253251, -0.014242518, 0.03279057, 0.015643286, -0.016980214, -0.049869288, 0.016022455, 0.0038936953, -0.014269809, 0.004006784, -0.014277141, -0.023819407, 0.07587253, -0.04314552, 0.0044202395, 0.016503155, 0.0015757723, -0.00020066711, -0.009828448], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, -1, -1, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, 17, -1, 19, 21, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [0.53470457, 0.71158266, 0.0, 0.0, 1.2114799, 2.3166783, 0.0, 2.5269003, 5.727731, 2.752746, 0.0, 3.3123612, 0.0, 0.0, 3.212771, 0.0, 2.4326558, 1.5457401, 0.0, 1.5858319, 0.0, 0.0, 0.0, 0.7545786, 1.1845102, 1.0139537, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 14, 14, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, -1, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, 18, -1, 20, 22, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, 0.005494328, 0.008408348, 1.1951114, -0.1923077, -0.010299496, 0.8530801, 0.84814256, 0.35120344, -0.018329106, -0.03846154, 0.026253251, -0.014242518, 1.0, 0.015643286, 0.67325324, 1.0, 0.016022455, 0.4405278, -0.014269809, 0.004006784, -0.014277141, 1.3846154, 1.0, 1.0, 0.0044202395, 0.016503155, 0.0015757723, -0.00020066711, -0.009828448], "split_indices": [41, 1, 0, 0, 143, 1, 0, 143, 143, 142, 0, 1, 0, 0, 97, 0, 143, 59, 0, 143, 0, 0, 0, 1, 111, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1909.0, 169.0, 88.0, 1821.0, 1696.0, 125.0, 551.0, 1145.0, 432.0, 119.0, 1052.0, 93.0, 127.0, 305.0, 125.0, 927.0, 185.0, 120.0, 795.0, 132.0, 94.0, 91.0, 574.0, 221.0, 447.0, 127.0, 89.0, 132.0, 256.0, 191.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.003146635, -0.007403918, 0.036814056, 0.0025488094, -0.01552874, -0.052851055, 0.11552771, -0.0049224026, 0.011199414, 0.0062324367, -0.017210351, 0.019544093, -0.002265549, 0.029644659, -0.03646485, -0.0140818395, 0.09000625, -0.0036174583, -0.12428133, 0.032853324, -0.010160006, -0.0019721815, 0.017952127, -0.0279827, 0.010641081, -0.02064575, -0.0035111408, 0.011919136, -0.0014632582, 0.0019892056, -0.011817856, -0.032765023, 0.009297503, 0.005027661, -0.008408939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [0.732443, 2.3107955, 3.4724717, 1.2028211, 0.0, 3.1590395, 2.8931777, 1.5013841, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7340868, 2.07687, 1.5650269, 2.7109578, 1.4047734, 1.4362175, 1.0167606, 0.0, 0.0, 0.0, 1.1597339, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0182099, 0.0, 0.9930599, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 29, 29, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.1095077, 0.49909803, 5.0, -0.01552874, 0.30769083, -0.15384616, 1.0, 0.011199414, 0.0062324367, -0.017210351, 0.019544093, -0.002265549, 0.55767125, 0.5139595, 1.0, 1.0, 1.0, 0.635029, 0.23076923, -0.010160006, -0.0019721815, 0.017952127, 0.40078467, 0.010641081, -0.02064575, -0.0035111408, 0.011919136, -0.0014632582, 0.33186364, -0.011817856, 0.1702571, 0.009297503, 0.005027661, -0.008408939], "split_indices": [61, 143, 143, 0, 0, 140, 1, 106, 0, 0, 0, 0, 0, 143, 140, 93, 93, 121, 142, 1, 0, 0, 0, 143, 0, 0, 0, 0, 0, 140, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1570.0, 492.0, 1471.0, 99.0, 230.0, 262.0, 1377.0, 94.0, 117.0, 113.0, 166.0, 96.0, 657.0, 720.0, 381.0, 276.0, 524.0, 196.0, 248.0, 133.0, 124.0, 152.0, 429.0, 95.0, 102.0, 94.0, 88.0, 160.0, 322.0, 107.0, 233.0, 89.0, 89.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0034058618, 0.008214176, -0.0055495957, 0.0015303458, 0.01345803, 0.010660325, -0.053320017, -0.0019935768, 0.019891942, 0.0024255381, -0.020406317, 0.007348585, -0.0130236, -0.0007813667, 0.011931219, -0.01854871, 0.05430314, -0.0116317775, 0.0023929472, -0.002987534, 0.016423775, 0.024724407, -0.017549738, -0.0014454959, 0.009117865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, -1, 21, -1, -1, 23, -1, -1, -1], "loss_changes": [0.5854123, 1.6140485, 0.0, 0.90892047, 0.0, 3.7067227, 3.028736, 1.746774, 0.0, 0.0, 0.0, 1.237042, 0.0, 1.2400196, 0.0, 1.9614539, 2.859526, 0.0, 3.1343422, 0.0, 0.0, 1.8251485, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, -1, 22, -1, -1, 24, -1, -1, -1], "split_conditions": [1.6397896, 1.0, -0.0055495957, 1.0, 0.01345803, 0.9677354, 0.6368173, 0.8449834, 0.019891942, 0.0024255381, -0.020406317, 0.73786306, -0.0130236, 1.0, 0.011931219, -1.0, 1.0, -0.0116317775, 0.67889345, -0.002987534, 0.016423775, 0.43565965, -0.017549738, -0.0014454959, 0.009117865], "split_indices": [138, 102, 0, 64, 0, 141, 139, 139, 0, 0, 0, 139, 0, 116, 0, 0, 71, 0, 140, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1911.0, 156.0, 1815.0, 96.0, 1556.0, 259.0, 1458.0, 98.0, 171.0, 88.0, 1359.0, 99.0, 1267.0, 92.0, 958.0, 309.0, 169.0, 789.0, 175.0, 134.0, 701.0, 88.0, 441.0, 260.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0010064533, 0.007879629, -0.058939297, 5.867141e-06, 0.012999321, -0.016408697, 0.004231399, -0.0090698525, 0.08050133, 0.003408838, -0.01769545, 0.00041191574, 0.015688347, -0.0038847686, 0.01138665, 0.0037880552, -0.011371826, 0.038029082, -0.022140663, 0.0032838096, 0.015348186, -0.006691586, -0.013114244, 0.07937896, -0.05442167, -0.040087882, 0.045422785, -0.0027025922, 0.017899208, -0.01249274, 0.0063087842, -0.00025145998, -0.010349278, 0.016738988, -0.0021659099], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, 25, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8491683, 1.7778007, 2.2570684, 1.268973, 0.0, 0.0, 0.0, 3.270265, 1.0268247, 1.1705875, 0.0, 0.0, 0.0, 1.1486455, 0.0, 1.1310903, 0.0, 2.202279, 1.2208828, 1.8530475, 0.0, 1.1051712, 0.0, 1.9290773, 1.9884226, 0.9219615, 2.0290828, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, 26, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0558407, 1.0, 1.2337983, 3.0, 0.012999321, -0.016408697, 0.004231399, 0.8314529, 0.50570595, 0.8316313, -0.01769545, 0.00041191574, 0.015688347, 0.7457551, 0.01138665, 0.07692308, -0.011371826, 0.5429218, 0.5457873, 0.31776503, 0.015348186, 0.33666655, -0.013114244, 0.2785644, 0.46315563, 0.27805993, 0.40132928, -0.0027025922, 0.017899208, -0.01249274, 0.0063087842, -0.00025145998, -0.010349278, 0.016738988, -0.0021659099], "split_indices": [141, 125, 140, 0, 0, 0, 0, 141, 140, 139, 0, 0, 0, 140, 0, 1, 0, 140, 140, 140, 0, 139, 0, 142, 139, 141, 139, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1849.0, 212.0, 1737.0, 112.0, 104.0, 108.0, 1561.0, 176.0, 1453.0, 108.0, 88.0, 88.0, 1363.0, 90.0, 1274.0, 89.0, 549.0, 725.0, 422.0, 127.0, 635.0, 90.0, 182.0, 240.0, 387.0, 248.0, 88.0, 94.0, 150.0, 90.0, 243.0, 144.0, 88.0, 160.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.003038104, 0.004317652, -0.06804899, -0.0018003732, 0.009958408, -0.015190939, 0.0012676417, 0.0047352114, -0.012333291, -0.007443745, 0.08213926, 0.0004729549, -0.012531464, -0.0056051095, 0.025487725, -0.019337516, 0.052215137, -0.0033625246, -0.014408766, 0.0053042225, 0.015533824, 0.022136046, -0.06051782, 0.010665393, -0.01123645, -0.00528521, 0.01414556, -0.011627847, -0.0005176401, 0.005585217, -0.007423311], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [0.9879701, 1.0817558, 1.4216303, 1.3852354, 0.0, 0.0, 0.0, 1.5601696, 0.0, 1.3344028, 5.3709126, 1.3735497, 0.0, 0.0, 0.0, 1.9311028, 1.7947488, 1.2518882, 0.0, 3.0410514, 0.0, 1.9435042, 0.8177562, 0.0, 0.0, 2.035987, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 22, 22, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0558407, 1.0, 1.2337983, 1.0199403, 0.009958408, -0.015190939, 0.0012676417, 1.4491278, -0.012333291, 0.7457551, 1.0, 1.0, -0.012531464, -0.0056051095, 0.025487725, 0.59144264, 0.457961, 1.0, -0.014408766, 0.28601104, 0.015533824, 1.0, 1.2564335, 0.010665393, -0.01123645, 1.2634896, 0.01414556, -0.011627847, -0.0005176401, 0.005585217, -0.007423311], "split_indices": [141, 125, 140, 143, 0, 0, 0, 138, 0, 140, 109, 50, 0, 0, 0, 141, 141, 109, 0, 142, 0, 121, 138, 0, 0, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1856.0, 210.0, 1744.0, 112.0, 103.0, 107.0, 1655.0, 89.0, 1430.0, 225.0, 1340.0, 90.0, 125.0, 100.0, 969.0, 371.0, 859.0, 110.0, 255.0, 116.0, 594.0, 265.0, 137.0, 118.0, 483.0, 111.0, 132.0, 133.0, 256.0, 227.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029913406, -0.008435266, 0.0031403583, 0.0111157475, -0.064103104, 0.002345705, 0.017376013, -0.01559558, 0.00084121566, 0.012004912, -0.015749855, 0.00343058, 0.01468642, 0.035683267, -0.02143991, 0.015703749, 0.015457559, 0.006996402, -0.04318297, 0.0077061662, -0.007156596, -0.019613372, -0.018510202, -0.043834582, 0.0098202685, -0.011218784, 0.009383265, 0.0026513455, -0.007706432, -0.005430335, 0.005865523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, -1, 19, -1, 21, -1, 23, 25, -1, 27, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [1.0321894, 0.0, 1.0318273, 2.4534047, 1.3587878, 2.5197563, 0.0, 0.0, 0.0, 1.7795887, 0.0, 1.1606969, 0.0, 1.5463185, 1.6237067, 0.0, 0.75228757, 0.0, 2.207683, 0.0, 1.5264245, 1.5193413, 0.0, 0.6849306, 0.0, 0.0, 1.3524628, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 16, 16, 18, 18, 20, 20, 21, 21, 23, 23, 26, 26], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, -1, 20, -1, 22, -1, 24, 26, -1, 28, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, -0.008435266, 1.0, 1.3131162, 1.0, 1.5516421, 0.017376013, -0.01559558, 0.00084121566, 0.9271665, -0.015749855, 0.3222687, 0.01468642, 1.0, 1.0, 0.015703749, 0.0, 0.006996402, 1.3461539, 0.0077061662, 0.26206392, 0.43592757, -0.018510202, 0.124228664, 0.0098202685, -0.011218784, 1.0, 0.0026513455, -0.007706432, -0.005430335, 0.005865523], "split_indices": [104, 0, 119, 139, 93, 138, 0, 0, 0, 140, 0, 143, 0, 137, 89, 0, 1, 0, 1, 0, 143, 143, 0, 139, 0, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 145.0, 1924.0, 1720.0, 204.0, 1632.0, 88.0, 90.0, 114.0, 1539.0, 93.0, 1447.0, 92.0, 630.0, 817.0, 90.0, 540.0, 157.0, 660.0, 145.0, 395.0, 566.0, 94.0, 293.0, 102.0, 135.0, 431.0, 94.0, 199.0, 188.0, 243.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.001984212, 0.0031631782, -0.009482616, -0.00656537, 0.06453622, 0.010257205, -0.053465817, 0.014440313, -0.0052699572, -0.0061032996, 0.014639127, 0.008241401, -0.019502945, 0.009262823, -0.00939494, -0.078042135, 0.018080851, -0.005313166, 0.011819233, 0.00028885467, -0.01800852, 0.013744605, -0.009938559, -0.026409814, 0.047525313, 0.0025312796, -0.0073770294, 0.011113285, -0.0017481116, -0.006511514, 0.0075480426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, -1, 29, -1, -1], "loss_changes": [0.99162984, 1.1738411, 0.0, 1.3389099, 2.5187173, 2.7818007, 3.9135034, 0.0, 0.0, 1.5050874, 0.0, 4.6455865, 0.0, 1.50678, 0.0, 1.7177513, 0.0, 1.5005827, 0.0, 0.0, 0.0, 0.9440856, 0.0, 0.7789753, 1.184713, 0.0, 0.0, 0.0, 1.0423672, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 21, 21, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, -1, 30, -1, -1], "split_conditions": [1.6878304, 0.8711886, -0.009482616, 1.0, 1.0516728, 0.6888997, 0.65399134, 0.014440313, -0.0052699572, 0.535755, 0.014639127, 0.52329415, -0.019502945, 0.55767125, -0.00939494, 1.3047148, 0.018080851, 0.45340723, 0.011819233, 0.00028885467, -0.01800852, 0.21691436, -0.009938559, 0.13974896, 1.2634896, 0.0025312796, -0.0073770294, 0.011113285, 1.3047148, -0.006511514, 0.0075480426], "split_indices": [138, 139, 0, 105, 142, 139, 139, 0, 0, 139, 0, 139, 0, 143, 0, 138, 0, 140, 0, 0, 0, 139, 0, 139, 138, 0, 0, 0, 138, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1966.0, 109.0, 1697.0, 269.0, 1249.0, 448.0, 160.0, 109.0, 1115.0, 134.0, 312.0, 136.0, 949.0, 166.0, 208.0, 104.0, 837.0, 112.0, 116.0, 92.0, 696.0, 141.0, 318.0, 378.0, 152.0, 166.0, 165.0, 213.0, 117.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0013789403, -0.0076388842, 0.0073485835, -0.0015387374, 0.08313401, 0.0062846397, -0.009159326, 0.01725488, -0.0008990275, -0.0017268488, 0.010813507, 0.012645304, -0.08693807, 0.0057633244, 0.010359689, -0.026431834, 0.0047575296, 0.028183652, -0.021107158, 0.043436017, -0.0062827687, -0.011348664, 0.008685217, 0.014303523, 0.013598769, -0.005075638, 0.054917585, -0.0017521309, 0.007983913, 0.0144025255, -0.0021670798], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [0.9572749, 0.0, 1.2898091, 1.2075654, 1.6556922, 1.2867907, 0.0, 0.0, 0.0, 1.7904658, 0.0, 0.78303444, 5.0344644, 0.7006435, 0.0, 0.0, 0.0, 0.8800796, 1.4559162, 1.4640695, 0.0, 0.0, 1.0992503, 0.8613776, 0.0, 0.0, 1.5355382, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 22, 22, 23, 23, 26, 26], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, -0.0076388842, 1.0183754, 0.84173065, 1.1951114, 0.72836596, -0.009159326, 0.01725488, -0.0008990275, 0.6368173, 0.010813507, 0.65894747, 1.0, 0.3222687, 0.010359689, -0.026431834, 0.0047575296, 0.39922574, 0.39070654, 0.3186285, -0.0062827687, -0.011348664, 1.0, 1.0, 0.013598769, -0.005075638, 1.0, -0.0017521309, 0.007983913, 0.0144025255, -0.0021670798], "split_indices": [104, 0, 142, 142, 143, 142, 0, 0, 0, 139, 0, 140, 39, 143, 0, 0, 0, 141, 143, 141, 0, 0, 13, 83, 0, 0, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 147.0, 1915.0, 1714.0, 201.0, 1577.0, 137.0, 102.0, 99.0, 1462.0, 115.0, 1251.0, 211.0, 1163.0, 88.0, 91.0, 120.0, 634.0, 529.0, 543.0, 91.0, 129.0, 400.0, 413.0, 130.0, 175.0, 225.0, 278.0, 135.0, 104.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007240349, -0.02347737, 0.013429613, 0.0059046512, -0.107878685, -0.008822831, 0.10332243, -0.01667789, 0.012716311, -0.003562469, -0.018603092, 0.0052754753, -0.012173408, 0.02436157, -0.0012571978, 0.006080873, -0.04320864, 0.019309362, -0.01158041, -0.01150153, -0.00582169, -0.052610587, 0.048827816, -0.0058336197, 0.008286945, -0.0154023515, 0.0035924488, 0.021355588, 0.019494383, 0.10442546, -0.06962568, 0.02133224, -0.0039438372, -0.012957299, -0.0013204673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, -1, -1, -1, -1, 19, 21, -1, -1, 23, 25, 27, -1, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.663408, 1.9591064, 2.5404255, 1.6046582, 1.1519489, 1.6205112, 4.0973206, 1.0155544, 0.0, 0.0, 0.0, 1.5377911, 0.0, 0.0, 0.0, 0.0, 0.9879442, 1.7217251, 0.0, 0.0, 1.1271322, 2.1189504, 2.308125, 0.0, 0.0, 0.0, 0.0, 3.6579766, 0.0, 3.9635828, 0.7813078, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 16, 16, 17, 17, 20, 20, 21, 21, 22, 22, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, -1, -1, -1, -1, 20, 22, -1, -1, 24, 26, 28, -1, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 0.5076032, 0.9476538, 0.44216087, 0.6720697, 0.87664425, 1.0953796, 0.1464, 0.012716311, -0.003562469, -0.018603092, 1.3461539, -0.012173408, 0.02436157, -0.0012571978, 0.006080873, 1.2297175, 1.0, -0.01158041, -0.01150153, 0.7692308, 1.319978, 0.72836596, -0.0058336197, 0.008286945, -0.0154023515, 0.0035924488, 1.3243612, 0.019494383, 1.0, 1.0, 0.02133224, -0.0039438372, -0.012957299, -0.0013204673], "split_indices": [17, 142, 140, 139, 139, 142, 139, 139, 0, 0, 0, 1, 0, 0, 0, 0, 138, 5, 0, 0, 1, 138, 142, 0, 0, 0, 0, 138, 0, 80, 109, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 790.0, 1270.0, 586.0, 204.0, 1018.0, 252.0, 494.0, 92.0, 106.0, 98.0, 905.0, 113.0, 114.0, 138.0, 126.0, 368.0, 811.0, 94.0, 126.0, 242.0, 236.0, 575.0, 152.0, 90.0, 110.0, 126.0, 484.0, 91.0, 253.0, 231.0, 144.0, 109.0, 112.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037205697, 0.0014546665, -0.0080791805, -0.007066279, 0.0105923815, -0.0013883598, -0.011688236, -0.012409259, 0.011876254, -0.0009863024, -0.013797389, 0.0055819047, -0.009870666, 0.019525168, -0.044854235, 0.07531457, -0.021099068, 0.0008023552, -0.011870836, 0.0036065532, 0.14767446, 0.004843851, -0.012318342, 0.0071445117, -0.008222996, 0.004565638, 0.027467653, -0.0032486417, 0.011561073], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8240488, 1.723381, 0.0, 1.1161127, 0.0, 2.2537384, 0.0, 2.2361035, 0.0, 0.91720045, 0.0, 0.94164425, 0.0, 2.3774557, 1.1325207, 2.2934418, 1.6075586, 0.0, 0.0, 1.2927117, 2.850431, 2.0013196, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.289564, 1.0, -0.0080791805, 1.0728176, 0.0105923815, 0.8711886, -0.011688236, 0.74911517, 0.011876254, 1.0, -0.013797389, 1.0, -0.009870666, 0.1923077, 0.41860023, 0.40912068, 0.47377545, 0.0008023552, -0.011870836, 0.26820892, 1.0, 0.38534552, -0.012318342, 0.0071445117, -0.008222996, 0.004565638, 0.027467653, -0.0032486417, 0.011561073], "split_indices": [140, 125, 0, 143, 0, 139, 0, 141, 0, 84, 0, 7, 0, 1, 143, 140, 141, 0, 0, 140, 111, 143, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1936.0, 130.0, 1790.0, 146.0, 1702.0, 88.0, 1559.0, 143.0, 1429.0, 130.0, 1339.0, 90.0, 1049.0, 290.0, 442.0, 607.0, 169.0, 121.0, 222.0, 220.0, 484.0, 123.0, 124.0, 98.0, 122.0, 98.0, 362.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00652806, -0.0013015751, 0.050454073, 0.0072257374, -0.0142513905, 0.01313517, -0.010900853, -0.00020511118, 0.00990327, -0.010033027, 0.007852853, -0.0054207286, 0.008143061, 0.0033570211, -0.056548014, -0.003099516, 0.008605877, 0.001132338, -0.013480792, -0.026047654, 0.030700447, -0.059193507, 0.007557315, -0.0089262, 0.016843192, -0.012017866, -0.14187768, 0.009930718, -0.07843389, -0.006456382, 0.005040247, -0.00504549, -0.022945104, -0.014784371, -0.0022791282], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, 25, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7115803, 2.1145082, 1.553566, 1.1297294, 0.0, 0.0, 1.4235761, 0.6522958, 0.0, 0.0, 0.0, 0.646247, 0.0, 0.6562456, 1.12075, 0.8842367, 0.0, 0.0, 0.0, 2.2870812, 2.5160635, 1.9971472, 0.0, 2.6932526, 0.0, 1.0692592, 1.4891527, 0.0, 0.84194684, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 23, 23, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, 26, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9233974, 0.97098887, 1.0, 0.7704152, -0.0142513905, 0.01313517, -0.30769232, 3.6923077, 0.00990327, -0.010033027, 0.007852853, 2.0, 0.008143061, 0.69867027, 0.42225748, 1.0, 0.008605877, 0.001132338, -0.013480792, 1.0, 1.0, 1.0, 0.007557315, 1.2634896, 0.016843192, 1.0, 0.43840483, 0.009930718, 1.0, -0.006456382, 0.005040247, -0.00504549, -0.022945104, -0.014784371, -0.0022791282], "split_indices": [142, 140, 39, 140, 0, 0, 1, 1, 0, 0, 0, 0, 0, 141, 143, 39, 0, 0, 0, 83, 121, 121, 0, 138, 0, 13, 142, 0, 69, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1756.0, 313.0, 1656.0, 100.0, 135.0, 178.0, 1532.0, 124.0, 89.0, 89.0, 1440.0, 92.0, 1229.0, 211.0, 1140.0, 89.0, 113.0, 98.0, 679.0, 461.0, 512.0, 167.0, 358.0, 103.0, 326.0, 186.0, 140.0, 218.0, 177.0, 149.0, 91.0, 95.0, 97.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00021216115, -0.005941879, 0.0042475574, -0.008274911, 0.05321936, 0.0017136224, -0.014139314, 0.016359879, -0.013008284, -0.011372397, 0.016302636, 0.016124276, -0.013318144, 0.00082952646, -0.015542619, 0.012582304, -0.013628617, 0.002007546, 0.011269658, 0.01188884, -0.009130443, -0.013109999, 0.056491643, 0.002180527, -0.006691882, -0.0002062653, 0.021198586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [0.54657316, 0.0, 1.1805023, 2.038363, 2.8655872, 3.010203, 0.0, 0.0, 5.1303735, 2.318451, 0.0, 0.0, 0.0, 1.9595721, 0.0, 1.185726, 0.0, 0.9340296, 0.0, 1.0213568, 0.0, 1.1028262, 2.9954972, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, -0.005941879, 0.7922416, 0.88695586, 1.0, 0.72909546, -0.014139314, 0.016359879, 1.0, 0.67325324, 0.016302636, 0.016124276, -0.013318144, 0.59144264, -0.015542619, 0.556836, -0.013628617, 0.48259154, 0.011269658, 1.0, -0.009130443, 0.3188401, 0.39850324, 0.002180527, -0.006691882, -0.0002062653, 0.021198586], "split_indices": [104, 0, 143, 140, 69, 141, 0, 0, 39, 143, 0, 0, 0, 141, 0, 140, 0, 140, 0, 39, 0, 142, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 145.0, 1925.0, 1533.0, 392.0, 1426.0, 107.0, 147.0, 245.0, 1319.0, 107.0, 100.0, 145.0, 1216.0, 103.0, 1120.0, 96.0, 1013.0, 107.0, 916.0, 97.0, 587.0, 329.0, 356.0, 231.0, 239.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.006037835, -0.0019813995, 0.031797588, 0.006957674, -0.013374338, -0.059424024, 0.113157414, -0.0014403908, 0.01261756, -0.01274993, 0.005119826, 0.018962154, -0.0032897636, 0.011348542, -0.016475508, 0.022146592, -0.0058965804, 0.003415477, 0.06644083, 0.03200398, -0.051298838, 0.0216943, 0.006239942, 0.00981691, 0.002116885, -0.016756548, 0.00465255, 0.011103456, -0.009766656, -0.028617369, 0.009431968, 0.0008757271, -0.010210686], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.42636776, 1.8539047, 3.63667, 1.4757689, 0.0, 1.739579, 2.892505, 2.876031, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9695721, 0.0, 0.9184559, 0.0, 1.2169477, 2.9808593, 1.0104938, 3.0367804, 0.0, 2.5588777, 0.0, 0.9974922, 0.0, 0.0, 0.0, 0.0, 0.72511417, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.1095077, 1.0, 0.9782575, -0.013374338, 1.0, 1.0399631, 0.88695586, 0.01261756, -0.01274993, 0.005119826, 0.018962154, -0.0032897636, 1.0, -0.016475508, 0.5325338, -0.0058965804, 0.39922574, 0.55832404, 1.2186605, 0.46736357, 0.0216943, 0.60480964, 0.00981691, 0.34831125, -0.016756548, 0.00465255, 0.011103456, -0.009766656, 0.30958152, 0.009431968, 0.0008757271, -0.010210686], "split_indices": [61, 143, 17, 141, 0, 126, 141, 140, 0, 0, 0, 0, 0, 40, 0, 139, 0, 141, 142, 138, 141, 0, 140, 0, 141, 0, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1574.0, 490.0, 1474.0, 100.0, 231.0, 259.0, 1377.0, 97.0, 143.0, 88.0, 170.0, 89.0, 1277.0, 100.0, 1107.0, 170.0, 778.0, 329.0, 511.0, 267.0, 94.0, 235.0, 159.0, 352.0, 122.0, 145.0, 117.0, 118.0, 264.0, 88.0, 175.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00045930245, 0.005800461, -0.006552983, -0.0020264136, 0.013061489, 0.0062721707, -0.01036841, -0.0008227497, 0.0074464767, 0.0053617246, -0.0077225664, -0.0020047398, 0.008026396, 0.010763896, -0.010039321, -0.008294298, 0.06938857, 0.00779276, -0.010235172, 0.00406608, 0.02000335, -0.0016530504, 0.014525271, 0.009967803, -0.008357818], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1], "loss_changes": [0.7295887, 1.8707771, 0.0, 1.520194, 0.0, 0.806046, 0.0, 0.7130206, 0.0, 0.7702637, 0.0, 1.5967406, 0.0, 1.2569406, 0.0, 1.2846284, 2.3553977, 2.4240193, 0.0, 1.5418903, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1], "split_conditions": [1.6397896, 0.99982387, -0.006552983, 0.89191693, 0.013061489, 0.78044474, -0.01036841, 1.0, 0.0074464767, 0.7508028, -0.0077225664, 0.5887627, 0.008026396, 1.0, -0.010039321, 0.47496155, 0.47206855, 0.38907775, -0.010235172, 0.37522858, 0.02000335, -0.0016530504, 0.014525271, 0.009967803, -0.008357818], "split_indices": [138, 139, 0, 142, 0, 140, 0, 119, 0, 143, 0, 141, 0, 71, 0, 140, 140, 142, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1915.0, 155.0, 1802.0, 113.0, 1666.0, 136.0, 1509.0, 157.0, 1396.0, 113.0, 1271.0, 125.0, 1125.0, 146.0, 849.0, 276.0, 725.0, 124.0, 184.0, 92.0, 616.0, 109.0, 88.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011640295, -0.00877298, 0.041749466, -0.030350221, 0.013504329, -0.0037457289, 0.010659712, -0.07658466, 0.026145713, 0.04130849, -0.045316007, 0.0056644883, -0.11979424, -0.028081551, 0.020081462, 0.01757357, 0.011279854, 0.00044294074, -0.014359379, 0.0002625335, -0.19958559, 0.02661279, -0.013278218, 0.08511375, -0.10102537, -0.0066694357, 0.0075878142, -0.009667092, -0.032267958, -0.006301793, 0.009783722, -0.010130467, 0.030266022, 0.00032475335, -0.019487103, 0.0048332587, -0.008370198], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [0.67427754, 0.8431178, 1.597412, 2.3273435, 1.4113933, 0.0, 0.0, 2.8208287, 3.7981994, 2.3654854, 1.2456938, 0.0, 3.6141696, 1.7523191, 0.0, 0.0, 3.971836, 0.9571936, 0.0, 0.0, 2.8376703, 1.2831635, 0.0, 5.9880943, 1.8592566, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.86454445, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 16, 16, 17, 17, 20, 20, 21, 21, 23, 23, 24, 24, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 0.31766167, 1.0, 1.0, -0.0037457289, 0.010659712, 0.32927072, 0.68515503, 1.0, 0.49731833, 0.0056644883, -0.34615386, 0.4428979, 0.020081462, 0.01757357, 0.79743034, 0.27199793, -0.014359379, 0.0002625335, 1.0, 0.27657035, -0.013278218, 0.5338557, -0.3846154, -0.0066694357, 0.0075878142, -0.009667092, -0.032267958, -0.006301793, 0.009783722, 0.32048866, 0.030266022, 0.00032475335, -0.019487103, 0.0048332587, -0.008370198], "split_indices": [1, 124, 139, 115, 106, 0, 0, 140, 142, 81, 140, 0, 1, 141, 0, 0, 140, 139, 0, 0, 39, 143, 0, 143, 1, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1754.0, 311.0, 891.0, 863.0, 140.0, 171.0, 490.0, 401.0, 586.0, 277.0, 120.0, 370.0, 306.0, 95.0, 107.0, 479.0, 189.0, 88.0, 146.0, 224.0, 201.0, 105.0, 289.0, 190.0, 100.0, 89.0, 122.0, 102.0, 89.0, 112.0, 201.0, 88.0, 90.0, 100.0, 112.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013339001, -0.009090113, 0.042776424, 0.028692955, -0.023592409, -0.00932107, 0.017012589, 0.0012322413, 0.015211186, -0.008198077, -0.10481076, -0.0071506426, 0.00695325, -0.042085618, 0.005865361, -0.038482804, 0.03892755, -0.0022085824, -0.01747509, 0.00011477883, -0.010837688, -0.0018530727, -0.025324866, -0.016366169, 0.013026721, -0.05119564, 0.0989891, -0.007683109, 0.009105552, -0.009678223, -0.01731257, -0.00030458046, 0.02021453, -0.009397885, 0.007049578], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, 23, -1, -1, -1, -1, 25, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.7092335, 0.96602035, 2.0567226, 1.6573043, 1.5928849, 1.0787781, 0.0, 0.99494827, 0.0, 1.5285172, 1.1745155, 0.0, 0.0, 0.6534476, 0.0, 5.1291633, 2.1161637, 0.0, 0.0, 0.0, 0.0, 2.771527, 0.0, 1.6952587, 0.0, 1.8932705, 1.9261732, 0.0, 0.0, 1.8856825, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 16, 16, 21, 21, 23, 23, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, 24, -1, -1, -1, -1, 26, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.31497514, 0.46594483, 0.33549544, 1.0, 3.0384614, 0.017012589, 0.25118595, 0.015211186, 0.7960763, 1.0, -0.0071506426, 0.00695325, 0.1702571, 0.005865361, 0.8961227, 1.0, -0.0022085824, -0.01747509, 0.00011477883, -0.010837688, 0.5718726, -0.025324866, 1.0, 0.013026721, 0.602346, 1.0, -0.007683109, 0.009105552, 1.0, -0.01731257, -0.00030458046, 0.02021453, -0.009397885, 0.007049578], "split_indices": [1, 139, 139, 142, 83, 1, 0, 140, 0, 143, 122, 0, 0, 140, 0, 140, 12, 0, 0, 0, 0, 140, 0, 0, 0, 142, 109, 0, 0, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1763.0, 310.0, 489.0, 1274.0, 220.0, 90.0, 400.0, 89.0, 1071.0, 203.0, 123.0, 97.0, 228.0, 172.0, 652.0, 419.0, 93.0, 110.0, 138.0, 90.0, 557.0, 95.0, 261.0, 158.0, 374.0, 183.0, 167.0, 94.0, 279.0, 95.0, 92.0, 91.0, 136.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004754312, 0.00080839853, 0.00853404, -0.06004046, 0.007832058, 0.0041921735, -0.016101277, -0.00016734724, 0.01576876, 0.007637314, -0.012136917, -0.0014540766, 0.012828255, 0.005097378, -0.009648468, -0.008173816, 0.062435094, 0.009443646, -0.07702101, 0.016963148, 0.00023792707, -0.006218951, 0.010632441, -0.019657902, 0.0025040682, 0.0053450093, -0.0027695582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [0.66077405, 0.8466429, 0.0, 2.110549, 2.12899, 0.0, 0.0, 1.5948538, 0.0, 1.7373838, 0.0, 0.9170735, 0.0, 1.0485753, 0.0, 1.3572497, 1.667381, 1.3520077, 2.7821236, 0.0, 0.0, 0.9829028, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.2520225, -0.5, 0.00853404, 0.6725825, 1.1314709, 0.0041921735, -0.016101277, 1.5298582, 0.01576876, 0.9157179, -0.012136917, 0.76061165, 0.012828255, 1.0, -0.009648468, 1.0, 1.0, 0.5964612, 1.0, 0.016963148, 0.00023792707, 1.0, 0.010632441, -0.019657902, 0.0025040682, 0.0053450093, -0.0027695582], "split_indices": [142, 1, 0, 142, 139, 0, 0, 138, 0, 143, 0, 143, 0, 61, 0, 0, 23, 139, 106, 0, 0, 5, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1981.0, 97.0, 205.0, 1776.0, 102.0, 103.0, 1686.0, 90.0, 1584.0, 102.0, 1473.0, 111.0, 1378.0, 95.0, 1119.0, 259.0, 891.0, 228.0, 93.0, 166.0, 767.0, 124.0, 105.0, 123.0, 203.0, 564.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006769878, 0.0047358493, -0.0070571634, -0.0019276511, 0.012627812, -0.009632097, 0.011584028, 0.00072650245, -0.01151815, -0.006877764, 0.00784104, -0.08265076, 0.005399691, 0.0013766057, -0.015978424, -0.0028272455, 0.01088763, 0.0069088526, -0.011554966, -0.007835816, 0.01591636, 0.0016127265, -0.005778484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [0.78427476, 1.5582422, 0.0, 1.6549816, 0.0, 1.8718052, 0.0, 0.92094684, 0.0, 1.3210255, 0.0, 1.4725195, 1.0402836, 0.0, 0.0, 1.2423437, 0.0, 2.3392339, 0.0, 1.137086, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.2294465, 1.0, -0.0070571634, 1.0570872, 0.012627812, 0.84173065, 0.011584028, 3.0, -0.01151815, -0.3846154, 0.00784104, 0.457417, 0.78044474, 0.0013766057, -0.015978424, 0.63586605, 0.01088763, 0.59135294, -0.011554966, 0.37509307, 0.01591636, 0.0016127265, -0.005778484], "split_indices": [139, 102, 0, 143, 0, 142, 0, 0, 0, 1, 0, 142, 140, 0, 0, 140, 0, 139, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1924.0, 149.0, 1824.0, 100.0, 1712.0, 112.0, 1559.0, 153.0, 1420.0, 139.0, 198.0, 1222.0, 88.0, 110.0, 1132.0, 90.0, 1042.0, 90.0, 950.0, 92.0, 642.0, 308.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.004145449, -0.0080571305, 0.007432764, -0.0009488297, -0.01597547, -0.011859054, 0.07351343, -0.003443887, -0.01600807, 0.021700244, -0.0023818247, 0.0037226588, -0.0081072375, -0.005645678, 0.010797546, 0.005966463, -0.064028956, -0.0048017437, 0.01145276, -0.019282116, 0.005120613, 0.010296292, -0.0077449954, -0.00046729573, 0.013447304], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, -1, -1], "loss_changes": [0.6335689, 2.1199605, 0.0, 1.525687, 0.0, 2.043093, 3.3518467, 0.8623088, 0.0, 0.0, 0.0, 1.3859026, 0.0, 0.88269746, 0.0, 1.2695439, 3.2057383, 1.0836834, 0.0, 0.0, 0.0, 1.5205256, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, -1, -1], "split_conditions": [1.2604058, 1.2337983, 0.007432764, 0.82821655, -0.01597547, 0.81649506, 1.0, 0.6844557, -0.01600807, 0.021700244, -0.0023818247, 0.6718423, -0.0081072375, 0.5073127, 0.010797546, 0.5325338, 1.0, 0.4564035, 0.01145276, -0.019282116, 0.005120613, 0.43565965, -0.0077449954, -0.00046729573, 0.013447304], "split_indices": [141, 140, 0, 141, 0, 142, 39, 141, 0, 0, 0, 140, 0, 141, 0, 139, 39, 143, 0, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1966.0, 98.0, 1878.0, 88.0, 1638.0, 240.0, 1550.0, 88.0, 97.0, 143.0, 1419.0, 131.0, 1302.0, 117.0, 1086.0, 216.0, 988.0, 98.0, 102.0, 114.0, 818.0, 170.0, 730.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0075820345, -0.014775906, 0.03292959, -0.040286683, 0.011651245, -0.0035698868, 0.0089518, -0.08323571, 0.0132377315, 0.047568835, -0.05114733, 0.004147034, -0.12987095, -0.04295265, 0.019751806, 0.024133956, 0.0054634353, -0.02196479, -0.011269067, -0.0015156363, -0.21425234, 0.0029266889, -0.010670202, 0.04567901, -0.0088670775, -0.0071183476, 0.0034711238, -0.011498628, -0.033232667, -0.032930598, 0.024307651, 0.013155967, -0.014795013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.60297984, 1.1845292, 1.2116755, 2.055147, 1.94656, 0.0, 0.0, 2.8845856, 4.1212034, 4.4791775, 0.563941, 0.0, 3.4943986, 1.404205, 0.0, 0.0, 1.7073332, 0.59416777, 0.0, 0.0, 2.4379196, 0.0, 0.0, 4.9034805, 0.0, 0.0, 0.0, 0.0, 0.0, 4.2758284, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 16, 16, 17, 17, 20, 20, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 0.31766167, 1.0, 0.115384616, -0.0035698868, 0.0089518, 0.36329922, 0.68515503, 1.0, 0.41359463, 0.004147034, -0.34615386, -0.07692308, 0.019751806, 0.024133956, 1.0, 0.24535228, -0.011269067, -0.0015156363, 1.0, 0.0029266889, -0.010670202, 1.1168385, -0.0088670775, -0.0071183476, 0.0034711238, -0.011498628, -0.033232667, 0.5418762, 0.024307651, 0.013155967, -0.014795013], "split_indices": [1, 124, 139, 115, 1, 0, 0, 140, 142, 81, 141, 0, 1, 1, 0, 0, 97, 139, 0, 0, 39, 0, 0, 139, 0, 0, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1757.0, 312.0, 894.0, 863.0, 141.0, 171.0, 496.0, 398.0, 549.0, 314.0, 135.0, 361.0, 305.0, 93.0, 98.0, 451.0, 213.0, 101.0, 153.0, 208.0, 143.0, 162.0, 316.0, 135.0, 114.0, 99.0, 113.0, 95.0, 226.0, 90.0, 93.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00052026997, 0.006803998, -0.007657622, -0.00039749293, 0.015447505, -0.0067182328, 0.006629029, 0.0009904243, -0.0127512915, -0.04317619, 0.007712845, 0.0049925996, -0.011202489, 0.009800328, 0.0012381149, -0.010367667, 0.04563135, -0.027135758, 0.05763156, -0.00088437897, 0.015213333, -0.011017728, -0.014650988, -0.0056431084, 0.016182343, -0.0028173106, 0.010887712], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, -1, -1, -1, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [1.0028183, 2.035447, 0.0, 0.7692668, 0.0, 1.552252, 0.0, 0.46525246, 0.0, 1.3268629, 0.79506415, 0.0, 0.0, 0.0, 0.65381205, 1.1470584, 1.5258503, 1.5527287, 2.364996, 0.0, 0.0, 1.462414, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, -1, -1, -1, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.6397896, 1.562096, -0.007657622, 0.9271665, 0.015447505, 1.486252, 0.006629029, 1.1975179, -0.0127512915, 0.16273141, 1.2157785, 0.0049925996, -0.011202489, 0.009800328, 1.0, 1.0, 0.63267636, 0.7666822, 1.0, -0.00088437897, 0.015213333, 0.62448454, -0.014650988, -0.0056431084, 0.016182343, -0.0028173106, 0.010887712], "split_indices": [138, 138, 0, 140, 0, 138, 0, 138, 0, 142, 138, 0, 0, 0, 0, 58, 143, 143, 13, 0, 0, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1914.0, 156.0, 1825.0, 89.0, 1667.0, 158.0, 1567.0, 100.0, 207.0, 1360.0, 88.0, 119.0, 91.0, 1269.0, 1006.0, 263.0, 807.0, 199.0, 174.0, 89.0, 711.0, 96.0, 95.0, 104.0, 622.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0008103812, 0.0072534154, -0.04502647, 0.0002187889, 0.011371492, -0.014461808, 0.008745776, 0.008275578, -0.009841391, -5.307112e-05, 0.014028022, 0.008843897, -0.010420898, -0.0018315249, 0.008446149, 0.009733269, -0.013824637, 0.00039429122, 0.009269028, 0.0065109045, -0.006106071, -0.0030266542, 0.002007713], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, 21, -1, -1], "loss_changes": [0.6086717, 1.3532926, 3.351357, 1.3469532, 0.0, 0.0, 0.0, 1.7227921, 0.0, 1.3659146, 0.0, 1.0962452, 0.0, 1.8773558, 0.0, 0.84988254, 0.0, 0.41478017, 0.0, 0.0, 0.5668083, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 20, 20], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, 22, -1, -1], "split_conditions": [3.0, 1.2532037, 1.0, 1.016993, 0.011371492, -0.014461808, 0.008745776, 1.5125633, -0.009841391, 0.7322595, 0.014028022, 0.59135294, -0.010420898, 0.54254174, 0.008446149, 0.48429644, -0.013824637, 0.09660301, 0.009269028, 0.0065109045, 1.0, -0.0030266542, 0.002007713], "split_indices": [0, 143, 122, 140, 0, 0, 0, 138, 0, 139, 0, 139, 0, 139, 0, 139, 0, 139, 0, 0, 17, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1807.0, 254.0, 1695.0, 112.0, 145.0, 109.0, 1567.0, 128.0, 1474.0, 93.0, 1358.0, 116.0, 1190.0, 168.0, 1097.0, 93.0, 986.0, 111.0, 90.0, 896.0, 466.0, 430.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.0027162407, 0.006624706, -0.0074900324, 0.014523519, -0.0478422, 0.004198058, 0.016391711, -0.01333146, 0.0020907316, -0.0035460857, 0.013018615, 0.0077551007, -0.016852014, 0.023297487, -0.07628629, 0.011509578, 0.015673174, 0.0009595151, -0.013985211, 0.01993076, -0.006648471, 0.0016673424, 0.09448295, 0.0027355114, -0.009582237, 0.018765451, -0.0011998793], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [0.6264416, 0.8458202, 0.0, 2.6485708, 1.4631706, 1.5669264, 0.0, 0.0, 0.0, 2.8208416, 0.0, 1.8495843, 0.0, 1.879628, 1.206467, 0.7211709, 0.0, 0.0, 0.0, 1.3493234, 0.0, 1.9934175, 1.9346092, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, 3.0, -0.0074900324, 1.1254623, 1.0, 1.562096, 0.016391711, -0.01333146, 0.0020907316, 0.86804646, 0.013018615, 1.0, -0.016852014, 0.78044474, 0.38341978, 1.41507, 0.015673174, 0.0009595151, -0.013985211, 0.49155742, -0.006648471, 0.39922574, 1.0, 0.0027355114, -0.009582237, 0.018765451, -0.0011998793], "split_indices": [52, 0, 0, 142, 59, 138, 0, 0, 0, 142, 0, 113, 0, 140, 140, 138, 0, 0, 0, 141, 0, 141, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1966.0, 99.0, 1717.0, 249.0, 1606.0, 111.0, 111.0, 138.0, 1513.0, 93.0, 1416.0, 97.0, 1195.0, 221.0, 1098.0, 97.0, 94.0, 127.0, 991.0, 107.0, 796.0, 195.0, 630.0, 166.0, 104.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0049054343, 0.002408686, -0.06361089, -0.004860427, 0.008151932, -0.0154380305, 0.008079498, 0.00046849437, -0.009988291, 0.0059614247, -0.008777274, -0.002525487, 0.013835727, 0.006343461, -0.013356673, -0.0045118537, 0.06546237, 0.009342983, -0.07762157, -0.004433311, 0.015565154, -0.004033854, 0.009837573, -0.003204725, -0.012371375, -0.0044566016, 0.0051668235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [0.8836617, 1.0523676, 2.9885418, 0.84867203, 0.0, 0.0, 0.0, 0.7692232, 0.0, 1.6787065, 0.0, 1.6317263, 0.0, 0.8439073, 0.0, 1.1253581, 2.0200825, 1.1123724, 0.37180924, 0.0, 0.0, 1.8332736, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.5727334, 0.9271665, 1.0, 0.89191693, 0.008151932, -0.0154380305, 0.008079498, 0.8916121, -0.009988291, 0.73725533, -0.008777274, 0.6555352, 0.013835727, 0.5919221, -0.013356673, 0.4991331, 0.50570595, 0.44437784, 0.45957798, -0.004433311, 0.015565154, 1.0, 0.009837573, -0.003204725, -0.012371375, -0.0044566016, 0.0051668235], "split_indices": [138, 140, 12, 142, 0, 0, 0, 143, 0, 141, 0, 141, 0, 143, 0, 140, 140, 141, 139, 0, 0, 115, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1830.0, 228.0, 1676.0, 154.0, 140.0, 88.0, 1587.0, 89.0, 1494.0, 93.0, 1404.0, 90.0, 1315.0, 89.0, 1111.0, 204.0, 934.0, 177.0, 92.0, 112.0, 812.0, 122.0, 89.0, 88.0, 470.0, 342.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0015943011, 0.007940016, -0.008251922, -0.0003418943, 0.013342142, 0.0077429647, -0.03450922, 0.019874306, -0.02912152, -0.013594341, 0.00075500173, 0.046864357, -0.023296975, -0.09835402, 0.004611378, 0.00500932, -0.0057860436, -0.005041143, 0.07683888, -0.0605967, 0.0103366645, -0.0038508985, -0.015948601, 0.03701664, 0.02054451, -0.018601526, -0.005101753, 0.084417224, -0.0066194288, -0.0076517784, 0.0060262047, 0.0017843392, 0.018741822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, -1, -1, 23, 25, -1, -1, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.1032826, 1.9973922, 0.0, 0.49805722, 0.0, 0.6520404, 1.2340647, 1.2782191, 1.8803508, 0.0, 0.7403469, 1.968162, 1.9937468, 0.6877873, 0.0, 0.0, 0.0, 0.0, 2.6426353, 2.268991, 0.0, 0.0, 0.0, 1.9275498, 0.0, 0.0, 1.0549734, 1.8514355, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 18, 18, 19, 19, 23, 23, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, -1, -1, 24, 26, -1, -1, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.2294465, 0.99982387, -0.008251922, 1.0, 0.013342142, 1.0, -0.3846154, 0.46153846, 0.42307693, -0.013594341, 0.42792067, 1.0, 1.0, 0.2993035, 0.004611378, 0.00500932, -0.0057860436, -0.005041143, 1.0, 0.9230769, 0.0103366645, -0.0038508985, -0.015948601, 1.0, 0.02054451, -0.018601526, 0.33214927, 0.55767125, -0.0066194288, -0.0076517784, 0.0060262047, 0.0017843392, 0.018741822], "split_indices": [139, 139, 0, 7, 0, 116, 1, 1, 1, 0, 140, 89, 0, 143, 0, 0, 0, 0, 42, 1, 0, 0, 0, 121, 0, 0, 139, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1922.0, 145.0, 1803.0, 119.0, 1458.0, 345.0, 1097.0, 361.0, 89.0, 256.0, 675.0, 422.0, 188.0, 173.0, 139.0, 117.0, 159.0, 516.0, 326.0, 96.0, 95.0, 93.0, 394.0, 122.0, 100.0, 226.0, 270.0, 124.0, 108.0, 118.0, 164.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037821822, 0.0015246731, -0.0072237076, -0.0045437166, 0.0084490245, -0.012846967, 0.008127403, -0.0036374144, -0.015868068, 0.00810179, -0.04143508, -0.0053941365, 0.013096129, -0.017744254, 0.004567431, 0.004560538, -0.009749907, -0.006399836, 0.009817466, -0.007020651, 0.010225534, -0.030080441, 0.04378023, 0.0010983375, -0.009692425, -0.0025026286, 0.008522996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [0.75235355, 0.96766454, 0.0, 1.276206, 0.0, 2.193222, 0.0, 0.6815457, 0.0, 1.9432968, 2.277434, 0.9682199, 0.0, 0.0, 1.7457651, 1.0782456, 0.0, 0.0, 0.0, 0.9980819, 0.0, 1.6084893, 0.7586352, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.2294465, 1.0570872, -0.0072237076, 0.8711886, 0.0084490245, 0.83346164, 0.008127403, 0.54411215, -0.015868068, 0.48429644, 1.0, 0.50989574, 0.013096129, -0.017744254, 0.6904005, 0.44610116, -0.009749907, -0.006399836, 0.009817466, 0.88461536, 0.010225534, 0.31487146, 1.0, 0.0010983375, -0.009692425, -0.0025026286, 0.008522996], "split_indices": [139, 143, 0, 139, 0, 142, 0, 139, 0, 139, 81, 141, 0, 0, 143, 141, 0, 0, 0, 1, 0, 140, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1922.0, 149.0, 1791.0, 131.0, 1633.0, 158.0, 1536.0, 97.0, 1172.0, 364.0, 1056.0, 116.0, 92.0, 272.0, 953.0, 103.0, 157.0, 115.0, 852.0, 101.0, 586.0, 266.0, 363.0, 223.0, 100.0, 166.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027018148, 0.003136169, -0.060036976, -0.004994529, 0.009103007, -0.017247582, 0.0041157957, 0.0043332577, -0.05098571, -0.003979363, 0.00960539, 0.0027932774, -0.01106558, 0.008138993, -0.008253976, -0.0009354791, 0.008581034, 0.01356449, -0.043419834, -0.0026962121, 0.010566053, 0.0044171587, -0.0092258705, -0.008742585, 0.012073169, -0.004969876, 0.0030841203], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, 25, -1, -1], "loss_changes": [0.6881881, 1.3335161, 2.1618664, 0.73272526, 0.0, 0.0, 0.0, 1.0826637, 1.3562132, 1.2395339, 0.0, 0.0, 0.0, 0.7950445, 0.0, 0.6221822, 0.0, 1.1276526, 1.099412, 0.80089915, 0.0, 0.0, 0.0, 0.0, 0.6318389, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, 26, -1, -1], "split_conditions": [1.0902765, 0.9233974, 1.2604058, 0.67420596, 0.009103007, -0.017247582, 0.0041157957, 1.4352999, 1.0, 0.54254174, 0.00960539, 0.0027932774, -0.01106558, 0.52807486, -0.008253976, 0.37509307, 0.008581034, 0.39586842, 0.378754, 1.0, 0.010566053, 0.0044171587, -0.0092258705, -0.008742585, -0.15384616, -0.004969876, 0.0030841203], "split_indices": [141, 142, 141, 142, 0, 0, 0, 138, 69, 139, 0, 0, 0, 142, 0, 139, 0, 142, 142, 89, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1866.0, 190.0, 1708.0, 158.0, 90.0, 100.0, 1420.0, 288.0, 1302.0, 118.0, 124.0, 164.0, 1128.0, 174.0, 1010.0, 118.0, 753.0, 257.0, 640.0, 113.0, 92.0, 165.0, 95.0, 545.0, 127.0, 418.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0037423705, -0.003980654, 0.044416975, 0.004424692, -0.0134703815, 0.015358669, -0.009426896, -0.0031312797, 0.012539898, -0.009428088, 0.01095531, 0.009718786, -0.065953836, -0.0036997383, 0.07593765, -0.016121993, 0.0005655563, 0.013567026, -0.051168095, 0.016546706, 0.0013901507, -0.003321092, 0.008545565, -0.014643328, 0.0008509501, 0.006789318, -0.017727038, 0.011705063, -0.0735017, -0.0053789127, 0.0041399477, -0.012071878, -0.0026803454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 112, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1], "loss_changes": [0.649623, 1.9096688, 1.93978, 1.4926902, 0.0, 0.0, 2.2311997, 1.2407801, 0.0, 0.0, 0.0, 1.1338022, 1.7805287, 0.86962223, 1.1941224, 0.0, 0.0, 0.9445417, 1.6089114, 0.0, 0.0, 0.6463224, 0.0, 0.0, 0.0, 0.0, 0.86018056, 0.66707027, 0.3990963, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1], "split_conditions": [0.90107125, 0.96184605, 1.0516728, 0.78377116, -0.0134703815, 0.015358669, 1.2635074, 0.6151461, 0.012539898, -0.009428088, 0.01095531, 0.52807486, 1.0, 0.39922574, 1.3243612, -0.016121993, 0.0005655563, 0.34326252, 0.44610116, 0.016546706, 0.0013901507, 1.1749831, 0.008545565, -0.014643328, 0.0008509501, 0.006789318, 0.29444343, 1.0, 0.2997789, -0.0053789127, 0.0041399477, -0.012071878, -0.0026803454], "split_indices": [142, 140, 142, 142, 0, 0, 139, 139, 0, 0, 0, 142, 39, 141, 138, 0, 0, 141, 141, 0, 0, 138, 0, 0, 0, 0, 140, 53, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1738.0, 330.0, 1633.0, 105.0, 109.0, 221.0, 1537.0, 96.0, 129.0, 92.0, 1276.0, 261.0, 1061.0, 215.0, 112.0, 149.0, 778.0, 283.0, 88.0, 127.0, 630.0, 148.0, 109.0, 174.0, 106.0, 524.0, 343.0, 181.0, 107.0, 236.0, 90.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.004575112, -0.020266432, 0.011025418, -0.0062951637, -0.009306851, 0.011985962, -0.0025493442, -0.020181684, 0.005131734, -0.044193648, 0.030945385, 0.0022866917, -0.07448027, 0.0067066737, -0.077642195, 0.07301781, -0.00827721, -0.018118694, 0.007589868, -0.0017797606, -0.012104101, -0.01689087, -0.015503281, -0.0005361377, 0.02359779, 0.014076805, -0.009305652, 0.0109451255, -0.013025749, 0.017469436, -0.009336661, -0.0015120989, 0.005283942], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 113, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, 13, 15, 17, 19, -1, 21, 23, -1, 25, -1, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5062318, 1.0486684, 1.5320625, 0.6920321, 0.0, 0.0, 1.2860653, 0.85034096, 0.0, 1.5295358, 2.4448137, 0.740526, 0.5383936, 0.0, 1.792099, 4.470912, 0.0, 0.93128735, 0.0, 0.0, 0.0, 0.0, 2.695743, 4.180549, 0.0, 0.3055815, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 17, 17, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, 14, 16, 18, 20, -1, 22, 24, -1, 26, -1, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.57407886, 0.40983817, 0.40983817, -0.009306851, 0.011985962, 0.7014454, 1.0, 0.005131734, 0.37522858, 1.1373166, 0.37027234, 0.23689131, 0.0067066737, 0.53717035, 0.9677354, -0.00827721, 1.0, 0.007589868, -0.0017797606, -0.012104101, -0.01689087, 1.0, 0.68301666, 0.02359779, 0.22819103, -0.009305652, 0.0109451255, -0.013025749, 0.017469436, -0.009336661, -0.0015120989, 0.005283942], "split_indices": [71, 143, 142, 142, 0, 0, 143, 15, 0, 143, 142, 140, 142, 0, 140, 141, 0, 83, 0, 0, 0, 0, 13, 142, 0, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1031.0, 1037.0, 865.0, 166.0, 115.0, 922.0, 697.0, 168.0, 411.0, 511.0, 493.0, 204.0, 95.0, 316.0, 373.0, 138.0, 386.0, 107.0, 92.0, 112.0, 128.0, 188.0, 257.0, 116.0, 270.0, 116.0, 90.0, 98.0, 89.0, 168.0, 154.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004690993, 0.010937167, -0.040963765, 0.0035089338, 0.0136051895, 0.0050515826, -0.013619254, -0.021767786, 0.0249262, -0.004326053, -0.013277543, 0.017215448, 0.00915174, -0.050754156, 0.058649078, -0.044356965, 0.04718543, -0.09869795, 0.007532025, -0.0024408193, 0.017901177, 0.0042615277, -0.12234839, 0.017106129, 0.0011645067, -0.0018910194, -0.02077412, -0.0034084946, -0.021453463, -0.08598877, 0.014175485, -1.4413885e-05, -0.015438527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.59001166, 1.691474, 2.169161, 0.9300536, 0.0, 0.0, 0.0, 1.5256983, 2.159875, 1.9911186, 0.0, 0.0, 1.7095122, 2.369439, 2.8891323, 2.3672986, 2.7991319, 2.4708893, 0.0, 0.0, 0.0, 0.0, 1.4971473, 0.0, 4.3865414, 0.0, 0.0, 0.0, 0.0, 1.2975953, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.3461539, 1.1538461, 0.34591028, 1.0, 0.0136051895, 0.0050515826, -0.013619254, 1.3909502, 0.4140956, 1.0, -0.013277543, 0.017215448, 0.6951184, 1.2996999, 1.0, 1.0, 0.8362515, 0.29337478, 0.007532025, -0.0024408193, 0.017901177, 0.0042615277, 0.547418, 0.017106129, 1.0, -0.0018910194, -0.02077412, -0.0034084946, -0.021453463, 1.5298582, 0.014175485, -1.4413885e-05, -0.015438527], "split_indices": [1, 1, 143, 71, 0, 0, 0, 138, 142, 12, 0, 0, 143, 138, 83, 53, 143, 141, 0, 0, 0, 0, 143, 0, 12, 0, 0, 0, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1820.0, 249.0, 1718.0, 102.0, 127.0, 122.0, 788.0, 930.0, 681.0, 107.0, 90.0, 840.0, 392.0, 289.0, 349.0, 491.0, 284.0, 108.0, 171.0, 118.0, 165.0, 184.0, 133.0, 358.0, 164.0, 120.0, 94.0, 90.0, 221.0, 137.0, 98.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002899286, 0.0035416565, -0.059707806, -0.001469317, 0.008330728, -0.016486779, 0.0015528583, 0.006130108, -0.014508123, -0.0031085094, 0.008468477, 0.015304718, -0.086929925, -0.0025807614, 0.11797331, 0.00025072356, -0.02023574, 0.010365977, -0.047119766, 0.0031839875, 0.02070101, -0.0072323834, 0.01501752, 0.0028783823, -0.014707205, 0.0042074127, -0.0065110023, -0.0013127626, 0.007532554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [0.7581458, 0.7438483, 1.6694024, 1.9109856, 0.0, 0.0, 0.0, 1.2069, 0.0, 2.2966132, 0.0, 2.2402585, 2.7667007, 0.5991237, 1.3880966, 0.0, 0.0, 1.9806329, 1.7752968, 0.0, 0.0, 0.47340763, 0.0, 0.0, 0.0, 0.7360031, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0558407, 1.0, 1.1936536, 1.0199403, 0.008330728, -0.016486779, 0.0015528583, 3.0, -0.014508123, 0.67325324, 0.008468477, 0.56020635, 0.7066865, 0.45463473, 0.5276414, 0.00025072356, -0.02023574, 0.40983817, 1.3659338, 0.0031839875, 0.02070101, 1.3100263, 0.01501752, 0.0028783823, -0.014707205, 0.30109692, -0.0065110023, -0.0013127626, 0.007532554], "split_indices": [141, 125, 141, 143, 0, 0, 0, 0, 0, 143, 0, 143, 142, 142, 139, 0, 0, 142, 138, 0, 0, 138, 0, 0, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1861.0, 211.0, 1751.0, 110.0, 88.0, 123.0, 1663.0, 88.0, 1488.0, 175.0, 1220.0, 268.0, 1039.0, 181.0, 151.0, 117.0, 805.0, 234.0, 92.0, 89.0, 715.0, 90.0, 133.0, 101.0, 597.0, 118.0, 480.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0018498197, -0.0030388217, 0.00947815, 0.0035165027, -0.013159222, -0.006092863, 0.09227116, 0.00420314, -0.06447775, 0.017777625, 0.0012223809, 0.0144637255, -0.05569117, -5.9217564e-05, -0.01615838, -0.011022958, 0.04267956, 0.0048270524, -0.016268091, 0.030933965, -0.079742536, 0.10868373, -0.02978153, -0.010684579, 0.013211011, -0.01453986, -0.0028086647, 0.1633671, -0.0014794817, -0.08904878, 0.008684109, -0.008355563, 0.0035918977, -0.00019478971, 0.029225677, 0.00095123023, -0.01844305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, -1, -1, 23, 25, 27, 29, 31, -1, -1, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.93633246, 1.650025, 0.0, 1.5889082, 0.0, 1.0105015, 1.2456906, 0.87819284, 1.5633259, 0.0, 0.0, 0.8773363, 2.3246727, 0.0, 0.0, 1.8481712, 2.7692032, 0.0, 0.0, 1.6759002, 0.8241396, 2.045924, 1.9076848, 0.95768625, 0.0, 0.0, 0.0, 4.474555, 0.0, 1.7203703, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 27, 27, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, -1, -1, 24, 26, 28, 30, 32, -1, -1, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3494815, 1.6290085, 0.00947815, 0.8711886, -0.013159222, 2.0, 0.87239504, 1.3461539, 1.0, 0.017777625, 0.0012223809, 0.41479585, 0.378754, -5.9217564e-05, -0.01615838, 0.31252044, 0.66754913, 0.0048270524, -0.016268091, 0.30246237, 1.0, 1.0, 0.9157179, 1.1945249, 0.013211011, -0.01453986, -0.0028086647, 0.48429644, -0.0014794817, 0.66038257, 0.008684109, -0.008355563, 0.0035918977, -0.00019478971, 0.029225677, 0.00095123023, -0.01844305], "split_indices": [140, 138, 0, 139, 0, 0, 143, 1, 12, 0, 0, 143, 142, 0, 0, 139, 143, 0, 0, 140, 124, 7, 143, 138, 0, 0, 0, 139, 0, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1958.0, 103.0, 1863.0, 95.0, 1681.0, 182.0, 1429.0, 252.0, 88.0, 94.0, 1220.0, 209.0, 152.0, 100.0, 641.0, 579.0, 106.0, 103.0, 398.0, 243.0, 303.0, 276.0, 282.0, 116.0, 107.0, 136.0, 210.0, 93.0, 183.0, 93.0, 110.0, 172.0, 92.0, 118.0, 90.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025616072, 0.0074746865, -0.006452841, -0.0011277385, -0.009453289, -0.01033078, 0.015213827, -0.020268148, 0.058323264, -0.008069564, -0.1062422, 0.016692942, -0.002488305, 0.031753246, -0.021706963, 0.0013155247, -0.022563968, -0.044706013, 0.020142002, -0.008284288, -0.011599304, 0.0021248225, -0.012941198, 0.0054095103, -0.009929354, -0.009703421, 0.009538746, 0.0014100485, -0.012016192], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, 23, -1, -1, -1, 25, -1, 27, -1, -1, -1], "loss_changes": [0.61518764, 0.0, 0.9132115, 2.5897024, 0.0, 1.1816405, 0.0, 1.586776, 1.979042, 0.71958035, 2.6800828, 0.0, 0.0, 4.384738, 1.2491186, 0.0, 0.0, 1.3017055, 0.0, 1.0767711, 0.0, 0.0, 0.0, 1.0212331, 0.0, 1.6906683, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 19, 19, 23, 23, 25, 25], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, 24, -1, -1, -1, 26, -1, 28, -1, -1, -1], "split_conditions": [-0.5769231, 0.0074746865, 1.289564, 1.0933275, -0.009453289, 0.8026405, 0.015213827, 0.6887748, 1.0, 0.0, 0.7425969, 0.016692942, -0.002488305, 0.46812254, 0.6000145, 0.0013155247, -0.022563968, 0.31487146, 0.020142002, 1.0, -0.011599304, 0.0021248225, -0.012941198, 0.49837223, -0.009929354, 0.44589856, 0.009538746, 0.0014100485, -0.012016192], "split_indices": [1, 0, 140, 142, 0, 143, 0, 141, 39, 0, 140, 0, 0, 140, 142, 0, 0, 140, 0, 40, 0, 0, 0, 142, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 98.0, 1947.0, 1836.0, 111.0, 1732.0, 104.0, 1513.0, 219.0, 1325.0, 188.0, 95.0, 124.0, 338.0, 987.0, 94.0, 94.0, 233.0, 105.0, 864.0, 123.0, 131.0, 102.0, 751.0, 113.0, 643.0, 108.0, 529.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.002940702, -0.0007754988, 0.0070038806, 0.0056682653, -0.009732636, -0.0021046477, 0.009262128, 0.0028229, -0.007986139, -0.0027534936, 0.0067642923, 0.009710646, -0.082689345, -0.0060008103, 0.049946226, -0.0016192313, -0.015633656, 0.0032202078, -0.008452509, -0.0007001155, 0.092025585, -0.013603005, 0.049115803, -0.00081323006, 0.019120157, 0.005482521, -0.005738949, -0.0030063663, 0.01119359], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5131625, 1.2131946, 0.0, 1.2355057, 0.0, 0.64292604, 0.0, 0.5703872, 0.0, 1.4476701, 0.0, 0.7946247, 0.9598743, 0.654563, 0.8458976, 0.0, 0.0, 0.6246382, 0.0, 0.0, 2.0164518, 1.7737687, 1.0793717, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3466678, 1.5727334, 0.0070038806, 0.9476538, -0.009732636, 1.4922987, 0.009262128, 0.78377116, -0.007986139, 0.6151461, 0.0067642923, 0.44982666, 1.0, 0.41246155, 1.3173673, -0.0016192313, -0.015633656, 1.0, -0.008452509, -0.0007001155, 1.0, 1.0, 1.0, -0.00081323006, 0.019120157, 0.005482521, -0.005738949, -0.0030063663, 0.01119359], "split_indices": [139, 138, 0, 140, 0, 138, 0, 142, 0, 139, 0, 142, 121, 142, 138, 0, 0, 93, 0, 0, 111, 122, 122, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1950.0, 108.0, 1828.0, 122.0, 1678.0, 150.0, 1578.0, 100.0, 1453.0, 125.0, 1257.0, 196.0, 904.0, 353.0, 103.0, 93.0, 809.0, 95.0, 150.0, 203.0, 592.0, 217.0, 101.0, 102.0, 231.0, 361.0, 96.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0019419829, -0.015978621, 0.01328934, -0.039783414, 0.014533927, 0.0874709, 0.0009029328, -0.005028796, -0.020225417, 0.014341113, 0.003453817, -0.027870914, 0.030868923, -0.030396082, 0.011121738, 0.004826119, -0.061376978, -0.0030582761, 0.018859433, -0.08173537, 0.025486497, -0.014082562, -0.015596579, 0.027289001, -0.008703655, 0.0022560002, -0.017632889, -0.0057161395, 0.008991283, 0.0035672092, -0.005039003, -0.02635024, 0.015115698, 0.004920553, -0.010056877], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, 21, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.42012417, 3.075951, 1.1623362, 3.9413323, 0.0, 0.5359529, 0.9346647, 1.6955884, 0.0, 0.0, 0.0, 1.4106407, 2.841478, 1.354155, 0.0, 0.0, 1.7178322, 1.1136998, 0.0, 2.4269538, 1.2033824, 0.4624552, 0.0, 2.1327834, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2561109, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, 22, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [-0.1923077, 1.2135563, -0.03846154, 0.8509382, 0.014533927, 0.61421114, 1.0, 0.6718423, -0.020225417, 0.014341113, 0.003453817, 1.0, 0.5981277, 1.0, 0.011121738, 0.004826119, 0.61088866, 0.37509307, 0.018859433, -0.42307693, 1.0, 0.27870917, -0.015596579, 0.32253915, -0.008703655, 0.0022560002, -0.017632889, -0.0057161395, 0.008991283, 0.0035672092, -0.005039003, 0.18455012, 0.015115698, 0.004920553, -0.010056877], "split_indices": [1, 143, 1, 140, 0, 140, 122, 140, 0, 0, 0, 126, 139, 69, 0, 0, 143, 139, 0, 1, 13, 143, 0, 141, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 801.0, 1265.0, 698.0, 103.0, 181.0, 1084.0, 575.0, 123.0, 88.0, 93.0, 553.0, 531.0, 472.0, 103.0, 169.0, 384.0, 437.0, 94.0, 246.0, 226.0, 256.0, 128.0, 321.0, 116.0, 117.0, 129.0, 99.0, 127.0, 108.0, 148.0, 224.0, 97.0, 111.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0016153613, 0.0064668935, -0.04174519, -0.0027951591, 0.0666329, -0.014161455, 0.0035930963, 0.003901714, -0.008600383, 0.01943252, -0.01101718, -0.006247307, 0.016377734, 0.0038104833, -0.010635378, -0.0047035785, 0.008420559, 0.0037854544, -0.007086622, 0.010717397, -0.0061502093, 0.00821187, 0.0030029668, 0.0025850807, -0.003737925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, 23, -1, -1], "loss_changes": [0.43482456, 1.0359478, 1.6135538, 0.89771056, 5.598997, 0.0, 0.0, 2.419269, 0.0, 0.0, 0.0, 1.4116038, 0.0, 0.8720391, 0.0, 0.64702886, 0.0, 0.4620733, 0.0, 0.50840694, 0.0, 0.0, 0.76856446, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 22, 22], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, 24, -1, -1], "split_conditions": [1.0558407, 0.7869461, 1.1765813, 0.82424533, 1.0, -0.014161455, 0.0035930963, 0.8105995, -0.008600383, 0.01943252, -0.01101718, 0.6318365, 0.016377734, 0.5981277, -0.010635378, 0.5786564, 0.008420559, 1.0, -0.007086622, 0.13091123, -0.0061502093, 0.00821187, 1.0, 0.0025850807, -0.003737925], "split_indices": [141, 141, 140, 143, 126, 0, 0, 140, 0, 0, 0, 140, 0, 139, 0, 143, 0, 43, 0, 141, 0, 0, 109, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1859.0, 208.0, 1611.0, 248.0, 91.0, 117.0, 1491.0, 120.0, 144.0, 104.0, 1402.0, 89.0, 1274.0, 128.0, 1152.0, 122.0, 1021.0, 131.0, 923.0, 98.0, 90.0, 833.0, 532.0, 301.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014836137, 0.002267609, -0.0068833577, -0.0040526106, 0.05091927, 0.0022131077, -0.010114144, -0.0025352852, 0.014799291, 0.010570597, -0.010976243, 0.0010418743, 0.013714077, -0.011003981, 0.054480836, 0.0047322973, -0.011826762, -0.0050847176, 0.011153353, -0.0042765103, 0.009714852, -0.060640194, 0.012560433, -0.0025989409, -0.010867875, -0.00070290215, 0.007850756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 121, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [0.5219642, 0.60175645, 0.0, 1.0536301, 1.6659032, 1.5226023, 0.0, 0.0, 0.0, 1.8259624, 0.0, 0.90635514, 0.0, 1.9394326, 1.5563952, 0.83422536, 0.0, 0.0, 0.0, 0.86643004, 0.0, 0.34956008, 0.9081837, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.6878304, 0.9476538, -0.0068833577, 0.8922892, -0.07692308, 0.81785303, -0.010114144, -0.0025352852, 0.014799291, 0.73725533, -0.010976243, 0.5653381, 0.013714077, 0.48244315, 0.5919221, 0.44630072, -0.011826762, -0.0050847176, 0.011153353, 0.15268248, 0.009714852, 0.16381486, 1.0, -0.0025989409, -0.010867875, -0.00070290215, 0.007850756], "split_indices": [138, 140, 0, 141, 1, 139, 0, 0, 0, 141, 0, 142, 0, 142, 143, 142, 0, 0, 0, 143, 0, 142, 116, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1957.0, 109.0, 1732.0, 225.0, 1627.0, 105.0, 126.0, 99.0, 1514.0, 113.0, 1408.0, 106.0, 1149.0, 259.0, 1002.0, 147.0, 91.0, 168.0, 913.0, 89.0, 210.0, 703.0, 122.0, 88.0, 542.0, 161.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009670977, 0.0028672016, -0.007571698, -0.008713823, 0.03136182, 0.0029399288, -0.015701355, 0.05878275, -0.01108097, -0.005026602, 0.0098379, 0.021785902, 0.0070829527, 0.006256664, -0.010017921, 0.068634294, -0.012075442, -0.005827506, 0.012623523, -0.0056238887, 0.019875428, 0.0053222184, -0.008740159, 0.023942266, -0.031787656, -0.010428316, 0.07512292, 0.0018778549, -0.011150918, 0.0033648168, -0.008854764, -0.00092181045, 0.01764944], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.59329003, 0.6497639, 0.0, 2.419548, 2.2182324, 0.98689353, 0.0, 3.9229474, 0.0, 1.2862117, 0.0, 0.0, 2.832682, 1.5527806, 0.0, 3.9483843, 0.0, 0.8849715, 0.0, 0.0, 0.0, 0.5914854, 0.0, 1.0026925, 1.1529278, 1.1741399, 1.9578987, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.7014454, -0.007571698, 0.81041974, 0.34615386, 1.4212015, -0.015701355, 0.8362515, -0.01108097, 0.54411215, 0.0098379, 0.021785902, 1.0, 0.48429644, -0.010017921, 1.562096, -0.012075442, 0.5139595, 0.012623523, -0.0056238887, 0.019875428, 1.0, -0.008740159, 1.0, 0.3215633, 0.28601104, 0.26037228, 0.0018778549, -0.011150918, 0.0033648168, -0.008854764, -0.00092181045, 0.01764944], "split_indices": [117, 143, 0, 139, 1, 138, 0, 143, 0, 139, 0, 0, 97, 139, 0, 138, 0, 140, 0, 0, 0, 15, 0, 39, 143, 142, 142, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1969.0, 101.0, 1400.0, 569.0, 1298.0, 102.0, 477.0, 92.0, 1198.0, 100.0, 117.0, 360.0, 1071.0, 127.0, 243.0, 117.0, 973.0, 98.0, 124.0, 119.0, 856.0, 117.0, 570.0, 286.0, 341.0, 229.0, 175.0, 111.0, 218.0, 123.0, 125.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0019582375, -0.0056877844, 0.04066061, -0.03242883, 0.014388399, 0.10032922, -0.009106064, -0.05346692, 0.0071743615, 0.05922941, -0.027369238, 0.001918531, 0.02017591, 0.026835267, -0.09520816, 0.0019388833, 0.021576336, 0.0030660063, -0.016906224, -0.011111588, 0.016743936, -0.046657592, -0.01819295, -0.09344738, 0.016116058, -0.031442016, 0.011460712, -0.012156057, 0.003119429, 0.004634421, -0.023453336, 0.00088038313, -0.008001461], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.60988957, 0.9239329, 2.6722722, 1.6173927, 1.840623, 1.9259167, 0.0, 2.058075, 0.0, 4.25079, 2.1950433, 0.0, 0.0, 4.0732646, 1.7009888, 5.270085, 0.0, 1.6127578, 0.0, 0.0, 0.0, 1.5103168, 0.0, 4.279812, 0.0, 0.62555075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 17, 17, 21, 21, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.53846157, 0.115384616, 0.43214852, -0.009106064, -0.34615386, 0.0071743615, -0.15384616, 0.5217996, 0.001918531, 0.02017591, -0.5, 1.3973255, 1.0, 0.021576336, 0.39494628, -0.016906224, -0.011111588, 0.016743936, 1.2851628, -0.01819295, 0.5418762, 0.016116058, 1.244291, 0.011460712, -0.012156057, 0.003119429, 0.004634421, -0.023453336, 0.00088038313, -0.008001461], "split_indices": [62, 124, 108, 1, 1, 142, 0, 1, 0, 1, 142, 0, 0, 1, 138, 61, 0, 142, 0, 0, 0, 138, 0, 141, 0, 138, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1721.0, 340.0, 738.0, 983.0, 234.0, 106.0, 614.0, 124.0, 474.0, 509.0, 130.0, 104.0, 210.0, 404.0, 347.0, 127.0, 419.0, 90.0, 106.0, 104.0, 259.0, 145.0, 217.0, 130.0, 320.0, 99.0, 132.0, 127.0, 109.0, 108.0, 175.0, 145.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0006153399, 0.0044654827, -0.008081106, -0.018432183, 0.017552452, 0.009712377, -0.012966104, 0.0021676316, 0.014849692, -0.017358938, 0.013000098, 0.009697722, -0.0073871487, 0.006017251, -0.039059326, -0.0028122747, 0.013140961, -0.01362586, -0.0027909232, 0.010359691, -0.010680811, 0.006894619, -0.0067405226, -0.011969443, 0.061486334, -0.07733549, 0.020374889, -0.0012480986, 0.016010946, -0.014511749, -0.0015653836, -0.0248042, 0.06327222, 0.0052803117, -0.009917791, -0.002198662, 0.016769039], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, 15, -1, -1, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [0.65271354, 0.5957261, 0.0, 2.2633426, 2.5484142, 1.8789263, 0.0, 0.64816207, 0.0, 0.7924398, 0.0, 1.5682942, 0.0, 0.0, 1.2972963, 1.2794211, 0.0, 0.0, 1.2422453, 0.94639784, 0.0, 0.0, 0.0, 1.2199057, 1.8383121, 0.79855263, 0.7480922, 0.0, 0.0, 0.0, 0.0, 1.0851256, 1.7627096, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 14, 14, 15, 15, 18, 18, 19, 19, 23, 23, 24, 24, 25, 25, 26, 26, 31, 31, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, 16, -1, -1, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.4310507, -0.1923077, -0.008081106, 0.8509382, 1.5094159, 0.6718423, -0.012966104, 0.726592, 0.014849692, 0.24772593, 0.013000098, 0.69867027, -0.0073871487, 0.006017251, 1.2985203, 0.54411215, 0.013140961, -0.01362586, 0.5138817, 1.0, -0.010680811, 0.006894619, -0.0067405226, 1.0, 0.33186364, 1.0, 1.0, -0.0012480986, 0.016010946, -0.014511749, -0.0015653836, 1.2609842, 0.27199793, 0.0052803117, -0.009917791, -0.002198662, 0.016769039], "split_indices": [143, 1, 0, 140, 138, 140, 0, 139, 0, 139, 0, 141, 0, 0, 138, 139, 0, 0, 141, 83, 0, 0, 0, 124, 140, 71, 53, 0, 0, 0, 0, 138, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2082.0, 1988.0, 94.0, 723.0, 1265.0, 577.0, 146.0, 1132.0, 133.0, 471.0, 106.0, 1030.0, 102.0, 103.0, 368.0, 934.0, 96.0, 100.0, 268.0, 829.0, 105.0, 127.0, 141.0, 577.0, 252.0, 191.0, 386.0, 144.0, 108.0, 91.0, 100.0, 188.0, 198.0, 92.0, 96.0, 109.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027221825, 0.0013740757, -0.007984215, -0.005153187, 0.008882022, 0.0019430296, -0.007193874, -0.0053640404, 0.012131751, 0.0014457105, -0.009291801, -0.005798143, 0.0066640414, 0.008961106, -0.010034169, -0.004475653, 0.06900049, 0.004941079, -0.008585608, -0.00029926436, 0.015236099, -0.00041166335, 0.007943826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1], "loss_changes": [0.6513928, 1.1175953, 0.0, 0.8634912, 0.0, 1.4366416, 0.0, 0.92533493, 0.0, 0.68005586, 0.0, 1.8084279, 0.0, 0.9043498, 0.0, 0.7019655, 1.2302842, 0.5539897, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1], "split_conditions": [1.6878304, 1.562096, -0.007984215, 1.486252, 0.008882022, 0.76394844, -0.007193874, 0.76379925, 0.012131751, 0.638004, -0.009291801, 0.54411215, 0.0066640414, 0.4370801, -0.010034169, 0.46418417, 0.46418417, 2.1153846, -0.008585608, -0.00029926436, 0.015236099, -0.00041166335, 0.007943826], "split_indices": [138, 138, 0, 138, 0, 139, 0, 142, 0, 142, 0, 139, 0, 139, 0, 142, 142, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1958.0, 104.0, 1822.0, 136.0, 1647.0, 175.0, 1552.0, 95.0, 1440.0, 112.0, 1296.0, 144.0, 1121.0, 175.0, 916.0, 205.0, 821.0, 95.0, 110.0, 95.0, 732.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.00062885345, 0.0055066347, -0.008022654, -0.0011482434, 0.0131677715, 0.003256747, -0.008768187, -0.004783546, 0.01098555, -9.435564e-05, -0.007956885, -0.018119195, 0.017493097, 0.0070673665, -0.013761336, -0.007535237, 0.016436992, -0.0123479795, 0.0116610825, 0.021546043, -0.07524689, 0.025989199, -0.06870541, -0.029829372, 0.012234594, 0.00063143607, -0.0140058985, -0.0072538885, 0.0072301957, -0.013706908, -0.0017432634, 0.00023528082, -0.010049749], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 126, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8179749, 1.6423621, 0.0, 0.7082323, 0.0, 1.5153269, 0.0, 0.5765218, 0.0, 0.49041605, 0.0, 2.2993712, 2.8783731, 1.3420264, 0.0, 1.317356, 0.0, 1.1580735, 0.0, 2.423602, 1.0625179, 1.4556311, 0.76062703, 0.7050191, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3062673, 1.1144832, -0.008022654, 0.9786442, 0.0131677715, 0.8711886, -0.008768187, 0.82049423, 0.01098555, 1.0, -0.007956885, 0.5724227, 0.62448454, 0.46374416, -0.013761336, 0.45340723, 0.016436992, 0.31487146, 0.0116610825, 0.33186364, -0.07692308, 1.0, 0.40912068, 0.30109692, 0.012234594, 0.00063143607, -0.0140058985, -0.0072538885, 0.0072301957, -0.013706908, -0.0017432634, 0.00023528082, -0.010049749], "split_indices": [140, 142, 0, 142, 0, 139, 0, 141, 0, 111, 0, 141, 141, 141, 0, 140, 0, 140, 0, 140, 1, 26, 140, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1956.0, 118.0, 1858.0, 98.0, 1768.0, 90.0, 1644.0, 124.0, 1547.0, 97.0, 764.0, 783.0, 631.0, 133.0, 669.0, 114.0, 536.0, 95.0, 468.0, 201.0, 319.0, 217.0, 310.0, 158.0, 89.0, 112.0, 102.0, 217.0, 93.0, 124.0, 213.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0002176994, -0.0066178823, 0.034794193, 0.0032181332, -0.08210932, 0.08619693, -0.008171866, -0.011846405, 0.058014832, -0.0011623629, -0.014717299, 0.0012849969, 0.01744849, 0.004852339, -0.021366261, 0.01926563, 0.0077337725, -0.032763463, 0.028971372, -0.010773001, 0.007778182, -0.017329156, -0.015806345, 0.009638275, -0.0042789094, 0.005373563, -0.03294275, 0.0011010017, -0.108654656, -0.025962204, 0.0105355885, -0.01828748, -0.0037463866, -0.0064925784, 0.0055152844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, -1, -1, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [0.49113625, 1.2882982, 2.054254, 1.2671227, 0.91721106, 1.5412053, 0.0, 0.6919529, 2.2408419, 0.0, 0.0, 0.0, 0.0, 0.0, 0.59665656, 0.0, 1.9492109, 1.6399624, 0.9287948, 0.0, 0.0, 0.83773047, 0.0, 0.0, 0.0, 0.0, 1.5954835, 1.2047687, 1.0144873, 1.0714204, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 14, 14, 16, 16, 17, 17, 18, 18, 21, 21, 26, 26, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, -1, -1, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0558407, 1.0, 1.4332378, 1.1373166, 0.43214852, -0.008171866, 0.17621006, 0.6645929, -0.0011623629, -0.014717299, 0.0012849969, 0.01744849, 0.004852339, 0.61693144, 0.01926563, 1.0, 0.6472997, 0.7446223, -0.010773001, 0.007778182, 1.0, -0.015806345, 0.009638275, -0.0042789094, 0.005373563, 0.42291117, 0.39016172, 0.44437784, 0.34326252, 0.0105355885, -0.01828748, -0.0037463866, -0.0064925784, 0.0055152844], "split_indices": [62, 141, 108, 138, 142, 142, 0, 141, 140, 0, 0, 0, 0, 0, 141, 0, 109, 143, 141, 0, 0, 89, 0, 0, 0, 0, 142, 139, 141, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1735.0, 343.0, 1535.0, 200.0, 238.0, 105.0, 1204.0, 331.0, 96.0, 104.0, 130.0, 108.0, 164.0, 1040.0, 90.0, 241.0, 848.0, 192.0, 91.0, 150.0, 755.0, 93.0, 99.0, 93.0, 136.0, 619.0, 427.0, 192.0, 339.0, 88.0, 94.0, 98.0, 229.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00043973202, 0.004952843, -0.048327874, -0.0010813245, 0.0098028565, 0.005390605, -0.013858849, 0.0049523055, -0.008649657, -0.001538577, 0.011332185, 0.010714914, -0.08580491, -0.0034305865, 0.013206312, -0.01821283, 0.0019840058, 0.005833423, -0.011643118, -0.0057570795, 0.0543619, 0.01346644, -0.008785284, -0.0014959058, 0.013338782, 0.00012599718, 0.009454305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [0.5332665, 1.0423938, 1.928589, 0.8982796, 0.0, 0.0, 0.0, 1.1451583, 0.0, 1.5860075, 0.0, 2.3018687, 1.9843365, 1.2572533, 0.0, 0.0, 0.0, 0.6243412, 0.0, 1.4140401, 1.1723251, 0.7184922, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [1.0558407, 1.0, 1.0, 0.95287216, 0.0098028565, 0.005390605, -0.013858849, 0.82821655, -0.008649657, 0.6555352, 0.011332185, 1.4060924, 1.0, 0.6633733, 0.013206312, -0.01821283, 0.0019840058, 1.0, -0.011643118, 1.0, 1.0, 0.49214, -0.008785284, -0.0014959058, 0.013338782, 0.00012599718, 0.009454305], "split_indices": [141, 125, 69, 143, 0, 0, 0, 141, 0, 141, 0, 138, 111, 143, 0, 0, 0, 62, 0, 74, 74, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1856.0, 209.0, 1743.0, 113.0, 98.0, 111.0, 1628.0, 115.0, 1536.0, 92.0, 1341.0, 195.0, 1201.0, 140.0, 102.0, 93.0, 1110.0, 91.0, 896.0, 214.0, 726.0, 170.0, 114.0, 100.0, 631.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0010902974, -0.0035284138, 0.008748221, 0.0016637609, -0.009831108, -0.0074154795, 0.009566713, -0.00021375022, -0.011557727, -0.047219597, 0.00960144, 0.010401845, -0.01696652, 0.0002594197, 0.011524482, 0.009943569, -0.0075820372, -0.0083602, 0.010752583, 0.0255688, -0.036931995, 0.07313063, -0.028759133, -0.08432788, 0.017497893, 0.00038038567, 0.020095183, 0.0056339847, -0.008201838, -0.016461054, -0.004211351, 0.015489514, -0.0057582553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, -1, -1, 19, -1, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.82557124, 0.9665401, 0.0, 1.5891784, 0.0, 1.3226599, 0.0, 0.7345033, 0.0, 1.9402609, 1.2997767, 1.4355421, 0.0, 1.1187611, 0.0, 0.0, 0.0, 1.085742, 0.0, 1.3229753, 1.5684898, 2.4191704, 1.0832217, 1.1014509, 2.9193852, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, -1, -1, 20, -1, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3466678, 1.0953796, 0.008748221, 0.9271665, -0.009831108, 0.8388688, 0.009566713, -1.0, -0.011557727, 0.48379704, 0.7922416, 1.0, -0.01696652, 1.0, 0.011524482, 0.009943569, -0.0075820372, 0.34710723, 0.010752583, 1.0, 0.49933174, 1.0, 0.20089458, 0.31701317, 0.5311527, 0.00038038567, 0.020095183, 0.0056339847, -0.008201838, -0.016461054, -0.004211351, 0.015489514, -0.0057582553], "split_indices": [139, 139, 0, 140, 0, 139, 0, 0, 0, 141, 143, 126, 0, 88, 0, 0, 0, 141, 0, 13, 141, 83, 139, 143, 143, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1964.0, 105.0, 1862.0, 102.0, 1698.0, 164.0, 1592.0, 106.0, 275.0, 1317.0, 187.0, 88.0, 1210.0, 107.0, 92.0, 95.0, 1120.0, 90.0, 512.0, 608.0, 273.0, 239.0, 325.0, 283.0, 177.0, 96.0, 92.0, 147.0, 112.0, 213.0, 100.0, 183.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00043751008, -0.005880435, 0.0033229599, 0.008750698, -0.0011764511, -0.009030265, 0.0032748557, 0.026862288, -0.017853457, 0.018802859, 0.007813411, -0.001383591, -0.013838954, -0.037657127, 0.07423607, -0.017260028, 0.013026594, 0.030510448, -0.1218441, 0.02483867, 0.0005257912, 0.0005904116, -0.014742787, -0.008085425, 0.009435011, -0.02125181, -0.0047656237, -0.015913943, 0.012456326, -0.02812177, 0.008834204, -0.0052789925, -0.011065743, 0.003685609, -0.004506137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, -1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, -1, 17, 19, 21, -1, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [0.35333163, 0.0, 0.7465726, 0.0, 0.74227846, 0.0, 0.8880823, 2.5849707, 1.8661, 0.0, 2.2742667, 1.7285335, 0.0, 2.5652537, 3.9280272, 1.7147831, 0.0, 1.7560427, 1.3453815, 0.0, 4.257961, 1.6351819, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9219327, 0.0, 0.6419976, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21, 29, 29, 31, 31], "right_children": [2, -1, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, -1, 18, 20, 22, -1, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [0.10299488, -0.005880435, 0.15081438, 0.008750698, 1.1906027, -0.009030265, 1.0, 0.24274075, 1.0, 0.018802859, 0.7869461, 0.9476538, -0.013838954, 0.5030815, 0.87239504, 0.7406681, 0.013026594, 0.29735926, 1.4109008, 0.02483867, 1.0, 0.5992283, -0.014742787, -0.008085425, 0.009435011, -0.02125181, -0.0047656237, -0.015913943, 0.012456326, 1.0, 0.008834204, 0.35711887, -0.011065743, 0.003685609, -0.004506137], "split_indices": [140, 0, 141, 0, 138, 0, 126, 139, 64, 0, 141, 140, 0, 141, 143, 141, 0, 143, 138, 0, 61, 143, 0, 0, 0, 0, 0, 0, 0, 109, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 96.0, 1971.0, 100.0, 1871.0, 89.0, 1782.0, 842.0, 940.0, 89.0, 753.0, 827.0, 113.0, 447.0, 306.0, 738.0, 89.0, 247.0, 200.0, 91.0, 215.0, 649.0, 89.0, 90.0, 157.0, 90.0, 110.0, 94.0, 121.0, 489.0, 160.0, 383.0, 106.0, 186.0, 197.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0057561817, 0.0042948932, -0.07191054, -0.0013151468, 0.010125228, -0.020618808, -0.0010418287, 0.0045249006, -0.008799662, 0.009213277, -0.009016539, -0.0020151122, 0.010178334, 0.008823097, -0.011087538, 0.0010361507, 0.011665533, 0.011109517, -0.00915582, 0.00307586, 0.010679124, -0.007446127, 0.008249992, 0.0007472203, -0.0072778137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.3863634, 0.98452234, 2.6169205, 0.8661493, 0.0, 0.0, 1.4947294, 1.0196227, 0.0, 0.0, 0.0, 1.7721354, 0.0, 1.1470077, 0.0, 1.1883065, 0.0, 0.88320637, 0.0, 0.885841, 0.0, 0.91226745, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.0078171, 1.0570872, 0.9782575, 1.0, 0.010125228, -0.020618808, 1.1460509, 0.8711886, -0.008799662, 0.009213277, -0.009016539, 0.7446223, 0.010178334, 0.6960004, -0.011087538, 0.575291, 0.011665533, 0.5981277, -0.00915582, 0.48727876, 0.010679124, 0.39922574, 0.008249992, 0.0007472203, -0.0072778137], "split_indices": [140, 143, 141, 84, 0, 0, 142, 139, 0, 0, 0, 141, 0, 140, 0, 140, 0, 139, 0, 141, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2085.0, 1810.0, 275.0, 1711.0, 99.0, 95.0, 180.0, 1603.0, 108.0, 88.0, 92.0, 1502.0, 101.0, 1366.0, 136.0, 1274.0, 92.0, 1149.0, 125.0, 1060.0, 89.0, 936.0, 124.0, 762.0, 174.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.00231154, -0.0010784157, 0.006973402, 0.0049396018, -0.012247416, -0.0033032948, 0.013476525, 0.001614937, -0.0084283715, -0.00782393, 0.010844895, 0.007199073, -0.06521466, -0.0011315561, 0.010395589, -0.0024777143, -0.019745424, 0.010684959, -0.060920566, 0.008020899, -0.008910191, 4.662937e-05, 0.011133069, -0.013794524, 0.0009685349, 0.0021273203, -0.0049370164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [0.47266075, 1.4384763, 0.0, 2.007582, 0.0, 0.702567, 0.0, 1.6769562, 0.0, 1.3174127, 0.0, 0.976121, 2.629929, 0.7877451, 0.0, 1.5399741, 0.0, 0.99682355, 1.0006655, 0.0, 0.0, 0.883215, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.2520225, 1.2521182, 0.006973402, 1.0570872, -0.012247416, 1.5481575, 0.013476525, 0.78555995, -0.0084283715, 0.62705463, 0.010844895, 0.56020635, 0.65399134, 1.3479978, 0.010395589, 1.0, -0.019745424, 0.44216087, 1.3839768, 0.008020899, -0.008910191, 0.3465536, 0.011133069, -0.013794524, 0.0009685349, 0.0021273203, -0.0049370164], "split_indices": [142, 140, 0, 143, 0, 138, 0, 139, 0, 143, 0, 143, 139, 138, 0, 122, 0, 139, 138, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1969.0, 99.0, 1876.0, 93.0, 1764.0, 112.0, 1663.0, 101.0, 1528.0, 135.0, 1211.0, 317.0, 1115.0, 96.0, 215.0, 102.0, 931.0, 184.0, 110.0, 105.0, 842.0, 89.0, 88.0, 96.0, 589.0, 253.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.004294531, 0.00046888893, -0.010962501, -0.0033771757, 0.008166842, 0.0022627949, -0.010371229, -0.0032843184, 0.010282663, 0.002569556, -0.010132134, -0.0037443999, 0.010199778, 0.004384075, -0.009611346, 0.02801812, -0.014185528, -0.0074651917, 0.0714591, -0.0001892474, -0.011129371, -0.0057078493, 0.0042446963, 0.018694663, -0.0002285935, -0.0027735247, 0.0073017227], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 133, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0320659, 0.614604, 0.0, 1.063302, 0.0, 0.9923959, 0.0, 0.9675898, 0.0, 0.9988069, 0.0, 1.1232263, 0.0, 0.6034529, 0.0, 0.9325652, 1.0465478, 0.82461023, 2.3165243, 1.3571349, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.7083547, 1.1509038, -0.010962501, 1.5481575, 0.008166842, 0.8711886, -0.010371229, 0.88207287, 0.010282663, 0.78377116, -0.010132134, 0.7140961, 0.010199778, 1.0, -0.009611346, 0.45939127, 0.42981234, 1.0, 1.0, 1.0, -0.011129371, -0.0057078493, 0.0042446963, 0.018694663, -0.0002285935, -0.0027735247, 0.0073017227], "split_indices": [138, 139, 0, 138, 0, 139, 0, 141, 0, 142, 0, 139, 0, 137, 0, 140, 139, 17, 59, 50, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1968.0, 89.0, 1879.0, 89.0, 1779.0, 100.0, 1686.0, 93.0, 1591.0, 95.0, 1496.0, 95.0, 1375.0, 121.0, 605.0, 770.0, 333.0, 272.0, 673.0, 97.0, 167.0, 166.0, 106.0, 166.0, 489.0, 184.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0022466509, 0.008418673, -0.037704367, 0.003971218, 0.008292451, -0.018728737, 0.03194273, 0.010414837, -0.010315391, 0.013833779, -0.006075789, 0.0036331126, 0.010114329, 0.010303237, -0.0067689917, 0.001339171, 0.064412735, 0.011931055, -0.0063771657, -0.005581206, 0.019917024, -0.000929115, 0.008167277, -0.012426514, 0.008682651, -7.026511e-05, -0.009923575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [0.51041746, 0.5941305, 2.8857925, 1.1679426, 0.0, 0.0, 1.8640862, 0.982011, 0.0, 0.0, 0.0, 0.706464, 0.0, 0.65868556, 0.0, 0.80343795, 3.1268306, 0.8986843, 0.0, 0.0, 0.0, 0.85358113, 0.0, 0.7612689, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0078171, 1.0, 0.9233974, 0.8922892, 0.008292451, -0.018728737, 1.1460509, 0.78377116, -0.010315391, 0.013833779, -0.006075789, 0.6725825, 0.010114329, 0.5653381, -0.0067689917, 0.48244315, 1.0, 0.44610116, -0.0063771657, -0.005581206, 0.019917024, 1.0, 0.008167277, 0.39922574, 0.008682651, -7.026511e-05, -0.009923575], "split_indices": [140, 125, 142, 141, 0, 0, 142, 142, 0, 0, 0, 142, 0, 142, 0, 142, 15, 141, 0, 0, 0, 64, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1793.0, 277.0, 1692.0, 101.0, 88.0, 189.0, 1596.0, 96.0, 88.0, 101.0, 1485.0, 111.0, 1358.0, 127.0, 1165.0, 193.0, 1002.0, 163.0, 102.0, 91.0, 846.0, 156.0, 748.0, 98.0, 659.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0006744656, -0.003131892, 0.0070576295, 0.003551153, -0.008764459, -0.0041512987, 0.06814854, 0.004865235, -0.010974339, 0.018579414, -0.003579075, -0.0010466871, 0.0069849114, 0.0070530623, -0.04972785, -0.00056929863, 0.0074256905, -0.011169086, 0.0010983355, 0.009520694, -0.009412217, -0.0011677969, 0.0071225897, -0.0018422727, 0.004660278], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [0.55130005, 1.1098365, 0.0, 0.9060538, 0.0, 1.5490259, 2.3722322, 0.57588553, 0.0, 0.0, 0.0, 0.54177547, 0.0, 0.603433, 0.7373227, 0.998697, 0.0, 0.0, 0.0, 0.6298566, 0.0, 0.6709621, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.3466678, 1.0516728, 0.0070576295, 0.8071148, -0.008764459, 1.4762797, 1.0, 1.4212015, -0.010974339, 0.018579414, -0.003579075, 0.54411215, 0.0069849114, 0.48429644, -0.115384616, 0.5138817, 0.0074256905, -0.011169086, 0.0010983355, 0.43565965, -0.009412217, 1.0, 0.0071225897, -0.0018422727, 0.004660278], "split_indices": [139, 142, 0, 142, 0, 138, 69, 138, 0, 0, 0, 139, 0, 139, 1, 141, 0, 0, 0, 142, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1965.0, 107.0, 1821.0, 144.0, 1627.0, 194.0, 1499.0, 128.0, 91.0, 103.0, 1374.0, 125.0, 1178.0, 196.0, 1058.0, 120.0, 97.0, 99.0, 955.0, 103.0, 814.0, 141.0, 598.0, 216.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0059832474, -0.00052464893, -0.0065710447, -0.006039345, 0.0110574765, 0.00018575389, -0.011488235, -0.0057987724, 0.009370868, 0.0023697026, -0.013049307, -0.005538663, 0.010822016, 0.009373139, -0.03624715, 3.8230202e-05, 0.006482939, -0.011390307, 0.0019442763, -0.0094097005, 0.0056479373, -0.005887911, 0.008382194, -0.0023434684, 0.008129532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [0.6696589, 1.1530625, 0.0, 1.2148627, 0.0, 0.9492352, 0.0, 1.6235888, 0.0, 1.252308, 0.0, 0.63742316, 0.0, 0.4850651, 1.349435, 0.4276683, 0.0, 0.0, 1.5189235, 0.87395746, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.1337914, 1.1314709, -0.0065710447, 0.9235219, 0.0110574765, 0.9157179, -0.011488235, 0.7751207, 0.009370868, 0.761162, -0.013049307, 0.45463473, 0.010822016, 0.40307778, 0.47982812, 1.3846154, 0.006482939, -0.011390307, 1.0, 0.39838645, 0.0056479373, -0.005887911, 0.008382194, -0.0023434684, 0.008129532], "split_indices": [143, 139, 0, 139, 0, 143, 0, 143, 0, 139, 0, 142, 0, 142, 141, 1, 0, 0, 122, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1882.0, 172.0, 1793.0, 89.0, 1696.0, 97.0, 1594.0, 102.0, 1496.0, 98.0, 1392.0, 104.0, 937.0, 455.0, 802.0, 135.0, 150.0, 305.0, 687.0, 115.0, 175.0, 130.0, 595.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0018049922, 0.0055635027, -0.007964313, -0.0019701966, 0.07460948, -0.017746639, 0.022325518, 0.00037709667, 0.014544797, 0.0037042561, -0.11673359, -0.0021782657, 0.01863124, 0.014279761, -0.007849148, -0.02500881, 0.002821693, 0.026131628, -0.046005312, -0.013942559, 0.102049716, 0.01098011, -0.023637263, 0.0012324094, -0.010682746, -0.0867281, 0.026451504, 0.0018458165, 0.017973071, -0.012551481, 0.0076498757, -0.0028877861, -0.016667841, -0.010109749, 0.007884822, 0.0069482946, -0.007152776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, 25, 27, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [0.6315328, 1.0257787, 0.0, 0.68150723, 0.97350955, 2.2889807, 2.8128104, 0.0, 0.0, 0.7701654, 3.7113235, 0.7556101, 0.0, 1.9445016, 0.0, 0.0, 0.0, 1.5407308, 0.8479053, 1.7464219, 1.2402533, 0.0, 2.3667738, 0.0, 0.0, 0.9805298, 0.7317937, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0998956, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 25, 25, 26, 26, 33, 33], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, 26, 28, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.4310507, 0.97041875, -0.007964313, 1.0, 1.5248721, 1.4228944, 0.8711886, 0.00037709667, 0.014544797, 0.63586605, 1.0, 1.0, 0.01863124, 0.44571885, -0.007849148, -0.02500881, 0.002821693, 0.31283978, 1.0, 1.0, 1.0, 0.01098011, 0.56020635, 0.0012324094, -0.010682746, 0.26206392, 1.0, 0.0018458165, 0.017973071, -0.012551481, 0.0076498757, -0.0028877861, -0.016667841, 0.2382665, 0.007884822, 0.0069482946, -0.007152776], "split_indices": [143, 143, 0, 109, 138, 138, 139, 0, 0, 140, 122, 106, 0, 140, 0, 0, 0, 143, 108, 59, 108, 0, 143, 0, 0, 143, 81, 0, 0, 0, 0, 0, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1972.0, 91.0, 1778.0, 194.0, 1078.0, 700.0, 97.0, 97.0, 886.0, 192.0, 609.0, 91.0, 785.0, 101.0, 100.0, 92.0, 370.0, 239.0, 594.0, 191.0, 138.0, 232.0, 122.0, 117.0, 212.0, 382.0, 92.0, 99.0, 115.0, 117.0, 123.0, 89.0, 225.0, 157.0, 98.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-7.830943e-05, -0.0062069236, 0.00460873, -0.0017694678, 0.013210339, 0.00333236, -0.009439955, -0.005801257, 0.01350828, 0.000101682126, -0.010335847, -0.0052285716, 0.006857866, 0.0019574435, -0.011318031, -0.0073250583, 0.010975709, 0.0015589669, -0.01213201, 0.0019507445, -0.0021242376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [0.60348064, 0.0, 1.5702628, 0.86908007, 0.0, 2.0974536, 0.0, 0.9386755, 0.0, 0.5610047, 0.0, 1.1062095, 0.0, 1.33787, 0.0, 1.2466772, 0.0, 0.46736282, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.0, -0.0062069236, 1.3466678, 1.5727334, 0.013210339, 0.97098887, -0.009439955, 0.8256487, 0.01350828, 0.72240794, -0.010335847, 0.65399134, 0.006857866, 0.6718423, -0.011318031, 0.6433131, 0.010975709, 0.3222687, -0.01213201, 0.0019507445, -0.0021242376], "split_indices": [104, 0, 139, 138, 0, 140, 0, 139, 0, 139, 0, 139, 0, 140, 0, 142, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 146.0, 1931.0, 1839.0, 92.0, 1743.0, 96.0, 1630.0, 113.0, 1537.0, 93.0, 1426.0, 111.0, 1337.0, 89.0, 1231.0, 106.0, 1142.0, 89.0, 639.0, 503.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.0027839006, -0.0021291936, 0.04804247, -0.006184239, 0.007998544, 0.012878858, -0.0036781696, -0.00022760702, -0.006575054, -0.00540732, 0.007935158, 0.017378049, -0.028222673, 0.0697798, -0.03092154, 0.0074621052, -0.07069477, 0.13972193, -0.0018842496, -0.013154099, 0.0030715053, -0.032406554, 0.013062077, -0.0002776958, -0.13086107, 2.726778e-05, 0.026367677, -0.0040761065, 0.0055345926, 0.0027968627, -0.011658867, -0.008835857, -0.017336354, 0.0074859182, -0.004997316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.4609516, 0.6226697, 1.3903922, 0.6322793, 0.0, 0.0, 0.0, 0.66775954, 0.0, 0.79070127, 0.0, 1.9260782, 1.1518619, 2.2624283, 1.3544635, 2.0279007, 1.4179676, 3.5262234, 0.0, 0.0, 0.6782315, 0.9246116, 0.0, 0.0, 0.3323884, 0.0, 0.0, 0.0, 0.0, 0.8366007, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0769875, 1.0, 1.1263903, 1.0, 0.007998544, 0.012878858, -0.0036781696, 5.0, -0.006575054, 1.0, 0.007935158, 0.1923077, 1.0, 1.0, 0.53846157, 1.0, 0.34831125, 1.0, -0.0018842496, -0.013154099, 0.38445532, 0.5190839, 0.013062077, -0.0002776958, 0.57407886, 2.726778e-05, 0.026367677, -0.0040761065, 0.0055345926, 1.0, -0.011658867, -0.008835857, -0.017336354, 0.0074859182, -0.004997316], "split_indices": [142, 102, 141, 119, 0, 0, 0, 0, 0, 106, 0, 1, 12, 39, 1, 121, 141, 17, 0, 0, 141, 143, 0, 0, 143, 0, 0, 0, 0, 115, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1870.0, 203.0, 1782.0, 88.0, 104.0, 99.0, 1620.0, 162.0, 1521.0, 99.0, 761.0, 760.0, 365.0, 396.0, 413.0, 347.0, 204.0, 161.0, 100.0, 296.0, 312.0, 101.0, 163.0, 184.0, 96.0, 108.0, 161.0, 135.0, 220.0, 92.0, 92.0, 92.0, 93.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00013909857, -0.008462193, 0.0039408184, 0.010313423, -0.039301842, 0.0004942547, 0.014524232, 0.005200696, -0.014635357, 0.007839474, -0.010261596, -0.0067969686, 0.09265065, 0.0053041317, -0.07426219, 0.023961496, -0.0015822969, -0.009683203, 0.010717458, -0.0023321444, -0.011970929, 0.0034816812, -0.0103898, 0.017688422, -0.010410476, -0.0012821702, 0.008575991], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 140, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [0.6605871, 0.0, 0.5406651, 2.2655604, 2.463241, 1.207243, 0.0, 0.0, 0.0, 1.8471051, 0.0, 1.0360161, 3.491244, 1.6428012, 0.4468155, 0.0, 0.0, 1.163427, 0.0, 0.0, 0.0, 1.2579162, 0.0, 1.5098844, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 21, 21, 23, 23], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.167164, -0.008462193, 3.0, 1.1254623, 1.0, 0.948359, 0.014524232, 0.005200696, -0.014635357, 1.0, -0.010261596, 1.0, 0.0, 1.0, 0.42508063, 0.023961496, -0.0015822969, 0.7666822, 0.010717458, -0.0023321444, -0.011970929, 1.0, -0.0103898, 1.0, -0.010410476, -0.0012821702, 0.008575991], "split_indices": [138, 0, 0, 142, 109, 142, 0, 0, 0, 64, 0, 113, 1, 0, 142, 0, 0, 143, 0, 0, 0, 42, 0, 116, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 88.0, 1962.0, 1710.0, 252.0, 1594.0, 116.0, 136.0, 116.0, 1488.0, 106.0, 1269.0, 219.0, 1076.0, 193.0, 93.0, 126.0, 938.0, 138.0, 91.0, 102.0, 823.0, 115.0, 727.0, 96.0, 502.0, 225.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0028278003, -0.00037332714, 0.0062490753, 0.007433762, -0.009866676, -0.0007582508, 0.009506189, 0.0033727444, -0.00672375, -0.0040078736, 0.00889046, 0.0021383225, -0.008530809, -0.0063753678, 0.006298008, 0.003039401, -0.011189926, -0.004583032, 0.0068646795, 0.00078946596, -0.004884354], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [0.3938189, 1.501774, 0.0, 1.3014637, 0.0, 0.45532918, 0.0, 0.9854252, 0.0, 0.7180505, 0.0, 0.69203204, 0.0, 1.1643627, 0.0, 0.53809494, 0.0, 0.5323873, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.3466678, 1.0516728, 0.0062490753, 1.5125633, -0.009866676, 0.7794498, 0.009506189, 1.4306145, -0.00672375, 0.7425969, 0.00889046, 0.5689899, -0.008530809, 0.602346, 0.006298008, 0.5076032, -0.011189926, 0.39922574, 0.0068646795, 0.00078946596, -0.004884354], "split_indices": [139, 142, 0, 138, 0, 139, 0, 138, 0, 140, 0, 140, 0, 142, 0, 142, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1957.0, 105.0, 1813.0, 144.0, 1658.0, 155.0, 1561.0, 97.0, 1437.0, 124.0, 1336.0, 101.0, 1172.0, 164.0, 1076.0, 96.0, 964.0, 112.0, 752.0, 212.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015327148, 0.004035487, -0.0073844106, -0.0005604753, 0.009341232, 0.0040905746, -0.009252445, 0.01294109, -0.033444133, 0.002669753, 0.0086751385, -0.009566925, 0.0036515896, 0.055483367, -0.009020214, -0.0056749107, 0.00701534, 0.016205803, -0.0028923745, 0.002160507, -0.012641782, -0.009849396, 0.007822325, 0.010710148, -0.07011014, -0.0029005355, 0.005304578, -0.016649315, 0.0023465566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, 15, 17, 19, -1, -1, -1, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.83347416, 0.789505, 0.0, 0.78188884, 0.0, 0.5780306, 0.0, 1.0674473, 0.766351, 0.7630935, 0.0, 0.0, 0.83548516, 2.015028, 1.3283412, 0.0, 0.0, 0.0, 0.0, 0.84407985, 0.0, 0.9886691, 0.0, 1.0004218, 1.8308794, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 19, 19, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, 16, 18, 20, -1, -1, -1, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.2294465, 1.562096, -0.0073844106, 1.5248721, 0.009341232, 1.0, -0.009252445, 1.4491278, 1.0, -0.30769232, 0.0086751385, -0.009566925, 0.43565965, 1.3275425, 1.4050424, -0.0056749107, 0.00701534, 0.016205803, -0.0028923745, 0.5325338, -0.012641782, 0.4167162, 0.007822325, 0.24451698, 1.0, -0.0029005355, 0.005304578, -0.016649315, 0.0023465566], "split_indices": [139, 138, 0, 138, 0, 7, 0, 138, 111, 1, 0, 0, 142, 138, 138, 0, 0, 0, 0, 139, 0, 142, 0, 143, 124, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1922.0, 148.0, 1828.0, 94.0, 1740.0, 88.0, 1408.0, 332.0, 1236.0, 172.0, 124.0, 208.0, 224.0, 1012.0, 109.0, 99.0, 99.0, 125.0, 924.0, 88.0, 798.0, 126.0, 595.0, 203.0, 307.0, 288.0, 100.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-4.8831037E-9", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}