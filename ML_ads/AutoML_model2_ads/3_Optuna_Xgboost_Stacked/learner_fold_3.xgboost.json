{"learner": {"attributes": {"best_iteration": "19", "best_score": "1.023821"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "70"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.0026328564, -0.16017331, 0.40084428, -0.24739656, 0.04008813, 0.13480535, 0.57406193, -0.3454727, -0.19422273, -0.012093003, 0.109983474, 0.025549803, -0.0030543592, 0.4877286, 0.07870174, -0.028576562, -0.3971363, -0.07138129, -0.27179092, 0.027033309, 0.007866072, 0.02622859, 0.060575444, -0.043225512, -0.036708266, -0.019138198, 0.0015599051, -0.3357519, -0.015112695, -0.00988597, 0.010191157, -0.038815863, -0.024734855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [134.26558, 25.677155, 27.695656, 5.3402443, 5.0194716, 4.7296696, 6.6921997, 1.1104813, 6.3269806, 0.0, 5.0924654, 0.0, 0.0, 6.8914833, 0.0, 0.0, 0.20369911, 2.6824896, 3.141138, 0.0, 1.9070448, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2323589, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 13, 13, 16, 16, 17, 17, 18, 18, 20, 20, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.6985336, 0.48706603, 0.8316313, 0.2243912, 0.44131443, 1.0, 1.3550751, 1.0, 1.2622243, -0.012093003, 0.5489027, 0.025549803, -0.0030543592, 1.0, 0.07870174, -0.028576562, 0.16003081, 0.22045416, 0.44921184, 0.027033309, 0.6896655, 0.02622859, 0.060575444, -0.043225512, -0.036708266, -0.019138198, 0.0015599051, 1.0, -0.015112695, -0.00988597, 0.010191157, -0.038815863, -0.024734855], "split_indices": [140, 139, 139, 142, 140, 108, 139, 23, 138, 0, 140, 0, 0, 109, 0, 0, 140, 139, 142, 0, 139, 0, 0, 0, 0, 0, 0, 93, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1470.0, 601.0, 1024.0, 446.0, 237.0, 364.0, 360.0, 664.0, 135.0, 311.0, 137.0, 100.0, 259.0, 105.0, 167.0, 193.0, 257.0, 407.0, 121.0, 190.0, 89.0, 170.0, 89.0, 104.0, 108.0, 149.0, 266.0, 141.0, 89.0, 101.0, 167.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016246208, -0.13166516, 0.38497558, -0.21826756, 0.047416866, 0.23205115, 0.58343744, -0.309612, -0.16982937, 0.16305582, -0.033334393, 0.038563963, 0.007846265, 0.078757904, 0.036228415, -0.37075198, -0.2501517, -0.06993846, -0.23211145, 0.029014826, 0.0035963368, -0.013955074, 0.0042622634, -0.03545897, -0.038709786, -0.018875293, -0.03157367, 0.004780512, -0.024655385, -0.1670587, -0.31776422, -0.024107112, -0.0041742227, -0.023614867, -0.0397586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [103.26216, 23.837233, 15.690788, 4.583843, 4.678334, 6.888109, 10.157974, 1.3051071, 4.211897, 3.3274121, 2.3800247, 0.0, 0.0, 0.0, 0.0, 0.04675865, 0.73288345, 5.4067864, 2.3234997, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1981683, 1.1726437, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 17, 17, 18, 18, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.76299673, 0.48394743, 0.97360194, 0.2243912, 1.0, 1.0, 1.1489253, 0.21688554, 1.2622243, 1.3975503, 0.55697036, 0.038563963, 0.007846265, 0.078757904, 0.036228415, 1.2076659, 0.17867625, 0.35274446, 1.0, 0.029014826, 0.0035963368, -0.013955074, 0.0042622634, -0.03545897, -0.038709786, -0.018875293, -0.03157367, 0.004780512, -0.024655385, 0.48013937, 0.34615386, -0.024107112, -0.0041742227, -0.023614867, -0.0397586], "split_indices": [139, 139, 141, 142, 53, 108, 143, 141, 138, 138, 142, 0, 0, 0, 0, 138, 142, 143, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1537.0, 517.0, 1036.0, 501.0, 292.0, 225.0, 359.0, 677.0, 206.0, 295.0, 146.0, 146.0, 117.0, 108.0, 177.0, 182.0, 260.0, 417.0, 103.0, 103.0, 123.0, 172.0, 89.0, 88.0, 94.0, 88.0, 156.0, 104.0, 237.0, 180.0, 149.0, 88.0, 89.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.002779715, -0.12625441, 0.35875794, -0.21986805, 0.0048628063, 0.0126250135, 0.46726158, -0.26351067, -0.1291569, 0.0674771, -0.0755144, 0.5616366, 0.022525024, -0.012427706, -0.28797063, 0.0020440908, -0.22860546, -0.00041541163, 0.025559599, -0.15265386, 0.007876449, 0.029981742, 0.07083185, -0.34496233, -0.19510596, -0.01996111, -0.02575998, 0.013861177, -0.013532328, -0.0049651996, -0.026013404, -0.024129627, -0.39255443, -0.0030160288, -0.035838548, -0.035426337, -0.042446364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [92.584915, 18.976162, 13.320351, 3.5708961, 3.241099, 0.0, 8.222343, 2.0740395, 4.3590465, 4.6234155, 3.35608, 9.946663, 0.0, 0.0, 2.7415237, 0.0, 0.14795685, 4.9890594, 0.0, 2.081283, 0.0, 0.0, 0.0, 1.5837097, 5.305652, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.26880264, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [0.76299673, 0.42782113, 0.7982205, 0.35320818, 1.0, 0.0126250135, 1.0, 1.1743733, 0.39554682, 0.71817666, 0.5981277, 1.0436205, 0.022525024, -0.012427706, 0.2708205, 0.0020440908, 0.36598742, 0.50745165, 0.025559599, -0.03846154, 0.007876449, 0.029981742, 0.07083185, 0.07692308, 1.2642944, -0.01996111, -0.02575998, 0.013861177, -0.013532328, -0.0049651996, -0.026013404, -0.024129627, 0.20548163, -0.0030160288, -0.035838548, -0.035426337, -0.042446364], "split_indices": [139, 139, 142, 142, 122, 0, 97, 138, 141, 140, 139, 139, 0, 0, 139, 0, 139, 141, 0, 1, 0, 0, 0, 1, 138, 0, 0, 0, 0, 0, 0, 0, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1546.0, 528.0, 902.0, 644.0, 168.0, 360.0, 609.0, 293.0, 362.0, 282.0, 259.0, 101.0, 91.0, 518.0, 117.0, 176.0, 266.0, 96.0, 188.0, 94.0, 93.0, 166.0, 321.0, 197.0, 88.0, 88.0, 131.0, 135.0, 96.0, 92.0, 101.0, 220.0, 98.0, 99.0, 100.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015938808, -0.1404527, 0.23249276, -0.19002545, -0.023538278, 0.1259234, 0.43790317, -0.00059036753, -0.21436074, -0.01278878, 0.050665807, 0.03250152, 0.078590296, 0.032697443, 0.06361588, -0.27826643, -0.1366853, 0.024535429, -0.007438954, -0.007709261, 0.1790309, -0.01684225, -0.33920458, -0.060600873, -0.027410908, 0.0022546945, 0.03459471, -0.3215911, -0.03681763, 0.0074995407, -0.015561727, -0.03078093, -0.033537287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, -1, 17, -1, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [66.96029, 7.493929, 16.789982, 4.0684357, 2.981117, 4.758932, 5.761963, 0.0, 3.9810486, 0.0, 5.4780383, 0.0, 6.3798494, 0.0, 0.0, 2.945221, 3.7850013, 0.0, 0.0, 0.0, 6.4776864, 0.0, 0.144413, 3.001942, 0.0, 0.0, 0.0, 0.033426285, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 12, 12, 15, 15, 16, 16, 20, 20, 22, 22, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, -1, 18, -1, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.5981277, 0.42782113, 1.0063491, 1.1743733, 1.0, 0.6569235, -0.1923077, -0.00059036753, 0.285204, -0.01278878, 1.3350109, 0.03250152, 0.76299673, 0.032697443, 0.06361588, 0.07692308, 0.37351757, 0.024535429, -0.007438954, -0.007709261, 1.0, -0.01684225, 1.0, 1.0, -0.027410908, 0.0022546945, 0.03459471, 1.0, -0.03681763, 0.0074995407, -0.015561727, -0.03078093, -0.033537287], "split_indices": [139, 139, 139, 138, 69, 139, 1, 0, 139, 0, 138, 0, 139, 0, 0, 1, 139, 0, 0, 0, 53, 0, 109, 97, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1293.0, 767.0, 908.0, 385.0, 505.0, 262.0, 106.0, 802.0, 160.0, 225.0, 97.0, 408.0, 168.0, 94.0, 440.0, 362.0, 88.0, 137.0, 160.0, 248.0, 157.0, 283.0, 233.0, 129.0, 128.0, 120.0, 176.0, 107.0, 96.0, 137.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003017147, -0.11019054, 0.28056467, -0.18199626, -0.005952579, 0.21628529, 0.0583596, -0.25614604, -0.13146558, -0.0720878, 0.10585449, 0.15389945, 0.044800412, -0.033352826, -0.21103533, 0.0055331737, -0.1728782, 0.018991718, -0.021459319, 0.027890394, -0.0009081322, 0.0016260246, 0.25915292, -0.014032478, -0.03012273, -0.028508434, -0.12649967, -0.009052764, 0.010938867, 0.04311529, 0.006210437, 0.0002775191, -0.18733491, -0.028700871, -0.0091494685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [65.071884, 11.01026, 11.687195, 3.2634964, 4.436631, 7.1557026, 0.0, 1.2322388, 4.007128, 4.893205, 4.4353766, 5.649932, 0.0, 0.0, 1.4221869, 0.0, 2.2064762, 2.2770495, 0.0, 0.0, 0.0, 0.0, 7.4902077, 0.0, 0.0, 0.0, 2.3593407, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9487615, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 16, 16, 17, 17, 22, 22, 26, 26, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [0.6985336, 0.4074681, 1.3550751, 0.26228148, 0.5981277, 1.0, 0.0583596, 1.0, 0.20312513, 0.5325959, 0.61489445, 0.7890327, 0.044800412, -0.033352826, 0.19637428, 0.0055331737, 0.2724345, 1.0, -0.021459319, 0.027890394, -0.0009081322, 0.0016260246, 0.86566544, -0.014032478, -0.03012273, -0.028508434, 0.32150277, -0.009052764, 0.010938867, 0.04311529, 0.006210437, 0.0002775191, 0.48013937, -0.028700871, -0.0091494685], "split_indices": [140, 140, 139, 141, 139, 125, 0, 69, 143, 140, 141, 139, 0, 0, 140, 0, 139, 124, 0, 0, 0, 0, 143, 0, 0, 0, 143, 0, 0, 0, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1471.0, 600.0, 871.0, 600.0, 495.0, 105.0, 353.0, 518.0, 377.0, 223.0, 390.0, 105.0, 130.0, 223.0, 94.0, 424.0, 230.0, 147.0, 89.0, 134.0, 169.0, 221.0, 125.0, 98.0, 124.0, 300.0, 104.0, 126.0, 118.0, 103.0, 96.0, 204.0, 100.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0023431506, -0.098895624, 0.24611548, -0.17244023, 0.005820197, 0.073211245, 0.3604401, -0.22635901, -0.121685974, -0.061256636, 0.11722094, 0.019230766, -0.008751109, 0.048332732, 0.2526775, -0.16862707, -0.27515015, -0.0024284127, -0.16716166, 0.01824113, -0.018703736, 0.026785139, 7.1767085e-05, 0.010290821, 0.038645197, -0.022743614, -0.011511078, -0.03311009, -0.02274544, -0.010398592, -0.025421483, 0.013965606, -0.010006056], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [51.135326, 11.27468, 12.018463, 2.3534794, 4.513335, 4.6322327, 4.8468094, 1.1746063, 1.9622297, 3.7697322, 3.983609, 0.0, 0.0, 0.0, 3.9068823, 0.60112286, 0.60310364, 0.0, 1.6608925, 3.3179896, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6985336, 0.4074681, 0.8316313, 0.28475475, 0.5981277, 1.0, 1.0, 1.0, 0.28295493, 0.5325959, 0.7140961, 0.019230766, -0.008751109, 0.048332732, 1.0374465, 1.0, 1.0, -0.0024284127, 1.0, 1.0, -0.018703736, 0.026785139, 7.1767085e-05, 0.010290821, 0.038645197, -0.022743614, -0.011511078, -0.03311009, -0.02274544, -0.010398592, -0.025421483, 0.013965606, -0.010006056], "split_indices": [140, 140, 139, 141, 139, 108, 69, 23, 142, 140, 139, 0, 0, 0, 142, 126, 108, 0, 12, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1464.0, 608.0, 860.0, 604.0, 242.0, 366.0, 417.0, 443.0, 377.0, 227.0, 139.0, 103.0, 171.0, 195.0, 191.0, 226.0, 141.0, 302.0, 231.0, 146.0, 99.0, 128.0, 92.0, 103.0, 91.0, 100.0, 104.0, 122.0, 175.0, 127.0, 114.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022654324, -0.08552467, 0.20287412, -0.12991564, 0.023931077, 0.062269032, 0.28359187, -0.19438767, -0.09549895, 0.022459961, -0.05834302, 0.019595226, -0.006776827, 0.20898724, 0.050583508, -0.014361863, -0.23764494, 0.0034493157, -0.11950447, -0.13543715, 0.009355527, 0.008037445, 0.036035456, -0.02835976, -0.019529639, -0.16493805, -0.033226535, -0.00284347, -0.0262429, -0.110097684, -0.02848432, 0.0071675912, -0.01469629, -0.01764177, 0.0005609529], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [35.20139, 7.12307, 6.752844, 2.3143272, 6.9836564, 3.7722867, 6.267378, 0.7971935, 2.1219587, 0.0, 3.5131397, 0.0, 0.0, 5.5093784, 0.0, 0.0, 0.38142014, 0.0, 2.2500315, 2.704099, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4724407, 2.3623824, 0.0, 0.0, 1.9798157, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 13, 13, 16, 16, 18, 18, 19, 19, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.6985336, 0.49648586, 0.8081766, 0.2243912, 0.48037243, 0.64865166, 1.0, 1.0, 0.26338395, 0.022459961, 0.71012646, 0.019595226, -0.006776827, 1.1155719, 0.050583508, -0.014361863, 0.53846157, 0.0034493157, 0.44921184, 1.0, 0.009355527, 0.008037445, 0.036035456, -0.02835976, -0.019529639, 0.37351757, 0.45939127, -0.00284347, -0.0262429, 0.36752534, -0.02848432, 0.0071675912, -0.01469629, -0.01764177, 0.0005609529], "split_indices": [140, 139, 139, 142, 141, 139, 59, 23, 142, 0, 142, 0, 0, 142, 0, 0, 1, 0, 142, 124, 0, 0, 0, 0, 0, 139, 140, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1466.0, 595.0, 1043.0, 423.0, 217.0, 378.0, 363.0, 680.0, 123.0, 300.0, 107.0, 110.0, 283.0, 95.0, 167.0, 196.0, 106.0, 574.0, 199.0, 101.0, 153.0, 130.0, 94.0, 102.0, 376.0, 198.0, 108.0, 91.0, 258.0, 118.0, 103.0, 95.0, 164.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00027353797, -0.107750915, 0.13679563, -0.12573266, 0.0068873516, 0.038420185, 0.32289436, -0.026700864, -0.108375035, 0.13659754, -0.13591343, 0.1797496, 0.054222908, -0.013127323, -0.13247384, 0.04880731, 0.030153675, 0.00016098504, -0.025209693, 0.0026780656, 0.03174216, -0.012987447, 0.012086653, -0.021338142, -0.10863625, -0.009928934, 0.019220252, -0.045580965, -0.15857133, 0.008784852, -0.011711847, -0.024953028, -0.08590579, 0.0012015057, -0.018059852], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, 13, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, 27, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [30.435968, 3.677827, 16.623251, 2.5772781, 0.0, 10.166676, 9.858536, 0.0, 2.1484537, 5.502419, 3.4192793, 4.001312, 0.0, 2.956603, 1.4406948, 5.266615, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.816783, 0.0, 0.0, 2.434029, 2.1282835, 0.0, 0.0, 0.0, 1.6597588, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 24, 24, 27, 27, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, 14, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, 28, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [0.55767125, 3.5769231, 0.9305812, 0.10128946, 0.0068873516, 0.15384616, 1.0, -0.026700864, 1.2173017, 0.7577333, 1.0, 1.0, 0.054222908, 0.20888147, 0.2724345, 1.0, 0.030153675, 0.00016098504, -0.025209693, 0.0026780656, 0.03174216, -0.012987447, 0.012086653, -0.021338142, 1.3155193, -0.009928934, 0.019220252, 0.27044263, 0.5114968, 0.008784852, -0.011711847, -0.024953028, 0.55072904, 0.0012015057, -0.018059852], "split_indices": [143, 1, 140, 143, 0, 1, 0, 0, 138, 139, 111, 124, 0, 142, 139, 126, 0, 0, 0, 0, 0, 0, 0, 0, 138, 0, 0, 142, 139, 0, 0, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1158.0, 908.0, 1051.0, 107.0, 594.0, 314.0, 115.0, 936.0, 380.0, 214.0, 190.0, 124.0, 189.0, 747.0, 248.0, 132.0, 98.0, 116.0, 90.0, 100.0, 101.0, 88.0, 170.0, 577.0, 122.0, 126.0, 255.0, 322.0, 89.0, 166.0, 143.0, 179.0, 88.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001218843, -0.06417693, 0.19602013, -0.11522744, 0.00694607, 0.0047853165, 0.2638606, 0.0041279667, -0.13567561, 0.013566107, -0.03578199, 0.16966018, 0.04256552, -0.16186625, -0.0014009841, -0.016614025, 0.01245792, -0.0039685047, 0.034326356, -0.19915359, -0.10223609, -0.01187525, 0.071610175, -0.010570258, -0.24467215, 0.0028589119, -0.020195603, 0.018781181, -0.0077171335, -0.021258397, -0.030316615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, -1, 15, 17, -1, 19, -1, -1, 21, -1, -1, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [26.306408, 5.613319, 5.2168407, 2.8802557, 3.5528338, 0.0, 5.4258366, 0.0, 2.536458, 0.0, 3.0499089, 8.177185, 0.0, 1.456358, 0.0, 0.0, 2.7475328, 0.0, 0.0, 1.714262, 3.287563, 0.0, 4.2184324, 0.0, 0.50865555, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 10, 10, 11, 11, 13, 13, 16, 16, 19, 19, 20, 20, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, -1, 16, 18, -1, 20, -1, -1, 22, -1, -1, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [0.76299673, 0.42782113, 0.7982205, 1.1743733, 1.3350109, 0.0047853165, 1.0, 0.0041279667, 0.43434185, 0.013566107, 1.0, 1.0, 0.04256552, 0.285204, -0.0014009841, -0.016614025, 0.5469401, -0.0039685047, 0.034326356, 0.07692308, 1.2692025, -0.01187525, 0.6569235, -0.010570258, 0.2770242, 0.0028589119, -0.020195603, 0.018781181, -0.0077171335, -0.021258397, -0.030316615], "split_indices": [139, 139, 142, 138, 138, 0, 12, 0, 142, 0, 81, 113, 0, 139, 0, 0, 143, 0, 0, 1, 138, 0, 139, 0, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1546.0, 519.0, 900.0, 646.0, 163.0, 356.0, 104.0, 796.0, 161.0, 485.0, 225.0, 131.0, 655.0, 141.0, 131.0, 354.0, 102.0, 123.0, 403.0, 252.0, 110.0, 244.0, 132.0, 271.0, 109.0, 143.0, 137.0, 107.0, 175.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0019684124, -0.064866416, 0.16472565, -0.10160589, 0.026632369, 0.1167636, 0.03656701, 0.004320023, -0.117763855, 0.022951365, -0.056426402, 0.05801427, 0.03199608, -0.026853515, -0.102014266, -0.013700239, 0.0035743236, -0.00836898, 0.16478665, -0.124438085, -0.024932388, 0.035498433, -0.003079391, -0.05512686, -0.16920517, -0.013513259, 0.009996113, -0.018600613, 0.0027187124, -0.008285545, -0.22361252, -0.015681658, -0.026636189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, -1, -1, 19, -1, -1, -1, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [22.495398, 4.928132, 5.801899, 2.447402, 7.0774503, 5.80172, 0.0, 0.0, 2.2344837, 0.0, 2.213145, 5.7040415, 0.0, 0.0, 1.4726543, 0.0, 0.0, 0.0, 7.997779, 2.0478878, 2.642552, 0.0, 0.0, 2.790258, 1.8839216, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7024479, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 11, 11, 14, 14, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, -1, -1, 20, -1, -1, -1, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [0.6985336, 0.49648586, 1.3443526, 1.1743733, 0.48037243, 1.0, 0.03656701, 0.004320023, 1.1966957, 0.022951365, 0.66372657, 0.7890327, 0.03199608, -0.026853515, 0.44921184, -0.013700239, 0.0035743236, -0.00836898, 0.86566544, 1.0, 1.0, 0.035498433, -0.003079391, 0.2724345, 1.0, -0.013513259, 0.009996113, -0.018600613, 0.0027187124, -0.008285545, 1.0, -0.015681658, -0.026636189], "split_indices": [140, 139, 140, 138, 141, 125, 0, 0, 138, 0, 142, 139, 0, 0, 142, 0, 0, 0, 143, 97, 122, 0, 0, 139, 59, 0, 0, 0, 0, 0, 106, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1466.0, 602.0, 1046.0, 420.0, 486.0, 116.0, 105.0, 941.0, 122.0, 298.0, 377.0, 109.0, 89.0, 852.0, 159.0, 139.0, 162.0, 215.0, 660.0, 192.0, 109.0, 106.0, 259.0, 401.0, 102.0, 90.0, 100.0, 159.0, 155.0, 246.0, 96.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023959912, -0.08531874, 0.10361684, -0.10259101, 0.008593739, 0.031552922, 0.23664048, -0.15740652, -0.07354755, 0.10257076, -0.016989633, 0.3483094, 0.005960436, -0.0073328475, -0.1919502, -0.048310388, -0.021663979, 0.21372008, 0.025287231, 0.015173188, 0.05595256, -0.02499379, -0.01570135, -0.078138925, 0.00978193, 0.02710065, 0.015770666, -0.0042847088, 0.013697135, -0.021119086, -0.15939218, 0.011485285, -0.08803723, -0.023217425, -0.0068619, -0.020834325, 0.0014748465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1, -1, 31, 33, -1, 35, -1, -1, -1, -1], "loss_changes": [18.126785, 3.4223852, 8.675515, 1.6732235, 0.0, 8.397909, 6.2866783, 1.057189, 2.4809227, 3.728064, 0.0, 8.096468, 0.0, 0.0, 0.52268124, 2.5455594, 0.0, 0.571167, 1.9480379, 0.0, 0.0, 0.0, 0.0, 2.2470276, 0.0, 0.0, 0.0, 0.0, 0.0, 2.593212, 1.321331, 0.0, 2.3618567, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 14, 14, 15, 15, 17, 17, 18, 18, 23, 23, 29, 29, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1, 32, 34, -1, 36, -1, -1, -1, -1], "split_conditions": [0.55767125, 3.5769231, 0.9305812, 0.2243912, 0.008593739, 0.46153846, 1.1955256, 1.0, 0.5912575, 1.0, -0.016989633, 0.9964899, 0.005960436, -0.0073328475, 1.0, 0.48312518, -0.021663979, 0.74006224, 1.0, 0.015173188, 0.05595256, -0.02499379, -0.01570135, 0.35274446, 0.00978193, 0.02710065, 0.015770666, -0.0042847088, 0.013697135, 0.24772593, 0.3887776, 0.011485285, 0.26845482, -0.023217425, -0.0068619, -0.020834325, 0.0014748465], "split_indices": [143, 1, 140, 142, 0, 1, 143, 53, 141, 53, 0, 139, 0, 0, 69, 141, 0, 139, 124, 0, 0, 0, 0, 143, 0, 0, 0, 0, 0, 139, 141, 0, 143, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1157.0, 905.0, 1051.0, 106.0, 587.0, 318.0, 364.0, 687.0, 434.0, 153.0, 195.0, 123.0, 106.0, 258.0, 584.0, 103.0, 178.0, 256.0, 101.0, 94.0, 97.0, 161.0, 485.0, 99.0, 88.0, 90.0, 159.0, 97.0, 285.0, 200.0, 94.0, 191.0, 111.0, 89.0, 88.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0045388243, -0.05519973, 0.12134065, -0.09450801, 0.0007414581, 0.080038175, 0.02956882, -0.16901727, -0.07196359, 0.036786426, -0.017289821, 0.02467539, 0.026258571, -0.023925023, -0.011038239, -0.13332103, -0.03856105, 0.0154268965, -0.024090882, 0.09749268, -0.0149277, -0.021715062, -0.0078623695, 0.011070235, -0.086472765, 0.01045578, -0.08246452, -0.0038386926, 0.02587058, -0.13634722, -0.0026419826, 0.0014567443, -0.022747938, -0.020226648, -0.0069678845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [13.111458, 3.2236633, 4.248579, 1.4462795, 3.7865956, 4.8207235, 0.0, 0.82361937, 1.3547153, 3.5831628, 0.0, 4.636028, 0.0, 0.0, 0.0, 1.0683637, 3.0608273, 0.0, 2.4781983, 5.651639, 0.0, 0.0, 0.0, 0.0, 0.9704144, 0.0, 3.1941352, 0.0, 0.0, 0.77786636, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 16, 16, 18, 18, 19, 19, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.6985336, 0.4074681, 1.3443526, 1.0, 1.0, 1.0, 0.02956882, 0.15879108, 0.25978217, 1.0, -0.017289821, 0.9546799, 0.026258571, -0.023925023, -0.011038239, 1.0, 0.23736568, 0.0154268965, -0.30769232, 0.7890327, -0.0149277, -0.021715062, -0.0078623695, 0.011070235, 0.32587615, 0.01045578, 1.0, -0.0038386926, 0.02587058, 0.3682784, -0.0026419826, 0.0014567443, -0.022747938, -0.020226648, -0.0069678845], "split_indices": [140, 140, 140, 26, 113, 125, 0, 143, 141, 53, 0, 143, 0, 0, 0, 69, 139, 0, 1, 139, 0, 0, 0, 0, 140, 0, 15, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1466.0, 590.0, 861.0, 605.0, 477.0, 113.0, 200.0, 661.0, 501.0, 104.0, 366.0, 111.0, 91.0, 109.0, 233.0, 428.0, 171.0, 330.0, 258.0, 108.0, 92.0, 141.0, 104.0, 324.0, 103.0, 227.0, 140.0, 118.0, 177.0, 147.0, 136.0, 91.0, 89.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00086785684, -0.08297481, 0.090408824, -0.09691778, 0.006932529, 0.062937416, 0.030448778, -0.112887636, 0.0054207514, 0.037012406, 0.028344238, -0.09383087, -0.025545387, -0.011218517, 0.07287721, -0.118489906, 0.00052734953, -0.009880449, 0.10294577, -0.15815334, -0.03529343, -0.0035058667, 0.14237562, -0.12350944, -0.021324964, -0.017612187, 0.007936331, 0.26752743, 0.035744015, -0.0080591375, -0.018266676, 0.012438809, 0.039909992, -0.002247334, 0.0110000875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, 17, 19, -1, -1, 21, 23, 25, -1, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [15.44606, 2.3040142, 5.739904, 2.3989697, 0.0, 4.944852, 0.0, 2.4424486, 0.0, 4.141629, 0.0, 1.9379478, 0.0, 0.0, 3.2212253, 2.0954084, 0.0, 0.0, 2.8894339, 0.82076263, 3.310121, 0.0, 5.5115414, 0.6702733, 0.0, 0.0, 0.0, 3.5783062, 0.96403766, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, 18, 20, -1, -1, 22, 24, 26, -1, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [0.49648586, 0.5607472, 1.3443526, 1.0, 0.006932529, 1.1307212, 0.030448778, 0.5067336, 0.0054207514, 1.0, 0.028344238, 0.38964713, -0.025545387, -0.011218517, -0.5, 0.30221263, 0.00052734953, -0.009880449, 1.0, 1.0, 1.0, -0.0035058667, 1.0, 0.19257316, -0.021324964, -0.017612187, 0.007936331, -0.1923077, 1.0, -0.0080591375, -0.018266676, 0.012438809, 0.039909992, -0.002247334, 0.0110000875], "split_indices": [139, 142, 140, 41, 0, 142, 0, 140, 0, 5, 0, 142, 0, 0, 1, 140, 0, 0, 81, 115, 17, 0, 121, 140, 0, 0, 0, 1, 53, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1085.0, 976.0, 994.0, 91.0, 865.0, 111.0, 899.0, 95.0, 774.0, 91.0, 793.0, 106.0, 150.0, 624.0, 635.0, 158.0, 93.0, 531.0, 430.0, 205.0, 118.0, 413.0, 264.0, 166.0, 92.0, 113.0, 190.0, 223.0, 153.0, 111.0, 91.0, 99.0, 125.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00021812269, -0.0484531, 0.11347861, -0.08608307, 0.0065249125, 0.19168152, 0.031104852, -0.10304672, 0.0017801778, 0.03952274, -0.0151347015, 0.09798911, 0.036471706, 0.0155700175, -0.008860436, -0.085202545, -0.020763338, -0.010954245, 0.08600544, -0.014577045, 0.035393666, -0.01930753, -0.06226408, 0.021135882, 0.0031826661, -0.0168636, -0.13246872, -0.01152902, 0.018288871, -0.011217268, 0.024537615, -0.006855686, -0.021096911, 0.016493805, -0.008131983], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, 21, -1, -1, 23, -1, -1, -1, 25, -1, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [11.341241, 3.0039282, 3.9681897, 1.519074, 3.073564, 5.123028, 4.4745626, 1.3829002, 0.0, 3.381329, 0.0, 12.789883, 0.0, 0.0, 0.0, 1.5663176, 0.0, 0.0, 3.8621454, 0.0, 0.0, 0.0, 1.6637826, 0.0, 4.769026, 1.2508544, 1.0285058, 0.0, 0.0, 0.0, 3.2845974, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 18, 18, 22, 22, 24, 24, 25, 25, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, 22, -1, -1, 24, -1, -1, -1, 26, -1, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [0.6885574, 0.4074681, 1.0, 1.0, 1.0, -0.07692308, 1.0, 2.0, 0.0017801778, 1.0, -0.0151347015, 1.0, 0.036471706, 0.0155700175, -0.008860436, 0.11508339, -0.020763338, -0.010954245, 0.5067336, -0.014577045, 0.035393666, -0.01930753, 0.5769231, 0.021135882, 0.64865166, 0.26228148, 1.2642944, -0.01152902, 0.018288871, -0.011217268, 0.35062885, -0.006855686, -0.021096911, 0.016493805, -0.008131983], "split_indices": [140, 140, 121, 113, 113, 1, 69, 0, 0, 5, 0, 61, 0, 0, 0, 143, 0, 0, 140, 0, 0, 0, 1, 0, 139, 141, 138, 0, 0, 0, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1452.0, 616.0, 862.0, 590.0, 316.0, 300.0, 741.0, 121.0, 488.0, 102.0, 205.0, 111.0, 147.0, 153.0, 633.0, 108.0, 116.0, 372.0, 105.0, 100.0, 111.0, 522.0, 148.0, 224.0, 317.0, 205.0, 135.0, 89.0, 96.0, 221.0, 113.0, 92.0, 95.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.001328001, -0.06545318, 0.080343515, 0.010768334, -0.08180224, 0.13978747, -0.065114, -0.07049393, -0.020505015, 0.07441957, 0.035974156, 0.008479593, -0.016411114, -0.091709815, 0.006659332, -0.0058249645, 0.023636766, -0.04913746, -0.1227193, 0.015408875, -0.09709649, -0.016178463, -0.014030061, -0.09030792, -0.024782723, -0.0012982714, -0.020472592, 0.0061050127, -0.008768643, -0.16234854, -0.028011916, -0.011544208, -0.020873375, -0.012147359, 0.0064551057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, -1, 7, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [10.835769, 3.2806883, 7.8683777, 0.0, 1.4759545, 9.288144, 3.9179332, 2.8211746, 0.0, 6.4717355, 0.0, 0.0, 0.0, 1.1089239, 0.0, 4.8603244, 0.0, 1.063644, 1.9706898, 0.0, 1.91926, 1.4358398, 0.0, 1.732307, 0.0, 0.0, 0.0, 0.0, 0.0, 0.38946056, 1.7907757, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21, 23, 23, 29, 29, 30, 30], "right_children": [2, 4, 6, -1, 8, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.55767125, 1.1743733, 0.15384616, 0.010768334, 0.6350785, -0.115384616, 1.0, 0.5245731, -0.020505015, 1.0, 0.035974156, 0.008479593, -0.016411114, 1.0, 0.006659332, 0.6558667, 0.023636766, 1.3155193, 0.4088675, 0.015408875, 1.0, 1.0, -0.014030061, 0.26228148, -0.024782723, -0.0012982714, -0.020472592, 0.0061050127, -0.008768643, 0.15879108, 1.0, -0.011544208, -0.020873375, -0.012147359, 0.0064551057], "split_indices": [143, 138, 1, 0, 141, 1, 126, 139, 0, 61, 0, 0, 0, 122, 0, 141, 0, 138, 140, 0, 124, 23, 0, 141, 0, 0, 0, 0, 0, 143, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1159.0, 910.0, 100.0, 1059.0, 646.0, 264.0, 970.0, 89.0, 498.0, 148.0, 105.0, 159.0, 840.0, 130.0, 333.0, 165.0, 354.0, 486.0, 121.0, 212.0, 260.0, 94.0, 386.0, 100.0, 119.0, 93.0, 125.0, 135.0, 179.0, 207.0, 89.0, 90.0, 103.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0026664017, -0.06938698, 0.054201275, 0.00678016, -0.08494446, 0.036895096, 0.024806544, -0.015796127, -0.06383993, -0.020359516, 0.08221781, -0.028800437, -0.14344049, 0.027033139, -0.023389276, 0.03333618, 0.022981493, 0.023996456, -0.09929463, -0.019261854, -0.008979167, -0.040274575, 0.02664345, 0.08717434, -0.019731136, 0.014789804, -0.005774415, -0.014096441, -0.005715662, 0.051851574, -0.02151437, 0.026236806, -0.006119854, 0.017359247, -0.0100658955, -0.013970411, 0.013878507], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, 11, 13, 15, 17, 19, 21, -1, 23, -1, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [7.693879, 1.8440418, 4.052898, 0.0, 1.1958041, 2.8777828, 0.0, 0.0, 1.6790755, 4.958755, 4.4659534, 1.5557436, 0.48545456, 6.4615374, 0.0, 5.7742014, 0.0, 2.4205422, 0.3143022, 0.0, 0.0, 5.0424366, 0.0, 6.1618986, 0.0, 0.0, 0.0, 0.0, 0.0, 3.8061879, 0.0, 0.0, 4.761826, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21, 23, 23, 29, 29, 32, 32], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, 12, 14, 16, 18, 20, 22, -1, 24, -1, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [0.4074681, 0.091577284, 1.2170341, 0.00678016, 0.18635547, 1.0, 0.024806544, -0.015796127, 1.3170784, 1.0192571, 0.91847384, 1.0, 0.393348, 0.7851144, -0.023389276, 0.7913911, 0.022981493, 1.0, 0.309244, -0.019261854, -0.008979167, 0.1923077, 0.02664345, 1.0, -0.019731136, 0.014789804, -0.005774415, -0.014096441, -0.005715662, 0.5956393, -0.02151437, 0.026236806, 0.5666099, 0.017359247, -0.0100658955, -0.013970411, 0.013878507], "split_indices": [140, 139, 141, 0, 139, 39, 0, 0, 138, 140, 140, 23, 143, 141, 0, 141, 0, 126, 141, 0, 0, 1, 0, 53, 0, 0, 0, 0, 0, 142, 0, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 864.0, 1208.0, 88.0, 776.0, 1109.0, 99.0, 174.0, 602.0, 490.0, 619.0, 418.0, 184.0, 401.0, 89.0, 465.0, 154.0, 239.0, 179.0, 96.0, 88.0, 313.0, 88.0, 377.0, 88.0, 95.0, 144.0, 90.0, 89.0, 205.0, 108.0, 131.0, 246.0, 114.0, 91.0, 128.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009746171, -0.014493608, 0.022488574, -0.028500771, 0.0147771165, -0.01827362, -0.020636883, -0.039333593, 0.101651214, -0.02815751, -0.019877108, -0.005613517, 0.028371245, -0.051161837, 0.08238274, 0.008847031, -0.07949494, 0.022930723, -0.0016631568, -0.0442383, 0.013404831, -0.11712811, -0.03874299, 0.00028229534, -0.013087382, 0.007086485, -0.17154713, -0.10954873, 0.0054756887, -0.025187055, -0.01162132, -0.018241154, -0.0053017223], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [6.2716913, 4.40482, 0.0, 3.245244, 0.0, 4.2607107, 0.0, 2.5570064, 7.239152, 3.4100342, 0.0, 0.0, 0.0, 1.8872633, 3.3605022, 2.3661013, 1.156353, 0.0, 0.0, 1.0192945, 0.0, 4.010315, 2.3965592, 0.0, 0.0, 0.0, 1.35116, 0.8485222, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 21, 21, 22, 22, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.3188831, 1.0, 0.022488574, 1.5730919, 0.0147771165, 1.4453799, -0.020636883, 0.74306023, 1.0, 0.5647758, -0.019877108, -0.005613517, 0.028371245, 1.0, 0.6320423, 0.4245479, 1.0, 0.022930723, -0.0016631568, 0.3220392, 0.013404831, 0.18145066, 0.34021482, 0.00028229534, -0.013087382, 0.007086485, 1.0, 1.0, 0.0054756887, -0.025187055, -0.01162132, -0.018241154, -0.0053017223], "split_indices": [139, 125, 0, 138, 0, 138, 0, 141, 109, 142, 0, 0, 0, 53, 142, 140, 13, 0, 0, 143, 0, 140, 140, 0, 0, 0, 115, 81, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1938.0, 116.0, 1784.0, 154.0, 1687.0, 97.0, 1435.0, 252.0, 1341.0, 94.0, 135.0, 117.0, 1110.0, 231.0, 356.0, 754.0, 93.0, 138.0, 250.0, 106.0, 392.0, 362.0, 162.0, 88.0, 88.0, 304.0, 206.0, 156.0, 124.0, 180.0, 90.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0043891687, -0.017451392, 0.1514055, 0.004179266, -0.091933325, 0.024569273, 0.0018044238, 0.020507162, -0.018149635, -0.05868741, -0.01586714, 0.07230912, -0.035927486, -0.12278556, 0.006200801, -0.029488493, 0.123779826, -0.10266371, 0.028463364, -0.0039516194, -0.020700114, 0.009705699, -0.011864556, 0.02967367, 0.06969891, -0.044721853, -0.020379854, -0.0099919615, 0.08873708, 0.12145618, -0.0073496187, -0.013260119, 0.0046896986, 0.02137839, -0.0024028346, 0.02881088, -0.0026258605], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.653025, 2.9064126, 3.369903, 4.238305, 0.90081906, 0.0, 0.0, 3.7566018, 0.0, 2.096552, 0.0, 3.5105286, 2.6427794, 1.2412255, 0.0, 2.5385456, 4.1623816, 1.769701, 2.4220312, 0.0, 0.0, 0.0, 0.0, 0.0, 2.512461, 1.5458696, 0.0, 0.0, 3.0035048, 6.1296477, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 24, 24, 25, 25, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0063491, 1.0, 1.1955256, 4.0, 1.0, 0.024569273, 0.0018044238, 1.0, -0.018149635, 0.31884238, -0.01586714, 0.42092142, 1.0, 0.21822353, 0.006200801, 0.3127959, 0.50745165, 1.3208424, -0.23076923, -0.0039516194, -0.020700114, 0.009705699, -0.011864556, 0.02967367, 1.4844531, 1.0, -0.020379854, -0.0099919615, 1.0, 1.0, -0.0073496187, -0.013260119, 0.0046896986, 0.02137839, -0.0024028346, 0.02881088, -0.0026258605], "split_indices": [139, 80, 143, 0, 58, 0, 0, 122, 0, 139, 0, 140, 39, 140, 0, 139, 141, 138, 1, 0, 0, 0, 0, 0, 138, 13, 0, 0, 97, 126, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1804.0, 268.0, 1398.0, 406.0, 157.0, 111.0, 1285.0, 113.0, 271.0, 135.0, 670.0, 615.0, 177.0, 94.0, 225.0, 445.0, 302.0, 313.0, 89.0, 88.0, 93.0, 132.0, 106.0, 339.0, 192.0, 110.0, 100.0, 213.0, 249.0, 90.0, 98.0, 94.0, 101.0, 112.0, 117.0, 132.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00013371387, -0.039379627, 0.050035425, -0.023435397, -0.01845414, 0.09567312, -0.058544967, -0.040879518, 0.013473578, 0.045394287, 0.027041841, 0.0054527433, -0.017765943, -0.05694934, 0.00916177, -0.019217044, 0.017079381, -0.08726377, 0.007864708, -0.119565204, 0.012606311, -0.0005864378, -0.11067752, 0.0104493275, -0.011496819, -0.0058359853, -0.01860355, -0.14924845, -0.0030875565, -0.11142359, -0.02347655, -0.015685853, -0.004946682], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [4.077661, 2.6894393, 4.5044217, 2.8888376, 0.0, 5.6230316, 3.6230423, 2.0078416, 0.0, 4.0268087, 0.0, 0.0, 0.0, 1.6523979, 0.0, 4.78178, 0.0, 1.0920606, 3.1809368, 0.7892568, 0.0, 0.0, 1.3697252, 0.0, 0.0, 0.0, 0.0, 0.9704008, 0.0, 0.58551955, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 22, 22, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [0.55767125, 0.5912575, 0.15384616, 3.0384614, -0.01845414, -0.115384616, 1.0, 0.48312518, 0.013473578, 1.0, 0.027041841, 0.0054527433, -0.017765943, 1.0, 0.00916177, 1.0, 0.017079381, -0.26923078, 1.2630832, 0.7913911, 0.012606311, -0.0005864378, 0.9230769, 0.0104493275, -0.011496819, -0.0058359853, -0.01860355, 0.34826937, -0.0030875565, 1.2537314, -0.02347655, -0.015685853, -0.004946682], "split_indices": [143, 141, 1, 1, 0, 1, 59, 141, 0, 61, 0, 0, 0, 83, 0, 97, 0, 1, 138, 141, 0, 0, 1, 0, 0, 0, 0, 141, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1162.0, 909.0, 1047.0, 115.0, 640.0, 269.0, 943.0, 104.0, 497.0, 143.0, 138.0, 131.0, 841.0, 102.0, 328.0, 169.0, 573.0, 268.0, 194.0, 134.0, 128.0, 445.0, 150.0, 118.0, 101.0, 93.0, 300.0, 145.0, 208.0, 92.0, 120.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00035233644, -0.010282976, 0.019316664, -0.02873997, 0.068932444, -0.045628276, 0.04981168, 0.0206742, -0.006603338, -0.029741779, -0.022379456, -0.010001221, 0.015827317, -0.010425879, 0.0072556734, -0.048515182, 0.015816446, -0.033814445, -0.019419946, -0.051423687, 0.009901388, -0.06767929, 0.00903613, -0.046029244, -0.018533489, -0.0075920084, 0.005111595], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [4.236623, 2.8627493, 0.0, 2.1066477, 3.8515353, 3.6993818, 4.566284, 0.0, 1.8475688, 4.233167, 0.0, 0.0, 0.0, 0.0, 0.0, 2.3365576, 0.0, 2.317955, 0.0, 2.016701, 0.0, 1.9995904, 0.0, 1.9251869, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.1981136, 0.8026405, 0.019316664, 1.3076923, 0.9546799, 0.89335084, 0.33102146, 0.0206742, 1.0, 0.71012646, -0.022379456, -0.010001221, 0.015827317, -0.010425879, 0.0072556734, 0.66575265, 0.015816446, 0.56020635, -0.019419946, 1.0, 0.009901388, 0.4994389, 0.00903613, 0.38964713, -0.018533489, -0.0075920084, 0.005111595], "split_indices": [141, 143, 0, 1, 143, 140, 140, 0, 39, 142, 0, 0, 0, 0, 0, 143, 0, 143, 0, 41, 0, 140, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1958.0, 108.0, 1588.0, 370.0, 1307.0, 281.0, 131.0, 239.0, 1200.0, 107.0, 118.0, 163.0, 107.0, 132.0, 1091.0, 109.0, 991.0, 100.0, 875.0, 116.0, 785.0, 90.0, 663.0, 122.0, 507.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019063602, -0.0119013535, 0.016783467, -0.0016810675, -0.10456878, 0.0069402372, -0.015102724, -0.0037147743, -0.017629325, -0.009012031, 0.017650284, 0.0014146029, -0.017868184, -0.035093576, 0.06544434, -0.0009691414, -0.1082846, 0.027003506, 0.0034005279, -0.026503323, 0.012621069, -0.021950541, -0.0034137366, 0.049294867, -0.015416999, 0.025228351, -0.08153264, -0.00224912, 0.013371934, 0.015492496, -0.004409223, -0.0034970646, -0.015799232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, 23, 25, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [3.508487, 1.8496621, 0.0, 2.2648163, 0.9381323, 4.4982624, 0.0, 0.0, 0.0, 2.68901, 0.0, 3.3474562, 0.0, 2.277813, 6.6006656, 2.019903, 2.3915465, 0.0, 2.8854063, 1.4746213, 0.0, 0.0, 0.0, 1.8726954, 0.0, 2.4005024, 0.8935883, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, 24, 26, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3443526, 1.0, 0.016783467, 1.6281253, 0.29588273, 0.9524887, -0.015102724, -0.0037147743, -0.017629325, 0.896009, 0.017650284, 0.49648586, -0.017868184, 0.3992645, 1.0, 0.36517957, 0.4578711, 0.027003506, 1.0, 1.0, 0.012621069, -0.021950541, -0.0034137366, 1.420945, -0.015416999, 1.2245126, 1.0, -0.00224912, 0.013371934, 0.015492496, -0.004409223, -0.0034970646, -0.015799232], "split_indices": [140, 40, 0, 138, 143, 140, 0, 0, 0, 141, 0, 139, 0, 141, 17, 142, 141, 0, 0, 23, 0, 0, 0, 138, 0, 138, 115, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1953.0, 115.0, 1759.0, 194.0, 1663.0, 96.0, 100.0, 94.0, 1520.0, 143.0, 1432.0, 88.0, 912.0, 520.0, 622.0, 290.0, 121.0, 399.0, 518.0, 104.0, 116.0, 174.0, 309.0, 90.0, 267.0, 251.0, 167.0, 142.0, 93.0, 174.0, 156.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004992833, -0.0109136645, 0.11839682, -0.0007295755, -0.017875526, 0.023016289, -0.0041268966, 0.011937694, -0.01128422, -0.0037241383, 0.027035793, -0.029788699, 0.052128486, 0.009352383, -0.043183234, 0.11491147, -0.008512808, -0.08142374, -0.00011713368, 0.21735837, -0.0017972541, -0.044212013, -0.017268753, 0.0552793, -0.054468367, 0.008277012, 0.035345882, -0.114533745, 0.009833201, 0.016364615, -0.002418969, -0.013512567, 0.0029291115, -0.0049164994, -0.021258684], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, -1, 17, 19, -1, 21, 23, 25, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.7394025, 3.107532, 4.550531, 2.434156, 0.0, 0.0, 0.0, 6.232896, 0.0, 2.1137843, 0.0, 1.6351964, 3.9812279, 0.0, 1.4706541, 4.315498, 0.0, 1.6063459, 1.2645632, 3.2788343, 0.0, 3.3680449, 0.0, 1.7912555, 1.4322331, 0.0, 0.0, 1.4421611, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, -1, 18, 20, -1, 22, 24, 26, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0170169, 1.0, 1.6909655, 1.4844531, -0.017875526, 0.023016289, -0.0041268966, 0.7669308, -0.01128422, 0.48394743, 0.027035793, 1.1743733, 0.6569235, 0.009352383, 1.0, 1.0, -0.008512808, 1.0, 0.33424312, 0.56020635, -0.0017972541, 0.285204, -0.017268753, 1.0, 1.0, 0.008277012, 0.035345882, 0.25233552, 0.009833201, 0.016364615, -0.002418969, -0.013512567, 0.0029291115, -0.0049164994, -0.021258684], "split_indices": [139, 117, 138, 138, 0, 0, 0, 139, 0, 139, 0, 138, 139, 0, 17, 115, 0, 127, 140, 143, 0, 139, 0, 69, 69, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1818.0, 255.0, 1714.0, 104.0, 150.0, 105.0, 1540.0, 174.0, 1452.0, 88.0, 990.0, 462.0, 97.0, 893.0, 317.0, 145.0, 473.0, 420.0, 179.0, 138.0, 336.0, 137.0, 208.0, 212.0, 90.0, 89.0, 225.0, 111.0, 88.0, 120.0, 108.0, 104.0, 135.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00052373327, -0.0113806, 0.018319331, -0.025394522, 0.01472073, -0.013506144, -0.024455411, -0.033402164, 0.089302, -0.021534966, -0.02131632, -0.0032928903, 0.022171883, -0.0586644, 0.015096454, 0.006640674, -0.10291248, -0.013844933, 0.019723848, -0.16837882, -0.047117293, 0.033074923, -0.017118523, -0.008628615, -0.02605394, 0.0042502894, -0.013146574, -0.054731842, 0.021912703, 0.001424374, -0.014652245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [4.1108522, 4.324864, 0.0, 4.658547, 0.0, 3.4691217, 0.0, 3.0313616, 4.4509935, 1.8130186, 0.0, 0.0, 0.0, 3.6636128, 3.537139, 0.0, 1.7861733, 4.2744007, 0.0, 1.7022829, 1.9956615, 7.2861395, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9183874, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.3188831, 1.0, 0.018319331, 1.5730919, 0.01472073, 1.4375004, -0.024455411, 0.74306023, 1.0, 1.0, -0.02131632, -0.0032928903, 0.022171883, -0.26923078, 0.5715085, 0.006640674, 0.42307693, 0.4558188, 0.019723848, 0.35628006, 1.0, 0.33102146, -0.017118523, -0.008628615, -0.02605394, 0.0042502894, -0.013146574, 0.2928758, 0.021912703, 0.001424374, -0.014652245], "split_indices": [139, 125, 0, 138, 0, 138, 0, 141, 109, 111, 0, 0, 0, 1, 141, 0, 1, 140, 0, 141, 12, 140, 0, 0, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1946.0, 115.0, 1788.0, 158.0, 1696.0, 92.0, 1421.0, 275.0, 1333.0, 88.0, 143.0, 132.0, 662.0, 671.0, 173.0, 489.0, 579.0, 92.0, 225.0, 264.0, 446.0, 133.0, 119.0, 106.0, 128.0, 136.0, 303.0, 143.0, 173.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0014549256, -0.013071118, 0.10136981, 0.0010185968, -0.1382617, 0.020456955, -0.004217162, -0.016983127, 0.019512415, -0.02406695, -0.003472854, -0.006105813, -0.01185047, -0.020635832, 0.020127714, -0.0075479103, -0.01445348, -0.020975163, 0.013791396, 0.0102545405, -0.032819606, -0.011126616, -0.015822846, 0.00024847683, -0.010408197], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, 21, -1, 23, -1, -1], "loss_changes": [3.0072348, 3.1908953, 3.895935, 5.6816263, 1.9402752, 0.0, 0.0, 1.6431713, 0.0, 0.0, 0.0, 4.0498457, 0.0, 2.0367038, 0.0, 2.2187822, 0.0, 1.5215532, 0.0, 0.0, 1.2653365, 0.0, 1.2603353, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 20, 20, 22, 22], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, 22, -1, 24, -1, -1], "split_conditions": [1.0063491, 0.83869666, 1.1955256, 0.71012646, 0.9867292, 0.020456955, -0.004217162, 0.6642266, 0.019512415, -0.02406695, -0.003472854, 1.4334207, -0.01185047, 0.658762, 0.020127714, 0.56020635, -0.01445348, 0.091577284, 0.013791396, 0.0102545405, 0.18321145, -0.011126616, 1.3524544, 0.00024847683, -0.010408197], "split_indices": [139, 142, 143, 142, 142, 0, 0, 141, 0, 0, 0, 138, 0, 143, 0, 143, 0, 139, 0, 0, 139, 0, 138, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1809.0, 263.0, 1626.0, 183.0, 153.0, 110.0, 1488.0, 138.0, 92.0, 91.0, 1344.0, 144.0, 1256.0, 88.0, 1136.0, 120.0, 1040.0, 96.0, 91.0, 949.0, 169.0, 780.0, 646.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-3.8552356e-05, -0.009035458, 0.015126591, -0.020060021, 0.011403747, -0.006502004, -0.01724708, -0.04583554, 0.033709943, 0.0051273387, -0.059554532, -0.012461154, 0.105304375, -0.09459864, -0.021682648, -0.058858242, 0.016402971, -0.005351754, 0.027893556, -0.055572476, -0.014480911, 0.012524539, -0.010499919, -0.014142614, -0.018258592, 0.011193356, -0.014529449, -0.011496076, 0.0014224033, -0.0066352966, 0.013577066, -0.009380692, 0.010535381], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, 13, 15, 17, 19, 21, 23, -1, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.7892456, 2.6240997, 0.0, 3.6678386, 0.0, 2.5781353, 0.0, 1.0977632, 2.664309, 0.0, 0.95822835, 4.012444, 6.071421, 0.73482037, 0.98895866, 2.1466331, 0.0, 3.167753, 0.0, 0.87461525, 0.0, 2.3914518, 0.0, 2.7130857, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, 14, 16, 18, 20, 22, 24, -1, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3188831, 1.0, 0.015126591, 0.9546799, 0.011403747, 0.4074681, -0.01724708, 0.10083316, 1.0, 0.0051273387, 1.0, 1.0, 1.423565, 0.2992334, 1.3170784, 1.0, 0.016402971, 1.3504932, 0.027893556, 1.2386552, -0.014480911, 0.285204, -0.010499919, 0.64295137, -0.018258592, 0.011193356, -0.014529449, -0.011496076, 0.0014224033, -0.0066352966, 0.013577066, -0.009380692, 0.010535381], "split_indices": [139, 125, 0, 143, 0, 140, 0, 139, 109, 0, 17, 23, 138, 142, 138, 50, 0, 138, 0, 138, 0, 139, 0, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1934.0, 115.0, 1775.0, 159.0, 1630.0, 145.0, 824.0, 806.0, 102.0, 722.0, 490.0, 316.0, 375.0, 347.0, 388.0, 102.0, 193.0, 123.0, 211.0, 164.0, 246.0, 101.0, 285.0, 103.0, 105.0, 88.0, 114.0, 97.0, 150.0, 96.0, 171.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011197164, -0.010571171, 0.01623988, -0.017792402, 0.012774885, -0.0073817163, -0.02153715, -0.015338995, 0.014246541, -0.005078518, -0.01586192, -0.018832978, 0.020067694, -0.006362847, -0.015379195, -0.018496243, 0.011949542, 0.010051188, -0.029547, -0.004905583, 0.0022349027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, -1, 19, -1, -1], "loss_changes": [3.196068, 1.9527333, 0.0, 3.8217816, 0.0, 2.1045418, 0.0, 2.4639254, 0.0, 4.4262056, 0.0, 2.4672132, 0.0, 2.049352, 0.0, 1.6097188, 0.0, 0.0, 1.1339225, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, -1, 20, -1, -1], "split_conditions": [1.3443526, 1.1307212, 0.01623988, 1.0800644, 0.012774885, 0.91847384, -0.02153715, 0.83869666, 0.014246541, 0.7300078, -0.01586192, 0.7140961, 0.020067694, 0.5954426, -0.015379195, 1.1743733, 0.011949542, 0.010051188, 0.43693843, -0.004905583, 0.0022349027], "split_indices": [140, 142, 0, 140, 0, 140, 0, 142, 0, 142, 0, 139, 0, 139, 0, 138, 0, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1955.0, 113.0, 1858.0, 97.0, 1765.0, 93.0, 1676.0, 89.0, 1564.0, 112.0, 1466.0, 98.0, 1342.0, 124.0, 1224.0, 118.0, 104.0, 1120.0, 814.0, 306.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.00029983837, -0.008081529, 0.015495981, -0.020584071, 0.013225949, -0.010104633, -0.01796638, -0.027547983, 0.07667911, -0.013630619, -0.11175151, 0.023740185, -0.004971458, -0.05073207, 0.022139288, 0.0010287917, -0.025085026, 0.0048944047, -0.0064339126, -0.009230128, -0.018075211, 0.01639223, -0.005829616, -0.06745411, 0.0591689, 0.011552966, -0.031711932, -0.0006949343, -0.01170407, 0.014102246, -0.0040287543, -0.10257343, 0.039149553, -0.0018341391, -0.020347638, 0.01405962, -0.008711911], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, -1, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [2.6728928, 3.4320352, 0.0, 2.9940503, 0.0, 2.5507517, 0.0, 1.644163, 3.7007165, 1.5978471, 3.378131, 0.0, 0.5985567, 3.189085, 2.430861, 0.0, 0.0, 0.0, 0.0, 1.7841437, 0.0, 0.0, 1.6082221, 0.7260541, 1.6770184, 0.0, 2.1190104, 0.0, 0.0, 0.0, 0.0, 1.7933438, 2.7028112, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 19, 19, 22, 22, 23, 23, 24, 24, 26, 26, 31, 31, 32, 32], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, -1, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.1981136, 1.0, 0.015495981, 1.0675184, 0.013225949, 1.4375004, -0.01796638, 2.0, 0.6517612, 0.34523815, 0.3737156, 0.023740185, 0.79667294, 0.3480368, 1.0, 0.0010287917, -0.025085026, 0.0048944047, -0.0064339126, 0.25028193, -0.018075211, 0.01639223, 0.3464803, 0.13741437, 1.2515761, 0.011552966, 0.52568775, -0.0006949343, -0.01170407, 0.014102246, -0.0040287543, 0.47014588, 0.6476206, -0.0018341391, -0.020347638, 0.01405962, -0.008711911], "split_indices": [141, 125, 0, 140, 0, 138, 0, 0, 141, 139, 143, 0, 140, 140, 89, 0, 0, 0, 0, 141, 0, 0, 142, 139, 138, 0, 142, 0, 0, 0, 0, 139, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1956.0, 106.0, 1796.0, 160.0, 1685.0, 111.0, 1403.0, 282.0, 1204.0, 199.0, 95.0, 187.0, 591.0, 613.0, 106.0, 93.0, 98.0, 89.0, 448.0, 143.0, 101.0, 512.0, 242.0, 206.0, 90.0, 422.0, 109.0, 133.0, 113.0, 93.0, 211.0, 211.0, 115.0, 96.0, 117.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0010584072, 0.01705642, -0.064750694, 0.0064104106, 0.010952814, -0.025017407, -0.015691888, -0.0050373417, 0.014498849, -0.008604458, 0.0038204961, 0.022588843, -0.14610204, 0.0035155565, 0.02172104, -0.0014584797, -0.024499482, 0.01867444, -0.015081475, 0.00033721662, 0.013129781, -0.025067506, 0.00957885, 0.004622471, -0.019230088, 0.003888119, -0.008080749], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [2.1803794, 1.6401012, 1.4831679, 2.3700933, 0.0, 1.0918939, 0.0, 5.3779707, 0.0, 0.0, 0.0, 4.283732, 2.9393787, 2.458788, 0.0, 0.0, 0.0, 1.976396, 0.0, 1.9957039, 0.0, 3.2273514, 0.0, 1.6155497, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.37159434, 1.1180499, 0.010952814, 0.19934252, -0.015691888, 0.7650251, 0.014498849, -0.008604458, 0.0038204961, 0.78044474, 0.8090963, 1.4535464, 0.02172104, -0.0014584797, -0.024499482, 1.3907844, -0.015081475, 1.0, 0.013129781, 1.3524544, 0.00957885, 0.5769231, -0.019230088, 0.003888119, -0.008080749], "split_indices": [80, 125, 140, 141, 0, 139, 0, 141, 0, 0, 0, 140, 140, 138, 0, 0, 0, 138, 0, 50, 0, 138, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1666.0, 405.0, 1494.0, 172.0, 283.0, 122.0, 1380.0, 114.0, 144.0, 139.0, 1154.0, 226.0, 1051.0, 103.0, 97.0, 129.0, 957.0, 94.0, 823.0, 134.0, 650.0, 173.0, 552.0, 98.0, 394.0, 158.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028300392, 0.012065156, -0.009578014, -1.5884387e-05, -0.012319119, -0.016710754, 0.02264419, -0.004488427, -0.015564983, -0.02104498, 0.01789271, -0.046525292, 0.07763649, -0.017308405, -0.023537084, -0.012134988, 0.028138751, -0.058322053, 0.028698204, 0.013055319, -0.016058838, -0.08514484, 0.0066949166, 0.018278476, -0.019152028, -0.010860271, 0.0004688096, -0.01358171, 0.006791144], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, 23, -1, -1, 25, -1, -1, 27, -1, -1, -1, -1], "loss_changes": [1.7206624, 0.0, 2.1271393, 6.8279138, 0.0, 2.8563023, 0.0, 4.6947827, 0.0, 3.5654683, 0.0, 6.218199, 5.322691, 1.8416134, 0.0, 4.2788744, 0.0, 1.7338235, 3.3916163, 0.0, 0.0, 0.8955982, 0.0, 0.0, 3.5652003, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21, 24, 24], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, 24, -1, -1, 26, -1, -1, 28, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.012065156, 1.1489253, 0.98500085, -0.012319119, 0.8293257, 0.02264419, 0.8026405, -0.015564983, 1.2692307, 0.01789271, 0.66150194, 1.3231709, 0.3435279, -0.023537084, 0.3172657, 0.028138751, 1.0, 0.36353478, 0.013055319, -0.016058838, 0.33689666, 0.0066949166, 0.018278476, 0.47862867, -0.010860271, 0.0004688096, -0.01358171, 0.006791144], "split_indices": [1, 0, 143, 141, 0, 142, 0, 143, 0, 1, 0, 139, 138, 142, 0, 142, 0, 73, 139, 0, 0, 139, 0, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 107.0, 1958.0, 1806.0, 152.0, 1682.0, 124.0, 1546.0, 136.0, 1418.0, 128.0, 1127.0, 291.0, 976.0, 151.0, 202.0, 89.0, 516.0, 460.0, 103.0, 99.0, 425.0, 91.0, 109.0, 351.0, 337.0, 88.0, 150.0, 201.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020057058, 0.0076958546, -0.010810602, -0.007403901, 0.018179305, 0.0018919316, -0.013762573, 0.015534527, -0.07645391, 0.07286909, -0.03450714, 0.0007227432, -0.017950596, -0.0021135262, 0.20024543, -0.017179018, -0.012662755, 0.095138736, -0.09023414, 0.040505726, 0.0069783092, -0.08487594, 0.014668937, 0.022673417, -0.00463617, -0.0030661516, -0.0151508795, -0.011852535, -0.0043286746, 0.07669619, -0.03838067, -0.002627692, 0.019437978, -0.014443703, 0.00658312], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [2.1255848, 4.9737377, 0.0, 2.107516, 0.0, 1.7368658, 0.0, 3.9708583, 2.0782723, 6.1604033, 1.1796458, 0.0, 0.0, 3.479391, 6.386134, 1.3410376, 0.0, 3.5938172, 0.7775116, 0.0, 0.0, 0.27849042, 1.3918906, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.363058, 2.5199318, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.1489253, 0.9660048, -0.010810602, 1.521938, 0.018179305, 2.0, -0.013762573, 1.0, 0.52008766, 0.51898706, 0.69032747, 0.0007227432, -0.017950596, 0.34826937, 0.6633733, 0.26228148, -0.012662755, 1.0, 0.3309855, 0.040505726, 0.0069783092, 1.0, 0.3730786, 0.022673417, -0.00463617, -0.0030661516, -0.0151508795, -0.011852535, -0.0043286746, 1.0, 1.0, -0.002627692, 0.019437978, -0.014443703, 0.00658312], "split_indices": [143, 141, 0, 138, 0, 0, 0, 122, 142, 143, 143, 0, 0, 141, 143, 141, 0, 80, 143, 0, 0, 116, 141, 0, 0, 0, 0, 0, 0, 39, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1892.0, 173.0, 1741.0, 151.0, 1625.0, 116.0, 1384.0, 241.0, 645.0, 739.0, 133.0, 108.0, 406.0, 239.0, 622.0, 117.0, 193.0, 213.0, 93.0, 146.0, 199.0, 423.0, 100.0, 93.0, 108.0, 105.0, 110.0, 89.0, 195.0, 228.0, 104.0, 91.0, 113.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003105839, 0.008049816, -0.009212919, -0.0010631534, 0.019455506, 0.0117203, -0.08624992, -0.0023303044, 0.02317468, -0.024530487, 0.0063603483, -0.009651218, 0.010499114, 0.0028728251, -0.017879175, -0.016234595, 0.06548148, -0.043453354, 0.046031725, -0.0063600093, 0.019456308, -0.018064195, -0.019896192, -0.042632736, 0.022515189, -0.008602277, 0.00047945938, 0.0026980068, -0.010689075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [1.5821068, 3.2105765, 0.0, 1.9612551, 0.0, 4.8412976, 5.601208, 1.1565368, 0.0, 0.0, 0.0, 2.9190497, 0.0, 1.53484, 0.0, 1.6660002, 4.9986167, 2.7005901, 4.748597, 0.0, 0.0, 0.91342926, 0.0, 0.8946363, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.1489253, 1.0291983, -0.009212919, 1.4844531, 0.019455506, 1.4453799, -0.1923077, 0.79509693, 0.02317468, -0.024530487, 0.0063603483, 0.6940378, 0.010499114, 1.0, -0.017879175, 1.0, 0.33102146, 0.55072904, 1.0, -0.0063600093, 0.019456308, 1.21019, -0.019896192, 0.84615386, 0.022515189, -0.008602277, 0.00047945938, 0.0026980068, -0.010689075], "split_indices": [143, 143, 0, 138, 0, 138, 1, 143, 0, 0, 0, 143, 0, 74, 0, 50, 140, 141, 71, 0, 0, 138, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1889.0, 172.0, 1801.0, 88.0, 1566.0, 235.0, 1472.0, 94.0, 114.0, 121.0, 1378.0, 94.0, 1283.0, 95.0, 983.0, 300.0, 684.0, 299.0, 150.0, 150.0, 588.0, 96.0, 200.0, 99.0, 148.0, 440.0, 96.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00095744414, 0.008921839, -0.015224001, 0.015709959, -0.012159198, 0.002335123, 0.020848444, 0.012543509, -0.010856821, -0.0027043645, 0.01936735, -0.01682825, 0.076465376, 0.05727665, -0.04158261, 0.016961468, -0.00064216764, -0.03380041, 0.020575523, -0.10811822, -0.00044314415, -0.00767345, 0.0010941402, -0.061455272, -0.023917157, -0.011292062, 0.0364474, 0.0017630531, -0.010518509, -0.00072903186, 0.019775966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [2.5183377, 1.7382207, 0.0, 4.8085794, 0.0, 1.9744588, 0.0, 4.4106708, 0.0, 1.6470853, 0.0, 2.2930243, 1.7217549, 4.2326975, 2.564793, 0.0, 0.0, 0.37266433, 0.0, 2.189289, 2.402477, 0.0, 0.0, 0.9130199, 0.0, 0.0, 3.076168, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.4244329, -0.015224001, 1.1476526, -0.012159198, 0.9546799, 0.020848444, 0.79509693, -0.010856821, 1.0, 0.01936735, 0.21936503, 1.0, 0.26228148, 1.0, 0.016961468, -0.00064216764, 0.42307693, 0.020575523, 0.53242654, 0.33689666, -0.00767345, 0.0010941402, 1.0, -0.023917157, -0.011292062, 1.2692307, 0.0017630531, -0.010518509, -0.00072903186, 0.019775966], "split_indices": [117, 143, 0, 139, 0, 143, 0, 143, 0, 42, 0, 143, 69, 141, 39, 0, 0, 1, 0, 143, 139, 0, 0, 69, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1962.0, 102.0, 1865.0, 97.0, 1744.0, 121.0, 1597.0, 147.0, 1473.0, 124.0, 1250.0, 223.0, 313.0, 937.0, 105.0, 118.0, 194.0, 119.0, 358.0, 579.0, 99.0, 95.0, 264.0, 94.0, 143.0, 436.0, 94.0, 170.0, 343.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0036897517, -0.0026653837, 0.012893088, 0.0069036325, -0.08834715, 0.015555478, -0.014098044, -0.0008612533, -0.016647095, 0.0037466919, 0.016164156, 0.040736858, -0.024484878, -0.00012296478, 0.025343707, 0.008867692, -0.020767516, 0.04474586, -0.015204106, -0.019221304, 0.013739616, -0.030489745, 0.024137003, -0.03745961, 0.007494786, 0.043600388, -0.0130960485, -0.07353735, 0.019129325, -0.0008535959, 0.009401799, -0.0030268587, -0.013311508, 0.012587336, -0.0070655355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, 25, -1, 27, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6316453, 1.599606, 0.0, 2.2454698, 1.2209171, 2.8602138, 0.0, 0.0, 0.0, 1.6019416, 0.0, 5.770754, 5.3155847, 3.7967277, 0.0, 2.6571336, 0.0, 6.3610506, 0.0, 1.0373613, 0.0, 2.3150501, 0.0, 1.0330501, 0.0, 0.4705178, 0.0, 0.7965566, 1.8880442, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, 26, -1, 28, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.242295, 1.0, 0.012893088, 1.6369263, 0.29588273, 1.0, -0.014098044, -0.0008612533, -0.016647095, -0.03846154, 0.016164156, -0.15384616, 0.6940378, 0.72090995, 0.025343707, 0.55767125, -0.020767516, 0.6252839, -0.015204106, 3.0384614, 0.013739616, 0.3737156, 0.024137003, 1.0, 0.007494786, 0.28597352, -0.0130960485, 0.29397288, 1.2546202, -0.0008535959, 0.009401799, -0.0030268587, -0.013311508, 0.012587336, -0.0070655355], "split_indices": [142, 40, 0, 138, 143, 125, 0, 0, 0, 1, 0, 1, 143, 140, 0, 143, 0, 143, 0, 1, 0, 143, 0, 115, 0, 142, 0, 140, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1951.0, 99.0, 1755.0, 196.0, 1658.0, 97.0, 97.0, 99.0, 1534.0, 124.0, 664.0, 870.0, 557.0, 107.0, 736.0, 134.0, 430.0, 127.0, 604.0, 132.0, 311.0, 119.0, 506.0, 98.0, 179.0, 132.0, 309.0, 197.0, 88.0, 91.0, 179.0, 130.0, 90.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0010655128, -0.0057667512, 0.011889213, 0.004741346, -0.100126274, -0.0052658045, 0.011275187, -0.001606876, -0.018678349, 0.007621781, -0.021200983, -0.0070491745, 0.093213335, -0.049081244, 0.033026442, 0.020413578, -0.005900999, -0.0032565016, -0.018512344, -0.036446802, 0.07585704, -0.05260348, 0.020783886, 0.0063543767, -0.009038113, 0.020982346, 0.0046406914, -0.0071480335, -0.014827983, -0.00781254, 0.013595231, -0.007340891, 0.007963609], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, 25, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.6744472, 1.9493663, 0.0, 1.9120729, 1.4349846, 4.313715, 0.0, 0.0, 0.0, 1.9137022, 0.0, 2.1914837, 3.7653518, 3.9586515, 1.981737, 0.0, 0.0, 4.948036, 0.0, 1.369803, 3.9307258, 1.6743691, 0.0, 0.0, 0.0, 0.0, 2.9235325, 1.5008523, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 21, 21, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, 26, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.3188831, 1.0, 0.011889213, 1.0, 0.30790225, 1.5730919, 0.011275187, -0.001606876, -0.018678349, 0.7577333, -0.021200983, 1.0, 0.81574035, 0.5153528, 0.32812127, 0.020413578, -0.005900999, 0.43474787, -0.018512344, 0.18567689, 0.4558188, 0.31765035, 0.020783886, 0.0063543767, -0.009038113, 0.020982346, 0.5954426, 1.0, -0.014827983, -0.00781254, 0.013595231, -0.007340891, 0.007963609], "split_indices": [139, 40, 0, 125, 140, 138, 0, 0, 0, 139, 0, 111, 143, 140, 140, 0, 0, 140, 0, 140, 140, 140, 0, 0, 0, 0, 139, 17, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1966.0, 114.0, 1769.0, 197.0, 1619.0, 150.0, 100.0, 97.0, 1524.0, 95.0, 1301.0, 223.0, 635.0, 666.0, 129.0, 94.0, 475.0, 160.0, 254.0, 412.0, 385.0, 90.0, 89.0, 165.0, 143.0, 269.0, 261.0, 124.0, 165.0, 104.0, 148.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002759032, -0.008141107, 0.07226208, 0.0036664892, -0.014745377, 0.021011509, -0.0022981795, -0.010657583, 0.012917267, 0.0023593723, -0.015865711, 0.010785581, -0.0063335313, -0.024588242, 0.0697026, 0.0015316758, -0.12504947, -0.003878516, 0.021860741, -0.035627957, 0.12506236, -0.001704167, -0.02639166, 0.0055781, -0.07083277, 0.002174931, 0.023650112, -0.0033869676, -0.01519388], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, -1, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, -1, -1, -1], "loss_changes": [1.5591265, 2.9263625, 3.663174, 2.948326, 0.0, 0.0, 0.0, 2.8358133, 0.0, 1.2407966, 0.0, 0.0, 1.735022, 2.645032, 3.909353, 3.6722844, 3.1197362, 0.0, 0.0, 1.9790914, 2.1299188, 0.0, 0.0, 0.0, 1.33108, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, -1, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, -1, -1, -1], "split_conditions": [0.9964899, 0.878437, 1.6369263, 0.71012646, -0.014745377, 0.021011509, -0.0022981795, 0.7140961, 0.012917267, 1.1743733, -0.015865711, 0.010785581, 1.0, 0.5325959, 0.41382575, 0.44921184, 0.115384616, -0.003878516, 0.021860741, 0.0, 1.0, -0.001704167, -0.02639166, 0.0055781, 0.3172657, 0.002174931, 0.023650112, -0.0033869676, -0.01519388], "split_indices": [139, 142, 138, 142, 0, 0, 0, 139, 0, 138, 0, 0, 61, 140, 139, 142, 1, 0, 0, 0, 16, 0, 0, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1779.0, 279.0, 1640.0, 139.0, 114.0, 165.0, 1472.0, 168.0, 1353.0, 119.0, 103.0, 1250.0, 1008.0, 242.0, 800.0, 208.0, 140.0, 102.0, 615.0, 185.0, 117.0, 91.0, 171.0, 444.0, 96.0, 89.0, 305.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0054065557, -0.001780126, 0.013914731, -0.010431733, 0.0102269845, 0.0010929026, -0.013479236, -0.015020883, 0.08690962, -0.0017694605, -0.014345772, -0.001968752, 0.026713574, -0.015929846, 0.01365666, -0.0059875576, -0.011319542, -0.03614939, 0.070600845, -0.011952924, -0.014530233, 0.016447313, -0.00042666082, -0.0037390247, 0.012567637], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [1.9790131, 1.7589893, 0.0, 2.5855129, 0.0, 2.2830563, 0.0, 2.3657389, 5.014226, 2.4682033, 0.0, 0.0, 0.0, 1.1053292, 0.0, 2.3955183, 0.0, 1.9649893, 2.0591984, 2.1320605, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.3550751, 1.0, 0.013914731, 0.9546799, 0.0102269845, 1.0, -0.013479236, 0.76681185, 0.53698444, 0.72090995, -0.014345772, -0.001968752, 0.026713574, 0.61489445, 0.01365666, 1.0, -0.011319542, 0.45846936, -0.115384616, 0.3682784, -0.014530233, 0.016447313, -0.00042666082, -0.0037390247, 0.012567637], "split_indices": [139, 125, 0, 143, 0, 42, 0, 142, 143, 140, 0, 0, 0, 141, 0, 71, 0, 143, 1, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1954.0, 105.0, 1804.0, 150.0, 1651.0, 153.0, 1390.0, 261.0, 1260.0, 130.0, 164.0, 97.0, 1143.0, 117.0, 1037.0, 106.0, 744.0, 293.0, 609.0, 135.0, 130.0, 163.0, 514.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021975837, -0.0089985, 0.011990692, -0.016149493, 0.0118412925, -0.009148477, -0.013887729, -0.019558191, 0.011364054, -0.011775968, -0.014556395, -0.021007929, 0.010672881, -0.0023000538, -0.018574115, -0.019294541, 0.06830305, -0.031958908, 0.009747087, 0.022139726, -0.0021578043, -0.014397417, -0.0017252604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1], "loss_changes": [1.7156526, 1.7830579, 0.0, 1.592133, 0.0, 2.2406821, 0.0, 1.584657, 0.0, 1.665116, 0.0, 4.3515134, 0.0, 1.5214269, 0.0, 1.5112922, 3.385028, 1.5188382, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1], "split_conditions": [1.3188831, 1.0, 0.011990692, 1.5730919, 0.0118412925, 0.9524887, -0.013887729, 1.492255, 0.011364054, 0.77939034, -0.014556395, 0.6375918, 0.010672881, 0.48706603, -0.018574115, 1.0, 1.0, -0.3846154, 0.009747087, 0.022139726, -0.0021578043, -0.014397417, -0.0017252604], "split_indices": [139, 102, 0, 138, 0, 140, 0, 138, 0, 142, 0, 139, 0, 139, 0, 41, 53, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1957.0, 109.0, 1853.0, 104.0, 1753.0, 100.0, 1616.0, 137.0, 1522.0, 94.0, 1412.0, 110.0, 1268.0, 144.0, 1022.0, 246.0, 922.0, 100.0, 91.0, 155.0, 107.0, 815.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.001113557, 0.009400008, -0.009003739, -0.0007490079, 0.021026133, 0.01114787, -0.103431165, -0.0034331759, 0.02469265, -0.025192225, 0.0028560883, 0.008014929, -0.017137809, -0.013427626, 0.05080976, 0.004926769, -0.017823081, 0.13072939, -0.012522774, -0.016947433, 0.019508323, 0.0049412525, 0.020053236, -0.0073625604, 0.010237708, -0.048639543, 0.03326742, -0.012951501, -0.0015266223, -0.0020117515, 0.011156535], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [1.5589758, 3.8569276, 0.0, 2.2000966, 0.0, 5.5487704, 3.6651337, 2.9224298, 0.0, 0.0, 0.0, 1.3057884, 0.0, 2.8675704, 2.4042192, 3.5480719, 0.0, 1.1919925, 1.860487, 1.217432, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2658695, 1.2372596, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.1489253, 1.1314709, -0.009003739, 1.492255, 0.021026133, 0.76299673, 0.72203034, 0.76681185, 0.02469265, -0.025192225, 0.0028560883, 1.0, -0.017137809, 1.3777522, 1.0, 0.45471916, -0.017823081, 0.48013937, 1.0, 1.0, 0.019508323, 0.0049412525, 0.020053236, -0.0073625604, 0.010237708, 1.0, 0.32812127, -0.012951501, -0.0015266223, -0.0020117515, 0.011156535], "split_indices": [143, 139, 0, 138, 0, 139, 143, 142, 0, 0, 0, 71, 0, 138, 121, 139, 0, 143, 39, 83, 0, 0, 0, 0, 0, 124, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1892.0, 172.0, 1801.0, 91.0, 1614.0, 187.0, 1520.0, 94.0, 88.0, 99.0, 1423.0, 97.0, 948.0, 475.0, 853.0, 95.0, 210.0, 265.0, 765.0, 88.0, 97.0, 113.0, 173.0, 92.0, 469.0, 296.0, 137.0, 332.0, 176.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0045051267, -0.011266844, 0.013038421, -0.097796634, -0.00096449757, 0.003953927, -0.024329115, -0.018843379, 0.06378732, 0.0052893944, -0.07337196, 0.19014737, -0.05230998, -0.0156077435, 0.016145822, 0.0010685967, -0.017861553, 0.039621543, -0.0004841716, 0.009771294, -0.018375866, 0.043339457, -0.050460562, -0.014111568, 0.009521767, -0.005563713, 0.012650323, 0.013034222, -0.094702095, 0.010216871, -0.0062387246, -0.06663678, -0.016550321, -0.011741479, -0.00035489135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, 27, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [1.8725072, 1.7428039, 0.0, 4.1561775, 2.0224845, 0.0, 0.0, 1.8015028, 5.5452833, 3.0970442, 3.2904444, 7.2727656, 3.884902, 1.7195964, 0.0, 3.2930837, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5599244, 1.4775903, 0.0, 0.0, 0.0, 0.0, 1.4520934, 0.6159868, 0.0, 0.0, 0.71117145, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 21, 21, 22, 22, 27, 27, 28, 28, 31, 31], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, 28, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [1.242295, -0.5, 0.013038421, 0.6419091, 0.71012646, 0.003953927, -0.024329115, 0.47377545, 1.0, 0.49934718, 1.0, 1.0, 0.81106013, 1.0, 0.016145822, 1.0, -0.017861553, 0.039621543, -0.0004841716, 0.009771294, -0.018375866, 0.2724345, 1.2262855, -0.014111568, 0.009521767, -0.005563713, 0.012650323, 1.0, 0.39102602, 0.010216871, -0.0062387246, 1.0, -0.016550321, -0.011741479, -0.00035489135], "split_indices": [142, 1, 0, 141, 142, 0, 0, 141, 69, 139, 108, 64, 139, 97, 0, 39, 0, 0, 0, 0, 0, 139, 138, 0, 0, 0, 0, 23, 140, 0, 0, 109, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1955.0, 98.0, 208.0, 1747.0, 107.0, 101.0, 1369.0, 378.0, 949.0, 420.0, 181.0, 197.0, 837.0, 112.0, 246.0, 174.0, 88.0, 93.0, 92.0, 105.0, 311.0, 526.0, 98.0, 148.0, 142.0, 169.0, 216.0, 310.0, 99.0, 117.0, 222.0, 88.0, 123.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.002455002, 0.005291196, -0.01463443, -0.002065369, 0.013721424, 0.008152241, -0.0769185, -0.0037731617, 0.013404085, -0.01753151, 0.003067401, 0.009537042, -0.089235224, -0.0021157225, 0.08327206, -0.02623846, 0.0052716024, 0.01148009, -0.011231921, 0.013020272, 0.0036868674, -0.00011205564, 0.012689203, 0.012440509, -0.015214387, -0.003766397, 0.008326444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, -1, -1, 21, -1, -1, -1, 23, -1, -1, 25, -1, -1], "loss_changes": [2.3127854, 1.910916, 0.0, 1.4263898, 0.0, 2.4635882, 2.3714294, 1.7051389, 0.0, 0.0, 0.0, 1.1144046, 4.9649124, 1.6781029, 0.3854599, 0.0, 0.0, 1.3338588, 0.0, 0.0, 0.0, 1.703732, 0.0, 0.0, 1.7863331, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, -1, -1, 22, -1, -1, -1, 24, -1, -1, 26, -1, -1], "split_conditions": [1.6909655, 1.5950041, -0.01463443, 0.83869666, 0.013721424, 0.71012646, 1.0, 0.6375918, 0.013404085, -0.01753151, 0.003067401, 0.55697036, 1.0, 0.4813653, 1.3524544, -0.02623846, 0.0052716024, 0.44341716, -0.011231921, 0.013020272, 0.0036868674, 1.1743733, 0.012689203, 0.012440509, 1.0, -0.003766397, 0.008326444], "split_indices": [138, 138, 0, 142, 0, 142, 124, 139, 0, 0, 0, 142, 39, 142, 138, 0, 0, 142, 0, 0, 0, 138, 0, 0, 61, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1969.0, 106.0, 1865.0, 104.0, 1641.0, 224.0, 1499.0, 142.0, 117.0, 107.0, 1297.0, 202.0, 1120.0, 177.0, 91.0, 111.0, 997.0, 123.0, 88.0, 89.0, 906.0, 91.0, 98.0, 808.0, 658.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.001113897, 0.008752734, -0.014619581, 0.012657731, 0.0017733559, 0.008465187, -0.012809224, -0.00125696, 0.011244711, 0.008339478, -0.01392629, -0.0006704473, 0.012080267, 0.012476383, -0.079881765, 0.0009542251, 0.013252625, -0.01954948, 0.0025726263, -0.046721734, 0.037812307, -0.0017664913, -0.01699737, 0.00040149852, 0.014030874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, -1, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [2.3281934, 1.6175468, 0.0, 0.0, 1.6138045, 1.7852983, 0.0, 2.1388507, 0.0, 1.5300606, 0.0, 1.4558462, 0.0, 1.6584966, 2.4297235, 1.9224254, 0.0, 0.0, 0.0, 1.7082846, 2.137352, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20], "right_children": [2, 4, -1, -1, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.014619581, 0.012657731, 1.6909655, 0.9964899, -0.012809224, 0.855101, 0.011244711, 0.76299673, -0.01392629, 2.0, 0.012080267, 0.7230125, 1.0, 1.0, 0.013252625, -0.01954948, 0.0025726263, 0.6272841, 0.44341716, -0.0017664913, -0.01699737, 0.00040149852, 0.014030874], "split_indices": [117, 1, 0, 0, 138, 139, 0, 139, 0, 139, 0, 0, 0, 141, 106, 124, 0, 0, 0, 143, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1967.0, 102.0, 110.0, 1857.0, 1766.0, 91.0, 1615.0, 151.0, 1510.0, 105.0, 1398.0, 112.0, 1199.0, 199.0, 1094.0, 105.0, 95.0, 104.0, 477.0, 617.0, 386.0, 91.0, 464.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029224295, -0.009490133, 0.013164706, -0.0019235923, -0.011911559, 0.007934577, -0.018503388, -0.00527867, 0.025393853, 0.005647587, -0.06591293, -0.0068161963, 0.010907224, 0.001307262, -0.016377117, 0.0018171468, -0.011705738, -0.017253734, 0.013452961, 0.032482725, -0.06582904, -0.0019180439, 0.012410636, -0.003501546, -0.015500165], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [1.823305, 1.6315976, 0.0, 3.3214438, 0.0, 5.6753936, 0.0, 1.097772, 0.0, 1.8098443, 1.9555353, 1.1925433, 0.0, 0.0, 0.0, 2.9409568, 0.0, 2.4546192, 0.0, 2.376251, 1.4123313, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.242295, 1.0685234, 0.013164706, 1.0800644, -0.011911559, 0.89923126, -0.018503388, 2.0, 0.025393853, 0.6985336, 0.48445827, 0.62681913, 0.010907224, 0.001307262, -0.016377117, 0.5981277, -0.011705738, 0.30075353, 0.013452961, 0.25120658, 0.49242237, -0.0019180439, 0.012410636, -0.003501546, -0.015500165], "split_indices": [142, 141, 0, 140, 0, 140, 0, 0, 0, 140, 143, 140, 0, 0, 0, 139, 0, 143, 0, 142, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1967.0, 96.0, 1840.0, 127.0, 1746.0, 94.0, 1657.0, 89.0, 1404.0, 253.0, 1253.0, 151.0, 140.0, 113.0, 1162.0, 91.0, 1016.0, 146.0, 502.0, 514.0, 321.0, 181.0, 382.0, 132.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034941358, 0.011360745, -0.0100863455, -0.04753505, 0.010264924, -0.094445735, 0.009319698, 0.14520352, -0.013267964, -0.04558469, -0.02880106, 0.0030611402, 0.02754218, -0.03267171, 0.0154089285, -0.11030704, 0.027144529, -0.0008538426, -0.020174308, 0.0012183421, -0.01932237, -0.006134813, 0.010696147, -0.04127688, 0.04706701, -0.000273239, -0.01303639, -0.008220232, 0.020827299, -0.006702705, 0.029430594, -0.07388825, 0.013049419, -0.0013458276, 0.0068030604, 0.0004456854, -0.015223338], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1, 25, 27, 29, -1, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1], "loss_changes": [1.5933217, 0.0, 1.4891996, 4.5420628, 4.020177, 4.8802147, 0.0, 2.8053327, 3.5006506, 1.939369, 0.0, 0.0, 0.0, 5.1965876, 0.0, 2.2141173, 1.3702637, 1.5748675, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6109254, 3.3155003, 0.5988191, 0.0, 2.5232208, 0.0, 0.0, 0.34600198, 1.1539365, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 23, 23, 24, 24, 25, 25, 27, 27, 30, 30, 31, 31], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1, 26, 28, 30, -1, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.011360745, -0.1923077, 1.0, -0.03846154, 0.7878005, 0.009319698, 0.579256, 0.91847384, 1.0, -0.02880106, 0.0030611402, 0.02754218, 0.6774205, 0.0154089285, -0.42307693, 0.47256365, 0.32587615, -0.020174308, 0.0012183421, -0.01932237, -0.006134813, 0.010696147, 0.2928758, 1.0, 0.13804872, -0.01303639, 0.5114968, 0.020827299, -0.006702705, 0.19985382, 1.28009, 0.013049419, -0.0013458276, 0.0068030604, 0.0004456854, -0.015223338], "split_indices": [1, 0, 1, 61, 1, 140, 0, 143, 140, 69, 0, 0, 0, 143, 0, 1, 143, 140, 0, 0, 0, 0, 0, 141, 74, 142, 0, 139, 0, 0, 141, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 110.0, 1954.0, 688.0, 1266.0, 516.0, 172.0, 188.0, 1078.0, 412.0, 104.0, 100.0, 88.0, 966.0, 112.0, 218.0, 194.0, 813.0, 153.0, 88.0, 130.0, 92.0, 102.0, 441.0, 372.0, 302.0, 139.0, 277.0, 95.0, 93.0, 209.0, 188.0, 89.0, 99.0, 110.0, 94.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.003337064, 0.0103075905, -0.0022039814, -0.010628472, 0.0063498123, -0.007847061, 0.08855344, 0.003547889, -0.017087385, 0.024393868, -0.0061119073, -0.008333352, 0.016508479, -0.062514305, 0.026313417, -0.010987494, -0.017493641, 0.0036923848, 0.1024789, -0.04804548, 0.0076635554, 0.02874514, -0.012086232, 0.02430084, -0.0021187023, 0.0024982826, -0.013708879, 0.09475753, -0.02081897, 0.0011351971, 0.015524246, 0.006045343, -0.0077755586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [1.1445539, 0.0, 1.7467397, 0.0, 2.115834, 2.8719773, 6.2095933, 2.7733295, 0.0, 0.0, 0.0, 2.5267045, 0.0, 3.041195, 1.4145348, 1.1689681, 0.0, 1.9752375, 3.267198, 1.645179, 0.0, 1.7242625, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1401196, 1.39284, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 27, 27, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0103075905, -0.5, -0.010628472, 0.87652975, 0.81910807, 1.0, 0.71012646, -0.017087385, 0.024393868, -0.0061119073, 1.0, 0.016508479, 0.46940464, 0.55767125, 0.373101, -0.017493641, 1.0, 1.0, 0.25369966, 0.0076635554, 0.2628967, -0.012086232, 0.02430084, -0.0021187023, 0.0024982826, -0.013708879, 0.15458627, 0.373101, 0.0011351971, 0.015524246, 0.006045343, -0.0077755586], "split_indices": [1, 0, 1, 0, 139, 142, 69, 142, 0, 0, 0, 69, 0, 141, 143, 142, 0, 58, 122, 142, 0, 143, 0, 0, 0, 0, 0, 143, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 109.0, 1962.0, 149.0, 1813.0, 1546.0, 267.0, 1445.0, 101.0, 131.0, 136.0, 1346.0, 99.0, 525.0, 821.0, 360.0, 165.0, 633.0, 188.0, 253.0, 107.0, 527.0, 106.0, 88.0, 100.0, 139.0, 114.0, 226.0, 301.0, 95.0, 131.0, 124.0, 177.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0022013076, 0.00810153, -0.010734241, -0.00087981526, 0.017072495, -0.012957767, 0.057434034, 0.0058428715, -0.12740019, -0.0039484175, 0.017149426, -0.022445157, 0.055759463, -0.0012546583, -0.025563478, 0.00033050534, -0.01906694, 0.110074595, -0.00086693955, -0.032291785, 0.017394746, -0.00091602886, 0.01805316, 0.010434325, -0.01164103, -0.06979894, 0.008315231, -0.039368283, -0.017674094, 0.0039838306, -0.006841071], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [1.340493, 2.8744164, 0.0, 1.3135414, 0.0, 3.3242083, 3.5374446, 1.8737799, 3.2107482, 0.0, 0.0, 3.245212, 1.4763222, 0.0, 0.0, 4.225182, 0.0, 2.058229, 2.8567398, 2.7192273, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5425446, 0.0, 0.8488297, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.6909655, 1.5950041, -0.010734241, 1.0, 0.017072495, 0.76681185, 0.53698444, 1.0, 1.0, -0.0039484175, 0.017149426, 1.3777522, 1.0, -0.0012546583, -0.025563478, 0.42782113, -0.01906694, 0.43944928, 0.5418762, 1.0, 0.017394746, -0.00091602886, 0.01805316, 0.010434325, -0.01164103, 0.31522822, 0.008315231, 0.17566948, -0.017674094, 0.0039838306, -0.006841071], "split_indices": [138, 138, 0, 42, 0, 142, 143, 71, 105, 0, 0, 138, 15, 0, 0, 139, 0, 141, 141, 62, 0, 0, 0, 0, 0, 139, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1968.0, 106.0, 1865.0, 103.0, 1545.0, 320.0, 1327.0, 218.0, 173.0, 147.0, 847.0, 480.0, 115.0, 103.0, 746.0, 101.0, 245.0, 235.0, 628.0, 118.0, 91.0, 154.0, 123.0, 112.0, 474.0, 154.0, 369.0, 105.0, 99.0, 270.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031520994, 0.0029515014, -0.010782353, 0.012080286, -0.0026694888, -0.007958397, 0.00927467, 0.0033706264, -0.012502499, -0.008581378, 0.01942253, -0.0007624206, -0.013255524, -0.012848038, 0.012968232, 0.0036222008, -0.11391952, -0.011740449, 0.077188715, -0.0051661204, -0.017550109, -0.0027145832, 0.008643827, 0.016738458, -0.0005862888], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, -1, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3218278, 1.2950732, 0.0, 0.0, 0.9416719, 2.3448107, 0.0, 3.6771271, 0.0, 1.4704982, 0.0, 2.2496727, 0.0, 2.1740613, 0.0, 1.2691885, 0.70161486, 1.4050945, 1.4532372, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18], "right_children": [2, 4, -1, -1, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1743733, -0.010782353, 0.012080286, 1.2170341, 1.0800644, 0.00927467, 0.9305812, -0.012502499, 0.896009, 0.01942253, 0.77939034, -0.013255524, 0.66150194, 0.012968232, 0.55697036, -0.115384616, 0.49934718, 1.356032, -0.0051661204, -0.017550109, -0.0027145832, 0.008643827, 0.016738458, -0.0005862888], "split_indices": [43, 138, 0, 0, 141, 140, 0, 140, 0, 141, 0, 142, 0, 139, 0, 142, 1, 139, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1955.0, 114.0, 89.0, 1866.0, 1768.0, 98.0, 1612.0, 156.0, 1517.0, 95.0, 1427.0, 90.0, 1306.0, 121.0, 1123.0, 183.0, 929.0, 194.0, 91.0, 92.0, 803.0, 126.0, 93.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0043345713, 0.009812318, -0.009705539, 0.0011712475, 0.01938278, 0.008440259, -0.013962629, -0.00086046895, 0.01874793, 0.00851567, -0.011654875, -0.0024684032, 0.015259506, 0.0064211534, -0.012982303, 0.010750524, -0.00065155205, -0.06490551, 0.016907334, -0.0019476733, -0.012030644, 4.8727055e-05, 0.006771531], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, 17, 19, 21, -1, -1, -1, -1], "loss_changes": [1.1485431, 3.1197574, 0.0, 1.9179617, 0.0, 2.9673753, 0.0, 1.8374981, 0.0, 2.4799008, 0.0, 1.6483759, 0.0, 0.973031, 0.0, 0.0, 1.4351062, 0.68708515, 0.83343565, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, 18, 20, 22, -1, -1, -1, -1], "split_conditions": [1.6909655, 1.1314709, -0.009705539, 1.54695, 0.01938278, 0.87652975, -0.013962629, 0.81910807, 0.01874793, 0.71012646, -0.011654875, 0.7140961, 0.015259506, 0.091577284, -0.012982303, 0.010750524, 0.22045416, 0.21721204, 1.0, -0.0019476733, -0.012030644, 4.8727055e-05, 0.006771531], "split_indices": [138, 139, 0, 138, 0, 139, 0, 142, 0, 142, 0, 139, 0, 139, 0, 0, 139, 143, 74, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1962.0, 106.0, 1874.0, 88.0, 1782.0, 92.0, 1694.0, 88.0, 1567.0, 127.0, 1456.0, 111.0, 1361.0, 95.0, 89.0, 1272.0, 273.0, 999.0, 150.0, 123.0, 755.0, 244.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.00071599986, 0.0049464316, -0.011104935, 0.009906176, 6.7121204e-05, 0.009289807, -0.06672624, -0.0008159108, 0.018175334, 0.002306826, -0.0142650055, -0.02189004, 0.04819159, 0.005668826, -0.02458348, 0.02391959, -0.0012327016, -0.015213071, 0.020915134, -0.0163314, 0.07436252, -0.001102539, -0.012867917, 0.01730949, -0.0022055798, -0.025374897, 0.051586226, 0.007354354, -0.0049014348, 0.017127065, -0.0021163116], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [1.2926184, 0.9037404, 0.0, 0.0, 1.1525621, 2.8652754, 1.5475824, 1.6039234, 0.0, 0.0, 0.0, 6.702428, 4.408599, 4.1088815, 0.0, 0.0, 4.5457044, 1.4041355, 0.0, 0.0, 2.4084616, 0.99752676, 0.0, 0.0, 0.0, 1.2486935, 2.1419137, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 20, 20, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.1743733, -0.011104935, 0.009906176, 1.3461539, 1.1153846, 0.37820587, 0.7707342, 0.018175334, 0.002306826, -0.0142650055, 0.81395763, 0.8743044, 0.71012646, -0.02458348, 0.02391959, 1.0, 0.5912575, 0.020915134, -0.0163314, 1.0, 1.0, -0.012867917, 0.01730949, -0.0022055798, 1.2135607, 0.4237185, 0.007354354, -0.0049014348, 0.017127065, -0.0021163116], "split_indices": [117, 138, 0, 0, 1, 1, 142, 143, 0, 0, 0, 140, 143, 142, 0, 0, 89, 141, 0, 0, 105, 71, 0, 0, 0, 138, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1968.0, 101.0, 97.0, 1871.0, 1644.0, 227.0, 1553.0, 91.0, 104.0, 123.0, 1086.0, 467.0, 967.0, 119.0, 96.0, 371.0, 877.0, 90.0, 118.0, 253.0, 780.0, 97.0, 125.0, 128.0, 534.0, 246.0, 103.0, 431.0, 93.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00061504176, 0.006641666, -0.011119315, -0.0019001423, 0.015507574, -0.014736653, 0.05961822, 0.004252929, -0.1309813, -0.0032802874, 0.01671285, -0.012944869, 0.075752445, -0.004055166, -0.023027657, 0.061385456, -0.029148368, -0.0017364109, 0.024838429, -0.00828544, 0.021027824, 0.028214822, -0.050979268, -0.0057919384, 0.009125509, -0.015383355, -0.1349247, 0.031547558, -0.009465083, -0.017493641, -0.009086681, -0.00527529, 0.01784474], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, -1, -1, -1, -1, 23, 25, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.3833642, 2.469861, 0.0, 1.4545926, 0.0, 3.3641348, 3.1597176, 1.610821, 1.9215555, 0.0, 0.0, 1.2718583, 4.0830207, 0.0, 0.0, 4.059016, 1.0857356, 0.0, 0.0, 0.0, 0.0, 1.2977518, 1.8765352, 0.0, 0.0, 1.6405622, 0.32964897, 3.4302921, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 21, 21, 22, 22, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, -1, -1, -1, -1, 24, 26, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.6909655, 1.5950041, -0.011119315, 1.0, 0.015507574, 0.76681185, 0.53698444, 1.0, 1.0, -0.0032802874, 0.01671285, 0.17894638, 0.47509506, -0.004055166, -0.023027657, 0.21688554, 0.0, -0.0017364109, 0.024838429, -0.00828544, 0.021027824, -0.1923077, 1.0, -0.0057919384, 0.009125509, 0.47509506, 0.45518103, 0.40986034, -0.009465083, -0.017493641, -0.009086681, -0.00527529, 0.01784474], "split_indices": [138, 138, 0, 42, 0, 142, 143, 61, 105, 0, 0, 143, 140, 0, 0, 141, 0, 0, 0, 0, 0, 1, 109, 0, 0, 140, 142, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1948.0, 105.0, 1842.0, 106.0, 1524.0, 318.0, 1310.0, 214.0, 171.0, 147.0, 1056.0, 254.0, 112.0, 102.0, 189.0, 867.0, 165.0, 89.0, 96.0, 93.0, 239.0, 628.0, 101.0, 138.0, 441.0, 187.0, 277.0, 164.0, 98.0, 89.0, 176.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001943692, -0.0071517536, 0.010665115, -0.000612139, -0.0141213825, -0.008604061, 0.012304855, 0.0045060124, -0.023979248, -0.006193512, 0.0130894175, -0.059910487, 0.010531892, -0.11125394, 0.007876974, 0.05884013, -0.02632714, -0.018931348, -0.006149097, 0.023109745, 0.019291412, 0.0091708675, -0.013869983, -0.04535742, 0.08818746, -0.03260721, 0.01189527, 0.002287148, -0.012108404, 0.018896017, -0.0020896357, -0.010631904, 0.0007570471], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5320756, 1.6964415, 0.0, 1.8233883, 0.0, 5.2525454, 0.0, 2.2177613, 0.0, 1.3584386, 0.0, 2.5561957, 2.0530262, 1.0177314, 0.0, 2.3904662, 2.608811, 0.0, 0.0, 1.7555408, 0.0, 2.2794778, 0.0, 0.9920151, 2.2205203, 1.0661657, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.178437, 1.0516728, 0.010665115, 0.9014308, -0.0141213825, 0.9883086, 0.012304855, 0.7702536, -0.023979248, 1.0, 0.0130894175, 0.1923077, 0.1923077, 1.0, 0.007876974, 1.0, 0.47377545, -0.018931348, -0.006149097, 1.0, 0.019291412, 0.36752534, -0.013869983, 0.320987, 0.5867963, 1.0, 0.01189527, 0.002287148, -0.012108404, 0.018896017, -0.0020896357, -0.010631904, 0.0007570471], "split_indices": [142, 142, 0, 142, 0, 140, 0, 140, 0, 5, 0, 1, 1, 111, 0, 83, 141, 0, 0, 71, 0, 140, 0, 139, 141, 81, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1935.0, 126.0, 1845.0, 90.0, 1733.0, 112.0, 1640.0, 93.0, 1512.0, 128.0, 359.0, 1153.0, 262.0, 97.0, 499.0, 654.0, 102.0, 160.0, 394.0, 105.0, 497.0, 157.0, 192.0, 202.0, 360.0, 137.0, 101.0, 91.0, 105.0, 97.0, 127.0, 233.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0055751046, 0.0033809703, -0.06731414, -0.006255313, 0.01878623, -0.02122604, 0.013429289, -0.012702189, 0.011071964, -0.08425429, 0.0020415573, -0.0202575, -0.026982017, 0.013662341, -0.009039122, 0.007480785, -0.0116557125, -0.05128894, 0.029037077, -0.031465735, -0.016504705, 0.08535351, -0.0054705343, -0.06972619, 0.024987178, 0.020857275, -0.0009901054, -0.072753936, 0.06659441, -0.02445353, -0.017756667, -0.008511468, 0.01172347, 0.0030020166, -0.019125879, 0.016825208, -0.0038207278, -0.008816923, 0.002076404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, -1, 15, -1, 17, -1, -1, 19, 21, 23, -1, 25, 27, 29, 31, -1, -1, 33, 35, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1479025, 3.2229965, 7.6854353, 1.299354, 0.0, 0.0, 0.0, 1.7227271, 0.0, 1.8906415, 2.0191643, 0.0, 1.7141536, 0.0, 2.0124993, 0.0, 0.0, 1.3372442, 1.2787216, 1.0907569, 0.0, 2.9343, 1.9782999, 1.4695492, 2.0719512, 0.0, 0.0, 2.569818, 2.0988176, 0.61078686, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, -1, 16, -1, 18, -1, -1, 20, 22, 24, -1, 26, 28, 30, 32, -1, -1, 34, 36, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.178437, 1.0, 1.0, 0.01878623, -0.02122604, 0.013429289, -0.3846154, 0.011071964, 1.0, -0.30769232, -0.0202575, 0.6633733, 0.013662341, 1.0, 0.007480785, -0.0116557125, 1.4482117, 0.1923077, 1.0, -0.016504705, 1.0, 1.2307693, 0.28295493, 0.44131443, 0.020857275, -0.0009901054, 1.2889981, 1.0, 0.1302929, -0.017756667, -0.008511468, 0.01172347, 0.0030020166, -0.019125879, 0.016825208, -0.0038207278, -0.008816923, 0.002076404], "split_indices": [0, 142, 106, 102, 0, 0, 0, 1, 0, 5, 1, 0, 143, 0, 111, 0, 0, 138, 1, 71, 0, 69, 1, 142, 140, 0, 0, 138, 13, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1813.0, 263.0, 1723.0, 90.0, 153.0, 110.0, 1633.0, 90.0, 279.0, 1354.0, 91.0, 188.0, 103.0, 1251.0, 88.0, 100.0, 593.0, 658.0, 505.0, 88.0, 250.0, 408.0, 301.0, 204.0, 109.0, 141.0, 211.0, 197.0, 212.0, 89.0, 93.0, 111.0, 113.0, 98.0, 100.0, 97.0, 88.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.0016764842, -0.0040264395, 0.010527963, 0.0049725557, -0.01472346, -0.0022617502, 0.013400899, 0.004784696, -0.0119631365, 0.011954448, -0.008656987, 0.0026357344, 0.0138333915, 0.011580514, -0.011329559, -0.0057007056, 0.08792952, 0.015163344, -0.08479865, 0.022049403, -0.0012209113, -0.0005021619, 0.0076579223, -0.014713257, -0.0025174883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2230408, 2.528488, 0.0, 1.7232211, 0.0, 1.445664, 0.0, 1.0800776, 0.0, 1.8006939, 0.0, 1.4766593, 0.0, 1.7442522, 0.0, 1.7790275, 3.239059, 1.0574448, 0.8362303, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3550751, 1.0928699, 0.010527963, 0.928859, -0.01472346, 1.0, 0.013400899, 0.9546799, -0.0119631365, 0.79509693, -0.008656987, 0.7027832, 0.0138333915, 0.55767125, -0.011329559, 1.3447025, 1.0, 1.0, 1.0, 0.022049403, -0.0012209113, -0.0005021619, 0.0076579223, -0.014713257, -0.0025174883], "split_indices": [139, 139, 0, 139, 0, 117, 0, 143, 0, 143, 0, 143, 0, 143, 0, 138, 115, 74, 71, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1962.0, 108.0, 1846.0, 116.0, 1748.0, 98.0, 1649.0, 99.0, 1529.0, 120.0, 1424.0, 105.0, 1322.0, 102.0, 1078.0, 244.0, 853.0, 225.0, 105.0, 139.0, 642.0, 211.0, 110.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.00035995673, 0.0072644013, -0.07797097, -0.002077478, 0.018406447, -0.0139765665, -0.00148181, 0.0059039514, -0.011242076, -0.0010895499, 0.009401063, 0.010146669, -0.010814149, -0.0024150994, 0.08240734, 0.010267559, -0.01084535, 0.021954611, -0.003269124, -0.00010046599, -0.00766403, -0.013469712, 0.010114117, -0.0036420107, 0.0041931025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, 17, -1, 19, -1, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.2171973, 3.0935314, 0.7180618, 1.5667604, 0.0, 0.0, 0.0, 1.0222334, 0.0, 1.8487947, 0.0, 1.2626413, 0.0, 1.0498396, 3.251603, 0.0, 0.7755339, 0.0, 0.0, 1.276373, 0.0, 1.0591335, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 16, 16, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, 18, -1, 20, -1, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.6257311, 0.9792887, 1.432192, 0.9068304, 0.018406447, -0.0139765665, -0.00148181, 0.77939034, -0.011242076, 0.6731602, 0.009401063, 0.5647758, -0.010814149, 0.091577284, 1.0, 0.010267559, 0.48442143, 0.021954611, -0.003269124, 0.43847784, -0.00766403, 1.0, 0.010114117, -0.0036420107, 0.0041931025], "split_indices": [138, 139, 139, 141, 0, 0, 0, 142, 0, 142, 0, 142, 0, 139, 106, 0, 142, 0, 0, 142, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1873.0, 184.0, 1779.0, 94.0, 93.0, 91.0, 1659.0, 120.0, 1537.0, 122.0, 1391.0, 146.0, 1185.0, 206.0, 88.0, 1097.0, 94.0, 112.0, 943.0, 154.0, 833.0, 110.0, 589.0, 244.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.00033246673, 0.007854487, -0.009038897, -0.0009695851, 0.018049939, -0.010968881, 0.06371464, 0.0016682415, -0.02222099, -0.002013232, 0.019483706, -0.007209026, 0.012081512, 0.0048454837, -0.01362026, -0.009066944, 0.064739674, 0.010194064, -0.06045282, 0.018809171, -0.0022858123, -0.00964061, 0.01354864, -0.011399547, 0.0014972442, 0.005189067, -0.0039617396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [1.5217633, 2.8823318, 0.0, 1.1642339, 0.0, 4.161718, 2.6496067, 1.555875, 0.0, 0.0, 0.0, 2.128733, 0.0, 1.0432588, 0.0, 1.0055798, 2.550067, 1.8365134, 1.1186559, 0.0, 0.0, 1.1767974, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.1489253, 1.2103982, -0.009038897, 0.8026405, 0.018049939, 0.89335084, -0.15384616, 0.76299673, -0.02222099, -0.002013232, 0.019483706, 0.6774205, 0.012081512, 0.49934718, -0.01362026, 0.3992645, 0.48037243, 1.3846154, 0.48727876, 0.018809171, -0.0022858123, -0.07692308, 0.01354864, -0.011399547, 0.0014972442, 0.005189067, -0.0039617396], "split_indices": [143, 140, 0, 143, 0, 140, 1, 139, 0, 0, 0, 143, 0, 139, 0, 141, 141, 1, 141, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1892.0, 172.0, 1800.0, 92.0, 1559.0, 241.0, 1471.0, 88.0, 147.0, 94.0, 1369.0, 102.0, 1252.0, 117.0, 1016.0, 236.0, 739.0, 277.0, 98.0, 138.0, 638.0, 101.0, 162.0, 115.0, 209.0, 429.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.00089587714, 0.0063361516, -0.008228831, -0.0028112864, 0.015393132, 0.0046167634, -0.013998802, -0.008378602, 0.012707618, -0.0014332893, -0.012266053, -0.008891063, 0.010243732, 0.0033875257, -0.012133711, -0.006052948, 0.008377909, 0.008110749, -0.015270625, -0.009214983, 0.0017970633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [1.2190582, 2.5679245, 0.0, 1.82495, 0.0, 2.7037969, 0.0, 1.2191591, 0.0, 1.1216836, 0.0, 1.8652974, 0.0, 0.92438227, 0.0, 2.2640975, 0.0, 0.9826261, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.1489253, 1.0268755, -0.008228831, 1.0675184, 0.015393132, 0.78044474, -0.013998802, 1.4844531, 0.012707618, 1.4302269, -0.012266053, 0.6425222, 0.010243732, 0.5461393, -0.012133711, 0.579256, 0.008377909, 0.09826693, -0.015270625, -0.009214983, 0.0017970633], "split_indices": [143, 142, 0, 140, 0, 140, 0, 138, 0, 138, 0, 142, 0, 142, 0, 143, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1902.0, 169.0, 1791.0, 111.0, 1699.0, 92.0, 1536.0, 163.0, 1448.0, 88.0, 1351.0, 97.0, 1218.0, 133.0, 1090.0, 128.0, 994.0, 96.0, 89.0, 905.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [-0.001776062, -0.0075041098, 0.010363069, 0.00018189957, -0.013028151, -0.012263955, 0.012835904, -0.0012521076, -0.015246354, -0.008507548, 0.009478351, 0.003966407, -0.0100413365, -0.0077367024, 0.008156918, 0.008757649, -0.011624037, -0.004230508, 0.013849166, 0.004010827, -0.0026857003], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [1.2534364, 1.8580831, 0.0, 2.956043, 0.0, 2.607573, 0.0, 1.0911583, 0.0, 1.669201, 0.0, 1.1643047, 0.0, 1.9937236, 0.0, 1.6294008, 0.0, 0.8818401, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.3550751, 1.0928699, 0.010363069, 0.9524887, -0.013028151, 0.83869666, 0.012835904, 0.7128561, -0.015246354, 0.6375918, 0.009478351, 0.55697036, -0.0100413365, 0.5153528, 0.008156918, 3.0384614, -0.011624037, -0.07692308, 0.013849166, 0.004010827, -0.0026857003], "split_indices": [139, 139, 0, 140, 0, 142, 0, 142, 0, 139, 0, 142, 0, 140, 0, 1, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1969.0, 107.0, 1853.0, 116.0, 1689.0, 164.0, 1566.0, 123.0, 1456.0, 110.0, 1282.0, 174.0, 1114.0, 168.0, 967.0, 147.0, 879.0, 88.0, 297.0, 582.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.0025769053, 0.008407963, -0.010496757, 0.0001958435, 0.018058563, 0.007087534, -0.013269389, -0.0005062559, 0.014434735, 0.009164422, -0.011883883, 0.0002533388, 0.015761493, 0.010574838, -0.013633114, 0.0010077095, 0.01078929, 0.013552885, -0.015385098, -0.00011621793, 0.0147021115], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [1.2924489, 2.764259, 0.0, 1.7089477, 0.0, 1.8490801, 0.0, 1.923663, 0.0, 2.055716, 0.0, 2.0667026, 0.0, 1.2690269, 0.0, 2.4109266, 0.0, 2.254665, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.6909655, 1.1314709, -0.010496757, 1.54695, 0.018058563, 0.86858714, -0.013269389, 0.81910807, 0.014434735, 0.7300078, -0.011883883, 0.7140961, 0.015761493, 0.5981277, -0.013633114, 0.54286444, 0.01078929, 0.48394743, -0.015385098, -0.00011621793, 0.0147021115], "split_indices": [138, 139, 0, 138, 0, 139, 0, 142, 0, 142, 0, 139, 0, 139, 0, 139, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1955.0, 106.0, 1866.0, 89.0, 1774.0, 92.0, 1681.0, 93.0, 1554.0, 127.0, 1466.0, 88.0, 1363.0, 103.0, 1241.0, 122.0, 1148.0, 93.0, 1034.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.004197112, -0.0018226003, 0.011435225, 0.005049997, -0.01140345, -0.0070930207, 0.012951595, 0.0039061122, -0.015412419, -0.00491526, 0.014185739, 0.003949015, -0.011559674, -0.0066898502, 0.015419346, 0.0042703697, -0.012967619, -0.00974836, 0.016429272, 0.0003952754, -0.011958757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [1.3693063, 1.5099849, 0.0, 2.788519, 0.0, 2.7185395, 0.0, 1.9032624, 0.0, 1.4422336, 0.0, 2.1754644, 0.0, 1.7132543, 0.0, 2.6179435, 0.0, 1.614779, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.3550751, 1.0928699, 0.011435225, 0.9524887, -0.01140345, 1.492255, 0.012951595, 0.74006224, -0.015412419, 0.6569235, 0.014185739, 0.5981277, -0.011559674, 0.54286444, 0.015419346, 0.49648586, -0.012967619, 0.579256, 0.016429272, 0.0003952754, -0.011958757], "split_indices": [139, 139, 0, 140, 0, 138, 0, 139, 0, 139, 0, 139, 0, 139, 0, 139, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1958.0, 107.0, 1845.0, 113.0, 1681.0, 164.0, 1564.0, 117.0, 1470.0, 94.0, 1361.0, 109.0, 1271.0, 90.0, 1167.0, 104.0, 1073.0, 94.0, 954.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.002381008, 0.010557878, -0.04941552, 0.0007785141, 0.015902637, -0.0167573, 0.012367479, 0.010821549, -0.017553251, -0.011965882, 0.036886416, -0.024021102, 0.0067533376, 0.0759821, -0.006342235, -0.009818765, -0.011925147, 0.16591237, -0.0042488906, 0.00867144, -0.05247289, -0.034076322, 0.0073229657, 0.0019341622, 0.030470058, -0.012528575, 0.0048573497, -0.012852052, -0.0072679366, 0.0058249542, -0.06042364, -0.01253805, -0.00045835003], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [0.8729024, 2.58443, 5.746988, 2.9570649, 0.0, 0.0, 0.0, 0.9384433, 0.0, 0.80791515, 1.2455698, 0.9900249, 0.0, 4.123149, 1.5024673, 1.2832693, 0.0, 4.475304, 0.0, 0.0, 1.7216496, 1.2482245, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3373291, 0.0, 0.7689659, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 21, 21, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [1.0192571, 1.0, 1.0, 0.896009, 0.015902637, -0.0167573, 0.012367479, 0.4074681, -0.017553251, 1.0, 1.0, 0.47014588, 0.0067533376, 0.115384616, 0.50300807, 1.0, -0.011925147, 1.0, -0.0042488906, 0.00867144, 0.6204358, 1.0, 0.0073229657, 0.0019341622, 0.030470058, -0.012528575, 0.0048573497, -0.012852052, 0.21267514, 0.0058249542, 0.317474, -0.01253805, -0.00045835003], "split_indices": [140, 125, 0, 141, 0, 0, 0, 140, 0, 113, 122, 139, 0, 1, 140, 62, 0, 124, 0, 0, 141, 69, 0, 0, 0, 0, 0, 0, 140, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1780.0, 281.0, 1670.0, 110.0, 167.0, 114.0, 1580.0, 90.0, 843.0, 737.0, 732.0, 111.0, 387.0, 350.0, 637.0, 95.0, 220.0, 167.0, 116.0, 234.0, 493.0, 144.0, 107.0, 113.0, 136.0, 98.0, 109.0, 384.0, 172.0, 212.0, 98.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00017271645, 0.007469074, -0.06844251, 0.014564622, -0.011624635, 0.00024563627, -0.015527374, 0.0041241217, 0.0127518205, 0.016653921, -0.011365598, -0.016063109, 0.04809398, 0.023774788, -0.12915134, 0.0055217324, 0.14548269, -0.018780744, 0.015814308, -0.022038234, -0.003391017, 0.039943166, -0.014164743, 0.023231506, 0.0014269298, 0.02970604, -0.010256066, -0.0063620764, 0.017841792, 0.009800312, -0.0058280253, 0.008048731, -0.007624091], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.0313207, 1.634517, 1.218935, 2.0767326, 0.0, 0.0, 0.0, 2.3789268, 0.0, 1.4987072, 0.0, 3.216712, 3.0805194, 3.0191643, 1.6161435, 2.619005, 2.5749464, 1.6289499, 0.0, 0.0, 0.0, 2.6866734, 0.0, 0.0, 0.0, 1.5263386, 0.0, 1.9056454, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 21, 21, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.6909655, 1.2740794, 1.0170169, -0.011624635, 0.00024563627, -0.015527374, 0.87036675, 0.0127518205, 1.0, -0.011365598, 0.5538719, 0.5623515, 0.4558188, 0.70799714, 0.46940464, 1.0, 0.31222537, 0.015814308, -0.022038234, -0.003391017, 0.3958441, -0.014164743, 0.023231506, 0.0014269298, 0.5769231, -0.010256066, 1.2465608, 0.017841792, 0.009800312, -0.0058280253, 0.008048731, -0.007624091], "split_indices": [40, 138, 138, 139, 0, 0, 0, 142, 0, 111, 0, 141, 141, 140, 139, 141, 108, 140, 0, 0, 0, 142, 0, 0, 0, 1, 0, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1862.0, 198.0, 1761.0, 101.0, 109.0, 89.0, 1612.0, 149.0, 1457.0, 155.0, 714.0, 743.0, 528.0, 186.0, 517.0, 226.0, 401.0, 127.0, 95.0, 91.0, 419.0, 98.0, 136.0, 90.0, 254.0, 147.0, 314.0, 105.0, 143.0, 111.0, 140.0, 174.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00078990223, 0.006063103, -0.0104247965, -0.003519315, 0.018735474, 0.0067589623, -0.08901823, -0.0054371688, 0.022628935, 0.0031456773, -0.024371913, 0.0022876896, -0.011206912, 0.012362398, -0.0073651136, 0.0016850798, 0.009036801, 0.02348955, -0.06157754, 0.048505433, -0.004443811, -0.018358318, -0.0068853428, 0.013047612, 0.0015563935, 0.0036357467, -0.013672587, 0.0066452, -0.0072683357], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1470971, 3.4257834, 0.0, 1.6459583, 0.0, 4.476649, 3.7461572, 1.3047675, 0.0, 0.0, 0.0, 1.1299957, 0.0, 1.0860893, 0.0, 1.5821807, 0.0, 0.5960574, 1.9617897, 1.2151062, 2.175103, 0.0, 0.97956663, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4244329, 1.1155719, -0.0104247965, 1.0, 0.018735474, 0.9660048, 0.73796576, 0.7913911, 0.022628935, 0.0031456773, -0.024371913, 1.0, -0.011206912, 1.4453799, -0.0073651136, 1.3486931, 0.009036801, 0.30075353, 0.45061445, 1.0, 1.0, -0.018358318, 0.5791541, 0.013047612, 0.0015563935, 0.0036357467, -0.013672587, 0.0066452, -0.0072683357], "split_indices": [143, 142, 0, 119, 0, 141, 142, 141, 0, 0, 0, 40, 0, 138, 0, 138, 0, 143, 141, 53, 7, 0, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1972.0, 99.0, 1873.0, 99.0, 1672.0, 201.0, 1584.0, 88.0, 113.0, 88.0, 1477.0, 107.0, 1304.0, 173.0, 1147.0, 157.0, 853.0, 294.0, 450.0, 403.0, 91.0, 203.0, 129.0, 321.0, 308.0, 95.0, 96.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007070815, 0.010646648, -0.0066306987, -0.00880112, 3.835546e-05, 0.009679646, -0.0049178204, -0.05829667, 0.011975253, 0.0012337716, -0.082376584, 0.09230399, -0.0015447105, -0.0031600744, -0.014830937, 0.01956126, -0.0002571216, -0.015745878, 0.00879319, 0.011995663, -0.014718795, -0.01236368, 0.029720364, 0.082093954, -0.024162542, 0.0015801877, 0.017253213, 0.013201867, -0.015472675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, -1, 19, -1, 21, -1, -1, 23, 25, 27, -1, -1, -1, -1], "loss_changes": [1.3090711, 0.0, 1.0604967, 0.0, 0.8660679, 0.0, 1.549177, 0.7024596, 1.4172845, 0.0, 1.0311193, 1.842668, 1.4193408, 0.0, 0.0, 0.0, 0.0, 3.5151355, 0.0, 1.9136194, 0.0, 0.0, 1.9867173, 2.1403337, 7.0759125, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 17, 17, 19, 19, 22, 22, 23, 23, 24, 24], "right_children": [2, -1, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, -1, 20, -1, 22, -1, -1, 24, 26, 28, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010646648, -0.5, -0.00880112, 0.091577284, 0.009679646, 0.285204, -0.07692308, 0.36353478, 0.0012337716, 0.2770242, 1.2622243, 1.2692307, -0.0031600744, -0.014830937, 0.01956126, -0.0002571216, 0.46153846, 0.00879319, 1.3127538, -0.014718795, -0.01236368, 0.74348426, 1.0, 1.0, 0.0015801877, 0.017253213, 0.013201867, -0.015472675], "split_indices": [1, 0, 1, 0, 139, 0, 139, 1, 139, 0, 141, 138, 1, 0, 0, 0, 0, 1, 0, 138, 0, 0, 140, 108, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 108.0, 1954.0, 148.0, 1806.0, 88.0, 1718.0, 413.0, 1305.0, 105.0, 308.0, 188.0, 1117.0, 174.0, 134.0, 90.0, 98.0, 964.0, 153.0, 796.0, 168.0, 92.0, 704.0, 357.0, 347.0, 206.0, 151.0, 158.0, 189.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0072405282, 0.012538016, -0.0083332425, -0.00868743, 0.017395297, 0.010903236, 0.010032083, -0.055522375, 0.018204771, 0.0049843346, -0.014732621, -0.00785768, 0.025186189, 0.014415756, 0.014353916, -0.045079663, 0.031563044, -0.012020882, 0.0019935935, 0.013492495, 0.016070582, -0.01106274, 0.03860985, -0.0006559731, 0.010296914], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1], "loss_changes": [0.98120934, 0.9329116, 0.0, 0.0, 1.242877, 0.0, 0.91346407, 1.828193, 1.0243195, 0.0, 0.0, 0.0, 1.8222642, 0.0, 1.3255489, 1.4214096, 1.6093366, 0.0, 0.0, 0.0, 2.495864, 0.0, 2.1570551, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 6, 6, 7, 7, 8, 8, 12, 12, 14, 14, 15, 15, 16, 16, 20, 20, 22, 22], "right_children": [2, 4, -1, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1], "split_conditions": [1.0, 0.0919324, -0.0083332425, -0.00868743, 0.16867037, 0.010903236, 1.2371556, 0.29713005, 0.25500777, 0.0049843346, -0.014732621, -0.00785768, 0.28910288, 0.014415756, 0.44131443, 1.0, 0.47377545, -0.012020882, 0.0019935935, 0.013492495, 0.5686546, -0.01106274, -0.115384616, -0.0006559731, 0.010296914], "split_indices": [43, 143, 0, 0, 143, 0, 138, 141, 143, 0, 0, 0, 142, 0, 140, 122, 141, 0, 0, 0, 140, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1932.0, 113.0, 90.0, 1842.0, 137.0, 1705.0, 189.0, 1516.0, 88.0, 101.0, 102.0, 1414.0, 118.0, 1296.0, 291.0, 1005.0, 135.0, 156.0, 131.0, 874.0, 132.0, 742.0, 436.0, 306.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056600478, -0.0140680205, 0.041715633, -0.046124294, 0.01925265, -0.015006069, 0.017326174, 0.03015784, -0.07902554, 0.05845259, -0.026023272, 0.0074899117, -0.009702487, -0.0078386925, 0.012234657, -0.020268733, -0.19710411, -0.0061318222, 0.13461347, 0.022212243, -0.011462612, -0.06301345, 0.0117464205, -0.0341694, -0.0021750417, 0.009157362, -0.009632149, 0.006885098, 0.022727877, -0.0104692485, 0.011590032, -0.008755294, -0.016329411, -0.008355419, 0.007284348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.82455015, 1.8777844, 2.3279946, 2.2487607, 1.5298893, 1.6075137, 0.0, 2.7017832, 4.3431377, 2.2724888, 1.7095219, 0.0, 0.0, 0.0, 0.0, 2.4609146, 5.2737064, 2.2030053, 1.2919061, 3.07937, 0.0, 1.7356925, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2634243, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 0.46186653, -0.34615386, 1.0, 0.3172657, 0.017326174, -0.5, 0.61705035, 1.0, 0.8090963, 0.0074899117, -0.009702487, -0.0078386925, 0.012234657, 0.49991417, 1.0, 1.2546202, 0.6936832, 0.42782113, -0.011462612, 0.342185, 0.0117464205, -0.0341694, -0.0021750417, 0.009157362, -0.009632149, 0.006885098, 0.022727877, -0.0104692485, 0.011590032, 1.0, -0.016329411, -0.008355419, 0.007284348], "split_indices": [1, 124, 139, 1, 15, 142, 0, 1, 139, 93, 140, 0, 0, 0, 0, 141, 115, 138, 139, 139, 0, 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1758.0, 312.0, 896.0, 862.0, 218.0, 94.0, 270.0, 626.0, 462.0, 400.0, 104.0, 114.0, 124.0, 146.0, 418.0, 208.0, 250.0, 212.0, 259.0, 141.0, 319.0, 99.0, 114.0, 94.0, 120.0, 130.0, 124.0, 88.0, 110.0, 149.0, 207.0, 112.0, 108.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00020517658, -0.0054913806, 0.00878982, -0.014409758, 0.009778916, 0.0068154642, -0.10590429, -0.0007133202, 0.011983, 0.0006006547, -0.20769891, 0.012398909, -0.04770615, -0.026393104, -0.015209856, -0.0032937515, 0.014906774, -0.0140364915, 0.005272816, -0.03505939, 0.0380932, -0.009192176, -0.016672056, 0.096006475, -0.0050895466, 0.0068630385, -0.050469697, 0.015941178, 0.0025141705, -0.011163704, 0.0018618581], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, 13, 15, 17, -1, -1, 19, -1, -1, -1, 21, 23, 25, -1, 27, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [0.9626687, 1.7961347, 0.0, 3.4858756, 0.0, 1.2397065, 3.8504705, 0.84170306, 0.0, 0.0, 0.55339384, 2.2905376, 2.7732239, 0.0, 0.0, 1.2594664, 0.0, 0.0, 0.0, 1.8458929, 2.1439078, 1.4551818, 0.0, 1.1322865, 0.0, 0.0, 1.2508805, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 20, 20, 21, 21, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, 14, 16, 18, -1, -1, 20, -1, -1, -1, 22, 24, 26, -1, 28, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.3188831, 1.0, 0.00878982, 0.74348426, 0.009778916, 0.67119515, 1.0, 0.5138817, 0.011983, 0.0006006547, 1.0373238, 0.5245731, 1.0, -0.026393104, -0.015209856, 1.0, 0.014906774, -0.0140364915, 0.005272816, 0.4219849, 1.0, 0.19637428, -0.016672056, 0.31522822, -0.0050895466, 0.0068630385, 0.32449418, 0.015941178, 0.0025141705, -0.011163704, 0.0018618581], "split_indices": [139, 125, 0, 140, 0, 140, 50, 141, 0, 0, 140, 139, 121, 0, 0, 115, 0, 0, 0, 143, 23, 140, 0, 139, 0, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1950.0, 117.0, 1795.0, 155.0, 1457.0, 338.0, 1366.0, 91.0, 161.0, 177.0, 1068.0, 298.0, 88.0, 89.0, 958.0, 110.0, 155.0, 143.0, 542.0, 416.0, 453.0, 89.0, 252.0, 164.0, 157.0, 296.0, 133.0, 119.0, 157.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0042354516, 0.014435047, -0.03728612, 0.07672292, 0.0035335682, -0.0052327486, -0.011095716, -0.0025344961, 0.020066252, -0.051955678, 0.026988363, 0.0043742475, -0.006616258, 0.0034246028, -0.08592259, 0.012347805, 0.011988804, -0.001809082, -0.13189743, -0.009974431, 0.030058853, -0.022349982, -0.0038236529, -0.010967528, 0.04934731, 0.01837802, 0.019641163, 0.057994317, -0.008132936, -0.00075579516, 0.02437205, 0.007683718, -0.011814008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1], "loss_changes": [0.8783457, 1.1305845, 0.9658145, 3.1372628, 1.8442098, 0.85045487, 0.0, 0.0, 0.0, 1.2326906, 1.4415134, 0.0, 0.0, 0.0, 0.9418032, 0.0, 1.7403976, 0.0, 1.5443208, 0.0, 1.9998798, 0.0, 0.0, 0.0, 2.6037505, 0.0, 2.0679352, 4.2227263, 0.0, 2.6778097, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 14, 14, 16, 16, 18, 18, 20, 20, 24, 24, 26, 26, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1], "split_conditions": [1.0, 0.2628967, 0.3758132, 0.25120658, 0.44131443, 1.0, -0.011095716, -0.0025344961, 0.020066252, 0.3172657, 0.47377545, 0.0043742475, -0.006616258, 0.0034246028, 0.30221263, 0.012347805, 0.5190839, -0.001809082, 1.0, -0.009974431, 0.5686546, -0.022349982, -0.0038236529, -0.010967528, 0.66987, 0.01837802, 1.6369263, 0.9964899, -0.008132936, 1.0, 0.02437205, 0.007683718, -0.011814008], "split_indices": [80, 143, 140, 142, 140, 116, 0, 0, 0, 142, 141, 0, 0, 0, 140, 0, 143, 0, 16, 0, 140, 0, 0, 0, 143, 0, 138, 139, 0, 108, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1665.0, 409.0, 248.0, 1417.0, 285.0, 124.0, 136.0, 112.0, 421.0, 996.0, 158.0, 127.0, 119.0, 302.0, 134.0, 862.0, 122.0, 180.0, 120.0, 742.0, 91.0, 89.0, 90.0, 652.0, 118.0, 534.0, 387.0, 147.0, 294.0, 93.0, 177.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0043040053, -0.010723473, 0.011613741, -0.0037730213, -0.012181084, -0.01529441, 0.011575254, -0.0021231305, -0.019749708, -0.011863445, 0.012287754, 0.0044969297, -0.1248827, 0.03962318, -0.019444378, -0.005013232, -0.019418249, -0.009572654, 0.072003506, 0.002927453, -0.016214628, -0.009043788, 0.14494608, -0.019225502, 0.014249111, 0.0062444964, -0.008655268, 0.0058956635, 0.02309355, -0.004133929, 0.0058968817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, -1, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.604327, 1.5210512, 0.0, 2.553144, 0.0, 4.058133, 0.0, 1.9200696, 0.0, 2.705142, 0.0, 1.0747576, 0.95833373, 2.270222, 2.4263027, 0.0, 0.0, 0.0, 2.4711323, 2.0312784, 0.0, 1.0971209, 1.6267185, 0.9804408, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, -1, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3550751, 1.0928699, 0.011613741, 0.9524887, -0.012181084, 1.492255, 0.011575254, 0.77939034, -0.019749708, 0.6205023, 0.012287754, 1.0, 0.61489445, -1.0, 0.49575895, -0.005013232, -0.019418249, -0.009572654, 0.44437784, 0.3435279, -0.016214628, 0.3587899, 0.48394743, 1.0, 0.014249111, 0.0062444964, -0.008655268, 0.0058956635, 0.02309355, -0.004133929, 0.0058968817], "split_indices": [139, 139, 0, 140, 0, 138, 0, 142, 0, 139, 0, 137, 141, 0, 140, 0, 0, 0, 141, 142, 0, 141, 139, 93, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1970.0, 105.0, 1854.0, 116.0, 1691.0, 163.0, 1577.0, 114.0, 1463.0, 114.0, 1278.0, 185.0, 518.0, 760.0, 89.0, 96.0, 100.0, 418.0, 657.0, 103.0, 198.0, 220.0, 567.0, 90.0, 103.0, 95.0, 110.0, 110.0, 442.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0030589583, -0.0023065144, 0.008527324, 0.0040903967, -0.013422507, -0.0044022626, 0.013633326, 0.008100377, -0.023967925, -0.0021944675, 0.0097503, 0.013616015, -0.052895516, -0.00011038854, 0.017064007, -0.12232804, 0.006195526, 0.013314637, -0.014371294, -0.0027237996, -0.024515266, -0.002477752, 0.012602103, 0.0019746097, -0.009005814], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, -1, -1], "loss_changes": [0.91443884, 1.642174, 0.0, 2.084462, 0.0, 5.130122, 0.0, 1.5241597, 0.0, 1.1903881, 0.0, 2.4398856, 2.8149562, 2.006911, 0.0, 2.5694668, 0.0, 1.6944683, 0.0, 0.0, 0.0, 1.625222, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, -1, -1], "split_conditions": [1.178437, 1.0516728, 0.008527324, 0.9014308, -0.013422507, 1.0093737, 0.013633326, 0.8026405, -0.023967925, 0.54286444, 0.0097503, 0.4944892, 1.0, 0.579256, 0.017064007, 0.6517612, 0.006195526, 0.43249872, -0.014371294, -0.0027237996, -0.024515266, 0.3992645, 0.012602103, 0.0019746097, -0.009005814], "split_indices": [142, 142, 0, 142, 0, 140, 0, 143, 0, 139, 0, 139, 97, 143, 0, 141, 0, 143, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1946.0, 127.0, 1856.0, 90.0, 1744.0, 112.0, 1656.0, 88.0, 1485.0, 171.0, 1132.0, 353.0, 1041.0, 91.0, 220.0, 133.0, 952.0, 89.0, 124.0, 96.0, 835.0, 117.0, 666.0, 169.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.00025216286, -0.005224433, 0.011125206, 0.0024613505, -0.009081181, -0.0066886498, 0.013810612, 0.0028329857, -0.06506392, -0.0068714526, 0.012368921, -0.017972214, 0.0014377832, 0.0017492161, -0.012030133, -0.008189967, 0.012258879, -0.018884387, 0.011836064, -0.008721507, -0.011180212, -0.007029193, 0.00057796313], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [1.2541012, 1.2932471, 0.0, 2.2390344, 0.0, 0.93934965, 0.0, 1.7041395, 2.1587508, 1.315197, 0.0, 0.0, 0.0, 1.5013084, 0.0, 1.5631601, 0.0, 1.0056916, 0.0, 0.8571278, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.2170341, 1.0800644, 0.011125206, 1.0, -0.009081181, 0.74348426, 0.013810612, 0.70774835, 1.4400707, 0.63230115, 0.012368921, -0.017972214, 0.0014377832, 3.5, -0.012030133, 1.420945, 0.012258879, 1.3461539, 0.011836064, -1.0, -0.011180212, -0.007029193, 0.00057796313], "split_indices": [141, 140, 0, 125, 0, 140, 0, 141, 138, 140, 0, 0, 0, 1, 0, 138, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1966.0, 97.0, 1804.0, 162.0, 1690.0, 114.0, 1453.0, 237.0, 1345.0, 108.0, 97.0, 140.0, 1250.0, 95.0, 1155.0, 95.0, 1065.0, 90.0, 960.0, 105.0, 183.0, 777.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0074479394, -0.03537013, 0.009844529, -0.070288144, 0.05608942, -0.005382718, 0.011436738, -0.005588463, -0.18580073, -0.0063740187, 0.01503816, 0.01122196, -0.022321327, -0.06773444, 0.010723036, -0.012725974, -0.025630165, 0.0067728604, -0.11618172, 0.0037499026, -0.013617301, -0.015275172, 0.0105016325, -0.0020989876, -0.019755537, 0.008651315, -0.03396288, -0.070353225, -0.005206861, -0.0020167453, -0.014212424, 0.0062995143, -0.008257035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, 17, 19, -1, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1], "loss_changes": [0.99610645, 2.5197396, 2.0276928, 4.267441, 2.463181, 2.2151256, 0.0, 2.5661123, 0.84607315, 0.0, 0.0, 0.0, 2.65433, 1.6996789, 0.0, 0.0, 0.0, 1.6072279, 1.7816045, 0.0, 0.0, 1.1527272, 0.0, 0.0, 0.0, 0.0, 0.53577775, 0.81402516, 1.5090349, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 12, 12, 13, 13, 17, 17, 18, 18, 21, 21, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, 18, 20, -1, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1], "split_conditions": [-0.1923077, 1.0, 0.91847384, 0.6558667, 1.0, -0.03846154, 0.011436738, 0.45438722, 1.0, -0.0063740187, 0.01503816, 0.01122196, 0.60124767, 0.2562659, 0.010723036, -0.012725974, -0.025630165, 0.43249872, 0.57724565, 0.0037499026, -0.013617301, 0.104056425, 0.0105016325, -0.0020989876, -0.019755537, 0.008651315, 0.2708205, 0.21567571, 0.3762596, -0.0020167453, -0.014212424, 0.0062995143, -0.008257035], "split_indices": [1, 61, 140, 141, 113, 1, 0, 141, 124, 0, 0, 0, 143, 139, 0, 0, 0, 143, 140, 0, 0, 139, 0, 0, 0, 0, 139, 142, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 789.0, 1274.0, 571.0, 218.0, 1112.0, 162.0, 366.0, 205.0, 96.0, 122.0, 140.0, 972.0, 236.0, 130.0, 112.0, 93.0, 742.0, 230.0, 93.0, 143.0, 606.0, 136.0, 106.0, 124.0, 94.0, 512.0, 226.0, 286.0, 133.0, 93.0, 152.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.6258356E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}