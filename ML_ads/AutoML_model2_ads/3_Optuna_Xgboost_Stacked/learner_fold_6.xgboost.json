{"learner": {"attributes": {"best_iteration": "6", "best_score": "0.994359"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "57"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.004007997, -0.14579388, 0.43846166, -0.24485934, 0.060873713, 0.3247505, 0.081564985, -0.2897221, -0.12411162, 0.029866839, 0.00854725, 0.011506865, 0.4600291, -0.22870627, -0.35806665, 0.0006164005, -0.20257309, 0.057106577, -0.013386229, 0.030833384, 0.05977217, -0.29731107, -0.15906721, -0.027790962, -0.39408863, -0.0048077027, -0.035706915, 0.15978177, -0.008297738, -0.034967925, -0.023469692, -0.005470935, -0.02644792, -0.044921782, -0.033574354, 0.027976701, 0.003979647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, -1, 19, 21, 23, -1, 25, 27, -1, -1, -1, 29, 31, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [134.78477, 31.529373, 22.774857, 5.6391754, 6.209033, 11.573105, 0.0, 3.1651077, 2.8824968, 0.0, 2.828362, 0.0, 5.18005, 1.9158039, 1.0336914, 0.0, 4.200949, 4.3868594, 0.0, 0.0, 0.0, 0.6623535, 2.1891122, 0.0, 0.79447556, 0.0, 0.0, 2.5337777, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 21, 21, 22, 22, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, -1, 20, 22, 24, -1, 26, 28, -1, -1, -1, 30, 32, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.76225835, 0.47862867, 1.3316327, 0.4156466, 1.3419461, 0.8037069, 0.081564985, 1.0, 1.0, 0.029866839, 1.0, 0.011506865, 1.0008775, 0.27374282, 1.1975179, 0.0006164005, 0.50722474, 1.0, -0.013386229, 0.030833384, 0.05977217, 0.22502147, 0.31376776, -0.027790962, 0.24823487, -0.0048077027, -0.035706915, 0.6310756, -0.008297738, -0.034967925, -0.023469692, -0.005470935, -0.02644792, -0.044921782, -0.033574354, 0.027976701, 0.003979647], "split_indices": [139, 139, 139, 140, 138, 142, 0, 23, 59, 0, 0, 0, 139, 140, 138, 0, 140, 106, 0, 0, 0, 142, 139, 0, 139, 0, 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1540.0, 531.0, 1041.0, 499.0, 408.0, 123.0, 759.0, 282.0, 90.0, 409.0, 160.0, 248.0, 401.0, 358.0, 106.0, 176.0, 305.0, 104.0, 118.0, 130.0, 202.0, 199.0, 111.0, 247.0, 88.0, 88.0, 176.0, 129.0, 110.0, 92.0, 100.0, 99.0, 127.0, 120.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0038268624, -0.13274562, 0.39554042, -0.24482833, 0.0018507959, 0.0077248528, 0.504049, -0.18539472, -0.31816173, -0.11760188, 0.0835204, 0.39952278, 0.07758172, -0.23551406, -0.006123542, -0.3697546, -0.024265157, -0.0200568, 0.00021578965, 0.026228322, 0.023739312, 0.054300155, 0.026385238, -0.18212803, -0.03549719, -0.030247156, -0.04401526, 0.15737802, -0.018000493, -0.027108137, -0.00477096, 0.031035647, 0.00076543994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, -1, 23, -1, 25, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [109.883446, 22.975872, 18.339355, 3.6218834, 6.750911, 0.0, 11.24913, 2.8562565, 1.449234, 2.7920182, 4.3922057, 5.5672226, 0.0, 2.0853996, 0.0, 1.0467815, 0.0, 0.0, 0.0, 0.0, 8.386261, 0.0, 0.0, 2.7022743, 0.0, 0.0, 0.0, 4.2602334, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 20, 20, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, -1, 24, -1, 26, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.76225835, 0.4074681, 0.69538397, 1.0, 0.46780387, 0.0077248528, 1.3316327, 0.39660126, 0.28376868, 1.0, 0.50745165, 1.0, 0.07758172, 1.2743992, -0.006123542, 0.17648624, -0.024265157, -0.0200568, 0.00021578965, 0.026228322, 1.0, 0.054300155, 0.026385238, 0.2780017, -0.03549719, -0.030247156, -0.04401526, 0.6375918, -0.018000493, -0.027108137, -0.00477096, 0.031035647, 0.00076543994], "split_indices": [139, 140, 143, 23, 139, 0, 139, 142, 141, 93, 141, 108, 0, 138, 0, 141, 0, 0, 0, 0, 108, 0, 0, 140, 0, 0, 0, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1523.0, 531.0, 831.0, 692.0, 135.0, 396.0, 459.0, 372.0, 281.0, 411.0, 286.0, 110.0, 327.0, 132.0, 221.0, 151.0, 166.0, 115.0, 103.0, 308.0, 139.0, 147.0, 226.0, 101.0, 113.0, 108.0, 186.0, 122.0, 136.0, 90.0, 92.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0027082833, -0.1183053, 0.3534657, -0.19805317, 0.048382107, 0.19858319, 0.54772514, -0.039053746, -0.17943218, 0.023169294, 0.0074965935, 0.03499598, 0.006273234, 0.0778909, 0.030447949, -0.0004875918, -0.20273408, -0.014661834, 0.062977985, -0.2879926, -0.1417106, 0.01650383, -0.0036396503, -0.22084847, -0.036156967, -0.055589363, -0.19643645, -0.03214391, -0.011460667, -0.019796723, 0.009607399, -0.27643836, -0.005897859, -0.02004633, -0.03455066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, -1, 17, -1, -1, -1, -1, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [88.03387, 20.49775, 16.006485, 3.738369, 3.739884, 6.0871334, 13.271332, 0.0, 3.868185, 0.0, 3.488608, 0.0, 0.0, 0.0, 0.0, 0.0, 4.3651237, 0.0, 3.0426574, 1.7290897, 2.3046846, 0.0, 0.0, 1.9557066, 0.0, 4.102766, 3.2880697, 0.0, 0.0, 0.0, 0.0, 0.99176884, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 16, 16, 18, 18, 19, 19, 20, 20, 23, 23, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, -1, 18, -1, -1, -1, -1, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.76225835, 0.47862867, 0.97360194, 0.09822583, 1.3419461, 1.0, 1.1391696, -0.039053746, 1.1749831, 0.023169294, 0.5273946, 0.03499598, 0.006273234, 0.0778909, 0.030447949, -0.0004875918, 0.32774028, -0.014661834, 1.0, 1.0, 1.0, 0.01650383, -0.0036396503, 0.26206392, -0.036156967, 0.4374075, 0.43565965, -0.03214391, -0.011460667, -0.019796723, 0.009607399, 1.0, -0.005897859, -0.02004633, -0.03455066], "split_indices": [139, 139, 141, 140, 138, 124, 143, 0, 138, 0, 143, 0, 0, 0, 0, 0, 141, 0, 115, 23, 59, 0, 0, 143, 0, 140, 142, 0, 0, 0, 0, 108, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1542.0, 532.0, 1043.0, 499.0, 296.0, 236.0, 92.0, 951.0, 91.0, 408.0, 140.0, 156.0, 121.0, 115.0, 112.0, 839.0, 108.0, 300.0, 350.0, 489.0, 148.0, 152.0, 183.0, 167.0, 190.0, 299.0, 94.0, 89.0, 98.0, 92.0, 189.0, 110.0, 90.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010073982, -0.16757055, 0.20256978, -0.20185173, -0.032979615, 0.09135927, 0.45564243, -0.006239478, -0.23007737, -0.022025865, 0.00988093, 0.030286893, 0.056107655, 0.032336276, 0.062525906, -0.28512326, -0.16317791, 0.16424985, -0.07841068, -0.32716167, -0.021092126, -0.22383578, -0.0009637614, 0.0017545469, 0.23030159, -0.019198185, 0.009604396, -0.02961701, -0.036137633, -0.010698464, -0.0323739, 0.03428034, 0.012894854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, -1, 19, 21, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [69.85142, 5.2275887, 26.089798, 3.5544395, 5.676699, 4.8017, 6.349621, 0.0, 2.7655869, 0.0, 0.0, 0.0, 8.0300045, 0.0, 0.0, 1.2851639, 3.1572523, 2.9651632, 4.8740025, 0.27887535, 0.0, 2.8367348, 0.0, 0.0, 2.4059067, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, -1, 20, 22, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.52685916, 0.46354476, 1.0008775, 1.1840912, 1.0, 0.46811774, 1.3316327, -0.006239478, 0.32774028, -0.022025865, 0.00988093, 0.030286893, 1.0, 0.032336276, 0.062525906, 0.2845349, 1.0, 0.63661104, 1.0, 1.0, -0.021092126, 0.03846154, -0.0009637614, 0.0017545469, 1.4334517, -0.019198185, 0.009604396, -0.02961701, -0.036137633, -0.010698464, -0.0323739, 0.03428034, 0.012894854], "split_indices": [139, 141, 139, 138, 69, 141, 139, 0, 141, 0, 0, 0, 108, 0, 0, 139, 74, 141, 12, 80, 0, 1, 0, 0, 138, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1133.0, 927.0, 903.0, 230.0, 644.0, 283.0, 152.0, 751.0, 95.0, 135.0, 92.0, 552.0, 159.0, 124.0, 412.0, 339.0, 306.0, 246.0, 263.0, 149.0, 243.0, 96.0, 95.0, 211.0, 149.0, 97.0, 138.0, 125.0, 112.0, 131.0, 100.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0011034495, -0.11223961, 0.27639195, -0.18789329, -0.006250154, 0.06584001, 0.39616045, -0.28922608, -0.15859222, -0.08473699, 0.13127856, 0.020857967, -0.009789075, 0.021194993, 0.48683771, -0.024227131, -0.033716932, -0.19024913, -0.0062077283, -0.0059987595, -0.021436045, 0.030634586, -0.0037591625, 0.06847673, 0.025564274, -0.15361343, -0.2470718, -0.011230534, 0.008558842, -0.09067415, -0.026461542, -0.032609146, -0.016475964, -0.016867254, -0.00023902152], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [64.61942, 11.763126, 15.231358, 2.5416012, 6.595252, 5.118222, 6.4309235, 0.43222237, 2.028761, 3.9702587, 6.5631304, 0.0, 0.0, 0.0, 11.806156, 0.0, 0.0, 1.0408688, 0.0, 2.3561895, 0.0, 0.0, 0.0, 0.0, 0.0, 2.12386, 1.2748375, 0.0, 0.0, 1.3358846, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 17, 17, 19, 19, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.6985336, 0.40845418, 0.80976486, 1.0, 0.60352135, 1.0, 0.900655, 1.2298169, 1.0, 0.5338411, 0.64012194, 0.020857967, -0.009789075, 0.021194993, 1.1852549, -0.024227131, -0.033716932, 1.2631044, -0.0062077283, 1.0, -0.021436045, 0.030634586, -0.0037591625, 0.06847673, 0.025564274, 1.0, 0.36576095, -0.011230534, 0.008558842, 0.18450688, -0.026461542, -0.032609146, -0.016475964, -0.016867254, -0.00023902152], "split_indices": [140, 140, 139, 26, 139, 108, 142, 138, 71, 140, 141, 0, 0, 0, 143, 0, 0, 138, 0, 124, 0, 0, 0, 0, 0, 23, 139, 0, 0, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1467.0, 604.0, 856.0, 611.0, 219.0, 385.0, 192.0, 664.0, 389.0, 222.0, 117.0, 102.0, 127.0, 258.0, 97.0, 95.0, 500.0, 164.0, 242.0, 147.0, 109.0, 113.0, 139.0, 119.0, 304.0, 196.0, 112.0, 130.0, 194.0, 110.0, 100.0, 96.0, 103.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.007796713, -0.09868637, 0.25664073, -0.178616, -0.004777506, 0.0015312106, 0.33912012, -0.029708326, -0.16336614, -0.033259246, 0.014387715, 0.44455624, 0.018262377, -0.0034940422, -0.1828003, 0.0006079273, -0.021150755, 0.02255625, 0.07998126, -0.027822081, -0.16127138, -0.066110566, 0.016395323, -0.10159594, -0.20426123, 0.0063061477, -0.1782863, -0.0036726973, -0.01981622, -0.25129375, -0.011766861, -0.026757995, -0.008899264, -0.019100426, -0.030305171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, -1, 17, -1, -1, 19, 21, -1, -1, -1, -1, 23, 25, -1, 27, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [49.799747, 11.574407, 10.549461, 1.5049038, 3.0018654, 0.0, 6.517639, 0.0, 1.8419342, 3.591877, 0.0, 18.360546, 0.0, 0.0, 1.3168049, 5.4490767, 0.0, 0.0, 0.0, 0.0, 1.3417225, 5.1439395, 0.0, 1.3718491, 1.2380896, 0.0, 1.5149369, 0.0, 0.0, 0.6147299, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 11, 11, 14, 14, 15, 15, 20, 20, 21, 21, 23, 23, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, -1, 18, -1, -1, 20, 22, -1, -1, -1, -1, 24, 26, -1, 28, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.76225835, 0.4074681, 0.69538397, 0.09087678, 0.81507945, 0.0015312106, 1.1391696, -0.029708326, 1.1749831, 0.6940378, 0.014387715, 1.019717, 0.018262377, -0.0034940422, 0.18195362, 0.56020635, -0.021150755, 0.02255625, 0.07998126, -0.027822081, 1.0, 0.5042354, 0.016395323, 0.30816057, 0.3284141, 0.0063061477, 1.0, -0.0036726973, -0.01981622, 1.0, -0.011766861, -0.026757995, -0.008899264, -0.019100426, -0.030305171], "split_indices": [139, 140, 143, 143, 143, 0, 143, 0, 138, 143, 0, 141, 0, 0, 139, 143, 0, 0, 0, 0, 126, 140, 0, 140, 140, 0, 13, 0, 0, 115, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1542.0, 530.0, 833.0, 709.0, 135.0, 395.0, 95.0, 738.0, 595.0, 114.0, 236.0, 159.0, 97.0, 641.0, 500.0, 95.0, 146.0, 90.0, 118.0, 523.0, 355.0, 145.0, 219.0, 304.0, 165.0, 190.0, 131.0, 88.0, 197.0, 107.0, 95.0, 95.0, 91.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0039359056, -0.0881068, 0.22858773, -0.124186315, 0.07791422, 0.16117302, 0.049768478, -0.13783519, 0.0024464778, 0.025124222, -0.0014867217, 0.10093702, 0.039535582, -0.16150643, -0.027288038, 0.011720566, 0.023682961, -0.074944474, -0.19287701, 0.0070047043, -0.010979114, -0.008388276, 0.01500403, 0.0056646867, -0.016668776, -0.16753566, -0.032073554, -0.22331573, -0.12747072, -0.013867274, -0.035212025, -0.18897313, 0.0026953798, -0.010396414, -0.026232764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, 23, 25, -1, -1, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [42.61646, 8.757318, 10.866516, 2.4367352, 4.197304, 6.7568855, 0.0, 2.8784637, 0.0, 0.0, 0.0, 4.619188, 0.0, 2.4602432, 1.5579067, 3.04148, 0.0, 2.909502, 2.154667, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2403259, 0.0, 2.529356, 3.067687, 0.0, 0.0, 1.4404669, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 25, 25, 27, 27, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, 24, 26, -1, -1, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [0.6985336, 0.60352135, 1.3316327, 3.1538463, 0.58433825, 1.0, 0.049768478, 0.5286266, 0.0024464778, 0.025124222, -0.0014867217, 1.0, 0.039535582, 0.0, 0.60375583, 1.0008775, 0.023682961, 1.0, 1.1923077, 0.0070047043, -0.010979114, -0.008388276, 0.01500403, 0.0056646867, -0.016668776, 1.0, -0.032073554, 0.35121173, 0.37940434, -0.013867274, -0.035212025, 0.24138997, 0.0026953798, -0.010396414, -0.026232764], "split_indices": [140, 139, 139, 1, 141, 125, 0, 142, 0, 0, 0, 53, 0, 0, 142, 139, 0, 106, 1, 0, 0, 0, 0, 0, 0, 69, 0, 141, 141, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1462.0, 599.0, 1201.0, 261.0, 479.0, 120.0, 1100.0, 101.0, 91.0, 170.0, 381.0, 98.0, 906.0, 194.0, 230.0, 151.0, 241.0, 665.0, 89.0, 105.0, 136.0, 94.0, 99.0, 142.0, 555.0, 110.0, 232.0, 323.0, 140.0, 92.0, 231.0, 92.0, 107.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00088302325, -0.05535935, 0.2826229, -0.11421869, 0.038939316, 0.0058824173, 0.38005248, -0.027632674, -0.09964717, 0.0043925787, 0.026291082, 0.065150574, 0.0126696145, 0.005137509, -0.11994681, -0.03947288, 0.018451273, -0.053740874, -0.16940022, 0.004051795, -0.019994268, -0.018502733, 0.0072135353, -0.0911544, -0.22583535, -0.06696817, 0.0137144355, 0.015728496, -0.008056408, -0.025263045, 0.00512381, -0.17940277, -0.032747105, 0.0038986548, -0.020117752, -0.011009003, -0.025691375], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, -1, -1, 17, 19, -1, 21, 23, 25, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [31.90805, 9.618769, 7.260933, 2.520423, 5.153164, 0.0, 15.955662, 0.0, 3.001318, 4.5589085, 0.0, 0.0, 0.0, 0.0, 2.8255568, 3.2407606, 0.0, 2.9529185, 2.181409, 3.4500644, 0.0, 0.0, 3.3195744, 4.7595477, 1.3544102, 3.3843877, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0583811, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 22, 22, 23, 23, 24, 24, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, -1, -1, 18, 20, -1, 22, 24, 26, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [0.9525937, 0.49648586, 0.776592, 0.0927076, 1.0, 0.0058824173, 1.1852549, -0.027632674, 1.1749831, 1.0, 0.026291082, 0.065150574, 0.0126696145, 0.005137509, 1.0, 1.0, 0.018451273, 0.26801515, 0.115384616, 0.75101584, -0.019994268, -0.018502733, 1.2798964, 0.34300375, 0.39435303, 0.58252776, 0.0137144355, 0.015728496, -0.008056408, -0.025263045, 0.00512381, 1.0, -0.032747105, 0.0038986548, -0.020117752, -0.011009003, -0.025691375], "split_indices": [140, 139, 143, 140, 23, 0, 143, 0, 138, 64, 0, 0, 0, 0, 97, 61, 0, 139, 1, 139, 0, 0, 138, 141, 140, 140, 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1733.0, 333.0, 1067.0, 666.0, 101.0, 232.0, 88.0, 979.0, 577.0, 89.0, 112.0, 120.0, 116.0, 863.0, 464.0, 113.0, 369.0, 494.0, 365.0, 99.0, 117.0, 252.0, 207.0, 287.0, 238.0, 127.0, 93.0, 159.0, 97.0, 110.0, 197.0, 90.0, 133.0, 105.0, 104.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [9.646149e-05, -0.047069725, 0.29709345, -0.12621409, 0.028940206, 0.017740043, 0.045504894, -0.20011675, -0.10495889, -0.04991395, 0.08331733, -0.023672225, -0.015560775, -0.0009867874, -0.1233744, 0.009259373, -0.14993082, 0.14854784, -0.0072169215, -0.0010929314, -0.14538996, -0.02531881, -0.006560401, 0.07643542, 0.032717492, -0.10957988, -0.021457857, 0.020854054, -0.007454183, -0.16412176, 0.0027694015, -0.023333058, -0.0064822114], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, -1, -1, 25, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [28.926966, 10.72008, 5.350445, 1.3713217, 3.8976672, 0.0, 0.0, 0.31770706, 1.1872802, 5.287927, 5.4566474, 0.0, 0.0, 0.0, 1.406106, 0.0, 1.8982034, 4.8819866, 0.0, 0.0, 1.1768818, 0.0, 0.0, 5.3851147, 0.0, 2.3434865, 0.0, 0.0, 0.0, 1.5394192, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 16, 16, 17, 17, 20, 20, 23, 23, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, -1, -1, 26, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0008775, 0.4156466, 1.3316327, 1.0, 0.56020635, 0.017740043, 0.045504894, 0.7307692, 1.0, 0.5042354, 0.83346164, -0.023672225, -0.015560775, -0.0009867874, 1.1861864, 0.009259373, -0.115384616, 0.71048903, -0.0072169215, -0.0010929314, 0.5769231, -0.02531881, -0.006560401, 1.0, 0.032717492, 1.0, -0.021457857, 0.020854054, -0.007454183, 0.29588273, 0.0027694015, -0.023333058, -0.0064822114], "split_indices": [139, 140, 139, 26, 143, 0, 0, 1, 89, 140, 142, 0, 0, 0, 138, 0, 1, 142, 0, 0, 1, 0, 0, 115, 0, 71, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1782.0, 283.0, 873.0, 909.0, 161.0, 122.0, 195.0, 678.0, 371.0, 538.0, 107.0, 88.0, 110.0, 568.0, 153.0, 218.0, 379.0, 159.0, 93.0, 475.0, 98.0, 120.0, 270.0, 109.0, 313.0, 162.0, 144.0, 126.0, 224.0, 89.0, 132.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004469353, -0.062475514, 0.1705918, -0.108417, -6.86009e-05, 0.11869497, 0.039149776, -0.1868111, -0.085507296, 0.075608455, -0.080491096, -0.006986361, 0.20587534, -0.014533686, -0.02282853, -0.14092962, -0.057605892, -0.037808783, 0.032377888, -0.023502523, -0.017239898, 0.018081138, -0.016820898, 0.0014841039, 0.2944995, -0.004055219, -0.0234263, 0.008748647, -0.09683145, 0.01300324, -0.017652055, -0.013890087, 0.015173363, 0.04158802, 0.017311877, -0.052295804, -0.018787332, -0.011505584, 0.0039173565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, 23, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, 37, -1, -1, -1], "loss_changes": [22.998322, 4.2260547, 6.809805, 1.5247908, 3.8038363, 5.2702913, 0.0, 0.3302598, 1.0159583, 9.063272, 2.9616637, 5.964617, 4.8081913, 0.0, 0.0, 2.0610838, 2.4871106, 5.145222, 0.0, 0.0, 4.419859, 0.0, 0.0, 0.0, 2.8582535, 0.0, 0.0, 0.0, 1.3947847, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3260837, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 20, 20, 24, 24, 28, 28, 35, 35], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, 24, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, 38, -1, -1, -1], "split_conditions": [0.6985336, 0.4074681, 1.3441783, 1.0, 0.5275373, 0.79231423, 0.039149776, 1.2298169, 0.21497679, 0.49153346, 0.53051406, 1.0, 1.0, -0.014533686, -0.02282853, 0.16683866, 1.2274326, 1.0, 0.032377888, -0.023502523, 1.0, 0.018081138, -0.016820898, 0.0014841039, 1.0, -0.004055219, -0.0234263, 0.008748647, 0.5769231, 0.01300324, -0.017652055, -0.013890087, 0.015173363, 0.04158802, 0.017311877, 1.0, -0.018787332, -0.011505584, 0.0039173565], "split_indices": [140, 140, 140, 26, 141, 139, 0, 138, 142, 139, 140, 93, 93, 0, 0, 142, 138, 97, 0, 0, 105, 0, 0, 0, 15, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 71, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1474.0, 594.0, 849.0, 625.0, 481.0, 113.0, 192.0, 657.0, 322.0, 303.0, 197.0, 284.0, 96.0, 96.0, 220.0, 437.0, 221.0, 101.0, 88.0, 215.0, 91.0, 106.0, 90.0, 194.0, 106.0, 114.0, 93.0, 344.0, 100.0, 121.0, 125.0, 90.0, 97.0, 97.0, 231.0, 113.0, 137.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.0025234348, -0.086375445, 0.11707571, -0.103498936, 0.00788898, 0.05518714, 0.26720843, -0.12337541, 0.0048018442, 0.09480203, -0.005745277, 0.008203791, 0.036517654, -0.109447315, -0.02389786, 0.037545186, 0.028322905, -0.12886387, 0.0035582432, 0.15226778, -0.008504408, -0.14945242, -0.002888586, 0.026308326, 0.0037838717, -0.0025367704, -0.17845595, -0.15601695, -0.024363589, -0.021498213, -0.12314698, -0.0068628527, -0.019181924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, -1, 25, -1, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1], "loss_changes": [20.998516, 3.2855358, 8.37164, 3.1682367, 0.0, 2.8468952, 4.7710304, 1.4974222, 0.0, 5.0922832, 0.0, 0.0, 0.0, 2.3372622, 0.0, 5.091081, 0.0, 1.5067492, 0.0, 2.3712544, 0.0, 2.1845293, 0.0, 0.0, 0.0, 0.0, 0.71958447, 0.7093735, 0.0, 0.0, 0.87981725, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 26, 26, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, -1, 26, -1, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1], "split_conditions": [0.56020635, 3.1538463, 1.0008775, 1.0, 0.00788898, 0.83346164, 0.97360194, 0.5974733, 0.0048018442, 0.71048903, -0.005745277, 0.008203791, 0.036517654, 0.48165447, -0.02389786, 1.0, 0.028322905, 1.0, 0.0035582432, 0.69192046, -0.008504408, -0.30769232, -0.002888586, 0.026308326, 0.0037838717, -0.0025367704, 0.3558193, 0.15599424, -0.024363589, -0.021498213, 0.30318457, -0.0068628527, -0.019181924], "split_indices": [143, 1, 139, 73, 0, 142, 141, 141, 0, 142, 0, 0, 0, 141, 0, 115, 0, 62, 0, 143, 0, 1, 0, 0, 0, 0, 140, 139, 0, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1161.0, 901.0, 1052.0, 109.0, 638.0, 263.0, 930.0, 122.0, 472.0, 166.0, 91.0, 172.0, 830.0, 100.0, 362.0, 110.0, 732.0, 98.0, 187.0, 175.0, 607.0, 125.0, 95.0, 92.0, 115.0, 492.0, 366.0, 126.0, 131.0, 235.0, 131.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0047203964, -0.055456936, 0.15495905, -0.08400068, 0.028337684, -0.0003584796, 0.19776247, -0.11210601, -0.024676053, 0.026413206, -0.06404443, 0.25376564, -0.002207974, -0.019890666, -0.14401385, -0.090474725, 0.005075167, -0.027358878, 0.04190494, 0.15392773, 0.05260508, -0.0131127415, 0.008133476, -0.11937278, -0.024531601, -0.017799659, -0.0011793411, -0.0026359016, 0.011016893, 0.026778976, -0.005244718, -0.15712677, -0.005301016, -0.009541894, -0.020190256], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, 17, 19, -1, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [18.588217, 3.51118, 3.9902964, 1.8257356, 8.125129, 0.0, 5.7003937, 2.1861973, 1.7469915, 0.0, 5.949894, 10.031034, 0.0, 2.150658, 1.3778973, 1.2946306, 0.0, 0.0, 0.8294749, 6.344533, 0.0, 0.0, 0.0, 1.1124206, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7819333, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 23, 23, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, 18, 20, -1, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.6985336, 0.52685916, 1.4128941, 1.0, 0.50745165, -0.0003584796, 1.0, 1.2044352, 1.0, 0.026413206, 0.52471125, 1.3316327, -0.002207974, 1.0, 0.4726218, 0.15384616, 0.005075167, -0.027358878, 0.68388295, 1.0083756, 0.05260508, -0.0131127415, 0.008133476, 0.34300375, -0.024531601, -0.017799659, -0.0011793411, -0.0026359016, 0.011016893, 0.026778976, -0.005244718, 0.2111425, -0.005301016, -0.009541894, -0.020190256], "split_indices": [140, 139, 138, 93, 141, 0, 119, 138, 17, 0, 140, 139, 0, 111, 140, 1, 0, 0, 142, 143, 0, 0, 0, 141, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1468.0, 588.0, 1095.0, 373.0, 125.0, 463.0, 743.0, 352.0, 105.0, 268.0, 369.0, 94.0, 191.0, 552.0, 188.0, 164.0, 90.0, 178.0, 270.0, 99.0, 91.0, 100.0, 444.0, 108.0, 89.0, 99.0, 89.0, 89.0, 174.0, 96.0, 283.0, 161.0, 119.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018043402, -0.051364508, 0.12783462, -0.09369412, 0.014934891, 0.07065449, 0.034226008, -0.07908156, -0.01889777, -0.030535476, 0.023364738, -0.009235384, 0.15714872, -0.020115525, -0.063560754, 0.050958455, -0.017303346, -0.008654233, 0.26124975, -0.093438946, -0.0028989683, 0.02011326, -0.008607543, 0.00811484, 0.044488247, -0.12642361, -0.00055656926, -0.008073202, 0.011053853, -0.0017119227, -0.16802122, -0.0048877574, -0.023997158], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, -1, 17, -1, 19, 21, -1, -1, 23, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [13.2417755, 4.1843853, 6.988697, 1.2670269, 5.7780094, 6.344676, 0.0, 1.4949031, 0.0, 5.585719, 0.0, 0.0, 7.4583373, 0.0, 1.2687247, 6.2971573, 0.0, 0.0, 6.812934, 1.3593817, 2.0395417, 0.0, 0.0, 0.0, 0.0, 1.5504589, 0.0, 0.0, 0.0, 0.0, 2.1173892, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 12, 12, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, -1, 18, -1, 20, 22, -1, -1, 24, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [0.71048903, 0.4294398, 1.0, 0.3787072, 1.2692307, 0.900655, 0.034226008, 0.09822583, -0.01889777, 0.6375918, 0.023364738, -0.009235384, 1.0, -0.020115525, 1.0, 1.0, -0.017303346, -0.008654233, 1.1633925, 1.0, 0.3284141, 0.02011326, -0.008607543, 0.00811484, 0.044488247, 0.1755746, -0.00055656926, -0.008073202, 0.011053853, -0.0017119227, 1.0, -0.0048877574, -0.023997158], "split_indices": [142, 139, 115, 139, 1, 142, 0, 140, 0, 139, 0, 0, 93, 0, 83, 106, 0, 0, 140, 71, 140, 0, 0, 0, 0, 142, 0, 0, 0, 0, 69, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1491.0, 570.0, 910.0, 581.0, 450.0, 120.0, 789.0, 121.0, 481.0, 100.0, 156.0, 294.0, 89.0, 700.0, 306.0, 175.0, 88.0, 206.0, 469.0, 231.0, 146.0, 160.0, 104.0, 102.0, 341.0, 128.0, 137.0, 94.0, 94.0, 247.0, 93.0, 154.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002760561, -0.047281224, 0.14860968, -0.084351, -0.0037557532, 0.038639434, 0.09383206, -0.063877515, -0.02624013, -0.061293583, 0.11943421, 0.18688403, 0.009154759, -0.09364445, 0.013907882, -0.010699573, -0.025920544, -0.0026759536, 0.022664297, 0.0059453147, 0.031714667, 0.023407506, -0.026025523, -0.06049778, -0.022501247, -0.09244799, 0.011530752, 0.008328243, -0.09528332, 0.0029242896, -0.01811085, -0.13141836, 0.00026210025, -0.0074390545, -0.019411119], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, -1, -1, 27, -1, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [13.936466, 2.5783403, 6.1218805, 3.1459022, 5.2097406, 0.0, 3.0099263, 4.676034, 0.0, 5.016589, 3.6675413, 3.0211053, 12.119155, 2.939227, 0.0, 4.110051, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.695794, 0.0, 2.6109815, 0.0, 0.0, 1.5353959, 0.0, 0.0, 1.1333485, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 23, 23, 25, 25, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, -1, -1, 28, -1, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [0.7923925, 1.0, 0.8752063, 0.6672731, 0.65384614, 0.038639434, 1.0, 0.5273946, -0.02624013, 2.0, 0.40845418, 1.0008775, 1.0, 1.3436549, 0.013907882, 0.5653381, -0.025920544, -0.0026759536, 0.022664297, 0.0059453147, 0.031714667, 0.023407506, -0.026025523, -0.26923078, -0.022501247, 0.29230356, 0.011530752, 0.008328243, 1.0, 0.0029242896, -0.01811085, 0.25546485, 0.00026210025, -0.0074390545, -0.019411119], "split_indices": [143, 39, 143, 143, 1, 0, 69, 143, 0, 0, 140, 139, 105, 138, 0, 142, 0, 0, 0, 0, 0, 0, 0, 1, 0, 142, 0, 0, 83, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1598.0, 470.0, 863.0, 735.0, 88.0, 382.0, 774.0, 89.0, 501.0, 234.0, 182.0, 200.0, 675.0, 99.0, 399.0, 102.0, 99.0, 135.0, 92.0, 90.0, 109.0, 91.0, 539.0, 136.0, 242.0, 157.0, 105.0, 434.0, 102.0, 140.0, 317.0, 117.0, 166.0, 151.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025354195, -0.038094003, 0.1277787, -0.07925203, 0.009093893, 0.035539333, 0.0748865, -0.057782143, -0.1606687, 0.13332973, -0.029976737, 0.14726563, -0.0011141234, -0.0069236583, -0.110015176, -0.011702981, -0.02033378, 0.0002664231, 0.026696486, -0.06763097, 0.016153345, 0.0038189262, 0.026061947, -0.009855452, 0.06638101, -0.061831463, -0.018431371, -0.119273074, 0.010482522, 0.017846538, -0.0045703333, -0.00062172284, -0.0123238815, -0.19087134, -0.042793088, -0.010587088, -0.028147621, -0.003423338, -0.0051352754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, -1, 23, 25, -1, -1, -1, -1, 27, -1, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, 35, 37, -1, -1, -1, -1], "loss_changes": [10.023889, 3.101631, 5.6824236, 1.4910493, 3.6113555, 0.0, 2.3847928, 1.7931328, 0.33144093, 3.1081471, 4.0815215, 2.5717578, 0.0, 2.2972038, 1.1921315, 0.0, 0.0, 0.0, 0.0, 4.2125387, 0.0, 0.0, 0.0, 0.0, 2.3869522, 0.6898544, 0.0, 1.9932027, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4478726, 0.012894958, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 19, 19, 24, 24, 25, 25, 27, 27, 33, 33, 34, 34], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, -1, 24, 26, -1, -1, -1, -1, 28, -1, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, 36, 38, -1, -1, -1, -1], "split_conditions": [0.7923925, 0.4241147, 0.8752063, 1.3172762, 0.0, 0.035539333, 1.1391696, 1.0, 0.41527092, 1.0, 1.2692307, 0.8572975, -0.0011141234, 0.25788847, 0.25546485, -0.011702981, -0.02033378, 0.0002664231, 0.026696486, 1.0, 0.016153345, 0.0038189262, 0.026061947, -0.009855452, 1.0, 0.14799438, -0.018431371, 1.0, 0.010482522, 0.017846538, -0.0045703333, -0.00062172284, -0.0123238815, 0.58332, 1.0, -0.010587088, -0.028147621, -0.003423338, -0.0051352754], "split_indices": [143, 140, 143, 138, 0, 0, 143, 23, 141, 13, 1, 141, 0, 141, 140, 0, 0, 0, 0, 42, 0, 0, 0, 0, 69, 139, 0, 124, 0, 0, 0, 0, 0, 143, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1597.0, 472.0, 853.0, 744.0, 89.0, 383.0, 675.0, 178.0, 178.0, 566.0, 208.0, 175.0, 342.0, 333.0, 88.0, 90.0, 90.0, 88.0, 473.0, 93.0, 106.0, 102.0, 152.0, 190.0, 202.0, 131.0, 364.0, 109.0, 95.0, 95.0, 106.0, 96.0, 188.0, 176.0, 97.0, 91.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.0012885409, -0.035519287, 0.12849323, -0.06963926, 0.0038604601, 0.20056619, 0.0006844055, -0.0055401376, -0.11460998, 0.11641443, -0.03089304, 0.09386626, 0.0447916, 0.008910721, -0.08866957, -0.05703994, -0.19844982, 0.005994161, 0.01728872, -0.08193414, 0.009765489, 0.020001225, -0.00405853, -0.020167122, 0.0016260494, -0.014225043, 0.0027041917, -0.012299026, -0.029013721, -0.16409972, -0.00817618, -0.0103315255, -0.021504289, -0.010635485, 0.0109037105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [9.70137, 2.159223, 4.0769415, 2.481904, 2.9180872, 7.706524, 0.0, 2.7931333, 2.4422913, 0.56129456, 3.7398999, 2.911385, 0.0, 0.0, 2.2410245, 2.1493971, 1.4252491, 0.0, 0.0, 2.4726286, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.59763384, 2.4741864, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 16, 16, 19, 19, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.7923925, 1.0, 1.1391696, -0.03846154, 0.0, 0.9849453, 0.0006844055, 1.3216816, 0.33421066, 0.45561248, 0.84615386, 0.95005274, 0.0447916, 0.008910721, 1.4061775, 0.20086846, 1.3322752, 0.005994161, 0.01728872, 1.0, 0.009765489, 0.020001225, -0.00405853, -0.020167122, 0.0016260494, -0.014225043, 0.0027041917, -0.012299026, -0.029013721, 0.44334212, 1.0, -0.0103315255, -0.021504289, -0.010635485, 0.0109037105], "split_indices": [143, 39, 143, 1, 0, 139, 0, 138, 141, 143, 1, 143, 0, 0, 138, 141, 138, 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1607.0, 465.0, 861.0, 746.0, 292.0, 173.0, 355.0, 506.0, 176.0, 570.0, 204.0, 88.0, 166.0, 189.0, 300.0, 206.0, 88.0, 88.0, 408.0, 162.0, 114.0, 90.0, 91.0, 98.0, 149.0, 151.0, 113.0, 93.0, 193.0, 215.0, 88.0, 105.0, 117.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.006984265, -0.020559238, 0.021981139, -0.064629234, 0.0183714, 0.003956194, -0.082173, 0.019695196, -0.011156073, -0.11232434, -0.01292711, -0.13014749, 0.025510905, -0.09115171, -0.021865798, -0.013429201, 0.0071766353, -0.0045570256, -0.021636695, 0.12215135, -0.022167893, -0.02621792, -0.13412258, 0.0014306298, 0.0203672, -0.017519549, 0.050108973, -0.007873843, 0.0028689853, -0.008509081, -0.020006184, 0.019939087, -0.048336376, 0.0020876124, -0.011680469], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, 23, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [6.323741, 3.3249748, 0.0, 1.6615663, 5.4259505, 0.0, 1.6243515, 0.0, 3.8525784, 1.2202377, 2.4258, 1.5167775, 3.110198, 1.2611978, 0.0, 0.0, 0.0, 0.0, 0.0, 1.960526, 4.999281, 0.51908135, 0.8794069, 0.0, 0.0, 0.0, 4.5117064, 0.0, 0.0, 0.0, 0.0, 0.0, 0.87669003, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 19, 19, 20, 20, 21, 21, 22, 22, 26, 26, 32, 32], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, 24, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.3441783, 0.4294398, 0.021981139, 1.1749831, 1.3350589, 0.003956194, 1.0, 0.019695196, 1.3907099, 0.4827842, 0.2845349, 0.5338411, 0.6571012, 1.0, -0.021865798, -0.013429201, 0.0071766353, -0.0045570256, -0.021636695, 1.0, 0.77644604, 0.30254164, 0.29602364, 0.0014306298, 0.0203672, -0.017519549, 0.9037621, -0.007873843, 0.0028689853, -0.008509081, -0.020006184, 0.019939087, 1.0947773, 0.0020876124, -0.011680469], "split_indices": [140, 139, 0, 138, 138, 0, 93, 0, 138, 143, 139, 140, 141, 122, 0, 0, 0, 0, 0, 53, 142, 140, 140, 0, 0, 0, 141, 0, 0, 0, 0, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1938.0, 116.0, 909.0, 1029.0, 131.0, 778.0, 146.0, 883.0, 542.0, 236.0, 208.0, 675.0, 452.0, 90.0, 97.0, 139.0, 105.0, 103.0, 223.0, 452.0, 180.0, 272.0, 96.0, 127.0, 145.0, 307.0, 92.0, 88.0, 156.0, 116.0, 122.0, 185.0, 92.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002785807, -0.04731048, 0.06613161, -0.061635654, 0.0083752265, 0.112091504, -0.047201317, -0.023645777, -0.10723953, 0.062627845, 0.028318156, 0.0057720947, -0.015535198, -0.07877237, 0.072759174, -0.00021863605, -0.14365, 0.028218305, 0.0110940905, -0.11725087, 0.0039257123, 0.021799559, -0.006297575, -0.081428714, -0.02324762, -0.017821996, 0.10644712, -0.005224842, -0.02052283, -0.0022664296, -0.013851412, 0.022562398, -0.0023730653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, -1, 25, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [6.5752625, 2.172263, 4.766024, 1.8069823, 0.0, 5.509244, 2.995717, 3.0239377, 1.8130665, 5.7138233, 0.0, 0.0, 0.0, 1.6440585, 4.0807266, 0.0, 1.9454608, 0.0, 7.383132, 1.5612171, 0.0, 0.0, 0.0, 0.6943997, 0.0, 0.0, 4.2198577, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 18, 18, 19, 19, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, -1, 26, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [0.56020635, 3.1538463, 0.15384616, 1.2807903, 0.0083752265, -0.115384616, 1.0, 0.30254164, -0.34615386, 0.58433825, 0.028318156, 0.0057720947, -0.015535198, 0.24110529, 0.2993035, -0.00021863605, 0.4605518, 0.028218305, 0.76225835, 0.16683866, 0.0039257123, 0.021799559, -0.006297575, 1.0, -0.02324762, -0.017821996, 1.6265124, -0.005224842, -0.02052283, -0.0022664296, -0.013851412, 0.022562398, -0.0023730653], "split_indices": [143, 1, 1, 138, 0, 1, 59, 140, 1, 141, 0, 0, 0, 142, 143, 0, 140, 0, 139, 142, 0, 0, 0, 137, 0, 0, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1157.0, 915.0, 1043.0, 114.0, 651.0, 264.0, 569.0, 474.0, 505.0, 146.0, 134.0, 130.0, 362.0, 207.0, 122.0, 352.0, 96.0, 409.0, 273.0, 89.0, 100.0, 107.0, 207.0, 145.0, 137.0, 272.0, 157.0, 116.0, 102.0, 105.0, 142.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032038908, -0.04910153, 0.055124357, 0.0046077324, -0.061439537, 0.09686363, -0.04732656, -0.026519423, -0.09274499, 0.050389968, 0.026098385, 0.0046492447, -0.013973477, -0.054625344, 0.00919485, -0.06483895, -0.023446998, 0.026247649, 0.0006092643, 0.002130609, -0.104832545, -0.104855895, 0.0062622363, 0.06287651, -0.012034147, 0.013080186, -0.012377348, -0.017994491, -0.0037914224, -0.045541245, -0.016995732, -0.010309189, 0.15151873, -0.0123985885, 0.003466616, 0.0065600364, 0.023743706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, -1, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, 21, -1, -1, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [5.544334, 1.3610337, 3.8999186, 0.0, 1.1216128, 4.94247, 2.2887874, 1.6148801, 2.1396441, 5.3316956, 0.0, 0.0, 0.0, 1.1170269, 0.0, 2.3054762, 0.0, 0.0, 3.0802886, 2.9808435, 1.0454888, 1.3283446, 0.0, 3.972189, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1325315, 0.0, 0.0, 1.2992244, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23, 29, 29, 32, 32], "right_children": [2, 4, 6, -1, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, 22, -1, -1, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [0.56020635, 1.1749831, 0.15384616, 0.0046077324, 1.0, -0.115384616, 1.0, 0.5286266, 0.50989574, 0.58433825, 0.026098385, 0.0046492447, -0.013973477, 1.0, 0.00919485, 1.3269972, -0.023446998, 0.026247649, -0.34615386, 1.0, 1.0, 0.84615386, 0.0062622363, 1.0, -0.012034147, 0.013080186, -0.012377348, -0.017994491, -0.0037914224, 0.31486017, -0.016995732, -0.010309189, 1.0, -0.0123985885, 0.003466616, 0.0065600364, 0.023743706], "split_indices": [143, 138, 1, 0, 108, 1, 59, 142, 141, 141, 0, 0, 0, 97, 0, 138, 0, 0, 1, 69, 13, 1, 0, 89, 0, 0, 0, 0, 0, 141, 0, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1159.0, 912.0, 133.0, 1026.0, 648.0, 264.0, 485.0, 541.0, 505.0, 143.0, 131.0, 133.0, 392.0, 93.0, 452.0, 89.0, 96.0, 409.0, 184.0, 208.0, 344.0, 108.0, 270.0, 139.0, 91.0, 93.0, 98.0, 110.0, 180.0, 164.0, 94.0, 176.0, 91.0, 89.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00027143574, -0.027664777, 0.095397525, -0.015516015, -0.1251294, 0.032295328, 0.0428387, -0.047118098, 0.02149804, -0.008066672, -0.017009731, -0.0075867022, 0.09352206, -0.0193677, -0.025112249, 0.0707872, -0.073119536, -0.0072920336, 0.018102322, 0.0107876, -0.046696734, 0.023017002, 0.016666546, 0.0015664503, -0.019149829, 0.022314345, -0.11498895, -0.060856607, 0.013822287, 0.011199001, -0.005953725, -0.002408118, -0.16793522, -0.025089933, 0.009721628, -0.011914941, -0.021360703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [5.490318, 1.8909665, 5.6092687, 1.6610041, 0.3538921, 0.0, 2.2922502, 2.4704669, 3.050008, 0.0, 0.0, 0.0, 3.8885624, 0.0, 1.9117413, 3.7091374, 2.3542724, 0.0, 0.0, 0.0, 2.7005033, 0.0, 3.024921, 0.0, 0.0, 2.0919275, 1.3862088, 5.8879585, 0.0, 0.0, 0.0, 0.0, 0.40551996, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 12, 12, 14, 14, 15, 15, 16, 16, 20, 20, 22, 22, 25, 25, 26, 26, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [0.7923925, 1.0, 0.8752063, 0.44334874, 0.20889734, 0.032295328, 1.0, -0.3846154, 0.68592155, -0.008066672, -0.017009731, -0.0075867022, 1.0570605, -0.0193677, 1.0, 0.0, 1.0, -0.0072920336, 0.018102322, 0.0107876, 0.25873837, 0.023017002, 1.0, 0.0015664503, -0.019149829, 1.0, 0.115384616, 1.0, 0.013822287, 0.011199001, -0.005953725, -0.002408118, 0.36584678, -0.025089933, 0.009721628, -0.011914941, -0.021360703], "split_indices": [143, 40, 143, 140, 143, 0, 89, 1, 141, 0, 0, 0, 143, 0, 89, 0, 121, 0, 0, 0, 142, 0, 121, 0, 0, 23, 1, 13, 0, 0, 0, 0, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1597.0, 469.0, 1420.0, 177.0, 88.0, 381.0, 766.0, 654.0, 89.0, 88.0, 114.0, 267.0, 100.0, 666.0, 430.0, 224.0, 92.0, 175.0, 93.0, 573.0, 109.0, 321.0, 128.0, 96.0, 285.0, 288.0, 196.0, 125.0, 136.0, 149.0, 106.0, 182.0, 89.0, 107.0, 88.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0006027056, -0.012106396, 0.019984864, -0.021074029, 0.016243203, -0.009220345, -0.12047648, -0.041269235, 0.036135387, -0.0044257618, -0.021488389, -0.016464643, -0.020116342, 0.0681624, -0.010165522, -0.035593156, 0.011421702, 0.022390848, 0.031106366, -0.05911942, 0.013630894, 0.019351348, -0.020733884, 0.019102495, -0.11466531, -0.015876444, 0.06804346, -0.05289898, 0.016556008, -0.17652564, -0.004285043, 0.018615542, -0.0020086218, -0.0132746445, 0.0026061253, -0.0119614275, -0.02419131], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, 25, 27, 29, -1, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.2366667, 3.0427413, 0.0, 2.178649, 0.0, 2.4013488, 1.4175372, 3.8391936, 3.0185065, 0.0, 0.0, 2.0947864, 0.0, 6.170472, 0.0, 2.9563203, 0.0, 3.4462824, 0.0, 2.7937746, 0.0, 0.0, 4.570737, 2.8155584, 1.6703763, 0.0, 2.3628821, 1.1285549, 0.0, 0.7516999, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 22, 22, 23, 23, 24, 24, 26, 26, 27, 27, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, 26, 28, 30, -1, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3316327, 1.0, 0.019984864, 1.0, 0.016243203, 0.56020635, 0.3567492, 0.4827842, 1.5461816, -0.0044257618, -0.021488389, 1.0, -0.020116342, 0.9525937, -0.010165522, 1.5384616, 0.011421702, 1.0, 0.031106366, 1.2509577, 0.013630894, 0.019351348, 1.0, 0.3129986, 1.0, -0.015876444, 1.0, 0.46153846, 0.016556008, 0.34183747, -0.004285043, 0.018615542, -0.0020086218, -0.0132746445, 0.0026061253, -0.0119614275, -0.02419131], "split_indices": [139, 102, 0, 40, 0, 143, 142, 143, 138, 0, 0, 73, 0, 140, 0, 1, 0, 17, 0, 138, 0, 0, 59, 141, 93, 0, 106, 1, 0, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1944.0, 124.0, 1849.0, 95.0, 1652.0, 197.0, 968.0, 684.0, 109.0, 88.0, 838.0, 130.0, 555.0, 129.0, 731.0, 107.0, 467.0, 88.0, 643.0, 88.0, 94.0, 373.0, 267.0, 376.0, 146.0, 227.0, 179.0, 88.0, 202.0, 174.0, 97.0, 130.0, 89.0, 90.0, 108.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00039433804, -0.012269574, 0.018625753, -0.021710988, 0.016605752, -0.08161686, -0.005143913, -0.12312591, -0.041930147, 0.004617066, -0.014595126, -0.0042712833, -0.019573187, 0.0022628757, -0.00976856, -0.01301888, 0.020242568, 0.05500458, -0.048274945, -0.019797629, 0.017404957, -0.027317425, -0.020267624, 0.017618146, -0.016711079, -0.0664996, 0.08893736, -0.04089516, -0.019801326, 0.025565926, -0.0070613674, -0.008363463, 0.0035613754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [4.594877, 3.281453, 0.0, 1.8370527, 0.0, 0.66058993, 1.9929059, 1.1443388, 0.73789984, 4.7304635, 0.0, 0.0, 0.0, 0.0, 0.0, 2.9858081, 0.0, 3.784553, 2.6534111, 7.5351477, 0.0, 3.288793, 0.0, 0.0, 0.0, 1.8183594, 4.841319, 1.4780178, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.3316327, 1.0, 0.018625753, 1.0, 0.016605752, 1.0, 1.055643, 0.33421066, 0.54792327, 0.8462448, -0.014595126, -0.0042712833, -0.019573187, 0.0022628757, -0.00976856, -0.03846154, 0.020242568, 1.0, 1.0, 1.3269972, 0.017404957, 0.49938026, -0.020267624, 0.017618146, -0.016711079, 0.42129, 0.6000778, 0.2845349, -0.019801326, 0.025565926, -0.0070613674, -0.008363463, 0.0035613754], "split_indices": [139, 102, 0, 5, 0, 71, 141, 141, 141, 141, 0, 0, 0, 0, 0, 1, 0, 111, 15, 138, 0, 139, 0, 0, 0, 142, 142, 139, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1949.0, 124.0, 1851.0, 98.0, 401.0, 1450.0, 196.0, 205.0, 1356.0, 94.0, 93.0, 103.0, 95.0, 110.0, 1245.0, 111.0, 425.0, 820.0, 261.0, 164.0, 722.0, 98.0, 112.0, 149.0, 540.0, 182.0, 452.0, 88.0, 89.0, 93.0, 290.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019554633, -0.011481889, 0.019303853, -0.022959137, 0.01254145, -0.032053504, 0.012959433, -0.012209849, -0.18112768, -0.0584254, 0.043302704, -0.028761342, -0.009478787, 0.005674345, -0.07616851, 0.017303286, 0.016351715, -0.09828007, -0.016900372, -0.07007263, 0.07734188, -0.05818909, -0.17273474, -0.014451968, 0.01275478, 0.003094315, -0.017023237, -0.0035364411, 0.16750692, -0.09223894, 0.001842304, -0.012426081, -0.022120861, 0.008980468, 0.024605373, -0.014417096, -0.0055469535], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.8285048, 3.0873957, 0.0, 2.5153139, 0.0, 5.0614395, 0.0, 3.87397, 1.8479843, 1.683805, 2.3984993, 0.0, 0.0, 0.0, 0.9357047, 0.0, 2.993948, 1.5521789, 3.576269, 2.3776631, 3.3840022, 0.8817178, 0.4276476, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1291027, 0.44682455, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.253856, 1.0, 0.019303853, 1.1466392, 0.01254145, 0.7650251, 0.012959433, 0.4074681, 0.76225835, 0.09383088, 0.45524624, -0.028761342, -0.009478787, 0.005674345, 1.0, 0.017303286, 1.0, 0.30816057, 0.31846502, 1.3447689, 1.0, 1.0, 1.0, -0.014451968, 0.01275478, 0.003094315, -0.017023237, -0.0035364411, 0.6772645, 1.0, 0.001842304, -0.012426081, -0.022120861, 0.008980468, 0.024605373, -0.014417096, -0.0055469535], "split_indices": [141, 125, 0, 139, 0, 141, 0, 140, 139, 139, 140, 0, 0, 0, 50, 0, 71, 140, 141, 138, 39, 2, 15, 0, 0, 0, 0, 0, 139, 81, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1965.0, 96.0, 1813.0, 152.0, 1711.0, 102.0, 1510.0, 201.0, 824.0, 686.0, 90.0, 111.0, 110.0, 714.0, 118.0, 568.0, 520.0, 194.0, 235.0, 333.0, 338.0, 182.0, 103.0, 91.0, 117.0, 118.0, 148.0, 185.0, 234.0, 104.0, 91.0, 91.0, 93.0, 92.0, 97.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00022218963, -0.008573917, 0.014719988, 0.0022159119, -0.10486083, -0.017027466, 0.07990623, -0.0032041145, -0.01950579, -0.00651091, -0.014898998, -0.014544177, 0.17556417, -0.03628672, 0.055792492, 0.033383023, 0.008685453, 0.016231565, -0.08868619, 0.022822777, -0.0006014282, 0.071059495, -0.040364996, -0.04273976, -0.15076272, -0.08180718, 0.012644625, 0.016293718, -0.007093324, -0.009472403, 0.002206713, -0.013185534, 0.0015127484, -0.024323262, -0.004790292, -0.015748201, 0.0007472063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, -1, 15, 17, 19, -1, -1, 21, 23, -1, 25, 27, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.6787472, 2.0310874, 0.0, 2.6282527, 1.2939193, 1.9553969, 7.523157, 0.0, 0.0, 2.4209502, 0.0, 0.0, 3.4397316, 2.4299545, 4.1036563, 0.0, 0.0, 1.3684549, 1.2606695, 0.0, 3.2808068, 2.9222956, 0.7364439, 1.3098462, 1.7881494, 1.3107016, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, -1, 16, 18, 20, -1, -1, 22, 24, -1, 26, 28, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3441783, 1.0, 0.014719988, 0.8026405, 0.3567492, 0.8955618, 1.0, -0.0032041145, -0.01950579, 0.49648586, -0.014898998, -0.014544177, 0.95005274, 1.261253, 1.0, 0.033383023, 0.008685453, 1.0, 0.39899793, 0.022822777, 1.0, 1.0, 1.0, 0.27046308, 0.46261773, 0.6510364, 0.012644625, 0.016293718, -0.007093324, -0.009472403, 0.002206713, -0.013185534, 0.0015127484, -0.024323262, -0.004790292, -0.015748201, 0.0007472063], "split_indices": [140, 40, 0, 143, 142, 140, 89, 0, 0, 139, 0, 0, 143, 138, 17, 0, 0, 23, 141, 0, 97, 15, 116, 143, 142, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1955.0, 117.0, 1758.0, 197.0, 1409.0, 349.0, 109.0, 88.0, 1305.0, 104.0, 104.0, 245.0, 883.0, 422.0, 88.0, 157.0, 441.0, 442.0, 104.0, 318.0, 224.0, 217.0, 254.0, 188.0, 194.0, 124.0, 136.0, 88.0, 116.0, 101.0, 100.0, 154.0, 99.0, 89.0, 105.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0047741644, -0.012319082, 0.114121586, -0.0009616281, -0.012367751, 0.019645736, -0.0014718605, -0.020844461, 0.019664936, -0.0036400182, -0.06996109, -0.024789592, 0.013636418, 0.0069827503, -0.016975494, -0.03968926, 0.0117678, 0.008558158, -0.010340883, -0.018458772, -0.014220633, -0.067662835, 0.06423397, -0.01778912, -0.019556476, -0.0065589095, 0.024765374, -0.006452658, 0.010061241], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1], "loss_changes": [3.8297913, 2.241134, 2.9384596, 6.317938, 0.0, 0.0, 0.0, 1.2345803, 0.0, 3.2038333, 2.91016, 1.9953566, 0.0, 1.8568029, 0.0, 1.8521903, 0.0, 0.0, 0.0, 2.8685174, 0.0, 2.8194923, 6.2625875, 1.7597439, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1], "split_conditions": [1.0008775, 0.85554415, 1.2045896, 0.78044474, -0.012367751, 0.019645736, -0.0014718605, 0.5138817, 0.019664936, 0.4508597, 1.0, 1.0, 0.013636418, 1.0, -0.016975494, 1.0, 0.0117678, 0.008558158, -0.010340883, 1.0, -0.014220633, 0.34807172, 0.3270204, 0.2680222, -0.019556476, -0.0065589095, 0.024765374, -0.006452658, 0.010061241], "split_indices": [139, 142, 143, 140, 0, 0, 0, 141, 0, 141, 124, 41, 0, 12, 0, 7, 0, 0, 0, 115, 0, 141, 140, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1772.0, 277.0, 1608.0, 164.0, 169.0, 108.0, 1461.0, 147.0, 1082.0, 379.0, 940.0, 142.0, 214.0, 165.0, 851.0, 89.0, 125.0, 89.0, 705.0, 146.0, 442.0, 263.0, 318.0, 124.0, 154.0, 109.0, 228.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026121205, 0.0040000016, -0.013411991, -0.007795562, 0.019593662, -0.017201953, 0.012351458, -0.0065751136, -0.020437138, 0.011574863, -0.15267743, -0.018296845, 0.059841543, -0.0068153986, -0.024004994, -0.043916807, 0.044767655, -0.010314542, 0.092509195, -0.082706936, 0.027141212, -0.0039805337, 0.0149754835, 0.02211564, 0.16537268, -0.029986998, -0.02133992, 0.013997248, -0.005618032, 0.015863968, -0.009112521, -0.00021980074, 0.029397348, 0.0025721206, -0.010069358], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, 23, 25, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.7982206, 4.4578176, 0.0, 2.2912111, 0.0, 3.4429917, 0.0, 4.343573, 0.0, 2.1007147, 1.3366885, 1.454139, 2.9656916, 0.0, 0.0, 1.7640642, 2.3085616, 0.0, 2.3799112, 2.8524957, 2.1246881, 0.0, 0.0, 3.6485834, 4.9133387, 1.1619867, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, 24, 26, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4244329, 1.0941913, -0.013411991, 5.0, 0.019593662, 1.0711201, 0.012351458, 2.0, -0.020437138, 0.48394743, 0.41860023, 1.0, -0.46153846, -0.0068153986, -0.024004994, 1.0, 0.3284141, -0.010314542, 0.62454957, 0.3449051, 1.0, -0.0039805337, 0.0149754835, 0.46811774, -0.15384616, 1.0, -0.02133992, 0.013997248, -0.005618032, 0.015863968, -0.009112521, -0.00021980074, 0.029397348, 0.0025721206, -0.010069358], "split_indices": [143, 142, 0, 0, 0, 140, 0, 0, 0, 139, 143, 83, 1, 0, 0, 71, 140, 0, 141, 140, 121, 0, 0, 141, 1, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1969.0, 99.0, 1855.0, 114.0, 1731.0, 124.0, 1638.0, 93.0, 1457.0, 181.0, 900.0, 557.0, 92.0, 89.0, 640.0, 260.0, 93.0, 464.0, 414.0, 226.0, 144.0, 116.0, 236.0, 228.0, 295.0, 119.0, 96.0, 130.0, 107.0, 129.0, 99.0, 129.0, 165.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.006478492, -0.016123533, 0.014556812, -0.025067013, 0.009705554, -0.01576814, -0.019166362, -0.032016367, 0.0790458, -0.013646156, -0.022217674, -0.006529988, 0.027377633, -0.050123565, 0.050569277, 0.0072499933, -0.06771233, 0.009945862, 0.02315281, -0.083287425, 0.004750799, -0.04030731, 0.01340223, -0.06777774, -0.01648624, 0.010876085, -0.015446712, -0.009356531, 0.00093967206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, -1, 19, 21, -1, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [3.0239143, 1.9626837, 0.0, 2.7838411, 0.0, 2.6220303, 0.0, 5.0757446, 6.999019, 3.103697, 0.0, 0.0, 0.0, 1.8224921, 3.5285587, 0.0, 1.3261862, 2.4442122, 0.0, 0.82364655, 0.0, 4.747908, 0.0, 1.088608, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, -1, 20, 22, -1, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.3316327, 1.0, 0.014556812, 1.5727334, 0.009705554, 1.0, -0.019166362, 0.8019608, 0.53443044, 0.4508597, -0.022217674, -0.006529988, 0.027377633, 0.09383088, 1.0, 0.0072499933, 1.0, 0.6913856, 0.02315281, 0.45524624, 0.004750799, 0.49750775, 0.01340223, 0.39070654, -0.01648624, 0.010876085, -0.015446712, -0.009356531, 0.00093967206], "split_indices": [139, 125, 0, 138, 0, 42, 0, 141, 143, 141, 0, 0, 0, 139, 61, 0, 41, 139, 0, 140, 0, 140, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1939.0, 123.0, 1797.0, 142.0, 1702.0, 95.0, 1453.0, 249.0, 1325.0, 128.0, 143.0, 106.0, 845.0, 480.0, 106.0, 739.0, 392.0, 88.0, 651.0, 88.0, 279.0, 113.0, 547.0, 104.0, 121.0, 158.0, 410.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.001224909, 0.0056854645, -0.01418546, -0.014678321, 0.057620447, -0.04918624, 0.02751004, 0.0024404316, 0.025599485, -0.016310858, -0.030443216, -0.044789862, 0.08124112, -0.013926177, 0.05157557, -0.013434516, 0.013144351, 0.0044421386, -0.014223603, -0.0026644513, 0.019293709, 0.13723831, -0.016027847, -0.03094999, 0.015238965, -0.011504968, 0.011522981, -0.00058725436, 0.022923816, 0.041072782, -0.1242254, -0.0030107342, 0.015325888, -0.004456287, -0.019801801], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, -1, 17, 19, -1, 21, -1, 23, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [2.012606, 2.0876884, 0.0, 2.0643709, 6.086144, 6.545219, 2.478472, 3.0287125, 0.0, 2.4024174, 0.0, 2.364576, 3.4301212, 0.0, 5.8618026, 0.0, 3.3953815, 0.0, 0.0, 2.769161, 0.0, 3.0282216, 0.0, 2.8215406, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8925452, 1.0757661, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 16, 16, 19, 19, 21, 21, 23, 23, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, -1, 18, 20, -1, 22, -1, 24, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 0.71048903, -0.01418546, 1.0, 1.0, 0.6375918, 1.0, 0.86804646, 0.025599485, -1.0, -0.030443216, 0.45160067, 0.44334212, -0.013926177, 1.0, -0.013434516, 0.4445411, 0.0044421386, -0.014223603, 0.42307693, 0.019293709, 1.5844367, -0.016027847, 0.29541424, 0.015238965, -0.011504968, 0.011522981, -0.00058725436, 0.022923816, 0.1975233, 0.3319169, -0.0030107342, 0.015325888, -0.004456287, -0.019801801], "split_indices": [117, 142, 0, 39, 115, 139, 69, 142, 0, 0, 0, 140, 139, 0, 97, 0, 141, 0, 0, 1, 0, 138, 0, 142, 0, 0, 0, 0, 0, 142, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1974.0, 97.0, 1418.0, 556.0, 780.0, 638.0, 435.0, 121.0, 691.0, 89.0, 272.0, 366.0, 112.0, 323.0, 138.0, 553.0, 142.0, 130.0, 209.0, 157.0, 230.0, 93.0, 420.0, 133.0, 107.0, 102.0, 90.0, 140.0, 237.0, 183.0, 145.0, 92.0, 88.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0020026097, 0.008793332, -0.013881549, 0.01834631, -0.07767085, 0.027863042, -0.007323085, -0.0016288813, -0.015300331, 0.0020738144, 0.16523635, 0.09011753, -0.015675671, 0.008135108, 0.030329753, 0.021363927, -0.0020022665, -0.110313244, 0.0043927873, -0.006157683, -0.015855733, -0.016345574, 0.011744568, 0.004460666, -0.014268259, -0.027865898, 0.01615959, 0.0012187188, -0.010565322], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, 19, 21, -1, -1, 23, -1, 25, -1, 27, -1, -1, -1], "loss_changes": [1.9746696, 1.6272011, 0.0, 1.5460682, 0.9063151, 5.693202, 0.0, 0.0, 0.0, 2.114375, 2.941649, 3.08827, 2.1385334, 0.0, 0.0, 0.0, 0.0, 0.46319437, 2.1780703, 0.0, 0.0, 2.0634499, 0.0, 3.423679, 0.0, 1.7416333, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 18, 18, 21, 21, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, 20, 22, -1, -1, 24, -1, 26, -1, 28, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.013881549, 1.1391696, 0.3567492, 0.79509693, -0.007323085, -0.0016288813, -0.015300331, 1.2193315, 0.92508495, 1.0, 0.2845349, 0.008135108, 0.030329753, 0.021363927, -0.0020022665, 1.0, 1.2692307, -0.006157683, -0.015855733, 0.8955618, 0.011744568, 0.69209933, -0.014268259, 0.50989574, 0.01615959, 0.0012187188, -0.010565322], "split_indices": [117, 40, 0, 143, 142, 143, 0, 0, 0, 138, 140, 23, 139, 0, 0, 0, 0, 108, 1, 0, 0, 140, 0, 142, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1970.0, 95.0, 1774.0, 196.0, 1607.0, 167.0, 108.0, 88.0, 1353.0, 254.0, 227.0, 1126.0, 158.0, 96.0, 107.0, 120.0, 197.0, 929.0, 98.0, 99.0, 785.0, 144.0, 674.0, 111.0, 559.0, 115.0, 369.0, 190.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0025150222, 0.015560442, -0.0493523, -0.02375226, 0.035922036, -0.013880146, -0.017216982, 0.02493588, -0.07968788, 0.14335637, 0.012288889, -0.06432988, 0.010848725, 0.07826154, -0.006131255, -0.0001925704, -0.01300621, 0.027738208, 0.0029560926, -0.015576374, 0.019662833, -0.0008104158, -0.011065985, 0.001444851, 0.01368116, -0.12349761, 0.032343876, -0.026271788, -0.00018168754, 0.023515342, -0.010787564, 0.044378385, -0.01793221, -0.03403455, 0.024673438, -0.016975578, 0.006628112], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, 31, 33, -1, 35, -1, -1, -1], "loss_changes": [1.3972428, 1.3207743, 1.8079895, 1.5332751, 2.759905, 1.9878354, 0.0, 1.3843756, 1.0263084, 2.9892964, 4.5767713, 0.59392506, 0.0, 0.6949434, 0.0, 0.0, 0.0, 0.0, 0.0, 4.0028286, 0.0, 0.0, 0.0, 0.0, 0.0, 4.0318203, 4.6886435, 0.0, 0.0, 0.0, 4.109437, 5.283821, 0.0, 3.267592, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 19, 19, 25, 25, 26, 26, 30, 30, 31, 31, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, 32, 34, -1, 36, -1, -1, -1], "split_conditions": [1.0, 0.4241147, 0.35110125, 0.31376776, 1.3282632, 0.28752056, -0.017216982, 1.0, 1.0, 1.0, 1.0, 0.16375841, 0.010848725, 1.0, -0.006131255, -0.0001925704, -0.01300621, 0.027738208, 0.0029560926, 1.4212835, 0.019662833, -0.0008104158, -0.011065985, 0.001444851, 0.01368116, 1.0, 1.4632982, -0.026271788, -0.00018168754, 0.023515342, 1.0, 1.3316327, -0.01793221, 1.0, 0.024673438, -0.016975578, 0.006628112], "split_indices": [80, 140, 143, 139, 138, 139, 0, 108, 53, 97, 62, 142, 0, 15, 0, 0, 0, 0, 0, 138, 0, 0, 0, 0, 0, 105, 138, 0, 0, 0, 119, 139, 0, 109, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1650.0, 415.0, 563.0, 1087.0, 322.0, 93.0, 301.0, 262.0, 196.0, 891.0, 228.0, 94.0, 186.0, 115.0, 103.0, 159.0, 90.0, 106.0, 774.0, 117.0, 103.0, 125.0, 89.0, 97.0, 238.0, 536.0, 111.0, 127.0, 94.0, 442.0, 333.0, 109.0, 240.0, 93.0, 102.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.003678842, -0.0032249917, 0.012158908, 0.0067303106, -0.017964447, -0.0060346522, 0.015510117, 0.004339059, -0.011791226, -0.0062586926, 0.01490084, 0.042351563, -0.03223915, 0.018440219, -0.0015549839, -0.0075588757, -0.017261913, 0.0064539053, -0.05436846, -0.028768755, 0.014133445, -0.016068464, 0.0042455173, -0.049275305, 0.008526295, -0.0027166333, -0.017015366], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, -1, -1], "loss_changes": [1.6777219, 3.4195347, 0.0, 3.4905477, 0.0, 1.969515, 0.0, 2.3810127, 0.0, 1.8274405, 0.0, 3.1434245, 3.2671328, 0.0, 1.3439025, 2.5327234, 0.0, 0.0, 2.2028992, 1.6415544, 0.0, 0.0, 0.0, 1.5901346, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 18, 18, 19, 19, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, -1, -1], "split_conditions": [1.3441783, 1.6290085, 0.012158908, 0.9525937, -0.017964447, 1.4902518, 0.015510117, 1.4448426, -0.011791226, 1.0, 0.01490084, 1.0, 0.5930298, 0.018440219, 0.32576862, 0.52568775, -0.017261913, 0.0064539053, 0.5594404, 0.4431363, 0.014133445, -0.016068464, 0.0042455173, 0.4247245, 0.008526295, -0.0027166333, -0.017015366], "split_indices": [140, 138, 0, 140, 0, 138, 0, 138, 0, 53, 0, 81, 141, 0, 143, 142, 0, 0, 143, 141, 0, 0, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1947.0, 114.0, 1843.0, 104.0, 1697.0, 146.0, 1553.0, 144.0, 1447.0, 106.0, 504.0, 943.0, 119.0, 385.0, 802.0, 141.0, 171.0, 214.0, 702.0, 100.0, 102.0, 112.0, 595.0, 107.0, 503.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010034549, 0.01428216, -0.06285601, 0.023845157, -0.008237469, 0.0038698807, -0.09400383, 0.0119752325, 0.015850067, -0.013503181, -0.0048263157, 0.035968512, -0.064657845, 0.014429373, 0.02036148, -0.016231345, -0.017138418, 0.056525756, -0.056782655, 0.013134432, -0.011317889, 0.2284436, -0.023845825, -0.1175527, 0.005388796, 0.011205615, 0.035122592, -0.0205971, 0.08084029, -0.004682187, -0.021903602, 0.021861162, -0.0034967472], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, -1, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.9514179, 1.5297644, 1.2937535, 2.4071157, 0.0, 0.0, 0.58738995, 2.5447316, 0.0, 0.0, 0.0, 3.8059478, 1.7055656, 2.7999163, 0.0, 3.2477114, 0.0, 8.110761, 2.333734, 0.0, 0.0, 2.672289, 7.6263905, 1.607871, 0.0, 0.0, 0.0, 0.0, 4.0525675, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, -1, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 0.88461536, -0.008237469, 0.0038698807, 0.26923078, 0.1923077, 0.015850067, -0.013503181, -0.0048263157, 0.03846154, 0.60375583, 1.0, 0.02036148, 1.0, -0.017138418, 0.6489657, 0.6326447, 0.013134432, -0.011317889, 0.41125435, 0.900655, 0.4522735, 0.005388796, 0.011205615, 0.035122592, -0.0205971, 1.1395987, -0.004682187, -0.021903602, 0.021861162, -0.0034967472], "split_indices": [80, 1, 26, 1, 0, 0, 1, 1, 0, 0, 0, 1, 142, 106, 0, 97, 0, 141, 143, 0, 0, 143, 142, 142, 0, 0, 0, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1655.0, 409.0, 1506.0, 149.0, 96.0, 313.0, 1384.0, 122.0, 165.0, 148.0, 1054.0, 330.0, 934.0, 120.0, 227.0, 103.0, 587.0, 347.0, 90.0, 137.0, 187.0, 400.0, 224.0, 123.0, 96.0, 91.0, 146.0, 254.0, 132.0, 92.0, 116.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0033187151, 0.009775962, -0.012529907, 0.00022979546, 0.01608687, -0.006662189, 0.009791373, -0.106879875, 0.0064182985, 0.0037121966, -0.022208137, -0.009046011, 0.06994173, 0.033627078, -0.04874819, 0.016030401, -0.0039976547, 0.016627839, -0.020769492, -0.021337474, -0.017329862, 0.015045251, -0.011712069, -0.06705916, 0.04413253, -0.025838563, 0.010498967, -0.0016526909, -0.0116607, -0.00670949, 0.015432048, 0.008543879, -0.011165417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, -1, 13, 15, 17, 19, -1, -1, -1, 21, 23, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.7025588, 2.8154793, 0.0, 1.236062, 0.0, 2.2481873, 0.0, 3.284668, 1.4902191, 0.0, 0.0, 2.066942, 2.9499428, 4.2428765, 2.1576579, 0.0, 0.0, 0.0, 1.438981, 1.5505807, 0.0, 1.1178906, 0.0, 0.76364756, 2.6105118, 1.9958107, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, -1, 14, 16, 18, 20, -1, -1, -1, 22, 24, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4244329, 1.077678, -0.012529907, 5.0, 0.01608687, -0.46153846, 0.009791373, 1.3595198, 1.4266527, 0.0037121966, -0.022208137, 1.0, 1.0, -0.03846154, 0.52471125, 0.016030401, -0.0039976547, 0.016627839, 1.0, 0.33214152, -0.017329862, 1.0, -0.011712069, 1.0, 0.07692308, 0.23310745, 0.010498967, -0.0016526909, -0.0116607, -0.00670949, 0.015432048, 0.008543879, -0.011165417], "split_indices": [143, 142, 0, 0, 0, 1, 0, 138, 138, 0, 0, 106, 105, 1, 140, 0, 0, 0, 121, 140, 0, 115, 0, 126, 1, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1952.0, 98.0, 1836.0, 116.0, 1715.0, 121.0, 198.0, 1517.0, 88.0, 110.0, 1220.0, 297.0, 588.0, 632.0, 163.0, 134.0, 171.0, 417.0, 518.0, 114.0, 304.0, 113.0, 305.0, 213.0, 209.0, 95.0, 151.0, 154.0, 106.0, 107.0, 91.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00046468325, -0.0077640917, 0.07572788, -0.017738447, 0.012287742, 0.026100665, -0.01042063, -0.007198903, -0.016967572, -0.019358471, 0.06713906, -0.008257195, -0.014340314, -0.0039498135, 0.023183431, -0.018192008, 0.01252488, 0.009041323, -0.028863048, -0.019755093, -0.012996132, -0.039976627, 0.01524168, -0.001550386, -0.010591152], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, -1, 19, 21, -1, 23, -1, -1, -1], "loss_changes": [1.288194, 2.4432464, 6.8342876, 2.7895503, 0.0, 0.0, 0.0, 1.4724815, 0.0, 1.9278753, 4.021845, 1.7043682, 0.0, 0.0, 0.0, 1.3860815, 0.0, 0.0, 1.0027492, 3.478098, 0.0, 1.4425663, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 18, 18, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, -1, 20, 22, -1, 24, -1, -1, -1], "split_conditions": [1.1466392, 1.0, 1.1391696, 1.5261788, 0.012287742, 0.026100665, -0.01042063, 1.0, -0.016967572, 0.8365981, 0.53443044, 0.8046712, -0.014340314, -0.0039498135, 0.023183431, 0.09383088, 0.01252488, 0.009041323, 0.66498107, 0.60352135, -0.012996132, 0.45561248, 0.01524168, -0.001550386, -0.010591152], "split_indices": [139, 125, 143, 138, 0, 0, 0, 42, 0, 143, 143, 140, 0, 0, 0, 139, 0, 0, 140, 139, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1875.0, 205.0, 1742.0, 133.0, 101.0, 104.0, 1629.0, 113.0, 1400.0, 229.0, 1285.0, 115.0, 139.0, 90.0, 1196.0, 89.0, 107.0, 1089.0, 999.0, 90.0, 894.0, 105.0, 652.0, 242.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.005920975, -0.0123444665, 0.012250794, 0.012003287, 0.005849049, -0.01240957, 0.016557531, 0.026618984, -0.012847239, 0.004261382, 0.16254528, 0.014484687, -0.013987595, 0.02802187, 0.0074062133, 0.07103307, -0.0039944085, 0.0014169329, 0.02214039, 0.052227154, -0.042818744, -0.008775186, 0.009395062, 0.019184468, -0.0058217025, -0.112744555, -0.010186693, 0.014150898, -0.007597919, -0.0169405, -0.0053537525, 0.004407032, -0.010707423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, -1, 3, -1, 5, -1, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6852156, 0.0, 1.3537666, 0.0, 2.5770779, 0.0, 2.4967108, 4.862377, 0.0, 2.0246708, 2.3531365, 1.3406876, 0.0, 0.0, 0.0, 3.3079615, 2.1107337, 1.7822412, 0.0, 3.2013333, 1.3052024, 0.0, 0.0, 0.0, 2.8838418, 0.6105542, 2.0501633, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 24, 24, 25, 25, 26, 26], "right_children": [2, -1, 4, -1, 6, -1, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [0.09087678, -0.0123444665, -0.5769231, 0.012003287, -0.5, -0.01240957, 1.1303518, 0.79509693, -0.012847239, 0.7258471, 1.0, 0.0, -0.013987595, 0.02802187, 0.0074062133, 0.49431247, 1.0, 1.0, 0.02214039, -0.115384616, -0.115384616, -0.008775186, 0.009395062, 0.019184468, 0.28219718, 1.0, 1.0, 0.014150898, -0.007597919, -0.0169405, -0.0053537525, 0.004407032, -0.010707423], "split_indices": [143, 0, 1, 0, 1, 0, 143, 143, 0, 143, 69, 0, 0, 0, 0, 143, 53, 13, 0, 1, 1, 0, 0, 0, 143, 69, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 96.0, 1962.0, 110.0, 1852.0, 141.0, 1711.0, 1600.0, 111.0, 1374.0, 226.0, 1283.0, 91.0, 97.0, 129.0, 316.0, 967.0, 216.0, 100.0, 395.0, 572.0, 110.0, 106.0, 116.0, 279.0, 182.0, 390.0, 90.0, 189.0, 93.0, 89.0, 250.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0041423575, -0.0132887, 0.08741887, -0.006802969, -0.014022368, 0.025970448, -0.0069044554, 0.0159969, -0.016199967, -0.005913269, -0.013830542, -0.016305408, 0.014212373, -0.002233314, -0.01225083, 0.03242658, -0.029164344, 0.153917, -0.0263902, 0.008003084, -0.017835028, 0.006466318, 0.024220062, 0.020516, -0.012601745, -0.024510337, 0.014479381, -0.0059917006, 0.009668364, -0.0060082166, 0.007835151], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, 9, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.72431, 1.5411537, 5.0408463, 2.7911043, 0.0, 0.0, 0.0, 0.0, 2.1177201, 2.3922443, 0.0, 2.171504, 0.0, 1.1975864, 0.0, 4.008724, 4.0033884, 1.4419751, 1.7664456, 2.5706754, 0.0, 0.0, 0.0, 1.5744832, 0.0, 1.7087455, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, 10, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.1789773, 1.0567883, 1.1852549, 1.0, -0.014022368, 0.025970448, -0.0069044554, 0.0159969, 0.8559731, 0.76225835, -0.013830542, 0.6375918, 0.014212373, 1.0, -0.01225083, -0.03846154, 0.49750775, 1.0, 0.4235597, 0.44982666, -0.017835028, 0.006466318, 0.024220062, 0.26801515, -0.012601745, 1.0, 0.014479381, -0.0059917006, 0.009668364, -0.0060082166, 0.007835151], "split_indices": [139, 142, 143, 104, 0, 0, 0, 0, 139, 139, 0, 139, 0, 122, 0, 1, 140, 126, 142, 142, 0, 0, 0, 139, 0, 93, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1872.0, 187.0, 1781.0, 91.0, 89.0, 98.0, 95.0, 1686.0, 1555.0, 131.0, 1453.0, 102.0, 1283.0, 170.0, 561.0, 722.0, 183.0, 378.0, 578.0, 144.0, 91.0, 92.0, 257.0, 121.0, 467.0, 111.0, 125.0, 132.0, 347.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011070935, -0.006891385, 0.01150509, 0.00037167798, -0.015008844, -0.010786966, 0.022671467, -0.020678379, 0.014588925, -0.0019362422, -0.02103339, 0.02512564, -0.031287126, 0.10229522, -0.026862282, 0.00011810017, -0.019496694, -0.0020063433, 0.023418834, -0.06925467, 0.009854849, -0.049058132, 0.07048094, -0.0300548, -0.017501261, 0.0005600718, -0.014788451, -0.00463064, 0.01588825, -0.008230185, 0.00586563, -0.0076768445, 0.007853302], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.388128, 2.0468173, 0.0, 4.730601, 0.0, 2.766302, 0.0, 5.968088, 0.0, 1.2136754, 0.0, 3.1894498, 3.7679152, 5.164246, 2.5253196, 2.1280105, 0.0, 0.0, 0.0, 1.4717218, 0.0, 1.7750988, 2.612018, 1.2004374, 0.0, 1.4531173, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.253856, 1.2337983, 0.01150509, 1.1466392, -0.015008844, 1.0, 0.022671467, 0.8019608, 0.014588925, 0.1923077, -0.02103339, 1.0, 0.54242957, 1.0, 0.5966164, 1.0, -0.019496694, -0.0020063433, 0.023418834, 0.4522735, 0.009854849, 0.33421066, 0.29541424, 0.2845349, -0.017501261, 0.20086846, -0.014788451, -0.00463064, 0.01588825, -0.008230185, 0.00586563, -0.0076768445, 0.007853302], "split_indices": [141, 140, 0, 139, 0, 125, 0, 141, 0, 1, 0, 122, 142, 111, 142, 39, 0, 0, 0, 142, 0, 141, 142, 139, 0, 141, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1968.0, 98.0, 1873.0, 95.0, 1785.0, 88.0, 1679.0, 106.0, 1528.0, 151.0, 795.0, 733.0, 320.0, 475.0, 615.0, 118.0, 166.0, 154.0, 355.0, 120.0, 362.0, 253.0, 259.0, 96.0, 241.0, 121.0, 109.0, 144.0, 163.0, 96.0, 121.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0004554379, 0.0068067973, -0.01081862, -0.002135016, 0.019378114, -0.011704116, 0.01263431, -0.0002271133, -0.014221402, -0.008972923, 0.008639232, 0.00837803, -0.06858357, -0.011478098, 0.01870832, -0.022385688, -0.0066075106, 0.007922297, -0.022929745, 0.011189864, -0.00957099, -0.06965983, 0.026930058, 0.0029187598, -0.009457259, 0.0097587025, -0.0021457577], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, 19, -1, 21, -1, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [1.4242054, 3.2601857, 0.0, 2.2879505, 0.0, 2.5942972, 0.0, 1.2060307, 0.0, 1.4956001, 0.0, 3.9742007, 3.1371713, 1.0469857, 0.0, 0.0, 2.4602892, 0.0, 2.0853078, 0.0, 0.0, 1.1377044, 1.4803941, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 16, 16, 18, 18, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, 20, -1, 22, -1, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.6937072, 1.1466392, -0.01081862, 1.0, 0.019378114, 0.85554415, 0.01263431, 0.71048903, -0.014221402, 0.54254174, 0.008639232, 0.48394743, 0.60352135, 0.09383088, 0.01870832, -0.022385688, 0.71545964, 0.007922297, 1.0, 0.011189864, -0.00957099, 0.17656, 1.0, 0.0029187598, -0.009457259, 0.0097587025, -0.0021457577], "split_indices": [138, 139, 0, 125, 0, 142, 0, 142, 0, 139, 0, 139, 139, 139, 0, 0, 139, 0, 17, 0, 0, 140, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1950.0, 114.0, 1861.0, 89.0, 1732.0, 129.0, 1592.0, 140.0, 1446.0, 146.0, 1120.0, 326.0, 1008.0, 112.0, 93.0, 233.0, 113.0, 895.0, 100.0, 133.0, 462.0, 433.0, 93.0, 369.0, 176.0, 257.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0026744748, 0.011450244, -0.07600923, -0.01843192, 0.032018136, -0.0008483112, -0.01294429, 0.008812236, -0.03843186, 0.05027136, -0.063188985, -0.01590179, -0.017963376, 0.024518145, 0.027337546, -0.0026078352, -0.0100299595, 0.0066444697, -0.04120043, 0.053062703, -0.018423194, -0.010016574, -0.014048009, 0.022447368, 0.018910525, -0.008985717, 0.0039151385, -0.004768101, 0.014013591, -0.07654162, 0.104780935, 0.0031274909, -0.015050878, -0.0016982716, 0.021670511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, 25, 27, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [1.4176168, 1.1351905, 0.74328244, 1.6047025, 1.9011935, 0.0, 0.0, 0.0, 1.5648468, 5.274503, 0.2423864, 0.0, 1.0630759, 4.9039927, 0.0, 0.0, 0.0, 0.0, 0.6804462, 3.0154507, 0.0, 0.0, 1.1736035, 1.8929433, 0.0, 0.0, 0.0, 3.7741053, 0.0, 2.3127165, 2.5893776, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 18, 18, 19, 19, 22, 22, 23, 23, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, 26, 28, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 0.42739695, 0.07692308, 0.16683866, 1.0, -0.0008483112, -0.01294429, 0.008812236, 1.1966957, 1.3316327, 0.78879374, -0.01590179, 1.2461375, 1.0279931, 0.027337546, -0.0026078352, -0.0100299595, 0.0066444697, 1.0, 1.0, -0.018423194, -0.010016574, 0.36576095, 1.0, 0.018910525, -0.008985717, 0.0039151385, 0.6913856, 0.014013591, 0.5338411, 0.7513225, 0.0031274909, -0.015050878, -0.0016982716, 0.021670511], "split_indices": [40, 140, 1, 142, 119, 0, 0, 0, 138, 139, 140, 0, 138, 142, 0, 0, 0, 0, 81, 42, 0, 0, 139, 116, 0, 0, 0, 139, 0, 140, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1847.0, 206.0, 753.0, 1094.0, 91.0, 115.0, 119.0, 634.0, 918.0, 176.0, 92.0, 542.0, 823.0, 95.0, 88.0, 88.0, 117.0, 425.0, 724.0, 99.0, 134.0, 291.0, 591.0, 133.0, 120.0, 171.0, 480.0, 111.0, 290.0, 190.0, 118.0, 172.0, 91.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0015128837, 0.0084465025, -0.0075200335, -0.0030719568, 0.019619742, 0.0077104596, -0.02119813, -0.0013704568, 0.011865254, -0.0134577835, 0.06274071, 0.0050767073, -0.12495894, -0.006023081, 0.019811274, -0.013961887, 0.016794471, -0.0018085319, -0.023764087, -9.235426e-05, -0.013717177, -0.012854692, 0.011546987, 0.00035625548, -0.013883548], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.1036928, 4.1154313, 0.0, 4.0388184, 0.0, 1.7177123, 0.0, 1.2212937, 0.0, 2.7403347, 4.161727, 3.525585, 2.2760746, 0.0, 0.0, 1.7396227, 0.0, 0.0, 0.0, 1.349482, 0.0, 1.7042447, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.638525, 1.0977916, -0.0075200335, 0.95005274, 0.019619742, 0.79509693, -0.02119813, 1.4230363, 0.011865254, 0.54411215, -0.07692308, 0.48394743, -0.03846154, -0.006023081, 0.019811274, 0.50989574, 0.016794471, -0.0018085319, -0.023764087, 0.446167, -0.013717177, 0.45561248, 0.011546987, 0.00035625548, -0.013883548], "split_indices": [138, 143, 0, 143, 0, 143, 0, 138, 0, 139, 1, 139, 1, 0, 0, 141, 0, 0, 0, 141, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1903.0, 172.0, 1793.0, 110.0, 1705.0, 88.0, 1576.0, 129.0, 1326.0, 250.0, 1137.0, 189.0, 131.0, 119.0, 1018.0, 119.0, 97.0, 92.0, 915.0, 103.0, 824.0, 91.0, 729.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0072497386, 0.0015582233, -0.1014004, -0.010079734, 0.022416515, -0.011238939, -0.009028649, 0.0047456753, -0.015023159, -0.007923439, 0.020665972, 0.0016716362, -0.01596522, -0.008453095, 0.011714597, -0.030950747, 0.032377746, -0.00962252, -0.11183025, 0.071257, -0.00703961, -0.032762054, 0.012661737, -0.018556563, -0.0036419015, 0.003180448, 0.022218142, -0.005518852, 0.0050015263, 0.010889967, -0.006010723], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.7157711, 4.901586, 0.021616578, 3.7359002, 0.0, 0.0, 0.0, 4.1594257, 0.0, 2.2274492, 0.0, 1.6824024, 0.0, 1.2153052, 0.0, 1.4714386, 1.8780124, 2.127956, 0.9897642, 3.5035746, 0.0, 1.0711441, 0.0, 0.0, 0.0, 1.5723202, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.1303518, 1.1466392, 1.0947773, 1.4969914, 0.022416515, -0.011238939, -0.009028649, 0.76225835, -0.015023159, 0.787365, 0.020665972, 0.708655, -0.01596522, 1.0, 0.011714597, 0.46482533, 0.50989574, 1.0, 0.49550733, 0.42739695, -0.00703961, 1.3076923, 0.012661737, -0.018556563, -0.0036419015, 0.2707212, 0.022218142, -0.005518852, 0.0050015263, 0.010889967, -0.006010723], "split_indices": [143, 139, 141, 138, 0, 0, 0, 139, 0, 142, 0, 140, 0, 93, 0, 140, 141, 73, 141, 140, 0, 1, 0, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1892.0, 177.0, 1798.0, 94.0, 89.0, 88.0, 1626.0, 172.0, 1530.0, 96.0, 1439.0, 91.0, 1323.0, 116.0, 853.0, 470.0, 675.0, 178.0, 341.0, 129.0, 577.0, 98.0, 90.0, 88.0, 235.0, 106.0, 454.0, 123.0, 88.0, 147.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.002716643, 0.0031906243, -0.009921859, -0.0043455786, 0.015843643, -0.055235084, 0.009743663, -0.014038195, -0.018655013, -0.0023439121, 0.012213241, -0.008355482, 0.00573199, 0.036020197, -0.051449977, 0.0043907003, 0.026020724, -0.006682215, -0.13225031, 0.052603386, -0.020053577, -0.078510724, 0.016756838, -0.003776917, -0.025061125, -0.0023999654, 0.013431333, -0.06370381, 0.0050212643, 0.004824146, -0.017767062, 0.0012830591, -0.018721975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.1760397, 2.2744105, 0.0, 1.3293079, 0.0, 2.1747277, 1.9725534, 1.5179356, 0.0, 2.4698064, 0.0, 0.0, 0.0, 5.2189183, 2.0799181, 0.7601482, 0.0, 4.6309795, 2.2924893, 1.3582532, 1.3127347, 3.2930083, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4956503, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.6848469, 1.1268123, -0.009921859, 1.0, 0.015843643, 1.0, 0.9525937, 1.0, -0.018655013, 1.3282632, 0.012213241, -0.008355482, 0.00573199, 0.4294398, 0.23076923, 0.22160263, 0.026020724, -0.1923077, 0.5588055, 0.15396746, 0.30725226, 1.0, 0.016756838, -0.003776917, -0.025061125, -0.0023999654, 0.013431333, 0.33214152, 0.0050212643, 0.004824146, -0.017767062, 0.0012830591, -0.018721975], "split_indices": [138, 139, 0, 5, 0, 113, 140, 97, 0, 138, 0, 0, 0, 139, 1, 141, 0, 1, 142, 143, 139, 93, 0, 0, 0, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1944.0, 119.0, 1854.0, 90.0, 402.0, 1452.0, 306.0, 96.0, 1311.0, 141.0, 155.0, 151.0, 736.0, 575.0, 645.0, 91.0, 370.0, 205.0, 217.0, 428.0, 262.0, 108.0, 114.0, 91.0, 112.0, 105.0, 264.0, 164.0, 115.0, 147.0, 163.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024619398, 0.005641429, -0.009159902, -0.0047574383, 0.01693546, 0.0074012172, -0.110796876, -0.006306146, 0.016460756, 0.00039346694, -0.022182744, -0.019751353, 0.05048908, 0.0038893605, -0.098900124, -0.012775515, 0.018923989, -0.029328158, 0.09770454, -0.0019729293, -0.022172589, 0.008594121, -0.0104582105, -0.010996653, -0.01045848, 0.0013171614, 0.024275532, 0.00620442, -0.04069924, -0.011652531, -0.0011241621], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1], "loss_changes": [1.4908494, 3.2210011, 0.0, 2.2936597, 0.0, 3.439196, 2.331184, 1.1209993, 0.0, 0.0, 0.0, 2.221036, 2.4666224, 2.8483052, 2.6547105, 1.7491294, 0.0, 1.0270932, 2.930514, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1993172, 0.0, 0.0, 0.0, 0.86219054, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1], "split_conditions": [1.638525, 1.0008775, -0.009159902, 0.8201793, 0.01693546, 0.71048903, 0.8598245, 1.0, 0.016460756, 0.00039346694, -0.022182744, 0.536399, 1.0, 0.4074681, 0.30769232, 0.30816057, 0.018923989, 1.0, 0.42307693, -0.0019729293, -0.022172589, 0.008594121, -0.0104582105, -0.010996653, 1.2154769, 0.0013171614, 0.024275532, 0.00620442, 0.23882464, -0.011652531, -0.0011241621], "split_indices": [138, 139, 0, 142, 0, 142, 140, 61, 0, 0, 0, 140, 39, 140, 1, 140, 0, 26, 1, 0, 0, 0, 0, 0, 138, 0, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1892.0, 172.0, 1779.0, 113.0, 1596.0, 183.0, 1468.0, 128.0, 90.0, 93.0, 1187.0, 281.0, 914.0, 273.0, 193.0, 88.0, 675.0, 239.0, 166.0, 107.0, 93.0, 100.0, 128.0, 547.0, 151.0, 88.0, 161.0, 386.0, 108.0, 278.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.00066879933, 0.0047480143, -0.010972367, -0.0051581017, 0.0139617985, 0.0064669214, -0.056561977, 0.06088094, -0.006196865, 0.001171385, -0.10637548, -0.008767311, 0.14851116, 0.022538833, -0.09278332, -0.019122133, -0.002324365, 0.006422486, 0.023471301, -0.018727684, 0.07593647, -0.018558277, 0.0011048209, -0.059342824, 0.026031846, 0.11343031, -0.0050553693, 0.0028061415, -0.10451806, -0.0042160656, 0.0072271987, 0.0021716654, 0.020818083, -0.014143029, -0.006760582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, 25, -1, -1, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2234019, 2.6360023, 0.0, 1.0983361, 0.0, 1.0329424, 1.152959, 3.6840444, 3.0255568, 0.0, 1.3824639, 0.0, 1.2932823, 2.0118272, 2.9195595, 0.0, 0.0, 0.0, 0.0, 0.9362261, 1.8875556, 0.0, 0.0, 1.0660973, 0.77254176, 2.6678035, 0.0, 0.0, 0.24252665, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 12, 12, 13, 13, 14, 14, 19, 19, 20, 20, 23, 23, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, 26, -1, -1, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4244329, 1.1466392, -0.010972367, 1.0, 0.0139617985, -0.34615386, 0.378347, 1.0, 1.0, 0.001171385, 1.0, -0.008767311, 0.60352135, 0.4241147, 1.0, -0.019122133, -0.002324365, 0.006422486, 0.023471301, 0.88461536, 1.3461539, -0.018558277, 0.0011048209, 0.17775789, 1.0, 1.0, -0.0050553693, 0.0028061415, 0.29022333, -0.0042160656, 0.0072271987, 0.0021716654, 0.020818083, -0.014143029, -0.006760582], "split_indices": [143, 139, 0, 7, 0, 1, 143, 89, 15, 0, 97, 0, 139, 140, 111, 0, 0, 0, 0, 1, 1, 0, 0, 142, 59, 124, 0, 0, 143, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1973.0, 98.0, 1838.0, 135.0, 1499.0, 339.0, 283.0, 1216.0, 143.0, 196.0, 105.0, 178.0, 913.0, 303.0, 97.0, 99.0, 90.0, 88.0, 515.0, 398.0, 160.0, 143.0, 270.0, 245.0, 307.0, 91.0, 92.0, 178.0, 99.0, 146.0, 156.0, 151.0, 89.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009842947, 0.0063374536, -0.06773722, 0.013351651, -0.010582591, -0.002281414, -0.011975339, -0.0012717233, 0.115119755, 0.008607045, -0.015654022, 0.02740846, -0.0021135795, 0.05027307, -0.01637413, 0.12253619, -0.03227254, -0.0006229423, -0.0137537075, 0.19006863, 0.0015810787, -0.013078408, 0.004852108, -0.028517429, 0.02167088, 0.0067219078, 0.031153781, -0.0020647885, -0.013414169, -0.007002614, 0.022827074, -0.0019034653, 0.0122206155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [1.0136638, 1.470409, 0.47902852, 2.617732, 0.0, 0.0, 0.0, 2.3590794, 4.786826, 1.5050926, 0.0, 0.0, 0.0, 3.2330317, 1.7252479, 2.0829458, 2.0136535, 4.8498855, 0.0, 2.6412697, 0.0, 0.0, 0.0, 1.9809742, 0.0, 0.0, 0.0, 0.9591853, 0.0, 0.0, 1.7264749, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.6937072, 0.3567492, 0.9525937, -0.010582591, -0.002281414, -0.011975339, 0.9037621, 1.0, 1.0, -0.015654022, 0.02740846, -0.0021135795, 0.46153846, 0.68592155, 1.0, 1.0, 1.4061775, -0.0137537075, 0.5273946, 0.0015810787, -0.013078408, 0.004852108, 0.5042354, 0.02167088, 0.0067219078, 0.031153781, 1.0, -0.013414169, -0.007002614, 0.36278746, -0.0019034653, 0.0122206155], "split_indices": [40, 138, 142, 140, 0, 0, 0, 141, 108, 53, 0, 0, 0, 1, 141, 106, 23, 138, 0, 143, 0, 0, 0, 140, 0, 0, 0, 5, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1869.0, 205.0, 1759.0, 110.0, 110.0, 95.0, 1538.0, 221.0, 1446.0, 92.0, 102.0, 119.0, 542.0, 904.0, 289.0, 253.0, 800.0, 104.0, 177.0, 112.0, 114.0, 139.0, 709.0, 91.0, 88.0, 89.0, 567.0, 142.0, 152.0, 415.0, 292.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00014116251, -0.0060266866, 0.010121099, 0.0017011603, -0.013782944, -0.008581256, 0.016843008, 0.0058072326, -0.11421214, -0.0036606733, 0.011783089, -0.020425737, -0.0035523933, -0.012776624, 0.010182388, 5.669353e-06, -0.013804314, 0.011351569, -0.0132119125, -0.0453698, 0.03420176, 0.00027971715, -0.009384754, 0.019532533, 0.0012939116], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [1.2897805, 1.9861758, 0.0, 3.1578813, 0.0, 2.6369727, 0.0, 1.6195815, 1.4737833, 1.3539213, 0.0, 0.0, 0.0, 2.075147, 0.0, 1.7629116, 0.0, 1.40367, 0.0, 0.7261934, 2.6448045, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.0, 1.6937072, 0.010121099, 1.5917592, -0.013782944, 0.8201793, 0.016843008, 0.71048903, 0.9158009, 3.1923077, 0.011783089, -0.020425737, -0.0035523933, 0.71545964, 0.010182388, 1.3461539, -0.013804314, 0.22502147, -0.0132119125, 0.16683866, 1.2274326, 0.00027971715, -0.009384754, 0.019532533, 0.0012939116], "split_indices": [84, 138, 0, 138, 0, 142, 0, 142, 143, 1, 0, 0, 0, 139, 0, 1, 0, 142, 0, 142, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1950.0, 119.0, 1842.0, 108.0, 1735.0, 107.0, 1527.0, 208.0, 1408.0, 119.0, 97.0, 111.0, 1296.0, 112.0, 1176.0, 120.0, 1083.0, 93.0, 311.0, 772.0, 156.0, 155.0, 90.0, 682.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0075854156, 0.0014729941, 0.011433447, 0.009094208, -0.013424319, -0.0013380036, 0.021821266, 0.0065440806, -0.014458634, 0.019843303, -0.06874752, 0.05300714, -0.014411757, -0.014569622, 0.0025225205, -0.00421483, 0.1407475, -0.06016399, 0.028919041, 0.053973053, -0.013866412, 0.028169055, 0.054748338, 0.006157319, -0.019698907, -0.0079174405, 0.100480914, -0.0025493396, 0.011624503, 0.009822997, 0.0010772552, -0.0065503116, 0.0064736903, 0.020893795, 0.0005227306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, 25, 27, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3493598, 2.0231335, 0.0, 4.040264, 0.0, 1.9917251, 0.0, 1.6742064, 0.0, 1.6142975, 1.8150014, 3.6249292, 1.3857542, 0.0, 0.0, 3.4187913, 3.4544802, 3.0853024, 2.7769976, 1.5093026, 0.0, 0.0, 0.33844835, 0.96130496, 0.0, 0.0, 2.23148, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, 26, 28, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3441783, 1.6290085, 0.011433447, 1.0008775, -0.013424319, 0.99087244, 0.021821266, 2.0, -0.014458634, 1.0, 4.0, 0.5273946, 0.03846154, -0.014569622, 0.0025225205, 0.45160067, 0.6672731, 0.512418, 0.27374282, 1.0, -0.013866412, 0.028169055, 0.7513225, 1.265656, -0.019698907, -0.0079174405, 0.6923077, -0.0025493396, 0.011624503, 0.009822997, 0.0010772552, -0.0065503116, 0.0064736903, 0.020893795, 0.0005227306], "split_indices": [140, 138, 0, 139, 0, 140, 0, 0, 0, 106, 0, 143, 1, 0, 0, 140, 143, 140, 140, 111, 0, 0, 142, 138, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1956.0, 112.0, 1852.0, 104.0, 1764.0, 88.0, 1672.0, 92.0, 1421.0, 251.0, 722.0, 699.0, 138.0, 113.0, 437.0, 285.0, 340.0, 359.0, 305.0, 132.0, 108.0, 177.0, 229.0, 111.0, 143.0, 216.0, 134.0, 171.0, 89.0, 88.0, 103.0, 126.0, 101.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002988965, 0.010988297, -0.0031864026, -0.010515367, 0.004856243, -0.0064124577, 0.06869841, 0.008559203, -0.118906714, -0.0038796135, 0.024507299, -0.0048308726, 0.019310942, -0.02318392, -0.0007215216, -0.019187324, 0.05421581, -0.030462291, 0.008688937, -0.004554301, 0.012391035, -0.003989398, -0.014507613, -0.05178926, 0.059053138, -0.008543747, 0.00094905525, -0.0004421491, 0.022594152], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, 19, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [1.3657674, 0.0, 1.6040897, 0.0, 1.3043054, 2.5953925, 5.156932, 3.3607528, 2.2830608, 0.0, 0.0, 1.0748847, 0.0, 0.0, 0.0, 1.219931, 1.7242563, 2.7974944, 0.0, 0.0, 0.0, 2.2570553, 0.0, 0.8783932, 3.4215972, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, 20, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010988297, -0.5, -0.010515367, 1.0, 0.9037621, 0.7037037, 1.5082182, -0.115384616, -0.0038796135, 0.024507299, 1.0, 0.019310942, -0.02318392, -0.0007215216, 0.6897765, 0.31486017, 0.53829104, 0.008688937, -0.004554301, 0.012391035, 0.42307693, -0.014507613, 0.41356456, 0.38274837, -0.008543747, 0.00094905525, -0.0004421491, 0.022594152], "split_indices": [1, 0, 1, 0, 42, 141, 142, 138, 1, 0, 0, 61, 0, 0, 0, 142, 141, 140, 0, 0, 0, 1, 0, 142, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 113.0, 1956.0, 143.0, 1813.0, 1541.0, 272.0, 1360.0, 181.0, 169.0, 103.0, 1268.0, 92.0, 90.0, 91.0, 1020.0, 248.0, 922.0, 98.0, 102.0, 146.0, 749.0, 173.0, 426.0, 323.0, 275.0, 151.0, 234.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0067136185, -0.0014177803, -0.010116414, 0.0061838087, -0.008074261, -0.0030886193, 0.01700618, 0.0077078664, -0.010282447, 0.02417464, -0.042528994, -0.002575842, 0.07588766, -0.0076953927, -0.012401471, 0.026951406, -0.010844185, 0.1267785, -0.0061759925, 0.0064669945, -0.009737896, 0.00026489794, 0.012994806, 0.0011340579, 0.02578396, 0.030735647, -0.010102211, -0.00041813753, 0.012504764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, -1, 23, -1, -1, -1, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [1.0268995, 1.1722221, 0.0, 2.6956768, 0.0, 1.8079418, 0.0, 1.253267, 0.0, 1.5784004, 1.0615765, 2.350701, 2.7249463, 1.7003752, 0.0, 1.6161888, 0.0, 4.296753, 0.0, 0.0, 0.0, 1.4412977, 0.0, 0.0, 0.0, 1.1822209, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, -1, 24, -1, -1, -1, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0, 1.638525, -0.010116414, 1.5618256, -0.008074261, 1.4902518, 0.01700618, 1.0, -0.010282447, 0.56519663, 0.36752534, 0.50989574, 0.81781596, 1.0, -0.012401471, 0.43978718, -0.010844185, 0.6374897, -0.0061759925, 0.0064669945, -0.009737896, 1.3136374, 0.012994806, 0.0011340579, 0.02578396, 1.0, -0.010102211, -0.00041813753, 0.012504764], "split_indices": [43, 138, 0, 138, 0, 138, 0, 80, 0, 140, 140, 141, 142, 116, 0, 140, 0, 142, 0, 0, 0, 138, 0, 0, 0, 62, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1944.0, 109.0, 1774.0, 170.0, 1679.0, 95.0, 1515.0, 164.0, 1141.0, 374.0, 752.0, 389.0, 262.0, 112.0, 588.0, 164.0, 284.0, 105.0, 145.0, 117.0, 467.0, 121.0, 151.0, 133.0, 359.0, 108.0, 262.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0036912484, 0.011905773, -0.061384838, -0.002174257, 0.025043806, 0.0066482476, -0.024443703, 0.00727977, -0.016113838, 0.014458289, -0.010750173, -0.010580271, 0.05201613, 0.01213531, -0.013214782, 0.023914171, 0.020293882, -0.018221138, 0.017023172, -0.081264116, 0.07789395, 0.0034220312, -0.010413297, -0.0149428565, -0.0002255302, -0.00057006674, 0.13939881, -0.019774009, 0.009088603, 0.0045131925, 0.023269385, 0.0036094852, -0.0042007547], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.1017468, 6.146132, 5.406877, 2.5969272, 0.0, 0.0, 0.0, 1.3438803, 0.0, 1.4435053, 0.0, 2.5433211, 3.644731, 3.7242148, 0.0, 0.0, 3.0711176, 1.2104726, 0.0, 1.0232618, 1.722395, 1.0549859, 0.0, 0.0, 0.0, 0.0, 1.6973639, 0.51052874, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3316327, 1.4846051, 1.5727334, 0.025043806, 0.0066482476, -0.024443703, 1.0, -0.016113838, 1.0, -0.010750173, 1.3755236, 0.3598161, 0.42559734, -0.013214782, 0.023914171, 1.3805978, 0.37210763, 0.017023172, 0.54242957, 1.0, 0.33214152, -0.010413297, -0.0149428565, -0.0002255302, -0.00057006674, 1.0, 0.0, 0.009088603, 0.0045131925, 0.023269385, 0.0036094852, -0.0042007547], "split_indices": [119, 139, 138, 138, 0, 0, 0, 117, 0, 71, 0, 138, 139, 139, 0, 0, 138, 141, 0, 142, 69, 140, 0, 0, 0, 0, 126, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1830.0, 231.0, 1728.0, 102.0, 136.0, 95.0, 1631.0, 97.0, 1535.0, 96.0, 921.0, 614.0, 776.0, 145.0, 89.0, 525.0, 651.0, 125.0, 190.0, 335.0, 520.0, 131.0, 102.0, 88.0, 142.0, 193.0, 411.0, 109.0, 96.0, 97.0, 117.0, 294.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0036663753, -0.0021029229, 0.010204548, 0.0076107252, -0.013142918, -0.00018359476, 0.012344113, 0.008813561, -0.012489202, -0.015559666, 0.035694856, -0.03316283, 0.03560465, 0.015311964, 0.020271894, -0.013936882, -0.01566071, 0.010615506, -0.0033639248, 0.10197742, -0.009514841, -0.03663037, 0.008778957, 0.0011207163, 0.019376753, -0.045053046, 0.011651414, 0.0030116558, -0.062785126, -0.08660566, 0.0032160364, -0.00985662, -0.002196107, 0.003027964, -0.015749095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [1.1782931, 2.4634671, 0.0, 1.6467426, 0.0, 1.9175344, 0.0, 1.0443631, 0.0, 0.7529464, 1.3727663, 1.1561916, 1.04543, 0.0, 1.6306068, 0.0, 1.1584152, 0.0, 0.0, 1.491394, 2.1991122, 0.77511305, 0.0, 0.0, 0.0, 1.2288246, 0.0, 0.0, 0.46597207, 2.0630772, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 16, 16, 19, 19, 20, 20, 21, 21, 25, 25, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.3441783, 1.0711201, 0.010204548, 1.0, -0.013142918, 0.87664425, 0.012344113, 0.4074681, -0.012489202, 1.0, 0.2993035, -0.3846154, 0.2707212, 0.015311964, 0.0, -0.013936882, 1.0, 0.010615506, -0.0033639248, 1.0, 0.74455947, 0.1360992, 0.008778957, 0.0011207163, 0.019376753, 1.0, 0.011651414, 0.0030116558, 0.29588273, 0.46872732, 0.0032160364, -0.00985662, -0.002196107, 0.003027964, -0.015749095], "split_indices": [140, 140, 0, 125, 0, 142, 0, 140, 0, 50, 143, 1, 142, 0, 0, 0, 121, 0, 0, 111, 143, 143, 0, 0, 0, 105, 0, 0, 143, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1961.0, 115.0, 1824.0, 137.0, 1709.0, 115.0, 1594.0, 115.0, 836.0, 758.0, 622.0, 214.0, 88.0, 670.0, 88.0, 534.0, 106.0, 108.0, 179.0, 491.0, 444.0, 90.0, 90.0, 89.0, 383.0, 108.0, 125.0, 319.0, 249.0, 134.0, 170.0, 149.0, 94.0, 155.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002875855, 0.009452848, -0.0023173243, -0.0111427745, 0.00640429, -0.0015790276, 0.012219654, -0.014381589, 0.073577695, 0.00036029544, -0.013579088, -0.0069633736, 0.023795953, 0.012450727, -0.008464819, -0.0061558075, 0.01963692, 0.021622121, -0.09785785, 0.0129970405, 0.0062557897, -0.016402833, -0.0042376425, -0.001567079, 0.009911155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, -1, 23, -1, -1, -1, -1], "loss_changes": [0.9852551, 0.0, 1.864222, 0.0, 1.6768734, 1.6328508, 0.0, 2.595213, 5.8147163, 1.3289312, 0.0, 0.0, 0.0, 3.8738017, 0.0, 2.6186175, 0.0, 1.313618, 0.87742376, 0.0, 1.4068826, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 20, 20], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, -1, 24, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009452848, -0.5, -0.0111427745, 1.1789773, 1.0, 0.012219654, 0.8158683, 0.53443044, 1.0, -0.013579088, -0.0069633736, 0.023795953, 0.7011734, -0.008464819, 1.3489369, 0.01963692, 0.10272476, 1.0, 0.0129970405, 0.4186171, -0.016402833, -0.0042376425, -0.001567079, 0.009911155], "split_indices": [1, 0, 1, 0, 139, 42, 0, 139, 143, 64, 0, 0, 0, 139, 0, 138, 0, 139, 71, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 111.0, 1959.0, 145.0, 1814.0, 1697.0, 117.0, 1450.0, 247.0, 1293.0, 157.0, 132.0, 115.0, 1132.0, 161.0, 1028.0, 104.0, 789.0, 239.0, 98.0, 691.0, 109.0, 130.0, 559.0, 132.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0044266144, -0.005084148, 0.05402051, 0.0069558457, -0.014929252, -0.0058452557, 0.11470366, -0.021351274, 0.040083665, -0.00065401505, 0.02262047, 0.008246113, -0.022288261, 0.0077042747, 0.018245772, -0.00829139, 0.013246533, 0.011354052, -0.013547979, 0.06778217, -0.043713674, -0.012431162, 0.011886627, 0.017316934, -0.0011258172, 0.006876561, -0.014323001, 0.01100182, -0.020716285, 0.0074745873, -0.03262408, 0.009423895, -0.07962371, -0.009799977, 0.002433689, 0.0017857648, -0.017613994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, -1, 17, -1, 19, -1, -1, 21, 23, 25, -1, 27, -1, -1, 29, -1, -1, 31, -1, 33, -1, 35, -1, -1, -1, -1], "loss_changes": [0.9702372, 2.996799, 2.2591481, 1.4938407, 0.0, 0.0, 2.9065452, 5.123765, 3.3837285, 0.0, 0.0, 1.538653, 0.0, 1.3450572, 0.0, 1.7811962, 0.0, 0.0, 1.4029806, 1.7492666, 2.2705848, 0.0, 1.2957472, 0.0, 0.0, 0.8015836, 0.0, 0.0, 2.0586019, 0.0, 0.70381033, 0.0, 1.8911158, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 18, 18, 19, 19, 20, 20, 22, 22, 25, 25, 28, 28, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, -1, 18, -1, 20, -1, -1, 22, 24, 26, -1, 28, -1, -1, 30, -1, -1, 32, -1, 34, -1, 36, -1, -1, -1, -1], "split_conditions": [0.900655, 0.82173043, 1.0008775, 1.0, -0.014929252, -0.0058452557, -0.42307693, 0.66016495, 0.6913856, -0.00065401505, 0.02262047, 0.52685916, -0.022288261, 1.2154769, 0.018245772, -0.07692308, 0.013246533, 0.011354052, 0.2845349, 1.0, 0.33421066, -0.012431162, 0.37210763, 0.017316934, -0.0011258172, 1.0, -0.014323001, 0.01100182, 1.0, 0.0074745873, 1.2154769, 0.009423895, 0.5028171, -0.009799977, 0.002433689, 0.0017857648, -0.017613994], "split_indices": [142, 141, 139, 39, 0, 0, 1, 139, 139, 0, 0, 139, 0, 138, 0, 1, 0, 0, 139, 122, 141, 0, 141, 0, 0, 23, 0, 0, 53, 0, 138, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1726.0, 331.0, 1593.0, 133.0, 116.0, 215.0, 859.0, 734.0, 103.0, 112.0, 749.0, 110.0, 598.0, 136.0, 661.0, 88.0, 100.0, 498.0, 210.0, 451.0, 93.0, 405.0, 90.0, 120.0, 299.0, 152.0, 101.0, 304.0, 110.0, 189.0, 103.0, 201.0, 88.0, 101.0, 100.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.008316456, -0.014003177, 0.008220494, -0.002006409, -0.01542791, -0.011789951, 0.018268507, 0.028923867, -0.04427484, -0.002174892, 0.022114385, -0.007936933, -0.13014092, 0.041346483, -0.08821946, -0.031186597, 0.011583983, -0.023364775, -0.07471101, 0.0077599892, 0.016683444, -0.0022059646, -0.018595552, -0.056216028, 0.009422964, 0.0052435263, -0.020609554, 0.0654272, -0.00842434, -0.027673166, -0.01568088, -0.0055043283, 0.018026825, 0.017919961, -0.007830276, 0.008638601, -0.004705289], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [1.062485, 3.268109, 0.0, 3.2326093, 0.0, 2.2470698, 0.0, 4.507264, 2.9485826, 2.4303613, 0.0, 1.9108379, 1.6122012, 1.8165356, 1.4096308, 1.7547548, 0.0, 0.0, 3.057025, 1.8038967, 0.0, 0.0, 0.0, 1.3379813, 0.0, 0.0, 0.0, 2.8915062, 0.0, 0.83793545, 0.0, 0.0, 0.0, 0.8496511, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 23, 23, 27, 27, 29, 29, 33, 33], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.2066275, 1.0711201, 0.008220494, 0.92508495, -0.01542791, -0.03846154, 0.018268507, -0.15384616, 0.53139174, -0.30769232, 0.022114385, 3.1538463, 0.5028171, 0.7585821, 1.3659338, 0.3940456, 0.011583983, -0.023364775, 0.71545964, 0.50745165, 0.016683444, -0.0022059646, -0.018595552, 1.2969342, 0.009422964, 0.0052435263, -0.020609554, 0.3451843, -0.00842434, 1.0, -0.01568088, -0.0055043283, 0.018026825, 1.0, -0.007830276, 0.008638601, -0.004705289], "split_indices": [141, 140, 0, 140, 0, 1, 0, 1, 143, 1, 0, 1, 139, 139, 138, 143, 0, 0, 139, 141, 0, 0, 0, 138, 0, 0, 0, 141, 0, 80, 0, 0, 0, 23, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1942.0, 122.0, 1789.0, 153.0, 1699.0, 90.0, 754.0, 945.0, 649.0, 105.0, 664.0, 281.0, 431.0, 218.0, 559.0, 105.0, 98.0, 183.0, 340.0, 91.0, 130.0, 88.0, 466.0, 93.0, 93.0, 90.0, 209.0, 131.0, 363.0, 103.0, 102.0, 107.0, 191.0, 172.0, 93.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0028955766, -0.0018766606, 0.010917172, 0.00901963, -0.019863855, -0.00083597394, 0.016591022, 0.006380186, -0.009987779, -0.011080047, 0.046664946, 0.008383239, -0.08801038, 0.12958911, -0.030473797, -0.003843464, 0.012073308, -0.0021119143, -0.014428394, 0.026474172, 0.0009335186, -0.0145518705, 0.00606034, -0.020361822, 0.012343479, -0.0021591405, -0.013279012, -0.007638601, 0.0010372665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, 25, -1, 27, -1, -1, -1], "loss_changes": [1.0503597, 4.2493587, 0.0, 2.9038606, 0.0, 1.262878, 0.0, 1.1584692, 0.0, 1.7204176, 3.1855392, 1.259654, 0.87329566, 3.9006305, 2.703316, 1.7387075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.498035, 0.0, 0.58602405, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, 26, -1, 28, -1, -1, -1], "split_conditions": [1.2520225, 1.638525, 0.010917172, 1.0715915, -0.019863855, 0.8870337, 0.016591022, 0.56020635, -0.009987779, 1.3506603, -0.03846154, 3.1538463, 0.4877159, 0.6489657, 1.0, 0.4768662, 0.012073308, -0.0021119143, -0.014428394, 0.026474172, 0.0009335186, -0.0145518705, 0.00606034, 1.3091755, 0.012343479, 0.13875088, -0.013279012, -0.007638601, 0.0010372665], "split_indices": [142, 138, 0, 143, 0, 143, 0, 143, 0, 138, 1, 1, 142, 141, 93, 142, 0, 0, 0, 0, 0, 0, 0, 138, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1982.0, 89.0, 1878.0, 104.0, 1767.0, 111.0, 1647.0, 120.0, 1149.0, 498.0, 917.0, 232.0, 240.0, 258.0, 827.0, 90.0, 106.0, 126.0, 113.0, 127.0, 114.0, 144.0, 732.0, 95.0, 630.0, 102.0, 91.0, 539.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021252926, -0.008359238, 0.0036757675, -0.013764912, 0.11004476, -0.00015819284, -0.01843467, 0.026204208, 0.0007469869, -0.06175137, 0.013388406, 0.0042439695, -0.019497931, 0.030212667, -0.07773588, 0.018282128, 0.013379665, 0.0031123757, -0.020453945, -0.0030178658, 0.014425344, 0.012368608, -0.012450693, -0.021551449, 0.013716093, 0.003166923, -0.007131625], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, 15, 17, -1, 19, -1, -1, 21, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [0.98110837, 0.0, 3.5952766, 3.864562, 4.2563715, 1.2866111, 0.0, 0.0, 0.0, 3.858963, 1.9378368, 0.0, 0.0, 2.740974, 2.719347, 0.0, 2.0623133, 0.0, 0.0, 1.5963726, 0.0, 3.2085862, 0.0, 1.5785158, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 13, 13, 14, 14, 16, 16, 19, 19, 21, 21, 23, 23], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, 16, 18, -1, 20, -1, -1, 22, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0, -0.008359238, 0.900655, 0.8559731, 1.0, -1.0, -0.01843467, 0.026204208, 0.0007469869, 0.43015048, 1.3461539, 0.0042439695, -0.019497931, 0.0, 0.39554682, 0.018282128, 0.78044474, 0.0031123757, -0.020453945, 0.68014574, 0.014425344, 0.5286266, -0.012450693, 1.2604802, 0.013716093, 0.003166923, -0.007131625], "split_indices": [104, 0, 142, 139, 69, 0, 0, 0, 0, 142, 1, 0, 0, 0, 141, 0, 140, 0, 0, 142, 0, 142, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 138.0, 1938.0, 1665.0, 273.0, 1542.0, 123.0, 110.0, 163.0, 278.0, 1264.0, 156.0, 122.0, 1067.0, 197.0, 106.0, 961.0, 106.0, 91.0, 854.0, 107.0, 758.0, 96.0, 596.0, 162.0, 288.0, 308.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056419536, 0.008950405, -0.010954924, -0.012148401, -0.0020856815, 0.0075052115, -0.08370925, 0.013176302, -0.008649926, -0.0028001245, -0.014060249, 0.0005387281, 0.019922221, 0.019404862, -0.06722162, -0.0009227277, 0.014188181, -0.015285014, -0.022945395, 0.00856229, -0.012845882, -0.012096087, 0.0077981413, -0.030052003, 0.006653167, 0.007690006, -0.0051590963], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, -1, 21, -1, 23, -1, -1, 25, -1, -1, -1], "loss_changes": [1.0418514, 0.0, 1.9135634, 0.0, 1.4145974, 0.86203516, 0.60218716, 3.585532, 0.0, 0.0, 0.0, 1.8255206, 0.0, 2.780952, 1.1790962, 0.988557, 0.0, 0.0, 2.0279403, 0.0, 1.1499864, 0.0, 0.0, 1.5941166, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 20, 20, 23, 23], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, -1, 22, -1, 24, -1, -1, 26, -1, -1, -1], "split_conditions": [-0.5769231, 0.008950405, -0.5, -0.012148401, 1.0, 1.234658, 0.27748662, 1.0715915, -0.008649926, -0.0028001245, -0.014060249, 0.6693096, 0.019922221, 0.60352135, 1.0, 0.16720141, 0.014188181, -0.015285014, -0.07692308, 0.00856229, 0.5550927, -0.012096087, 0.0077981413, 1.0, 0.006653167, 0.007690006, -0.0051590963], "split_indices": [1, 0, 1, 0, 40, 139, 143, 143, 0, 0, 0, 142, 0, 139, 93, 141, 0, 0, 1, 0, 143, 0, 0, 89, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 109.0, 1952.0, 145.0, 1807.0, 1617.0, 190.0, 1525.0, 92.0, 96.0, 94.0, 1428.0, 97.0, 1117.0, 311.0, 958.0, 159.0, 106.0, 205.0, 116.0, 842.0, 104.0, 101.0, 692.0, 150.0, 116.0, 576.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.6929958E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}