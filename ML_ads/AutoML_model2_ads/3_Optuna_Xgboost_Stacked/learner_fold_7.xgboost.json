{"learner": {"attributes": {"best_iteration": "12", "best_score": "0.780173"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "63"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.0012107533, -0.1442213, 0.4633094, -0.2560876, -0.0063690646, 0.27115685, 0.65086865, -0.29023215, -0.00780482, -0.11366424, 0.07476957, 0.039286595, 0.0146430135, 0.049839273, 0.0786148, -0.04246551, -0.26457557, -0.020958167, -0.0022665646, 0.027382357, 0.012321243, -0.22774695, -0.3242253, 0.017060688, -0.08840595, -0.2712543, -0.010469577, -0.04310016, -0.0217449, -0.016909039, 0.00023640125, -0.016839908, -0.32577318, -0.024901282, -0.040684592], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, -1, -1, -1, -1, -1, 21, -1, -1, -1, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [139.24661, 24.303532, 17.875832, 5.2887955, 6.146284, 3.7191887, 5.17733, 2.5176468, 0.0, 2.6534176, 4.997096, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3466492, 0.0, 0.0, 0.0, 4.8787622, 2.029026, 2.6678734, 0.0, 1.3695372, 1.5701141, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1388378, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 16, 16, 20, 20, 21, 21, 22, 22, 24, 24, 25, 25, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, -1, -1, -1, -1, -1, 22, -1, -1, -1, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [0.7612845, 0.40845418, 1.0152625, 0.450694, 0.48619443, 1.0, 1.069748, 0.13875088, -0.00780482, 1.0, 1.0, 0.039286595, 0.0146430135, 0.049839273, 0.0786148, -0.04246551, 1.2634896, -0.020958167, -0.0022665646, 0.027382357, 1.0, 0.26845157, 1.0, 0.017060688, 0.70742667, 1.177135, -0.010469577, -0.04310016, -0.0217449, -0.016909039, 0.00023640125, -0.016839908, 1.0, -0.024901282, -0.040684592], "split_indices": [139, 140, 139, 142, 139, 108, 142, 142, 0, 108, 17, 0, 0, 0, 0, 0, 138, 0, 0, 0, 71, 139, 13, 0, 142, 138, 0, 0, 0, 0, 0, 0, 111, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1576.0, 496.0, 870.0, 706.0, 245.0, 251.0, 730.0, 140.0, 304.0, 402.0, 124.0, 121.0, 118.0, 133.0, 117.0, 613.0, 148.0, 156.0, 96.0, 306.0, 379.0, 234.0, 119.0, 187.0, 280.0, 99.0, 117.0, 117.0, 99.0, 88.0, 97.0, 183.0, 94.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0064049372, -0.1251579, 0.42772886, -0.22809999, 0.00088768674, 0.013154285, 0.56004864, -0.31362474, -0.16469072, -0.10237798, 0.069082, 0.03254841, 0.68422985, -0.3501559, -0.019951621, 0.003278641, -0.20857453, -0.021040153, -0.00032330654, 0.032267325, 0.002665235, 0.087935835, 0.048006754, -0.023104152, -0.40624675, -0.26579532, -0.00408009, -0.06342129, 0.017031632, -0.035808932, -0.04519218, -0.038220596, -0.19910175, 0.0015812576, -0.01873296, -0.004151219, -0.033815134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [113.90981, 20.319471, 19.16452, 4.674679, 4.95766, 0.0, 9.845436, 1.5298424, 4.2896957, 2.998795, 7.141309, 0.0, 8.804161, 1.8573799, 0.0, 0.0, 3.8880558, 0.0, 0.0, 0.0, 3.7227046, 0.0, 0.0, 0.0, 0.41572, 2.344677, 0.0, 2.3660746, 0.0, 0.0, 0.0, 0.0, 4.207249, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 16, 16, 20, 20, 24, 24, 25, 25, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [0.7612845, 0.40845418, 0.8000281, 0.2585062, 0.4696581, 0.013154285, 1.0054001, 0.2659622, 0.20979661, 1.0, 0.48662078, 0.03254841, 1.1391696, 1.177135, -0.019951621, 0.003278641, 0.46736735, -0.021040153, -0.00032330654, 0.032267325, 0.78735083, 0.087935835, 0.048006754, -0.023104152, 0.17694144, 0.2724345, -0.00408009, 1.400257, 0.017031632, -0.035808932, -0.04519218, -0.038220596, 0.31718948, 0.0015812576, -0.01873296, -0.004151219, -0.033815134], "split_indices": [139, 140, 142, 141, 139, 0, 139, 143, 143, 108, 141, 0, 143, 138, 0, 0, 142, 0, 0, 0, 140, 0, 0, 0, 141, 139, 0, 138, 0, 0, 0, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1566.0, 489.0, 862.0, 704.0, 151.0, 338.0, 367.0, 495.0, 280.0, 424.0, 117.0, 221.0, 278.0, 89.0, 90.0, 405.0, 134.0, 146.0, 88.0, 336.0, 113.0, 108.0, 89.0, 189.0, 302.0, 103.0, 241.0, 95.0, 92.0, 97.0, 110.0, 192.0, 147.0, 94.0, 90.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-2.418909e-05, -0.119816035, 0.3783164, -0.20739977, 0.008275157, 0.28623107, 0.06858013, -0.27747858, -0.160971, -0.011841497, 0.048077196, 0.010684766, 0.40504345, -0.018204382, -0.31688154, -0.06350912, -0.27135393, 0.16651833, -0.025751103, 0.05582661, 0.02633125, -0.03590437, -0.024063079, 0.0047517945, -0.014437278, -0.0355575, -0.012869375, 0.036079124, -0.0029843508, -0.016828012, 0.011869103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [94.043396, 17.68068, 14.129105, 3.0454369, 3.227216, 8.1841755, 0.0, 1.4026337, 6.0568266, 0.0, 4.2584786, 0.0, 5.016487, 0.0, 0.8487301, 2.6844387, 3.171959, 7.1336384, 6.1761584, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.7612845, 0.42835203, 1.3215449, 0.22530004, 0.40629318, 0.80708444, 0.06858013, 1.0, 1.2624887, -0.011841497, 0.55228823, 0.010684766, 1.0279931, -0.018204382, 0.23886508, 0.29891056, 0.43508187, 0.527192, -0.115384616, 0.05582661, 0.02633125, -0.03590437, -0.024063079, 0.0047517945, -0.014437278, -0.0355575, -0.012869375, 0.036079124, -0.0029843508, -0.016828012, 0.011869103], "split_indices": [139, 139, 139, 142, 140, 142, 0, 53, 138, 0, 141, 0, 142, 0, 141, 143, 142, 140, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1576.0, 499.0, 936.0, 640.0, 384.0, 115.0, 373.0, 563.0, 153.0, 487.0, 153.0, 231.0, 109.0, 264.0, 299.0, 264.0, 187.0, 300.0, 111.0, 120.0, 170.0, 94.0, 126.0, 173.0, 166.0, 98.0, 94.0, 93.0, 151.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.005655748, -0.109227024, 0.33180684, -0.16996925, 0.024331925, 0.20199454, 0.49287024, -0.084589124, -0.2134917, 0.074910074, -0.013820979, 0.00750013, 0.029177954, 0.03680485, 0.06676207, 0.0047740927, -0.16561943, -0.18356417, -0.039844375, -0.0072776214, 0.12206606, -0.010081879, -0.022139044, -0.13525444, -0.26562738, 0.024047574, 0.00061232166, -0.20883185, -0.00180211, -0.0316344, -0.018436553, -0.01107801, -0.028183386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, 21, 23, -1, -1, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [72.034904, 12.793678, 10.119476, 4.0280914, 4.0529823, 3.0557575, 4.7115326, 3.9245245, 3.9742432, 2.618574, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.8203764, 2.4500294, 0.0, 0.0, 3.9126945, 0.0, 0.0, 3.3554068, 0.9437809, 0.0, 0.0, 1.7107544, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 16, 16, 17, 17, 20, 20, 23, 23, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, 22, 24, -1, -1, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.7612845, 0.48619443, 0.9782575, -0.03846154, 1.0, 1.0, 1.3215449, 1.0, 1.0, -1.0, -0.013820979, 0.00750013, 0.029177954, 0.03680485, 0.06676207, 0.0047740927, 1.0, 1.0, -0.039844375, -0.0072776214, 1.0, -0.010081879, -0.022139044, 1.0, 1.1923077, 0.024047574, 0.00061232166, 1.0, -0.00180211, -0.0316344, -0.018436553, -0.01107801, -0.028183386], "split_indices": [139, 139, 141, 1, 0, 39, 139, 122, 64, 0, 0, 0, 0, 0, 0, 0, 97, 80, 0, 0, 53, 0, 0, 39, 1, 0, 0, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1577.0, 484.0, 1084.0, 493.0, 268.0, 216.0, 366.0, 718.0, 376.0, 117.0, 111.0, 157.0, 126.0, 90.0, 139.0, 227.0, 618.0, 100.0, 91.0, 285.0, 105.0, 122.0, 389.0, 229.0, 141.0, 144.0, 239.0, 150.0, 141.0, 88.0, 102.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00081457524, -0.09400282, 0.30288938, -0.16359006, 0.013694384, 0.21830639, 0.0582381, -0.18549204, -0.0010641118, 0.015536827, -0.03569084, 0.0074493, 0.3120977, -0.004670034, -0.20177694, -0.024181271, 0.02544699, 0.04468177, 0.018860437, -0.2693715, -0.17851858, 0.10616936, -0.009794289, -0.020726662, -0.033278383, -0.122349754, -0.23080029, 0.0016670525, 0.023431538, -0.021171957, -0.0026792756, -0.1826086, -0.033735737, -0.027286906, -0.008769554], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [59.34611, 11.818592, 11.70192, 3.2091885, 4.3308935, 5.1256084, 0.0, 1.8940544, 0.0, 0.0, 5.7842464, 0.0, 3.8265114, 0.0, 1.179102, 0.0, 3.5259545, 0.0, 0.0, 0.75613594, 1.6386223, 2.4543483, 0.0, 0.0, 0.0, 2.297236, 1.4840612, 0.0, 0.0, 0.0, 0.0, 1.7048111, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 12, 12, 14, 14, 16, 16, 19, 19, 20, 20, 21, 21, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.7612845, 0.44131443, 1.3215449, 0.50696194, 0.5149149, 0.80708444, 0.0582381, 1.1550102, -0.0010641118, 0.015536827, 1.0, 0.0074493, 1.0279931, -0.004670034, 0.18195362, -0.024181271, 1.0, 0.04468177, 0.018860437, 0.18567689, 1.2695129, 0.7707342, -0.009794289, -0.020726662, -0.033278383, 1.0, 1.0, 0.0016670525, 0.023431538, -0.021171957, -0.0026792756, 1.0, -0.033735737, -0.027286906, -0.008769554], "split_indices": [139, 140, 139, 142, 140, 142, 0, 138, 0, 0, 2, 0, 142, 0, 139, 0, 122, 0, 0, 140, 138, 143, 0, 0, 0, 17, 83, 0, 0, 0, 0, 13, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1577.0, 495.0, 958.0, 619.0, 380.0, 115.0, 838.0, 120.0, 160.0, 459.0, 150.0, 230.0, 88.0, 750.0, 105.0, 354.0, 110.0, 120.0, 192.0, 558.0, 214.0, 140.0, 97.0, 95.0, 269.0, 289.0, 126.0, 88.0, 139.0, 130.0, 199.0, 90.0, 102.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005283387, -0.08457708, 0.28873104, -0.13734518, 0.043213468, 0.16419947, 0.4428598, -0.15711072, 0.0063077398, 0.022304552, -0.012631805, 0.026573196, 0.006556788, 0.06676047, 0.018569974, -0.13543424, -0.02870201, -0.015508855, 0.05951366, -0.21086301, -0.07924131, -0.005084531, 0.018724397, -0.16561511, -0.026674142, 0.0066332407, -0.11673758, -0.007893694, -0.023211816, -0.20215696, 0.0032746315, -0.012204896, -0.027976159], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, -1, -1, -1, -1, 19, -1, -1, 21, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [52.800846, 10.613897, 9.577751, 4.413067, 4.6196747, 2.763948, 12.888374, 2.8554, 0.0, 0.0, 3.6074407, 0.0, 0.0, 0.0, 0.0, 3.6833124, 0.0, 0.0, 3.2844117, 0.93802834, 2.7183187, 0.0, 0.0, 1.1816936, 0.0, 0.0, 5.056454, 0.0, 0.0, 1.5666208, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 15, 15, 18, 18, 19, 19, 20, 20, 23, 23, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, -1, -1, -1, -1, 20, -1, -1, 22, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [0.7612845, 0.49834487, 0.9782575, 0.56573635, 0.46698824, 1.0, 1.1391696, 1.3147875, 0.0063077398, 0.022304552, 0.56480145, 0.026573196, 0.006556788, 0.06676047, 0.018569974, 0.22053023, -0.02870201, -0.015508855, 1.0, 0.16507967, 1.2043438, -0.005084531, 0.018724397, 1.1864421, -0.026674142, 0.0066332407, 1.0, -0.007893694, -0.023211816, 1.0, 0.0032746315, -0.012204896, -0.027976159], "split_indices": [139, 139, 141, 142, 141, 108, 143, 138, 0, 0, 140, 0, 0, 0, 0, 142, 0, 0, 121, 142, 138, 0, 0, 138, 0, 0, 93, 0, 0, 108, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1574.0, 499.0, 1114.0, 460.0, 276.0, 223.0, 1014.0, 100.0, 109.0, 351.0, 136.0, 140.0, 119.0, 104.0, 869.0, 145.0, 118.0, 233.0, 371.0, 498.0, 125.0, 108.0, 205.0, 166.0, 102.0, 396.0, 89.0, 116.0, 252.0, 144.0, 124.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002756785, -0.07729201, 0.26025528, -0.13686983, -0.0041277455, 0.008546155, 0.3375976, -0.20511135, -0.0851004, 0.028703503, -0.018996503, 0.01968514, 0.42191255, -0.014797281, -0.25071228, -0.01834354, -0.1255459, -0.042934693, 0.16880383, 0.028322926, 0.052421987, -0.019564947, -0.031620804, -0.012931789, 0.010524151, -0.021307454, -0.0014027928, 0.012023154, -0.09008339, 0.036407307, 0.0019380389, -0.0254582, -0.015593434, 0.010908427, -0.011303065], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [42.50286, 6.856656, 6.6107674, 3.0629578, 4.307497, 0.0, 4.0229187, 0.97448254, 1.3311071, 6.021921, 0.0, 0.0, 3.0079155, 0.0, 0.75012493, 2.5509477, 2.9966311, 3.0541506, 5.9230914, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.7740757, 0.0, 0.0, 0.0, 2.5754285, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [0.7612845, 0.40845418, 0.8000281, 0.2585062, 2.0, 0.008546155, 1.0152625, 1.0, 0.28194562, 0.5954426, -0.018996503, 0.01968514, 1.0, -0.014797281, 0.17694144, 0.29183525, 0.43177223, 0.38183376, 0.66602075, 0.028322926, 0.052421987, -0.019564947, -0.031620804, -0.012931789, 0.010524151, -0.021307454, -0.0014027928, 0.012023154, 0.46374416, 0.036407307, 0.0019380389, -0.0254582, 1.0, 0.010908427, -0.011303065], "split_indices": [139, 140, 142, 141, 0, 0, 139, 23, 142, 139, 0, 0, 113, 0, 141, 140, 142, 141, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0, 141, 0, 0, 0, 126, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1573.0, 489.0, 867.0, 706.0, 150.0, 339.0, 374.0, 493.0, 600.0, 106.0, 127.0, 212.0, 166.0, 208.0, 186.0, 307.0, 397.0, 203.0, 90.0, 122.0, 113.0, 95.0, 98.0, 88.0, 172.0, 135.0, 89.0, 308.0, 88.0, 115.0, 96.0, 212.0, 93.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00067668845, -0.07693178, 0.20253502, -0.12597123, 0.0120555265, 0.036350377, 0.29889417, -0.026694795, -0.11118234, 0.014526292, -0.050156705, 0.020528091, -0.00961727, 0.41580728, 0.011335816, -0.07621682, -0.16022985, -0.13270567, 0.009843142, 0.02199792, 0.06488427, 0.0026999903, -0.123991415, -0.23055522, -0.0081318235, -0.023370145, 0.0020416905, -0.16983911, -0.0015543931, -0.014459846, -0.032492075, -0.00887857, -0.023327222], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, -1, 17, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [32.030087, 6.5589247, 9.031561, 2.020257, 4.4253273, 4.6341496, 7.7438984, 0.0, 1.5040302, 0.0, 4.4647503, 0.0, 0.0, 9.994034, 0.0, 2.5247428, 2.0255613, 3.6187482, 0.0, 0.0, 0.0, 0.0, 1.7402234, 1.5654907, 0.0, 0.0, 0.0, 1.2648005, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 13, 13, 15, 15, 16, 16, 17, 17, 22, 22, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, -1, 18, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.6985078, 0.44131443, 0.80976486, 0.10190928, 0.5149149, 1.0, 1.1391696, -0.026694795, 1.0, 0.014526292, 0.67553604, 0.020528091, -0.00961727, 1.0109537, 0.011335816, 0.23995012, 1.0, 0.5871468, 0.009843142, 0.02199792, 0.06488427, 0.0026999903, 0.490939, 0.26820892, -0.0081318235, -0.023370145, 0.0020416905, 1.2718703, -0.0015543931, -0.014459846, -0.032492075, -0.00887857, -0.023327222], "split_indices": [140, 140, 139, 140, 140, 93, 143, 0, 23, 0, 139, 0, 0, 141, 0, 139, 111, 142, 0, 0, 0, 0, 142, 140, 0, 0, 0, 138, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1503.0, 564.0, 969.0, 534.0, 207.0, 357.0, 92.0, 877.0, 170.0, 364.0, 91.0, 116.0, 219.0, 138.0, 512.0, 365.0, 234.0, 130.0, 119.0, 100.0, 162.0, 350.0, 193.0, 172.0, 141.0, 93.0, 246.0, 104.0, 101.0, 92.0, 108.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004688007, -0.03969872, 0.30766106, -0.108647525, 0.035656475, 0.043461934, 0.014567973, -0.13953318, 0.007996738, 0.016259328, 0.007127288, -0.18312933, -0.07317834, 0.01150588, -0.007843866, -0.01630285, 0.033714134, -0.16105111, -0.027119681, 0.0048075113, -0.1372605, 0.07021703, -0.01261879, -0.20298359, -0.0076481365, -0.02199981, -0.00603145, 0.024323277, 0.021360245, -0.013727552, -0.028465807, -0.021427406, 0.09936333, 0.008484373, 0.02547918, 0.017860731, -0.015978938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, 25, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1, 33, 35, -1, -1, -1], "loss_changes": [27.783531, 9.362562, 5.429125, 3.3900785, 3.1180282, 0.0, 0.0, 2.1522532, 1.8230288, 0.0, 3.1803055, 0.8730211, 2.2922041, 0.0, 0.0, 0.0, 3.5488274, 1.2730923, 0.0, 0.0, 1.2287004, 4.184231, 0.0, 1.2880001, 0.0, 0.0, 0.0, 0.0, 7.0947576, 0.0, 0.0, 0.0, 4.0963006, 5.238783, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 16, 16, 17, 17, 20, 20, 21, 21, 23, 23, 28, 28, 32, 32, 33, 33], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, 26, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1, 34, 36, -1, -1, -1], "split_conditions": [1.0054001, 0.42835203, 1.1391696, 1.0, 1.3252649, 0.043461934, 0.014567973, 1.0, 0.34896147, 0.016259328, 0.52332455, 0.3789014, 1.2043438, 0.01150588, -0.007843866, -0.01630285, 0.95005274, 0.26353046, -0.027119681, 0.0048075113, 0.2859163, 1.0, -0.01261879, 0.16507967, -0.0076481365, -0.02199981, -0.00603145, 0.024323277, 1.0, -0.013727552, -0.028465807, -0.021427406, 0.76655036, 0.63934857, 0.02547918, 0.017860731, -0.015978938], "split_indices": [139, 139, 143, 71, 138, 0, 0, 83, 143, 0, 139, 140, 138, 0, 0, 0, 143, 141, 0, 0, 139, 89, 0, 142, 0, 0, 0, 0, 81, 0, 0, 0, 139, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1802.0, 264.0, 941.0, 861.0, 148.0, 116.0, 744.0, 197.0, 158.0, 703.0, 449.0, 295.0, 88.0, 109.0, 95.0, 608.0, 359.0, 90.0, 102.0, 193.0, 495.0, 113.0, 240.0, 119.0, 93.0, 100.0, 109.0, 386.0, 133.0, 107.0, 96.0, 290.0, 183.0, 107.0, 91.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027098118, -0.039922357, 0.26804864, -0.09835725, 0.045088187, 0.013176744, 0.03958783, -0.13555545, -0.05583445, -0.003228831, 0.124638714, 0.0037153258, -0.17191519, -0.08833111, 0.009328908, 0.04472857, -0.015997851, 0.021966534, -0.0029002433, -0.2282222, -0.0075388844, 0.0017233478, -0.13682368, -0.032138467, 0.016737675, -0.030809602, -0.18888135, -0.0037007933, -0.021693058, -0.02250313, 0.010198231, -0.024983605, -0.013639258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, -1, 23, -1, -1, -1, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [20.84644, 9.036032, 4.355194, 1.7051487, 2.8481407, 0.0, 0.0, 3.610795, 2.4375467, 3.4654794, 4.0880003, 0.0, 2.5816774, 2.114188, 0.0, 3.3279438, 0.0, 0.0, 0.0, 0.94268894, 0.0, 0.0, 2.2628474, 5.613994, 0.0, 0.0, 0.6430855, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 19, 19, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, -1, 24, -1, -1, -1, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0152625, 0.48619443, 1.0, 1.0, 1.0, 0.013176744, 0.03958783, 1.177135, 1.0, 1.0, 1.0, 0.0037153258, 0.32674077, 1.2179879, 0.009328908, 0.70742667, -0.015997851, 0.021966534, -0.0029002433, 1.0, -0.0075388844, 0.0017233478, 1.0, 1.0, 0.016737675, -0.030809602, 1.223285, -0.0037007933, -0.021693058, -0.02250313, 0.010198231, -0.024983605, -0.013639258], "split_indices": [139, 139, 113, 17, 97, 0, 0, 138, 71, 113, 12, 0, 139, 138, 0, 142, 0, 0, 0, 53, 0, 0, 97, 69, 0, 0, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1819.0, 250.0, 1078.0, 741.0, 121.0, 129.0, 575.0, 503.0, 461.0, 280.0, 100.0, 475.0, 413.0, 90.0, 353.0, 108.0, 173.0, 107.0, 300.0, 175.0, 130.0, 283.0, 217.0, 136.0, 99.0, 201.0, 126.0, 157.0, 89.0, 128.0, 93.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032681494, -0.039728872, 0.24846448, -0.099440545, 0.024238117, 0.03521488, 0.01147663, -0.11474413, 0.004551351, -0.0386346, 0.06799165, -0.09573427, -0.022191776, 0.031483203, -0.011155713, 0.027455492, 0.016727768, -0.12514505, 0.00056506647, -0.008105689, 0.01391829, -0.06437129, 0.016871763, -0.17203386, -0.058889087, 0.0011660046, -0.01861691, -0.0121824, -0.022132523, 0.0020475476, -0.012102726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [18.934944, 6.882877, 3.618084, 2.0674696, 2.393286, 0.0, 0.0, 1.7174902, 0.0, 1.8254013, 5.4322767, 2.134976, 0.0, 2.2059371, 0.0, 0.0, 5.0660825, 1.7241974, 0.0, 0.0, 0.0, 2.4818006, 0.0, 0.80434513, 1.1342611, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0054001, 0.42835203, 1.1391696, 2.3076923, 0.5894446, 0.03521488, 0.01147663, 1.1923077, 0.004551351, 0.54340225, 0.6673955, 1.0, -0.022191776, 0.42036474, -0.011155713, 0.027455492, 1.0, 1.0, 0.00056506647, -0.008105689, 0.01391829, -0.03846154, 0.016871763, 0.20378265, 0.26353046, 0.0011660046, -0.01861691, -0.0121824, -0.022132523, 0.0020475476, -0.012102726], "split_indices": [139, 139, 143, 1, 143, 0, 0, 1, 0, 139, 143, 71, 0, 140, 0, 0, 97, 83, 0, 0, 0, 1, 0, 143, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1802.0, 261.0, 932.0, 870.0, 147.0, 114.0, 843.0, 89.0, 357.0, 513.0, 716.0, 127.0, 182.0, 175.0, 102.0, 411.0, 555.0, 161.0, 89.0, 93.0, 268.0, 143.0, 325.0, 230.0, 165.0, 103.0, 161.0, 164.0, 101.0, 129.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028156275, -0.059537087, 0.14660431, -0.11055388, -0.01890804, 0.09622932, 0.035543155, -0.1384984, 0.0016722776, 0.035017453, -0.09286301, 0.035758764, 0.03291053, -0.08307624, -0.1968596, -0.040949248, 0.029668057, -0.17020507, 0.0009064576, 0.12303094, -0.02279971, -0.01913572, -0.022244222, -0.01446288, -0.025765283, 0.0051461905, -0.115416504, -0.005018579, -0.02713324, -0.002519685, 0.029734684, 0.0049716462, -0.009584041, -0.00027918693, -0.02176129], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.433727, 3.0904903, 5.954132, 2.3509693, 3.3100886, 6.4214554, 0.0, 1.7530994, 0.0, 9.541289, 2.7591517, 8.332714, 0.0, 1.8311713, 0.83827305, 2.5599575, 0.0, 2.415307, 0.0, 7.0280614, 0.0, 0.0, 0.942694, 0.0, 0.0, 0.0, 2.3710248, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 22, 22, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6985078, 0.3314175, 1.3215449, 0.3898465, 0.51125157, 1.0, 0.035543155, 0.20574999, 0.0016722776, 0.49834487, 1.0, 0.9858656, 0.03291053, 0.13875088, 0.2782904, 1.2624887, 0.029668057, 1.0, 0.0009064576, 0.7922069, -0.02279971, -0.01913572, 1.2119944, -0.01446288, -0.025765283, 0.0051461905, 0.39554682, -0.005018579, -0.02713324, -0.002519685, 0.029734684, 0.0049716462, -0.009584041, -0.00027918693, -0.02176129], "split_indices": [140, 140, 139, 142, 141, 125, 0, 143, 0, 139, 105, 143, 0, 142, 141, 138, 0, 71, 0, 139, 0, 0, 138, 0, 0, 0, 141, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1491.0, 566.0, 661.0, 830.0, 456.0, 110.0, 542.0, 119.0, 480.0, 350.0, 362.0, 94.0, 278.0, 264.0, 372.0, 108.0, 199.0, 151.0, 272.0, 90.0, 100.0, 178.0, 142.0, 122.0, 166.0, 206.0, 91.0, 108.0, 147.0, 125.0, 90.0, 88.0, 98.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.009410951, -0.05973773, 0.124913186, -0.012205062, -0.09125052, -0.004460945, 0.16900428, 0.075274765, -0.08820317, -0.065466866, -0.024716098, 0.034022942, 0.28911477, -0.005653097, 0.018140407, 0.002793576, -0.13506626, -0.019172989, -0.14668708, 0.020114953, -0.014634135, 0.016326237, 0.045162316, -0.021324422, -0.0069497633, -0.10775973, 0.0829532, -0.18831064, -0.0065192482, -0.01979545, -0.0056219855, 0.024375899, -0.0065690787, -0.0071862596, -0.029985556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.939329, 2.2468305, 4.200634, 3.9756844, 3.6259847, 0.0, 7.230853, 3.8887901, 1.7416415, 2.9102385, 0.0, 6.330171, 4.826687, 0.0, 0.0, 0.0, 1.1687322, 4.460184, 0.9531784, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.227237, 5.473746, 2.4159875, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6985078, -0.03846154, 1.0, 1.0, 1.0, -0.004460945, 0.9494613, 1.0, 0.29183525, 1.0, -0.024716098, 0.8035619, -0.1923077, -0.005653097, 0.018140407, 0.002793576, 1.0, 0.3314175, 1.0, 0.020114953, -0.014634135, 0.016326237, 0.045162316, -0.021324422, -0.0069497633, 0.42307693, 1.0, 0.24580406, -0.0065192482, -0.01979545, -0.0056219855, 0.024375899, -0.0065690787, -0.0071862596, -0.029985556], "split_indices": [140, 1, 17, 106, 64, 0, 140, 71, 140, 109, 0, 140, 1, 0, 0, 0, 69, 140, 93, 0, 0, 0, 0, 0, 0, 1, 127, 142, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1500.0, 562.0, 598.0, 902.0, 116.0, 446.0, 278.0, 320.0, 774.0, 128.0, 210.0, 236.0, 124.0, 154.0, 92.0, 228.0, 493.0, 281.0, 109.0, 101.0, 133.0, 103.0, 104.0, 124.0, 264.0, 229.0, 186.0, 95.0, 96.0, 168.0, 110.0, 119.0, 91.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0020271246, -0.01375057, 0.027260078, -0.02819814, 0.017212098, -0.07617012, 0.015527631, -0.061001208, -0.020362189, 0.038957234, -0.016135529, -0.083897516, 0.00879359, 0.071820974, -0.009610471, -0.1060792, 0.00024005478, 0.026906557, 0.18966094, 0.0011907563, -0.13434115, 0.021435259, -0.03764097, 0.004068298, 0.033863887, -0.008481775, -0.16640802, 0.0076654726, -0.11107985, -0.012259367, -0.021950504, -0.020783326, -0.0026625579], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [8.832621, 5.249942, 0.0, 3.8050675, 0.0, 1.6723075, 3.9329376, 2.6360145, 0.0, 3.7195811, 0.0, 1.2825389, 0.0, 3.5672882, 0.0, 1.7773085, 0.0, 5.9044, 4.1281633, 0.0, 0.68286514, 0.0, 3.0469306, 0.0, 0.0, 0.0, 0.6071925, 0.0, 1.8058429, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 20, 20, 22, 22, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.3215449, 1.0, 0.027260078, 0.40845418, 0.017212098, 1.3479978, 0.9858656, 0.43177223, -0.020362189, 1.0, -0.016135529, 0.32674077, 0.00879359, 1.4462762, -0.009610471, 0.08975307, 0.00024005478, 0.48535565, 1.0, 0.0011907563, 1.0, 0.021435259, 1.3360624, 0.004068298, 0.033863887, -0.008481775, 0.216585, 0.0076654726, 0.6174475, -0.012259367, -0.021950504, -0.020783326, -0.0026625579], "split_indices": [139, 125, 0, 140, 0, 138, 143, 142, 0, 7, 0, 139, 0, 138, 0, 139, 0, 140, 13, 0, 124, 0, 138, 0, 0, 0, 142, 0, 139, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1955.0, 114.0, 1814.0, 141.0, 865.0, 949.0, 773.0, 92.0, 838.0, 111.0, 670.0, 103.0, 674.0, 164.0, 533.0, 137.0, 488.0, 186.0, 103.0, 430.0, 125.0, 363.0, 93.0, 93.0, 169.0, 261.0, 142.0, 221.0, 143.0, 118.0, 103.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0031841996, -0.035650916, 0.10637857, -0.019545713, -0.14250115, -0.0066014603, 0.15033692, -0.04788675, 0.04380071, -0.020364525, -0.008892725, 0.010261792, 0.2551218, -0.006490387, -0.09678245, 0.026846796, -0.030102976, 0.016764961, -0.016615089, 0.041412305, 0.0030736835, 0.06174195, -0.075565115, -0.03986861, -0.017685888, 0.008528023, -0.012105211, 0.01870161, -0.007638078, -0.014378892, 0.0038391107, -0.010417583, 0.006683895], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.29566, 2.5881498, 4.289216, 2.3464618, 0.64531755, 0.0, 6.619666, 1.827766, 6.707911, 0.0, 0.0, 5.3586855, 9.204792, 2.3047209, 1.8867867, 0.0, 3.190177, 0.0, 0.0, 0.0, 0.0, 4.2565894, 1.8892105, 1.6606205, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6985078, 1.0, 1.0, 0.49834487, 0.27024493, -0.0066014603, 0.91847384, 1.2615721, 0.46698824, -0.020364525, -0.008892725, 0.79667294, 1.1826671, 1.0, 1.0, 0.026846796, 1.0, 0.016764961, -0.016615089, 0.041412305, 0.0030736835, 1.2179879, 0.2841135, 0.42064157, -0.017685888, 0.008528023, -0.012105211, 0.01870161, -0.007638078, -0.014378892, 0.0038391107, -0.010417583, 0.006683895], "split_indices": [140, 40, 17, 139, 141, 0, 140, 138, 141, 0, 0, 140, 143, 23, 12, 0, 53, 0, 0, 0, 0, 138, 141, 139, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1504.0, 566.0, 1307.0, 197.0, 115.0, 451.0, 903.0, 404.0, 92.0, 105.0, 193.0, 258.0, 489.0, 414.0, 100.0, 304.0, 102.0, 91.0, 151.0, 107.0, 246.0, 243.0, 242.0, 172.0, 134.0, 170.0, 129.0, 117.0, 152.0, 91.0, 151.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002290256, -0.038229425, 0.11063012, -0.028007813, -0.019375177, 0.16271828, -0.0030556223, -0.06700232, 0.021707047, 0.06534377, 0.041725866, -0.031422533, -0.022078339, -0.031211376, 0.025751898, 0.02213264, -0.00653057, 0.029385827, -0.10801974, 0.011344093, -0.06889995, 0.013743535, -0.012955601, -0.04041421, 0.018738776, -0.002569489, -0.019949177, -0.027594313, -0.019240787, -0.009760074, 0.0064211115, -0.014383783, 0.007022032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, 21, 23, 25, -1, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [9.095866, 2.39725, 4.147734, 2.7431283, 0.0, 10.211727, 0.0, 4.3388977, 7.7618117, 3.3409228, 0.0, 2.9995916, 0.0, 2.7694836, 0.0, 0.0, 3.6131403, 3.9592466, 2.1461694, 0.0, 2.0559332, 0.0, 0.0, 1.4898069, 0.0, 0.0, 0.0, 3.433836, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 20, 20, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, 22, 24, 26, -1, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.6985078, 0.74716765, 1.1391696, 1.0, -0.019375177, 1.0355495, -0.0030556223, 1.3507949, 0.6011969, 0.69113594, 0.041725866, 1.0, -0.022078339, 0.21818505, 0.025751898, 0.02213264, 0.86215216, 0.37856585, 1.0, 0.011344093, 0.5052178, 0.013743535, -0.012955601, 1.0, 0.018738776, -0.002569489, -0.019949177, 0.3142033, -0.019240787, -0.009760074, 0.0064211115, -0.014383783, 0.007022032], "split_indices": [140, 141, 143, 39, 0, 139, 0, 138, 139, 141, 0, 12, 0, 143, 0, 0, 140, 141, 106, 0, 140, 0, 0, 83, 0, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1508.0, 564.0, 1415.0, 93.0, 412.0, 152.0, 793.0, 622.0, 298.0, 114.0, 644.0, 149.0, 508.0, 114.0, 94.0, 204.0, 359.0, 285.0, 105.0, 403.0, 94.0, 110.0, 249.0, 110.0, 150.0, 135.0, 302.0, 101.0, 161.0, 88.0, 138.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [2.12877e-05, -0.03263174, 0.11324212, -0.022054868, -0.019075593, 0.035450447, 0.054556668, -0.040967714, 0.083044134, -0.030494595, 0.018803991, -0.027595382, -0.018447263, -0.004640059, 0.02487334, -0.012189049, 0.011019344, -0.041480385, 0.013933666, -0.023149611, -0.014728839, -0.052019365, 0.021127276, -0.10150354, -0.017266322, -0.017540846, -0.05223358, 0.015696509, -0.059462998, -0.010509058, 0.00091487094, -0.017793931, 0.00018824372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, -1, 17, -1, -1, -1, -1, -1, 19, -1, 21, -1, 23, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [7.5973406, 2.6675718, 6.512951, 2.971643, 0.0, 0.0, 4.2005806, 2.4313667, 4.890054, 2.9059782, 0.0, 2.6863902, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0753102, 0.0, 6.1721573, 0.0, 1.3964169, 0.0, 1.2198329, 3.5068972, 0.0, 0.6521416, 0.0, 2.7909055, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, -1, 18, -1, -1, -1, -1, -1, 20, -1, 22, -1, 24, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [0.7927253, 1.0, 0.87417185, 1.4184856, -0.019075593, 0.035450447, 1.1254623, 0.6725299, 1.0, 1.0, 0.018803991, 0.59656245, -0.018447263, -0.004640059, 0.02487334, -0.012189049, 0.011019344, 0.5149149, 0.013933666, 0.4450249, -0.014728839, 0.21497679, 0.021127276, 0.26923078, 0.22037145, -0.017540846, 0.20502685, 0.015696509, 1.0, -0.010509058, 0.00091487094, -0.017793931, 0.00018824372], "split_indices": [143, 84, 143, 138, 0, 0, 142, 143, 109, 111, 0, 143, 0, 0, 0, 0, 0, 140, 0, 140, 0, 142, 0, 1, 143, 0, 141, 0, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1595.0, 460.0, 1495.0, 100.0, 90.0, 370.0, 1267.0, 228.0, 226.0, 144.0, 1159.0, 108.0, 128.0, 100.0, 137.0, 89.0, 1070.0, 89.0, 912.0, 158.0, 812.0, 100.0, 335.0, 477.0, 134.0, 201.0, 93.0, 384.0, 108.0, 93.0, 131.0, 253.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0032546169, -0.015390331, 0.14046039, 0.029038409, -0.049606066, -0.0013189506, 0.028450718, -0.015100064, 0.019227242, -0.023921832, -0.020406088, 0.022368658, -0.011378224, 0.019204063, -0.10068051, 0.105633914, -0.07836198, -0.07591796, 0.100026704, 0.0003247802, -0.14170486, -0.00030444347, 0.029313395, -0.019507622, 0.004660476, -0.0131837, 0.001961037, 0.1891182, -0.0065666805, -0.01961133, -0.006127495, 0.02902258, 0.010563483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [5.303138, 2.7742956, 5.4889297, 5.7206917, 4.0900316, 0.0, 0.0, 2.3109348, 0.0, 2.9262938, 0.0, 3.7994752, 0.0, 4.3514156, 1.3558226, 5.0535455, 2.9900074, 1.3888819, 4.5171366, 0.0, 0.9977422, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.679718, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0152625, -0.03846154, 1.0, -0.1923077, 1.0, -0.0013189506, 0.028450718, 1.0, 0.019227242, 1.0, -0.020406088, 0.61189383, -0.011378224, 0.32555068, 0.23158629, 0.48013937, 0.83975834, 0.24580406, 0.6078314, 0.0003247802, 1.0, -0.00030444347, 0.029313395, -0.019507622, 0.004660476, -0.0131837, 0.001961037, 0.37783793, -0.0065666805, -0.01961133, -0.006127495, 0.02902258, 0.010563483], "split_indices": [139, 1, 113, 1, 64, 0, 0, 7, 0, 109, 0, 141, 0, 140, 140, 143, 141, 142, 143, 0, 93, 0, 0, 0, 0, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1825.0, 248.0, 794.0, 1031.0, 120.0, 128.0, 625.0, 169.0, 884.0, 147.0, 453.0, 172.0, 566.0, 318.0, 248.0, 205.0, 260.0, 306.0, 90.0, 228.0, 157.0, 91.0, 106.0, 99.0, 164.0, 96.0, 199.0, 107.0, 136.0, 92.0, 90.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029161624, -0.02619176, 0.08026969, -0.01402612, -0.016437855, 0.16365768, -0.006119206, -0.032415207, 0.019372497, 0.046783265, 0.03798753, -0.020540325, -0.015695177, 0.016852546, -0.0068551414, -0.03277118, 0.011399907, -0.018655881, -0.013002071, -0.069864064, 0.014414307, 0.0015941085, -0.1081756, 0.016080737, -0.011403071, -0.014460988, -0.0041758907, 0.012012802, -0.03919134, 0.0057285996, -0.008949739], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1], "loss_changes": [4.0118074, 2.72175, 5.343683, 5.6846848, 0.0, 7.202036, 0.0, 2.0215974, 0.0, 2.5976033, 0.0, 2.053624, 0.0, 0.0, 0.0, 1.5703754, 0.0, 1.6917708, 0.0, 1.2886326, 2.294148, 0.0, 0.6557772, 0.0, 1.8859906, 0.0, 0.0, 0.0, 2.0675457, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22, 24, 24, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1], "split_conditions": [0.8032599, 1.4857019, 1.1391696, 1.4184856, -0.016437855, 0.9292002, -0.006119206, 0.6725299, 0.019372497, 1.4228944, 0.03798753, 0.59656245, -0.015695177, 0.016852546, -0.0068551414, 1.3507949, 0.011399907, 0.22530004, -0.013002071, 1.0, 0.26170823, 0.0015941085, 1.223285, 0.016080737, 1.0, -0.014460988, -0.0041758907, 0.012012802, 0.29891056, 0.0057285996, -0.008949739], "split_indices": [143, 138, 143, 138, 0, 140, 0, 143, 0, 138, 0, 143, 0, 0, 0, 138, 0, 142, 0, 53, 142, 0, 138, 0, 89, 0, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1619.0, 453.0, 1488.0, 131.0, 285.0, 168.0, 1367.0, 121.0, 185.0, 100.0, 1248.0, 119.0, 90.0, 95.0, 1144.0, 104.0, 999.0, 145.0, 392.0, 607.0, 121.0, 271.0, 91.0, 516.0, 175.0, 96.0, 90.0, 426.0, 146.0, 280.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0007173182, -0.009934159, 0.020805202, -0.048177704, 0.020801019, -0.03585353, -0.014490376, -0.011727314, 0.043175515, 0.006481576, -0.07940453, 0.0027458698, 0.14216867, -0.034241155, 0.010058875, -0.017208891, -0.04347254, 0.03977821, -0.015912129, 0.029524034, 0.0012005661, -0.010155525, 0.0031620096, -0.009701106, 0.0017536447, -0.058182374, 0.09527034, 0.0018266374, -0.013945949, 0.013838456, 0.01825536, -0.005791446, 0.008803753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [4.564807, 2.3108804, 0.0, 1.0442517, 3.3673801, 1.4325833, 0.0, 0.0, 3.7541182, 1.5099269, 1.2755172, 3.992216, 5.4194007, 1.2191826, 0.0, 0.0, 0.9015076, 2.9463348, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2178531, 2.4592428, 0.0, 0.0, 0.9529962, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.2553585, 0.40845418, 0.020805202, 0.5273946, -0.5, 1.0, -0.014490376, -0.011727314, 0.7927253, 0.31108832, 1.0, 2.0, 0.95005274, 0.2585062, 0.010058875, -0.017208891, 1.0, 1.0, -0.015912129, 0.029524034, 0.0012005661, -0.010155525, 0.0031620096, -0.009701106, 0.0017536447, 0.55228823, 0.5598305, 0.0018266374, -0.013945949, 0.7307692, 0.01825536, -0.005791446, 0.008803753], "split_indices": [142, 140, 0, 143, 1, 23, 0, 0, 143, 142, 69, 0, 143, 141, 0, 0, 111, 16, 0, 0, 0, 0, 0, 0, 0, 141, 143, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1966.0, 101.0, 876.0, 1090.0, 777.0, 99.0, 152.0, 938.0, 394.0, 383.0, 666.0, 272.0, 275.0, 119.0, 107.0, 276.0, 542.0, 124.0, 125.0, 147.0, 136.0, 139.0, 147.0, 129.0, 196.0, 346.0, 101.0, 95.0, 179.0, 167.0, 91.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0011897169, -0.010922642, 0.020699453, -0.00042510105, -0.10147038, -0.027811762, 0.037254777, -0.0041147834, -0.016360259, -0.007741697, -0.021754624, 0.08091615, -0.101915844, -0.024953341, 0.014118428, 0.13546805, -0.0084701795, -0.0027320397, -0.017651126, -0.006556948, -0.01548102, 0.22311804, 0.018762797, -0.03746822, 0.010250733, 0.0100491075, 0.035972875, -0.005405811, 0.009572129, -0.009012141, -0.017705275, -0.05058668, 0.014315064, -0.009504202, 0.0014156968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, 19, -1, 21, -1, -1, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [5.1575656, 1.8573327, 0.0, 1.8069024, 0.76083755, 3.8612945, 4.4782925, 0.0, 0.0, 2.3505101, 0.0, 5.0685077, 0.9793477, 1.9636736, 0.0, 4.3167276, 0.0, 0.0, 0.0, 2.427347, 0.0, 4.0372677, 1.0143579, 2.2283072, 0.0, 0.0, 0.0, 0.0, 0.0, 2.9479613, 0.0, 1.0534215, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 29, 29, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, 20, -1, 22, -1, -1, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [1.3215449, 1.0, 0.020699453, 0.55677325, 0.3084008, 0.6031522, 0.46153846, -0.0041147834, -0.016360259, 0.48662078, -0.021754624, 1.0279931, 0.70520204, 0.43301028, 0.014118428, 1.0, -0.0084701795, -0.0027320397, -0.017651126, 0.36517957, -0.01548102, 1.0, 0.6705351, 0.2981182, 0.010250733, 0.0100491075, 0.035972875, -0.005405811, 0.009572129, 0.3314175, -0.017705275, 1.0, 0.014315064, -0.009504202, 0.0014156968], "split_indices": [139, 40, 0, 143, 140, 141, 1, 0, 0, 141, 0, 142, 143, 141, 0, 106, 0, 0, 0, 142, 0, 108, 142, 142, 0, 0, 0, 0, 0, 140, 0, 109, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1954.0, 115.0, 1751.0, 203.0, 1014.0, 737.0, 103.0, 100.0, 917.0, 97.0, 561.0, 176.0, 822.0, 95.0, 422.0, 139.0, 88.0, 88.0, 720.0, 102.0, 241.0, 181.0, 561.0, 159.0, 127.0, 114.0, 93.0, 88.0, 466.0, 95.0, 366.0, 100.0, 217.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001543088, -0.009586618, 0.016935147, 0.00036682334, -0.09270744, -0.09707952, 0.012193754, -0.004204414, -0.014848821, 0.0034643605, -0.024051141, -0.00956175, 0.122592285, 0.010752537, -0.080204874, 0.023621628, -0.002718476, -0.028114634, 0.09870845, 0.00029285575, -0.019562982, 0.015716894, -0.05488796, -0.0036933569, 0.20043999, 0.016554914, -0.10497781, 0.024450803, 0.015637191, 0.012964504, -0.010302396, -0.07018344, -0.020412302, -0.016188076, -0.00080846203], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [3.8735235, 1.6091732, 0.0, 2.001877, 0.5878154, 3.5519407, 3.720351, 0.0, 0.0, 0.0, 0.0, 1.856974, 4.3396587, 3.4356909, 2.77315, 0.0, 0.0, 3.4575787, 4.2501144, 0.0, 0.0, 0.0, 2.1793451, 0.0, 0.34178925, 3.3943205, 1.2349899, 0.0, 0.0, 0.0, 0.0, 1.5089877, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 24, 24, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.1772171, 1.0, 0.016935147, -0.5, 0.29724786, 0.6406223, 0.7927253, -0.004204414, -0.014848821, 0.0034643605, -0.024051141, 1.0, 0.9858656, 0.49834487, 0.4564035, 0.023621628, -0.002718476, 1.177135, 1.0, 0.00029285575, -0.019562982, 0.015716894, 1.0, -0.0036933569, 1.400257, 1.0, 0.45255446, 0.024450803, 0.015637191, 0.012964504, -0.010302396, 0.25659388, -0.020412302, -0.016188076, -0.00080846203], "split_indices": [142, 40, 0, 1, 143, 141, 143, 0, 0, 0, 0, 0, 143, 139, 143, 0, 0, 138, 59, 0, 0, 0, 126, 0, 138, 12, 140, 0, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1945.0, 129.0, 1737.0, 208.0, 188.0, 1549.0, 109.0, 99.0, 98.0, 90.0, 1294.0, 255.0, 1005.0, 289.0, 145.0, 110.0, 697.0, 308.0, 168.0, 121.0, 88.0, 609.0, 132.0, 176.0, 251.0, 358.0, 88.0, 88.0, 129.0, 122.0, 265.0, 93.0, 107.0, 158.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0019983207, -0.009550593, 0.11987258, -0.022748852, 0.014819133, 0.02718669, -0.0042368504, -0.005478556, -0.14533566, -0.016187403, 0.016866188, -0.0257206, -0.0047203745, -0.006226938, -0.014460969, 0.006234405, -0.01200913, -0.015679322, 0.016276104, -0.02987467, 0.013573768, -0.050121725, 0.00676793, -0.0074656256, 0.00464925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [2.8070414, 3.909843, 4.5373898, 3.668952, 0.0, 0.0, 0.0, 2.8326967, 2.3493013, 1.8304572, 0.0, 0.0, 0.0, 1.8843033, 0.0, 4.1058087, 0.0, 2.2568877, 0.0, 1.8961732, 0.0, 1.884456, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.0219027, 1.0, 0.74716765, 0.014819133, 0.02718669, -0.0042368504, 0.78044474, 1.4485375, 1.0, 0.016866188, -0.0257206, -0.0047203745, 0.62855196, -0.014460969, 0.5973865, -0.01200913, 3.1538463, 0.016276104, 0.47138909, 0.013573768, 1.0, 0.00676793, -0.0074656256, 0.00464925], "split_indices": [125, 141, 15, 141, 0, 0, 0, 140, 138, 43, 0, 0, 0, 140, 0, 139, 0, 1, 0, 141, 0, 62, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1878.0, 184.0, 1733.0, 145.0, 95.0, 89.0, 1519.0, 214.0, 1431.0, 88.0, 100.0, 114.0, 1328.0, 103.0, 1197.0, 131.0, 1050.0, 147.0, 960.0, 90.0, 795.0, 165.0, 634.0, 161.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0023184349, -0.011136088, 0.08373203, 0.001133723, -0.022624124, -0.0016636357, 0.022523504, -0.013510058, 0.018850213, 0.0025167274, -0.10967078, -0.02711623, 0.0830196, 0.00021567028, -0.0274927, -0.005247975, -0.016022732, 0.018785799, 0.019367708, 0.010700221, -0.01888585, -0.008817187, 0.012129655, -0.01123133, 0.004509999, 0.015262701, -0.020997426, -0.0047205174, 0.008944948], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, -1, 21, -1, 23, -1, -1, -1, 25, -1, 27, -1, -1], "loss_changes": [2.2707253, 4.6953144, 4.1755147, 4.6177855, 0.0, 0.0, 0.0, 2.4057314, 0.0, 3.1918502, 4.1210823, 2.8468668, 2.4023383, 0.0, 0.0, 1.2859174, 0.0, 0.0, 2.4553502, 0.0, 1.6371756, 0.0, 0.0, 0.0, 2.2630725, 0.0, 1.4791226, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 18, 18, 20, 20, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, -1, 22, -1, 24, -1, -1, -1, 26, -1, 28, -1, -1], "split_conditions": [0.9442745, 0.97098887, 1.0, 0.78044474, -0.022624124, -0.0016636357, 0.022523504, 2.0, 0.018850213, 0.5341993, 0.51125157, 0.4540588, 0.621171, 0.00021567028, -0.0274927, 0.08975307, -0.016022732, 0.018785799, 1.0, 0.010700221, 0.18195362, -0.008817187, 0.012129655, -0.01123133, 1.2063513, 0.015262701, 0.4593882, -0.0047205174, 0.008944948], "split_indices": [142, 140, 0, 140, 0, 0, 0, 0, 0, 142, 141, 142, 142, 0, 0, 139, 0, 0, 59, 0, 139, 0, 0, 0, 138, 0, 139, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1779.0, 294.0, 1683.0, 96.0, 172.0, 122.0, 1561.0, 122.0, 1338.0, 223.0, 978.0, 360.0, 133.0, 90.0, 840.0, 138.0, 136.0, 224.0, 91.0, 749.0, 109.0, 115.0, 150.0, 599.0, 88.0, 511.0, 413.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.000987197, -0.010816631, 0.010912585, -0.018747786, 0.014349601, -0.010050039, -0.017393386, 0.0063504307, -0.078402005, 0.07069517, -0.032543395, -0.0052554724, -0.017303212, -0.02573143, 0.16453563, 0.0049525574, -0.15319869, -0.009901947, 0.008167154, 0.011973941, -0.01960691, 0.005736905, 0.03398991, -0.04622528, 0.015416989, -0.021766286, -0.006968826, -0.0054388084, -0.013198144, 0.007839072, -0.06709123, -0.011808541, -0.00085637794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [2.218815, 2.3033369, 0.0, 2.4160867, 0.0, 1.9001026, 0.0, 3.4210722, 2.270371, 4.6600895, 3.854521, 1.5078658, 0.0, 6.293908, 4.9049993, 4.9638033, 1.0874524, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.692882, 0.0, 0.0, 0.0, 1.6952002, 0.0, 0.0, 0.5640813, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.1740773, 1.0, 0.010912585, 1.0, 0.014349601, 1.0, -0.017393386, -0.03846154, 0.42914736, 0.59656245, 0.63015807, 1.0, -0.017303212, 1.3147875, 1.0, 0.42914736, 0.68609506, -0.009901947, 0.008167154, 0.011973941, -0.01960691, 0.005736905, 0.03398991, 1.2662113, 0.015416989, -0.021766286, -0.006968826, 1.0, -0.013198144, 0.007839072, 1.0, -0.011808541, -0.00085637794], "split_indices": [139, 102, 0, 117, 0, 7, 0, 1, 140, 143, 143, 69, 0, 138, 111, 140, 142, 0, 0, 0, 0, 0, 0, 138, 0, 0, 0, 23, 0, 0, 111, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1882.0, 168.0, 1790.0, 92.0, 1695.0, 95.0, 1367.0, 328.0, 515.0, 852.0, 185.0, 143.0, 254.0, 261.0, 650.0, 202.0, 89.0, 96.0, 137.0, 117.0, 162.0, 99.0, 484.0, 166.0, 114.0, 88.0, 328.0, 156.0, 139.0, 189.0, 101.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.009872554, -0.020160671, 0.08367402, -0.010219972, -0.020124534, 0.029212316, -0.008603678, -0.024237465, 0.062367108, -0.01666991, -0.012443478, 0.017235486, -0.009650407, -0.035412826, 0.053891648, -0.06182598, 0.020516446, 0.017353049, -0.002404449, -0.038851332, -0.021209888, 0.09415637, -0.0076044486, 0.02370248, -0.091850825, -0.0024694328, 0.020830013, 0.08459813, -0.007244853, -0.1495188, 0.00032352828, 0.018102508, -0.0009709502, -0.020025795, -0.010658566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.9912431, 3.355401, 7.252095, 1.7979028, 0.0, 0.0, 0.0, 1.122966, 4.9975314, 1.8211229, 0.0, 0.0, 0.0, 1.6072681, 2.6946914, 2.551372, 2.4816484, 0.0, 0.0, 2.1251204, 0.0, 2.686082, 0.0, 1.7214226, 1.9027491, 0.0, 0.0, 1.6368843, 0.0, 0.47053146, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 24, 24, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.069748, 1.0906444, 1.1391696, 1.4327465, -0.020124534, 0.029212316, -0.008603678, 0.74716765, 1.0, 0.5275764, -0.012443478, 0.017235486, -0.009650407, 1.0, 1.3287823, 0.4759349, 0.35269818, 0.017353049, -0.002404449, 1.0, -0.021209888, 1.0, -0.0076044486, 0.27870917, 0.27905032, -0.0024694328, 0.020830013, 1.2190228, -0.007244853, 1.0, 0.00032352828, 0.018102508, -0.0009709502, -0.020025795, -0.010658566], "split_indices": [142, 140, 143, 138, 0, 0, 0, 141, 50, 142, 0, 0, 0, 93, 138, 140, 143, 0, 0, 106, 0, 106, 0, 143, 142, 0, 0, 138, 0, 124, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1864.0, 205.0, 1767.0, 97.0, 92.0, 113.0, 1481.0, 286.0, 1377.0, 104.0, 169.0, 117.0, 1088.0, 289.0, 739.0, 349.0, 114.0, 175.0, 641.0, 98.0, 198.0, 151.0, 294.0, 347.0, 97.0, 101.0, 180.0, 114.0, 216.0, 131.0, 89.0, 91.0, 99.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025835466, 0.009356009, -0.012897653, -0.0007079065, 0.10960162, -0.012446701, 0.013091129, 0.023120025, -0.00161424, 0.0039094347, -0.124816515, -0.0075061647, 0.016420852, -0.02583291, -0.0018927198, -0.038752552, 0.030258505, -0.009657234, -0.018665373, -0.008962596, 0.01639538, 0.042832658, -0.08175426, 0.011257754, -0.045087032, -0.023614254, 0.023993364, 0.0005265279, -0.017956136, -0.095917426, 0.005531348, -0.0067614415, 0.005596047, -0.0017922638, -0.020248454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.8381029, 1.9793903, 0.0, 2.7548249, 2.7369645, 3.0087013, 0.0, 0.0, 0.0, 2.6149423, 2.9406104, 1.5741328, 0.0, 0.0, 0.0, 3.141359, 3.1671817, 2.3084626, 0.0, 2.0503955, 0.0, 4.6231537, 2.1873605, 0.0, 1.8372234, 0.92434335, 0.0, 0.0, 0.0, 1.9864907, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 22, 22, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.012897653, 1.0219027, 0.9782575, 0.74716765, 0.013091129, 0.023120025, -0.00161424, 0.8032599, 0.79351616, 1.0, 0.016420852, -0.02583291, -0.0018927198, 0.50395054, 1.0, 1.0, -0.018665373, 0.0, 0.01639538, 0.39378572, 0.26170823, 0.011257754, 1.0, 1.0, 0.023993364, 0.0005265279, -0.017956136, 0.3453007, 0.005531348, -0.0067614415, 0.005596047, -0.0017922638, -0.020248454], "split_indices": [117, 125, 0, 141, 141, 141, 0, 0, 0, 143, 142, 39, 0, 0, 0, 141, 121, 12, 0, 0, 0, 141, 142, 0, 93, 116, 0, 0, 0, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1962.0, 101.0, 1783.0, 179.0, 1637.0, 146.0, 91.0, 88.0, 1429.0, 208.0, 1334.0, 95.0, 92.0, 116.0, 730.0, 604.0, 610.0, 120.0, 467.0, 137.0, 353.0, 257.0, 107.0, 360.0, 264.0, 89.0, 136.0, 121.0, 239.0, 121.0, 170.0, 94.0, 138.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0030975025, -0.004852881, 0.013715744, -0.01503777, 0.012048359, -0.022600539, 0.0127740605, -0.006882725, -0.09749749, -0.027801463, 0.0891458, 0.003532714, -0.020456223, 0.0071223387, -0.041754086, -0.002354534, 0.026558146, -0.02996656, -0.011726257, -0.06555316, 0.006931984, -0.017374402, -0.030127823, 0.010437892, -0.05521341, -0.007038129, 0.0045262123, 0.0028677469, -0.010286742], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, -1, 17, -1, -1, 19, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1], "loss_changes": [2.208396, 2.4969084, 0.0, 1.9533575, 0.0, 2.0224574, 0.0, 2.8524895, 4.237809, 1.6110115, 5.0502157, 0.0, 0.0, 0.0, 0.909639, 0.0, 0.0, 1.1607751, 0.0, 1.7247132, 2.6282513, 0.0, 1.0287658, 0.0, 1.0594004, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 17, 17, 19, 19, 20, 20, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, -1, 18, -1, -1, 20, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1], "split_conditions": [1.3215449, 1.0, 0.013715744, 1.0, 0.012048359, 0.743426, 0.0127740605, 0.5973865, 1.0, 1.177135, 1.0, 0.003532714, -0.020456223, 0.0071223387, 1.3479978, -0.002354534, 0.026558146, 1.0, -0.011726257, 1.2179879, 0.31487146, -0.017374402, 1.0, 0.010437892, 0.34244996, -0.007038129, 0.0045262123, 0.0028677469, -0.010286742], "split_indices": [139, 125, 0, 114, 0, 140, 0, 139, 50, 138, 50, 0, 0, 0, 138, 0, 0, 17, 0, 138, 140, 0, 93, 0, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1956.0, 116.0, 1809.0, 147.0, 1718.0, 91.0, 1420.0, 298.0, 1166.0, 254.0, 133.0, 165.0, 144.0, 1022.0, 155.0, 99.0, 884.0, 138.0, 450.0, 434.0, 111.0, 339.0, 169.0, 265.0, 221.0, 118.0, 96.0, 169.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.005087984, 0.006382084, -0.10700147, 0.01438549, -0.012852144, -0.00088346977, -0.018124523, -0.0029375036, 0.16032706, 0.0060797515, -0.0141108185, 0.0017045898, 0.031316027, -0.022565203, 0.055321544, 0.062131193, -0.04233519, 0.0259575, -0.0004276319, -0.0012991057, 0.013725348, -0.07161425, 0.00984851, -0.1190079, 0.08753559, -0.006684143, -0.12918897, 0.010375706, -0.010484304, 0.00017156491, -0.023579484, -0.0055241208, 0.020867957, 0.010274916, -0.008833825, -0.02108345, -0.005005559], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, 23, -1, -1, 25, 27, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.4150608, 2.0049796, 1.523248, 4.4318385, 0.0, 0.0, 0.0, 1.9523574, 4.0730495, 2.0748885, 0.0, 0.0, 0.0, 1.5572351, 6.160348, 0.99323094, 1.1520289, 0.0, 4.4330487, 0.0, 0.0, 1.8056147, 2.91881, 2.5519075, 4.220358, 2.028399, 1.6539865, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, 24, -1, -1, 26, 28, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.6874348, 0.07692308, 0.9494613, -0.012852144, -0.00088346977, -0.018124523, 0.9036089, 0.94488615, 0.49834487, -0.0141108185, 0.0017045898, 0.031316027, 0.16985749, 1.0, 0.20502685, 1.0, 0.0259575, 1.0, -0.0012991057, 0.013725348, 1.0, 0.40983817, 0.6517612, -0.1923077, 0.07692308, 1.0, 0.010375706, -0.010484304, 0.00017156491, -0.023579484, -0.0055241208, 0.020867957, 0.010274916, -0.008833825, -0.02108345, -0.005005559], "split_indices": [40, 138, 1, 140, 0, 0, 0, 141, 139, 139, 0, 0, 0, 143, 17, 141, 93, 0, 59, 0, 0, 39, 142, 141, 1, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1857.0, 209.0, 1753.0, 104.0, 90.0, 119.0, 1567.0, 186.0, 1471.0, 96.0, 96.0, 90.0, 930.0, 541.0, 176.0, 754.0, 116.0, 425.0, 88.0, 88.0, 483.0, 271.0, 181.0, 244.0, 227.0, 256.0, 149.0, 122.0, 89.0, 92.0, 112.0, 132.0, 97.0, 130.0, 126.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.000586996, 0.0090325875, -0.011364681, -0.0007682846, 0.020890433, 0.033002764, -0.033333227, -0.025974551, 0.105726674, -0.008639919, -0.12936273, 0.042737484, -0.13152193, 0.17563586, -0.011798269, -0.043098193, 0.015632566, -0.02204284, -0.003334781, 0.011363487, -0.0046421327, -0.026488854, -0.0018477816, 0.03643625, 0.067093596, -0.016401028, -0.014730322, -0.010679627, 0.024279485, -0.05725102, 0.006404587, 0.0009679286, -0.08932823, -0.0013133697, -0.015817063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, 33, -1, -1], "loss_changes": [2.2469575, 3.729779, 0.0, 1.9960502, 0.0, 3.8215537, 2.1910682, 3.5681686, 6.2400966, 4.1780543, 1.6525521, 1.883696, 2.924803, 6.2273827, 0.0, 1.6914427, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.896665, 1.5905476, 0.0, 0.0, 0.0, 0.68916714, 0.0, 0.0, 1.1382545, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 24, 24, 25, 25, 29, 29, 32, 32], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, 34, -1, -1], "split_conditions": [1.1391696, 1.1328944, -0.011364681, 1.0, 0.020890433, 0.51852506, 0.5861091, 0.32604873, 1.4904531, 1.0, 0.68187356, 1.0, 1.0, 1.0, -0.011798269, 0.4564035, 0.015632566, -0.02204284, -0.003334781, 0.011363487, -0.0046421327, -0.026488854, -0.0018477816, 0.03643625, 1.0, 0.27905032, -0.014730322, -0.010679627, 0.024279485, 1.0, 0.006404587, 0.0009679286, 0.16225934, -0.0013133697, -0.015817063], "split_indices": [143, 139, 0, 122, 0, 143, 141, 143, 138, 121, 140, 80, 53, 126, 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 108, 142, 0, 0, 0, 97, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1904.0, 162.0, 1815.0, 89.0, 891.0, 924.0, 492.0, 399.0, 735.0, 189.0, 298.0, 194.0, 304.0, 95.0, 608.0, 127.0, 97.0, 92.0, 166.0, 132.0, 89.0, 105.0, 111.0, 193.0, 484.0, 124.0, 97.0, 96.0, 321.0, 163.0, 104.0, 217.0, 103.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0019615653, -0.0040719016, 0.012418595, 0.0037685852, -0.015344121, -0.0047380747, 0.012231742, 0.0060888794, -0.01685459, -0.006002377, 0.018581428, -0.033853613, 0.026628995, 0.002488459, -0.110789105, -0.07528672, 0.07015981, -0.025058256, 0.014476274, -0.0017919288, -0.017077953, -0.016864212, 0.00072327483, 0.027717799, 0.01656543, 0.023653572, -0.09315542, -0.0064763203, 0.088773236, -0.011039979, 0.010566271, -0.017520644, -0.0019070491, 0.016321545, -0.0011046993], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5205951, 2.3012671, 0.0, 1.882785, 0.0, 3.0895069, 0.0, 3.5508554, 0.0, 1.3914096, 0.0, 2.309492, 3.1277142, 2.1986654, 1.4763935, 1.625468, 2.0021706, 1.5590549, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9310921, 0.0, 3.0122461, 1.1914332, 0.0, 1.5307535, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 23, 23, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2553585, 1.1962737, 0.012418595, 1.0, -0.015344121, 0.95005274, 0.012231742, 0.8032599, -0.01685459, 1.0, 0.018581428, 1.3211896, -0.1923077, 1.2868673, 0.4813653, 0.450694, 1.0, 0.29351673, 0.014476274, -0.0017919288, -0.017077953, -0.016864212, 0.00072327483, 1.0, 0.01656543, 1.0, 1.2377071, -0.0064763203, 0.44499752, -0.011039979, 0.010566271, -0.017520644, -0.0019070491, 0.016321545, -0.0011046993], "split_indices": [142, 140, 0, 125, 0, 143, 0, 143, 0, 39, 0, 138, 1, 138, 142, 142, 71, 141, 0, 0, 0, 0, 0, 115, 0, 26, 138, 0, 141, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1965.0, 97.0, 1867.0, 98.0, 1742.0, 125.0, 1634.0, 108.0, 1531.0, 103.0, 826.0, 705.0, 561.0, 265.0, 211.0, 494.0, 470.0, 91.0, 104.0, 161.0, 99.0, 112.0, 342.0, 152.0, 274.0, 196.0, 136.0, 206.0, 104.0, 170.0, 93.0, 103.0, 118.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.008712454, -0.016127083, 0.012058014, -0.025403952, 0.010218563, -0.009620859, -0.021674534, -0.024936344, 0.07674145, -0.004910332, -0.13082609, -0.00272739, 0.02515758, -0.029880872, 0.0723102, -0.00066476775, -0.028178802, 0.01197192, -0.04726333, 0.015477903, -0.0051040594, -0.06451535, 0.0046253554, -0.029026361, -0.120840676, -0.007367798, 0.0072486275, -0.005800298, -0.020225644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [1.9796258, 2.1435566, 0.0, 5.4691453, 0.0, 2.2128446, 0.0, 3.0133, 4.582735, 2.3042448, 4.2366433, 0.0, 0.0, 2.3481765, 2.9703984, 0.0, 0.0, 0.0, 1.3052044, 0.0, 0.0, 1.365268, 0.0, 1.8992028, 1.3506179, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 18, 18, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.3215449, 1.0, 0.012058014, 0.95005274, 0.010218563, 1.0, -0.021674534, 2.0, 0.5380342, 0.56573635, 0.53793645, -0.00272739, 0.02515758, 0.10037874, 0.69113594, -0.00066476775, -0.028178802, 0.01197192, 0.49834487, 0.015477903, -0.0051040594, 0.7307692, 0.0046253554, 1.0, 1.0, -0.007367798, 0.0072486275, -0.005800298, -0.020225644], "split_indices": [139, 125, 0, 143, 0, 42, 0, 0, 143, 142, 142, 0, 0, 139, 141, 0, 0, 0, 139, 0, 0, 1, 0, 71, 50, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1953.0, 112.0, 1811.0, 142.0, 1673.0, 138.0, 1421.0, 252.0, 1195.0, 226.0, 158.0, 94.0, 903.0, 292.0, 124.0, 102.0, 94.0, 809.0, 175.0, 117.0, 683.0, 126.0, 419.0, 264.0, 291.0, 128.0, 149.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0038746092, 0.009660203, -0.010782743, -0.022722103, 0.033507194, 0.00018976397, -0.01146473, -0.0126884505, 0.07568925, -0.014549366, 0.009632912, -0.06547335, 0.0097128395, 0.18895744, 0.0052877404, -0.03806261, 0.0064964863, 0.0013225414, -0.02586787, 0.039643854, 0.0034552868, 0.0149605945, -0.07461374, -0.060059942, 0.002934203, 0.008425548, -0.015057563, -0.014177116, 0.003604332, -0.012193621, -0.0071213427, 0.0029155498, -0.004182096], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [1.3254844, 1.50583, 0.0, 1.7418095, 2.1883087, 0.9380613, 0.0, 3.1070156, 4.6808853, 1.0731719, 0.0, 4.671728, 0.0, 7.208107, 4.174309, 0.656846, 0.0, 3.3886898, 0.0, 0.0, 0.0, 0.0, 1.7315265, 1.0940642, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2265827, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 22, 22, 23, 23, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 0.40629318, -0.010782743, 1.3147875, -0.1923077, 1.0, -0.01146473, 1.0, 0.115384616, 1.0, 0.009632912, 1.0, 0.0097128395, 1.0, 0.5381734, 1.2652541, 0.0064964863, -0.34615386, -0.02586787, 0.039643854, 0.0034552868, 0.0149605945, 0.8088062, 0.34615386, 0.002934203, 0.008425548, -0.015057563, -0.014177116, 0.003604332, -0.012193621, 0.16372892, 0.0029155498, -0.004182096], "split_indices": [117, 140, 0, 138, 1, 113, 0, 61, 1, 62, 0, 113, 0, 69, 140, 138, 0, 1, 0, 0, 0, 0, 140, 1, 0, 0, 0, 0, 0, 0, 142, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 1950.0, 101.0, 827.0, 1123.0, 662.0, 165.0, 536.0, 587.0, 574.0, 88.0, 362.0, 174.0, 225.0, 362.0, 443.0, 131.0, 269.0, 93.0, 96.0, 129.0, 129.0, 233.0, 334.0, 109.0, 174.0, 95.0, 145.0, 88.0, 154.0, 180.0, 88.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0037759051, -0.0031192296, 0.012165275, 0.0037490092, -0.013264454, -0.00872247, 0.01624853, 0.0042218636, -0.019406702, -0.004433863, 0.014455517, -0.07847864, 0.01165254, 0.0014768083, -0.019312628, -0.0003116346, 0.016250521, 0.0117209405, -0.01299538, 0.0038249483, -0.09035092, -0.0015883821, 0.013150351, -0.0010452444, -0.017646369], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, -1, 19, 21, 23, -1, -1, -1, -1], "loss_changes": [1.6913888, 1.7489752, 0.0, 3.696056, 0.0, 4.15295, 0.0, 1.9653635, 0.0, 1.8152581, 0.0, 2.9078212, 2.259645, 0.0, 0.0, 1.7291039, 0.0, 0.0, 1.3622994, 2.1640933, 1.2866116, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, -1, 20, 22, 24, -1, -1, -1, -1], "split_conditions": [1.3215449, 1.0928699, 0.012165275, 0.9494613, -0.013264454, 1.4857019, 0.01624853, 1.4435402, -0.019406702, -1.0, 0.014455517, 1.0, 0.8159486, 0.0014768083, -0.019312628, 0.0, 0.016250521, 0.0117209405, 1.3621147, 0.4696581, 0.54883325, -0.0015883821, 0.013150351, -0.0010452444, -0.017646369], "split_indices": [139, 139, 0, 140, 0, 138, 0, 138, 0, 0, 0, 127, 143, 0, 0, 0, 0, 0, 138, 139, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2081.0, 1966.0, 115.0, 1867.0, 99.0, 1731.0, 136.0, 1618.0, 113.0, 1524.0, 94.0, 272.0, 1252.0, 150.0, 122.0, 1160.0, 92.0, 113.0, 1047.0, 860.0, 187.0, 745.0, 115.0, 97.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.006950546, 0.0031280734, -0.012113469, -0.0059374757, 0.018897187, -0.088672206, 0.01808992, -0.14313063, 0.0007278324, 0.00086852117, 0.013844288, -0.0067416294, -0.023793811, 0.022737838, -0.08052923, -0.0040719877, 0.13950953, -7.037703e-05, -0.020745168, 0.012676975, -0.01025089, 0.0034718823, 0.02397441, -0.009074909, 0.015653607, 0.04342693, -0.058088265, -0.003924783, 0.011460389, -0.0016045686, -0.013321988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [2.369536, 3.1875968, 0.0, 3.586172, 0.0, 2.121478, 2.897559, 1.8591747, 0.0, 2.1770782, 0.0, 0.0, 0.0, 3.0179267, 2.6240947, 1.2925937, 1.8906565, 0.0, 0.0, 2.0965686, 0.0, 0.0, 0.0, 1.4976556, 0.0, 1.6535554, 0.9507762, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.1306531, 1.069748, -0.012113469, 1.0, 0.018897187, -0.07692308, 0.8032599, 0.483729, 0.0007278324, 0.61664844, 0.013844288, -0.0067416294, -0.023793811, 0.49834487, 1.458658, 1.3147875, 1.0, -7.037703e-05, -0.020745168, 0.4558188, -0.01025089, 0.0034718823, 0.02397441, 1.0, 0.015653607, 0.21497679, 0.26820892, -0.003924783, 0.011460389, -0.0016045686, -0.013321988], "split_indices": [143, 142, 0, 5, 0, 1, 143, 143, 0, 141, 0, 0, 0, 139, 138, 138, 39, 0, 0, 140, 0, 0, 0, 23, 0, 142, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1892.0, 167.0, 1804.0, 88.0, 406.0, 1398.0, 259.0, 147.0, 1223.0, 175.0, 144.0, 115.0, 964.0, 259.0, 784.0, 180.0, 159.0, 100.0, 670.0, 114.0, 88.0, 92.0, 582.0, 88.0, 281.0, 301.0, 130.0, 151.0, 193.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.005782713, -0.001272109, 0.012733795, 0.0053533916, -0.012111575, 0.0131283365, -0.013629635, -0.0019066227, 0.020353202, 0.008387726, -0.013928609, 0.02466721, -0.055822823, 0.00043294698, 0.09125481, 0.01764452, -0.016306147, 0.017918546, -0.010545982, -0.007878492, 0.15648557, -0.012642944, 0.016331933, -0.0020973412, 0.016625855, 0.022430975, 0.0065368214, 0.0021286458, -0.012050442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, 25, -1, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [1.7665546, 1.5459654, 0.0, 2.031933, 0.0, 5.006883, 0.0, 2.2924705, 0.0, 1.5763345, 0.0, 1.9412823, 2.4029548, 1.6331096, 3.5604749, 3.7988188, 0.0, 2.2476523, 0.0, 0.0, 1.4337502, 0.0, 0.0, 1.8467951, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, 26, -1, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.3215449, 1.0, 0.012733795, 1.0928699, -0.012111575, 0.9494613, -0.013629635, 0.83826023, 0.020353202, 1.0, -0.013928609, 0.5341993, 0.51125157, 0.5149149, -0.3846154, 0.309244, -0.016306147, 0.4389456, -0.010545982, -0.007878492, 0.61912066, -0.012642944, 0.016331933, 0.40145585, 0.016625855, 0.022430975, 0.0065368214, 0.0021286458, -0.012050442], "split_indices": [139, 117, 0, 139, 0, 140, 0, 139, 0, 0, 0, 142, 141, 140, 1, 141, 0, 140, 0, 0, 140, 0, 0, 141, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1947.0, 113.0, 1845.0, 102.0, 1749.0, 96.0, 1621.0, 128.0, 1508.0, 113.0, 1203.0, 305.0, 882.0, 321.0, 181.0, 124.0, 757.0, 125.0, 89.0, 232.0, 91.0, 90.0, 667.0, 90.0, 133.0, 99.0, 557.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0032821847, 0.010468938, -0.00812287, -0.0031755138, 0.017127203, 0.005836068, -0.010482366, -0.00376166, 0.009676522, -0.04305428, 0.022165207, 0.017597485, -0.10894217, -0.0050512995, 0.11638683, -0.010557134, 0.013914569, -0.0003655647, -0.025817437, 0.025727073, -0.013699273, -0.0027449168, 0.024765467, -0.01730282, 0.023584116, -0.05196327, 0.0058516893, 0.001071422, -0.01551968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, 23, -1, -1, -1, 25, -1, 27, -1, -1, -1], "loss_changes": [1.2554106, 4.179704, 0.0, 1.6085148, 0.0, 1.4076867, 0.0, 1.4863337, 0.0, 2.3178067, 2.2540934, 4.521227, 4.3679743, 2.769563, 3.719566, 0.0, 0.0, 0.0, 0.0, 4.999777, 0.0, 0.0, 0.0, 1.206227, 0.0, 2.0381827, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 19, 19, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, 24, -1, -1, -1, 26, -1, 28, -1, -1, -1], "split_conditions": [1.1391696, 0.96802163, -0.00812287, 0.8293257, 0.017127203, 0.6897765, -0.010482366, 1.0, 0.009676522, 0.1923077, 1.2692307, 1.0, 0.39411306, 0.54340225, 0.32674077, -0.010557134, 0.013914569, -0.0003655647, -0.025817437, 0.47138909, -0.013699273, -0.0027449168, 0.024765467, 0.34244996, 0.023584116, 0.27870563, 0.0058516893, 0.001071422, -0.01551968], "split_indices": [143, 141, 0, 142, 0, 142, 0, 69, 0, 1, 1, 97, 139, 139, 139, 0, 0, 0, 0, 141, 0, 0, 0, 142, 0, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1905.0, 162.0, 1756.0, 149.0, 1613.0, 143.0, 1459.0, 154.0, 580.0, 879.0, 302.0, 278.0, 682.0, 197.0, 150.0, 152.0, 163.0, 115.0, 553.0, 129.0, 94.0, 103.0, 459.0, 94.0, 315.0, 144.0, 196.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0016201094, 0.008657893, -0.012590711, -0.00080177106, 0.02095683, 0.0059103086, -0.0115884645, 0.019504549, -0.051946122, 0.068998426, -0.012635069, 0.0012379974, -0.0118694, 0.021898407, 0.027444417, 0.0076394575, -0.013077013, -0.006431126, 0.0074980943, 0.07021406, -0.008898425, 0.03170096, -0.010421896, 0.16119039, -0.011259685, -0.01656001, 0.14492865, 0.005968759, 0.02562339, 0.03090435, -0.012034404, 0.025963856, 0.0037540596, 0.01207552, -0.0032304667], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, 25, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.8533559, 3.7193673, 0.0, 1.4437008, 0.0, 1.3889841, 0.0, 2.2747216, 1.1927758, 5.4478674, 2.0765803, 0.9039198, 0.0, 2.4536746, 0.0, 1.9916964, 0.0, 0.0, 0.0, 5.305438, 0.0, 3.3278675, 0.0, 2.0548491, 0.0, 2.1034207, 2.2419617, 0.0, 0.0, 1.6640599, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 19, 19, 21, 21, 23, 23, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, 26, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.6874348, 1.5950041, -0.012590711, 1.0, 0.02095683, 1.0, -0.0115884645, -0.03846154, 0.42914736, -0.1923077, 1.0, -0.1923077, -0.0118694, 0.86566544, 0.027444417, 0.6725299, -0.013077013, -0.006431126, 0.0074980943, 1.0, -0.008898425, 0.42036474, -0.010421896, 0.45255446, -0.011259685, 1.2662113, 0.55333614, 0.005968759, 0.02562339, 1.0, -0.012034404, 0.025963856, 0.0037540596, 0.01207552, -0.0032304667], "split_indices": [138, 138, 0, 117, 0, 7, 0, 1, 140, 1, 64, 1, 0, 143, 0, 143, 0, 0, 0, 126, 0, 140, 0, 140, 0, 138, 140, 0, 0, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1957.0, 108.0, 1869.0, 88.0, 1766.0, 103.0, 1430.0, 336.0, 563.0, 867.0, 187.0, 149.0, 458.0, 105.0, 740.0, 127.0, 99.0, 88.0, 319.0, 139.0, 609.0, 131.0, 213.0, 106.0, 427.0, 182.0, 103.0, 110.0, 293.0, 134.0, 88.0, 94.0, 121.0, 172.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025691956, 0.009284705, -0.012463426, -0.0048442446, 0.01892796, 0.007338334, -0.013861059, -0.009245903, 0.015740057, -0.052756824, 0.01966436, -0.020004157, -0.0225484, -0.014776264, 0.07883962, 0.068389066, -0.0859141, 0.009056888, -0.040707383, 0.021237403, -0.0039433693, -0.0048782467, 0.02208381, -0.016746765, 0.00033780898, -0.07433509, 0.0033415635, -0.037213743, -0.015577475, -0.00994229, 0.0008439727], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [1.7546005, 4.9616637, 0.0, 2.947981, 0.0, 4.126211, 0.0, 1.878063, 0.0, 3.3717356, 1.8281159, 2.9188223, 0.0, 1.5488837, 5.2118745, 3.8226156, 2.0899622, 0.0, 1.1341274, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.94624496, 0.0, 0.61061406, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.6874348, 1.0152625, -0.012463426, 0.87168705, 0.01892796, 0.7114479, -0.013861059, 1.0, 0.015740057, 0.61189383, 0.42835203, -0.03846154, -0.0225484, 0.13752887, 0.54340225, 0.40983817, 1.0, 0.009056888, 1.0, 0.021237403, -0.0039433693, -0.0048782467, 0.02208381, -0.016746765, 0.00033780898, 0.37182087, 0.0033415635, 1.0, -0.015577475, -0.00994229, 0.0008439727], "split_indices": [138, 139, 0, 142, 0, 142, 0, 69, 0, 141, 139, 1, 0, 139, 139, 142, 62, 0, 93, 0, 0, 0, 0, 0, 0, 142, 0, 53, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1951.0, 103.0, 1809.0, 142.0, 1658.0, 151.0, 1493.0, 165.0, 596.0, 897.0, 501.0, 95.0, 567.0, 330.0, 214.0, 287.0, 112.0, 455.0, 155.0, 175.0, 121.0, 93.0, 150.0, 137.0, 313.0, 142.0, 215.0, 98.0, 91.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-4.1349154e-05, 0.012128676, -0.047318324, 0.02559107, -0.07062715, -0.013118595, -0.016296273, 0.006478492, 0.029520039, 0.0035976823, -0.024385864, -0.055150907, 0.009975466, 0.023120787, -0.018906845, -0.00080283027, -0.010034223, 0.0045735203, 0.019503366, 0.024537768, -0.022013513, 0.008232185, 0.017677864, 0.09806177, -0.03558712, -0.0027298268, 0.019925604, 0.006013143, -0.06592191, -0.01580785, -0.0033333197], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, -1, 17, -1, -1, -1, 19, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, 29, -1, -1], "loss_changes": [1.1944516, 1.8393651, 1.6808778, 7.3171587, 4.265916, 1.556138, 0.0, 4.315267, 0.0, 0.0, 0.0, 0.5089581, 0.0, 3.8963637, 0.0, 0.0, 0.0, 4.948211, 0.0, 2.5146468, 0.0, 3.6016872, 0.0, 3.805716, 1.7857156, 0.0, 0.0, 0.0, 1.402524, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, -1, 18, -1, -1, -1, 20, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 0.40358648, 1.3215449, 1.4829227, 0.2859163, -0.016296273, 1.54695, 0.029520039, 0.0035976823, -0.024385864, 0.16372892, 0.009975466, 1.4768418, -0.018906845, -0.00080283027, -0.010034223, 0.8293257, 0.019503366, 0.6897765, -0.022013513, 0.29891056, 0.017677864, 0.2585062, -0.26923078, -0.0027298268, 0.019925604, 0.006013143, 0.39070654, -0.01580785, -0.0033333197], "split_indices": [80, 119, 141, 139, 138, 139, 0, 138, 0, 0, 0, 142, 0, 138, 0, 0, 0, 142, 0, 142, 0, 143, 0, 141, 1, 0, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1651.0, 425.0, 1420.0, 231.0, 328.0, 97.0, 1326.0, 94.0, 143.0, 88.0, 239.0, 89.0, 1222.0, 104.0, 117.0, 122.0, 1103.0, 119.0, 1013.0, 90.0, 915.0, 98.0, 300.0, 615.0, 134.0, 166.0, 148.0, 467.0, 122.0, 345.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019429355, 0.007348363, -0.078131594, -0.0052825594, 0.024529867, 0.0047785547, -0.02383898, 0.004744625, -0.08581017, 0.013900131, -0.013315424, -0.0022950356, -0.016152764, -0.0019895625, 0.014310322, 0.033391967, -0.02741566, 0.0045554736, 0.014847337, 0.06875441, -0.061015192, -0.03010724, 0.012067559, -0.0032927138, 0.019100081, -0.13147652, -0.012771384, 0.004437787, -0.07976399, -0.00039295593, -0.023472881, 0.03121426, -0.01082831, -0.0011914335, -0.015699709, 0.012641974, -0.0035571659], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, 29, 31, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [1.4653358, 5.545207, 4.5403337, 1.4146794, 0.0, 0.0, 0.0, 1.9670273, 0.92335916, 2.9994295, 0.0, 0.0, 0.0, 1.1703981, 0.0, 1.8052876, 2.446071, 1.7508918, 0.0, 2.4363203, 1.9070199, 1.239061, 0.0, 0.0, 0.0, 3.0026486, 1.3989811, 0.0, 1.0532874, 0.0, 0.0, 1.4497123, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 25, 25, 26, 26, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, 30, 32, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3215449, 1.4513451, 1.0, 0.024529867, 0.0047785547, -0.02383898, 1.0279931, 0.3084008, 1.4768418, -0.013315424, -0.0022950356, -0.016152764, 1.2718703, 0.014310322, 0.3898465, -0.30769232, 0.2859163, 0.014847337, 1.0, 0.4289852, 0.16433378, 0.012067559, -0.0032927138, 0.019100081, 0.29207063, 0.72117895, 0.004437787, 1.0, -0.00039295593, -0.023472881, 0.1923077, -0.01082831, -0.0011914335, -0.015699709, 0.012641974, -0.0035571659], "split_indices": [119, 139, 138, 40, 0, 0, 0, 142, 140, 138, 0, 0, 0, 138, 0, 142, 1, 139, 0, 109, 143, 143, 0, 0, 0, 143, 143, 0, 80, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1845.0, 225.0, 1752.0, 93.0, 126.0, 99.0, 1558.0, 194.0, 1461.0, 97.0, 106.0, 88.0, 1301.0, 160.0, 544.0, 757.0, 435.0, 109.0, 196.0, 561.0, 335.0, 100.0, 107.0, 89.0, 228.0, 333.0, 134.0, 201.0, 102.0, 126.0, 228.0, 105.0, 107.0, 94.0, 94.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028337156, 0.0033546076, -0.011601645, -0.005235679, 0.018155536, -0.019404786, 0.06592979, -0.00241008, -0.12160266, -0.021251129, 0.024900975, -0.03592978, 0.033020597, -0.0027233977, -0.02410079, 0.012447258, -0.0143965855, -0.016174097, -0.01603485, 0.01922323, 0.007055961, -0.050122086, 0.011876201, -0.031466577, 0.012207328, 0.018389193, -0.13446854, -0.026026038, 0.029545091, -0.007931303, 0.0070688636, -0.008310057, -0.020685064, 0.016530452, -0.07173573, 0.0024222224, -0.015713835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, 17, 19, -1, -1, -1, -1, 21, -1, -1, 23, 25, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [1.4456493, 2.9957674, 0.0, 1.8825915, 0.0, 2.7042327, 4.947935, 1.5854802, 2.5015209, 3.7553139, 0.0, 1.6861718, 2.6828837, 0.0, 0.0, 0.0, 0.0, 2.7118392, 0.0, 0.0, 2.472364, 2.7333174, 0.0, 5.8349013, 0.0, 1.3336506, 0.78824115, 0.0, 4.537443, 0.0, 0.0, 0.0, 0.0, 0.0, 1.548867, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 17, 17, 20, 20, 21, 21, 23, 23, 25, 25, 26, 26, 28, 28, 34, 34], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, 18, 20, -1, -1, -1, -1, 22, -1, -1, 24, 26, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.6874348, 1.5950041, -0.011601645, 1.0, 0.018155536, 0.76996374, 0.70742667, 0.3898465, 1.0, 0.37783793, 0.024900975, 1.3147875, 0.36278594, -0.0027233977, -0.02410079, 0.012447258, -0.0143965855, 0.30609313, -0.01603485, 0.01922323, 0.6923077, 0.21818505, 0.011876201, 0.45529872, 0.012207328, 0.10128946, 0.27793968, -0.026026038, 1.0, -0.007931303, 0.0070688636, -0.008310057, -0.020685064, 0.016530452, 0.60685617, 0.0024222224, -0.015713835], "split_indices": [138, 138, 0, 42, 0, 142, 142, 142, 105, 142, 0, 138, 141, 0, 0, 0, 0, 139, 0, 0, 1, 143, 0, 141, 0, 143, 142, 0, 93, 0, 0, 0, 0, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1957.0, 107.0, 1867.0, 90.0, 1557.0, 310.0, 1335.0, 222.0, 210.0, 100.0, 686.0, 649.0, 124.0, 98.0, 96.0, 114.0, 592.0, 94.0, 91.0, 558.0, 473.0, 119.0, 418.0, 140.0, 261.0, 212.0, 88.0, 330.0, 91.0, 170.0, 124.0, 88.0, 141.0, 189.0, 89.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.005085411, 0.0008066907, -0.01118525, -0.007604883, 0.017945709, -0.013943574, 0.011768987, -0.00040014944, -0.07812192, 0.025161253, -0.0382673, -0.0024454484, -0.013248632, 0.0054765646, 0.015559368, -0.023221372, -0.0025811535, 0.024742631, -0.008906155, 0.028537638, -0.014434457, 0.0034801054, 0.015642527, 0.10335035, -0.06705636, -0.0079573905, 0.020127501, 0.023453528, 0.0014297772, -0.021297114, 0.008549087, 0.009797155, -0.00091229816], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, -1, -1, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2990559, 2.9408453, 0.0, 1.4843683, 0.0, 1.5462971, 0.0, 1.4219002, 0.90445495, 2.2517164, 4.09735, 0.0, 0.0, 1.3878895, 0.0, 0.0, 2.2057536, 1.7723396, 0.0, 2.9321752, 0.0, 0.7535348, 0.0, 2.6869414, 4.006602, 0.0, 1.0337474, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, -1, -1, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.6874348, 1.5950041, -0.01118525, 1.0, 0.017945709, 1.0, 0.011768987, 0.51125157, 0.39655662, 0.49834487, 0.50012213, -0.0024454484, -0.013248632, 0.4007161, 0.015559368, -0.023221372, 3.0, 0.42525762, -0.008906155, 1.0, -0.014434457, 0.13375174, 0.015642527, 0.737314, 0.79351616, -0.0079573905, 0.16433378, 0.023453528, 0.0014297772, -0.021297114, 0.008549087, 0.009797155, -0.00091229816], "split_indices": [138, 138, 0, 114, 0, 7, 0, 141, 139, 139, 140, 0, 0, 139, 0, 0, 0, 140, 0, 108, 0, 142, 0, 143, 142, 0, 143, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1957.0, 108.0, 1869.0, 88.0, 1779.0, 90.0, 1469.0, 310.0, 877.0, 592.0, 156.0, 154.0, 762.0, 115.0, 92.0, 500.0, 633.0, 129.0, 410.0, 90.0, 545.0, 88.0, 230.0, 180.0, 91.0, 454.0, 93.0, 137.0, 92.0, 88.0, 124.0, 330.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002786576, -0.0066063437, 0.059144106, 0.00475819, -0.01874648, -0.02372833, 0.018069036, -0.004956463, 0.010045385, 0.0037482658, -0.008493935, 0.004117817, -0.014883424, -0.010754473, 0.06118709, 0.004371006, -0.014988164, -0.0058829333, 0.01787871, -0.019799734, 0.060065407, -0.046120793, 0.00740112, 0.018037977, -0.007383536, -0.013043949, -0.016758783, -0.009398636, 0.009444613, 0.008863922, -0.0058012754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [1.0968376, 3.6503415, 2.9815595, 1.5534457, 0.0, 0.6594347, 0.0, 1.9805763, 0.0, 0.0, 0.0, 1.2111673, 0.0, 2.3821418, 4.1636105, 1.3744446, 0.0, 0.0, 0.0, 1.7580726, 2.5075593, 2.2338665, 0.0, 0.0, 1.7461101, 1.9982133, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [0.9442745, 0.83632547, 1.0, 0.7114479, -0.01874648, 1.0, 0.018069036, 0.6705351, 0.010045385, 0.0037482658, -0.008493935, 1.2692307, -0.014883424, 0.66602075, 0.32674077, 1.0, -0.014988164, -0.0058829333, 0.01787871, 0.40983817, 0.42229584, 0.28850037, 0.00740112, 0.018037977, 0.49834487, 1.0, -0.016758783, -0.009398636, 0.009444613, 0.008863922, -0.0058012754], "split_indices": [142, 142, 0, 142, 0, 105, 0, 142, 0, 0, 0, 1, 0, 139, 139, 71, 0, 0, 0, 142, 142, 142, 0, 0, 139, 53, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1776.0, 296.0, 1671.0, 105.0, 176.0, 120.0, 1517.0, 154.0, 88.0, 88.0, 1427.0, 90.0, 1132.0, 295.0, 1021.0, 111.0, 146.0, 149.0, 712.0, 309.0, 556.0, 156.0, 111.0, 198.0, 437.0, 119.0, 107.0, 91.0, 134.0, 303.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0019225625, -0.0068929363, 0.055537656, 0.0028201172, -0.017937636, 0.01793708, -0.0027961237, -0.0057280823, 0.011151432, 0.010432591, -0.04821079, -0.0051756008, 0.016819777, -0.0042248173, -0.016907657, -0.021742027, 0.014092447, -0.010669201, 0.008266254, -0.005981298, -0.010459232, -0.030683715, 0.011167537, 0.043084748, -0.062322196, 0.01686095, -0.006659704, -0.0036777188, -0.01659373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.9807361, 2.9854577, 3.0295997, 1.5674591, 0.0, 0.0, 0.0, 1.0737629, 0.0, 2.789933, 2.291367, 2.4953868, 0.0, 2.813381, 0.0, 1.2091529, 0.0, 0.0, 0.0, 2.2611823, 0.0, 1.5007122, 0.0, 2.6571817, 1.1910815, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 19, 19, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [0.9442745, 0.97098887, 1.0220126, 0.78044474, -0.017937636, 0.01793708, -0.0027961237, 0.51125157, 0.011151432, 3.0384614, 0.46153846, 0.46104842, 0.016819777, 0.62566674, -0.016907657, 0.39655995, 0.014092447, -0.010669201, 0.008266254, 0.3898465, -0.010459232, 1.0, 0.011167537, 0.7692308, 0.3525671, 0.01686095, -0.006659704, -0.0036777188, -0.01659373], "split_indices": [142, 140, 143, 140, 0, 0, 0, 141, 0, 1, 1, 141, 0, 143, 0, 141, 0, 0, 0, 142, 0, 53, 0, 1, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1782.0, 293.0, 1687.0, 95.0, 118.0, 175.0, 1564.0, 123.0, 1133.0, 431.0, 1031.0, 102.0, 316.0, 115.0, 926.0, 105.0, 145.0, 171.0, 778.0, 148.0, 643.0, 135.0, 193.0, 450.0, 90.0, 103.0, 361.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0016185825, 0.007632621, -0.010351967, 0.0008276918, 0.0076965108, 0.009702116, -0.013957237, 0.0007609884, 0.014493664, 0.017663756, -0.017225089, -0.017841557, 0.05082122, 0.02220906, -0.016632857, 0.011022456, 0.019534046, -0.024309179, 0.017531635, 0.054778583, -0.010612209, 0.020333726, -0.010575234, -0.018172529, 0.018618723, -0.005471946, 0.017297002, 0.009696264, -0.008725365], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [1.3088722, 0.92378956, 0.0, 2.2215638, 0.0, 2.0277426, 0.0, 4.600049, 0.0, 1.6870224, 0.0, 4.1153216, 4.261999, 3.881644, 0.0, 2.978085, 0.0, 1.5197893, 0.0, 4.055051, 0.0, 3.0930774, 0.0, 2.1633973, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010351967, 1.1306531, 0.0076965108, 0.9442745, -0.013957237, 0.79452574, 0.014493664, 1.0, -0.017225089, 0.55228823, 0.5743799, 0.45826778, -0.016632857, 0.45255446, 0.019534046, 0.3107788, 0.017531635, 0.3314175, -0.010612209, 0.24110529, -0.010575234, 0.19738461, 0.018618723, -0.005471946, 0.017297002, 0.009696264, -0.008725365], "split_indices": [43, 125, 0, 143, 0, 142, 0, 141, 0, 111, 0, 141, 141, 140, 0, 140, 0, 140, 0, 140, 0, 142, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1958.0, 112.0, 1783.0, 175.0, 1677.0, 106.0, 1573.0, 104.0, 1433.0, 140.0, 692.0, 741.0, 545.0, 147.0, 581.0, 160.0, 418.0, 127.0, 423.0, 158.0, 270.0, 148.0, 272.0, 151.0, 181.0, 89.0, 102.0, 170.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.004193165, 0.011855905, -0.06400154, -0.022899913, 0.034157302, 0.0013836208, -0.012061084, 0.013099739, -0.09285876, 0.16698012, 0.009730113, -0.04904391, 0.012850939, -0.022236643, 0.0044203494, 0.0081171235, 0.025278896, -0.007933617, 0.017000524, 0.00066113326, -0.011742038, 0.019162383, -0.017711287, 0.04076255, -0.012134777, -0.01682432, 0.10319493, 0.04973936, -0.012048076, 0.018180063, 0.0018786098, 0.00036694805, 0.010472596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, -1, -1, -1, 23, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.0811731, 1.4416921, 0.92092544, 1.8309456, 3.6760044, 0.0, 0.0, 3.442549, 4.3844013, 1.2959156, 2.7093215, 1.187319, 0.0, 0.0, 0.0, 0.0, 0.0, 3.9514782, 0.0, 0.0, 0.0, 2.2550366, 0.0, 2.315364, 0.0, 2.3114178, 2.0502193, 0.5167778, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 17, 17, 21, 21, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, -1, -1, -1, 24, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.1923077, 0.07692308, 0.78044474, -0.03846154, 0.0013836208, -0.012061084, 0.62566674, 1.0, 0.5501886, 0.84845734, 1.0, 0.012850939, -0.022236643, 0.0044203494, 0.0081171235, 0.025278896, 1.0, 0.017000524, 0.00066113326, -0.011742038, 0.6725299, -0.017711287, 0.3314175, -0.012134777, 0.2782904, 1.0, 1.0, -0.012048076, 0.018180063, 0.0018786098, 0.00036694805, 0.010472596], "split_indices": [40, 1, 1, 140, 1, 0, 0, 143, 61, 143, 143, 111, 0, 0, 0, 0, 0, 64, 0, 0, 0, 143, 0, 140, 0, 141, 97, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1860.0, 209.0, 727.0, 1133.0, 88.0, 121.0, 480.0, 247.0, 176.0, 957.0, 312.0, 168.0, 127.0, 120.0, 88.0, 88.0, 862.0, 95.0, 172.0, 140.0, 743.0, 119.0, 644.0, 99.0, 335.0, 309.0, 204.0, 131.0, 160.0, 149.0, 111.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0012675863, -0.007888777, 0.057244126, 0.0027013912, -0.019561348, -0.0064472244, 0.11351503, 0.015012288, -0.07065526, -0.0010392107, 0.026358036, 0.0036388496, 0.018386407, -0.014788124, 0.0053072353, 0.04729549, -0.03240737, -0.011073994, 0.16635762, 0.004058977, -0.12757565, 0.0735459, -0.014465637, 0.032256227, 0.004223071, -0.028258242, 0.014998624, -0.022041647, -0.004254385, 0.014887156, 0.0005638653, -0.00092249335, -0.013395644, -0.03630944, 0.010052005, 0.0042268476, -0.007840477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1], "loss_changes": [1.0609611, 3.5367172, 1.9930853, 1.5207968, 0.0, 0.0, 3.7002397, 2.7692528, 2.312307, 0.0, 0.0, 2.1260107, 0.0, 0.0, 0.0, 4.246203, 2.5681257, 4.634528, 3.8972282, 2.5230405, 1.6183562, 1.2839043, 0.0, 0.0, 0.0, 1.2655301, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2492299, 0.0, 0.853403, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 25, 25, 31, 31, 33, 33], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1], "split_conditions": [0.9442745, 0.8971796, 1.0, 2.0, -0.019561348, -0.0064472244, 1.1589121, 0.7821551, 0.46153846, -0.0010392107, 0.026358036, 1.0, 0.018386407, -0.014788124, 0.0053072353, 0.51852506, 0.47075045, 0.39554682, 0.6725299, 0.44963285, 1.0, 0.5769231, -0.014465637, 0.032256227, 0.004223071, 0.3564586, 0.014998624, -0.022041647, -0.004254385, 0.014887156, 0.0005638653, 0.27870917, -0.013395644, 1.1864421, 0.010052005, 0.0042268476, -0.007840477], "split_indices": [142, 141, 93, 0, 0, 0, 140, 142, 1, 0, 0, 122, 0, 0, 0, 143, 140, 141, 143, 142, 71, 1, 0, 0, 0, 141, 0, 0, 0, 0, 0, 143, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1779.0, 291.0, 1684.0, 95.0, 92.0, 199.0, 1442.0, 242.0, 109.0, 90.0, 1351.0, 91.0, 149.0, 93.0, 611.0, 740.0, 410.0, 201.0, 535.0, 205.0, 251.0, 159.0, 89.0, 112.0, 438.0, 97.0, 98.0, 107.0, 119.0, 132.0, 348.0, 90.0, 258.0, 90.0, 90.0, 168.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.005348308, 0.012806496, -0.061198886, 0.02050648, -0.011493989, -0.000316686, -0.011757283, 0.006295209, 0.13939215, 0.014919035, -0.009073979, 0.0019847823, 0.026690608, 0.000686329, 0.014095209, 0.0128246425, -0.013148638, 0.07858626, -0.0018042378, -0.006985759, 0.023855975, 0.012973092, -0.015881075, 0.0133741675, -0.05803822, -0.0015053427, 0.015066655, 0.001167018, -0.018373292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1], "loss_changes": [1.0194442, 1.8167927, 0.6771988, 2.9431374, 0.0, 0.0, 0.0, 1.302081, 2.8353028, 2.5633276, 0.0, 0.0, 0.0, 2.05999, 0.0, 1.1313344, 0.0, 5.0818768, 1.7812377, 0.0, 0.0, 0.0, 1.0717529, 2.0021842, 3.119264, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1], "split_conditions": [1.0, 1.6874348, 0.32253915, 0.9494613, -0.011493989, -0.000316686, -0.011757283, 0.87168705, 0.94488615, 0.7114479, -0.009073979, 0.0019847823, 0.026690608, 0.7169699, 0.014095209, 1.0, -0.013148638, 0.3898465, 1.177135, -0.006985759, 0.023855975, 0.012973092, 0.46153846, 0.6208087, 0.40983817, -0.0015053427, 0.015066655, 0.001167018, -0.018373292], "split_indices": [40, 138, 141, 140, 0, 0, 0, 142, 139, 142, 0, 0, 0, 139, 0, 89, 0, 142, 138, 0, 0, 0, 1, 143, 142, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1847.0, 207.0, 1742.0, 105.0, 102.0, 105.0, 1556.0, 186.0, 1429.0, 127.0, 96.0, 90.0, 1284.0, 145.0, 1176.0, 108.0, 214.0, 962.0, 111.0, 103.0, 93.0, 869.0, 513.0, 356.0, 425.0, 88.0, 229.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0023155005, 0.0098419385, -0.013938414, 0.0020302373, 0.017584054, -0.009878615, 0.06266066, 0.0048925728, -0.10052921, -0.011031733, 0.022220092, 0.009958844, -0.003884115, -0.00023132584, -0.020913335, 0.01001104, -0.0106436955, -0.03396538, 0.039301854, -0.012862918, -0.019517483, 0.00041266906, 0.022358352, -0.058544822, 0.031375304, 0.011459224, -0.036081426, 0.0020286182, -0.10556064, -0.0043079355, 0.014170359, -0.01742338, 0.018934995, -0.016843392, -0.0054444126, -0.0066676806, 0.01369892], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, -1, 17, -1, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, 29, -1, 31, -1, 33, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [2.199109, 2.5389998, 0.0, 1.3502127, 0.0, 2.0928838, 3.6093707, 1.1170202, 2.335998, 2.2267432, 0.0, 0.0, 1.597879, 0.0, 0.0, 0.0, 0.0, 0.99157685, 3.619114, 0.0, 1.2493262, 1.7375894, 0.0, 1.3194445, 2.2425468, 0.0, 2.4018056, 0.0, 0.7166908, 0.0, 0.0, 0.0, 2.2841444, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 12, 12, 17, 17, 18, 18, 20, 20, 21, 21, 23, 23, 24, 24, 26, 26, 28, 28, 32, 32], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, -1, 18, -1, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, 30, -1, 32, -1, 34, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [1.6874348, 1.5950041, -0.013938414, 1.0, 0.017584054, 0.76996374, 0.70742667, 0.10037874, 1.0, 0.37783793, 0.022220092, 0.009958844, 0.42835203, -0.00023132584, -0.020913335, 0.01001104, -0.0106436955, -0.3846154, 1.2307693, -0.012862918, 1.0, 1.3310086, 0.022358352, 1.0, 0.3142033, 0.011459224, 1.0, 0.0020286182, 1.2394228, -0.0043079355, 0.014170359, -0.01742338, 1.0, -0.016843392, -0.0054444126, -0.0066676806, 0.01369892], "split_indices": [138, 138, 0, 42, 0, 142, 142, 139, 121, 142, 0, 0, 139, 0, 0, 0, 0, 1, 1, 0, 17, 138, 0, 126, 142, 0, 81, 0, 138, 0, 0, 0, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1958.0, 104.0, 1870.0, 88.0, 1563.0, 307.0, 1344.0, 219.0, 210.0, 97.0, 114.0, 1230.0, 115.0, 104.0, 97.0, 113.0, 725.0, 505.0, 96.0, 629.0, 417.0, 88.0, 356.0, 273.0, 101.0, 316.0, 133.0, 223.0, 163.0, 110.0, 90.0, 226.0, 100.0, 123.0, 131.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.004015881, -0.011263246, 0.008052195, 0.003333412, -0.1196772, -0.011508015, 0.02665214, -0.019708877, -0.00081379357, 9.6128206e-05, -0.020342272, -0.01021683, 0.050649855, -0.020316472, 0.010720423, -0.0013720045, 0.015726253, 0.037752464, -0.03260264, -0.0077917064, 0.01862827, -0.0062780855, -0.10434994, -0.037265792, 0.06577944, -0.0028534306, -0.017957324, -0.011739862, -0.001157102, -0.0017283707, 0.017141413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2725291, 3.0272872, 0.0, 6.5856605, 1.9600146, 3.5543017, 0.0, 0.0, 0.0, 0.78464466, 0.0, 1.482388, 1.7499757, 0.8211748, 0.0, 0.0, 0.0, 3.4532652, 1.7942795, 0.0, 0.0, 1.5518638, 1.4542906, 1.0006711, 1.8338395, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 21, 21, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1254623, 0.87951106, 0.008052195, 0.7927253, 1.0, 0.89923126, 0.02665214, -0.019708877, -0.00081379357, 0.5943309, -0.020342272, 3.1923077, 0.6897765, 1.0, 0.010720423, -0.0013720045, 0.015726253, 0.3898465, 0.4540588, -0.0077917064, 0.01862827, 1.0, 1.0, 1.0, 1.0, -0.0028534306, -0.017957324, -0.011739862, -0.001157102, -0.0017283707, 0.017141413], "split_indices": [142, 143, 0, 143, 59, 140, 0, 0, 0, 140, 0, 1, 142, 89, 0, 0, 0, 142, 142, 0, 0, 93, 15, 26, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1913.0, 164.0, 1686.0, 227.0, 1596.0, 90.0, 134.0, 93.0, 1505.0, 91.0, 1250.0, 255.0, 1151.0, 99.0, 159.0, 96.0, 201.0, 950.0, 113.0, 88.0, 695.0, 255.0, 486.0, 209.0, 127.0, 128.0, 118.0, 368.0, 117.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014994597, 0.0035079077, -0.009615799, -0.0034803438, 0.0077814953, 0.006416807, -0.01455933, -0.003347523, 0.014714859, 0.006237622, -0.010784015, -0.0054451874, 0.01577739, 0.008339355, -0.015761467, -0.046985555, 0.013394462, -0.015334199, -0.007322934, 0.013843742, -0.03356611, -0.0067480765, 0.005254978, 0.0077156858, -0.0065609156], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, -1, 15, 17, 19, -1, 21, -1, 23, -1, -1, -1, -1], "loss_changes": [0.9811597, 1.0208975, 0.0, 2.5275052, 0.0, 2.3085754, 0.0, 1.5734777, 0.0, 2.5475621, 0.0, 1.2244239, 0.0, 0.0, 1.0897098, 2.4382205, 3.634823, 0.0, 1.516363, 0.0, 1.5965557, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 14, 14, 15, 15, 16, 16, 18, 18, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, -1, 16, 18, 20, -1, 22, -1, 24, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009615799, 1.0868651, 0.0077814953, 5.0, -0.01455933, 0.8365981, 0.014714859, 0.7821551, -0.010784015, 1.177135, 0.01577739, 0.008339355, 1.0, 0.0, 0.0, -0.015334199, 1.0, 0.013843742, 1.0, -0.0067480765, 0.005254978, 0.0077156858, -0.0065609156], "split_indices": [117, 125, 0, 143, 0, 0, 0, 143, 0, 142, 0, 138, 0, 0, 13, 0, 0, 0, 39, 0, 89, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1966.0, 104.0, 1797.0, 169.0, 1680.0, 117.0, 1571.0, 109.0, 1439.0, 132.0, 1336.0, 103.0, 139.0, 1197.0, 578.0, 619.0, 157.0, 421.0, 169.0, 450.0, 210.0, 211.0, 101.0, 349.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.003477334, -0.0011895811, 0.009485557, 0.009378201, -0.01630528, 5.1166633e-05, 0.012163334, 0.0117553, -0.06737582, 0.027357684, -0.033949506, 0.0021439714, -0.01992801, 0.014082737, 0.012609001, 0.0003784268, -0.011141523, -0.008690838, 0.034809034, -0.005530588, 0.008390491, 0.021524804, 0.009784644, -0.013169105, 0.034822445, 0.00016977916, 0.014241709], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, 21, -1, -1, -1, 23, -1, 25, -1, -1], "loss_changes": [0.8776436, 3.3492286, 0.0, 1.9243996, 0.0, 1.3392287, 0.0, 1.0311483, 2.9405031, 1.8040634, 0.9785999, 0.0, 0.0, 0.0, 2.107662, 1.1860342, 0.0, 0.0, 3.5219932, 0.0, 0.0, 0.0, 2.4264345, 0.0, 2.0742683, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 18, 18, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, 22, -1, -1, -1, 24, -1, 26, -1, -1], "split_conditions": [1.2553585, 1.0816492, 0.009485557, 0.8681637, -0.01630528, 2.0, 0.012163334, 1.0, 0.54340225, 1.2033349, 0.3564586, 0.0021439714, -0.01992801, 0.014082737, -0.3846154, 1.0, -0.011141523, -0.008690838, -0.26923078, -0.005530588, 0.008390491, 0.021524804, -0.15384616, -0.013169105, 0.61664844, 0.00016977916, 0.014241709], "split_indices": [142, 141, 0, 141, 0, 0, 0, 80, 139, 138, 141, 0, 0, 0, 1, 39, 0, 0, 1, 0, 0, 0, 1, 0, 141, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1958.0, 100.0, 1838.0, 120.0, 1697.0, 141.0, 1446.0, 251.0, 1078.0, 368.0, 150.0, 101.0, 124.0, 954.0, 255.0, 113.0, 174.0, 780.0, 153.0, 102.0, 95.0, 685.0, 103.0, 582.0, 445.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.00428183, 0.0038445322, -0.07107038, -0.008173493, 0.022720084, 0.0031454377, -0.021548701, -0.0009766179, -0.014067592, -0.01204598, 0.01144501, -0.0018187258, -0.016289797, -0.01071942, 0.008851979, 0.0024971918, -0.013947126, 0.013028057, -0.012375615, -0.0062472047, 0.08378747, 0.000959383, -0.010824125, 0.022560477, -0.0003484697], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [1.1207747, 4.9418, 3.3166075, 1.6659448, 0.0, 0.0, 0.0, 2.1171489, 0.0, 2.3327157, 0.0, 1.1385707, 0.0, 2.1934443, 0.0, 1.5542517, 0.0, 1.4716551, 0.0, 1.3701065, 2.8590193, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [1.0, 1.3215449, 1.4652065, 1.0509557, 0.022720084, 0.0031454377, -0.021548701, 1.4726204, -0.014067592, 0.8489387, 0.01144501, 0.71144396, -0.016289797, 0.5930298, 0.008851979, 0.6633733, -0.013947126, 1.0, -0.012375615, 0.4564035, 1.2804871, 0.000959383, -0.010824125, 0.022560477, -0.0003484697], "split_indices": [119, 139, 138, 139, 0, 0, 0, 138, 0, 140, 0, 141, 0, 141, 0, 143, 0, 71, 0, 143, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1841.0, 224.0, 1747.0, 94.0, 131.0, 93.0, 1657.0, 90.0, 1512.0, 145.0, 1416.0, 96.0, 1289.0, 127.0, 1169.0, 120.0, 1079.0, 90.0, 848.0, 231.0, 734.0, 114.0, 88.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0030655183, -0.017484596, 0.025095243, -0.00035238056, -0.012038983, 0.00086580036, 0.1339946, -0.028595468, 0.024461921, 0.022412226, -0.014870401, 0.022362096, 0.004631658, 0.00067456893, -0.016681511, -0.015982661, 0.10442237, 0.012377625, -0.028977657, 0.044617888, -0.015614721, -0.0047551333, 0.022528337, -4.805467e-05, -0.013554736, -0.0041958448, 0.013018166, -0.023460897, 0.008866057, 0.0036060058, -0.05335249, -0.0006782314, -0.012045357], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, 19, 21, -1, 23, 25, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [0.9380221, 1.88993, 2.6385703, 6.358334, 0.0, 2.636164, 1.430203, 3.3336523, 0.0, 2.2513711, 0.0, 0.0, 0.0, 2.4821627, 0.0, 4.1366024, 4.1878343, 0.0, 1.6894939, 2.5186512, 0.0, 0.0, 0.0, 0.89515275, 0.0, 0.0, 0.0, 0.6066991, 0.0, 0.0, 0.70935404, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 15, 15, 16, 16, 18, 18, 19, 19, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, 20, 22, -1, 24, 26, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 0.8543508, 0.91847384, 0.7257892, -0.012038983, 0.79934436, 1.0997353, 0.50395054, 0.024461921, 1.0, -0.014870401, 0.022362096, 0.004631658, -0.26923078, -0.016681511, 0.47514603, 1.0, 0.012377625, 0.41246155, 1.0, -0.015614721, -0.0047551333, 0.022528337, 0.36138788, -0.013554736, -0.0041958448, 0.013018166, 0.15303391, 0.008866057, 0.0036060058, 1.0, -0.0006782314, -0.012045357], "split_indices": [39, 140, 140, 141, 0, 141, 141, 141, 0, 71, 0, 0, 0, 1, 0, 143, 93, 0, 142, 115, 0, 0, 0, 140, 0, 0, 0, 142, 0, 0, 115, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1072.0, 1000.0, 919.0, 153.0, 818.0, 182.0, 824.0, 95.0, 715.0, 103.0, 90.0, 92.0, 680.0, 144.0, 487.0, 228.0, 132.0, 548.0, 340.0, 147.0, 101.0, 127.0, 431.0, 117.0, 169.0, 171.0, 341.0, 90.0, 114.0, 227.0, 134.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004426533, 0.0112581905, -0.007579304, 0.0005740649, 0.020924157, 0.008002677, -0.014529682, 0.04262876, -0.023705909, 0.011597363, 0.017696468, -0.0002549304, -0.0126918275, 0.08013379, -0.026814904, -0.027692059, 0.013235784, -0.003168054, 0.022742769, 0.03921828, -0.073457405, 0.0049676783, -0.04727307, -0.0025885997, 0.010287583, -0.014991678, 0.0038982828, -0.098771356, -0.008416948, -0.015985347, -0.0043797423, 0.00821679, -0.008969515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, 25, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [1.1382638, 4.048644, 0.0, 1.9678503, 0.0, 1.8972476, 0.0, 3.4432888, 2.1832285, 1.7665013, 0.0, 2.674307, 0.0, 3.9691668, 1.3243799, 0.9226107, 0.0, 0.0, 0.0, 0.7376996, 2.1664722, 0.0, 0.9724971, 0.0, 0.0, 0.0, 0.0, 0.7018056, 2.039433, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 22, 22, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, 26, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.6297011, 1.0054001, -0.007579304, 1.0081395, 0.020924157, 1.0, -0.014529682, 0.68689543, 0.5861091, -0.03846154, 0.017696468, 1.0, -0.0126918275, 1.0, 0.3342976, 0.16895212, 0.013235784, -0.003168054, 0.022742769, 0.21832429, 1.0, 0.0049676783, -0.03846154, -0.0025885997, 0.010287583, -0.014991678, 0.0038982828, 1.0, 1.2478638, -0.015985347, -0.0043797423, 0.00821679, -0.008969515], "split_indices": [138, 139, 0, 140, 0, 122, 0, 140, 141, 1, 0, 121, 0, 97, 141, 140, 0, 0, 0, 139, 93, 0, 1, 0, 0, 0, 0, 81, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1914.0, 163.0, 1816.0, 98.0, 1728.0, 88.0, 826.0, 902.0, 671.0, 155.0, 735.0, 167.0, 241.0, 430.0, 609.0, 126.0, 137.0, 104.0, 178.0, 252.0, 123.0, 486.0, 88.0, 90.0, 150.0, 102.0, 209.0, 277.0, 99.0, 110.0, 131.0, 146.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0045754006, 0.0027404854, -0.009154301, -0.008839309, 0.014098908, 0.001870452, -0.011487262, -0.045257125, 0.012087425, 0.0050626094, -0.017076282, -0.00022043871, 0.013526951, 0.012839545, -0.008686843, -0.012158158, 0.07323004, -0.034439288, 0.011118379, -0.0018484356, 0.018584995, 0.0072931997, -0.06518045, -0.003703364, -0.015709346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, -1, -1], "loss_changes": [1.3119377, 3.0448937, 0.0, 1.9929636, 0.0, 0.7675128, 0.0, 3.417625, 1.9861026, 0.0, 0.0, 1.3477612, 0.0, 1.5624608, 0.0, 2.011681, 3.1296473, 2.0464454, 0.0, 0.0, 0.0, 0.0, 1.2469616, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, -1, -1], "split_conditions": [1.1391696, 0.96802163, -0.009154301, 0.8159609, 0.014098908, -1.0, -0.011487262, 0.42764622, 0.7361629, 0.0050626094, -0.017076282, 0.57966477, 0.013526951, 0.43508187, -0.008686843, 1.0, 1.0, 0.17694144, 0.011118379, -0.0018484356, 0.018584995, 0.0072931997, 0.4167339, -0.003703364, -0.015709346], "split_indices": [143, 141, 0, 139, 0, 0, 0, 142, 141, 0, 0, 141, 0, 142, 0, 113, 124, 141, 0, 0, 0, 0, 140, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1902.0, 160.0, 1755.0, 147.0, 1594.0, 161.0, 284.0, 1310.0, 161.0, 123.0, 1191.0, 119.0, 1035.0, 156.0, 732.0, 303.0, 620.0, 112.0, 167.0, 136.0, 138.0, 482.0, 369.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019130927, -0.06868627, 0.0072792484, 0.003966232, -0.013069712, 0.015852956, -0.054919094, 0.0021139227, 0.023538513, 0.0027320941, -0.01631297, 0.017080925, -0.10378163, 0.0031244438, 0.012892395, -0.019999957, -0.0009610841, 0.017781382, -0.008742484, -0.00083272223, 0.10048839, 0.022563722, -0.011579513, 0.01846444, 0.0020761613, 0.0006447982, 0.0116696125, 0.008389867, -0.0019283973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [1.2681147, 1.6796972, 0.96841913, 0.0, 0.0, 4.8137913, 1.9578341, 2.3805783, 0.0, 0.0, 0.0, 2.0541902, 1.6853309, 1.552795, 0.0, 0.0, 0.0, 1.5502937, 0.0, 2.210943, 1.2412549, 1.4092207, 0.0, 0.0, 0.0, 0.9191679, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 20, 20, 21, 21, 25, 25], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.1971636, 0.1567043, 1.0, 0.003966232, -0.013069712, 1.3215449, 1.4652065, 0.87168705, 0.023538513, 0.0027320941, -0.01631297, 0.7114479, 0.9057718, 0.65766674, 0.012892395, -0.019999957, -0.0009610841, 0.54172677, -0.008742484, 0.4540588, 1.0, 1.0, -0.011579513, 0.01846444, 0.0020761613, 0.1831851, 0.0116696125, 0.008389867, -0.0019283973], "split_indices": [138, 142, 119, 0, 0, 139, 138, 142, 0, 0, 0, 142, 139, 139, 0, 0, 0, 142, 0, 142, 106, 61, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 250.0, 1816.0, 91.0, 159.0, 1596.0, 220.0, 1502.0, 94.0, 125.0, 95.0, 1316.0, 186.0, 1170.0, 146.0, 92.0, 94.0, 1007.0, 163.0, 822.0, 185.0, 683.0, 139.0, 90.0, 95.0, 554.0, 129.0, 107.0, 447.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00050977623, 0.008399197, -0.064225726, -0.0025574989, 0.020505497, 0.0059137205, -0.016736524, 0.003328099, -0.011311314, -0.005179936, 0.014131782, 0.0021487633, -0.012054789, 0.010957705, -0.009221259, -0.00035013014, 0.014809295, 0.014560226, -0.087907076, 0.0028227316, 0.013915822, -0.01459872, -0.0028521745, -0.0011412026, 0.013805297], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [1.053116, 3.9603348, 2.8500853, 1.1328446, 0.0, 0.0, 0.0, 1.9406577, 0.0, 1.3164392, 0.0, 1.2169112, 0.0, 2.0763912, 0.0, 1.6149104, 0.0, 1.5458286, 0.62083876, 1.8595213, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3215449, 0.69685656, 1.0509557, 0.020505497, 0.0059137205, -0.016736524, 0.9310844, -0.011311314, 0.8159609, 0.014131782, 0.743426, -0.012054789, 0.67818284, -0.009221259, 0.51125157, 0.014809295, 0.5275764, 0.58140844, 3.0384614, 0.013915822, -0.01459872, -0.0028521745, -0.0011412026, 0.013805297], "split_indices": [119, 139, 142, 139, 0, 0, 0, 141, 0, 139, 0, 140, 0, 141, 0, 141, 0, 142, 142, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1838.0, 224.0, 1741.0, 97.0, 102.0, 122.0, 1653.0, 88.0, 1557.0, 96.0, 1464.0, 93.0, 1339.0, 125.0, 1237.0, 102.0, 1057.0, 180.0, 966.0, 91.0, 91.0, 89.0, 874.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0066936268, 0.011288706, -0.008247819, 0.0038516195, 0.008762568, 0.011193771, -0.009371086, 0.00071124267, 0.01619286, 0.011675729, -0.07747529, -0.005219607, 0.10082395, 0.008621926, -0.02295955, 0.0097571835, -0.01505085, 1.3981029e-05, 0.018529622, -0.004019394, 0.015848164, -0.023736242, 0.011604737, -0.0060026585, 0.0020008322], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [0.8444979, 1.1127402, 0.0, 1.2793447, 0.0, 2.624516, 0.0, 1.3313482, 0.0, 2.0514297, 4.7561393, 2.4914765, 1.845588, 0.0, 0.0, 2.1267729, 0.0, 0.0, 0.0, 2.2489707, 0.0, 1.2954042, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008247819, 1.6152333, 0.008762568, 0.9057718, -0.009371086, 0.743426, 0.01619286, 0.56897336, 1.0, 0.5861091, 0.6031522, 0.008621926, -0.02295955, 1.9230769, -0.01505085, 1.3981029e-05, 0.018529622, 0.46104842, 0.015848164, 1.0, 0.011604737, -0.0060026585, 0.0020008322], "split_indices": [117, 125, 0, 138, 0, 139, 0, 140, 0, 140, 108, 141, 141, 0, 0, 1, 0, 0, 0, 141, 0, 115, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 1786.0, 174.0, 1661.0, 125.0, 1553.0, 108.0, 1362.0, 191.0, 1145.0, 217.0, 92.0, 99.0, 1038.0, 107.0, 99.0, 118.0, 950.0, 88.0, 816.0, 134.0, 446.0, 370.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029029467, 0.0038163783, -0.008005377, -0.0040070666, 0.016544169, -0.010488517, 0.0113545945, -0.0014052769, -0.010505044, 0.006359785, -0.01056281, -0.06615047, 0.022547279, 0.005388784, -0.013030889, 0.0099651385, 0.017716777, 0.013097118, -0.003264206, 0.04750635, -0.035203204, 0.0100827245, 0.00035355485, -0.0001003425, -0.010945038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, -1, 19, 21, 23, -1, -1, -1, -1], "loss_changes": [1.0741279, 2.4100738, 0.0, 1.3851593, 0.0, 1.4799337, 0.0, 1.2722147, 0.0, 1.7172096, 0.0, 2.0562925, 2.3267653, 0.0, 0.0, 1.7705191, 0.0, 0.0, 1.616696, 0.90265614, 1.5540124, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, -1, 20, 22, 24, -1, -1, -1, -1], "split_conditions": [1.6297011, 1.0, -0.008005377, 1.0663754, 0.016544169, 0.83632547, 0.0113545945, 1.0, -0.010505044, -1.0, -0.01056281, 1.0, 0.7897939, 0.005388784, -0.013030889, 0.0, 0.017716777, 0.013097118, 1.0, 1.0, 0.49431247, 0.0100827245, 0.00035355485, -0.0001003425, -0.010945038], "split_indices": [138, 102, 0, 143, 0, 142, 0, 43, 0, 0, 0, 122, 143, 0, 0, 0, 0, 0, 53, 137, 143, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1906.0, 166.0, 1818.0, 88.0, 1723.0, 95.0, 1572.0, 151.0, 1463.0, 109.0, 267.0, 1196.0, 93.0, 174.0, 1106.0, 90.0, 109.0, 997.0, 385.0, 612.0, 174.0, 211.0, 419.0, 193.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0038909556, 0.011209022, -0.05386498, -0.0012568986, 0.024156336, 0.0031865293, -0.019415092, 0.005075143, -0.011850643, -0.0049199453, 0.011026823, 0.0069750817, -0.018551974, -0.0013994005, 0.011452974, 0.008292713, -0.012260082, -0.0030375337, 0.01060386, 0.01342693, -0.00970476, -0.00069943373, 0.01350333], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [0.8719511, 5.2578616, 2.7902067, 1.2895994, 0.0, 0.0, 0.0, 1.7327307, 0.0, 3.233101, 0.0, 1.2718095, 0.0, 1.5388546, 0.0, 1.3433797, 0.0, 1.6824865, 0.0, 2.2971048, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.0, 1.3215449, 1.4829227, 1.0509557, 0.024156336, 0.0031865293, -0.019415092, 1.4768418, -0.011850643, 0.8293257, 0.011026823, 0.6897765, -0.018551974, 0.621171, 0.011452974, 0.54172677, -0.012260082, 0.4540588, 0.01060386, 0.4029062, -0.00970476, -0.00069943373, 0.01350333], "split_indices": [119, 139, 138, 139, 0, 0, 0, 138, 0, 142, 0, 142, 0, 142, 0, 142, 0, 142, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1831.0, 232.0, 1737.0, 94.0, 144.0, 88.0, 1648.0, 89.0, 1505.0, 143.0, 1412.0, 93.0, 1310.0, 102.0, 1213.0, 97.0, 1087.0, 126.0, 925.0, 162.0, 792.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.000912249, 0.004371956, -0.0093028825, 0.015177867, -0.038436066, 0.011872957, 0.0051021655, -0.0103230355, 0.007117983, 0.015712291, -0.009661143, -0.0056892703, 0.009268331, -0.019407392, 0.055975772, -0.05920679, 0.034253165, 0.13486198, -0.015325208, 0.009049035, -0.015269661, -0.059542, 0.021246402, -0.0010637635, 0.026290163, -0.013396877, 0.00800011, -0.009985621, 0.01798323, 0.0074514733, -0.018807065], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, 11, 13, -1, -1, -1, 15, 17, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9959171, 0.8950916, 0.0, 1.611985, 1.1511409, 0.0, 1.5195049, 0.0, 1.254255, 1.8029021, 0.0, 0.0, 0.0, 1.4543833, 3.3410506, 2.4950595, 4.847442, 5.253583, 3.5286741, 4.2034183, 0.0, 3.2737255, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, 12, 14, -1, -1, -1, 16, 18, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0093028825, 1.2043438, 1.0, 0.011872957, 1.3461539, -0.0103230355, 1.0, 1.0, -0.009661143, -0.0056892703, 0.009268331, 1.0, 1.0, 0.73837084, 0.6371679, 0.54327625, -0.34615386, 0.54883325, -0.015269661, 1.0, 0.021246402, -0.0010637635, 0.026290163, -0.013396877, 0.00800011, -0.009985621, 0.01798323, 0.0074514733, -0.018807065], "split_indices": [43, 80, 0, 138, 122, 0, 1, 0, 13, 124, 0, 0, 0, 115, 15, 142, 141, 143, 1, 140, 0, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2046.0, 1935.0, 111.0, 1545.0, 390.0, 137.0, 1408.0, 161.0, 229.0, 1275.0, 133.0, 131.0, 98.0, 681.0, 594.0, 391.0, 290.0, 282.0, 312.0, 226.0, 165.0, 190.0, 100.0, 132.0, 150.0, 139.0, 173.0, 138.0, 88.0, 93.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "2.2236364E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}