{"learner": {"attributes": {"best_iteration": "7", "best_score": "1.149037"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "58"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.00018214567, -0.2026696, 0.33618787, -0.34640318, -0.13295056, 0.16870415, 0.6543446, -0.025682136, -0.37570035, -0.17609043, -0.013615524, 0.05140468, 0.2793135, 0.04161634, 0.7701454, -0.04792748, -0.32092538, 0.0025732804, -0.22227693, 0.013515994, -0.0168974, 0.016334128, -0.0078052394, 0.043729234, 0.016923793, 0.06993907, 0.08432852, -0.039080534, -0.02496885, -0.2662159, -0.0054657888, -0.32252607, -0.017089337, -0.03651729, -0.025793478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [141.22615, 12.94709, 41.56315, 1.1075363, 4.4788456, 6.6299276, 7.419441, 0.0, 1.8041039, 5.956444, 5.339226, 3.5937643, 4.5734653, 0.0, 0.9366684, 0.0, 1.0354271, 0.0, 3.8298016, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.211462, 0.0, 0.71344185, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 16, 16, 18, 18, 29, 29, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [0.5968419, 0.24530737, 1.0048928, 1.1743733, 0.48619443, 0.7095425, 0.96403354, -0.025682136, 0.16191885, 1.2190228, 1.0, 1.0, 0.83632547, 0.04161634, 1.3550751, -0.04792748, 1.0, 0.0025732804, 0.53694934, 0.013515994, -0.0168974, 0.016334128, -0.0078052394, 0.043729234, 0.016923793, 0.06993907, 0.08432852, -0.039080534, -0.02496885, 1.0, -0.0054657888, 0.39630094, -0.017089337, -0.03651729, -0.025793478], "split_indices": [139, 142, 139, 138, 139, 142, 142, 0, 140, 138, 106, 115, 142, 0, 139, 0, 59, 0, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 93, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1292.0, 780.0, 422.0, 870.0, 511.0, 269.0, 104.0, 318.0, 639.0, 231.0, 248.0, 263.0, 88.0, 181.0, 110.0, 208.0, 119.0, 520.0, 118.0, 113.0, 133.0, 115.0, 108.0, 155.0, 92.0, 89.0, 105.0, 103.0, 412.0, 108.0, 259.0, 153.0, 156.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005192983, -0.14848475, 0.3658601, -0.24181874, 7.744743e-05, 0.11069679, 0.54468644, -0.29382774, -0.09796407, -0.094394304, 0.15692769, 0.025451723, -0.0067487783, 0.06968524, 0.40466136, -0.041427426, -0.27270058, 0.0030924329, -0.020206627, 0.009559154, -0.18140464, 0.030586643, 0.0014948671, 0.026343072, 0.054589193, -0.013552814, -0.30261263, -0.007993226, -0.02615144, -0.36575985, -0.23052946, -0.042105205, -0.029239131, -0.012956011, -0.03353822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [113.90147, 19.980764, 28.016777, 6.621338, 8.238763, 6.483526, 7.691841, 1.6540527, 3.153128, 5.736165, 4.4195495, 0.0, 0.0, 0.0, 3.7498608, 0.0, 2.26902, 0.0, 0.0, 0.0, 1.9346833, 0.0, 0.0, 0.0, 0.0, 0.0, 2.066536, 0.0, 0.0, 0.98172, 2.2444248, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 16, 16, 20, 20, 26, 26, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [0.6968186, 0.4088675, 0.83331686, 0.3898465, 0.5968419, 1.0, 1.0, 0.0999041, 0.30174637, 0.47078556, 0.63579667, 0.025451723, -0.0067487783, 0.06968524, 1.0426008, -0.041427426, 1.1743733, 0.0030924329, -0.020206627, 0.009559154, 1.0, 0.030586643, 0.0014948671, 0.026343072, 0.054589193, -0.013552814, 0.26801515, -0.007993226, -0.02615144, 0.26635787, 1.2949271, -0.042105205, -0.029239131, -0.012956011, -0.03353822], "split_indices": [140, 140, 139, 142, 139, 108, 69, 140, 140, 140, 141, 0, 0, 0, 142, 0, 138, 0, 0, 0, 111, 0, 0, 0, 0, 0, 139, 0, 0, 141, 138, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1441.0, 614.0, 885.0, 556.0, 253.0, 361.0, 650.0, 235.0, 347.0, 209.0, 140.0, 113.0, 173.0, 188.0, 97.0, 553.0, 105.0, 130.0, 109.0, 238.0, 102.0, 107.0, 94.0, 94.0, 99.0, 454.0, 105.0, 133.0, 242.0, 212.0, 138.0, 104.0, 108.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.006634897, -0.14040639, 0.31389123, -0.22331543, -0.006567494, 0.14945735, 0.54500496, -0.2673667, -0.09645535, -0.08472335, 0.12369226, 0.2518936, 0.003025878, 0.07045739, 0.031856906, -0.2866532, -0.014750004, 0.004917824, -0.020595431, 0.008035652, -0.15839538, 0.026290117, -0.0007782783, 0.03557791, 0.014358743, -0.03910663, -0.26401204, -0.024643106, -0.009425507, -0.013941834, -0.2927396, -0.33229786, -0.018687405, -0.028344378, -0.039596837], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1], "loss_changes": [88.970314, 16.245213, 23.219784, 5.0518646, 5.7011156, 4.359061, 9.177567, 1.5512199, 3.7155886, 4.2566195, 3.8435247, 2.1602736, 0.0, 0.0, 0.0, 1.3664093, 0.0, 0.0, 0.0, 0.0, 1.3664851, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7001495, 0.0, 0.0, 0.0, 1.6165161, 0.8740654, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 20, 20, 26, 26, 30, 30, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1], "split_conditions": [0.6968186, 0.4088675, 1.0048928, 0.39630094, 0.5968419, 1.0, 1.1785235, 1.3846154, 0.30174637, 0.35518932, 1.0, 0.76996374, 0.003025878, 0.07045739, 0.031856906, 0.13672182, -0.014750004, 0.004917824, -0.020595431, 0.008035652, 1.0, 0.026290117, -0.0007782783, 0.03557791, 0.014358743, -0.03910663, 0.1716032, -0.024643106, -0.009425507, -0.013941834, 0.32997853, 1.0, -0.018687405, -0.028344378, -0.039596837], "split_indices": [140, 140, 139, 142, 139, 108, 143, 1, 140, 143, 53, 142, 0, 0, 0, 142, 0, 0, 0, 0, 69, 0, 0, 0, 0, 0, 142, 0, 0, 0, 139, 108, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1464.0, 611.0, 904.0, 560.0, 357.0, 254.0, 671.0, 233.0, 350.0, 210.0, 192.0, 165.0, 149.0, 105.0, 578.0, 93.0, 100.0, 133.0, 108.0, 242.0, 102.0, 108.0, 98.0, 94.0, 103.0, 475.0, 102.0, 140.0, 89.0, 386.0, 281.0, 105.0, 159.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013757756, -0.08754334, 0.42871055, -0.19718501, 0.036962695, 0.29211554, 0.065356694, -0.22618377, -0.08900309, -0.048740316, 0.10203698, 0.04770557, 0.0117263, -0.27542916, -0.14879818, 0.0006542463, -0.01778605, -0.15242471, 0.009400325, 0.05398492, 0.027806437, -0.31294987, -0.020140179, 0.00054544955, -0.22551209, -0.019916696, -0.009360295, 0.017415423, -0.043728486, -0.03614248, -0.28407678, -0.02902444, -0.015423377, -0.014303766, 0.007789732, -0.030982485, -0.025747037], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [76.379616, 23.438854, 10.565701, 2.8642082, 4.483958, 6.92017, 0.0, 2.7438354, 1.638557, 5.1356983, 3.8655248, 0.0, 0.0, 1.2221222, 3.3133316, 0.0, 0.0, 0.55264044, 0.0, 4.2154336, 0.0, 0.40868568, 0.0, 0.0, 0.8628187, 0.0, 0.0, 0.0, 2.3915546, 0.0, 0.12536526, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 24, 24, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9035947, 0.4292953, 1.0, 1.0, 1.0, -0.34615386, 0.065356694, 1.0, 0.03846154, 1.0, 0.7605597, 0.04770557, 0.0117263, 0.9230769, 1.2052146, 0.0006542463, -0.01778605, 1.0, 0.009400325, 1.3658448, 0.027806437, 1.0, -0.020140179, 0.00054544955, 0.2830091, -0.019916696, -0.009360295, 0.017415423, 1.4038054, -0.03614248, 1.2435346, -0.02902444, -0.015423377, -0.014303766, 0.007789732, -0.030982485, -0.025747037], "split_indices": [139, 139, 0, 71, 69, 1, 0, 83, 1, 97, 139, 0, 0, 1, 138, 0, 0, 109, 0, 138, 0, 69, 0, 0, 139, 0, 0, 0, 138, 0, 138, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1717.0, 344.0, 913.0, 804.0, 214.0, 130.0, 720.0, 193.0, 347.0, 457.0, 104.0, 110.0, 440.0, 280.0, 93.0, 100.0, 201.0, 146.0, 359.0, 98.0, 292.0, 148.0, 93.0, 187.0, 112.0, 89.0, 161.0, 198.0, 109.0, 183.0, 98.0, 89.0, 109.0, 89.0, 93.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018497816, -0.114771925, 0.2644415, -0.16872358, 0.026064664, 0.18629597, 0.059839666, -0.19009592, 0.0014301258, 0.027324528, -0.070250526, 0.04542216, 0.3227328, -0.22072403, -0.119844005, 0.0073784846, -0.018405627, 0.016337512, -0.009115493, 0.046758402, 0.018672742, -0.2002652, -0.0319035, -0.18673874, -0.0007935964, -0.23730624, -0.0029952785, -0.0139424205, -0.023458485, -0.014200168, -0.2631009, -0.3150472, -0.020520892, -0.02889078, -0.034035232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1], "loss_changes": [62.305416, 11.055626, 16.101906, 4.115076, 9.594321, 9.610186, 0.0, 2.026886, 0.0, 0.0, 4.753696, 3.9629796, 5.003937, 1.3194313, 2.1410117, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.425539, 0.0, 0.40522242, 0.0, 1.0964241, 0.0, 0.0, 0.0, 0.0, 1.0555515, 0.12236786, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 21, 21, 23, 23, 25, 25, 30, 30, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1], "split_conditions": [0.6968186, 0.49867448, 1.354651, 0.52959335, 0.4672373, 0.8413322, 0.059839666, 1.0, 0.0014301258, 0.027324528, 1.0, 1.0, 1.0, 0.3927607, 0.37940434, 0.0073784846, -0.018405627, 0.016337512, -0.009115493, 0.046758402, 0.018672742, 0.35181224, -0.0319035, 1.0, -0.0007935964, 1.1721716, -0.0029952785, -0.0139424205, -0.023458485, -0.014200168, 1.0, 0.19397497, -0.020520892, -0.02889078, -0.034035232], "split_indices": [140, 139, 140, 142, 141, 139, 0, 93, 0, 0, 53, 108, 69, 143, 141, 0, 0, 0, 0, 0, 0, 142, 0, 12, 0, 138, 0, 0, 0, 0, 17, 140, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1455.0, 617.0, 1052.0, 403.0, 500.0, 117.0, 942.0, 110.0, 113.0, 290.0, 246.0, 254.0, 656.0, 286.0, 128.0, 162.0, 132.0, 114.0, 123.0, 131.0, 543.0, 113.0, 179.0, 107.0, 446.0, 97.0, 90.0, 89.0, 95.0, 351.0, 185.0, 166.0, 91.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00564084, -0.09779889, 0.2486156, -0.16718735, 0.013620559, 0.03953985, 0.36635268, -0.22371432, -0.120350696, 0.13631114, -0.05929269, 0.01923553, -0.011744327, 0.45121586, 0.01907056, -0.2533129, -0.015481268, 0.0020923237, -0.16977713, 0.00030902258, 0.031443793, -0.1114113, 0.009127216, 0.020403868, 0.070401065, -0.01964262, -0.03321894, -0.24508701, -0.007940525, -0.023011906, -0.0029703353, -0.015441443, -0.033042587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [52.101215, 11.241199, 15.237282, 2.3721905, 4.9917397, 5.349647, 5.9027634, 0.8279896, 3.421507, 4.935884, 2.746531, 0.0, 0.0, 16.683521, 0.0, 1.2743111, 0.0, 0.0, 2.470539, 0.0, 0.0, 2.5218344, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5321016, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 18, 18, 21, 21, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [0.6968186, 0.4088675, 0.79760754, 0.24530737, 0.4672373, 1.0, 1.1785235, 0.25132048, 1.2446306, 0.44442463, 0.7132902, 0.01923553, -0.011744327, 0.98500085, 0.01907056, 0.16507967, -0.015481268, 0.0020923237, 0.4186171, 0.00030902258, 0.031443793, 1.0, 0.009127216, 0.020403868, 0.070401065, -0.01964262, -0.03321894, 0.31830794, -0.007940525, -0.023011906, -0.0029703353, -0.015441443, -0.033042587], "split_indices": [140, 140, 139, 142, 141, 59, 143, 141, 138, 139, 142, 0, 0, 141, 0, 142, 0, 0, 139, 0, 0, 69, 0, 0, 0, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1454.0, 619.0, 896.0, 558.0, 223.0, 396.0, 406.0, 490.0, 208.0, 350.0, 113.0, 110.0, 267.0, 129.0, 284.0, 122.0, 127.0, 363.0, 119.0, 89.0, 260.0, 90.0, 135.0, 132.0, 165.0, 119.0, 198.0, 165.0, 106.0, 154.0, 96.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00566166, -0.08577356, 0.22129391, -0.14179623, 0.0136076575, 0.06197543, 0.3127999, -0.1668885, 0.0022017527, 0.1267199, -0.049008038, -0.0056952657, 0.02457734, 0.39855617, 0.0031029265, -0.14810358, -0.029314486, 0.026685541, -0.0004657115, -0.11393525, 0.009187172, 0.058435556, 0.02575672, -0.02668306, -0.12616926, -0.02414887, -0.0028899591, -0.0028119986, -0.1445165, -0.18439485, -0.09969594, -0.011101964, -0.027183363, -0.020233585, -0.0030780553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, -1, -1, 21, -1, 23, -1, -1, -1, 25, -1, -1, -1, -1, 27, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1], "loss_changes": [40.655178, 8.06189, 8.951256, 3.8062859, 3.6971188, 4.896359, 9.423801, 1.9044876, 0.0, 3.4243696, 3.0733685, 0.0, 0.0, 7.8325005, 0.0, 1.8203335, 0.0, 0.0, 0.0, 2.4947157, 0.0, 0.0, 0.0, 0.0, 1.0613699, 0.0, 0.0, 0.0, 0.8883219, 1.6873646, 1.6551914, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 19, 19, 24, 24, 28, 28, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, -1, -1, 22, -1, 24, -1, -1, -1, 26, -1, -1, -1, -1, 28, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1], "split_conditions": [0.6968186, 0.42670286, 0.79760754, 0.50866467, 0.4672373, 1.0, 1.0, 0.41135296, 0.0022017527, 0.49877596, 0.6897765, -0.0056952657, 0.02457734, 1.0, 0.0031029265, 0.13672182, -0.029314486, 0.026685541, -0.0004657115, 1.0, 0.009187172, 0.058435556, 0.02575672, -0.02668306, 0.1371918, -0.02414887, -0.0028899591, -0.0028119986, 1.0, 0.27044263, 1.0, -0.011101964, -0.027183363, -0.020233585, -0.0030780553], "split_indices": [140, 140, 139, 142, 141, 97, 119, 141, 0, 140, 142, 0, 0, 69, 0, 142, 0, 0, 0, 69, 0, 0, 0, 0, 140, 0, 0, 0, 13, 142, 81, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1448.0, 614.0, 926.0, 522.0, 224.0, 390.0, 803.0, 123.0, 186.0, 336.0, 136.0, 88.0, 299.0, 91.0, 699.0, 104.0, 90.0, 96.0, 230.0, 106.0, 129.0, 170.0, 109.0, 590.0, 92.0, 138.0, 93.0, 497.0, 263.0, 234.0, 143.0, 120.0, 94.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0041909837, -0.09660434, 0.17050327, -0.1281137, 0.032809097, 0.11788005, 0.048112, -0.14742935, 0.0046664625, 0.011624315, -0.006501011, 0.034093592, 0.2203788, -0.080688864, -0.18265955, -0.012251198, 0.1399275, 0.0036117209, 0.3255333, 0.002161686, -0.14392307, -0.15696576, -0.03254949, -0.0009753443, 0.025893617, 0.02173623, 0.04419347, -0.01999942, -0.009372603, -0.19466634, -0.008328804, -0.14144874, -0.028363949, -0.0053984406, -0.020493088], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [34.650158, 5.248045, 12.749611, 3.494112, 2.0566862, 5.7282, 0.0, 2.1913948, 0.0, 0.0, 0.0, 6.082725, 5.8127785, 2.0830894, 2.2386875, 0.0, 3.9011202, 0.0, 2.4049282, 0.0, 0.5601053, 1.4360657, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6193466, 0.0, 1.1882181, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 18, 18, 20, 20, 21, 21, 29, 29, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [0.5968419, 0.5275764, 1.3550751, 3.5, 0.64421266, 0.8048927, 0.048112, -0.03846154, 0.0046664625, 0.011624315, -0.006501011, 1.0, -0.42307693, 0.24578677, 1.0, -0.012251198, 1.0, 0.0036117209, 0.9242993, 0.002161686, 1.0, 1.0, -0.03254949, -0.0009753443, 0.025893617, 0.02173623, 0.04419347, -0.01999942, -0.009372603, 0.29223523, -0.008328804, 1.0, -0.028363949, -0.0053984406, -0.020493088], "split_indices": [139, 142, 139, 1, 142, 142, 0, 1, 0, 0, 0, 39, 1, 139, 40, 0, 111, 0, 139, 0, 69, 93, 0, 0, 0, 0, 0, 0, 0, 141, 0, 23, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1287.0, 780.0, 1035.0, 252.0, 667.0, 113.0, 932.0, 103.0, 136.0, 116.0, 367.0, 300.0, 322.0, 610.0, 148.0, 219.0, 109.0, 191.0, 123.0, 199.0, 517.0, 93.0, 97.0, 122.0, 99.0, 92.0, 94.0, 105.0, 342.0, 175.0, 214.0, 128.0, 90.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0042935256, -0.07390235, 0.18793346, -0.12620378, 0.009126139, 0.13521194, 0.04156358, -0.14265221, 0.0018036327, -0.052276086, 0.10991848, 0.03221475, 0.23378348, -0.17708276, -0.10458782, 0.009647004, -0.11742936, 0.025082147, -0.00089301023, 0.017495757, -0.009826109, 0.032241657, 0.007030462, -0.12590633, -0.21754275, -0.034838557, -0.020109622, -0.018606266, -0.0029371502, -0.006915564, -0.01845279, -0.026903022, -0.014482329, 0.0056154984, -0.011066653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [29.667526, 6.2922945, 7.406967, 2.1091747, 3.4657698, 5.086447, 0.0, 1.0458412, 0.0, 3.372572, 3.5501788, 4.563, 3.7093477, 0.86757946, 2.551196, 0.0, 1.462575, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6154597, 0.87612724, 1.5179684, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6968186, 0.4088675, 1.354651, 0.5275764, 0.5968419, 0.8413322, 0.04156358, 0.27073398, 0.0018036327, 0.35518932, 0.715213, 0.65480655, 1.6320093, 0.17621006, 0.3384693, 0.009647004, 1.0, 0.025082147, -0.00089301023, 0.017495757, -0.009826109, 0.032241657, 0.007030462, 0.5, 0.23882464, 0.33817554, -0.020109622, -0.018606266, -0.0029371502, -0.006915564, -0.01845279, -0.026903022, -0.014482329, 0.0056154984, -0.011066653], "split_indices": [140, 140, 140, 142, 139, 139, 0, 141, 0, 143, 139, 139, 138, 141, 143, 0, 121, 0, 0, 0, 0, 0, 0, 1, 143, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1449.0, 617.0, 889.0, 560.0, 501.0, 116.0, 798.0, 91.0, 348.0, 212.0, 245.0, 256.0, 419.0, 379.0, 106.0, 242.0, 97.0, 115.0, 117.0, 128.0, 166.0, 90.0, 185.0, 234.0, 220.0, 159.0, 136.0, 106.0, 94.0, 91.0, 137.0, 97.0, 100.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0070329793, -0.07324333, 0.14986612, -0.10755698, 0.0077463896, 0.10165736, 0.036360584, 0.0021971827, -0.1274395, 0.016398968, -0.06002801, -0.007166986, 0.17954877, -0.18215123, -0.068627596, 0.0059308806, -0.015649792, 0.01379319, -0.01881506, 0.2613001, -0.000102281294, -0.21758157, -0.14158843, 0.024776965, -0.14938788, 0.038492307, 0.015695771, -0.014754109, -0.02589691, -0.022832856, -0.0059579546, 0.015386595, -0.012700895, -0.020794643, -0.005727328], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, 19, 21, 23, -1, -1, -1, -1, 25, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.493483, 4.0435224, 6.3267336, 2.632018, 4.585165, 4.2467184, 0.0, 0.0, 2.8508806, 0.0, 3.4767485, 5.488449, 4.3104954, 0.65965366, 3.2210238, 0.0, 0.0, 0.0, 0.0, 2.592721, 0.0, 0.7102051, 1.5222797, 3.8795903, 1.2352471, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, 20, 22, 24, -1, -1, -1, -1, 26, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6968186, 0.48619443, 1.354651, 1.1743733, 0.4672373, 0.7882509, 0.036360584, 0.0021971827, 1.0, 0.016398968, 1.0, 0.65480655, 1.6320093, 0.30938345, 1.0, 0.0059308806, -0.015649792, 0.01379319, -0.01881506, 1.0, -0.000102281294, 0.17621006, 1.0, 1.0, 0.40983817, 0.038492307, 0.015695771, -0.014754109, -0.02589691, -0.022832856, -0.0059579546, 0.015386595, -0.012700895, -0.020794643, -0.005727328], "split_indices": [140, 139, 140, 138, 141, 139, 0, 0, 17, 0, 53, 139, 138, 141, 111, 0, 0, 0, 0, 121, 0, 141, 13, 80, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1455.0, 614.0, 1022.0, 433.0, 501.0, 113.0, 136.0, 886.0, 131.0, 302.0, 209.0, 292.0, 459.0, 427.0, 135.0, 167.0, 116.0, 93.0, 201.0, 91.0, 245.0, 214.0, 198.0, 229.0, 92.0, 109.0, 91.0, 154.0, 104.0, 110.0, 107.0, 91.0, 140.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0018352887, -0.055483405, 0.20157848, -0.10568038, 0.03377498, 0.306678, 0.0025597864, -0.08712544, -0.19058935, -0.013150417, 0.027489169, 0.14741938, 0.05569415, -0.1478384, -0.030589975, -0.023502065, -0.014211878, -0.018353967, 0.06968496, 0.012016941, 0.01746693, -0.008725461, -0.17735356, 0.02860066, -0.015706208, 0.025549179, -0.074493, -0.023312997, -0.011689363, -0.0047970633, 0.013396278, -0.02115293, 0.0073443884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, -1, 21, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [23.619328, 7.1822433, 8.507919, 1.6164417, 6.5284657, 11.47871, 0.0, 2.8901105, 0.39626312, 6.817187, 0.0, 0.13068962, 0.0, 0.7259846, 3.2638807, 0.0, 0.0, 0.0, 8.706507, 0.0, 0.0, 0.0, 0.92061996, 2.3961112, 0.0, 0.0, 3.709909, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 18, 18, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, -1, 22, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [0.79489726, 0.48619443, 1.1389784, 0.45865256, 0.96153843, 0.93067384, 0.0025597864, 0.24530737, 0.44524658, 0.52144444, 0.027489169, 0.7465814, 0.05569415, 1.0, 0.37270406, -0.023502065, -0.014211878, -0.018353967, 0.6672731, 0.012016941, 0.01746693, -0.008725461, 1.0, 1.0, -0.015706208, 0.025549179, 0.77566934, -0.023312997, -0.011689363, -0.0047970633, 0.013396278, -0.02115293, 0.0073443884], "split_indices": [143, 139, 143, 143, 1, 140, 0, 142, 141, 143, 0, 142, 0, 53, 139, 0, 0, 0, 143, 0, 0, 0, 59, 17, 0, 0, 139, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1603.0, 460.0, 1026.0, 577.0, 288.0, 172.0, 842.0, 184.0, 483.0, 94.0, 176.0, 112.0, 406.0, 436.0, 96.0, 88.0, 158.0, 325.0, 88.0, 88.0, 133.0, 273.0, 297.0, 139.0, 142.0, 183.0, 142.0, 131.0, 172.0, 125.0, 95.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.001779443, -0.033816036, 0.24458663, -0.094895504, 0.030765692, 0.03952687, -0.0001025077, -0.13304439, -0.0694399, 0.054227777, -0.008984923, -0.0966483, -0.02233479, -0.1043376, 0.006644491, 0.080467135, -0.006355338, -0.019836096, 0.004048215, 0.0030280252, -0.14706065, 0.021499274, 0.028226823, -0.10793585, -0.024844851, -0.11622768, 0.108612806, -0.01663404, -0.006050957, 0.0012837057, -0.024103759, -0.006594284, 0.025276199], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1, -1, 23, 25, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [17.778318, 7.076644, 9.733441, 0.8953562, 2.4676533, 0.0, 0.0, 1.2127895, 2.622363, 2.2560666, 0.0, 3.6682992, 0.0, 2.5305657, 0.0, 7.104167, 0.0, 0.0, 0.0, 0.0, 1.324903, 5.543021, 0.0, 0.66754794, 0.0, 2.8834326, 7.1208625, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 20, 20, 21, 21, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1, -1, 24, 26, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0048928, 0.4244175, 1.2019715, 1.0, 1.0, 0.03952687, -0.0001025077, 1.3054127, 0.4186171, 0.87661415, -0.008984923, 0.22756366, -0.02233479, 0.13440976, 0.006644491, 0.7095425, -0.006355338, -0.019836096, 0.004048215, 0.0030280252, 0.3215633, 1.0, 0.028226823, 0.26923078, -0.024844851, 0.5458753, 1.0, -0.01663404, -0.006050957, 0.0012837057, -0.024103759, -0.006594284, 0.025276199], "split_indices": [139, 140, 143, 69, 7, 0, 0, 138, 139, 142, 0, 139, 0, 139, 0, 142, 0, 0, 0, 0, 143, 69, 0, 1, 0, 141, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1794.0, 263.0, 922.0, 872.0, 163.0, 100.0, 369.0, 553.0, 730.0, 142.0, 263.0, 106.0, 440.0, 113.0, 597.0, 133.0, 151.0, 112.0, 106.0, 334.0, 462.0, 135.0, 241.0, 93.0, 179.0, 283.0, 108.0, 133.0, 88.0, 91.0, 128.0, 155.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033365553, -0.026822133, 0.03701226, -0.08154671, 0.039233353, -0.021654194, -0.11779028, 0.014838935, 0.025309104, -0.010784819, 0.043369327, -0.1400644, 0.0005709733, -0.030093405, 0.14805543, -0.007097298, 0.017970057, -0.1190202, -0.025293777, 0.01241903, -0.020760149, 0.00018089038, 0.02639986, -0.15167984, -0.0033100203, -0.07028109, 0.14601155, -0.1103164, -0.022532692, -0.019626116, 0.0030966676, 0.0031250634, 0.02582776, -0.014548266, -0.0057966597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, -1, 19, 21, -1, -1, 23, -1, 25, -1, -1, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.085604, 7.012826, 0.0, 2.303132, 4.5856843, 2.2418551, 1.8183155, 4.7227407, 0.0, 0.0, 3.554162, 1.330184, 0.0, 4.4523187, 3.3743005, 0.0, 0.0, 1.3244863, 0.0, 5.2589035, 0.0, 0.0, 0.0, 1.0418329, 0.0, 3.750029, 2.344843, 0.40316677, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 19, 19, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, -1, 20, 22, -1, -1, 24, -1, 26, -1, -1, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3188831, 0.48619443, 0.03701226, 0.03846154, 1.1538461, 1.0, 3.1538463, 1.0, 0.025309104, -0.010784819, 0.4053603, 1.0, 0.0005709733, 0.26923078, -0.3846154, -0.007097298, 0.017970057, 1.0, -0.025293777, -0.1923077, -0.020760149, 0.00018089038, 0.02639986, 0.3045081, -0.0033100203, 1.0, 1.0, 0.25502867, -0.022532692, -0.019626116, 0.0030966676, 0.0031250634, 0.02582776, -0.014548266, -0.0057966597], "split_indices": [139, 139, 0, 1, 1, 17, 1, 42, 0, 0, 142, 40, 0, 1, 1, 0, 0, 83, 0, 1, 0, 0, 0, 140, 0, 109, 108, 139, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1940.0, 122.0, 1061.0, 879.0, 400.0, 661.0, 789.0, 90.0, 172.0, 228.0, 560.0, 101.0, 590.0, 199.0, 124.0, 104.0, 472.0, 88.0, 476.0, 114.0, 88.0, 111.0, 342.0, 130.0, 294.0, 182.0, 219.0, 123.0, 131.0, 163.0, 90.0, 92.0, 131.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004265711, -0.04101148, 0.15872948, -0.027033024, -0.025294742, 0.08660507, 0.043177184, -0.072766066, 0.031558085, 0.02309007, -0.010241983, -0.08728574, 0.00040581726, -0.0027250599, 0.021843487, 0.0069068125, -0.0112445755, -0.0714145, -0.019928616, -0.062819265, 0.11681716, -0.10039497, 0.0003633168, 0.011891558, -0.030223353, 0.03121025, -0.005854108, 0.0013418624, -0.13523588, -0.009522981, 0.01236704, -0.11257795, -0.019798083, -0.015612543, -0.0046385764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, 15, 17, -1, 19, -1, -1, -1, 21, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [14.469935, 4.7400594, 9.236025, 4.022004, 0.0, 5.1845803, 0.0, 0.94033575, 4.2156253, 0.0, 1.7994862, 1.2603073, 0.0, 3.99419, 0.0, 0.0, 0.0, 1.3506234, 0.0, 6.6181297, 6.3695517, 1.7764854, 0.0, 3.3766408, 0.0, 0.0, 0.0, 0.0, 0.48763227, 0.0, 0.0, 0.7263901, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, 16, 18, -1, 20, -1, -1, -1, 22, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [0.7920647, 1.0, 1.3188831, 0.4244175, -0.025294742, 0.9504941, 0.043177184, 0.450694, 1.2692307, 0.02309007, 1.0, 0.40206662, 0.00040581726, 0.6897765, 0.021843487, 0.0069068125, -0.0112445755, 0.30699542, -0.019928616, 0.6574953, 0.78012604, 1.1743733, 0.0003633168, 1.0, -0.030223353, 0.03121025, -0.005854108, 0.0013418624, 1.2743348, -0.009522981, 0.01236704, 0.23654483, -0.019798083, -0.015612543, -0.0046385764], "split_indices": [143, 84, 139, 140, 0, 143, 0, 142, 1, 0, 108, 141, 0, 142, 0, 0, 0, 143, 0, 141, 140, 138, 0, 124, 0, 0, 0, 0, 138, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1600.0, 469.0, 1501.0, 99.0, 371.0, 98.0, 843.0, 658.0, 149.0, 222.0, 709.0, 134.0, 556.0, 102.0, 125.0, 97.0, 621.0, 88.0, 370.0, 186.0, 448.0, 173.0, 282.0, 88.0, 88.0, 98.0, 105.0, 343.0, 144.0, 138.0, 252.0, 91.0, 152.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0013184109, -0.015073889, 0.028257579, -0.058848966, 0.0433277, -0.041997943, -0.024241751, 0.0840811, -0.09757499, -0.061936915, 0.015074543, -0.008645229, 0.12753749, 0.0048898878, -0.02605066, -0.08056047, 0.0063461633, 0.06136942, 0.041111488, -0.048315573, -0.16359818, 0.15888155, -0.055543013, -0.07810355, 0.008392074, -0.027713163, -0.008126476, 0.0024389618, 0.024596265, 0.009046175, -0.018028498, 0.0021359108, -0.10879176, -0.14469364, -0.005291627, -0.019163474, -0.008468644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, -1, -1, 17, -1, -1, 19, -1, 21, -1, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1], "loss_changes": [9.543643, 5.000581, 0.0, 3.4583282, 4.8120165, 3.9353395, 0.0, 4.816997, 4.486663, 2.1672196, 0.0, 0.0, 9.719631, 0.0, 0.0, 2.163454, 0.0, 4.7881603, 0.0, 2.2925284, 2.1125555, 2.6819792, 3.4786682, 1.4498572, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7281904, 0.6225133, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 32, 32, 33, 33], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, -1, -1, 18, -1, -1, 20, -1, 22, -1, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1], "split_conditions": [1.3550751, 0.5485319, 0.028257579, 0.49275038, 0.46153846, 3.5, -0.024241751, -0.5, 0.6940361, 1.0, 0.015074543, -0.008645229, 1.0, 0.0048898878, -0.02605066, 1.3155193, 0.0063461633, 1.0, 0.041111488, 0.37940434, 1.0, 1.3908873, 0.6762858, -0.03846154, 0.008392074, -0.027713163, -0.008126476, 0.0024389618, 0.024596265, 0.009046175, -0.018028498, 0.0021359108, 0.22756366, 1.0, -0.005291627, -0.019163474, -0.008468644], "split_indices": [139, 143, 0, 143, 1, 1, 0, 1, 143, 73, 0, 0, 42, 0, 0, 138, 0, 121, 0, 141, 93, 138, 140, 1, 0, 0, 0, 0, 0, 0, 0, 0, 139, 116, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1956.0, 114.0, 1118.0, 838.0, 1024.0, 94.0, 650.0, 188.0, 928.0, 96.0, 132.0, 518.0, 99.0, 89.0, 808.0, 120.0, 420.0, 98.0, 582.0, 226.0, 229.0, 191.0, 475.0, 107.0, 95.0, 131.0, 90.0, 139.0, 88.0, 103.0, 112.0, 363.0, 221.0, 142.0, 124.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.005850453, -0.03768163, 0.10507637, -0.02659319, -0.021255558, 0.19391271, -0.005333058, -0.05802288, 0.03261435, 0.08415541, 0.045333903, -0.039724026, -0.018335547, -0.017457198, 0.027790872, 0.022763316, -0.0021061574, -0.09430602, 0.0069934577, 0.044501573, -0.017161066, -0.0021200026, -0.13017449, 0.019902885, -0.04832975, 0.10869401, -0.010368732, -0.017611116, -0.0072169676, -0.0065185647, -0.014373523, 0.02057912, 0.0014244893, 0.0068072616, -0.0068587163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, 21, 23, 25, -1, -1, 27, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [7.3160906, 3.1219175, 6.5014, 2.8173642, 0.0, 8.428284, 0.0, 2.2682145, 6.4481926, 3.1400306, 0.0, 2.200593, 0.0, 4.1643057, 0.0, 0.0, 0.0, 1.0436354, 4.940167, 2.9584208, 0.0, 0.0, 0.7114339, 0.0, 1.4400346, 1.990051, 0.0, 0.0, 0.0, 1.1620722, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 17, 17, 18, 18, 19, 19, 22, 22, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, 22, 24, 26, -1, -1, 28, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.79489726, 1.0, 1.1389784, 0.49648446, -0.021255558, 0.97328997, -0.005333058, 1.3334098, 0.8076923, 1.0, 0.045333903, 0.24530737, -0.018335547, 0.81328005, 0.027790872, 0.022763316, -0.0021061574, 1.0, 1.2190228, 1.0, -0.017161066, -0.0021200026, 1.0, 0.019902885, 0.7692308, 1.0, -0.010368732, -0.017611116, -0.0072169676, 1.0, -0.014373523, 0.02057912, 0.0014244893, 0.0068072616, -0.0068587163], "split_indices": [143, 84, 143, 139, 0, 139, 0, 138, 1, 39, 0, 142, 0, 140, 0, 0, 0, 53, 138, 0, 0, 0, 12, 0, 1, 106, 0, 0, 0, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1610.0, 462.0, 1514.0, 96.0, 296.0, 166.0, 989.0, 525.0, 208.0, 88.0, 863.0, 126.0, 436.0, 89.0, 88.0, 120.0, 398.0, 465.0, 311.0, 125.0, 131.0, 267.0, 104.0, 361.0, 217.0, 94.0, 149.0, 118.0, 251.0, 110.0, 107.0, 110.0, 114.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004361696, -0.018891154, 0.029213002, -0.04384991, 0.08758611, -0.033798445, -0.020532181, 0.02693062, -0.037965946, 0.0070437477, -0.043315973, -0.013392118, 0.00859762, -0.055439483, 0.00762834, -0.07086078, 0.030560745, -0.04637673, -0.021504458, 0.016010579, -0.011499545, -0.07623685, 0.012604858, -0.029652337, -0.12430023, -0.009423672, 0.00819025, -0.021378262, -0.008217038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, -1, 13, -1, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [8.85266, 5.2061214, 0.0, 2.5757465, 8.487306, 1.4821496, 0.0, 0.0, 2.6164374, 0.0, 1.9850011, 0.0, 0.0, 1.6485105, 0.0, 3.7208338, 3.5637996, 4.6389246, 0.0, 0.0, 0.0, 1.7195587, 0.0, 2.8098335, 1.4250131, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, -1, 14, -1, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.2553585, 0.79489726, 0.029213002, 1.0, 0.9504941, 1.1743733, -0.020532181, 0.02693062, 1.0, 0.0070437477, 1.0, -0.013392118, 0.00859762, 1.0, 0.00762834, 0.68592155, 1.0, 0.59067994, -0.021504458, 0.016010579, -0.011499545, 0.31357685, 0.012604858, 0.2859163, 1.0, -0.009423672, 0.00819025, -0.021378262, -0.008217038], "split_indices": [142, 143, 0, 84, 143, 138, 0, 0, 53, 0, 88, 0, 0, 42, 0, 141, 126, 140, 0, 0, 0, 143, 0, 139, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1959.0, 96.0, 1587.0, 372.0, 1494.0, 93.0, 152.0, 220.0, 125.0, 1369.0, 124.0, 96.0, 1243.0, 126.0, 1054.0, 189.0, 901.0, 153.0, 100.0, 89.0, 768.0, 133.0, 390.0, 378.0, 247.0, 143.0, 121.0, 257.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012088047, -0.015005622, 0.023587858, -0.027287723, 0.012794218, -0.01554724, -0.024829261, -0.035115447, 0.1138638, -0.020328732, -0.022035262, -0.0029265035, 0.028958637, -0.055287354, 0.041835587, -0.04356793, -0.015353101, 0.020787394, -0.0082809655, -0.07461645, 0.06130703, -0.10114216, 0.010697614, -0.049144335, -0.020717535, 0.013692828, -0.0014314172, -5.383424e-05, -0.018512445, -0.008166745, 0.0057717306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.780889, 3.4394152, 0.0, 4.6808453, 0.0, 4.3379016, 0.0, 4.075704, 5.6589694, 2.9946408, 0.0, 0.0, 0.0, 1.0154982, 4.127351, 2.5658948, 0.0, 0.0, 4.07781, 2.0529447, 1.0293429, 1.7827256, 0.0, 1.7724916, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3550751, 1.0, 0.023587858, 1.5732228, 0.012794218, 0.76372665, -0.024829261, 0.75050974, 0.8048927, 0.41587192, -0.022035262, -0.0029265035, 0.028958637, 0.3714244, 0.47078556, 0.3898465, -0.015353101, 0.020787394, 0.59329486, 0.3159845, 0.42227545, 0.46073782, 0.010697614, 1.0, -0.020717535, 0.013692828, -0.0014314172, -5.383424e-05, -0.018512445, -0.008166745, 0.0057717306], "split_indices": [139, 125, 0, 138, 0, 139, 0, 141, 142, 140, 0, 0, 0, 140, 140, 142, 0, 0, 143, 142, 139, 141, 0, 93, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1959.0, 114.0, 1804.0, 155.0, 1713.0, 91.0, 1488.0, 225.0, 1378.0, 110.0, 124.0, 101.0, 882.0, 496.0, 788.0, 94.0, 115.0, 381.0, 608.0, 180.0, 211.0, 170.0, 510.0, 98.0, 90.0, 90.0, 96.0, 115.0, 391.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011612085, -0.022378504, 0.13984376, -0.03900457, 0.08472909, 0.02786426, -0.0027166202, -0.025648857, -0.020514503, 0.023443198, -0.006010539, -0.063646026, 0.022429189, -0.028692368, -0.15171702, 0.078286655, -0.09195833, 0.03903067, -0.08541582, -0.005780615, -0.028418073, -0.03227712, 0.20793252, -0.01701846, -0.0014477045, 0.010654433, -0.008645663, -0.12976304, 0.0018375525, 0.0048986655, -0.011424757, 0.0065220403, 0.033555005, -0.0062241945, -0.01792076], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, 19, 21, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.1988935, 3.2071815, 6.281994, 3.4593031, 5.247079, 0.0, 0.0, 2.6361153, 0.0, 0.0, 0.0, 2.481193, 4.0700455, 2.2165365, 2.8487096, 6.1350102, 1.2667634, 2.2281642, 1.4452977, 0.0, 0.0, 1.5387441, 3.5878754, 0.0, 0.0, 0.0, 0.0, 0.73448086, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, 20, 22, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.9863768, 0.79489726, 1.1785235, 0.7825988, 0.9504941, 0.02786426, -0.0027166202, 1.0, -0.020514503, 0.023443198, -0.006010539, 1.3334098, 1.0, 1.0, 0.55188006, 0.4187106, -0.23076923, 0.32194895, 0.29478824, -0.005780615, -0.028418073, 1.2633044, 1.0, -0.01701846, -0.0014477045, 0.010654433, -0.008645663, 0.16474095, 0.0018375525, 0.0048986655, -0.011424757, 0.0065220403, 0.033555005, -0.0062241945, -0.01792076], "split_indices": [142, 143, 143, 141, 143, 0, 0, 39, 0, 0, 0, 138, 15, 122, 142, 142, 1, 143, 143, 0, 0, 138, 122, 0, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1801.0, 271.0, 1559.0, 242.0, 148.0, 123.0, 1443.0, 116.0, 119.0, 123.0, 806.0, 637.0, 577.0, 229.0, 428.0, 209.0, 263.0, 314.0, 134.0, 95.0, 231.0, 197.0, 104.0, 105.0, 171.0, 92.0, 220.0, 94.0, 116.0, 115.0, 93.0, 104.0, 93.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029290498, -0.017048236, 0.023454104, -0.008136117, -0.019310996, -0.03439477, 0.14687777, -0.024668345, -0.018555958, 0.01820362, 0.039857004, -0.015667813, -0.014862614, -0.0053642094, 0.009168223, -0.036595806, 0.01890696, -0.025495453, -0.017612604, -0.013192173, -0.015016866, -0.04450176, 0.08562869, -0.005858191, 0.0054059247, 0.020457424, -0.0024373566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [6.930412, 3.0612805, 0.0, 7.5588365, 0.0, 2.334824, 8.711909, 1.6646032, 0.0, 0.9396841, 0.0, 5.960078, 0.0, 0.0, 0.0, 1.9546294, 0.0, 1.7931161, 0.0, 3.2920585, 0.0, 1.1213049, 3.3495765, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.354651, 1.1785235, 0.023454104, 0.80249465, -0.019310996, 1.0, 0.93067384, 0.89856476, -0.018555958, 1.0, 0.039857004, 0.6897765, -0.014862614, -0.0053642094, 0.009168223, 0.6763955, 0.01890696, 0.59358287, -0.017612604, 0.42670286, -0.015016866, 0.450694, 0.5163761, -0.005858191, 0.0054059247, 0.020457424, -0.0024373566], "split_indices": [140, 143, 0, 143, 0, 84, 140, 140, 0, 59, 0, 142, 0, 0, 0, 143, 0, 141, 0, 140, 0, 142, 140, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1951.0, 116.0, 1857.0, 94.0, 1588.0, 269.0, 1492.0, 96.0, 178.0, 91.0, 1391.0, 101.0, 90.0, 88.0, 1262.0, 129.0, 1169.0, 93.0, 1064.0, 105.0, 808.0, 256.0, 707.0, 101.0, 123.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014707829, -0.013689565, 0.020614222, -0.022683289, 0.01479355, -0.038520858, 0.066552036, -0.025375267, -0.023411125, 0.022346744, -0.005716971, -0.051407974, 0.02621586, 0.0077459128, -0.072020605, 0.101968944, -0.087031156, -0.049268324, -0.017004145, 0.02536847, 0.03462193, -0.01767653, 0.0012249141, -0.076737545, 0.0056712194, -0.01153093, 0.016410803, -0.109617956, -0.0003778025, -0.013493605, -0.0027046432], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [5.2485924, 2.840356, 0.0, 2.6159637, 0.0, 4.041849, 5.416464, 1.978323, 0.0, 0.0, 0.0, 2.6005077, 4.237933, 0.0, 1.8822865, 3.0244093, 1.7639495, 1.9941736, 0.0, 0.0, 3.9798722, 0.0, 0.0, 1.3050227, 0.0, 0.0, 0.0, 0.783957, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.354651, 1.1228671, 0.020614222, 0.80249465, 0.01479355, 0.89856476, 0.9504941, 0.46627933, -0.023411125, 0.022346744, -0.005716971, 1.1743733, 1.0, 0.0077459128, 0.45865256, 0.5445301, 1.0, 1.0, -0.017004145, 0.02536847, 1.4138277, -0.01767653, 0.0012249141, 0.30742753, 0.0056712194, -0.01153093, 0.016410803, 1.0, -0.0003778025, -0.013493605, -0.0027046432], "split_indices": [140, 142, 0, 143, 0, 140, 143, 139, 0, 0, 0, 138, 12, 0, 143, 139, 121, 62, 0, 0, 138, 0, 0, 143, 0, 0, 0, 61, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1954.0, 115.0, 1851.0, 103.0, 1572.0, 279.0, 1473.0, 99.0, 123.0, 156.0, 979.0, 494.0, 135.0, 844.0, 296.0, 198.0, 685.0, 159.0, 91.0, 205.0, 104.0, 94.0, 544.0, 141.0, 95.0, 110.0, 375.0, 169.0, 287.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0017954507, -0.009626223, 0.017837819, -0.019344782, 0.014968869, -0.011074596, -0.017793238, 0.011025015, -0.08189987, -0.039339293, 0.052294813, -0.045371566, -0.020664008, 0.02996913, -0.07703022, -0.018888095, 0.10771704, -0.105288126, 0.009716674, -0.0075050755, 0.01606854, -0.11155891, 0.0026555809, -0.010621041, 0.008650089, 0.026043182, 0.060540803, -0.0051785223, -0.017773993, -0.018419715, -0.067414105, -0.026422355, 0.017584754, 0.00010457443, -0.013818162, 0.013465518, -0.018218968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1], "loss_changes": [4.18299, 3.0161107, 0.0, 2.4080033, 0.0, 2.7312932, 0.0, 2.7644382, 1.8909669, 1.5647671, 2.8838797, 2.7414699, 0.0, 2.896568, 1.3877559, 2.9448993, 2.961052, 0.8760617, 0.0, 0.0, 0.0, 0.93312, 0.0, 0.0, 0.0, 0.0, 3.1486158, 0.0, 0.0, 0.0, 0.8768972, 4.491221, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 21, 21, 26, 26, 30, 30, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1], "split_conditions": [1.2075883, 1.137993, 0.017837819, 1.5413971, 0.014968869, 1.0, -0.017793238, 0.4244175, 0.39833245, 0.23530261, -0.1923077, 0.2859163, -0.020664008, 0.25782612, 1.0, 1.0, 0.0, 0.18909962, 0.009716674, -0.0075050755, 0.01606854, 1.2871058, 0.0026555809, -0.010621041, 0.008650089, 0.026043182, 0.64882594, -0.0051785223, -0.017773993, -0.018419715, 0.40206662, 0.5325959, 0.017584754, 0.00010457443, -0.013818162, 0.013465518, -0.018218968], "split_indices": [141, 139, 0, 138, 0, 80, 0, 140, 141, 139, 1, 139, 0, 142, 7, 97, 1, 140, 0, 0, 0, 138, 0, 0, 0, 0, 141, 0, 0, 0, 141, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1948.0, 126.0, 1836.0, 112.0, 1745.0, 91.0, 1330.0, 415.0, 599.0, 731.0, 321.0, 94.0, 211.0, 388.0, 320.0, 411.0, 226.0, 95.0, 117.0, 94.0, 291.0, 97.0, 175.0, 145.0, 97.0, 314.0, 130.0, 96.0, 110.0, 181.0, 179.0, 135.0, 92.0, 89.0, 88.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00089124707, -0.021244148, 0.09373579, -0.009438632, -0.0163546, -0.019210937, 0.027952502, -0.026610509, 0.019039212, 0.010047883, -0.014210671, -0.0073432387, -0.08500432, -0.032246217, 0.019869165, -0.0112492675, -0.017323887, -0.010910983, -0.013918742, 0.006515223, -0.008843845, -0.028381305, 0.010589057, -0.004919037, -0.012090853, 0.043519888, -0.044550896, 0.013887229, -0.0031544752, 0.0026937793, -0.009746063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, 25, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [3.9712772, 2.8508692, 7.659265, 5.3771114, 0.0, 3.339026, 0.0, 1.6235038, 0.0, 0.0, 0.0, 5.5670075, 2.3297715, 2.2086034, 0.0, 1.1499871, 0.0, 1.6467322, 0.0, 0.0, 0.0, 1.5239704, 0.0, 1.0750458, 0.0, 1.8037142, 1.1649938, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 21, 21, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, 26, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [0.93067384, 0.7919334, -0.07692308, 0.6968186, -0.0163546, 1.6266692, 0.027952502, 0.5112663, 0.019039212, 0.010047883, -0.014210671, 0.52396023, 1.0, 0.45865256, 0.019869165, 1.0, -0.017323887, 0.42670286, -0.013918742, 0.006515223, -0.008843845, 1.0, 0.010589057, 1.2292287, -0.012090853, 1.0, 1.0, 0.013887229, -0.0031544752, 0.0026937793, -0.009746063], "split_indices": [140, 140, 1, 140, 0, 138, 0, 141, 0, 0, 0, 139, 124, 143, 0, 15, 0, 140, 0, 0, 0, 58, 0, 138, 0, 108, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1697.0, 365.0, 1567.0, 130.0, 227.0, 138.0, 1443.0, 124.0, 115.0, 112.0, 1085.0, 358.0, 968.0, 117.0, 195.0, 163.0, 807.0, 161.0, 98.0, 97.0, 702.0, 105.0, 560.0, 142.0, 252.0, 308.0, 111.0, 141.0, 131.0, 177.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.001579092, -0.009836333, 0.018255537, -0.0026806206, -0.0143626435, -0.020462614, 0.017201964, -0.011342819, -0.016610293, -0.024519742, 0.015054506, -0.008422367, -0.017185543, -0.031667035, 0.10211668, -0.015380228, -0.014676047, 8.184301e-07, 0.026273673, -0.03224581, 0.012549697, 0.0034507464, -0.0074446164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, -1, -1], "loss_changes": [4.2826552, 1.8668585, 0.0, 5.750165, 0.0, 2.2313912, 0.0, 3.3725638, 0.0, 3.4674504, 0.0, 3.3865268, 0.0, 2.0413349, 3.7557533, 2.2666812, 0.0, 0.0, 0.0, 2.4000943, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, -1, -1], "split_conditions": [1.3188831, 1.0953796, 0.018255537, 0.95011306, -0.0143626435, 1.4969914, 0.017201964, 0.7821551, -0.016610293, 0.65480655, 0.015054506, 0.5044889, -0.017185543, 1.0, 1.0, 0.52959335, -0.014676047, 8.184301e-07, 0.026273673, 0.22877026, 0.012549697, 0.0034507464, -0.0074446164], "split_indices": [139, 139, 0, 140, 0, 138, 0, 142, 0, 139, 0, 139, 0, 64, 105, 142, 0, 0, 0, 143, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1950.0, 123.0, 1851.0, 99.0, 1680.0, 171.0, 1581.0, 99.0, 1462.0, 119.0, 1318.0, 144.0, 1089.0, 229.0, 954.0, 135.0, 140.0, 89.0, 852.0, 102.0, 330.0, 522.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.002490973, -0.013381146, 0.016665798, -0.1366557, 0.0013029226, -0.00061933545, -0.028771734, -0.029529454, 0.10279604, -0.01767288, -0.017417964, 0.16423248, -0.009239259, -0.0022248796, -0.011308699, 0.026207408, 0.0045829946, 0.008105249, -0.009720191, 0.06144678, -0.022325072, -0.02027814, 0.019432778, 0.0018960561, -0.015685767, 0.00948844, -0.016887499, -0.05391312, 0.0123220375, -0.012290846, 0.0024337673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, -1, 15, -1, 17, -1, -1, -1, 19, -1, 21, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [3.7762253, 3.486392, 0.0, 4.04011, 5.3854814, 0.0, 0.0, 2.2638729, 4.8086696, 1.7982275, 0.0, 3.53333, 0.0, 1.0301814, 0.0, 0.0, 0.0, 1.5371705, 0.0, 3.7357337, 1.9648949, 3.6450248, 0.0, 3.4599864, 0.0, 0.0, 0.0, 1.8896295, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, -1, 16, -1, 18, -1, -1, -1, 20, -1, 22, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.2075883, -0.5, 0.016665798, 1.4265472, 1.4237114, -0.00061933545, -0.028771734, 0.743426, 1.1577346, 1.0, -0.017417964, 1.0, -0.009239259, 1.0, -0.011308699, 0.026207408, 0.0045829946, 1.0, -0.009720191, 1.3144865, 0.5427232, 0.30958152, 0.019432778, 0.39400846, -0.015685767, 0.00948844, -0.016887499, 0.2708205, 0.0123220375, -0.012290846, 0.0024337673], "split_indices": [141, 1, 0, 138, 138, 0, 0, 140, 140, 40, 0, 50, 0, 43, 0, 0, 0, 53, 0, 138, 139, 143, 0, 143, 0, 0, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1926.0, 124.0, 205.0, 1721.0, 110.0, 95.0, 1320.0, 401.0, 1220.0, 100.0, 305.0, 96.0, 1050.0, 170.0, 167.0, 138.0, 947.0, 103.0, 344.0, 603.0, 213.0, 131.0, 511.0, 92.0, 120.0, 93.0, 350.0, 161.0, 186.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046333834, -0.017555408, 0.08220454, -0.002941308, -0.14294127, 0.022667779, -0.01285408, -0.017777426, 0.016215233, -0.0015419096, -0.027600784, -0.0091022095, -0.0124771735, -0.0490437, 0.01854579, -0.008531884, -0.021477383, -0.013800768, 0.12688938, -0.012278901, 0.045236167, 0.022493059, -0.015278645, 0.025692789, 0.00075756735, 0.015803557, -0.004381597, -0.0136712175, 0.017638363, 0.011877223, -0.051027074, 0.00032698046, -0.01081817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [2.32167, 3.300155, 8.159814, 3.9507985, 3.1901598, 0.0, 0.0, 1.3737338, 0.0, 0.0, 0.0, 1.51179, 0.0, 3.7598555, 2.835175, 2.7645228, 0.0, 3.1426132, 2.8858593, 0.0, 3.0737789, 2.749279, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9790151, 0.0, 0.0, 0.96823555, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0048928, 0.83632547, 1.1785235, 0.7095425, 0.8806386, 0.022667779, -0.01285408, 1.0, 0.016215233, -0.0015419096, -0.027600784, 1.0, -0.0124771735, 0.5804528, 0.60680634, 0.22756366, -0.021477383, 1.3438222, 1.0, -0.012278901, 0.38124925, 0.39183533, -0.015278645, 0.025692789, 0.00075756735, 0.015803557, -0.004381597, 0.13440976, 0.017638363, 0.011877223, 0.26044834, 0.00032698046, -0.01081817], "split_indices": [139, 142, 143, 142, 140, 0, 0, 43, 0, 0, 0, 69, 0, 141, 143, 139, 0, 138, 122, 0, 139, 139, 0, 0, 0, 0, 0, 139, 0, 0, 143, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1801.0, 268.0, 1613.0, 188.0, 159.0, 109.0, 1480.0, 133.0, 96.0, 92.0, 1369.0, 111.0, 560.0, 809.0, 450.0, 110.0, 623.0, 186.0, 144.0, 306.0, 494.0, 129.0, 89.0, 97.0, 135.0, 171.0, 400.0, 94.0, 88.0, 312.0, 160.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0015906675, -0.006965822, 0.014653019, 0.0012279794, -0.01178879, -0.017931167, 0.11522858, -0.0021583862, -0.11662158, 0.0012713706, 0.027762836, -0.018908245, 0.020315174, -0.022498935, -0.001416475, 0.0017324534, -0.10172175, -0.015536203, 0.01084728, -0.002465995, -0.023615174, 0.037774496, -0.087498315, -0.0146784205, 0.027089858, -0.16028214, 0.00038205142, 0.00209905, -0.012459695, -0.0037776777, -0.026690716], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, 19, 21, -1, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [2.558478, 1.7704859, 0.0, 3.962056, 0.0, 2.4174342, 4.34523, 4.6047072, 2.3760457, 0.0, 0.0, 2.116149, 0.0, 0.0, 0.0, 1.8266733, 2.5587754, 3.272407, 0.0, 0.0, 0.0, 5.9917397, 2.4126918, 1.5682704, 0.0, 2.6385512, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, 20, 22, -1, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.354651, 1.0745199, 0.014653019, 0.80249465, -0.01178879, 0.6721605, 0.81785303, 0.62471664, 1.4138277, 0.0012713706, 0.027762836, 1.0, 0.020315174, -0.022498935, -0.001416475, 0.52144444, 1.3155193, 0.33817554, 0.01084728, -0.002465995, -0.023615174, 0.33102146, 1.0, 1.2672434, 0.027089858, 0.40070748, 0.00038205142, 0.00209905, -0.012459695, -0.0037776777, -0.026690716], "split_indices": [140, 141, 0, 143, 0, 143, 139, 143, 138, 0, 0, 0, 0, 0, 0, 143, 138, 141, 0, 0, 0, 140, 39, 138, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1948.0, 115.0, 1814.0, 134.0, 1553.0, 261.0, 1339.0, 214.0, 160.0, 101.0, 1238.0, 101.0, 104.0, 110.0, 991.0, 247.0, 853.0, 138.0, 157.0, 90.0, 490.0, 363.0, 400.0, 90.0, 202.0, 161.0, 302.0, 98.0, 94.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0043666973, -0.0052368734, 0.015654037, 0.005622864, -0.012833682, -0.007796302, 0.02622456, 0.0077870297, -0.06370309, -0.005748992, 0.014693277, -0.016015671, -0.019826131, 0.005401543, -0.012465232, -0.008919781, 0.008948679, -0.020664252, 0.107242696, 0.069491334, -0.048972037, 0.023907842, -0.003178404, 0.016478242, -0.003233932, -0.06959512, 0.0074766427, -0.00402649, -0.01362547], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [3.0280433, 2.605488, 0.0, 6.167601, 0.0, 1.4828064, 0.0, 2.5069122, 1.5701058, 1.6082389, 0.0, 0.0, 1.9337213, 2.9439192, 0.0, 0.0, 0.0, 2.2535086, 4.142285, 2.0474505, 1.7148557, 0.0, 0.0, 0.0, 0.0, 1.1261601, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.2075883, 1.0785918, 0.015654037, 0.93067384, -0.012833682, 1.0, 0.02622456, 0.7821551, 1.0, 0.715213, 0.014693277, -0.016015671, 1.0, 0.49867448, -0.012465232, -0.008919781, 0.008948679, 1.2033699, 0.5325959, 1.0, 0.5153114, 0.023907842, -0.003178404, 0.016478242, -0.003233932, 0.37820587, 0.0074766427, -0.00402649, -0.01362547], "split_indices": [141, 140, 0, 140, 0, 0, 0, 142, 81, 139, 0, 0, 106, 139, 0, 0, 0, 138, 140, 80, 142, 0, 0, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1949.0, 123.0, 1791.0, 158.0, 1702.0, 89.0, 1331.0, 371.0, 1213.0, 118.0, 116.0, 255.0, 1109.0, 104.0, 156.0, 99.0, 883.0, 226.0, 211.0, 672.0, 116.0, 110.0, 109.0, 102.0, 576.0, 96.0, 400.0, 176.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0010509624, 0.010191998, -0.011009703, -0.0079087615, 0.033421356, -0.020256514, 0.054658696, -0.005341127, -0.015751837, -0.005360236, 0.018896755, -0.022185493, 0.015294447, -0.010515218, -0.01701386, 0.02893245, -0.040100973, -0.03634723, 0.14099592, -0.0010648277, -0.025558045, 0.0037909974, -0.012598631, 0.0029829226, 0.02546607, -0.036978275, 0.015930112, 0.006290532, -0.0063278168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [2.0990717, 11.196356, 0.0, 1.396802, 0.0, 3.091443, 4.3330445, 3.6313918, 0.0, 0.0, 0.0, 2.1255102, 0.0, 1.3316487, 0.0, 3.577264, 5.4842906, 2.0568116, 2.2744322, 3.17913, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1847444, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.1785235, 1.137993, -0.011009703, 1.0, 0.033421356, 0.8103251, 0.53462654, 0.6913856, -0.015751837, -0.005360236, 0.018896755, 0.69685656, 0.015294447, 1.0, -0.01701386, 0.41496527, 0.52671957, 0.3324839, 0.47850215, 0.44921184, -0.025558045, 0.0037909974, -0.012598631, 0.0029829226, 0.02546607, 1.1840668, 0.015930112, 0.006290532, -0.0063278168], "split_indices": [143, 139, 0, 42, 0, 139, 143, 139, 0, 0, 0, 142, 0, 122, 0, 139, 140, 141, 143, 142, 0, 0, 0, 0, 0, 138, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1909.0, 157.0, 1808.0, 101.0, 1510.0, 298.0, 1362.0, 148.0, 165.0, 133.0, 1231.0, 131.0, 1141.0, 90.0, 489.0, 652.0, 309.0, 180.0, 552.0, 100.0, 169.0, 140.0, 91.0, 89.0, 451.0, 101.0, 94.0, 357.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0040888786, -0.004191875, 0.014329123, 0.003447937, -0.013449314, -0.00862066, 0.024115866, 0.0034914317, -0.023242524, -0.010498317, 0.013863601, 0.0035993978, -0.10821775, -0.024166044, 0.05447286, -0.020869866, 0.0003427664, -0.0090714, -0.015521496, -0.0076689464, 0.14603287, -0.009821245, 0.005762754, 0.015782569, -0.010563417, 0.0012869922, 0.027639238, 0.009715385, -0.0016867415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [2.3814793, 1.941181, 0.0, 5.2843947, 0.0, 4.7519307, 0.0, 3.1441329, 0.0, 2.0760748, 0.0, 1.8602943, 2.131465, 1.6853719, 2.6457133, 0.0, 0.0, 1.0102621, 0.0, 4.490923, 3.2635021, 0.0, 1.3546685, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.354651, 1.1389784, 0.014329123, 1.0426008, -0.013449314, 1.0044936, 0.024115866, 0.8141592, -0.023242524, 0.6721605, 0.013863601, 0.41587192, 1.411137, 1.3467784, 0.52144444, -0.020869866, 0.0003427664, 0.13672182, -0.015521496, 0.49877596, 1.0, -0.009821245, 0.15249547, 0.015782569, -0.010563417, 0.0012869922, 0.027639238, 0.009715385, -0.0016867415], "split_indices": [140, 143, 0, 142, 0, 140, 0, 143, 0, 143, 0, 140, 138, 138, 143, 0, 0, 142, 0, 140, 93, 0, 140, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1950.0, 116.0, 1842.0, 108.0, 1753.0, 89.0, 1663.0, 90.0, 1507.0, 156.0, 1317.0, 190.0, 852.0, 465.0, 100.0, 90.0, 764.0, 88.0, 277.0, 188.0, 109.0, 655.0, 103.0, 174.0, 93.0, 95.0, 130.0, 525.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0010839455, -0.0056272387, 0.013275742, -0.13760623, 0.009771561, 0.002161133, -0.03148794, -0.014420653, 0.08849877, -0.02397134, 0.009063688, 0.013307891, 0.023512097, -0.0106668, -0.011440851, 0.022409184, -0.10101558, 0.035960205, -0.03619744, -0.0029704156, -0.017313734, -0.039284233, 0.01579081, 0.008694039, -0.12111717, 0.0030493846, -0.011461988, -0.08705776, 0.078863315, -0.0051391474, -0.017007349, 0.00074818917, -0.01687474, 0.020771585, -0.007838043], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, -1, 15, -1, 17, -1, -1, 19, 21, 23, -1, -1, 25, -1, 27, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8221585, 3.9874089, 0.0, 5.786127, 3.3463573, 0.0, 0.0, 1.3485312, 4.5531807, 1.482373, 0.0, 6.578632, 0.0, 1.2785082, 0.0, 0.0, 0.9103291, 3.4868429, 2.645648, 0.0, 0.0, 1.2353429, 0.0, 3.050351, 0.8192425, 0.0, 0.0, 1.4827992, 5.3084497, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 23, 23, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, -1, 16, -1, 18, -1, -1, 20, 22, 24, -1, -1, 26, -1, 28, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2553585, -0.5, 0.013275742, 1.4265472, 0.6968186, 0.002161133, -0.03148794, 3.1923077, 1.0, 1.0, 0.009063688, 0.8624959, 0.023512097, 1.2610291, -0.011440851, 0.022409184, 1.085702, 0.24530737, 0.46153846, -0.0029704156, -0.017313734, 0.16507967, 0.01579081, 0.40042883, 0.3564586, 0.0030493846, -0.011461988, 0.33904427, 0.64421266, -0.0051391474, -0.017007349, 0.00074818917, -0.01687474, 0.020771585, -0.007838043], "split_indices": [142, 1, 0, 138, 140, 0, 0, 1, 0, 40, 0, 140, 0, 138, 0, 0, 140, 142, 1, 0, 0, 142, 0, 143, 141, 0, 0, 140, 142, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1962.0, 100.0, 205.0, 1757.0, 108.0, 97.0, 1344.0, 413.0, 1232.0, 112.0, 273.0, 140.0, 1074.0, 158.0, 96.0, 177.0, 380.0, 694.0, 89.0, 88.0, 235.0, 145.0, 454.0, 240.0, 122.0, 113.0, 192.0, 262.0, 99.0, 141.0, 89.0, 103.0, 144.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015606857, -0.009557568, 0.016426936, -0.102123894, 0.0011352567, 0.006043822, -0.022544691, -0.009978552, 0.018827388, -0.000643107, -0.01292562, -0.01743082, 0.056511633, -0.0017082395, -0.020593086, -0.056514237, 0.017018339, -0.020331874, 0.016641237, -0.0004893392, -0.010813504, 0.006531329, -0.0312198, -0.07225962, 0.010667167, -0.002389704, -0.01354066, 0.008001382, -0.0032384458], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1], "loss_changes": [2.738444, 1.9498975, 0.0, 4.089721, 3.6729681, 0.0, 0.0, 1.8562211, 0.0, 1.4833829, 0.0, 3.5416296, 4.509595, 3.4535115, 0.0, 0.46898848, 0.0, 0.92597044, 0.0, 0.0, 0.0, 0.0, 1.5144688, 1.3590076, 1.3016721, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1], "split_conditions": [1.2553585, -0.5, 0.016426936, 0.5656855, 1.0120424, 0.006043822, -0.022544691, 0.8866997, 0.018827388, 0.5968419, -0.01292562, 0.5427232, 1.0, 0.48619443, -0.020593086, 0.68322664, 0.017018339, 0.09589433, 0.016641237, -0.0004893392, -0.010813504, 0.006531329, 1.0, 1.0, 1.0, -0.002389704, -0.01354066, 0.008001382, -0.0032384458], "split_indices": [142, 1, 0, 141, 142, 0, 0, 141, 0, 139, 0, 139, 111, 139, 0, 142, 0, 139, 0, 0, 0, 0, 17, 12, 122, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1970.0, 95.0, 204.0, 1766.0, 88.0, 116.0, 1667.0, 99.0, 1546.0, 121.0, 1195.0, 351.0, 1103.0, 92.0, 176.0, 175.0, 993.0, 110.0, 88.0, 88.0, 112.0, 881.0, 445.0, 436.0, 252.0, 193.0, 167.0, 269.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0012720865, 0.008576583, -0.008505791, -0.005270559, 0.116519906, 0.004983065, -0.012422804, -0.008208456, 0.033654254, -0.0060360194, 0.015436445, -0.01643248, 0.011577735, -0.008446851, -0.012859422, -0.027468236, 0.05249921, -0.044540323, 0.006057015, 0.015719255, -0.0033986573, -0.0051649753, -0.09392825, -0.00508312, 0.009877678, 0.00067746796, -0.012878696], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [1.2933547, 2.8264906, 0.0, 2.0442934, 9.394958, 2.53985, 0.0, 0.0, 0.0, 1.8198566, 0.0, 1.1858826, 0.0, 1.4328681, 0.0, 1.4158251, 2.662019, 1.5343419, 0.0, 0.0, 0.0, 2.0837693, 1.2286317, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.1582154, 0.95011306, -0.008505791, 0.7919334, -0.07692308, 0.7055078, -0.012422804, -0.008208456, 0.033654254, 3.1923077, 0.015436445, 1.4621714, 0.011577735, 0.5275764, -0.012859422, 1.0, 1.0, 1.2633044, 0.006057015, 0.015719255, -0.0033986573, 0.24530737, 0.0, -0.00508312, 0.009877678, 0.00067746796, -0.012878696], "split_indices": [143, 140, 0, 140, 1, 140, 0, 0, 0, 1, 0, 138, 0, 142, 0, 105, 15, 138, 0, 0, 0, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 1891.0, 160.0, 1676.0, 215.0, 1543.0, 133.0, 113.0, 102.0, 1437.0, 106.0, 1324.0, 113.0, 1236.0, 88.0, 942.0, 294.0, 789.0, 153.0, 133.0, 161.0, 439.0, 350.0, 305.0, 134.0, 90.0, 260.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.00030758203, -0.0107721975, 0.07078908, -0.00059959653, -0.012362023, 0.018464822, -0.009683684, 0.008040549, -0.015170619, -0.000767862, 0.014993261, 0.010390895, -0.0104101645, 0.029302279, -0.0375585, 0.08431495, -0.044672195, -0.10032999, -0.016841834, -0.002938233, 0.021079827, 0.0026161363, -0.016087111, -0.0017120177, -0.016979871, -0.046872884, 0.0075266804, 0.008648711, -0.008242745, -0.012986514, 0.0036822495, -0.0062236874, 0.008634276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, -1, 13, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [1.5482621, 2.0823953, 5.0958934, 2.1724908, 0.0, 0.0, 0.0, 1.9672543, 0.0, 1.4958202, 0.0, 0.0, 1.4630182, 2.242313, 1.0481348, 3.4873998, 1.9342341, 1.156095, 1.6762681, 1.3292614, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9174284, 0.0, 0.0, 0.0, 0.0, 1.5474901, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, -1, 14, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0048928, 0.9504941, 1.1785235, 1.0, -0.012362023, 0.018464822, -0.009683684, 0.8141592, -0.015170619, 1.1743733, 0.014993261, 0.010390895, 1.0, 1.0, 0.2724345, 0.45100847, 0.44288877, 0.21845654, 1.0, 0.27519503, 0.021079827, 0.0026161363, -0.016087111, -0.0017120177, -0.016979871, 1.0, 0.0075266804, 0.008648711, -0.008242745, -0.012986514, 1.0, -0.0062236874, 0.008634276], "split_indices": [139, 143, 143, 117, 0, 0, 0, 143, 0, 138, 0, 0, 126, 12, 139, 140, 140, 140, 23, 140, 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 12, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2081.0, 1814.0, 267.0, 1664.0, 150.0, 159.0, 108.0, 1574.0, 90.0, 1482.0, 92.0, 125.0, 1357.0, 551.0, 806.0, 316.0, 235.0, 200.0, 606.0, 187.0, 129.0, 146.0, 89.0, 91.0, 109.0, 457.0, 149.0, 88.0, 99.0, 173.0, 284.0, 158.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.008010527, -0.013969054, 0.0108717, -0.0041976795, -0.0145991165, 0.0053349617, -0.017029677, -0.011863122, 0.021634225, 0.0016780639, -0.089453556, -0.015191352, 0.0690317, 0.0013462779, -0.022566637, 0.029926028, -0.06304831, -0.004954662, 0.01947966, 0.0009909708, 0.01712509, -0.123385385, -0.010627793, 0.010402582, -0.024837164, -0.0068156905, -0.022191798, 0.006597834, -0.010019806, -0.01226945, 0.0012268582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.432084, 2.527183, 0.0, 2.8880546, 0.0, 6.259889, 0.0, 1.6758134, 0.0, 1.5429821, 3.3223915, 2.3448703, 4.056334, 0.0, 0.0, 2.2858868, 1.6668487, 0.0, 0.0, 1.234796, 0.0, 1.3332415, 1.9349804, 0.0, 1.3471272, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2553585, 1.0745199, 0.0108717, 1.0785918, -0.0145991165, 0.8458368, -0.017029677, 2.0, 0.021634225, 0.5968419, 0.47850215, 0.3401452, -0.1923077, 0.0013462779, -0.022566637, 0.35027784, 0.4441229, -0.004954662, 0.01947966, 0.09851463, 0.01712509, 0.40206662, 0.5325959, 0.010402582, 0.1701686, -0.0068156905, -0.022191798, 0.006597834, -0.010019806, -0.01226945, 0.0012268582], "split_indices": [142, 141, 0, 140, 0, 140, 0, 0, 0, 139, 143, 141, 1, 0, 0, 140, 141, 0, 0, 139, 0, 141, 140, 0, 139, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1959.0, 100.0, 1824.0, 135.0, 1725.0, 99.0, 1595.0, 130.0, 1358.0, 237.0, 1086.0, 272.0, 135.0, 102.0, 559.0, 527.0, 140.0, 132.0, 464.0, 95.0, 245.0, 282.0, 93.0, 371.0, 157.0, 88.0, 152.0, 130.0, 102.0, 269.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0017269914, -0.0070616747, 0.014133714, -0.014830357, 0.008621464, -0.0045481636, -0.019035464, -0.012722243, 0.014249337, 0.0022826593, -0.02309489, 0.010727383, -0.0131409615, -0.0074844016, 0.08582617, -0.019496148, 0.010321843, -0.0040447046, 0.017390566, -0.0051267515, -0.0111647695, -0.002763838, 0.017565537], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, -1, -1], "loss_changes": [2.5275934, 1.4043405, 0.0, 3.2287412, 0.0, 2.0312598, 0.0, 5.242427, 0.0, 1.6912332, 0.0, 1.9270655, 0.0, 1.5079186, 3.0585728, 1.3546177, 0.0, 0.0, 0.0, 3.6016839, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, -1, -1], "split_conditions": [1.2075883, 1.0, 0.014133714, 1.0462813, 0.008621464, 0.9014308, -0.019035464, 0.79934436, 0.014249337, 1.0, -0.02309489, 0.58921087, -0.0131409615, 3.1538463, 1.0, 0.5427232, 0.010321843, -0.0040447046, 0.017390566, 0.48312518, -0.0111647695, -0.002763838, 0.017565537], "split_indices": [141, 125, 0, 143, 0, 142, 0, 141, 0, 84, 0, 142, 0, 1, 126, 139, 0, 0, 0, 141, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1938.0, 122.0, 1789.0, 149.0, 1690.0, 99.0, 1601.0, 89.0, 1498.0, 103.0, 1409.0, 89.0, 1134.0, 275.0, 1023.0, 111.0, 113.0, 162.0, 885.0, 138.0, 787.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033990473, 0.005252004, -0.011239093, -0.01035142, 0.018494636, 0.004568526, -0.13452643, -0.0097428, 0.06788382, -0.024402743, -0.0019074265, 0.0028913943, -0.015148646, 0.024077406, -0.012449013, -0.009926278, 0.01597279, 0.010455423, -0.011608048, -0.042190734, 0.026167225, -0.014550712, -0.018184556, -0.011558775, 0.056256734, 0.0100843, -0.04118004, 0.15042754, -0.020206727, -0.008466679, 0.0024050062, 0.003071969, 0.0260462, 0.007182061, -0.014352338], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, -1, 17, 19, -1, -1, -1, 21, 23, 25, -1, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.948963, 5.369368, 0.0, 3.2644305, 0.0, 1.4253361, 2.3893616, 2.2976186, 4.0277424, 0.0, 0.0, 2.3681087, 0.0, 0.0, 2.4007933, 1.2681811, 0.0, 0.0, 0.0, 2.2195354, 2.1923833, 1.4749713, 0.0, 0.0, 3.0530658, 0.0, 1.1062918, 2.502676, 2.65555, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 24, 24, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, -1, 18, 20, -1, -1, -1, 22, 24, 26, -1, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1785235, 1.0141282, -0.011239093, 0.8103251, 0.018494636, 0.5968419, -0.1923077, 0.5427232, 0.65480655, -0.024402743, -0.0019074265, 0.49867448, -0.015148646, 0.024077406, 0.66457164, 1.0, 0.01597279, 0.010455423, -0.011608048, 0.42644718, 0.18029054, 0.09589433, -0.018184556, -0.011558775, 1.2743348, 0.0100843, 0.26694217, 0.27905032, 1.0, -0.008466679, 0.0024050062, 0.003071969, 0.0260462, 0.007182061, -0.014352338], "split_indices": [143, 141, 0, 139, 0, 139, 1, 139, 139, 0, 0, 139, 0, 0, 140, 17, 0, 0, 0, 143, 139, 139, 0, 0, 138, 0, 143, 142, 12, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1915.0, 152.0, 1762.0, 153.0, 1573.0, 189.0, 1283.0, 290.0, 97.0, 92.0, 1178.0, 105.0, 92.0, 198.0, 1089.0, 89.0, 93.0, 105.0, 575.0, 514.0, 480.0, 95.0, 90.0, 424.0, 90.0, 390.0, 190.0, 234.0, 234.0, 156.0, 91.0, 99.0, 134.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0067804586, -0.013017374, 0.011449973, -0.051161632, 0.008722716, -0.0021676568, -0.020887002, 0.14820115, -0.014263953, -0.039430898, 0.010853407, 0.0039476026, 0.025816176, 0.0059234053, -0.08753015, 0.010010409, -0.08018398, -0.020864509, 0.010802365, -0.020859424, 0.009669776, -0.016579213, -0.019234698, -0.0038063317, -0.013309953, -0.012434029, 0.008818087, 0.037856955, -0.0582508, -0.025514828, 0.016518194, -0.000919512, -0.014018418, 0.004380673, -0.0074798153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, -1, 21, 23, -1, -1, -1, -1, 25, 27, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [1.561995, 1.6286659, 0.0, 5.5091805, 4.0108876, 2.244057, 0.0, 2.116119, 1.5885012, 2.314398, 0.0, 0.0, 0.0, 2.3029144, 5.174385, 0.0, 1.6435921, 1.2769878, 0.0, 0.0, 0.0, 0.0, 2.0773559, 1.3133663, 0.0, 0.0, 0.0, 2.6465707, 1.0088434, 0.748191, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 22, 22, 23, 23, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, -1, 22, 24, -1, -1, -1, -1, 26, 28, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.2553585, -0.1923077, 0.011449973, 1.4859524, -0.03846154, 1.4053268, -0.020887002, 0.57896143, 0.6763955, 0.23922633, 0.010853407, 0.0039476026, 0.025816176, 0.48619443, 0.8505889, 0.010010409, 1.0, 1.0, 0.010802365, -0.020859424, 0.009669776, -0.016579213, -0.3846154, 1.254349, -0.013309953, -0.012434029, 0.008818087, 0.24530737, 1.0, 1.0, 0.016518194, -0.000919512, -0.014018418, 0.004380673, -0.0074798153], "split_indices": [142, 1, 0, 138, 1, 138, 0, 139, 143, 140, 0, 0, 0, 139, 143, 0, 13, 64, 0, 0, 0, 0, 1, 138, 0, 0, 0, 142, 50, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1964.0, 101.0, 713.0, 1251.0, 544.0, 169.0, 177.0, 1074.0, 407.0, 137.0, 89.0, 88.0, 842.0, 232.0, 92.0, 315.0, 667.0, 175.0, 140.0, 92.0, 131.0, 184.0, 579.0, 88.0, 93.0, 91.0, 328.0, 251.0, 219.0, 109.0, 157.0, 94.0, 91.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-9.7133954e-05, -0.008519846, 0.017169107, 9.152428e-05, -0.082967915, 0.008799149, -0.013814198, -0.0022532558, -0.013719027, -0.003785448, 0.107919775, 0.0055035306, -0.010424129, 0.024269193, -0.003583717, -0.0056271725, 0.012646376, 0.009793358, -0.012450746, -0.0017363743, 0.014068206, 0.044388786, -0.033843875, 0.009630274, -0.0051507815, 0.0012966477, -0.009515627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [2.9719784, 1.2552738, 0.0, 2.1124673, 0.6652198, 2.0594466, 0.0, 0.0, 0.0, 1.3670388, 3.6036453, 1.8054858, 0.0, 0.0, 0.0, 2.2511663, 0.0, 1.6404049, 0.0, 1.4794827, 0.0, 2.0411325, 1.6904625, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.2553585, 1.0, 0.017169107, 1.6348165, 1.0, 0.93067384, -0.013814198, -0.0022532558, -0.013719027, 0.82071674, 1.0, 0.7081009, -0.010424129, 0.024269193, -0.003583717, 0.63230115, 0.012646376, 1.9230769, -0.012450746, -0.03846154, 0.014068206, 1.0, 0.33817554, 0.009630274, -0.0051507815, 0.0012966477, -0.009515627], "split_indices": [142, 40, 0, 138, 111, 140, 0, 0, 0, 141, 108, 141, 0, 0, 0, 140, 0, 1, 0, 1, 0, 7, 141, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1958.0, 96.0, 1755.0, 203.0, 1651.0, 104.0, 96.0, 107.0, 1465.0, 186.0, 1341.0, 124.0, 96.0, 90.0, 1228.0, 113.0, 1087.0, 141.0, 999.0, 88.0, 410.0, 589.0, 266.0, 144.0, 334.0, 255.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0021419278, 0.010031267, -0.060564708, -0.0031405021, 0.025253052, 0.0077416934, -0.022458065, 0.006355778, -0.0129783545, -0.007697111, 0.014077077, 0.004896118, -0.02058974, -0.0063630156, 0.015952958, 0.016785499, -0.06124253, -0.0030651186, 0.015504333, 0.0060137925, -0.100722685, 0.01712589, -0.013852133, -0.0054579237, -0.017053971, -0.00083538, 0.013249795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [1.027026, 5.8900027, 5.2504363, 2.1034138, 0.0, 0.0, 0.0, 3.0732715, 0.0, 3.6765816, 0.0, 2.4113388, 0.0, 1.6400597, 0.0, 2.4920096, 1.8353822, 2.1715884, 0.0, 0.0, 0.93104124, 2.0312946, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3550751, 1.4672666, 1.5459449, 0.025253052, 0.0077416934, -0.022458065, 1.4715632, -0.0129783545, 0.8157872, 0.014077077, 0.6897765, -0.02058974, 1.3310086, 0.015952958, 0.4292953, -0.30769232, 0.45268637, 0.015504333, 0.0060137925, 0.5752562, 0.3670953, -0.013852133, -0.0054579237, -0.017053971, -0.00083538, 0.013249795], "split_indices": [119, 139, 138, 138, 0, 0, 0, 138, 0, 142, 0, 142, 0, 138, 0, 139, 1, 143, 0, 0, 139, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1844.0, 232.0, 1749.0, 95.0, 126.0, 106.0, 1627.0, 122.0, 1473.0, 154.0, 1385.0, 88.0, 1291.0, 94.0, 908.0, 383.0, 794.0, 114.0, 94.0, 289.0, 691.0, 103.0, 174.0, 115.0, 566.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.006728931, 0.012310848, -0.011061004, 0.002123561, 0.015013881, 0.009776711, -0.015024367, -0.0148245925, 0.043185487, 0.005453275, -0.013468626, 0.11596711, -0.011873894, -0.015502271, 0.01810699, 0.021289568, 0.03304407, 0.0134487, -0.054842245, 0.0049328813, -0.01663226, 0.012533647, -0.012573319, -0.004592462, -0.017939752, -0.02650648, 0.021335116, -0.015568318, 0.012151473, -0.0038095908, -0.013907385, -0.0031560776, 0.008902177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, 19, -1, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [1.3558007, 2.774487, 0.0, 2.1456041, 0.0, 1.4399679, 0.0, 2.4524143, 2.977433, 3.1759632, 0.0, 6.4978647, 2.6601987, 2.3762507, 0.0, 3.395992, 0.0, 0.0, 2.0466516, 4.4491735, 0.0, 0.0, 0.0, 4.439495, 0.0, 1.5074078, 0.0, 0.0, 0.0, 1.2649043, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 19, 19, 23, 23, 25, 25, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, 20, -1, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.4310507, 1.137993, -0.011061004, 1.5438342, 0.015013881, 1.0, -0.015024367, 1.3753743, 1.0, 0.4643542, -0.013468626, 1.4337494, 0.37930202, 0.47078556, 0.01810699, 0.54947865, 0.03304407, 0.0134487, 1.4521428, 0.3870685, -0.01663226, 0.012533647, -0.012573319, 0.5291029, -0.017939752, 0.29196817, 0.021335116, -0.015568318, 0.012151473, 1.0, -0.013907385, -0.0031560776, 0.008902177], "split_indices": [143, 139, 0, 138, 0, 71, 0, 138, 121, 139, 0, 138, 143, 140, 0, 141, 0, 0, 138, 142, 0, 0, 0, 140, 0, 142, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1976.0, 94.0, 1840.0, 136.0, 1752.0, 88.0, 1009.0, 743.0, 863.0, 146.0, 320.0, 423.0, 771.0, 92.0, 222.0, 98.0, 96.0, 327.0, 679.0, 92.0, 130.0, 92.0, 233.0, 94.0, 590.0, 89.0, 106.0, 127.0, 491.0, 99.0, 378.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003809736, -0.0059946105, 0.070081234, 0.004936081, -0.019560868, -0.0071141096, 0.01776347, -0.0049243704, 0.018132864, 0.0069156657, -0.07351666, 0.050301734, -0.029336058, -0.016473955, 0.0028294579, 0.021434003, 0.021754626, -0.0076897256, -0.018561779, -0.041510552, 0.0809327, -0.0340948, 0.0098333685, 0.0028342525, -0.015257697, 0.0014305125, 0.019870868, 0.007898963, -0.05775155, -0.012585125, -0.026443966, -0.008399544, 0.0025942612], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [1.3410816, 3.7265584, 4.0402603, 2.9568279, 0.0, 0.0, 0.0, 1.307538, 0.0, 2.1594815, 2.201142, 3.0174804, 2.5304282, 0.0, 0.0, 1.9961486, 0.0, 1.839308, 0.0, 2.009408, 2.1501126, 1.4071604, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9274355, 0.0, 0.89844775, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [0.9863768, 1.0675184, 1.1045161, 0.8941447, -0.019560868, -0.0071141096, 0.01776347, 2.0, 0.018132864, 1.0, 4.0, 1.0, 0.715507, -0.016473955, 0.0028294579, 1.0, 0.021754626, 0.5044889, -0.018561779, 0.54947865, 0.46228623, 0.14165041, 0.0098333685, 0.0028342525, -0.015257697, 0.0014305125, 0.019870868, 0.007898963, -0.15384616, -0.012585125, 1.0, -0.008399544, 0.0025942612], "split_indices": [142, 140, 140, 139, 0, 0, 0, 0, 0, 122, 0, 74, 143, 0, 0, 97, 0, 139, 0, 141, 143, 140, 0, 0, 0, 0, 0, 0, 1, 0, 39, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1798.0, 266.0, 1700.0, 98.0, 115.0, 151.0, 1610.0, 90.0, 1373.0, 237.0, 625.0, 748.0, 125.0, 112.0, 533.0, 92.0, 657.0, 91.0, 259.0, 274.0, 526.0, 131.0, 159.0, 100.0, 175.0, 99.0, 91.0, 435.0, 137.0, 298.0, 142.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015221064, 0.0070175813, -0.009794943, -0.0037135894, 0.02128506, 0.004749273, -0.016669504, 0.011283758, -0.0110456515, -0.0025961841, 0.08818313, 0.01108907, -0.012322194, -0.0029249296, 0.026198316, -0.011549088, 0.12147705, 0.011808574, -0.08621704, -0.0022087572, 0.029864196, -0.016026806, 0.015766598, -0.00073109596, -0.020178651, 0.004813932, -0.0059903907], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1], "loss_changes": [1.7004436, 4.19015, 0.0, 2.4868598, 0.0, 1.290317, 0.0, 1.7312564, 0.0, 2.2681916, 5.0616207, 3.0837426, 0.0, 0.0, 0.0, 1.7859267, 5.3412695, 3.1667974, 2.225068, 0.0, 0.0, 1.8441025, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1], "split_conditions": [1.1389784, 1.137993, -0.009794943, 0.94258624, 0.02128506, 1.0, -0.016669504, 1.0, -0.0110456515, 0.76996374, 0.53462654, 1.0, -0.012322194, -0.0029249296, 0.026198316, 0.5325959, 0.37602052, 0.5153114, 0.1923077, -0.0022087572, 0.029864196, 0.23357868, 0.015766598, -0.00073109596, -0.020178651, 0.004813932, -0.0059903907], "split_indices": [143, 139, 0, 139, 0, 117, 0, 42, 0, 142, 143, 61, 0, 0, 0, 140, 141, 142, 1, 0, 0, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1897.0, 168.0, 1803.0, 94.0, 1714.0, 89.0, 1622.0, 92.0, 1374.0, 248.0, 1234.0, 140.0, 148.0, 100.0, 1024.0, 210.0, 780.0, 244.0, 116.0, 94.0, 655.0, 125.0, 145.0, 99.0, 266.0, 389.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0036284216, -0.002324174, 0.009795046, 0.005616948, -0.015394232, -0.0042888634, 0.015663715, -0.012339779, 0.01275851, 0.0051303385, -0.016725553, -0.0049840175, 0.0150102815, 0.006827767, -0.012216444, -0.006302252, 0.017810816, 0.008930227, -0.011474048, -0.0012057602, 0.021284359], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, -1, 19, -1, -1, -1], "loss_changes": [1.1633475, 2.346632, 0.0, 2.770551, 0.0, 1.8452448, 0.0, 4.4330783, 0.0, 2.1583989, 0.0, 1.9045355, 0.0, 2.8111439, 0.0, 1.9177204, 0.0, 4.356734, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, -1, 20, -1, -1, -1], "split_conditions": [1.2075883, 1.0653116, 0.009795046, 1.0, -0.015394232, 5.0, 0.015663715, 0.8362515, 0.01275851, 0.7821551, -0.016725553, 0.6787581, 0.0150102815, 0.6719101, -0.012216444, 0.5112663, 0.017810816, 0.52396023, -0.011474048, -0.0012057602, 0.021284359], "split_indices": [141, 141, 0, 125, 0, 0, 0, 143, 0, 142, 0, 141, 0, 140, 0, 141, 0, 139, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1949.0, 123.0, 1852.0, 97.0, 1738.0, 114.0, 1638.0, 100.0, 1472.0, 166.0, 1376.0, 96.0, 1250.0, 126.0, 1161.0, 89.0, 1018.0, 143.0, 923.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [0.00075100176, 0.0068561058, -0.013084049, -0.0028052784, 0.018141134, -0.010804434, 0.01198948, 0.000516738, -0.015077531, -0.012076178, 0.0152336955, 0.022421448, -0.03774976, -0.0002070039, 0.016327722, -0.09978487, 0.013530847, -0.042004578, 0.052509107, -0.022951867, -0.023451317, 0.09036817, -0.07116094, 0.00081164244, -0.008867978, 0.018821143, -0.002628577, -0.009040064, 0.0038789678, 0.02175949, -0.0038927253, -0.0013971141, -0.014662787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6670129, 3.3442214, 0.0, 1.8442327, 0.0, 2.795296, 0.0, 3.1201546, 0.0, 1.3347163, 0.0, 2.0494654, 2.7485557, 1.2206866, 0.0, 4.0474668, 3.0780432, 0.7228773, 2.6196995, 1.0369337, 0.0, 4.0795584, 0.97108567, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4310507, 1.1228671, -0.013084049, 5.0, 0.018141134, 0.9504941, 0.01198948, 0.79489726, -0.015077531, 0.31830794, 0.0152336955, 0.4201946, 0.52144444, 0.25132048, 0.016327722, 0.47743124, 1.0, 0.1612819, 0.24919115, 1.0, -0.023451317, 0.66575265, 1.0, 0.00081164244, -0.008867978, 0.018821143, -0.002628577, -0.009040064, 0.0038789678, 0.02175949, -0.0038927253, -0.0013971141, -0.014662787], "split_indices": [143, 142, 0, 0, 0, 143, 0, 143, 0, 143, 0, 140, 143, 141, 0, 140, 106, 142, 140, 122, 0, 143, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1983.0, 92.0, 1879.0, 104.0, 1764.0, 115.0, 1632.0, 132.0, 1507.0, 125.0, 643.0, 864.0, 554.0, 89.0, 391.0, 473.0, 309.0, 245.0, 249.0, 142.0, 248.0, 225.0, 149.0, 160.0, 90.0, 155.0, 119.0, 130.0, 125.0, 123.0, 128.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036550032, -0.06595516, 0.008331316, 0.0035739054, -0.016059546, -0.009570575, 0.068514064, -0.06056542, 0.01989578, 0.15271725, -0.006776213, -0.026567591, -0.020963278, -0.02518292, 0.06782618, 0.006625091, 0.030796362, -0.057741217, 0.00774574, -0.0743238, 0.0074805105, -0.0021508266, 0.11600657, 0.0024928418, -0.013173565, 0.004127677, -0.021272408, -0.001127077, 0.018630512, -0.004194564, 0.005486013], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, -1, -1, 23, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.5457718, 3.2145402, 1.8703406, 0.0, 0.0, 2.0105221, 4.567007, 2.4833026, 1.8322232, 3.3021998, 0.0, 1.2938914, 0.0, 2.1471996, 1.7690133, 0.0, 0.0, 1.877948, 0.0, 3.1813092, 0.0, 0.0, 2.3889596, 0.0, 0.0, 0.43709618, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 19, 19, 22, 22, 25, 25], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, -1, -1, 24, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [-1.0, 0.43434185, 0.7920647, 0.0035739054, -0.016059546, 1.0, 1.1389784, 1.4265472, 1.0, 0.9242993, -0.006776213, 0.5044889, -0.020963278, 1.0, 1.0, 0.006625091, 0.030796362, 0.30174637, 0.00774574, 0.32991913, 0.0074805105, -0.0021508266, 0.33484554, 0.0024928418, -0.013173565, 0.18909962, -0.021272408, -0.001127077, 0.018630512, -0.004194564, 0.005486013], "split_indices": [0, 142, 143, 0, 0, 16, 143, 138, 39, 139, 0, 139, 0, 42, 69, 0, 0, 140, 0, 141, 0, 0, 140, 0, 0, 140, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 334.0, 1736.0, 161.0, 173.0, 1338.0, 398.0, 490.0, 848.0, 246.0, 152.0, 399.0, 91.0, 437.0, 411.0, 158.0, 88.0, 307.0, 92.0, 293.0, 144.0, 144.0, 267.0, 145.0, 162.0, 187.0, 106.0, 95.0, 172.0, 98.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0011375232, 0.0070661684, -0.009688607, -0.0048112343, 0.01456616, 0.007672326, -0.1155857, -0.004635034, 0.014259748, -0.0017382203, -0.02116543, 0.0045608445, -0.009551197, -0.004785886, 0.0127445785, 0.0111176325, -0.0105043175, -0.0040979767, 0.0152115645, 0.014828831, -0.010099373, -0.0003603072, 0.016127134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [1.202393, 3.211646, 0.0, 2.4849994, 0.0, 2.6818252, 1.7170372, 1.2368264, 0.0, 0.0, 0.0, 1.5436817, 0.0, 1.9914604, 0.0, 2.3127103, 0.0, 1.7844115, 0.0, 2.1971607, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.6878304, 1.0048928, -0.009688607, 0.83632547, 0.01456616, 0.7095425, 0.8806386, 0.6787581, 0.014259748, -0.0017382203, -0.02116543, 0.67889345, -0.009551197, 0.5112663, 0.0127445785, 0.52396023, -0.0105043175, 0.45865256, 0.0152115645, 0.43165883, -0.010099373, -0.0003603072, 0.016127134], "split_indices": [138, 139, 0, 142, 0, 142, 140, 141, 0, 0, 0, 140, 0, 141, 0, 139, 0, 143, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1951.0, 118.0, 1797.0, 154.0, 1615.0, 182.0, 1480.0, 135.0, 90.0, 92.0, 1344.0, 136.0, 1249.0, 95.0, 1078.0, 171.0, 973.0, 105.0, 814.0, 159.0, 723.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.009023905, 0.00025013808, -0.08469354, -0.01106007, 0.01975002, 0.0046357973, -0.024119198, -0.0011674893, -0.07923382, -0.009727825, 0.0123306755, -0.025866767, 0.0044041327, 0.0027834657, -0.011374328, -0.008656954, 0.015670916, -0.020591995, 0.011025138, -0.00046738988, -0.0751366, -0.018152501, 0.013980766, -0.01644957, 0.0017998221, -0.0056229117, 0.0022170509], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [1.4526505, 4.113853, 4.6351156, 1.1761782, 0.0, 0.0, 0.0, 1.6228193, 4.8884625, 1.8544494, 0.0, 0.0, 0.0, 2.23996, 0.0, 1.6803037, 0.0, 1.1811128, 0.0, 1.9498928, 2.4135094, 1.0716839, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2075883, 1.4672666, 0.83632547, 0.01975002, 0.0046357973, -0.024119198, 0.86711395, 0.97633713, 0.68592155, 0.0123306755, -0.025866767, 0.0044041327, 0.69104004, -0.011374328, 3.1923077, 0.015670916, 1.3262159, 0.011025138, 1.0, 1.0, 1.0, 0.013980766, -0.01644957, 0.0017998221, -0.0056229117, 0.0022170509], "split_indices": [119, 141, 138, 142, 0, 0, 0, 143, 142, 141, 0, 0, 0, 140, 0, 1, 0, 138, 0, 105, 39, 17, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1844.0, 226.0, 1744.0, 100.0, 123.0, 103.0, 1523.0, 221.0, 1425.0, 98.0, 90.0, 131.0, 1272.0, 153.0, 1184.0, 88.0, 1076.0, 108.0, 786.0, 290.0, 698.0, 88.0, 148.0, 142.0, 359.0, 339.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.00037707057, 0.0073681907, -0.012084394, 0.013502351, -0.009099358, 0.0022836186, 0.013809713, 0.013253008, -0.010857257, 0.025434725, -0.06940863, 0.009945994, 0.10109647, 0.0024581302, -0.016533654, 0.031664696, -0.05552601, 0.02612443, -0.010068975, 0.0027970697, 0.0140333045, 0.002016129, -0.01879788, -0.007070134, 0.018120544, -0.00043316555, 0.0075204917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, 23, -1, -1, -1, -1, 25, -1, -1], "loss_changes": [1.7407043, 1.171738, 0.0, 2.5551696, 0.0, 2.0392733, 0.0, 1.5366219, 0.0, 1.5586327, 1.7671869, 1.5698515, 7.3033314, 0.0, 0.0, 2.6005704, 2.756874, 0.0, 0.0, 0.7376943, 0.0, 0.0, 0.0, 0.0, 0.69466525, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, 24, -1, -1, -1, -1, 26, -1, -1], "split_conditions": [1.0, 1.6878304, -0.012084394, 1.0048928, -0.009099358, 0.87661415, 0.013809713, 1.0, -0.010857257, 0.6913856, 0.25712252, 0.5325959, 1.0, 0.0024581302, -0.016533654, 0.42670286, 0.1923077, 0.02612443, -0.010068975, -1.0, 0.0140333045, 0.002016129, -0.01879788, -0.007070134, 1.0, -0.00043316555, 0.0075204917], "split_indices": [43, 138, 0, 139, 0, 142, 0, 40, 0, 139, 143, 140, 105, 0, 0, 140, 1, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1942.0, 112.0, 1828.0, 114.0, 1677.0, 151.0, 1526.0, 151.0, 1330.0, 196.0, 1104.0, 226.0, 99.0, 97.0, 829.0, 275.0, 126.0, 100.0, 655.0, 174.0, 175.0, 100.0, 113.0, 542.0, 389.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042329296, 0.0047101257, -0.07546632, -0.0065778852, 0.021341829, 0.0053712684, -0.023197168, 0.003696116, -0.019921545, -0.008896033, 0.102808535, 0.000931368, -0.01505141, -0.00375348, 0.025576701, 0.019413142, -0.086009964, 0.0075875316, 0.012573895, -0.0015318042, -0.014683788, 0.035482217, -0.01394622, 0.057667084, -0.006616216, 0.0020270594, 0.018109841], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, -1], "loss_changes": [1.3135853, 4.316009, 4.6499586, 3.4397779, 0.0, 0.0, 0.0, 2.0592637, 0.0, 2.0375044, 3.9928083, 2.1997502, 0.0, 0.0, 0.0, 1.4195676, 1.0320096, 4.167536, 0.0, 0.0, 0.0, 1.925742, 0.0, 3.2357447, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, -1], "split_conditions": [1.0, 1.3550751, 1.4672666, 1.5732228, 0.021341829, 0.0053712684, -0.023197168, 1.4715632, -0.019921545, 0.7977738, 0.79171604, 1.0, -0.01505141, -0.00375348, 0.025576701, 0.6526074, 1.0, 0.5427232, 0.012573895, -0.0015318042, -0.014683788, 0.47078556, -0.01394622, 0.39630094, -0.006616216, 0.0020270594, 0.018109841], "split_indices": [119, 139, 138, 138, 0, 0, 0, 138, 0, 142, 142, 58, 0, 0, 0, 142, 17, 139, 0, 0, 0, 140, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1832.0, 230.0, 1738.0, 94.0, 126.0, 104.0, 1650.0, 88.0, 1464.0, 186.0, 1369.0, 95.0, 97.0, 89.0, 1129.0, 240.0, 1016.0, 113.0, 111.0, 129.0, 854.0, 162.0, 701.0, 153.0, 538.0, 163.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.003127518, -0.006942887, 0.055493634, 0.005384823, -0.022803772, -0.0063709416, 0.15691502, -0.0045030117, 0.015703095, 0.032268092, 6.1246897e-06, -0.015336788, 0.016319, -0.003173884, -0.017498964, 0.0064707566, -0.013373953, -0.0026091856, 0.010626367, -0.019068116, 0.07495962, -0.0046210615, -0.012117693, 0.01587028, -0.0013910672, -0.0023686376, 0.010993102], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1], "loss_changes": [1.0953019, 4.7479835, 4.050063, 2.474096, 0.0, 0.0, 4.7061815, 2.814143, 0.0, 0.0, 0.0, 2.8253803, 0.0, 1.7025182, 0.0, 1.1407977, 0.0, 1.4733111, 0.0, 1.4043629, 1.5033411, 1.8214319, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1], "split_conditions": [0.9014308, 1.0044936, 1.1045161, 0.7821551, -0.022803772, -0.0063709416, 1.1785235, 0.78604263, 0.015703095, 0.032268092, 6.1246897e-06, 0.68000907, 0.016319, 0.6400306, -0.017498964, 3.1923077, -0.013373953, 0.5275764, 0.010626367, 0.45285252, 0.4611063, 0.3898465, -0.012117693, 0.01587028, -0.0013910672, -0.0023686376, 0.010993102], "split_indices": [142, 140, 140, 142, 0, 0, 143, 140, 0, 0, 0, 142, 0, 140, 0, 1, 0, 142, 0, 142, 140, 142, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1742.0, 335.0, 1650.0, 92.0, 154.0, 181.0, 1549.0, 101.0, 88.0, 93.0, 1455.0, 94.0, 1352.0, 103.0, 1259.0, 93.0, 1154.0, 105.0, 952.0, 202.0, 834.0, 118.0, 104.0, 98.0, 715.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.002551767, 0.0082139, -0.009370446, -0.0015322182, 0.012465099, 0.007499157, -0.010805123, -0.0026627039, 0.012633867, 0.007053444, -0.010239613, -0.0044921613, 0.009031765, 0.013992692, -0.0759376, -0.0057877223, 0.010957831, -0.024409125, 0.0027541548, 0.013217864, -0.007973675, 0.0091153905, -0.0020282478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, -1, -1], "loss_changes": [1.1281819, 2.218553, 0.0, 1.735472, 0.0, 2.0082898, 0.0, 1.4845463, 0.0, 1.3420249, 0.0, 1.6191274, 0.0, 1.8415645, 4.384901, 1.1341941, 0.0, 0.0, 0.0, 1.6761876, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, -1, -1], "split_conditions": [1.6878304, 1.0, -0.009370446, 0.9504941, 0.012465099, 1.5096489, -0.010805123, 0.736883, 0.012633867, 0.6407167, -0.010239613, 0.4802205, 0.009031765, 0.4339572, 1.0, 0.37499917, 0.010957831, -0.024409125, 0.0027541548, 1.0, -0.007973675, 0.0091153905, -0.0020282478], "split_indices": [138, 125, 0, 143, 0, 138, 0, 143, 0, 142, 0, 142, 0, 143, 59, 139, 0, 0, 0, 53, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1955.0, 115.0, 1804.0, 151.0, 1663.0, 141.0, 1532.0, 131.0, 1396.0, 136.0, 1226.0, 170.0, 974.0, 252.0, 807.0, 167.0, 96.0, 156.0, 642.0, 165.0, 193.0, 449.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011326795, 0.0073713413, -0.07290975, -0.00036364092, 0.01378147, 0.0050247763, -0.018120345, 0.00638948, -0.010861223, -0.0009114912, 0.013172286, 0.010487531, -0.073376715, -0.00071926496, 0.01488915, 0.007882422, -0.02040271, 0.016164081, -0.07692045, 0.000561423, 0.009853835, 0.00096082187, -0.018041557, -0.015737936, 0.009876172, 0.0018297635, -0.008911334], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1], "loss_changes": [1.2561904, 1.8565184, 2.9075065, 1.2697747, 0.0, 0.0, 0.0, 1.496116, 0.0, 1.2762208, 0.0, 2.0706723, 4.1758738, 1.588866, 0.0, 0.0, 0.0, 1.2993951, 2.0059865, 1.3605119, 0.0, 0.0, 0.0, 1.8205857, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1], "split_conditions": [1.0653116, 0.97633713, 1.0, 0.83632547, 0.01378147, 0.0050247763, -0.018120345, 0.754598, -0.010861223, 0.6574953, 0.013172286, 1.4337494, 1.4053268, 1.3516413, 0.01488915, 0.007882422, -0.02040271, 1.0, 0.1923077, 1.0, 0.009853835, 0.00096082187, -0.018041557, 1.0, 0.009876172, 0.0018297635, -0.008911334], "split_indices": [141, 142, 69, 142, 0, 0, 0, 142, 0, 141, 0, 138, 138, 138, 0, 0, 0, 105, 1, 113, 0, 0, 0, 109, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1840.0, 218.0, 1737.0, 103.0, 102.0, 116.0, 1635.0, 102.0, 1545.0, 90.0, 1335.0, 210.0, 1235.0, 100.0, 97.0, 113.0, 1011.0, 224.0, 850.0, 161.0, 122.0, 102.0, 729.0, 121.0, 498.0, 231.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.003554034, -0.04899449, 0.008608051, -0.015420469, 0.012601295, 0.07364714, -0.008867404, 0.08596675, -0.014072423, -0.0023093258, 0.017095001, -0.107989766, 0.011627818, -0.009597357, 0.026790712, -0.0017444432, -0.023396585, 0.013787999, -0.00957589, -0.03008769, 0.017525567, -0.0019802982, -0.014166551, 0.059813723, -0.049283545, -0.02124013, 0.020443926, -0.010086029, -0.012194969, -0.012557088, 0.007860324, 0.006509078, -0.0065251775], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, -1, 7, 9, 11, 13, -1, -1, -1, 15, 17, -1, -1, -1, -1, -1, 19, 21, -1, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.1412238, 2.825501, 1.8515017, 0.0, 3.093419, 3.2475266, 2.6084914, 6.1570263, 0.0, 0.0, 0.0, 2.5094397, 2.8483436, 0.0, 0.0, 0.0, 0.0, 0.0, 3.4538083, 2.571652, 0.0, 1.9146029, 0.0, 3.329178, 1.0567307, 1.8958457, 0.0, 0.99947155, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, 6, -1, 8, 10, 12, 14, -1, -1, -1, 16, 18, -1, -1, -1, -1, -1, 20, 22, -1, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.34615386, -0.34615386, -0.015420469, 1.0, 1.0, -0.1923077, 0.44721046, -0.014072423, -0.0023093258, 0.017095001, 0.5418762, -0.03846154, -0.009597357, 0.026790712, -0.0017444432, -0.023396585, 0.013787999, 0.93067384, 1.0, 0.017525567, 1.0, -0.014166551, 0.47509506, 0.35378668, 1.0, 0.020443926, 1.0, -0.012194969, -0.012557088, 0.007860324, 0.006509078, -0.0065251775], "split_indices": [89, 1, 1, 0, 115, 69, 1, 143, 0, 0, 0, 141, 1, 0, 0, 0, 0, 0, 140, 15, 0, 97, 0, 140, 141, 106, 0, 53, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 436.0, 1629.0, 161.0, 275.0, 345.0, 1284.0, 186.0, 89.0, 173.0, 172.0, 220.0, 1064.0, 93.0, 93.0, 128.0, 92.0, 153.0, 911.0, 820.0, 91.0, 655.0, 165.0, 284.0, 371.0, 182.0, 102.0, 241.0, 130.0, 89.0, 93.0, 102.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00022200747, -0.0074957637, 0.058606077, -0.0012094222, -0.011949524, 0.016979657, -0.0015250355, -0.009826263, 0.016113965, 0.0026496798, -0.08042308, -0.00940355, 0.014359648, 0.0038756987, -0.016590398, 0.01142262, -0.08147213, -0.016372792, 0.08394973, -0.001318402, -0.018302878, 0.02040716, -0.07321455, 0.0020676032, 0.01797153, -0.010384089, 0.051293693, -0.041400004, -0.013618081, 0.0012566434, 0.013177901, -0.007930613, 7.178826e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [0.88661116, 1.2982991, 1.8723667, 2.4425423, 0.0, 0.0, 0.0, 1.4603034, 0.0, 2.3937004, 2.5367174, 1.9481846, 0.0, 0.0, 0.0, 2.0300326, 2.018117, 1.5219841, 1.690584, 0.0, 0.0, 1.6962153, 0.5729269, 0.0, 0.0, 0.0, 1.103409, 0.3033402, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0570872, 1.0745199, 1.0426008, 0.9113018, -0.011949524, 0.016979657, -0.0015250355, 0.68592155, 0.016113965, 0.6968186, 0.7055078, 0.4672373, 0.014359648, 0.0038756987, -0.016590398, 0.3636057, 0.115384616, 0.24919115, 1.0, -0.001318402, -0.018302878, 0.09001305, 1.0, 0.0020676032, 0.01797153, -0.010384089, 0.2764516, 0.26694217, -0.013618081, 0.0012566434, 0.013177901, -0.007930613, 7.178826e-05], "split_indices": [143, 141, 142, 142, 0, 0, 0, 141, 0, 140, 140, 141, 0, 0, 0, 140, 1, 140, 115, 0, 0, 140, 127, 0, 0, 0, 141, 143, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1844.0, 228.0, 1746.0, 98.0, 91.0, 137.0, 1658.0, 88.0, 1409.0, 249.0, 1298.0, 111.0, 104.0, 145.0, 1007.0, 291.0, 728.0, 279.0, 174.0, 117.0, 442.0, 286.0, 168.0, 111.0, 88.0, 354.0, 190.0, 96.0, 239.0, 115.0, 100.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.007956995, 0.0028080635, 0.009661795, 0.011101159, -0.015672714, -0.0010005959, 0.015473649, 0.0083723795, -0.017493322, -0.0028194096, 0.015749808, 0.01425563, -0.12225483, 0.0055845627, 0.013222023, -0.004829155, -0.020104177, -0.0051692864, 0.06453369, 0.010715568, -0.01133997, 0.017405009, -0.002903369, 0.002655926, -0.0057055107], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, -1, -1, -1, -1], "loss_changes": [0.94816905, 2.5971286, 0.0, 3.243556, 0.0, 2.8056886, 0.0, 2.7254505, 0.0, 3.097795, 0.0, 1.3594056, 1.1071942, 0.7848056, 0.0, 0.0, 0.0, 1.8000283, 1.9572084, 0.9803224, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, -1, -1, -1, -1], "split_conditions": [1.354651, 1.6320093, 0.009661795, 0.9035947, -0.015672714, 0.9201908, 0.015473649, 0.7821551, -0.017493322, 0.65480655, 0.015749808, 0.58952457, 0.64421266, 0.52959335, 0.013222023, -0.004829155, -0.020104177, 0.45285252, 1.3278604, 1.0, -0.01133997, 0.017405009, -0.002903369, 0.002655926, -0.0057055107], "split_indices": [140, 138, 0, 139, 0, 141, 0, 142, 0, 139, 0, 139, 142, 142, 0, 0, 0, 142, 138, 58, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1963.0, 114.0, 1866.0, 97.0, 1721.0, 145.0, 1633.0, 88.0, 1519.0, 114.0, 1329.0, 190.0, 1238.0, 91.0, 98.0, 92.0, 1047.0, 191.0, 913.0, 134.0, 88.0, 103.0, 740.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.003019834, 0.0076983594, -0.007767435, -0.0053584017, 0.09643212, 0.0040180553, -0.016192574, 0.024308017, -0.0030516915, 0.011216188, -0.009368021, -0.0020733173, 0.014489879, 0.033001307, -0.02310157, -0.014737502, 0.017659364, 0.013966911, -0.127795, 0.05901888, -0.012634258, -0.0069683357, 0.014051932, -0.005122176, -0.019776708, 0.015762864, -0.0014191371, 0.02330859, -0.010009284, -0.006458902, 0.0051276023], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.7784662, 2.2580643, 0.0, 2.494212, 4.654207, 1.1273016, 0.0, 0.0, 0.0, 2.6524277, 0.0, 1.0016038, 0.0, 3.4891589, 3.2948217, 3.1444662, 0.0, 1.661178, 1.1894732, 1.6604269, 0.0, 1.5169041, 0.0, 0.0, 0.0, 0.0, 0.0, 0.99805766, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.6878304, 0.95011306, -0.007767435, 0.89691794, 1.0, 0.8103251, -0.016192574, 0.024308017, -0.0030516915, 0.7081009, -0.009368021, -0.03846154, 0.014489879, 0.605175, 0.45865256, 0.35231146, 0.017659364, 0.44288877, 0.4463277, 0.24578677, -0.012634258, 0.3401452, 0.014051932, -0.005122176, -0.019776708, 0.015762864, -0.0014191371, 0.13672182, -0.010009284, -0.006458902, 0.0051276023], "split_indices": [138, 140, 0, 141, 108, 139, 0, 0, 0, 141, 0, 1, 0, 143, 143, 143, 0, 140, 140, 139, 0, 141, 0, 0, 0, 0, 0, 142, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1949.0, 113.0, 1699.0, 250.0, 1603.0, 96.0, 116.0, 134.0, 1493.0, 110.0, 1358.0, 135.0, 509.0, 849.0, 382.0, 127.0, 627.0, 222.0, 230.0, 152.0, 538.0, 89.0, 106.0, 116.0, 98.0, 132.0, 406.0, 132.0, 98.0, 308.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00045728916, 0.008860526, -0.06899187, -0.0018053497, 0.020731391, 0.0029187265, -0.019422038, 0.0052206875, -0.013442183, -0.015751006, 0.037188184, 0.0021031946, -0.011728192, 0.022588288, 0.0057390574, -0.018027918, 0.010462809, -0.06127436, 0.08353626, -0.002176909, -0.012114286, -0.018435776, 0.023990102, 0.024167767, -0.0039104014, -0.02438593, 0.007365101, -0.009414287, 0.014345044, 0.00289157, -0.054619323, 0.00039512283, -0.012364892], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, 17, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [1.2057127, 3.9010406, 2.741747, 1.6296632, 0.0, 0.0, 0.0, 1.1135552, 0.0, 1.8181918, 3.9047575, 1.7605414, 0.0, 0.0, 2.9403894, 1.1653811, 0.0, 3.1798763, 5.061967, 1.0407513, 0.0, 0.0, 2.526085, 0.0, 0.0, 0.7702919, 0.0, 0.0, 0.0, 0.0, 1.2331464, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 22, 22, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, 18, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.3550751, 1.4672666, 1.5732228, 0.020731391, 0.0029187265, -0.019422038, 1.0, -0.013442183, 1.3753743, 0.32194895, 0.4292953, -0.011728192, 0.022588288, 1.0, 0.42914736, 0.010462809, 1.0, 1.0, 1.0, -0.012114286, -0.018435776, 0.5561432, 0.024167767, -0.0039104014, 0.16507967, 0.007365101, -0.009414287, 0.014345044, 0.00289157, 1.0, 0.00039512283, -0.012364892], "split_indices": [119, 139, 138, 138, 0, 0, 0, 71, 0, 138, 143, 139, 0, 0, 109, 140, 0, 53, 105, 62, 0, 0, 140, 0, 0, 142, 0, 0, 0, 0, 39, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1843.0, 223.0, 1749.0, 94.0, 125.0, 98.0, 1661.0, 88.0, 1003.0, 658.0, 853.0, 150.0, 94.0, 564.0, 713.0, 140.0, 303.0, 261.0, 618.0, 95.0, 124.0, 179.0, 114.0, 147.0, 478.0, 140.0, 90.0, 89.0, 173.0, 305.0, 165.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "144", "num_nodes": "33", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.5803202E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "144", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}