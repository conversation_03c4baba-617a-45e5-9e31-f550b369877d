{"uid": "f3e3d810-d099-45f4-b38f-a66b4a17b606", "name": "2_Optuna_LightGBM", "preprocessing": [{"scale_y": {"scale": [0.8211706673631429], "mean": [0.4207229727151287], "var": [0.6743212649376294], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8346999263942918], "mean": [0.42996936912635636], "var": [0.6967239671226361], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8351871699359255], "mean": [0.4267552162709529], "var": [0.6975376088255804], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.81886101693931], "mean": [0.41553713076390103], "var": [0.670533365062881], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8337176677697381], "mean": [0.4309213625254967], "var": [0.6950851495514113], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8134753858563379], "mean": [0.41305252889866456], "var": [0.6617422033941178], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8218611565977175], "mean": [0.4247006703486496], "var": [0.6754557607241379], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8298397500459297], "mean": [0.42137784377512366], "var": [0.6886340107562912], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8160733190945939], "mean": [0.4163287823768878], "var": [0.6659756621380668], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8086812963234827], "mean": [0.414534039849729], "var": [0.6539654390234284], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}], "learners": [{"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "a6b9f4fb-759a-419f-bb66-3417b52d3a0b", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_0"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "d4f854b6-fd65-4618-9dca-41cb285a799d", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_1"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "3f0d182e-ef0a-4cc5-8ea7-747c5d2d244e", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_2"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "b530d23e-67e6-4fc4-a8cc-f90a08d66e7e", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_3"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "285d37bb-1020-459c-956b-28a76e615acc", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_4"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "b8be7218-5513-4c9a-b007-b3a1bb3bab01", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_5"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "488cfc20-a094-409b-9b69-748ad33a24ad", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_6"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "3c343d67-6af4-41c7-95a1-b10f5f468eb0", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_7"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "eb993e51-0483-4f3b-a20e-a02dd4327bc5", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_8"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "dfa14105-5736-4212-bc2a-392c1a01a683", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_9"}], "params": {"additional": {"max_rounds": 10000, "early_stopping_rounds": 50, "max_rows_limit": null, "max_cols_limit": null}, "preprocessing": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}, "validation_strategy": {"validation_type": "kfold", "k_folds": 10, "shuffle": true, "X_path": "AutoML_model2_ads/X.data", "y_path": "AutoML_model2_ads/y.data", "results_path": "AutoML_model2_ads"}, "learner": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "automl_random_state": 1234, "ml_task": "regression", "explain_level": 0, "name": "2_Optuna_LightGBM", "status": "initialized", "final_loss": null, "train_time": null, "data_type": "original", "optuna_time_budget": 1, "optuna_init_params": {"original_LightGBM": {"learning_rate": 0.1, "num_leaves": 1598, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "bagging_freq": 7, "min_data_in_leaf": 36, "extra_trees": false, "metric": "l2", "custom_eval_metric_name": null, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "seed": 1234}}, "optuna_verbose": true, "max_time_for_learner": 3600}, "saved": ["2_Optuna_LightGBM/learner_fold_0.lightgbm", "2_Optuna_LightGBM/learner_fold_1.lightgbm", "2_Optuna_LightGBM/learner_fold_2.lightgbm", "2_Optuna_LightGBM/learner_fold_3.lightgbm", "2_Optuna_LightGBM/learner_fold_4.lightgbm", "2_Optuna_LightGBM/learner_fold_5.lightgbm", "2_Optuna_LightGBM/learner_fold_6.lightgbm", "2_Optuna_LightGBM/learner_fold_7.lightgbm", "2_Optuna_LightGBM/learner_fold_8.lightgbm", "2_Optuna_LightGBM/learner_fold_9.lightgbm"], "predictions_fname": "2_Optuna_LightGBM/predictions_out_of_folds.csv", "metric_name": "mse", "final_loss": "74.02963179809588", "train_time": 14.304166793823242, "is_stacked": false, "joblib_version": "1.3.2"}