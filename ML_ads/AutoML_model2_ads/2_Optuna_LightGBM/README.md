# Summary of 2_Optuna_LightGBM

[<< Go back](../README.md)


## LightGBM
- **n_jobs**: -1
- **objective**: regression
- **num_leaves**: 1598
- **learning_rate**: 0.1
- **feature_fraction**: 0.8613105322932351
- **bagging_fraction**: 0.970697557159987
- **min_data_in_leaf**: 36
- **metric**: l2
- **custom_eval_metric_name**: None
- **lambda_l1**: 2.840098794801191e-06
- **lambda_l2**: 3.0773599420974e-06
- **bagging_freq**: 7
- **extra_trees**: False
- **num_boost_round**: 1000
- **early_stopping_rounds**: 50
- **cat_feature**: []
- **feature_pre_filter**: False
- **explain_level**: 0

## Validation
 - **validation_type**: kfold
 - **k_folds**: 10
 - **shuffle**: True

## Optimized metric
mse

## Training time

14.3 seconds

### Metric details:
| Metric   |        Score |
|:---------|-------------:|
| MAE      |  2.08332     |
| MSE      | 74.0296      |
| RMSE     |  8.60405     |
| R2       | -0.00952521  |
| MAPE     |  1.67028e+15 |



## Learning curves
![Learning curves](learning_curves.png)
## True vs Predicted

![True vs Predicted](true_vs_predicted.png)


## Predicted vs Residuals

![Predicted vs Residuals](predicted_vs_residuals.png)



[<< Go back](../README.md)
