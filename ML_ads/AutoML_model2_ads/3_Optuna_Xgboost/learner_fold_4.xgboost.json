{"learner": {"attributes": {"best_iteration": "19", "best_score": "0.69252"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "70"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.00012998824, 0.15165845, -0.2420874, 0.1012122, 0.049549755, -0.27071032, -0.006332416, -0.098933555, 0.17290257, -0.23999017, -0.045316942, -0.0014051927, -0.017246157, 0.014183364, 0.24276136, -0.1961223, -0.03692384, 0.011721539, -0.0055657118, 0.17265335, 0.04435469, -0.012135435, -0.23453756, 0.06757141, 0.28232625, -0.01056186, -0.032298196, -0.0049668616, 0.02156641, 0.019210322, 0.036106627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, -1, -1, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [76.01155, 22.098024, 4.078026, 15.941206, 0.0, 3.8507538, 0.0, 1.8286643, 9.069933, 3.3338661, 0.0, 0.0, 0.0, 1.7989519, 7.9955444, 1.2609062, 0.0, 0.0, 0.0, 4.8518724, 0.0, 0.0, 3.3066263, 3.7329144, 1.4634571, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 19, 19, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, -1, -1, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.049549755, 1.0, -0.006332416, 1.0, 1.0, 1.0, -0.045316942, -0.0014051927, -0.017246157, 1.0, 2.0, 1.0, -0.03692384, 0.011721539, -0.0055657118, 1.0, 0.04435469, -0.012135435, 1.0, 1.0, 1.0, -0.01056186, -0.032298196, -0.0049668616, 0.02156641, 0.019210322, 0.036106627], "split_indices": [137, 125, 71, 17, 0, 40, 0, 53, 71, 116, 0, 0, 0, 122, 0, 97, 0, 0, 0, 109, 0, 0, 122, 12, 108, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1274.0, 797.0, 1111.0, 163.0, 687.0, 110.0, 293.0, 818.0, 588.0, 99.0, 136.0, 157.0, 250.0, 568.0, 439.0, 149.0, 101.0, 149.0, 421.0, 147.0, 149.0, 290.0, 215.0, 206.0, 118.0, 172.0, 120.0, 95.0, 96.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-6.452429e-05, -0.20685638, 0.1348576, -0.17326887, -0.042317227, 0.09353645, 0.042068414, -0.2178129, -0.06067769, -0.10201267, 0.16245352, -0.1773962, -0.03747709, -0.016054677, 0.0038192663, -0.002626179, -0.017309396, 0.23126443, -0.021042243, -0.007860862, -0.21973372, 0.13423246, 0.05145457, -0.018440751, 0.012401483, -0.29012248, -0.0066158227, 0.29090086, -0.06221186, -0.022988856, -0.036130797, 0.015683746, 0.047182135, -0.009623333, -0.0033698597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, 23, -1, 25, 27, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [57.308243, 5.8923264, 14.680674, 3.5207176, 0.0, 14.635666, 0.0, 3.1908894, 1.9649446, 1.5238042, 10.13909, 1.6729679, 0.0, 0.0, 0.0, 0.0, 0.0, 16.052605, 5.1897063, 0.0, 3.0267944, 13.38783, 0.0, 0.0, 0.0, 0.8232517, 0.0, 5.8696632, 0.18722188, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 17, 17, 18, 18, 20, 20, 21, 21, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, 24, -1, 26, 28, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.042317227, 1.0, 0.042068414, 1.0, 1.0, -0.03846154, 0.15384616, 0.0, -0.03747709, -0.016054677, 0.0038192663, -0.002626179, -0.017309396, -0.115384616, 1.0, -0.007860862, 1.3846154, 1.0, 0.05145457, -0.018440751, 0.012401483, 1.0, -0.0066158227, 1.0, 1.0, -0.022988856, -0.036130797, 0.015683746, 0.047182135, -0.009623333, -0.0033698597], "split_indices": [2, 40, 125, 93, 0, 17, 0, 0, 13, 1, 1, 1, 0, 0, 0, 0, 0, 1, 69, 0, 1, 122, 0, 0, 0, 23, 0, 61, 69, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 811.0, 1243.0, 702.0, 109.0, 1086.0, 157.0, 503.0, 199.0, 283.0, 803.0, 400.0, 103.0, 99.0, 100.0, 137.0, 146.0, 584.0, 219.0, 120.0, 280.0, 435.0, 149.0, 103.0, 116.0, 192.0, 88.0, 242.0, 193.0, 104.0, 88.0, 139.0, 103.0, 88.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054392777, -0.15804043, 0.14453585, -0.02912787, -0.22372715, 0.097871505, 0.04276749, 0.030425625, -0.017020607, -0.19754432, -0.038136397, -0.047387067, 0.16575378, 0.010448262, -0.008374554, -0.16294406, -0.029588187, -0.013344157, 0.0063451114, 0.09974635, 0.28420547, -0.19636367, -0.0071904436, 0.16073206, -0.002651313, 0.00867256, 0.045015495, -0.23738512, -0.011623762, 0.029386422, 0.00505537, -0.029460723, -0.017257232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, -1, -1, 21, -1, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [47.46635, 8.704945, 13.820271, 2.9153905, 2.8107414, 8.854714, 0.0, 2.0630624, 0.0, 1.9870605, 0.0, 2.7279038, 4.785038, 0.0, 0.0, 1.3143625, 0.0, 0.0, 0.0, 3.026111, 7.176996, 1.0386553, 0.0, 3.887094, 0.0, 0.0, 0.0, 0.7751217, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 19, 19, 20, 20, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, -1, -1, 22, -1, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.04276749, 1.0, -0.017020607, 1.0, -0.038136397, 1.0, 1.0, 0.010448262, -0.008374554, 1.0, -0.029588187, -0.013344157, 0.0063451114, 1.0, 1.0, 1.0, -0.0071904436, 1.0, -0.002651313, 0.00867256, 0.045015495, 1.0, -0.011623762, 0.029386422, 0.00505537, -0.029460723, -0.017257232], "split_indices": [71, 137, 125, 7, 40, 17, 0, 109, 0, 116, 0, 108, 113, 0, 0, 93, 0, 0, 0, 108, 109, 50, 0, 69, 0, 0, 0, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1028.0, 1046.0, 347.0, 681.0, 898.0, 148.0, 244.0, 103.0, 584.0, 97.0, 286.0, 612.0, 148.0, 96.0, 432.0, 152.0, 161.0, 125.0, 393.0, 219.0, 316.0, 116.0, 265.0, 128.0, 100.0, 119.0, 209.0, 107.0, 120.0, 145.0, 111.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017515633, 0.10612756, -0.17553402, 0.06843243, 0.036236193, -0.20529944, 0.00043871836, -0.07980475, 0.12123172, -0.24578875, -0.10449352, -0.00096118724, -0.014578603, 0.17626098, -0.025348462, -0.21400881, -0.037905943, -0.018971914, -0.0013827939, 0.09750021, 0.043898847, -0.011540022, 0.010287738, -0.035337392, -0.16787086, 0.23939674, -0.06081951, -0.006339448, -0.22709276, 0.040399645, 0.012265935, -0.018293709, 0.009182743, -0.019183656, -0.026675591], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [38.619846, 12.276318, 4.2254333, 8.672113, 0.0, 2.763218, 0.0, 1.3477416, 6.590087, 2.0456638, 1.4990432, 0.0, 0.0, 12.291414, 2.5749726, 2.5077076, 0.0, 0.0, 0.0, 10.266514, 0.0, 0.0, 0.0, 0.0, 1.8128757, 4.6307993, 4.0264287, 0.0, 0.26149368, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 19, 19, 24, 24, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 0.036236193, 1.0, 0.00043871836, 1.0, 0.15384616, 1.0, 1.0, -0.00096118724, -0.014578603, 2.0, 0.8076923, 1.0, -0.037905943, -0.018971914, -0.0013827939, 1.0, 0.043898847, -0.011540022, 0.010287738, -0.035337392, 0.0, 1.0, -0.23076923, -0.006339448, 0.6923077, 0.040399645, 0.012265935, -0.018293709, 0.009182743, -0.019183656, -0.026675591], "split_indices": [137, 125, 1, 17, 0, 93, 0, 53, 1, 0, 13, 0, 0, 0, 1, 26, 0, 0, 0, 122, 0, 0, 0, 0, 1, 39, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1271.0, 789.0, 1108.0, 163.0, 677.0, 112.0, 291.0, 817.0, 483.0, 194.0, 141.0, 150.0, 594.0, 223.0, 390.0, 93.0, 100.0, 94.0, 457.0, 137.0, 131.0, 92.0, 97.0, 293.0, 241.0, 216.0, 106.0, 187.0, 100.0, 141.0, 120.0, 96.0, 99.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0020598315, 0.104959786, -0.1647944, 0.07926539, 0.043703184, -0.20457748, -0.057714686, -0.15308216, 0.121531405, -0.23354466, -0.0054134964, -0.014940123, 0.004109312, -0.009130619, -0.021157213, -0.03608502, 0.19075637, -0.21033515, -0.03283372, 0.004046773, -0.011214069, 0.11651353, 0.39109415, -0.0118517075, -0.25660005, -0.011802226, 0.18556468, 0.042769913, 0.035487443, -0.018867793, -0.033593766, 0.09470716, 0.033335958, -0.005603985, 0.021441806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [35.557606, 10.929989, 3.365368, 11.676461, 0.0, 2.510147, 1.9387009, 0.66122866, 10.976457, 1.0626411, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7874374, 10.396677, 1.6482067, 0.0, 0.0, 0.0, 8.259434, 0.25057602, 0.0, 1.3903027, 0.0, 5.29074, 0.0, 0.0, 0.0, 0.0, 4.4032383, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 24, 24, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -1.0, 0.043703184, 2.7307692, 1.0, 1.0, 1.0, 1.0, -0.0054134964, -0.014940123, 0.004109312, -0.009130619, -0.021157213, -0.03846154, 1.0, 0.0, -0.03283372, 0.004046773, -0.011214069, 1.0, -0.34615386, -0.0118517075, 1.0, -0.011802226, 1.0, 0.042769913, 0.035487443, -0.018867793, -0.033593766, 1.0, 0.033335958, -0.005603985, 0.021441806], "split_indices": [137, 102, 93, 0, 0, 1, 13, 106, 17, 0, 0, 0, 0, 0, 0, 1, 113, 1, 0, 0, 0, 5, 1, 0, 23, 0, 15, 0, 0, 0, 0, 69, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1281.0, 790.0, 1189.0, 92.0, 576.0, 214.0, 183.0, 1006.0, 483.0, 93.0, 111.0, 103.0, 89.0, 94.0, 307.0, 699.0, 388.0, 95.0, 153.0, 154.0, 510.0, 189.0, 130.0, 258.0, 116.0, 394.0, 94.0, 95.0, 139.0, 119.0, 244.0, 150.0, 108.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001624375, -0.117700055, 0.11934714, -0.16004251, -0.026799737, 0.039863158, 0.20734152, -0.13417554, -0.025940735, 0.024088463, -0.015293754, -0.105386235, 0.23477048, 0.11974996, 0.3302248, -0.17230044, 0.00061497197, 0.008140035, -0.0068640853, -0.053674504, -0.020980412, 0.031638395, 0.010880183, 0.001892774, 0.025017867, 0.041577928, 0.022828752, -0.14636275, -0.027120566, 0.004517541, -0.014016821, -0.022276772, -0.10115061, 0.00012111729, -0.021972816], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [29.105803, 3.9605598, 7.2948914, 1.804327, 2.098989, 15.513973, 5.3279514, 2.979888, 0.0, 1.2382767, 0.0, 1.6954832, 2.4056911, 3.8003821, 1.7965622, 1.1236324, 0.0, 0.0, 0.0, 1.7954781, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1986871, 0.0, 0.0, 0.0, 0.0, 2.6460428, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.025940735, 1.0, -0.015293754, 1.0, 1.0, 1.0, 1.0, 1.0, 0.00061497197, 0.008140035, -0.0068640853, 1.0, -0.020980412, 0.031638395, 0.010880183, 0.001892774, 0.025017867, 0.041577928, 0.022828752, 1.0, -0.027120566, 0.004517541, -0.014016821, -0.022276772, 1.0, 0.00012111729, -0.021972816], "split_indices": [71, 2, 39, 58, 7, 42, 50, 93, 0, 109, 0, 106, 12, 106, 121, 0, 0, 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 106, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1029.0, 1043.0, 702.0, 327.0, 548.0, 495.0, 557.0, 145.0, 233.0, 94.0, 314.0, 234.0, 289.0, 206.0, 438.0, 119.0, 144.0, 89.0, 210.0, 104.0, 142.0, 92.0, 163.0, 126.0, 112.0, 94.0, 347.0, 91.0, 98.0, 112.0, 129.0, 218.0, 117.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0011023592, -0.11937138, 0.08415024, -0.14464234, 0.0038572082, 0.04771061, 0.03326872, -0.08950141, -0.21250808, 0.02006554, 0.031420905, -0.015715681, -0.03580182, -0.025831282, -0.016642055, 0.09404449, -0.083357766, 0.008183585, -0.015883574, 0.21483666, -0.06801831, 0.01849585, -0.1676185, 0.008017208, 0.037119213, -0.015271987, 0.0024048588, -0.00766543, 0.010757263, -0.019643871, -0.012598929], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, 17, -1, -1, 19, 21, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20.62049, 3.3567524, 11.049046, 2.7130814, 0.0, 7.8388777, 0.0, 1.4532263, 0.6860819, 7.375707, 0.0, 0.0, 3.2275739, 0.0, 0.0, 11.001665, 3.4500694, 0.0, 0.0, 6.7798834, 1.871571, 1.5425718, 0.263947, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, 18, -1, -1, 20, 22, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.7307692, 1.0, 1.0, 0.0038572082, 5.0, 0.03326872, 1.0, 1.0, 1.0, 0.031420905, -0.015715681, 1.0, -0.025831282, -0.016642055, -0.03846154, 1.0, 0.008183585, -0.015883574, 1.0, 1.0, 1.0, 1.0, 0.008017208, 0.037119213, -0.015271987, 0.0024048588, -0.00766543, 0.010757263, -0.019643871, -0.012598929], "split_indices": [127, 1, 125, 80, 0, 0, 0, 124, 17, 106, 0, 0, 111, 0, 0, 1, 15, 0, 0, 50, 39, 126, 97, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 841.0, 1220.0, 725.0, 116.0, 1064.0, 156.0, 400.0, 325.0, 964.0, 100.0, 177.0, 223.0, 163.0, 162.0, 562.0, 402.0, 114.0, 109.0, 322.0, 240.0, 182.0, 220.0, 173.0, 149.0, 125.0, 115.0, 88.0, 94.0, 130.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0065323026, -0.10379611, 0.08998115, -0.062304046, -0.18678021, 0.059662465, 0.027799908, 0.01569992, -0.119428165, -0.2287392, -0.0067032077, 0.1059242, -0.041617673, 0.008599937, -0.0068446374, -0.08478398, -0.01716137, -0.027088737, -0.019951645, 0.039922226, 0.032088894, -0.019762956, 0.006394724, -0.015550384, 0.002166799, 0.11079511, -0.0080235815, 0.0015754541, 0.019208765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, -1, -1, -1, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [19.394093, 3.5430336, 5.911373, 3.0567532, 1.7234058, 4.184057, 0.0, 1.7154772, 0.7159376, 0.31284618, 0.0, 8.697302, 4.6114273, 0.0, 0.0, 1.7917284, 0.0, 0.0, 0.0, 3.9939795, 0.0, 0.0, 0.0, 0.0, 0.0, 2.279196, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 19, 19, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, -1, -1, -1, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.4230769, 0.15384616, 0.027799908, 1.0, 1.0, 1.0, -0.0067032077, -0.115384616, 1.0, 0.008599937, -0.0068446374, 0.15384616, -0.01716137, -0.027088737, -0.019951645, 1.0, 0.032088894, -0.019762956, 0.006394724, -0.015550384, 0.002166799, -0.42307693, -0.0080235815, 0.0015754541, 0.019208765], "split_indices": [71, 23, 125, 122, 1, 1, 0, 81, 108, 97, 0, 1, 93, 0, 0, 1, 0, 0, 0, 122, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1029.0, 1037.0, 686.0, 343.0, 893.0, 144.0, 290.0, 396.0, 254.0, 89.0, 613.0, 280.0, 158.0, 132.0, 238.0, 158.0, 104.0, 150.0, 469.0, 144.0, 113.0, 167.0, 143.0, 95.0, 295.0, 174.0, 136.0, 159.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.006271103, -0.08428823, 0.095870756, -0.047920063, -0.15713091, 0.044984918, 0.24281076, 0.014535094, -0.09804855, -0.19118379, -0.0060328874, -0.003136476, 0.17784692, 0.0014199149, 0.04266268, 0.101510555, -0.013118692, -0.004823252, -0.14057441, -0.012288785, -0.022760827, -0.09022033, 0.09790737, 0.0072515826, 0.030722097, 0.024004918, -0.0027232384, -0.008462906, -0.018523252, -0.02187024, -0.02405905, 0.0011687921, 0.023534628, 0.013099479, -0.015426229], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, 23, -1, -1, 25, -1, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [16.755587, 2.7206817, 7.761297, 2.1445866, 1.1273642, 4.929394, 11.220002, 3.865643, 0.8050182, 0.6293726, 0.0, 4.9803967, 2.7935557, 0.0, 0.0, 3.4066515, 0.0, 0.0, 0.5121741, 0.0, 0.0, 3.1244547, 3.1046748, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.229766, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 18, 18, 21, 21, 22, 22, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, 24, -1, -1, 26, -1, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.4230769, 1.0, 1.0, 1.0, 1.0, 0.07692308, -0.0060328874, 1.0, 1.0, 0.0014199149, 0.04266268, 0.23076923, -0.013118692, -0.004823252, 1.0, -0.012288785, -0.022760827, 1.0, 1.0, 0.0072515826, 0.030722097, 0.024004918, -0.0027232384, -0.008462906, -0.018523252, -0.07692308, -0.02405905, 0.0011687921, 0.023534628, 0.013099479, -0.015426229], "split_indices": [71, 23, 113, 106, 1, 0, 109, 80, 137, 1, 0, 39, 106, 0, 0, 1, 0, 0, 69, 0, 0, 106, 111, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1027.0, 1038.0, 685.0, 342.0, 771.0, 267.0, 305.0, 380.0, 253.0, 89.0, 566.0, 205.0, 119.0, 148.0, 191.0, 114.0, 175.0, 205.0, 88.0, 165.0, 304.0, 262.0, 113.0, 92.0, 92.0, 99.0, 91.0, 114.0, 209.0, 95.0, 161.0, 101.0, 97.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009625622, -0.07816749, 0.07490997, -0.041648734, -0.15014648, 0.0032340721, 0.15335046, 0.0069565093, -0.13191563, -0.021505376, -0.09335259, -0.10504762, 0.14822932, 0.11116527, 0.027529195, 0.014629753, -0.08788014, -0.017800255, -0.00985193, -0.0006250501, -0.017674819, -0.034669846, -0.021033276, 0.008032865, 0.025044534, 0.021426348, 0.023844203, -0.0008887794, -0.0163925, -0.010393859, 0.0028237451, -0.003453425, 0.009065599], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [12.113793, 2.6942968, 5.8640494, 2.9834628, 1.2717857, 8.556681, 2.5617733, 5.8408694, 0.36631298, 0.0, 1.3365628, 2.3118362, 1.6171446, 4.2260194, 0.0, 0.0, 1.5798302, 0.0, 0.0, 0.0, 0.0, 0.81485456, 0.0, 0.0, 0.0, 0.8406869, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.021505376, 1.0, 0.03846154, 1.0, 0.03846154, 0.027529195, 0.014629753, 1.0, -0.017800255, -0.00985193, -0.0006250501, -0.017674819, 1.0, -0.021033276, 0.008032865, 0.025044534, 1.0, 0.023844203, -0.0008887794, -0.0163925, -0.010393859, 0.0028237451, -0.003453425, 0.009065599], "split_indices": [71, 23, 39, 80, 80, 42, 113, 122, 39, 0, 116, 1, 50, 1, 0, 0, 13, 0, 0, 0, 0, 124, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1025.0, 1043.0, 680.0, 345.0, 545.0, 498.0, 442.0, 238.0, 161.0, 184.0, 312.0, 233.0, 370.0, 128.0, 179.0, 263.0, 100.0, 138.0, 90.0, 94.0, 187.0, 125.0, 140.0, 93.0, 217.0, 153.0, 129.0, 134.0, 89.0, 98.0, 120.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020463823, -0.072088175, 0.067184895, -0.054164246, -0.019548751, 0.037850015, 0.024620803, -0.021461556, -0.122041985, -0.003603552, 0.1783429, -0.094974644, 0.03998555, -0.01931236, -0.0055225254, -0.082214385, 0.0484239, 0.039698426, 0.00042037462, 0.0029781472, -0.019299734, 0.013853866, -0.010195155, -0.012959774, -0.0017674969, -0.032863013, 0.14806594, 0.010248416, -0.012496116, -0.01784961, 0.013475239, 0.019482419, 0.0102302525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [9.99881, 2.2670956, 5.4459314, 1.986707, 0.0, 5.189123, 0.0, 2.728369, 1.3820863, 2.8138664, 7.729025, 3.3629563, 1.6270581, 0.0, 0.0, 0.8379173, 3.3532324, 0.0, 0.0, 0.0, 0.0, 0.0, 2.8191226, 0.0, 0.0, 5.5655603, 0.3980055, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.019548751, 2.0, 0.024620803, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.01931236, -0.0055225254, 1.0, 1.0, 0.039698426, 0.00042037462, 0.0029781472, -0.019299734, 0.013853866, 1.0, -0.012959774, -0.0017674969, 1.0, 1.0, 0.010248416, -0.012496116, -0.01784961, 0.013475239, 0.019482419, 0.0102302525], "split_indices": [71, 40, 125, 23, 0, 0, 0, 115, 80, 93, 69, 39, 106, 0, 0, 59, 50, 0, 0, 0, 0, 0, 13, 0, 0, 39, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1025.0, 1037.0, 895.0, 130.0, 891.0, 146.0, 604.0, 291.0, 688.0, 203.0, 275.0, 329.0, 141.0, 150.0, 274.0, 414.0, 90.0, 113.0, 121.0, 154.0, 111.0, 218.0, 158.0, 116.0, 228.0, 186.0, 110.0, 108.0, 122.0, 106.0, 92.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046871863, -0.024791386, 0.18937561, 0.0142111145, -0.0815223, 0.033553682, 0.0028916, 0.053495724, -0.079951055, -0.1045615, 0.0054408964, 0.10055528, -0.065324314, -0.019233337, 0.0038832743, -0.13498826, -0.031056825, -0.0037989896, 0.1381108, 0.0075271847, -0.019029869, -0.023994453, -0.09741515, 0.0047554076, -0.011657861, 0.195498, 0.062201794, 0.00051598286, -0.14558081, 0.005261579, 0.032951932, 0.013377345, -0.0002863323, -0.018911425, -0.009725467], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, 17, 19, -1, -1, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [8.021437, 4.122162, 4.526422, 4.083833, 2.3769965, 0.0, 0.0, 4.3558702, 4.3384895, 1.4514947, 0.0, 2.9033513, 3.8831728, 0.0, 0.0, 1.8100815, 1.2773597, 0.0, 1.9123726, 0.0, 0.0, 0.0, 1.66992, 0.0, 0.0, 4.7873135, 0.88013923, 0.0, 0.48387384, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 22, 22, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, 18, 20, -1, -1, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.1923077, 2.7307692, 0.033553682, 0.0028916, 1.0, 0.84615386, 1.0, 0.0054408964, 1.0, 1.0, -0.019233337, 0.0038832743, 1.0, 1.0, -0.0037989896, 1.0, 0.0075271847, -0.019029869, -0.023994453, 1.0, 0.0047554076, -0.011657861, 1.0, -0.30769232, 0.00051598286, 1.0, 0.005261579, 0.032951932, 0.013377345, -0.0002863323, -0.018911425, -0.009725467], "split_indices": [125, 137, 15, 1, 1, 0, 0, 7, 1, 83, 0, 17, 69, 0, 0, 124, 69, 0, 50, 0, 0, 0, 80, 0, 0, 115, 1, 0, 17, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1863.0, 193.0, 1104.0, 759.0, 101.0, 92.0, 779.0, 325.0, 649.0, 110.0, 558.0, 221.0, 167.0, 158.0, 459.0, 190.0, 119.0, 439.0, 104.0, 117.0, 121.0, 338.0, 99.0, 91.0, 250.0, 189.0, 108.0, 230.0, 121.0, 129.0, 90.0, 99.0, 121.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002046467, 0.048086137, -0.07202495, 0.028637849, 0.029160349, -0.09600397, 0.0028623852, -0.15697238, 0.06258867, -0.07323074, -0.021708535, -0.023515344, -0.0073460764, -0.040142644, 0.10799889, -0.12068597, -0.021141212, -0.111997776, 0.012114915, 0.056576636, 0.24530724, -0.0058094696, -0.019338813, 0.0055983765, -0.01414562, -0.006371032, -0.016874796, -0.048282944, 0.13577129, 0.026429115, 0.022711432, 0.004634147, -0.011716954, 0.039876293, 0.016957806, -0.010874744, 0.014655086], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [7.02847, 6.0194488, 1.9066339, 7.417006, 0.0, 1.7592287, 0.0, 1.188282, 4.6417265, 1.3274214, 0.0, 0.0, 0.0, 3.5348408, 4.8718853, 1.2786951, 2.3754988, 0.5782075, 0.0, 4.1687684, 0.064928055, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4079648, 8.936627, 0.0, 0.0, 0.0, 0.0, 0.0, 3.2092342, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 27, 27, 28, 28, 34, 34], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -1.0, 0.029160349, 1.0, 0.0028623852, 1.0, 1.0, 1.0, -0.021708535, -0.023515344, -0.0073460764, 1.0, 1.0, 1.0, 1.0, 1.0, 0.012114915, -0.15384616, -0.34615386, -0.0058094696, -0.019338813, 0.0055983765, -0.01414562, -0.006371032, -0.016874796, 1.0, 0.07692308, 0.026429115, 0.022711432, 0.004634147, -0.011716954, 0.039876293, 1.0, -0.010874744, 0.014655086], "split_indices": [137, 102, 113, 0, 0, 40, 0, 124, 17, 115, 0, 0, 0, 39, 113, 109, 23, 61, 0, 1, 1, 0, 0, 0, 0, 0, 0, 39, 1, 0, 0, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1271.0, 790.0, 1177.0, 94.0, 638.0, 152.0, 182.0, 995.0, 537.0, 101.0, 94.0, 88.0, 305.0, 690.0, 281.0, 256.0, 211.0, 94.0, 502.0, 188.0, 151.0, 130.0, 156.0, 100.0, 114.0, 97.0, 216.0, 286.0, 92.0, 96.0, 91.0, 125.0, 89.0, 197.0, 100.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.004716255, -0.059285037, 0.04911355, -0.077369526, 0.0053549795, 0.09059818, -0.054538585, -0.099562004, 0.0023169105, 0.13248295, -0.04061428, -0.015784891, 0.021530697, -0.0031073706, -0.12522542, 0.0019394404, 0.17415947, 0.0075184526, -0.013364925, 0.01296515, -0.010656891, -0.09235923, -0.024608819, 0.12236661, 0.033083286, -0.01803421, -0.03840746, 0.03834183, 0.03156236, -0.013133812, 0.002754331, -0.0038054602, 0.014602445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1, -1, 25, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [5.876189, 1.7283549, 5.250264, 1.628777, 0.0, 4.7923417, 2.7427018, 1.0510707, 0.0, 3.1153822, 2.2731748, 0.0, 2.783897, 0.0, 1.7279496, 0.0, 3.9193325, 0.0, 0.0, 0.0, 0.0, 1.6234157, 0.0, 5.8945284, 0.0, 0.0, 1.2993162, 2.0813222, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 21, 21, 23, 23, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1, -1, 26, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.7307692, 0.1923077, 1.0, 0.0053549795, 1.0, 1.0, 0.0, 0.0023169105, 1.0, 1.0, -0.015784891, 1.0, -0.0031073706, 0.96153843, 0.0019394404, 1.0, 0.0075184526, -0.013364925, 0.01296515, -0.010656891, 1.0, -0.024608819, 1.0, 0.033083286, -0.01803421, 1.0, 1.0, 0.03156236, -0.013133812, 0.002754331, -0.0038054602, 0.014602445], "split_indices": [127, 1, 1, 62, 0, 7, 69, 0, 0, 89, 69, 0, 124, 0, 1, 0, 113, 0, 0, 0, 0, 108, 0, 0, 0, 0, 109, 126, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 847.0, 1221.0, 730.0, 117.0, 872.0, 349.0, 598.0, 132.0, 661.0, 211.0, 148.0, 201.0, 163.0, 435.0, 178.0, 483.0, 94.0, 117.0, 109.0, 92.0, 342.0, 93.0, 363.0, 120.0, 130.0, 212.0, 253.0, 110.0, 88.0, 124.0, 148.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004735824, -0.048507143, 0.057212338, -0.031872176, -0.01574906, 0.03148868, 0.021138163, 0.008805654, -0.04618528, -0.001694107, 0.14427748, -0.0638189, 0.008088585, -0.07724338, 0.048066705, 0.030404149, 0.0022023253, -0.10224571, 0.01874679, -0.12711371, 0.0028164338, -0.038297236, 0.15696038, -0.13130979, -5.2139465e-05, 0.007163455, -0.0057388297, -0.019196551, -0.0063641714, -0.014907454, 0.008889149, 0.024575783, 0.006009039, -0.15646969, -0.008354528, -0.022651417, -0.011453514], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [5.7807975, 1.8618844, 4.132361, 1.5294476, 0.0, 3.3421843, 0.0, 0.0, 1.7836165, 2.5939813, 3.9649587, 2.217743, 0.0, 1.440341, 3.9122672, 0.0, 0.0, 1.4102612, 0.8939083, 0.76562595, 0.0, 3.2687929, 1.5827317, 0.44584846, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.71375895, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 33, 33], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.42307693, -0.01574906, 2.0, 0.021138163, 0.008805654, 2.7307692, 1.0, 1.0, 1.0, 0.008088585, 1.0, 1.0, 0.030404149, 0.0022023253, 1.0, 1.0, 1.0, 0.0028164338, 1.0, 1.0, 1.0, -5.2139465e-05, 0.007163455, -0.0057388297, -0.019196551, -0.0063641714, -0.014907454, 0.008889149, 0.024575783, 0.006009039, 1.0, -0.008354528, -0.022651417, -0.011453514], "split_indices": [71, 64, 125, 1, 0, 0, 0, 0, 1, 93, 69, 127, 0, 116, 50, 0, 0, 62, 93, 105, 0, 39, 111, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1027.0, 1042.0, 891.0, 136.0, 893.0, 149.0, 95.0, 796.0, 690.0, 203.0, 699.0, 97.0, 274.0, 416.0, 88.0, 115.0, 477.0, 222.0, 186.0, 88.0, 232.0, 184.0, 371.0, 106.0, 131.0, 91.0, 92.0, 94.0, 124.0, 108.0, 96.0, 88.0, 243.0, 128.0, 91.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [1.6527722e-05, 0.03730895, -0.05424306, -0.06607464, 0.07885122, -0.030319324, -0.017558286, -0.119982935, 0.0079761455, 0.1334003, -0.00881694, -0.084402286, 0.026759475, -0.019361414, -0.0066612647, 0.013702575, 0.3022596, -0.08289024, 0.017369352, -0.0017335389, -0.11934469, -0.010854437, 0.073452584, 0.016932504, -0.014390233, 0.025357444, 0.03437023, -0.017113572, 0.00017386077, -0.016535396, -0.0054745777, -0.0009465239, 0.020629035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.192633, 5.2740006, 2.4500484, 2.7673452, 4.189223, 2.1763287, 0.0, 1.0099373, 0.0, 10.914516, 4.542435, 0.84833884, 2.1669905, 0.0, 0.0, 7.7504897, 0.45195198, 1.78488, 0.0, 0.0, 0.707371, 0.0, 2.8087282, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 20, 20, 22, 22], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.1923077, 1.0, 1.0, 1.0, 1.0, 1.0, -0.017558286, 1.0, 0.0079761455, 1.0, 1.0, 1.0, 1.0, -0.019361414, -0.0066612647, 1.0, 1.0, -0.26923078, 0.017369352, -0.0017335389, 1.0, -0.010854437, 1.0, 0.016932504, -0.014390233, 0.025357444, 0.03437023, -0.017113572, 0.00017386077, -0.016535396, -0.0054745777, -0.0009465239, 0.020629035], "split_indices": [1, 127, 64, 83, 106, 39, 0, 124, 0, 50, 0, 124, 69, 0, 0, 69, 39, 1, 0, 0, 80, 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1228.0, 844.0, 352.0, 876.0, 705.0, 139.0, 257.0, 95.0, 540.0, 336.0, 362.0, 343.0, 108.0, 149.0, 316.0, 224.0, 239.0, 97.0, 124.0, 238.0, 88.0, 255.0, 159.0, 157.0, 103.0, 121.0, 117.0, 122.0, 139.0, 99.0, 157.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.001253805, -0.042582534, 0.04449662, 0.015016417, -0.0696391, 0.0017717014, 0.091019295, 0.014818369, -0.03422189, -0.12786463, -0.04152164, -0.0852562, 0.1195824, 0.015368362, 0.18481274, 0.0053420463, -0.012186428, -0.019094717, -0.004979217, -0.010281277, -0.0141997365, -0.025442453, -0.01749768, 0.0031092984, 0.017900594, -0.01331377, 0.011959936, 0.0077656596, 0.033229645, 0.009625879, -0.058354247, -0.008000248, 0.0023550189, -0.003127205, -0.009412315], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [3.893576, 1.5895989, 2.055258, 2.1375601, 1.1361842, 5.526269, 3.5123024, 0.0, 1.8281221, 1.1130512, 1.4690042, 1.6636217, 1.2041624, 4.2412286, 3.4926357, 0.0, 0.0, 0.0, 0.0, 1.8284458, 0.0, 0.49718556, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2382999, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 19, 19, 21, 21, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.03846154, 1.0, 1.0, -0.07692308, 0.014818369, 1.0, 1.0, 1.0, 0.03846154, -0.42307693, 1.0, 1.0, 0.0053420463, -0.012186428, -0.019094717, -0.004979217, 1.0, -0.0141997365, 1.0, -0.01749768, 0.0031092984, 0.017900594, -0.01331377, 0.011959936, 0.0077656596, 0.033229645, 0.009625879, 1.0, -0.008000248, 0.0023550189, -0.003127205, -0.009412315], "split_indices": [71, 53, 39, 1, 59, 42, 1, 0, 12, 12, 109, 1, 1, 109, 122, 0, 0, 0, 0, 122, 0, 124, 0, 0, 0, 0, 0, 0, 0, 0, 124, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1020.0, 1034.0, 326.0, 694.0, 539.0, 495.0, 88.0, 238.0, 226.0, 468.0, 310.0, 229.0, 274.0, 221.0, 119.0, 119.0, 125.0, 101.0, 357.0, 111.0, 186.0, 124.0, 92.0, 137.0, 113.0, 161.0, 128.0, 93.0, 111.0, 246.0, 88.0, 98.0, 140.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005672777, 0.015593329, -0.01879253, 0.031510577, -0.120785005, 0.0143365245, 0.018559188, -0.0042758784, -0.018756418, -6.980866e-05, 0.018744871, 0.044357173, -0.054775186, 0.13021977, -0.03477509, -0.007102351, -0.10467571, 0.048842665, 0.038120385, 0.010194729, -0.08195087, 0.05322854, -0.010514007, -0.015638445, -0.0725536, -0.0068168654, 0.020178663, -0.117279604, 0.00029148566, 0.013633052, -0.0029873408, -0.0032616735, -0.010516257, -0.006869798, -0.016046325], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.979482, 4.278583, 0.0, 4.6705427, 1.0733676, 3.9603326, 0.0, 0.0, 0.0, 3.5629592, 0.0, 5.496752, 1.5629365, 7.924648, 2.7154438, 1.9873405, 0.5331783, 5.243579, 0.0, 0.0, 0.93843675, 1.4364351, 0.0, 0.0, 0.25785506, 0.0, 0.0, 0.46364212, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 24, 24, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01879253, 1.0, 1.0, 5.0, 0.018559188, -0.0042758784, -0.018756418, 1.0, 0.018744871, -0.03846154, 1.0, -0.1923077, 0.0, 1.0, 1.0, 1.0, 0.038120385, 0.010194729, 1.0, 1.0, -0.010514007, -0.015638445, 1.0, -0.0068168654, 0.020178663, 1.0, 0.00029148566, 0.013633052, -0.0029873408, -0.0032616735, -0.010516257, -0.006869798, -0.016046325], "split_indices": [117, 40, 0, 125, 108, 0, 0, 0, 0, 106, 0, 1, 97, 1, 0, 108, 3, 61, 0, 0, 61, 13, 0, 0, 126, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1971.0, 101.0, 1765.0, 206.0, 1588.0, 177.0, 95.0, 111.0, 1466.0, 122.0, 809.0, 657.0, 388.0, 421.0, 336.0, 321.0, 293.0, 95.0, 108.0, 313.0, 208.0, 128.0, 123.0, 198.0, 166.0, 127.0, 221.0, 92.0, 104.0, 104.0, 89.0, 109.0, 104.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0073611005, -0.032805227, 0.047411233, -0.019329475, -0.012409584, 0.02573494, 0.018183433, -0.004742206, -0.0118045015, -0.0005364307, 0.11503173, 0.015893742, -0.010149193, 0.034809805, -0.061971568, 0.02503953, 0.0007220012, 0.016117252, -0.010980165, -0.031880002, 0.11715132, -0.011541202, 0.0012845036, 0.02753778, -0.075051494, -0.011978268, 0.00887707, 0.0254357, 0.00030269523, 0.009808283, -0.024651151, -0.012409898, -0.002552311, -0.0089015905, 0.003588992], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [3.3315487, 1.2720364, 3.0216043, 1.2974308, 0.0, 2.0949328, 0.0, 1.5672704, 0.0, 1.4983357, 2.9625366, 2.5260234, 0.0, 2.4052072, 1.0075551, 0.0, 0.0, 0.0, 1.3474714, 2.5665357, 3.0690668, 0.0, 0.0, 1.25545, 0.4979943, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7637555, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.012409584, 2.0, 0.018183433, 1.0, -0.0118045015, 1.0, 1.0, -0.30769232, -0.010149193, 1.0, 1.0, 0.02503953, 0.0007220012, 0.016117252, 1.0, 1.0, 1.0, -0.011541202, 0.0012845036, 1.0, 1.0, -0.011978268, 0.00887707, 0.0254357, 0.00030269523, 0.009808283, 0.53846157, -0.012409898, -0.002552311, -0.0089015905, 0.003588992], "split_indices": [71, 40, 125, 64, 0, 0, 0, 7, 0, 97, 69, 1, 0, 50, 59, 0, 0, 0, 109, 122, 126, 0, 0, 97, 111, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1034.0, 1037.0, 901.0, 133.0, 893.0, 144.0, 785.0, 116.0, 690.0, 203.0, 647.0, 138.0, 438.0, 252.0, 90.0, 113.0, 101.0, 546.0, 242.0, 196.0, 147.0, 105.0, 341.0, 205.0, 140.0, 102.0, 89.0, 107.0, 145.0, 196.0, 103.0, 102.0, 95.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005889709, 0.0064671165, -0.11172365, -0.00632154, 0.120877475, -0.007587158, -0.015409424, -0.018203536, 0.01425415, 0.028483966, -0.0057822843, 0.017141081, -0.061795235, -0.076653436, 0.06873661, -0.02855693, -0.10683143, -0.13704357, 0.004175854, 0.1653298, -0.04021149, 0.008058886, -0.071449324, -0.01824126, -0.006006296, -0.006852356, -0.019088067, 0.0028793763, 0.028348595, -0.007888856, 0.0018941633, 0.0021635888, -0.014417216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, 19, 21, 23, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.7018566, 2.7068367, 0.3281188, 2.9432662, 5.449818, 0.0, 0.0, 2.3742683, 0.0, 0.0, 0.0, 4.118311, 1.0328791, 2.1595762, 5.7774816, 1.8585652, 1.0357001, 0.73778343, 0.0, 4.694577, 0.5902704, 0.0, 1.9292854, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 22, 22], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, 20, 22, 24, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 5.0, 1.0, -0.007587158, -0.015409424, 1.0, 0.01425415, 0.028483966, -0.0057822843, 1.0, 1.0, 0.42307693, 0.03846154, 0.07692308, 1.0, 1.0, 0.004175854, 1.0, 1.0, 0.008058886, 1.0, -0.01824126, -0.006006296, -0.006852356, -0.019088067, 0.0028793763, 0.028348595, -0.007888856, 0.0018941633, 0.0021635888, -0.014417216], "split_indices": [40, 125, 115, 0, 15, 0, 0, 106, 0, 0, 0, 108, 15, 1, 1, 1, 115, 109, 0, 50, 109, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1850.0, 216.0, 1664.0, 186.0, 117.0, 99.0, 1541.0, 123.0, 97.0, 89.0, 851.0, 690.0, 302.0, 549.0, 397.0, 293.0, 200.0, 102.0, 291.0, 258.0, 112.0, 285.0, 112.0, 181.0, 88.0, 112.0, 135.0, 156.0, 156.0, 102.0, 125.0, 160.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0028225898, 0.018941965, -0.0070583117, 0.002191218, -0.018317694, -0.016768254, 0.09698859, -0.062308554, 0.014414267, 0.017767863, 0.022656145, -0.028385464, -0.019990059, -0.057796985, 0.0670176, -0.00638479, 0.008908263, 0.0538782, -0.10470614, -0.004412198, -0.021002289, 0.020379758, 0.024474557, 0.0154090645, -0.0020565311, -0.015142356, -0.007163989, -0.008310089, 0.00885835, -0.0061159027, 0.06290899, -0.007979255, 0.016815139], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [3.8128695, 0.0, 3.199385, 3.3537772, 0.0, 2.2081957, 3.1923692, 2.9498887, 3.5060642, 1.1233392, 0.0, 3.1831584, 0.0, 3.1612263, 4.4262447, 0.0, 0.0, 1.820281, 0.40627384, 2.1075, 0.0, 1.4668719, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.175075, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 21, 21, 30, 30], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.018941965, 1.0, 1.0, -0.018317694, 1.0, 1.0, 1.0, -0.07692308, 1.0, 0.022656145, 1.0, -0.019990059, 1.0, 1.0, -0.00638479, 0.008908263, 1.0, 0.0, 1.0, -0.021002289, 1.0, 0.024474557, 0.0154090645, -0.0020565311, -0.015142356, -0.007163989, -0.008310089, 0.00885835, -0.0061159027, 0.65384614, -0.007979255, 0.016815139], "split_indices": [1, 0, 117, 42, 0, 39, 105, 0, 1, 13, 0, 122, 0, 7, 15, 0, 0, 23, 1, 12, 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 104.0, 1964.0, 1866.0, 98.0, 1555.0, 311.0, 632.0, 923.0, 193.0, 118.0, 507.0, 125.0, 389.0, 534.0, 90.0, 103.0, 244.0, 263.0, 288.0, 101.0, 423.0, 111.0, 104.0, 140.0, 109.0, 154.0, 156.0, 132.0, 145.0, 278.0, 118.0, 160.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002913593, 0.014634072, -0.004662038, 0.006253589, -0.097576715, 0.015380037, -0.01363256, -0.0045307497, -0.014105727, 0.038655676, -0.02484425, 0.060932577, -0.037668545, -0.010155017, 0.004752318, 0.008798115, 0.12872213, -0.019244496, 0.007927361, 0.056208376, -0.045772396, -0.05363936, 0.017202051, -0.0018789085, 0.19908315, 0.010470611, -0.0004918945, -0.011047106, 0.0052010776, 0.021667467, -0.017214548, 0.0040149833, 0.04019123, 0.01074521, -0.0063276105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [2.2524207, 0.0, 1.9970033, 2.292787, 0.47044694, 1.5504234, 0.0, 0.0, 0.0, 1.7835803, 1.378031, 2.869747, 4.2896733, 0.0, 1.1387137, 4.6777587, 3.6638017, 0.0, 0.0, 0.6433048, 1.3981435, 2.962875, 0.0, 0.0, 7.7044764, 0.0, 0.0, 0.0, 0.0, 1.4792314, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 24, 24, 29, 29], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.014634072, 1.0, 1.0, 1.0, 1.0, -0.01363256, -0.0045307497, -0.014105727, 0.46153846, 1.0, -0.1923077, 0.84615386, -0.010155017, 0.7692308, 1.0, 1.0, -0.019244496, 0.007927361, 1.0, 1.9230769, 1.0, 0.017202051, -0.0018789085, 1.0, 0.010470611, -0.0004918945, -0.011047106, 0.0052010776, 1.0, -0.017214548, 0.0040149833, 0.04019123, 0.01074521, -0.0063276105], "split_indices": [1, 0, 40, 43, 69, 137, 0, 0, 0, 1, 124, 1, 1, 0, 1, 61, 71, 0, 0, 126, 1, 124, 0, 0, 59, 0, 0, 0, 0, 126, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 104.0, 1969.0, 1762.0, 207.0, 1656.0, 106.0, 94.0, 113.0, 1049.0, 607.0, 812.0, 237.0, 169.0, 438.0, 459.0, 353.0, 102.0, 135.0, 217.0, 221.0, 332.0, 127.0, 114.0, 239.0, 121.0, 96.0, 133.0, 88.0, 203.0, 129.0, 134.0, 105.0, 101.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014845303, 0.0059078885, -0.014494139, 0.014189123, -0.001944359, -0.016450925, 0.06998208, 0.016826218, -0.071039125, -0.0069848164, 0.12963444, -0.072381824, 0.050231244, -0.0070623397, -0.17165458, -0.0018080792, 0.02395346, 0.0026018384, -0.014634711, -0.03558966, 0.119779624, -0.0906916, 0.014255561, -0.011572967, -0.024660546, 0.0022309104, -0.008989225, 0.036817707, 0.024557805, -0.016066423, -0.004620899, 0.009996152, -0.00046767705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [2.185677, 2.092839, 0.0, 0.0, 1.9334306, 2.8011038, 2.5941148, 2.8548372, 3.7592382, 0.0, 3.538997, 1.8996102, 4.160187, 4.466941, 0.9514971, 0.0, 0.0, 0.0, 0.0, 0.98094475, 4.018045, 0.7127769, 0.0, 0.0, 0.0, 0.0, 0.0, 0.60786796, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 19, 19, 20, 20, 21, 21, 27, 27], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.014494139, 0.014189123, 1.0, 1.0, 1.0, -0.1923077, 1.0, -0.0069848164, -0.26923078, 1.0, 1.0, 1.0, 1.0, -0.0018080792, 0.02395346, 0.0026018384, -0.014634711, 1.0, 1.0, 1.0, 0.014255561, -0.011572967, -0.024660546, 0.0022309104, -0.008989225, 1.0, 0.024557805, -0.016066423, -0.004620899, 0.009996152, -0.00046767705], "split_indices": [117, 1, 0, 0, 42, 109, 109, 1, 105, 0, 1, 126, 39, 71, 50, 0, 0, 0, 0, 108, 71, 17, 0, 0, 0, 0, 0, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 107.0, 1853.0, 1542.0, 311.0, 958.0, 584.0, 93.0, 218.0, 261.0, 697.0, 357.0, 227.0, 93.0, 125.0, 112.0, 149.0, 312.0, 385.0, 229.0, 128.0, 130.0, 97.0, 151.0, 161.0, 232.0, 153.0, 89.0, 140.0, 92.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007673709, -0.010605932, 0.0950184, -0.01863991, 0.014724842, 0.021311663, -0.0034632876, -0.020480206, -0.0074944543, -0.041072816, 0.0136054605, -0.0035265593, -0.10798216, -0.013711706, 0.12922055, 0.008103286, -0.050049968, -0.0061775683, -0.015113364, -0.098618634, 0.03169639, 0.030785674, -0.002900005, 0.00062839134, -0.01043279, -0.017842649, -0.000320922, 0.026803557, -0.016823567, -0.060235027, 0.011076835, -0.018540977, -0.014665539, -0.009721564, 0.004628692], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, 9, 11, 13, 15, 17, 19, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1], "loss_changes": [1.9526407, 2.382944, 2.9551368, 3.7098522, 0.0, 0.0, 0.0, 0.0, 1.1952405, 1.6354389, 3.271975, 1.6404748, 0.46656704, 3.230877, 5.596258, 0.0, 0.8225178, 0.0, 0.0, 2.2234106, 6.2610745, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.509145, 1.2178863, 0.0, 1.1628715, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 19, 19, 20, 20, 28, 28, 29, 29, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, 10, 12, 14, 16, 18, 20, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.53846157, 0.014724842, 0.021311663, -0.0034632876, -0.020480206, 1.0, 1.0, 1.0, 1.0, 1.0, -0.1923077, -0.1923077, 0.008103286, 1.0, -0.0061775683, -0.015113364, 1.0, -0.03846154, 0.030785674, -0.002900005, 0.00062839134, -0.01043279, -0.017842649, -0.000320922, 0.026803557, 1.0, 1.0, 0.011076835, 1.0, -0.014665539, -0.009721564, 0.004628692], "split_indices": [125, 114, 15, 1, 0, 0, 0, 0, 17, 109, 61, 53, 124, 1, 1, 0, 115, 0, 0, 69, 1, 0, 0, 0, 0, 0, 0, 0, 15, 121, 0, 81, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1879.0, 193.0, 1788.0, 91.0, 101.0, 92.0, 101.0, 1687.0, 651.0, 1036.0, 417.0, 234.0, 838.0, 198.0, 148.0, 269.0, 113.0, 121.0, 292.0, 546.0, 93.0, 105.0, 132.0, 137.0, 159.0, 133.0, 93.0, 453.0, 338.0, 115.0, 228.0, 110.0, 103.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040969825, 0.0033291646, -0.015517269, 0.011087512, -0.0029662065, -0.015769355, 0.06193395, 0.014432168, -0.065124325, -0.002198949, 0.014476038, -0.007457918, 0.10880277, -0.005869546, -0.15593034, 0.024301365, -0.014804274, 0.00045525722, 0.02177916, -0.08137222, 0.012422735, -0.011579395, -0.018822756, 0.07643967, -0.06768061, -0.012012309, -0.0032444322, 0.018171292, 0.029274227, 8.842983e-05, -0.015009038, -0.0016000742, 0.0093840305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [2.2987952, 1.3222626, 0.0, 0.0, 1.5330589, 2.2970073, 2.1131282, 1.9748868, 3.1477034, 0.0, 0.0, 3.4647417, 2.0451784, 3.4772227, 0.29944277, 3.0357316, 0.0, 0.0, 0.0, 0.42470336, 0.0, 0.0, 0.0, 2.0059638, 1.2939463, 0.0, 0.0, 0.0, 0.81558055, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.015517269, 0.011087512, 1.0, 1.0, 1.0, 1.0, 1.0, -0.002198949, 0.014476038, 1.0, -0.115384616, 1.0, -0.115384616, 1.0, -0.014804274, 0.00045525722, 0.02177916, 1.0, 0.012422735, -0.011579395, -0.018822756, 1.0, 1.0, -0.012012309, -0.0032444322, 0.018171292, 1.0, 8.842983e-05, -0.015009038, -0.0016000742, 0.0093840305], "split_indices": [117, 1, 0, 0, 42, 109, 93, 105, 105, 0, 0, 113, 1, 71, 1, 23, 0, 0, 0, 115, 0, 0, 0, 122, 115, 0, 0, 0, 3, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1953.0, 96.0, 108.0, 1845.0, 1541.0, 304.0, 956.0, 585.0, 151.0, 153.0, 776.0, 180.0, 354.0, 231.0, 633.0, 143.0, 92.0, 88.0, 224.0, 130.0, 103.0, 128.0, 404.0, 229.0, 125.0, 99.0, 125.0, 279.0, 125.0, 104.0, 164.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014079373, -0.010593186, 0.08731966, -0.019042421, 0.009738303, 0.020748353, -0.0040514227, -0.009512198, -0.016147112, 0.025341207, -0.049462747, 0.08722432, -0.023326613, -0.098549634, -0.0067837453, 0.006169247, 0.0358943, 0.0046408414, -0.015013362, -0.033880234, -0.020424064, 0.041167706, -0.008204933, -0.014335054, 0.07500758, 0.015166904, -0.043878455, 0.0057908418, -0.013173043, -0.0028600402, 0.0128695, 0.0163169, -0.0009730068, 0.0036966407, -0.013263822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, 27, -1, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6853888, 1.7096807, 2.9800377, 2.3591213, 0.0, 0.0, 0.0, 2.2682402, 0.0, 2.6201935, 1.5900893, 8.435259, 1.7271312, 2.4127452, 1.4652923, 3.036344, 0.0, 2.8463485, 0.0, 1.9669571, 0.0, 1.5144403, 0.0, 0.0, 1.5090601, 0.0, 2.1527314, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 21, 21, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, 28, -1, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, 3.0, 0.009738303, 0.020748353, -0.0040514227, 1.0, -0.016147112, -0.03846154, 1.0, -0.1923077, 1.0, 1.0, 1.0, 1.0, 0.0358943, 0.0, -0.015013362, 1.0, -0.020424064, 1.0, -0.008204933, -0.014335054, 1.0, 0.015166904, 1.0, 0.0057908418, -0.013173043, -0.0028600402, 0.0128695, 0.0163169, -0.0009730068, 0.0036966407, -0.013263822], "split_indices": [125, 0, 15, 0, 0, 0, 0, 106, 0, 1, 39, 1, 64, 111, 15, 15, 0, 0, 0, 124, 0, 93, 0, 0, 113, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1874.0, 194.0, 1738.0, 136.0, 100.0, 94.0, 1629.0, 109.0, 870.0, 759.0, 383.0, 487.0, 353.0, 406.0, 295.0, 88.0, 399.0, 88.0, 219.0, 134.0, 248.0, 158.0, 93.0, 202.0, 99.0, 300.0, 113.0, 106.0, 138.0, 110.0, 99.0, 103.0, 157.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009396629, 0.004708969, -0.011176585, -0.0047844606, 0.097556815, -0.011592988, 0.012013718, 0.015077098, 0.0045499443, -0.0176303, -0.0013310025, -0.03185475, 0.030089352, -0.011382753, -0.012648259, -0.040310293, 0.074052624, 0.022759981, -0.09957644, -0.014309189, 0.01888328, 0.027022583, 0.018748462, 0.0977573, -0.041996732, -0.019295692, 0.0007598849, 0.01009917, -0.005741946, -0.008266038, 0.07621349, -0.00014211304, 0.0182401, 0.0011492773, -0.011535379, 0.013265512, -0.0016201893], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, 11, 13, 15, -1, 17, 19, 21, 23, 25, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2908461, 1.7293943, 0.0, 1.5139474, 0.504174, 2.8531446, 0.0, 0.0, 0.0, 0.0, 1.5239575, 1.2689736, 2.423384, 0.0, 2.0099173, 1.8312873, 5.229311, 2.2534535, 1.8915279, 0.0, 1.1966338, 0.0, 2.1911256, 1.8048887, 0.9770348, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2518584, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 22, 22, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, 12, 14, 16, -1, 18, 20, 22, 24, 26, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.011176585, 1.0, 1.0, -0.53846157, 0.012013718, 0.015077098, 0.0045499443, -0.0176303, 1.0, -1.0, -0.07692308, -0.011382753, 1.0, 1.0, 0.15384616, 1.0, 1.0, -0.014309189, 1.0, 0.027022583, 1.0, 1.0, 1.0, -0.019295692, 0.0007598849, 0.01009917, -0.005741946, -0.008266038, 1.0, -0.00014211304, 0.0182401, 0.0011492773, -0.011535379, 0.013265512, -0.0016201893], "split_indices": [117, 125, 0, 114, 58, 1, 0, 0, 0, 0, 39, 0, 1, 0, 0, 5, 1, 53, 93, 0, 93, 0, 69, 109, 12, 0, 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1962.0, 100.0, 1780.0, 182.0, 1688.0, 92.0, 90.0, 92.0, 99.0, 1589.0, 806.0, 783.0, 153.0, 653.0, 301.0, 482.0, 464.0, 189.0, 110.0, 191.0, 106.0, 376.0, 215.0, 249.0, 101.0, 88.0, 92.0, 99.0, 136.0, 240.0, 99.0, 116.0, 144.0, 105.0, 149.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.004829905, 0.012189195, -0.0015477097, -0.007972448, 0.007786553, -0.027360005, 0.05572299, -0.014927568, -0.011762695, -0.012279935, 0.02649628, 0.027566368, -0.049408905, -0.009939834, 0.0074838446, 0.12506579, -0.025547123, -0.004249542, -0.10319244, 0.023359312, 0.003769207, 0.012010243, -0.07360157, 0.0065492205, -0.06740458, -0.019518029, -0.004292452, -0.019774545, -0.017950859, -0.003516725, -0.009893337, 0.0034480707, -0.0068855323], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, -1, 3, 5, -1, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.5461605, 0.0, 1.0020508, 2.2438114, 0.0, 2.6488676, 6.033062, 0.0, 1.8285289, 2.4286766, 0.0, 3.1278353, 1.5325915, 0.0, 0.0, 2.0197585, 2.736652, 1.5107583, 1.5966477, 0.0, 0.0, 0.0, 2.0311563, 0.0, 0.18295234, 0.0, 0.0, 0.0, 0.5418073, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 22, 22, 24, 24, 28, 28], "right_children": [2, -1, 4, 6, -1, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.012189195, 5.0, 1.0, 0.007786553, -0.42307693, 1.0, -0.014927568, 1.0, 1.0, 0.02649628, -0.03846154, 1.0, -0.009939834, 0.0074838446, 1.0, 0.0, 1.0, 1.0, 0.023359312, 0.003769207, 0.012010243, 0.30769232, 0.0065492205, 1.0, -0.019518029, -0.004292452, -0.019774545, 1.0, -0.003516725, -0.009893337, 0.0034480707, -0.0068855323], "split_indices": [1, 0, 0, 61, 0, 1, 0, 0, 106, 17, 0, 1, 12, 0, 0, 53, 0, 15, 39, 0, 0, 0, 1, 0, 39, 0, 0, 0, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 107.0, 1964.0, 1817.0, 147.0, 1393.0, 424.0, 158.0, 1235.0, 320.0, 104.0, 604.0, 631.0, 160.0, 160.0, 213.0, 391.0, 343.0, 288.0, 95.0, 118.0, 97.0, 294.0, 163.0, 180.0, 114.0, 174.0, 91.0, 203.0, 89.0, 91.0, 100.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.001116658, -0.00767641, 0.008574348, -0.016419584, 0.014046724, -0.0046329433, -0.018270941, 0.003928865, -0.009467755, -0.039586656, 0.03213509, -0.010114794, -0.10542803, 0.05796445, -0.008289564, 0.009074784, -0.079381905, -0.019667191, -0.0006073554, 0.11627616, -0.025523098, -0.013439104, -0.003927106, 0.03925069, 0.22112757, 0.0056074928, -0.009512143, 0.013469726, -0.005330353, 0.01641698, 0.027188197], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, -1, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1765975, 2.4868712, 0.0, 3.5534766, 0.0, 1.3052096, 0.0, 1.8975737, 0.0, 1.1798043, 2.7869577, 2.9343147, 1.7043114, 3.7291195, 0.0, 0.0, 0.54940856, 0.0, 0.0, 3.6423788, 1.7889124, 0.0, 0.0, 2.2968357, 0.55215263, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 19, 19, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, -1, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.008574348, 3.0, 0.014046724, 1.0, -0.018270941, 1.0, -0.009467755, 1.0, 1.0, 1.0, 1.0, 1.0, -0.008289564, 0.009074784, 1.0, -0.019667191, -0.0006073554, 1.0, 1.0, -0.013439104, -0.003927106, 1.0, 1.0, 0.0056074928, -0.009512143, 0.013469726, -0.005330353, 0.01641698, 0.027188197], "split_indices": [0, 84, 0, 0, 0, 90, 0, 17, 0, 121, 80, 53, 53, 97, 0, 0, 97, 0, 0, 109, 13, 0, 0, 13, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1920.0, 145.0, 1813.0, 107.0, 1693.0, 120.0, 1546.0, 147.0, 608.0, 938.0, 420.0, 188.0, 766.0, 172.0, 171.0, 249.0, 98.0, 90.0, 451.0, 315.0, 105.0, 144.0, 260.0, 191.0, 145.0, 170.0, 128.0, 132.0, 90.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0019396833, 0.01128634, -0.0042416463, -0.01188796, 0.0046641105, -0.009279371, 0.08274762, 0.0025055732, -0.08112953, 0.018351533, -0.0013017415, -0.068828985, 0.03915228, 0.013511931, -0.023437677, -0.015686305, -0.14260684, 0.12664047, 0.0077218334, -0.010258877, 0.004374375, -0.00699664, -0.02234091, 0.027394762, 0.0023742076, -0.046661615, 0.045847803, -0.016542, 0.007299644, 0.031429406, -0.039269283, 0.005959417, -0.084435344, -0.014515589, -0.0010751948], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, 31, -1, 33, -1, -1], "loss_changes": [1.41588, 0.0, 1.9969547, 0.0, 1.9760925, 1.3039955, 2.653757, 3.4585555, 7.1912804, 0.0, 0.0, 1.7604179, 2.4033186, 0.0, 0.0, 1.3479655, 1.1034672, 3.5014203, 1.3332103, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.7657557, 8.63706, 0.0, 0.0, 0.0, 1.2815335, 0.0, 0.88139665, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 25, 25, 26, 26, 30, 30, 32, 32], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, 32, -1, 34, -1, -1], "split_conditions": [-0.5769231, 0.01128634, -0.5, -0.01188796, 1.0, 1.0, 1.0, -0.115384616, 0.0, 0.018351533, -0.0013017415, -0.26923078, 1.0, 0.013511931, -0.023437677, 1.0, 1.0, 0.3846154, 0.42307693, -0.010258877, 0.004374375, -0.00699664, -0.02234091, 0.027394762, 0.0023742076, 1.0, 1.0, -0.016542, 0.007299644, 0.031429406, 1.0769231, 0.005959417, 1.5, -0.014515589, -0.0010751948], "split_indices": [1, 0, 1, 0, 42, 64, 126, 1, 1, 0, 0, 1, 69, 0, 0, 5, 108, 1, 1, 0, 0, 0, 0, 0, 0, 124, 124, 0, 0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 109.0, 1956.0, 141.0, 1815.0, 1540.0, 275.0, 1323.0, 217.0, 134.0, 141.0, 449.0, 874.0, 90.0, 127.0, 261.0, 188.0, 231.0, 643.0, 106.0, 155.0, 99.0, 89.0, 95.0, 136.0, 265.0, 378.0, 133.0, 132.0, 91.0, 287.0, 90.0, 197.0, 108.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004660889, 0.00439784, -0.07677626, 0.084855795, -0.0073912395, -0.002015457, -0.014537558, -0.012141479, 0.021377495, 0.0031853507, -0.090134084, -0.024256801, 0.029863102, -0.005546847, -0.012596816, -0.010413207, 0.0020639948, 0.09295752, -0.017534645, -0.033694822, 0.009944973, 0.035190012, 0.023737624, -0.011931323, 0.05455851, -0.09862079, 0.03329227, -0.007263986, 0.011867123, -0.004215159, 0.014205815, -0.02061763, 0.0032156915, 0.011455371, -0.0054220012], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, 11, 13, 15, 17, -1, -1, -1, 19, 21, 23, 25, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.346397, 1.7367499, 0.8933679, 6.222582, 1.3975943, 0.0, 0.0, 0.0, 0.0, 1.0366465, 0.22483957, 1.4674621, 2.147203, 0.0, 0.0, 0.0, 1.8282595, 2.5695536, 3.0083916, 1.6700937, 0.0, 1.9803892, 0.0, 0.0, 2.0309038, 2.7428436, 1.3440498, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 9, 9, 10, 10, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, 12, 14, 16, 18, -1, -1, -1, 20, 22, 24, 26, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.46153846, 1.0, 1.0, 1.0, -0.002015457, -0.014537558, -0.012141479, 0.021377495, 1.0, 1.0, -0.1923077, -0.03846154, -0.005546847, -0.012596816, -0.010413207, 1.0, 1.0, 0.46153846, 1.0, 0.009944973, -0.30769232, 0.023737624, -0.011931323, 1.0, 0.7692308, 1.0, -0.007263986, 0.011867123, -0.004215159, 0.014205815, -0.02061763, 0.0032156915, 0.011455371, -0.0054220012], "split_indices": [119, 1, 15, 89, 40, 0, 0, 0, 0, 13, 13, 1, 1, 0, 0, 0, 83, 121, 1, 122, 0, 1, 0, 0, 53, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1831.0, 230.0, 234.0, 1597.0, 126.0, 104.0, 90.0, 144.0, 1416.0, 181.0, 698.0, 718.0, 92.0, 89.0, 173.0, 525.0, 308.0, 410.0, 384.0, 141.0, 220.0, 88.0, 170.0, 240.0, 195.0, 189.0, 96.0, 124.0, 114.0, 126.0, 107.0, 88.0, 98.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005613425, 0.0114666475, -0.011180936, 0.0053538624, 0.011980846, 0.017955588, -0.10169649, 0.122122444, 0.0038156093, -0.0029467328, -0.019034134, 0.019479422, 0.005706388, 0.016320355, -0.086668156, 0.006333261, 0.012655172, -0.006938399, -0.01035682, -0.013762179, 0.09460639, 0.04342416, -0.04997696, 0.023749141, -0.0075426768, 0.018300807, -0.0077054673, -0.01910415, -0.017320653, 0.010731274, -0.0069091613, -0.008990007, 0.00309147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, -1, -1, 15, 17, 19, -1, -1, -1, 21, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.4185907, 1.3020228, 0.0, 2.5105247, 0.0, 2.4524064, 1.2549369, 0.94085574, 1.6587443, 0.0, 0.0, 0.0, 0.0, 1.4179474, 0.051993966, 2.0949612, 0.0, 0.0, 0.0, 1.9922934, 5.320648, 2.6620543, 2.240817, 0.0, 0.0, 0.0, 1.927523, 1.6678728, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, -1, -1, 16, 18, 20, -1, -1, -1, 22, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.011180936, 1.0, 0.011980846, -0.46153846, 1.0, 1.0, 1.0, -0.0029467328, -0.019034134, 0.019479422, 0.005706388, 5.0, 1.0, 1.0, 0.012655172, -0.006938399, -0.01035682, 1.0, 1.0, 1.0, 1.0, 0.023749141, -0.0075426768, 0.018300807, 1.0, 1.0, -0.017320653, 0.010731274, -0.0069091613, -0.008990007, 0.00309147], "split_indices": [117, 102, 0, 119, 0, 1, 97, 53, 40, 0, 0, 0, 0, 0, 13, 61, 0, 0, 0, 53, 23, 81, 0, 0, 0, 0, 17, 81, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1966.0, 98.0, 1861.0, 105.0, 1665.0, 196.0, 199.0, 1466.0, 108.0, 88.0, 94.0, 105.0, 1288.0, 178.0, 1181.0, 107.0, 88.0, 90.0, 962.0, 219.0, 373.0, 589.0, 119.0, 100.0, 100.0, 273.0, 471.0, 118.0, 95.0, 178.0, 195.0, 276.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0018024752, 0.00696587, -0.010104389, 0.0121005075, -0.008812758, 0.005579938, 0.011536258, 0.017211402, -0.01739396, 0.0095509635, 0.015036526, -0.003668732, 0.07042863, -0.04377692, 0.03097015, -0.003621264, 0.01919501, -0.015812162, -0.009775732, -0.016773801, 0.072191715, -0.08865439, 0.057512585, 0.008018832, -0.05598961, 0.019964337, -0.0076779877, -0.022103492, 0.00076223287, 0.017733626, -0.001968922, -0.0025187144, -0.010305851, -0.006178374, 0.007054007], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, -1, 21, 23, 25, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0886245, 0.9531037, 0.0, 1.2470022, 0.0, 3.637426, 0.0, 1.6687472, 0.0, 1.2450016, 0.0, 1.7658037, 3.5767403, 2.289946, 1.342231, 0.0, 0.0, 0.0, 2.4096563, 1.2015735, 3.725707, 2.6637397, 2.2663984, 0.0, 0.32621312, 0.0, 0.95221055, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, -1, 22, 24, 26, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010104389, 3.1923077, -0.008812758, 1.3461539, 0.011536258, 1.0, -0.01739396, 1.0, 0.015036526, 1.0, 1.0, -0.5, 1.0, -0.003621264, 0.01919501, -0.015812162, 1.0, 0.03846154, 1.0, 1.0, -0.26923078, 0.008018832, 1.0, 0.019964337, 0.26923078, -0.022103492, 0.00076223287, 0.017733626, -0.001968922, -0.0025187144, -0.010305851, -0.006178374, 0.007054007], "split_indices": [117, 43, 0, 1, 0, 1, 0, 84, 0, 62, 0, 16, 97, 1, 71, 0, 0, 0, 93, 1, 121, 53, 1, 0, 23, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1952.0, 98.0, 1852.0, 100.0, 1742.0, 110.0, 1636.0, 106.0, 1547.0, 89.0, 1271.0, 276.0, 589.0, 682.0, 147.0, 129.0, 135.0, 454.0, 316.0, 366.0, 209.0, 245.0, 91.0, 225.0, 141.0, 225.0, 88.0, 121.0, 96.0, 149.0, 136.0, 89.0, 133.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026789783, 0.009403728, -0.007924109, -0.013883838, 0.0065032556, -0.0321433, 0.04603805, -0.013291827, -0.019119069, -0.016841268, 0.024072213, 0.015776753, -0.04784774, -0.010639997, 0.046993107, 0.12314632, -0.04311292, -0.06480668, 0.0038006864, 0.016290357, -0.00847233, 0.0040503903, 0.021274807, -0.09323472, 0.007586777, 0.0012038911, -0.08835614, -0.0022445624, -0.021788504, -0.1185628, -0.0028770368, -0.016989907, -0.0073266076], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, -1, 3, 5, -1, 7, 9, -1, 11, 13, -1, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [1.0551617, 0.0, 0.85786426, 1.9957138, 0.0, 1.8348978, 5.214923, 0.0, 1.2411083, 1.8408496, 0.0, 3.534534, 0.98862624, 0.0, 2.8702543, 1.4661703, 2.152833, 1.0260844, 0.0, 0.0, 0.0, 0.0, 0.0, 2.2412655, 0.0, 0.0, 0.7811508, 0.0, 0.0, 0.66970444, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 23, 23, 26, 26, 29, 29], "right_children": [2, -1, 4, 6, -1, 8, 10, -1, 12, 14, -1, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [-0.5769231, 0.009403728, 5.0, 1.0, 0.0065032556, -0.42307693, 1.0, -0.013291827, 1.0, 1.0, 0.024072213, -0.03846154, 1.0, -0.010639997, 0.6923077, -0.23076923, 1.0, 1.0, 0.0038006864, 0.016290357, -0.00847233, 0.0040503903, 0.021274807, 1.0, 0.007586777, 0.0012038911, 1.0, -0.0022445624, -0.021788504, 1.0, -0.0028770368, -0.016989907, -0.0073266076], "split_indices": [1, 0, 0, 61, 0, 1, 0, 0, 122, 108, 0, 1, 105, 0, 1, 1, 23, 5, 0, 0, 0, 0, 0, 121, 0, 0, 80, 0, 0, 137, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 107.0, 1973.0, 1824.0, 149.0, 1398.0, 426.0, 160.0, 1238.0, 322.0, 104.0, 559.0, 679.0, 134.0, 188.0, 198.0, 361.0, 567.0, 112.0, 100.0, 88.0, 103.0, 95.0, 254.0, 107.0, 133.0, 434.0, 162.0, 92.0, 288.0, 146.0, 135.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.007877119, -0.012965943, 0.009727403, -0.019755209, 0.007681962, -0.010495341, -0.016412495, -0.020406486, 0.015482663, -0.0306068, 0.010142604, -0.05636633, 0.0010351231, 0.005387999, -0.07742641, -0.07452391, 0.070117675, -0.107933275, 0.007892222, -0.13919899, 0.0054221777, -0.0017053289, 0.12419598, -0.1648942, -0.04099201, -0.011959136, 0.0132604465, -0.025321906, -0.0036377292, 0.0012518635, 0.022983668, -0.011924765, -0.025569111, 0.0008710368, -0.012345281], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1012266, 1.1966016, 0.0, 2.4397407, 0.0, 2.8100789, 0.0, 2.0107367, 0.0, 1.2169162, 0.0, 1.9108391, 3.4972737, 0.0, 1.7985373, 2.664524, 1.6499206, 1.940835, 2.893575, 2.4971557, 0.0, 0.0, 2.5482967, 1.1397548, 0.95904887, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.009727403, 3.0, 0.007681962, 1.0, -0.016412495, 1.0, 0.015482663, 1.0, 0.010142604, 1.0, 1.0, 0.005387999, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0054221777, -0.0017053289, 1.0, 0.0, 0.84615386, -0.011959136, 0.0132604465, -0.025321906, -0.0036377292, 0.0012518635, 0.022983668, -0.011924765, -0.025569111, 0.0008710368, -0.012345281], "split_indices": [114, 0, 0, 0, 0, 102, 0, 125, 0, 12, 0, 26, 111, 0, 105, 61, 69, 137, 93, 137, 0, 0, 2, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1963.0, 95.0, 1825.0, 138.0, 1715.0, 110.0, 1618.0, 97.0, 1493.0, 125.0, 823.0, 670.0, 132.0, 691.0, 320.0, 350.0, 509.0, 182.0, 213.0, 107.0, 134.0, 216.0, 275.0, 234.0, 90.0, 92.0, 101.0, 112.0, 105.0, 111.0, 183.0, 92.0, 146.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0033373516, 0.011499142, -0.060936738, 0.03710118, -0.022484705, -0.011597446, 0.002590051, -0.035621118, 0.07186347, -0.08128571, 0.03184857, 0.008685349, -0.09259844, 0.020886062, 0.2162071, -0.02172199, -0.020428719, 0.01936069, -0.038778298, -0.002806376, -0.013801171, -0.034278385, 0.016812077, 0.027332455, 0.016152015, -0.008962142, 0.008636279, -0.013453409, 0.002175696, 0.012667072, -0.016349463, 0.01647482, -0.06755153, -0.01160705, -0.0019032538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1], "loss_changes": [1.0801342, 1.589592, 1.1088035, 2.63417, 2.5079582, 0.0, 0.0, 2.3516788, 5.1875772, 2.7620618, 4.6611896, 0.0, 0.67406774, 4.231626, 0.5747366, 1.8640796, 0.0, 0.0, 1.6462352, 0.0, 0.0, 2.2990582, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.3915272, 0.0, 0.0, 0.42844427, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 21, 21, 29, 29, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1], "split_conditions": [1.0, 0.1923077, 1.0, 1.0, 0.88461536, -0.011597446, 0.002590051, 1.0, -0.115384616, 1.0, 1.0, 0.008685349, -0.30769232, 1.0, 1.0, 1.0, -0.020428719, 0.01936069, 1.0, -0.002806376, -0.013801171, 1.0, 0.016812077, 0.027332455, 0.016152015, -0.008962142, 0.008636279, -0.013453409, 0.002175696, 1.0, -0.016349463, 0.01647482, -0.34615386, -0.01160705, -0.0019032538], "split_indices": [119, 1, 58, 127, 1, 0, 0, 81, 1, 50, 108, 0, 1, 61, 106, 124, 0, 0, 59, 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 115, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1827.0, 232.0, 1042.0, 785.0, 142.0, 90.0, 337.0, 705.0, 377.0, 408.0, 107.0, 230.0, 521.0, 184.0, 254.0, 123.0, 124.0, 284.0, 95.0, 135.0, 379.0, 142.0, 90.0, 94.0, 156.0, 98.0, 110.0, 174.0, 278.0, 101.0, 96.0, 182.0, 91.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0004452977, 0.009739322, -0.004693632, -0.03466853, 0.01081542, -0.008626914, -0.013045795, 0.113209605, -0.007369337, -0.012177143, 0.03651052, -0.0025820017, 0.024260355, 0.015067708, -0.013070672, 0.012085012, -0.030157909, 0.061479878, -0.018071603, 0.006048752, -0.011256288, -0.008844678, 0.027172858, 0.012584589, -0.058996018, -0.012358242, 0.060251515, 0.005576729, -0.09010656, -0.0023820885, 0.014715785, -0.014963311, -0.017909197, -0.009747609, 0.006940366], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, -1, 15, -1, -1, 17, -1, -1, 19, 21, 23, -1, -1, 25, -1, -1, 27, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [1.0292991, 0.0, 0.9120992, 1.668828, 2.4075837, 2.6863103, 0.0, 3.507971, 3.0385249, 0.0, 2.114169, 0.0, 0.0, 1.4288647, 0.0, 0.0, 1.5686232, 5.722046, 3.1922386, 0.0, 0.0, 2.299103, 0.0, 0.0, 1.5066873, 0.0, 1.3224632, 0.0, 2.2199683, 0.0, 0.0, 1.2530437, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 24, 24, 26, 26, 28, 28, 31, 31], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, -1, 16, -1, -1, 18, -1, -1, 20, 22, 24, -1, -1, 26, -1, -1, 28, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [-0.5769231, 0.009739322, -0.1923077, -0.26923078, -0.03846154, 1.0, -0.013045795, 1.0, 1.0, -0.012177143, 1.0, -0.0025820017, 0.024260355, 1.0, -0.013070672, 0.012085012, 1.0, 0.65384614, 0.0, 0.006048752, -0.011256288, 1.0, 0.027172858, 0.012584589, 1.0, -0.012358242, 1.0, 0.005576729, 1.1923077, -0.0023820885, 0.014715785, 0.53846157, -0.017909197, -0.009747609, 0.006940366], "split_indices": [1, 0, 1, 1, 1, 89, 0, 111, 64, 0, 53, 0, 0, 124, 0, 0, 13, 1, 0, 0, 0, 106, 0, 0, 26, 0, 111, 0, 1, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 104.0, 1962.0, 669.0, 1293.0, 526.0, 143.0, 195.0, 1098.0, 150.0, 376.0, 94.0, 101.0, 929.0, 169.0, 166.0, 210.0, 387.0, 542.0, 100.0, 110.0, 290.0, 97.0, 120.0, 422.0, 109.0, 181.0, 90.0, 332.0, 92.0, 89.0, 180.0, 152.0, 91.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014142165, 0.00431651, -0.011762393, 0.013834942, -0.06385283, 0.0040287287, 0.018408746, -0.017401902, 0.004183503, -0.03278012, 0.04193635, -0.009002594, -0.014202818, 0.012976161, 0.018211039, -0.07788524, 0.04668975, 0.07155658, -0.08334124, -0.014508198, -0.0016605167, -0.008042605, 0.016345212, 0.12903778, -0.0051679625, -0.015740467, -0.00050855224, 0.0068670893, -0.008596421, 0.022686664, 0.0739077, -0.0005242023, 0.01504765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [1.3745544, 1.2763177, 0.0, 2.8816128, 2.8060184, 2.277188, 0.0, 0.0, 0.0, 2.1508522, 1.6752797, 2.6086404, 0.0, 0.0, 3.4291897, 1.2518173, 2.4028957, 2.9397628, 1.2635025, 0.0, 0.0, 1.5302757, 0.0, 1.5263066, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0969329, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 23, 23, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.3461539, -0.011762393, 1.1538461, 3.1923077, 1.0, 0.018408746, -0.017401902, 0.004183503, 1.0, 1.0, 1.0, -0.014202818, 0.012976161, 1.0, 1.0, 1.0, 1.0, 1.0, -0.014508198, -0.0016605167, 1.0, 0.016345212, -0.30769232, -0.0051679625, -0.015740467, -0.00050855224, 0.0068670893, -0.008596421, 0.022686664, 1.0, -0.0005242023, 0.01504765], "split_indices": [14, 1, 0, 1, 1, 124, 0, 0, 0, 50, 81, 15, 0, 0, 97, 106, 62, 0, 53, 0, 0, 39, 0, 1, 0, 0, 0, 0, 0, 0, 53, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1967.0, 97.0, 1726.0, 241.0, 1632.0, 94.0, 118.0, 123.0, 828.0, 804.0, 680.0, 148.0, 171.0, 633.0, 304.0, 376.0, 415.0, 218.0, 145.0, 159.0, 256.0, 120.0, 283.0, 132.0, 112.0, 106.0, 129.0, 127.0, 102.0, 181.0, 89.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0020187339, 0.0071685296, -0.009352221, 1.3795893e-05, 0.07535119, -0.0072572283, 0.008985504, 0.0196441, -0.0058122757, -0.013569027, 0.009832815, -0.0045844214, -0.01388301, 0.00257252, -0.009720778, -0.04241225, 0.014211643, 0.0042976676, -0.010417156, 0.0049820715, 0.010896199, 0.015679825, -0.009074626, -0.0019850328, 0.0049143354], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, -1, 17, 19, -1, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.0101093, 0.9502902, 0.0, 1.1516584, 2.9900324, 1.086953, 0.0, 0.0, 0.0, 1.7320228, 0.0, 0.9519247, 0.0, 0.6979364, 0.0, 1.4449561, 0.9261005, 0.0, 0.0, 0.9882352, 0.0, 1.032021, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 19, 19, 21, 21], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, -1, 18, 20, -1, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009352221, 5.0, 1.0, 1.0, 0.008985504, 0.0196441, -0.0058122757, 3.0, 0.009832815, 1.0, -0.01388301, -1.0, -0.009720778, 1.0, 1.0, 0.0042976676, -0.010417156, 1.0, 0.010896199, 1.0, -0.009074626, -0.0019850328, 0.0049143354], "split_indices": [43, 125, 0, 0, 15, 84, 0, 0, 0, 0, 0, 90, 0, 0, 0, 122, 73, 0, 0, 44, 0, 71, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1948.0, 105.0, 1763.0, 185.0, 1631.0, 132.0, 97.0, 88.0, 1539.0, 92.0, 1436.0, 103.0, 1333.0, 103.0, 274.0, 1059.0, 115.0, 159.0, 965.0, 94.0, 868.0, 97.0, 421.0, 447.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.001881775, 0.002618118, -0.009075469, -0.0041156597, 0.06855764, -0.009717425, 0.0022065549, 0.015617673, -0.0021982063, 0.011270201, -0.052364927, 0.00043348887, 0.016458113, -0.015909612, 0.0053476826, -0.006968055, 0.009841198, -0.005627472, 0.048444435, -0.048871886, 0.072269745, 0.020641403, -0.08462776, 0.013559177, -0.024569657, 0.00034443743, 0.016457042, 0.0018058205, -0.019423641, -0.0052837185, 0.010208768], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, 17, 19, 21, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.82983136, 0.8769438, 0.0, 1.0542991, 1.4517407, 0.0, 0.82996655, 0.0, 0.0, 2.3907344, 2.699892, 0.886519, 0.0, 0.0, 0.0, 0.0, 0.7076119, 2.849852, 7.126242, 6.6846585, 1.9184926, 0.0, 2.07097, 2.427602, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 11, 11, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, 18, 20, 22, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009075469, -0.53846157, 1.0, -0.009717425, 1.3461539, 0.015617673, -0.0021982063, 1.1538461, 3.1923077, 1.0, 0.016458113, -0.015909612, 0.0053476826, -0.006968055, 1.0, 1.0, 1.0, 1.0, 1.0, 0.020641403, 1.0, 1.0, -0.024569657, 0.00034443743, 0.016457042, 0.0018058205, -0.019423641, -0.0052837185, 0.010208768], "split_indices": [117, 125, 0, 1, 15, 0, 1, 0, 0, 1, 1, 26, 0, 0, 0, 0, 105, 109, 109, 113, 113, 0, 39, 124, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1975.0, 100.0, 1792.0, 183.0, 114.0, 1678.0, 93.0, 90.0, 1439.0, 239.0, 1344.0, 95.0, 119.0, 120.0, 159.0, 1185.0, 846.0, 339.0, 544.0, 302.0, 155.0, 184.0, 413.0, 131.0, 173.0, 129.0, 95.0, 89.0, 236.0, 177.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.001436504, 0.0064151855, -0.063910134, -0.0022094075, 0.013563411, -0.011911796, 0.0018011129, -0.017376611, 0.058371473, -0.039785553, 0.02272359, 0.017119026, -0.0056426562, -0.018485902, -0.017671185, -0.017537087, 0.089969814, -0.03387551, 0.009990914, 0.0054990384, -0.06234465, -0.0036027224, 0.022885295, -0.0017947536, -0.018902466, 4.13669e-05, -0.0115963435, -0.020595757, 0.0074425503, 0.008620597, -0.010003529, -0.0042900317, 0.0089524565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, -1, -1, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [1.0148933, 2.048379, 1.0447428, 1.5831658, 0.0, 0.0, 0.0, 1.2382749, 4.4682245, 2.5781698, 1.3374453, 0.0, 0.0, 1.3938706, 0.0, 1.0041817, 3.2372904, 3.3696334, 0.0, 0.0, 0.6427201, 0.0, 0.0, 0.80392253, 0.0, 0.0, 0.0, 1.0444205, 0.0, 1.3713531, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 16, 16, 17, 17, 20, 20, 23, 23, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, -1, -1, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.013563411, -0.011911796, 0.0018011129, 1.0, 1.0, 1.0, 1.0, 0.017119026, -0.0056426562, 1.6923077, -0.017671185, 1.0, -0.30769232, 1.0, 0.009990914, 0.0054990384, 0.30769232, -0.0036027224, 0.022885295, 1.0, -0.018902466, 4.13669e-05, -0.0115963435, 0.5769231, 0.0074425503, 1.0, -0.010003529, -0.0042900317, 0.0089524565], "split_indices": [119, 125, 58, 105, 0, 0, 0, 109, 109, 61, 113, 0, 0, 1, 0, 108, 1, 113, 0, 0, 1, 0, 0, 116, 0, 0, 0, 1, 0, 124, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1838.0, 231.0, 1723.0, 115.0, 138.0, 93.0, 1378.0, 345.0, 884.0, 494.0, 174.0, 171.0, 765.0, 119.0, 309.0, 185.0, 677.0, 88.0, 118.0, 191.0, 97.0, 88.0, 561.0, 116.0, 88.0, 103.0, 450.0, 111.0, 329.0, 121.0, 201.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00056084554, -0.0040906398, 0.008235997, 0.0045472165, -0.08018192, -0.0021179814, 0.009061294, -0.015319782, -0.0022295408, 0.0109078465, -0.012833005, -0.0676409, 0.009673658, -0.032734483, -0.018505337, 0.016646054, -0.00938146, -0.010013801, 0.01615925, 0.008667122, -0.012569456, -0.007347343, 0.010126506, 0.06429394, -0.027512917, -0.0028014726, 0.017570099, 0.010976477, -0.06797372, -0.01860118, -0.00065939047], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1], "loss_changes": [0.7849457, 1.2829826, 0.0, 1.0056, 0.8411002, 1.9385254, 0.0, 0.0, 0.0, 0.0, 1.8305779, 1.7705293, 3.1429474, 1.0974383, 0.0, 0.0, 1.9691312, 0.0, 1.4722548, 1.6342155, 0.0, 0.0, 0.0, 3.2908275, 2.7327473, 0.0, 0.0, 0.0, 2.7531583, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 18, 18, 19, 19, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 0.008235997, 1.0, -0.1923077, -0.5, 0.009061294, -0.015319782, -0.0022295408, 0.0109078465, -0.15384616, 1.0, -0.03846154, 1.0, -0.018505337, 0.016646054, 1.0, -0.010013801, 1.0, 1.0, -0.012569456, -0.007347343, 0.010126506, 0.42307693, 0.0, -0.0028014726, 0.017570099, 0.010976477, 1.0, -0.01860118, -0.00065939047], "split_indices": [102, 119, 0, 90, 1, 1, 0, 0, 0, 0, 1, 62, 1, 13, 0, 0, 64, 0, 93, 124, 0, 0, 0, 1, 0, 0, 0, 0, 137, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1952.0, 111.0, 1753.0, 199.0, 1627.0, 126.0, 88.0, 111.0, 143.0, 1484.0, 432.0, 1052.0, 333.0, 99.0, 114.0, 938.0, 140.0, 193.0, 812.0, 126.0, 94.0, 99.0, 320.0, 492.0, 175.0, 145.0, 112.0, 380.0, 130.0, 250.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029583722, 0.0019589919, -0.009289561, -0.006233699, 0.050420363, -0.03395272, 0.022087907, 0.01216226, -0.0024393555, -0.0070523745, -0.016619707, 0.11604603, -0.0036421574, -0.039970007, 0.081187546, 0.025098944, -0.0018897363, 0.023346232, -0.01613112, -0.006286231, -0.017874714, 0.019566586, -0.0020822806, -0.00852479, 0.01537543, -0.06643619, 0.009768155, -0.013932605, 0.03326969, 0.0012244938, -0.013443917, 0.09454816, -0.008400461, 0.0031089338, 0.015687376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, -1, -1, 27, -1, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [0.9128128, 0.7769859, 0.0, 1.3141692, 1.5075183, 3.0095754, 2.0017307, 0.0, 0.0, 2.0419683, 0.0, 3.2413313, 2.7659023, 2.3933628, 2.2304935, 0.0, 0.0, 2.3067129, 0.0, 2.5765066, 0.0, 0.0, 0.0, 2.43818, 0.0, 1.3964944, 0.0, 0.0, 2.4289994, 0.0, 0.0, 0.87803364, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 23, 23, 25, 25, 28, 28, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, -1, -1, 28, -1, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.2692307, -0.009289561, 1.0, 1.0, 1.0, 1.0, 0.01216226, -0.0024393555, 1.0, -0.016619707, 0.03846154, 3.0, 1.0, 1.0, 0.025098944, -0.0018897363, 1.0, -0.01613112, 1.0, -0.017874714, 0.019566586, -0.0020822806, 1.0, 0.01537543, -0.03846154, 0.009768155, -0.013932605, 1.0, 0.0012244938, -0.013443917, 1.0, -0.008400461, 0.0031089338, 0.015687376], "split_indices": [43, 1, 0, 124, 115, 64, 81, 0, 0, 0, 0, 1, 0, 61, 13, 0, 0, 64, 0, 105, 0, 0, 0, 89, 0, 1, 0, 0, 97, 0, 0, 13, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1957.0, 107.0, 1674.0, 283.0, 846.0, 828.0, 145.0, 138.0, 703.0, 143.0, 178.0, 650.0, 512.0, 191.0, 89.0, 89.0, 555.0, 95.0, 412.0, 100.0, 90.0, 101.0, 446.0, 109.0, 261.0, 151.0, 108.0, 338.0, 121.0, 140.0, 222.0, 116.0, 110.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0029346347, -0.03329785, 0.012822297, 0.006119644, -0.014875975, 0.0029869168, 0.014846111, -0.04632128, 0.012058208, -0.018433526, 0.068451, -0.013256024, 0.0011382705, -0.0025324908, -0.012132256, -0.0071144337, 0.14241719, 0.028546713, -0.030966662, -0.0005699769, -0.0008425914, 0.0045507667, 0.023433134, 0.07502798, -0.0050604227, -0.05531848, 0.00415268, 0.019817023, -0.003706303, -0.10268131, 0.0014630767, -0.00266272, -0.01686397, 0.001973966, -0.001681347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.74194527, 2.0207417, 2.170514, 1.986833, 0.0, 2.1272435, 0.0, 1.1296276, 0.0, 1.8699956, 2.090394, 0.0, 0.0, 0.8748743, 0.0, 0.0003431877, 1.6834898, 1.7401841, 0.91268474, 0.0, 0.0, 0.0, 0.0, 4.113336, 0.0, 1.0407728, 0.0, 0.0, 0.0, 1.0584617, 0.058789782, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 25, 25, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 5.0, 1.0, -0.014875975, 1.0, 0.014846111, 1.0, 0.012058208, 2.0, 1.0, -0.013256024, 0.0011382705, 1.0, -0.012132256, 1.0, 1.0, 1.0, 1.0, -0.0005699769, -0.0008425914, 0.0045507667, 0.023433134, 1.0, -0.0050604227, 1.0, 0.00415268, 0.019817023, -0.003706303, 1.0, 1.0, -0.00266272, -0.01686397, 0.001973966, -0.001681347], "split_indices": [5, 0, 0, 83, 0, 61, 0, 13, 0, 0, 17, 0, 0, 122, 0, 97, 13, 121, 58, 0, 0, 0, 0, 13, 0, 13, 0, 0, 0, 124, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 444.0, 1627.0, 331.0, 113.0, 1517.0, 110.0, 227.0, 104.0, 1143.0, 374.0, 91.0, 136.0, 990.0, 153.0, 185.0, 189.0, 473.0, 517.0, 89.0, 96.0, 92.0, 97.0, 298.0, 175.0, 387.0, 130.0, 142.0, 156.0, 211.0, 176.0, 98.0, 113.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003666065, 0.0034528237, -0.007656234, -0.003299973, 0.07079323, -0.009539499, 0.0028871456, 0.008592969, 0.0056314846, -0.0030659346, 0.0073369816, 0.0073362975, -0.04698648, 0.016674697, -0.041585755, 0.0062668486, -0.012343167, -0.023466924, 0.052420206, -0.0114251245, 0.003328168, -0.058453534, 0.0077119553, 0.07647956, -0.0025818592, 0.0040527717, -0.11381594, -0.0022045386, 0.115116805, -0.018096393, -0.0070146257, 0.017928641, 0.0028241018], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.6035846, 0.89810395, 0.0, 1.0227962, 0.03944677, 0.0, 0.705749, 0.0, 0.0, 0.7086082, 0.0, 0.57289445, 2.489631, 1.5109316, 1.0934963, 0.0, 0.0, 1.7455132, 1.0484831, 0.0, 0.0, 2.0165815, 0.0, 1.6216686, 0.0, 0.0, 0.6920295, 0.0, 1.7058835, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 23, 23, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.007656234, -0.53846157, 1.0, -0.009539499, 5.0, 0.008592969, 0.0056314846, 1.0, 0.0073369816, 1.3461539, 1.0, 1.0, 3.5, 0.0062668486, -0.012343167, 0.3846154, 0.7692308, -0.0114251245, 0.003328168, 0.0, 0.0077119553, -1.0, -0.0025818592, 0.0040527717, 1.0, -0.0022045386, 1.0, -0.018096393, -0.0070146257, 0.017928641, 0.0028241018], "split_indices": [117, 125, 0, 1, 39, 0, 0, 0, 0, 0, 0, 1, 59, 124, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 59, 0, 113, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1975.0, 99.0, 1795.0, 180.0, 113.0, 1682.0, 88.0, 92.0, 1551.0, 131.0, 1254.0, 297.0, 1053.0, 201.0, 122.0, 175.0, 496.0, 557.0, 102.0, 99.0, 368.0, 128.0, 426.0, 131.0, 132.0, 236.0, 120.0, 306.0, 93.0, 143.0, 176.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0009122671, 0.00527656, -0.008593272, -0.032049816, 0.012596792, 0.003505626, -0.0840386, 0.018684585, -0.008798127, 0.0007667123, -0.01698908, -0.01212265, 0.038992345, -0.080973566, 0.033222705, 0.05924912, -0.0053610024, -0.014182893, -0.0002487193, -0.0070638885, 0.01576242, 0.023428695, 0.12569666, 0.006217861, -0.0072008595, 0.06557658, -0.012916224, -0.0010891382, 0.19323188, 0.10223047, -0.0051873485, 0.03279035, 0.0064415485, -0.0007524413, 0.15280381, 0.026849717, 0.004792289], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [0.7841859, 0.5382782, 0.0, 1.1268708, 1.0084552, 0.0, 1.4329115, 0.97159725, 0.0, 0.0, 0.0, 1.926317, 1.7557724, 1.1701971, 1.8643574, 1.8279774, 0.0, 0.0, 0.0, 1.2636384, 0.0, 3.209261, 2.4813914, 0.0, 0.0, 1.6832558, 0.0, 0.0, 3.1226234, 1.6541004, 0.0, 0.0, 0.0, 0.0, 2.4753408, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22, 25, 25, 28, 28, 29, 29, 34, 34], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, -1.0, -0.008593272, 1.0, 1.0, 0.003505626, 1.0, 1.0, -0.008798127, 0.0007667123, -0.01698908, 0.15384616, 0.5769231, 1.0, 1.0, -0.115384616, -0.0053610024, -0.014182893, -0.0002487193, 1.0, 0.01576242, 3.0, 1.0, 0.006217861, -0.0072008595, 1.0, -0.012916224, -0.0010891382, 0.115384616, 1.0, -0.0051873485, 0.03279035, 0.0064415485, -0.0007524413, -0.42307693, 0.026849717, 0.004792289], "split_indices": [117, 0, 0, 127, 43, 0, 69, 127, 0, 0, 0, 1, 1, 12, 93, 1, 0, 0, 0, 53, 0, 0, 71, 0, 0, 7, 0, 0, 1, 89, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1970.0, 99.0, 323.0, 1647.0, 141.0, 182.0, 1553.0, 94.0, 88.0, 94.0, 617.0, 936.0, 245.0, 372.0, 768.0, 168.0, 138.0, 107.0, 281.0, 91.0, 499.0, 269.0, 136.0, 145.0, 391.0, 108.0, 89.0, 180.0, 298.0, 93.0, 88.0, 92.0, 94.0, 204.0, 97.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.002600927, -0.052591562, 0.010121663, 0.0061692013, -0.014368719, -0.00022096024, 0.06724706, -0.011119386, 0.048647415, 0.014838244, -0.0013308763, -0.04274328, 0.021941954, 0.018592486, -0.022588432, -0.0060395836, -0.11806412, 0.076784745, -0.027807072, 0.0049692686, -0.010055461, -0.06812455, 0.0546279, -0.016507473, -0.0074072527, -0.00092611293, 0.017888397, 0.06626064, -0.018693833, 0.0001596854, -0.014911408, -0.0015540887, 0.015332689, 0.017641293, -0.007483326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, 13, -1, -1, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8584022, 2.5818624, 1.0753044, 0.0, 0.0, 0.8207185, 1.8235242, 1.3173658, 2.7479205, 0.0, 0.0, 1.780372, 1.6806794, 0.0, 1.0425645, 1.630911, 0.43636227, 2.574069, 4.8350234, 0.0, 0.0, 1.208395, 1.5167043, 0.0, 0.0, 0.0, 0.0, 3.154989, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, 14, -1, -1, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5, -0.5769231, 1.0, 0.0061692013, -0.014368719, 1.2692307, 1.0, 1.0, 1.0, 0.014838244, -0.0013308763, 1.0, 1.0, 0.018592486, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0049692686, -0.010055461, 1.0, 1.0, -0.016507473, -0.0074072527, -0.00092611293, 0.017888397, 1.0, -0.018693833, 0.0001596854, -0.014911408, -0.0015540887, 0.015332689, 0.017641293, -0.007483326], "split_indices": [1, 1, 42, 0, 0, 1, 126, 15, 137, 0, 0, 50, 69, 0, 106, 106, 126, 97, 105, 0, 0, 127, 2, 0, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 248.0, 1820.0, 110.0, 138.0, 1541.0, 279.0, 1260.0, 281.0, 139.0, 140.0, 644.0, 616.0, 96.0, 185.0, 433.0, 211.0, 293.0, 323.0, 96.0, 89.0, 214.0, 219.0, 102.0, 109.0, 159.0, 134.0, 203.0, 120.0, 115.0, 99.0, 128.0, 91.0, 114.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.002498578, 0.007661587, -0.00681318, -0.032459676, 0.0100389905, -0.05770983, 0.043031804, 0.017041473, -0.0036318991, -0.021290055, -0.020276636, -0.0059555843, 0.013656764, -0.015357168, 0.010012617, -0.012331311, 0.021193702, 0.069286846, -0.029175323, -0.0434445, 0.013526114, 0.005427278, 0.029424665, -0.10109849, 0.0017465085, 0.0040254476, -0.015948175, 0.014719109, -0.056266222, -0.002858812, -0.017281203, -0.00959459, 0.044424433, -0.00037889704, -0.012186275, -0.0003368396, 0.010113386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, -1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, -1, 17, -1, 19, 21, 23, 25, -1, 27, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [0.70624757, 0.0, 0.8479748, 1.4830017, 2.5958955, 3.079946, 1.8711463, 0.0, 2.2320294, 2.0197945, 0.0, 0.0, 0.0, 0.0, 2.322835, 0.0, 2.425755, 5.717602, 1.3388458, 2.0395622, 0.0, 2.7112312, 0.0, 0.9411949, 1.7552793, 0.0, 0.0, 0.0, 0.743542, 0.0, 0.0, 0.0, 0.7941193, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 28, 28, 32, 32], "right_children": [2, -1, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, -1, 18, -1, 20, 22, 24, 26, -1, 28, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.007661587, -0.115384616, 1.0, -0.03846154, 1.0, 1.0, 0.017041473, 0.03846154, 1.0, -0.020276636, -0.0059555843, 0.013656764, -0.015357168, 1.0, -0.012331311, 1.0, 0.7692308, 1.0, 1.0, 0.013526114, 0.1923077, 0.029424665, 0.88461536, 1.0, 0.0040254476, -0.015948175, 0.014719109, 1.0, -0.002858812, -0.017281203, -0.00959459, 1.0, -0.00037889704, -0.012186275, -0.0003368396, 0.010113386], "split_indices": [1, 0, 1, 61, 1, 113, 113, 0, 1, 81, 0, 0, 0, 0, 124, 0, 97, 1, 137, 121, 0, 1, 0, 1, 81, 0, 0, 0, 12, 0, 0, 0, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 107.0, 1962.0, 778.0, 1184.0, 583.0, 195.0, 93.0, 1091.0, 466.0, 117.0, 93.0, 102.0, 91.0, 1000.0, 137.0, 329.0, 398.0, 602.0, 210.0, 119.0, 310.0, 88.0, 181.0, 421.0, 122.0, 88.0, 94.0, 216.0, 90.0, 91.0, 128.0, 293.0, 120.0, 96.0, 159.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.007459262, 0.0031903828, 0.0077461614, -0.0024590753, 0.007733439, 0.00489514, -0.010812094, -0.014558259, 0.031050064, 0.008530227, -0.08158031, 0.016295282, 0.011148198, 0.029078847, -0.007323613, -0.018222045, -0.0011959377, -0.04250798, 0.060983665, 0.06572211, -0.0092656985, 0.0114886165, -0.016269399, -0.004123468, 0.12384796, 0.014971045, -0.0016563743, -0.013755894, 0.006771024, 0.009048598, -0.0056833397, 0.0023557737, 0.022616422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, 17, 19, -1, -1, -1, 21, 23, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.613501, 0.81052035, 0.0, 1.3971546, 0.0, 0.8552965, 0.0, 1.4917302, 1.882204, 1.2046932, 1.7306445, 0.0, 1.6658901, 0.80510473, 0.0, 0.0, 0.0, 1.9468911, 2.075561, 2.0249386, 2.7651381, 1.1172327, 0.0, 0.0, 2.0522637, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, 18, 20, -1, -1, -1, 22, 24, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.0077461614, 3.0, 0.007733439, 1.0, -0.010812094, 1.0, -0.42307693, 1.0, 1.0, 0.016295282, 1.0, 1.0, -0.007323613, -0.018222045, -0.0011959377, 1.0, 1.0, 1.0, 1.0, 1.0, -0.016269399, -0.004123468, 1.0, 0.014971045, -0.0016563743, -0.013755894, 0.006771024, 0.009048598, -0.0056833397, 0.0023557737, 0.022616422], "split_indices": [84, 0, 0, 0, 0, 12, 0, 61, 1, 113, 93, 0, 111, 39, 0, 0, 0, 59, 69, 69, 81, 3, 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1935.0, 118.0, 1798.0, 137.0, 1681.0, 117.0, 964.0, 717.0, 717.0, 247.0, 94.0, 623.0, 573.0, 144.0, 101.0, 146.0, 300.0, 323.0, 293.0, 280.0, 207.0, 93.0, 123.0, 200.0, 145.0, 148.0, 105.0, 175.0, 96.0, 111.0, 101.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012901935, -0.010764713, 0.035398368, -0.0020636038, -0.009836371, 0.01615642, -0.021831488, -0.071604915, 0.012911032, -0.01272687, 0.004805831, -0.011699275, -0.0019574938, -0.010516433, 0.025133057, -0.035558328, 0.04799574, -0.11040979, 0.0129114855, -0.029531222, 0.08807077, -0.0059715994, -0.015431422, -0.008677888, 0.0031590306, 0.051531546, 0.02675189, 0.14652507, -0.016848996, 0.014013566, 0.015356791, -0.015350147, 0.010858572], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, 17, 19, 21, -1, 23, 25, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.71641695, 1.2484972, 3.0542517, 1.5516202, 0.0, 0.0, 2.144375, 0.6234431, 1.7692648, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5415878, 3.7471128, 2.507265, 0.4651668, 0.0, 0.9622427, 3.488268, 0.0, 0.0, 0.0, 0.0, 2.8711038, 0.0, 0.008324146, 4.405228, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, 18, 20, 22, -1, 24, 26, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [0.88461536, 0.5769231, 1.0, -1.0, -0.009836371, 0.01615642, 1.0, 1.0, 1.0, -0.01272687, 0.004805831, -0.011699275, -0.0019574938, -0.010516433, 1.0, -0.07692308, 1.0, 1.0, 0.0129114855, 1.0, 1.0, -0.0059715994, -0.015431422, -0.008677888, 0.0031590306, 1.0, 0.02675189, -0.23076923, 1.0, 0.014013566, 0.015356791, -0.015350147, 0.010858572], "split_indices": [1, 1, 108, 0, 0, 0, 23, 39, 104, 0, 0, 0, 0, 0, 5, 1, 17, 39, 0, 97, 62, 0, 0, 0, 0, 39, 0, 1, 124, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1638.0, 423.0, 1490.0, 148.0, 132.0, 291.0, 264.0, 1226.0, 116.0, 175.0, 141.0, 123.0, 115.0, 1111.0, 304.0, 807.0, 209.0, 95.0, 275.0, 532.0, 97.0, 112.0, 142.0, 133.0, 442.0, 90.0, 185.0, 257.0, 97.0, 88.0, 123.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0060845986, -0.010895156, 0.007594583, -0.020442303, 0.039563954, -0.009305013, -0.08104481, 0.021967815, -0.035756513, -0.09696797, 0.0096822465, 0.0033212292, -0.02233651, -0.012573666, 0.0025375013, -0.015631452, -0.004565327, 0.040429205, -0.012352302, -0.011358087, 0.01761747, -0.030531993, 0.009846642, -0.083651416, 0.00652384, 0.007943121, -0.014203133, 0.0008709826, -0.017811181, -0.028372968, 0.011621261, 0.0418613, -0.014484482, -0.0062562837, 0.016331114], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, -1, -1, -1, 19, 21, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [0.8192146, 0.9446932, 0.0, 1.1129885, 4.2326818, 2.3186204, 4.1628428, 0.0, 1.2101376, 0.75524616, 0.7757323, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.360289, 1.3437715, 1.9159458, 0.0, 2.4581413, 0.0, 1.5529583, 0.0, 1.6750001, 0.0, 0.0, 0.0, 2.6095207, 0.0, 2.5237772, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 25, 25, 29, 29, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, -1, -1, -1, 20, 22, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.2692307, 0.007594583, 0.5769231, 1.0, -1.0, 1.0, 0.021967815, 1.0, 1.0, 1.0, 0.0033212292, -0.02233651, -0.012573666, 0.0025375013, -0.015631452, -0.004565327, 1.0, 0.26923078, 1.0, 0.01761747, -0.03846154, 0.009846642, 1.0, 0.00652384, 1.0, -0.014203133, 0.0008709826, -0.017811181, 1.0, 0.011621261, 1.0, -0.014484482, -0.0062562837, 0.016331114], "split_indices": [102, 1, 0, 1, 108, 0, 2, 0, 23, 13, 69, 0, 0, 0, 0, 0, 0, 105, 1, 97, 0, 1, 0, 39, 0, 23, 0, 0, 0, 105, 0, 50, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1961.0, 115.0, 1649.0, 312.0, 1393.0, 256.0, 92.0, 220.0, 248.0, 1145.0, 142.0, 114.0, 89.0, 131.0, 115.0, 133.0, 478.0, 667.0, 346.0, 132.0, 573.0, 94.0, 178.0, 168.0, 426.0, 147.0, 90.0, 88.0, 319.0, 107.0, 199.0, 120.0, 107.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00056747295, 0.006767697, -0.0441627, -0.0049435766, 0.011812636, -0.010731081, 0.0026414562, -0.057813935, 0.010239067, -0.0057532447, -0.017944662, 0.051696073, -0.030574193, -0.013682139, 0.006368165, 0.099446654, -0.02021289, -0.012002869, -0.014638411, 0.011537939, 0.021330431, -0.008808205, 0.010060909, -0.055938378, 0.030838152, 0.007124302, -0.003397492, -0.011421177, 0.0014553606, -0.004359612, 0.010165411], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.57408774, 2.3709483, 1.123119, 1.320461, 0.0, 0.0, 0.0, 2.3239465, 2.1623702, 2.338881, 0.0, 2.1769629, 1.3850784, 0.0, 0.0, 3.8134587, 2.0746217, 1.0446445, 0.0, 0.5842301, 0.0, 0.0, 0.0, 1.1255395, 1.481189, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 0.88461536, 3.5, 1.0, 0.011812636, -0.010731081, 0.0026414562, 1.0, 1.0, -0.46153846, -0.017944662, 1.0, 1.0, -0.013682139, 0.006368165, 1.0, 0.0, 1.0, -0.014638411, 1.0, 0.021330431, -0.008808205, 0.010060909, 1.0, 1.0, 0.007124302, -0.003397492, -0.011421177, 0.0014553606, -0.004359612, 0.010165411], "split_indices": [1, 1, 1, 89, 0, 0, 0, 106, 126, 1, 0, 105, 113, 0, 0, 71, 1, 39, 0, 108, 0, 0, 0, 97, 106, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1818.0, 252.0, 1645.0, 173.0, 133.0, 119.0, 367.0, 1278.0, 257.0, 110.0, 634.0, 644.0, 89.0, 168.0, 381.0, 253.0, 555.0, 89.0, 215.0, 166.0, 162.0, 91.0, 274.0, 281.0, 93.0, 122.0, 150.0, 124.0, 137.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013046082, -0.020413322, 0.018854033, -0.008780562, -0.010113186, -0.0025528483, 0.07565259, 0.007983281, -0.023543019, 0.013922857, -0.032277413, -0.0033843077, 0.018675853, -0.004746302, -0.013063689, -0.110890165, 0.031847786, 0.032783046, -0.04929728, -0.002504065, -0.025228936, 0.014846253, -0.013041556, 0.01977702, -0.023012593, -0.013721875, -0.0068089045, -0.011044494, 0.0063281967, -0.014089897, 0.039205205, 0.0057445867, -0.009176802, 0.0104776975, -0.0029347062], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, 13, -1, 15, -1, -1, 17, -1, 19, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.7923682, 0.73302186, 1.2170961, 0.0, 1.106491, 3.0638618, 3.3333797, 0.0, 1.6043711, 0.0, 3.0296767, 0.0, 0.0, 1.1335952, 0.0, 3.2775433, 1.7327056, 3.387648, 1.1580479, 0.0, 0.0, 0.0, 1.7767668, 0.0, 2.0170238, 0.0, 1.1409371, 0.0, 0.0, 0.0, 0.8091171, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 22, 22, 24, 24, 26, 26, 30, 30], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, 14, -1, 16, -1, -1, 18, -1, 20, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, -0.008780562, -0.34615386, -0.42307693, -0.07692308, 0.007983281, 2.0, 0.013922857, 1.0, -0.0033843077, 0.018675853, 1.0, -0.013063689, 1.0, 0.0, -0.07692308, 0.0, -0.002504065, -0.025228936, 0.014846253, 1.0, 0.01977702, 0.42307693, -0.013721875, 0.61538464, -0.011044494, 0.0063281967, -0.014089897, 1.0, 0.0057445867, -0.009176802, 0.0104776975, -0.0029347062], "split_indices": [39, 1, 0, 0, 1, 1, 1, 0, 0, 0, 111, 0, 0, 122, 0, 121, 0, 1, 1, 0, 0, 0, 81, 0, 1, 0, 1, 0, 0, 0, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1056.0, 1001.0, 140.0, 916.0, 727.0, 274.0, 119.0, 797.0, 126.0, 601.0, 138.0, 136.0, 678.0, 119.0, 270.0, 331.0, 368.0, 310.0, 168.0, 102.0, 92.0, 239.0, 93.0, 275.0, 101.0, 209.0, 105.0, 134.0, 95.0, 180.0, 119.0, 90.0, 92.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0021889822, 0.0056670904, -0.0069112196, 0.021511218, -0.008441943, 0.037838764, -0.0109109115, -0.049957786, 0.028194092, 0.06690691, -0.0013328277, 0.00031682898, -0.11994795, -0.015447964, 0.019441342, 0.11446376, -0.0021865868, 0.0077009434, -0.072436966, 0.006253879, -0.0072354595, -0.01637847, -0.008321982, -0.07613612, 0.029345661, 0.03018716, 0.029505644, -0.024430836, 0.010321179, -0.0057801157, -0.009369905, -0.003918776, 0.014454016, 0.009420757, -0.0021691422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5118578, 0.4399373, 0.0, 1.9770213, 1.583336, 0.938244, 0.0, 1.7171397, 4.011548, 1.9968901, 1.9552311, 1.2841793, 0.32844782, 1.190678, 0.0, 4.6876783, 0.0, 0.0, 5.5547757, 0.0, 0.0, 0.0, 0.0, 0.059894443, 1.9894581, 0.69747084, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0069112196, 1.0, 1.0, 1.0, -0.0109109115, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.019441342, 1.0, -0.0021865868, 0.0077009434, 1.0, 0.006253879, -0.0072354595, -0.01637847, -0.008321982, 1.0, 1.0, 1.0, 0.029505644, -0.024430836, 0.010321179, -0.0057801157, -0.009369905, -0.003918776, 0.014454016, 0.009420757, -0.0021691422], "split_indices": [117, 126, 0, 7, 39, 15, 0, 93, 105, 105, 124, 115, 124, 115, 0, 2, 0, 0, 50, 0, 0, 0, 0, 97, 81, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1968.0, 96.0, 927.0, 1041.0, 824.0, 103.0, 488.0, 553.0, 473.0, 351.0, 284.0, 204.0, 438.0, 115.0, 308.0, 165.0, 167.0, 184.0, 153.0, 131.0, 93.0, 111.0, 186.0, 252.0, 210.0, 98.0, 93.0, 91.0, 91.0, 95.0, 158.0, 94.0, 94.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035692444, 0.0042689387, -0.044187423, 0.031522907, -0.023110911, -0.016252998, 0.006401147, 0.07609371, -0.008203235, -0.08430852, -0.0071656783, -0.003757085, 0.110844664, 0.009197882, -0.034624886, -0.010929362, -0.0059601, 0.0046585808, -0.024156095, 0.0028890027, 0.018774036, -0.011898109, 0.013370874, -0.0007646233, -0.013795951, 0.0074228793, -0.0032733586, -0.03218101, 0.0120664975, -0.0104492465, 0.023072336, -0.0017411929, 0.0068397145], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [0.65935004, 1.2954198, 4.289519, 1.540445, 0.8450514, 0.0, 0.0, 1.6194808, 1.2176085, 0.11049974, 0.6274097, 0.0, 1.9788148, 0.0, 1.4737419, 0.0, 0.0, 0.0, 1.3895793, 0.0, 0.0, 0.0, 0.65095073, 1.6518426, 0.0, 0.0, 0.0, 1.374435, 0.0, 0.0, 0.35781378, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 18, 18, 22, 22, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, -0.016252998, 0.006401147, 0.0, 0.0, 1.0, 0.0, -0.003757085, 1.0, 0.009197882, 1.0, -0.010929362, -0.0059601, 0.0046585808, 1.0, 0.0028890027, 0.018774036, -0.011898109, 1.0, 1.0, -0.013795951, 0.0074228793, -0.0032733586, 1.0, 0.0120664975, -0.0104492465, 1.0, -0.0017411929, 0.0068397145], "split_indices": [64, 108, 108, 13, 5, 0, 0, 0, 0, 59, 0, 0, 71, 0, 81, 0, 0, 0, 58, 0, 0, 0, 109, 42, 0, 0, 0, 137, 0, 0, 50, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1736.0, 335.0, 870.0, 866.0, 160.0, 175.0, 410.0, 460.0, 179.0, 687.0, 96.0, 314.0, 96.0, 364.0, 89.0, 90.0, 165.0, 522.0, 152.0, 162.0, 132.0, 232.0, 433.0, 89.0, 100.0, 132.0, 344.0, 89.0, 149.0, 195.0, 103.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0049629286, 0.0074420655, -0.009276818, -0.008476401, -0.0033651683, -0.009977748, 0.0089812055, -0.025757657, 0.030410908, -0.07181284, 0.009546884, 0.1403126, -0.038137928, -0.026878364, -0.019174138, 0.052601513, -0.030398235, 0.025620285, 0.0031741695, -0.08794739, 0.0077151353, 0.002984762, -0.061243497, 0.011373409, -0.0005671044, 0.008393222, -0.07138463, -0.014753056, -0.00080517476, -0.014149862, 0.00050277665, -0.010207115, -0.002746629], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.71092993, 0.0, 0.8786734, 0.0, 1.1250747, 1.0866466, 0.0, 1.9934235, 3.6086106, 2.8669076, 1.1935567, 2.3151445, 1.6940366, 0.75441635, 0.0, 1.189826, 1.6869576, 0.0, 0.0, 0.98064923, 0.0, 0.0, 1.2817851, 0.0, 0.0, 0.0, 0.3571403, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 22, 22, 26, 26], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0074420655, -0.5, -0.008476401, 1.0, 1.0, 0.0089812055, 0.07692308, 1.0, 1.0, 1.0, 0.0, 0.42307693, 1.0, -0.019174138, 1.0, 1.0, 0.025620285, 0.0031741695, 1.0, 0.0077151353, 0.002984762, 1.0, 0.011373409, -0.0005671044, 0.008393222, 1.0, -0.014753056, -0.00080517476, -0.014149862, 0.00050277665, -0.010207115, -0.002746629], "split_indices": [1, 0, 1, 0, 90, 105, 0, 1, 69, 0, 12, 1, 1, 59, 0, 122, 124, 0, 0, 122, 0, 0, 124, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 107.0, 1969.0, 143.0, 1826.0, 1705.0, 121.0, 1226.0, 479.0, 532.0, 694.0, 184.0, 295.0, 387.0, 145.0, 334.0, 360.0, 89.0, 95.0, 206.0, 89.0, 146.0, 241.0, 163.0, 171.0, 95.0, 265.0, 118.0, 88.0, 109.0, 132.0, 156.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004661555, -0.0057055755, 0.03522672, 0.024061136, -0.027585957, -0.009216748, 0.07122263, 0.012859925, -0.0063687633, -0.08359189, 0.0010371717, 0.13797516, 0.009822891, 0.01560808, -0.008006699, -0.0232588, -0.021581119, 0.012557076, -0.021218825, 0.0012315593, 0.02525471, -0.014382106, 0.011886056, -0.025013851, 0.010777551, -0.011737748, 0.0065532387, 0.013722464, -0.00880364, -0.0077271904, 0.0021397125, -0.0063336915, 0.048656065, -0.0020288762, 0.01246887], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.6530755, 1.0023615, 2.3937201, 2.0740676, 1.4219189, 0.0, 1.6681247, 0.0, 0.8179257, 2.3931592, 1.626941, 2.8074257, 3.5516315, 1.4564236, 0.0, 1.7215238, 0.0, 0.0, 1.1626769, 0.0, 0.0, 0.0, 0.0, 0.654844, 0.0, 0.0, 0.0, 0.8802714, 0.0, 0.0, 0.0, 0.0, 1.1794631, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 23, 23, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, -0.07692308, 1.0, 1.0, 0.34615386, -0.009216748, 1.0, 0.012859925, 1.0, 1.0, 0.5, 1.0, 1.0, 1.0, -0.008006699, 1.0, -0.021581119, 0.012557076, 1.0, 0.0012315593, 0.02525471, -0.014382106, 0.011886056, 1.0, 0.010777551, -0.011737748, 0.0065532387, 1.0, -0.00880364, -0.0077271904, 0.0021397125, -0.0063336915, 1.0, -0.0020288762, 0.01246887], "split_indices": [0, 1, 5, 81, 1, 0, 124, 0, 42, 121, 1, 106, 13, 58, 0, 12, 0, 0, 127, 0, 0, 0, 0, 124, 0, 0, 0, 81, 0, 0, 0, 0, 39, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1539.0, 522.0, 652.0, 887.0, 115.0, 407.0, 147.0, 505.0, 300.0, 587.0, 195.0, 212.0, 389.0, 116.0, 206.0, 94.0, 89.0, 498.0, 93.0, 102.0, 88.0, 124.0, 270.0, 119.0, 100.0, 106.0, 327.0, 171.0, 127.0, 143.0, 102.0, 225.0, 118.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014232373, -0.0071375966, 0.010013648, 0.00078485865, -0.0774394, -0.007967125, 0.015917676, -0.000692238, -0.013620356, -0.019785691, 0.040207937, -0.037618198, 0.036656134, 0.014366175, -0.006710128, 0.0013501603, -0.08253089, 0.011300641, -0.0009863172, -0.023588648, 0.008077069, -0.14940773, -0.035038635, 0.005986366, -0.0065932, 0.022230713, -0.06558974, -0.004585406, -0.024483949, -0.014807154, 0.011408879, 0.009453343, -0.0057763746, -0.0024464955, -0.009668407], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, 23, 25, -1, 27, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.19842, 1.0888628, 0.0, 2.4356296, 0.8204864, 0.9479845, 0.0, 0.0, 0.0, 1.3456895, 3.641308, 1.7781769, 0.9225582, 0.0, 0.0, 1.0774757, 1.4991331, 0.0, 0.84966785, 0.7967279, 0.0, 1.9369321, 4.6523414, 0.0, 0.0, 1.1451958, 0.27620912, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, 24, 26, -1, 28, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.010013648, 1.0, 1.0, 1.0, 0.015917676, -0.000692238, -0.013620356, 1.0, 1.0, 1.0, 1.0, 0.014366175, -0.006710128, 1.0, 1.0, 0.011300641, 1.0, 1.0, 0.008077069, -0.23076923, 1.0, 0.005986366, -0.0065932, 0.8076923, 1.0, -0.004585406, -0.024483949, -0.014807154, 0.011408879, 0.009453343, -0.0057763746, -0.0024464955, -0.009668407], "split_indices": [102, 119, 0, 125, 17, 105, 0, 0, 0, 74, 109, 2, 122, 0, 0, 113, 69, 0, 69, 106, 0, 1, 59, 0, 0, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1955.0, 110.0, 1757.0, 198.0, 1665.0, 92.0, 90.0, 108.0, 1337.0, 328.0, 1016.0, 321.0, 167.0, 161.0, 544.0, 472.0, 106.0, 215.0, 414.0, 130.0, 196.0, 276.0, 111.0, 104.0, 198.0, 216.0, 94.0, 102.0, 157.0, 119.0, 104.0, 94.0, 93.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0041726255, 0.00089943677, 0.0061131925, 0.0078242365, -0.062105462, 0.0014231418, 0.0119742295, -0.0037365926, -0.008327677, -0.011241101, 0.052118264, 0.014685734, -0.04274221, 0.013561082, -0.0039829216, 0.043236583, -0.01726797, -0.07008579, 0.002055588, 0.012618946, 0.013338365, 0.00897449, -0.008605087, -0.021674732, -0.014605259, 0.006691929, -0.0035536108, 0.006272574, -0.0030219106, -0.07324263, 0.0080427695, -0.014213348, -0.0007222195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.38424966, 0.85034144, 0.0, 1.2579949, 0.101086855, 1.0663877, 0.0, 0.0, 0.0, 1.0854263, 2.5487413, 0.6650708, 1.038478, 0.0, 0.0, 0.9548547, 0.6209314, 3.4093435, 0.0, 0.0, 0.7411034, 0.5245696, 0.0, 0.0, 1.6940345, 0.0, 0.0, 0.0, 0.0, 0.855062, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 24, 24, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0061131925, 1.0, 1.0, 1.0, 0.0119742295, -0.0037365926, -0.008327677, 1.0, 1.0, 1.0, 1.0, 0.013561082, -0.0039829216, 1.0, 1.0, 1.0, 0.002055588, 0.012618946, 1.0, 1.0, -0.008605087, -0.021674732, 1.0, 0.006691929, -0.0035536108, 0.006272574, -0.0030219106, 1.0, 0.0080427695, -0.014213348, -0.0007222195], "split_indices": [102, 119, 0, 125, 17, 105, 0, 0, 0, 127, 109, 80, 109, 0, 0, 59, 58, 59, 0, 0, 108, 97, 0, 0, 97, 0, 0, 0, 0, 71, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1949.0, 112.0, 1756.0, 193.0, 1661.0, 95.0, 89.0, 104.0, 1329.0, 332.0, 729.0, 600.0, 174.0, 158.0, 385.0, 344.0, 419.0, 181.0, 102.0, 283.0, 249.0, 95.0, 115.0, 304.0, 135.0, 148.0, 105.0, 144.0, 188.0, 116.0, 92.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021166, -0.005420986, 0.0066231983, -0.01262406, 0.03378262, -0.036128268, 0.011745329, 0.010080124, -0.004214054, -0.09261325, 0.014404254, 0.032612097, -0.049149502, -0.13116813, 0.00014473435, -0.043732386, 0.015572104, 0.009585919, 0.014269913, -0.010474344, 0.0023740288, -0.0059671425, -0.022689097, -0.017266436, 0.034150355, 0.043808352, -0.0062238923, 0.010789397, -0.003453239, -0.0034733997, 0.10961412, 0.013572, 0.00848822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, 19, 21, -1, 23, -1, 25, -1, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.465251, 0.5548894, 0.0, 0.9508202, 1.551921, 2.411908, 1.0356028, 0.0, 0.0, 1.4469724, 3.664195, 1.5386741, 0.84286416, 1.9368134, 0.0, 3.1731381, 0.0, 1.2339264, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9977882, 1.7573038, 0.0, 0.0, 0.0, 0.0, 0.11944413, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 24, 24, 25, 25, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, 20, 22, -1, 24, -1, 26, -1, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.2692307, 0.0066231983, 1.0, 1.0, 1.0, 1.0, 0.010080124, -0.004214054, 1.0, 1.0, 1.0, 1.0, 1.0, 0.00014473435, 1.0, 0.015572104, 0.115384616, 0.014269913, -0.010474344, 0.0023740288, -0.0059671425, -0.022689097, -0.017266436, -0.26923078, -0.34615386, -0.0062238923, 0.010789397, -0.003453239, -0.0034733997, 1.0, 0.013572, 0.00848822], "split_indices": [66, 1, 0, 124, 115, 15, 80, 0, 0, 23, 62, 58, 122, 111, 0, 53, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 50, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1965.0, 95.0, 1660.0, 305.0, 845.0, 815.0, 162.0, 143.0, 399.0, 446.0, 607.0, 208.0, 283.0, 116.0, 316.0, 130.0, 502.0, 105.0, 118.0, 90.0, 162.0, 121.0, 119.0, 197.0, 340.0, 162.0, 95.0, 102.0, 155.0, 185.0, 90.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003124353, -0.02109704, 0.012167962, -0.0048407633, -0.010588976, 0.02122863, -0.011050735, 0.008513159, -0.020375565, 0.011875535, 0.0047692205, -0.06320163, 0.017514605, 0.02385127, -0.009325343, 0.006873394, -0.017548725, -0.008057165, 0.061967503, 0.0013513224, 0.10574622, 0.0033483466, 0.009773076, 0.028811695, -0.011991366, 0.0021846544, 0.019523917, 0.009687153, 0.002239209, 0.047218665, -0.0055406857, 0.013309023, -0.0013088064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1], "loss_changes": [0.5372177, 1.0710272, 1.4383072, 0.9113019, 0.0, 1.9343045, 0.0, 0.0, 0.9022141, 0.0, 1.9284574, 3.8665762, 1.2862643, 1.5901911, 0.0, 0.0, 0.0, 0.0, 0.206792, 2.2543974, 1.3965673, 0.0, 0.0, 0.99830216, 0.0, 0.0, 0.0, 0.0, 1.0293767, 1.1548357, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 18, 18, 19, 19, 20, 20, 23, 23, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1], "split_conditions": [-0.1923077, 3.0, 1.0, -0.5769231, -0.010588976, -0.03846154, -0.011050735, 0.008513159, 1.0, 0.011875535, 1.0, 1.0, 0.0, 1.0, -0.009325343, 0.006873394, -0.017548725, -0.008057165, -0.34615386, 1.0, 1.0, 0.0033483466, 0.009773076, 0.0, -0.011991366, 0.0021846544, 0.019523917, 0.009687153, 1.0, 1.0, -0.0055406857, 0.013309023, -0.0013088064], "split_indices": [1, 0, 41, 1, 0, 1, 0, 0, 69, 0, 64, 109, 0, 0, 0, 0, 0, 0, 1, 15, 122, 0, 0, 0, 0, 0, 0, 0, 13, 137, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 777.0, 1294.0, 652.0, 125.0, 1205.0, 89.0, 96.0, 556.0, 174.0, 1031.0, 261.0, 295.0, 863.0, 168.0, 120.0, 141.0, 92.0, 203.0, 677.0, 186.0, 113.0, 90.0, 552.0, 125.0, 96.0, 90.0, 155.0, 397.0, 223.0, 174.0, 92.0, 131.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002031382, -0.0019593525, 0.0071512945, -0.059213836, 0.008946261, 0.0011992841, -0.011869237, 0.019248737, -0.059608515, 0.007557372, 0.011781234, -0.018323142, 0.0033615285, -0.02262984, 0.040993214, -0.08480214, 0.029465076, -0.0052405256, 0.07580537, -0.016779302, -0.027169567, -0.0065027513, 0.08784051, -0.0014620809, 0.11372604, 0.005626664, -0.011060581, 8.806047e-06, 0.021705836, 0.07498572, 0.02069582, 0.013456294, -0.0013703045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.57175666, 1.2175709, 0.0, 1.3214042, 1.156893, 0.0, 0.0, 1.6409361, 2.466264, 1.2848833, 0.0, 0.0, 0.0, 2.166798, 1.9638473, 1.4588077, 2.0078409, 0.0, 1.5087702, 0.0, 1.2530888, 0.0, 2.551316, 0.0, 1.1196699, 0.0, 0.0, 0.0, 0.0, 1.1571591, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.0071512945, 1.0, 1.3461539, 0.0011992841, -0.011869237, 0.88461536, 1.0, 1.0, 0.011781234, -0.018323142, 0.0033615285, 1.0, 1.0, 1.0, 1.0, -0.0052405256, 1.0, -0.016779302, 1.0, -0.0065027513, 1.0, -0.0014620809, 1.0, 0.005626664, -0.011060581, 8.806047e-06, 0.021705836, -0.03846154, 0.02069582, 0.013456294, -0.0013703045], "split_indices": [102, 0, 0, 2, 1, 0, 0, 1, 23, 124, 0, 0, 0, 15, 17, 69, 59, 0, 5, 0, 93, 0, 71, 0, 61, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1950.0, 112.0, 312.0, 1638.0, 142.0, 170.0, 1424.0, 214.0, 1273.0, 151.0, 92.0, 122.0, 669.0, 604.0, 305.0, 364.0, 164.0, 440.0, 125.0, 180.0, 139.0, 225.0, 130.0, 310.0, 90.0, 90.0, 134.0, 91.0, 219.0, 91.0, 131.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00061764615, 0.0051045464, -0.008949038, 0.014988319, -0.06490208, 0.008445463, 0.012726093, -0.0124620795, -0.0005676881, 0.01865609, -0.009041502, -0.0070742886, 0.03868948, -0.051881324, 0.042449266, 0.006109201, 0.088512234, -0.00015751387, -0.088703364, -0.0028032518, 0.012392824, -0.0639595, 0.046945427, -0.0011268104, 0.023963924, -0.012683216, -0.0048969104, -0.01870962, 0.0052627353, 0.011497623, -0.0018534207, 0.008452758, -0.010540175], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.82680494, 1.3478787, 0.0, 1.2539335, 0.8523812, 1.6282066, 0.0, 0.0, 0.0, 0.75361234, 0.0, 1.4201624, 1.3343022, 0.622398, 1.7458065, 1.4220866, 4.4027367, 0.0, 0.29391336, 0.0, 0.0, 2.6271706, 1.3987548, 1.822048, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, -0.008949038, 1.1538461, 3.1923077, 1.0, 0.012726093, -0.0124620795, -0.0005676881, 1.0, -0.009041502, 1.0, -0.115384616, -0.07692308, 1.0, 1.0, 1.0, -0.00015751387, 1.0, -0.0028032518, 0.012392824, 1.0, 1.0, 0.30769232, 0.023963924, -0.012683216, -0.0048969104, -0.01870962, 0.0052627353, 0.011497623, -0.0018534207, 0.008452758, -0.010540175], "split_indices": [14, 1, 0, 1, 1, 40, 0, 0, 0, 71, 0, 115, 1, 1, 12, 109, 108, 0, 122, 0, 0, 111, 105, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1948.0, 97.0, 1707.0, 241.0, 1613.0, 94.0, 120.0, 121.0, 1462.0, 151.0, 640.0, 822.0, 336.0, 304.0, 497.0, 325.0, 142.0, 194.0, 163.0, 141.0, 183.0, 314.0, 204.0, 121.0, 99.0, 95.0, 89.0, 94.0, 154.0, 160.0, 112.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019378312, -0.007646528, 0.05487728, -0.008796183, -0.002464894, 0.013974796, -0.0036521887, 0.006211919, -0.0553621, -0.0008747867, 0.010670143, -0.0008819323, -0.0118150925, 0.008374007, -0.0071568014, -0.030142121, 0.015210075, 0.00833183, -0.049052197, -0.0125088915, 0.03673872, -0.11790559, -0.00077084376, 0.015675828, 0.007056457, -0.017896147, -0.0032681723, -0.06626356, 0.017242095, -0.02095904, 0.012064106, 0.0011141738, -0.013056952, 0.009287796, -0.007363656], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, 11, 13, -1, -1, -1, -1, 15, 17, 19, -1, 21, -1, 23, 25, 27, -1, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.6713842, 0.78280514, 1.4660943, 0.0, 0.8110164, 0.0, 0.0, 1.0810283, 0.7276687, 0.75374025, 0.0, 0.0, 0.0, 0.0, 0.678625, 1.3967496, 2.020679, 0.0, 1.8549784, 0.0, 2.0662217, 1.1967857, 3.7204382, 0.0, 1.4796903, 0.0, 0.0, 1.1846744, 0.0, 2.2367508, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 8, 8, 9, 9, 14, 14, 15, 15, 16, 16, 18, 18, 20, 20, 21, 21, 22, 22, 24, 24, 27, 27, 29, 29], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, 12, 14, -1, -1, -1, -1, 16, 18, 20, -1, 22, -1, 24, 26, 28, -1, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 1.0, -0.008796183, 1.3461539, 0.013974796, -0.0036521887, 1.1538461, 1.0, -0.46153846, 0.010670143, -0.0008819323, -0.0118150925, 0.008374007, 1.0, -0.34615386, -0.34615386, 0.00833183, 1.0, -0.0125088915, 1.0, 0.1923077, 1.0, 0.015675828, 1.0, -0.017896147, -0.0032681723, 1.0, 0.017242095, 1.0, 0.012064106, 0.0011141738, -0.013056952, 0.009287796, -0.007363656], "split_indices": [125, 1, 15, 0, 1, 0, 0, 1, 17, 1, 0, 0, 0, 0, 124, 1, 1, 0, 59, 0, 89, 1, 105, 0, 116, 0, 0, 127, 0, 81, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1881.0, 189.0, 114.0, 1767.0, 98.0, 91.0, 1518.0, 249.0, 1418.0, 100.0, 143.0, 106.0, 98.0, 1320.0, 651.0, 669.0, 93.0, 558.0, 89.0, 580.0, 230.0, 328.0, 115.0, 465.0, 134.0, 96.0, 238.0, 90.0, 373.0, 92.0, 108.0, 130.0, 118.0, 255.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039833547, 0.0002206665, -0.008253853, -0.006321342, 0.006238411, 0.028293844, -0.015378347, 0.007854589, 0.020491563, -0.03214636, 0.0068350653, 0.012310489, -0.023259303, -0.011546036, -0.014789271, -0.0004811347, -0.014027654, -0.06023473, 0.032742195, -0.06701259, 0.055311903, -0.01040964, -0.0007600702, -0.0020245397, 0.010036072, -0.001981432, -0.013610694, -0.003714726, 0.015380104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, -1, 13, -1, -1, 15, -1, 17, 19, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6826221, 0.7489545, 0.0, 0.0, 0.8543665, 3.2020855, 1.2705917, 2.8507793, 0.0, 1.0903504, 0.0, 0.0, 1.668564, 0.0, 1.3478959, 1.9450841, 0.0, 0.7364483, 1.0927978, 0.7794105, 2.595274, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 12, 12, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, -1, 14, -1, -1, 16, -1, 18, 20, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, -0.008253853, -0.006321342, 1.0, 1.1538461, 1.0, -0.34615386, 0.020491563, -0.34615386, 0.0068350653, 0.012310489, 0.5769231, -0.011546036, 1.0, 1.0, -0.014027654, 1.0, 1.0, 1.0, 0.03846154, -0.01040964, -0.0007600702, -0.0020245397, 0.010036072, -0.001981432, -0.013610694, -0.003714726, 0.015380104], "split_indices": [43, 1, 0, 0, 108, 1, 64, 1, 0, 1, 0, 0, 1, 0, 13, 59, 0, 122, 53, 106, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1962.0, 105.0, 170.0, 1792.0, 887.0, 905.0, 795.0, 92.0, 754.0, 151.0, 169.0, 626.0, 130.0, 624.0, 524.0, 102.0, 319.0, 305.0, 239.0, 285.0, 174.0, 145.0, 171.0, 134.0, 142.0, 97.0, 147.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0013484888, -0.0021263864, 0.005894899, -0.024133217, 0.02353606, -0.0027744519, -0.016228352, 0.002257371, 0.017381677, 0.01585736, -0.008775371, 0.02629649, -0.028344143, -0.005167656, 0.010797418, -0.01942451, 0.086619504, 0.004693776, -0.07663821, 0.055039555, -0.074066766, -0.008469264, 0.0024231642, 0.0021750228, 0.015081301, -0.0020507916, -0.014985161, 0.021611009, -0.01348605, -0.015554589, 0.0018439591, -0.006781734, 0.002462692], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.4151207, 1.1046493, 0.0, 3.1071074, 2.8875918, 1.4439858, 0.0, 0.58188605, 0.0, 1.448695, 0.0, 1.2218069, 1.2652129, 2.5262678, 0.0, 0.7180378, 0.79535925, 0.0, 0.8712113, 3.587174, 2.1406043, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.47212592, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.005894899, 3.0, 3.0, 1.0, -0.016228352, 1.0, 0.017381677, 1.0, -0.008775371, 1.0, 1.0, 1.0, 0.010797418, 1.0, 1.0, 0.004693776, 1.0, 0.0, 1.0, -0.008469264, 0.0024231642, 0.0021750228, 0.015081301, -0.0020507916, -0.014985161, 0.021611009, 1.0, -0.015554589, 0.0018439591, -0.006781734, 0.002462692], "split_indices": [84, 122, 0, 0, 0, 80, 0, 108, 0, 0, 0, 93, 17, 121, 0, 83, 71, 0, 50, 0, 15, 0, 0, 0, 0, 0, 0, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1956.0, 118.0, 1053.0, 903.0, 912.0, 141.0, 791.0, 112.0, 748.0, 164.0, 443.0, 348.0, 609.0, 139.0, 252.0, 191.0, 136.0, 212.0, 325.0, 284.0, 101.0, 151.0, 95.0, 96.0, 120.0, 92.0, 97.0, 228.0, 151.0, 133.0, 94.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0015797899, -0.002997093, 0.0467256, -0.007556092, 0.0017638808, 0.010969796, -0.0022474763, 0.010751662, -0.004241204, -0.00886219, 0.006407089, -0.045236506, 0.0036892882, 0.0033681907, -0.10017972, -0.008974105, 0.06786369, -0.023710947, 0.0037910428, 0.015453488, -0.041544236, 0.01535132, -0.0012594911, -0.03222634, 0.06347758, -0.007991814, -0.014652009, 0.0015755165, -0.011296447, -0.0019304203, 0.015181936, 0.021554584, -0.0077771633, 0.009124552, -0.0056101005], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, 9, 11, -1, 13, 15, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.42875135, 0.65087414, 0.83232296, 0.0, 1.122776, 0.0, 0.0, 0.0, 0.52811444, 0.7154162, 0.0, 1.7430847, 0.94675565, 0.0, 4.4813504, 0.77412856, 1.3231184, 0.0, 0.0, 1.2731181, 1.4687548, 0.0, 0.0, 1.0808289, 2.0257268, 0.65151083, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2014399, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 24, 24, 25, 25, 31, 31], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, 10, 12, -1, 14, 16, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 1.0, -0.007556092, -0.46153846, 0.010969796, -0.0022474763, 0.010751662, 1.0, -0.1923077, 0.006407089, 1.0, 1.0, 0.0033681907, 1.0, 1.0, 0.42307693, -0.023710947, 0.0037910428, 1.0, 1.0, 0.01535132, -0.0012594911, 1.0, 1.0, 1.3461539, -0.014652009, 0.0015755165, -0.011296447, -0.0019304203, 0.015181936, 1.0, -0.0077771633, 0.009124552, -0.0056101005], "split_indices": [125, 1, 15, 0, 1, 0, 0, 0, 90, 1, 0, 59, 62, 0, 69, 106, 1, 0, 0, 50, 15, 0, 0, 116, 109, 1, 0, 0, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1884.0, 191.0, 116.0, 1768.0, 100.0, 91.0, 95.0, 1673.0, 1567.0, 106.0, 402.0, 1165.0, 165.0, 237.0, 973.0, 192.0, 119.0, 118.0, 556.0, 417.0, 93.0, 99.0, 279.0, 277.0, 316.0, 101.0, 175.0, 104.0, 143.0, 134.0, 222.0, 94.0, 117.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0029796406, -0.00035365747, 0.0060796165, -0.04220534, 0.007721194, 0.0028231451, -0.0100834835, 0.015672596, -0.045374837, -0.029413097, 0.041464195, 0.0029097127, -0.011846771, 0.008567296, -0.055470325, -0.04943959, 0.070490085, -0.0045179767, -0.12322013, -0.009405092, 0.0016224243, 0.03699525, 0.017771436, -0.007257362, 0.00706391, -0.016275195, -0.008099294, 0.10484739, -0.05418104, 0.03384137, 0.02661545, -0.01440183, 0.007957662, -0.0030778581, 0.011602113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [0.39950818, 0.66237456, 0.0, 1.3091068, 0.6936546, 0.0, 0.0, 1.661687, 1.1648815, 1.5593882, 2.398454, 0.0, 0.0, 0.0, 1.4636526, 0.64445746, 2.474515, 1.237797, 0.30381513, 0.0, 0.0, 3.2479157, 0.0, 0.0, 0.0, 0.0, 0.0, 3.4475853, 2.6916783, 1.1098847, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.0060796165, 1.0, 1.3461539, 0.0028231451, -0.0100834835, 1.0, 1.0, 1.0, 1.0, 0.0029097127, -0.011846771, 0.008567296, 1.0, 1.0, 1.0, 1.0, 0.34615386, -0.009405092, 0.0016224243, 0.07692308, 0.017771436, -0.007257362, 0.00706391, -0.016275195, -0.008099294, -0.1923077, 1.0, 1.0, 0.02661545, -0.01440183, 0.007957662, -0.0030778581, 0.011602113], "split_indices": [102, 0, 0, 127, 1, 0, 0, 17, 115, 89, 5, 0, 0, 0, 12, 113, 61, 122, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 83, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1960.0, 113.0, 317.0, 1643.0, 144.0, 173.0, 1429.0, 214.0, 520.0, 909.0, 106.0, 108.0, 96.0, 424.0, 220.0, 689.0, 242.0, 182.0, 131.0, 89.0, 525.0, 164.0, 127.0, 115.0, 94.0, 88.0, 301.0, 224.0, 209.0, 92.0, 134.0, 90.0, 117.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0068288376, 0.0035409862, 0.0063566067, 0.011783595, -0.06858183, 0.0038892499, 0.015570355, -0.0012073991, -0.014050086, -0.00687743, 0.046923548, -0.016039694, 0.008527965, 0.012798759, -0.0038143646, -0.03788228, 0.027375137, -0.059443157, 0.036210895, -0.0044416343, 0.06421197, -0.09839386, -0.02407687, 0.004153061, 0.0030588885, -0.0005215299, 0.014798375, -0.018903159, -0.0012526506, -0.061321285, 0.0037391528, -0.011737592, -0.001002598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.3848392, 1.1592362, 0.0, 1.988269, 0.8127975, 0.76867545, 0.0, 0.0, 0.0, 1.1204755, 2.2894363, 1.1445886, 0.0, 0.0, 0.0, 1.2828038, 1.0684067, 0.8568306, 0.0054130405, 0.0, 1.5528848, 2.303715, 0.7463296, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.58369344, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0063566067, 1.0, 1.0, 1.0, 0.015570355, -0.0012073991, -0.014050086, 1.0, 1.0, 0.65384614, 0.008527965, 0.012798759, -0.0038143646, 1.0, 1.0, 1.0, 0.0, -0.0044416343, 1.0, 1.0, 1.0, 0.004153061, 0.0030588885, -0.0005215299, 0.014798375, -0.018903159, -0.0012526506, -0.1923077, 0.0037391528, -0.011737592, -0.001002598], "split_indices": [102, 119, 0, 125, 97, 105, 0, 0, 0, 41, 109, 1, 0, 0, 0, 74, 81, 122, 1, 0, 53, 109, 80, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1950.0, 113.0, 1750.0, 200.0, 1659.0, 91.0, 112.0, 88.0, 1327.0, 332.0, 1207.0, 120.0, 170.0, 162.0, 803.0, 404.0, 622.0, 181.0, 137.0, 267.0, 296.0, 326.0, 93.0, 88.0, 146.0, 121.0, 144.0, 152.0, 203.0, 123.0, 97.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0038551546, 0.005927939, 0.0008232545, -0.0065193125, 0.0060302843, 0.025831582, -0.010875729, 0.017516388, 0.0048189755, -0.08813231, 0.023969965, 0.0112803755, -0.017418412, -0.0013355055, -0.01805042, 0.111645624, -0.013287552, -0.036494765, 0.008046828, -0.0013016363, 0.026233599, 0.04358378, -0.06991893, 0.009632947, -0.109315686, -0.00086215575, 0.011099459, -0.0028003126, -0.0138381375, -0.061478347, 0.010234103, -0.0143525945, -0.006780207, -0.00088684587, -0.01140882], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, -1, 3, -1, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, -1, 19, 21, 23, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.3466679, 0.0, 0.67237335, 0.0, 0.60692173, 2.6201134, 2.6328342, 0.0, 1.7577516, 2.099823, 2.2016733, 0.0, 1.1334636, 0.0, 0.0, 3.7758584, 1.5233921, 1.7064042, 0.0, 0.0, 0.0, 0.83053243, 0.68010867, 2.050296, 0.27977705, 0.0, 0.0, 0.0, 0.0, 0.48713213, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 23, 23, 24, 24, 29, 29], "right_children": [2, -1, 4, -1, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, -1, 20, 22, 24, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.005927939, -0.5, -0.0065193125, 1.0, 1.0, -0.15384616, 0.017516388, -0.34615386, 1.0, 0.1923077, 0.0112803755, 1.0, -0.0013355055, -0.01805042, 1.0, 1.0, 1.0, 0.008046828, -0.0013016363, 0.026233599, 1.0, 1.0, 0.26923078, 0.53846157, -0.00086215575, 0.011099459, -0.0028003126, -0.0138381375, -0.03846154, 0.010234103, -0.0143525945, -0.006780207, -0.00088684587, -0.01140882], "split_indices": [1, 0, 1, 0, 59, 81, 1, 0, 1, 93, 1, 0, 119, 0, 0, 71, 12, 50, 0, 0, 0, 74, 2, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 107.0, 1956.0, 143.0, 1813.0, 835.0, 978.0, 103.0, 732.0, 304.0, 674.0, 125.0, 607.0, 168.0, 136.0, 201.0, 473.0, 508.0, 99.0, 110.0, 91.0, 236.0, 237.0, 311.0, 197.0, 133.0, 103.0, 147.0, 90.0, 176.0, 135.0, 108.0, 89.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-1.8301144E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}