{"learner": {"attributes": {"best_iteration": "8", "best_score": "1.143473"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "59"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.00018214567, -0.23464713, 0.16190238, -0.26368085, -0.006258666, 0.11478127, 0.04830463, -0.23445839, -0.044573665, 0.04576091, 0.34843212, -0.19592895, -0.034319174, -0.14104305, 0.11569584, 0.009461879, 0.05217253, -0.1619728, -0.029106224, -0.023641845, 0.0004699112, 0.24657157, -0.040495254, -0.007996126, -0.22863458, 0.040630993, 0.12698138, -0.022929803, 0.04883077, -0.01809164, -0.027290916, 0.00062721345, 0.023427844, 0.0034232426, 0.0064402306], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, -1, -1, -1, 25, 27, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [78.68761, 4.221245, 18.567772, 3.8464432, 0.0, 17.255531, 0.0, 2.6100159, 0.0, 10.790966, 10.732124, 1.4859638, 0.0, 3.1275496, 12.285418, 0.0, 0.0, 1.853323, 0.0, 0.0, 0.0, 6.246725, 4.6210113, 0.0, 0.3950739, 0.0, 2.4219763, 0.0, 0.042280942, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 21, 21, 22, 22, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, -1, -1, -1, 26, 28, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.006258666, 2.0, 0.04830463, 1.0, -0.044573665, 1.0, 1.0, 1.0, -0.034319174, 1.0, 1.0, 0.009461879, 0.05217253, 1.0, -0.029106224, -0.023641845, 0.0004699112, 1.0, 1.0, -0.007996126, 1.0, 0.040630993, 1.0, -0.022929803, 1.0, -0.01809164, -0.027290916, 0.00062721345, 0.023427844, 0.0034232426, 0.0064402306], "split_indices": [127, 71, 125, 40, 0, 0, 0, 116, 0, 17, 15, 15, 0, 124, 106, 0, 0, 12, 0, 0, 0, 126, 81, 0, 80, 0, 15, 0, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 845.0, 1227.0, 723.0, 122.0, 1070.0, 157.0, 623.0, 100.0, 826.0, 244.0, 460.0, 163.0, 225.0, 601.0, 99.0, 145.0, 339.0, 121.0, 136.0, 89.0, 327.0, 274.0, 152.0, 187.0, 140.0, 187.0, 88.0, 186.0, 90.0, 97.0, 88.0, 99.0, 96.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005018739, -0.21736899, 0.14882235, -0.2551988, 0.0007979368, -0.024005838, 0.21645077, -0.29421726, -0.0042238333, -0.09927684, 0.011728151, 0.13191827, 0.44395798, -0.19605899, -0.34155247, -0.02053592, 0.0067594247, -0.010276778, 0.18305099, 0.020585781, 0.06281815, -0.009484352, -0.028338213, -0.043432955, -0.30606928, 0.0017745927, 0.27445084, -0.023248393, -0.03781216, 0.009123077, -0.008379214, 0.33919802, 0.010090167, 0.018642804, 0.049918544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [65.71922, 6.8795853, 14.586746, 5.741783, 0.0, 3.7328286, 17.250881, 2.7134628, 0.0, 4.0537767, 0.0, 7.8480883, 10.658859, 1.6793056, 1.2970543, 0.0, 0.0, 0.0, 8.897358, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5110664, 1.3778052, 4.0115433, 0.0, 0.0, 0.0, 0.0, 6.3547306, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 24, 24, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 2.9230769, 1.0, 1.0, 0.0007979368, 1.0, 1.0, 0.0, -0.0042238333, 1.0, 0.011728151, -1.0, 1.0, 1.0, 0.34615386, -0.02053592, 0.0067594247, -0.010276778, 1.0, 0.020585781, 0.06281815, -0.009484352, -0.028338213, -0.043432955, 1.0, 1.0, 1.0, -0.023248393, -0.03781216, 0.009123077, -0.008379214, -0.1923077, 0.010090167, 0.018642804, 0.049918544], "split_indices": [2, 1, 17, 71, 0, 116, 113, 1, 0, 119, 0, 0, 109, 15, 1, 0, 0, 0, 81, 0, 0, 0, 0, 0, 23, 15, 124, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 807.0, 1248.0, 691.0, 116.0, 351.0, 897.0, 584.0, 107.0, 229.0, 122.0, 654.0, 243.0, 190.0, 394.0, 140.0, 89.0, 117.0, 537.0, 106.0, 137.0, 88.0, 102.0, 109.0, 285.0, 180.0, 357.0, 141.0, 144.0, 88.0, 92.0, 260.0, 97.0, 133.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0065296334, 0.120459, -0.20974328, 0.080644, 0.03925689, -0.25388888, -0.09520335, -0.097926416, 0.14526117, -0.2969495, -0.0155218495, -0.02133097, 0.0034045072, -0.0013634103, -0.016957488, 0.2614588, 0.051171914, -0.037365332, -0.27009022, 0.34979278, 0.0050438778, 0.12466415, -0.014322688, -0.03314602, -0.021610016, 0.04887929, 0.013017254, -0.0032814331, 0.028214265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [53.54708, 13.835087, 4.035038, 12.854128, 0.0, 2.4473076, 3.388843, 1.7876656, 8.943151, 0.82614136, 0.0, 0.0, 0.0, 0.0, 0.0, 6.8223248, 6.457634, 0.0, 0.9840698, 7.876026, 0.0, 8.134229, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.03925689, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0155218495, -0.02133097, 0.0034045072, -0.0013634103, -0.016957488, 1.0, 1.0, -0.037365332, 1.0, 1.0, 0.0050438778, 1.0, -0.014322688, -0.03314602, -0.021610016, 0.04887929, 0.013017254, -0.0032814331, 0.028214265], "split_indices": [137, 125, 93, 17, 0, 50, 13, 53, 53, 26, 0, 0, 0, 0, 0, 106, 7, 0, 69, 97, 0, 15, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1277.0, 798.0, 1114.0, 163.0, 576.0, 222.0, 296.0, 818.0, 401.0, 175.0, 116.0, 106.0, 136.0, 160.0, 366.0, 452.0, 104.0, 297.0, 258.0, 108.0, 328.0, 124.0, 139.0, 158.0, 158.0, 100.0, 164.0, 164.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011379147, -0.180966, 0.12183276, -0.21320961, 0.0023526351, 0.18637823, -0.04129798, -0.12919764, -0.25680405, 0.3247194, 0.058655333, -0.0188209, 0.07730204, -0.0055956733, -0.023868905, -0.20695664, -0.31194496, 0.41362855, 0.0053717373, -0.019203134, 0.20079727, -0.0017283544, 0.018228168, -0.012408222, -0.025949791, -0.035805702, -0.026664188, 0.014544583, 0.5836619, 0.12910439, 0.035587203, 0.065148644, 0.052373927, 0.006231251, 0.019391233], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, 21, -1, -1, 23, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [45.576088, 5.5188217, 12.887922, 2.647953, 0.0, 15.496004, 6.0460067, 1.9807539, 1.3083477, 10.143814, 16.24869, 0.0, 1.9064763, 0.0, 0.0, 1.0885801, 0.47211456, 14.4552, 0.0, 0.0, 3.2352667, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7884598, 0.8613994, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 16, 16, 17, 17, 20, 20, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, 22, -1, -1, 24, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.9230769, 0.1923077, -0.03846154, 0.0023526351, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0188209, 0.6923077, -0.0055956733, -0.023868905, 1.0, 0.88461536, 1.0, 0.0053717373, -0.019203134, -0.15384616, -0.0017283544, 0.018228168, -0.012408222, -0.025949791, -0.035805702, -0.026664188, 0.014544583, -0.3846154, -0.42307693, 0.035587203, 0.065148644, 0.052373927, 0.006231251, 0.019391233], "split_indices": [127, 1, 1, 1, 0, 53, 39, 80, 23, 106, 5, 0, 1, 0, 0, 97, 1, 124, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 837.0, 1224.0, 723.0, 114.0, 877.0, 347.0, 247.0, 476.0, 421.0, 456.0, 155.0, 192.0, 148.0, 99.0, 250.0, 226.0, 317.0, 104.0, 165.0, 291.0, 101.0, 91.0, 97.0, 153.0, 112.0, 114.0, 123.0, 194.0, 199.0, 92.0, 91.0, 103.0, 98.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021535098, 0.09978252, -0.16689861, 0.060767524, 0.036714293, -0.19658421, 0.0011477636, 0.029222265, 0.037131984, -0.24569814, -0.07895631, -0.04856141, 0.16232802, -0.22222829, -0.034056574, -0.017564965, 0.0027915223, -0.01481616, -0.017842947, 0.2572637, -0.0056948154, -0.01362844, -0.2646986, 0.028873665, -0.018871883, 0.043546755, -0.002964443, -0.02215734, -0.030490553, -0.04456314, 0.014534353, 0.007302412, -0.015220073], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [34.796055, 13.351762, 4.193804, 10.942638, 0.0, 3.922697, 0.0, 10.498404, 0.0, 1.0665054, 2.0667536, 2.8047547, 7.7856083, 1.4016209, 0.0, 0.0, 0.0, 3.8596714, 0.0, 13.344442, 0.0, 0.0, 0.44561958, 3.4725893, 0.0, 0.0, 0.0, 0.0, 0.0, 3.151546, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.9230769, 5.0, 0.036714293, 1.0, 0.0011477636, 1.0, 0.037131984, 1.0, 1.0, 1.0, 1.0, 0.0, -0.034056574, -0.017564965, 0.0027915223, 0.46153846, -0.017842947, 0.03846154, -0.0056948154, -0.01362844, 1.0, -0.1923077, -0.018871883, 0.043546755, -0.002964443, -0.02215734, -0.030490553, 1.0, 0.014534353, 0.007302412, -0.015220073], "split_indices": [137, 125, 1, 0, 0, 93, 0, 50, 0, 0, 13, 23, 106, 1, 0, 0, 0, 1, 0, 1, 0, 0, 106, 1, 0, 0, 0, 0, 0, 122, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1280.0, 792.0, 1117.0, 163.0, 679.0, 113.0, 1014.0, 103.0, 479.0, 200.0, 640.0, 374.0, 384.0, 95.0, 105.0, 95.0, 508.0, 132.0, 261.0, 113.0, 127.0, 257.0, 406.0, 102.0, 161.0, 100.0, 124.0, 133.0, 249.0, 157.0, 119.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0053188493, -0.12047456, 0.13002472, -0.018015718, -0.17305608, 0.09465673, 0.03485745, 0.05452488, -0.012302206, -0.14622436, -0.027509212, 0.028875966, 0.31638598, 0.0023549097, 0.007882482, -0.18343806, -0.0011682514, -0.034029223, 0.117955, 0.021698814, 0.040565997, -0.027577294, -0.12893966, -0.012899532, 0.017803406, 0.0009923355, 0.026317785, -0.0032362074, -0.02315065, -0.007112647, 0.0086303465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, -1, -1, 21, -1, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [32.519516, 5.5598392, 8.046581, 2.6660283, 1.867178, 13.068623, 0.0, 0.15581065, 0.0, 2.7036705, 0.0, 3.872042, 1.8190956, 0.0, 0.0, 2.1285791, 0.0, 1.9935491, 4.486956, 0.0, 0.0, 0.0, 2.6349049, 0.0, 1.5960262, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, -1, -1, 22, -1, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 0.03485745, 1.0, -0.012302206, 1.0, -0.027509212, 1.0, 1.0, 0.0023549097, 0.007882482, 1.0, -0.0011682514, 1.0, 1.0, 0.021698814, 0.040565997, -0.027577294, 1.0, -0.012899532, 1.0, 0.0009923355, 0.026317785, -0.0032362074, -0.02315065, -0.007112647, 0.0086303465], "split_indices": [71, 137, 125, 93, 58, 0, 0, 39, 0, 93, 0, 50, 39, 0, 0, 111, 0, 17, 12, 0, 0, 0, 106, 0, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1032.0, 1041.0, 350.0, 682.0, 896.0, 145.0, 207.0, 143.0, 540.0, 142.0, 691.0, 205.0, 91.0, 116.0, 423.0, 117.0, 405.0, 286.0, 97.0, 108.0, 157.0, 266.0, 143.0, 262.0, 164.0, 122.0, 137.0, 129.0, 114.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.005162327, -0.12044373, 0.09029646, -0.15171893, 0.0076809498, 0.058781013, 0.030548472, -0.111107595, -0.20235617, -0.09724358, 0.11501323, -0.14323166, -0.00023799839, -0.24150915, -0.014513257, -0.017218847, -0.0039161276, 0.16651802, -0.045972932, -0.0057043936, -0.19824508, -0.021406034, -0.026519045, 0.07744896, 0.037957527, -0.018124219, 0.0112353545, -0.025743444, -0.01309844, -0.0069607114, 0.0319062, 0.075566426, -0.019264681, -0.0068803728, 0.025268037], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, -1, -1, -1, 23, 25, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [22.049715, 5.1388884, 8.334775, 1.4785843, 0.0, 9.405309, 0.0, 1.3936157, 0.7169504, 1.2362432, 6.5337486, 1.4603758, 0.0, 0.12350273, 0.0, 0.0, 0.0, 11.329151, 4.090591, 0.0, 0.7484493, 0.0, 0.0, 8.586075, 0.0, 0.0, 0.0, 0.0, 0.0, 4.7811327, 0.0, 5.5231133, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 18, 18, 20, 20, 23, 23, 29, 29, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, -1, -1, -1, 24, 26, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [1.0, 2.9230769, 1.0, 1.0, 0.0076809498, 1.0, 0.030548472, 1.0, 1.0, 1.0, 0.26923078, 1.0, -0.00023799839, 1.0, -0.014513257, -0.017218847, -0.0039161276, -0.115384616, 0.7307692, -0.0057043936, 1.0, -0.021406034, -0.026519045, 1.0, 0.037957527, -0.018124219, 0.0112353545, -0.025743444, -0.01309844, 1.0, 0.0319062, 1.0, -0.019264681, -0.0068803728, 0.025268037], "split_indices": [127, 1, 125, 80, 0, 17, 0, 93, 93, 105, 1, 122, 0, 122, 0, 0, 0, 1, 1, 0, 12, 0, 0, 61, 0, 0, 0, 0, 0, 7, 0, 111, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 833.0, 1229.0, 719.0, 114.0, 1072.0, 157.0, 399.0, 320.0, 284.0, 788.0, 308.0, 91.0, 190.0, 130.0, 124.0, 160.0, 597.0, 191.0, 120.0, 188.0, 88.0, 102.0, 421.0, 176.0, 103.0, 88.0, 100.0, 88.0, 312.0, 109.0, 216.0, 96.0, 119.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004030147, -0.10008116, 0.109052055, -0.076078184, -0.026626969, 0.13386483, -0.015301542, -0.15866037, -0.017068608, 0.08211185, 0.2954794, -0.20184559, -0.0035273961, 0.08085232, -0.087330066, 0.12629224, -0.00598405, 0.022254515, 0.038243942, -0.24266468, -0.012721672, -0.0004520115, 0.019408317, -0.01526961, -1.7531842e-05, -0.0054184846, 0.18026267, -0.029328382, -0.019148305, 0.03344446, 0.07246067, -0.009187422, 0.018127702], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [22.600512, 4.140602, 6.6911964, 4.4199376, 0.0, 7.8621902, 0.0, 2.0141602, 3.6395555, 4.465315, 1.4460564, 0.8529587, 0.0, 2.136362, 1.7546637, 5.289051, 0.0, 0.0, 0.0, 0.46892738, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.947628, 0.0, 0.0, 0.0, 4.3990517, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.026626969, 1.0, -0.015301542, 1.3461539, 1.0, 0.5769231, 1.0, 1.0, -0.0035273961, 1.0, 1.0, 1.0, -0.00598405, 0.022254515, 0.038243942, 1.0, -0.012721672, -0.0004520115, 0.019408317, -0.01526961, -1.7531842e-05, -0.0054184846, 1.0, -0.029328382, -0.019148305, 0.03344446, 1.0, -0.009187422, 0.018127702], "split_indices": [71, 40, 41, 17, 0, 113, 0, 1, 122, 1, 61, 83, 0, 127, 2, 89, 0, 0, 0, 122, 0, 0, 0, 0, 0, 0, 69, 0, 0, 0, 93, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1038.0, 1029.0, 907.0, 131.0, 940.0, 89.0, 378.0, 529.0, 712.0, 228.0, 280.0, 98.0, 221.0, 308.0, 543.0, 169.0, 124.0, 104.0, 181.0, 99.0, 126.0, 95.0, 176.0, 132.0, 125.0, 418.0, 91.0, 90.0, 172.0, 246.0, 98.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0039949617, -0.08922785, 0.09631967, -0.058262717, -0.20037052, 0.0633191, 0.03011982, -0.08441731, 0.01000414, -0.016119827, -0.025260016, 0.009106792, 0.24552533, -0.022769395, -0.1310062, 0.083624184, -0.06391168, 0.0037951656, 0.041122008, -0.014940867, 0.048548497, -0.20418426, -0.07104082, -0.010817746, 0.020388028, -0.029590677, -0.015801763, 0.0117130885, -0.0026063074, -0.020808576, -0.020032657, -0.015189903, -0.00026222988, 0.0096552735, -0.0136490045, -0.014639343, 0.005761136], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, 21, 23, 25, -1, -1, -1, 27, 29, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.781584, 3.5379105, 7.018029, 3.3288662, 0.458292, 8.830771, 0.0, 1.9817533, 0.0, 0.0, 0.0, 3.7489498, 7.050741, 2.6823993, 1.7245421, 3.8728118, 1.1239736, 0.0, 0.0, 0.0, 0.9722378, 0.0026626587, 1.1949548, 2.5772579, 0.0, 2.597287, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 20, 20, 21, 21, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, 22, 24, 26, -1, -1, -1, 28, 30, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 0.03011982, 1.0, 0.01000414, -0.016119827, -0.025260016, 1.0, -0.26923078, 1.0, 1.0, 1.0, 1.0, 0.0037951656, 0.041122008, -0.014940867, 0.23076923, 0.15384616, 1.0, 1.0, 0.020388028, 1.0, -0.015801763, 0.0117130885, -0.0026063074, -0.020808576, -0.020032657, -0.015189903, -0.00026222988, 0.0096552735, -0.0136490045, -0.014639343, 0.005761136], "split_indices": [71, 116, 125, 121, 109, 0, 0, 106, 0, 0, 0, 121, 1, 17, 39, 109, 115, 0, 0, 0, 1, 1, 124, 15, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1028.0, 1038.0, 804.0, 224.0, 894.0, 144.0, 690.0, 114.0, 128.0, 96.0, 689.0, 205.0, 297.0, 393.0, 341.0, 348.0, 91.0, 114.0, 107.0, 190.0, 177.0, 216.0, 191.0, 150.0, 255.0, 93.0, 99.0, 91.0, 88.0, 89.0, 99.0, 117.0, 103.0, 88.0, 109.0, 146.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.006426739, -0.08778987, 0.07470078, -0.06736689, -0.022719216, 0.033441935, 0.19535163, -0.04112303, -0.018799083, 0.00501952, 0.025436161, 0.011007682, 0.03630268, -0.12260665, 0.016649418, -0.079963155, 0.100000165, -0.022729198, -0.049731765, 0.014598431, -0.06858276, -0.15318972, 0.013105528, 0.023108969, 0.02791487, -0.010024708, 2.2840393e-05, -0.015689893, -0.0006241919, -0.023230737, -0.002237138, -0.006188092, 0.01491929], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, 19, 21, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.657033, 2.940961, 5.1571193, 2.8522391, 0.0, 4.8474417, 3.7747927, 3.4835558, 0.0, 5.521049, 0.0, 0.0, 0.0, 2.3420815, 4.773173, 5.578228, 4.4493055, 0.0, 0.45679972, 0.0, 1.4369881, 2.77381, 0.0, 2.4217842, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 20, 20, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, 20, 22, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.022719216, 4.0, 1.0, 1.0, -0.018799083, 1.0, 0.025436161, 0.011007682, 0.03630268, 1.0, 1.0, 1.0, 1.0, -0.022729198, 0.88461536, 0.014598431, 1.0, 1.0, 0.013105528, 1.0, 0.02791487, -0.010024708, 2.2840393e-05, -0.015689893, -0.0006241919, -0.023230737, -0.002237138, -0.006188092, 0.01491929], "split_indices": [71, 40, 113, 58, 0, 0, 16, 17, 0, 39, 0, 0, 0, 111, 122, 58, 50, 0, 1, 0, 16, 116, 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1033.0, 1036.0, 901.0, 132.0, 772.0, 264.0, 740.0, 161.0, 684.0, 88.0, 175.0, 89.0, 307.0, 433.0, 361.0, 323.0, 126.0, 181.0, 172.0, 261.0, 268.0, 93.0, 226.0, 97.0, 90.0, 91.0, 108.0, 153.0, 167.0, 101.0, 135.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0022461533, -0.0798771, 0.08476846, -0.061371192, -0.020856433, 0.107875064, -0.016231464, 0.0014354623, -0.09479711, 0.06440887, 0.26296073, 0.0067128325, -0.009154517, -0.019130616, -0.07557376, 0.013761687, 0.03214119, 0.019751433, 0.03424816, -0.11137426, 0.00341947, 0.05421138, -0.009450784, -0.1796617, -0.047710426, -0.012777694, 0.10464816, -0.025716234, -0.010471601, -0.010249685, 0.0007075961, 0.19532405, -0.004151595, 0.028629735, 0.010091776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, -1, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [13.980951, 2.4624434, 5.8748198, 1.8978312, 0.0, 6.3432646, 0.0, 1.9179633, 1.0945826, 9.56711, 1.0720949, 0.0, 0.0, 0.0, 1.933445, 2.6889942, 0.0, 0.0, 0.0, 1.6128998, 0.0, 4.10297, 0.0, 1.039691, 0.5762979, 0.0, 4.638747, 0.0, 0.0, 0.0, 0.0, 1.8551054, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, -1, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.020856433, 2.0, -0.016231464, 1.0, 1.0, 1.0, 1.0, 0.0067128325, -0.009154517, -0.019130616, 1.0, 1.0, 0.03214119, 0.019751433, 0.03424816, 1.0, 0.00341947, 1.0, -0.009450784, 1.0, 1.0, -0.012777694, 1.0, -0.025716234, -0.010471601, -0.010249685, 0.0007075961, 1.0, -0.004151595, 0.028629735, 0.010091776], "split_indices": [71, 40, 41, 137, 0, 0, 0, 93, 89, 125, 126, 0, 0, 0, 93, 23, 0, 0, 0, 39, 0, 89, 0, 12, 115, 0, 97, 0, 0, 0, 0, 59, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1034.0, 1029.0, 904.0, 130.0, 941.0, 88.0, 314.0, 590.0, 735.0, 206.0, 184.0, 130.0, 98.0, 492.0, 614.0, 121.0, 113.0, 93.0, 371.0, 121.0, 447.0, 167.0, 179.0, 192.0, 97.0, 350.0, 88.0, 91.0, 96.0, 96.0, 216.0, 134.0, 110.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002233194, -0.064871155, 0.06979582, -0.079679675, 0.0070147677, 0.10628444, -0.042722866, -0.14076039, -0.03710151, 0.21366951, 0.030594522, -0.017447032, 0.010341715, -0.17207316, -0.007862408, 0.058115236, -0.09904977, 0.06218484, 0.055682864, 0.1562569, -0.079489864, -0.021269657, -0.009544262, 0.013871305, -0.0042632, -0.062841624, -0.019210076, -0.0040514483, 0.014187954, -0.003836418, 0.033002574, -0.0029667437, -0.015023768, -0.013070688, 0.002376734], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, 21, -1, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [9.325915, 2.0634112, 4.208293, 2.4186544, 0.0, 6.291047, 4.832648, 0.7432418, 3.2323844, 16.634666, 6.2803936, 0.0, 0.0, 0.790699, 0.0, 1.7539217, 1.1185753, 1.8169796, 0.0, 7.169646, 0.85300803, 0.0, 0.0, 0.0, 0.0, 1.4047801, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, 22, -1, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.6153846, 0.30769232, 1.0, 0.0070147677, 1.0, 1.0, 1.0, 1.0, -0.1923077, 1.0, -0.017447032, 0.010341715, 0.88461536, -0.007862408, 0.15384616, 0.46153846, 1.0, 0.055682864, 1.0, 1.0, -0.021269657, -0.009544262, 0.013871305, -0.0042632, 1.0, -0.019210076, -0.0040514483, 0.014187954, -0.003836418, 0.033002574, -0.0029667437, -0.015023768, -0.013070688, 0.002376734], "split_indices": [71, 1, 1, 17, 0, 69, 39, 83, 122, 1, 121, 0, 0, 1, 0, 1, 1, 13, 0, 50, 39, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1032.0, 1025.0, 930.0, 102.0, 774.0, 251.0, 382.0, 548.0, 320.0, 454.0, 132.0, 119.0, 254.0, 128.0, 216.0, 332.0, 222.0, 98.0, 212.0, 242.0, 166.0, 88.0, 120.0, 96.0, 239.0, 93.0, 97.0, 125.0, 100.0, 112.0, 142.0, 100.0, 134.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030403538, -0.0777701, 0.048535418, -0.10343408, 0.00796936, 0.088560715, -0.063560344, -0.07880656, -0.020177428, 0.13437738, -0.055434484, -0.019878877, 0.041036215, 0.0037913586, -0.118834786, -0.00066571514, 0.1801109, 0.007150728, -0.016802633, -0.003395836, 0.011520671, -0.006256249, 0.007228568, -0.06949093, -0.17185317, 0.11918514, 0.031050688, 0.0038143084, -0.016132548, -0.021896282, -0.012163735, 0.022189692, 0.03261665, 0.0080692, -0.006345283], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [7.947452, 3.4026437, 5.4737325, 1.7534361, 0.0, 5.931044, 4.540362, 1.9143178, 0.0, 4.398904, 3.1014962, 0.0, 1.0067918, 0.8589789, 1.0202904, 0.0, 4.091404, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9966738, 0.44474125, 7.0467615, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1974583, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 16, 16, 23, 23, 24, 24, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 2.9230769, 0.23076923, 1.0, 0.00796936, 1.0, 1.0, -0.03846154, -0.020177428, 1.0, 1.0, -0.019878877, 0.9230769, 1.0, 1.0, -0.00066571514, -0.1923077, 0.007150728, -0.016802633, -0.003395836, 0.011520671, -0.006256249, 0.007228568, 1.0, 0.84615386, 1.0, 0.031050688, 0.0038143084, -0.016132548, -0.021896282, -0.012163735, -0.3846154, 0.03261665, 0.0080692, -0.006345283], "split_indices": [127, 1, 1, 0, 0, 7, 69, 1, 0, 17, 69, 0, 1, 13, 59, 0, 1, 0, 0, 0, 0, 0, 0, 23, 1, 61, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 842.0, 1220.0, 724.0, 118.0, 899.0, 321.0, 579.0, 145.0, 682.0, 217.0, 140.0, 181.0, 189.0, 390.0, 167.0, 515.0, 102.0, 115.0, 90.0, 91.0, 96.0, 93.0, 202.0, 188.0, 351.0, 164.0, 93.0, 109.0, 97.0, 91.0, 239.0, 112.0, 142.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00408051, 0.050788537, -0.07092293, 0.113906756, -0.0039200746, -0.08996383, 0.0041501145, 0.17272127, -0.0028539947, -0.061135665, 0.09836737, -0.10589001, 0.0016994914, 0.059308697, 0.2641357, -0.020741612, -0.0071947332, 0.016980346, -0.0018388348, -0.13530725, 5.1762578e-05, 0.010061634, 0.002015246, 0.0063943355, 0.041315105, 0.009858831, -0.010533082, -0.018758122, -0.10325073, -0.14834625, -0.00012733322, -0.011503055, -0.01781717], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, -1, 21, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1], "loss_changes": [7.2482505, 4.402716, 1.699681, 4.9597363, 3.9972138, 1.1566386, 0.0, 4.344001, 0.0, 3.45604, 2.04344, 1.8499603, 0.0, 0.30246317, 6.9209595, 0.0, 3.321963, 0.0, 0.0, 0.7758579, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.319833, 0.19773674, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 19, 19, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, -1, 22, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.9230769, 0.15384616, 1.0, 1.0, 0.0041501145, 1.0, -0.0028539947, 1.0, 1.0, 1.0, 0.0016994914, 1.0, 1.0, -0.020741612, 1.0, 0.016980346, -0.0018388348, 1.0, 5.1762578e-05, 0.010061634, 0.002015246, 0.0063943355, 0.041315105, 0.009858831, -0.010533082, -0.018758122, 1.0, 1.0, -0.00012733322, -0.011503055, -0.01781717], "split_indices": [137, 53, 1, 1, 97, 73, 0, 124, 0, 5, 12, 61, 0, 119, 109, 0, 13, 0, 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 93, 126, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1275.0, 794.0, 592.0, 683.0, 679.0, 115.0, 419.0, 173.0, 438.0, 245.0, 591.0, 88.0, 187.0, 232.0, 118.0, 320.0, 152.0, 93.0, 463.0, 128.0, 91.0, 96.0, 99.0, 133.0, 154.0, 166.0, 176.0, 287.0, 199.0, 88.0, 94.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0014915151, -0.047095694, 0.05055045, -0.026958603, -0.13660558, 0.07751673, -0.06790351, -0.078804836, 0.010480046, -0.021191502, -0.007226349, 0.15786059, 0.01915996, -0.023614293, 0.012089846, -0.12002249, 0.0028194503, 0.11059576, -0.052298777, 0.035891727, 0.037759814, -0.023943888, 0.019656636, -0.017060518, -0.0056572245, 0.020466268, 0.002943997, 0.00040693712, -0.012151557, -0.005543051, 0.010779901, 0.048305508, -0.01952248, -0.005310328, 0.01664827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [4.9341283, 1.874567, 3.2900908, 1.647954, 0.92550206, 3.933742, 6.0669117, 1.5700533, 3.098578, 0.0, 0.0, 9.460802, 3.7163932, 0.0, 0.0, 0.8248372, 0.0, 1.4504738, 1.1821918, 1.4906489, 0.0, 4.8386035, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.2956567, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.5769231, 1.0, 1.0, 1.0, 1.0, 1.3846154, 1.0, -0.021191502, -0.007226349, -0.1923077, 1.0, -0.023614293, 0.012089846, 1.0, 0.0028194503, 0.26923078, 1.0, 1.0, 0.037759814, 1.0, 0.019656636, -0.017060518, -0.0056572245, 0.020466268, 0.002943997, 0.00040693712, -0.012151557, -0.005543051, 0.010779901, 1.0, -0.01952248, -0.005310328, 0.01664827], "split_indices": [71, 58, 1, 17, 13, 69, 39, 1, 122, 0, 0, 1, 42, 0, 0, 83, 0, 1, 15, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 124, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1040.0, 1030.0, 849.0, 191.0, 839.0, 191.0, 356.0, 493.0, 88.0, 103.0, 353.0, 486.0, 101.0, 90.0, 257.0, 99.0, 190.0, 303.0, 227.0, 126.0, 391.0, 95.0, 143.0, 114.0, 88.0, 102.0, 167.0, 136.0, 100.0, 127.0, 275.0, 116.0, 148.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005802838, 0.004394631, -0.017466603, 0.03768009, -0.05190639, -0.10458239, 0.06404534, -0.07462285, 0.008706487, -0.01895124, -0.0010319379, 0.041382473, 0.021791425, -0.11245664, -0.007995175, -0.04575129, 0.07168422, -0.07797231, -0.021660626, 0.005147701, -0.0075322203, 0.0037346713, -0.010501974, 0.11014358, -0.0037100818, -0.019270513, -0.010231109, 0.04038322, 0.027529055, 0.0010272057, -0.0032086167, -0.027596842, 0.021883091, -0.013782904, 0.005507729], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [3.5679371, 3.6618063, 0.0, 4.605965, 2.291935, 1.5371051, 3.6126451, 1.5729649, 0.0, 0.0, 0.0, 2.3841953, 0.0, 1.4294286, 0.9049235, 1.1475462, 2.8031483, 2.3238692, 0.0, 0.0, 0.0, 0.0, 0.0, 5.702752, 0.0, 0.0, 0.08424246, 4.221549, 0.0, 0.0, 0.0, 2.2965648, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 23, 23, 26, 26, 27, 27, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.017466603, -1.0, 2.9230769, 1.0, 1.0, 1.0, 0.008706487, -0.01895124, -0.0010319379, 1.0, 0.021791425, 0.84615386, 1.0, 1.0, 0.26923078, 1.0, -0.021660626, 0.005147701, -0.0075322203, 0.0037346713, -0.010501974, -0.115384616, -0.0037100818, -0.019270513, 1.0, 1.0, 0.027529055, 0.0010272057, -0.0032086167, 1.0, 0.021883091, -0.013782904, 0.005507729], "split_indices": [43, 137, 0, 0, 1, 16, 125, 115, 0, 0, 0, 17, 0, 1, 69, 13, 1, 69, 0, 0, 0, 0, 0, 1, 0, 0, 122, 61, 0, 0, 0, 111, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1954.0, 118.0, 1228.0, 726.0, 192.0, 1036.0, 624.0, 102.0, 101.0, 91.0, 903.0, 133.0, 398.0, 226.0, 233.0, 670.0, 299.0, 99.0, 120.0, 106.0, 97.0, 136.0, 495.0, 175.0, 111.0, 188.0, 348.0, 147.0, 97.0, 91.0, 252.0, 96.0, 108.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.003946914, -0.016161252, 0.016312776, 0.033090655, -0.059286825, -0.059417795, 0.12076207, -0.03265963, -0.16658258, -0.11549296, 0.010037159, 0.035074286, 0.02734421, 0.0059752753, -0.057628203, -0.011661532, -0.0211413, -0.003996154, -0.018136337, -0.012815061, 0.114213645, -0.025235655, -0.14376292, 0.019018747, 0.0021167004, -0.060570892, 0.009254845, -0.019243434, -0.009509144, -0.124050416, 0.0014450344, -0.0043333443, -0.019043444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, -1, -1, -1, -1, -1, 23, 25, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [4.1936517, 4.0674925, 0.0, 7.25065, 2.916981, 3.8976943, 6.0050106, 1.8874574, 0.45472956, 1.6020408, 0.0, 3.797749, 0.0, 0.0, 1.7968385, 0.0, 0.0, 0.0, 0.0, 0.0, 1.399683, 1.9477829, 0.41692734, 0.0, 0.0, 1.7144328, 0.0, 0.0, 0.0, 1.0448713, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 14, 14, 20, 20, 21, 21, 22, 22, 25, 25, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, -1, -1, -1, -1, -1, 24, 26, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [5.0, -0.03846154, 0.016312776, 1.0, 1.0, -0.1923077, 1.0, 1.0, 1.0, 1.0, 0.010037159, -0.5, 0.02734421, 0.0059752753, 1.3461539, -0.011661532, -0.0211413, -0.003996154, -0.018136337, -0.012815061, -0.30769232, 0.88461536, 1.0, 0.019018747, 0.0021167004, 1.0, 0.009254845, -0.019243434, -0.009509144, 1.0, 0.0014450344, -0.0043333443, -0.019043444], "split_indices": [0, 1, 0, 109, 15, 1, 113, 26, 5, 71, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 50, 0, 0, 124, 0, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1915.0, 140.0, 894.0, 1021.0, 435.0, 459.0, 818.0, 203.0, 322.0, 113.0, 294.0, 165.0, 174.0, 644.0, 96.0, 107.0, 150.0, 172.0, 96.0, 198.0, 468.0, 176.0, 109.0, 89.0, 360.0, 108.0, 88.0, 88.0, 195.0, 165.0, 88.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.001911444, -0.015245182, 0.12947124, -0.026987633, 0.013671081, 0.024426963, 0.0005938146, 0.014414386, -0.05984571, -0.024685817, 0.022718469, -0.039707884, -0.018184004, -0.09030105, 0.017418504, -0.09285693, 0.018782767, -0.055685535, -0.02191107, -0.04531907, -0.016925706, -0.06234982, 0.017334925, 0.010504578, -0.127958, -0.014280133, 0.0018151987, -0.018603561, 0.006421237, 0.014070447, -0.010741228, -0.018981788, -0.0069353855, 0.009049656, -0.008305187], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, 25, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.6315272, 3.35812, 2.7086468, 2.3766026, 0.0, 0.0, 0.0, 6.430867, 2.3928256, 8.52097, 0.0, 2.5988917, 0.0, 2.1892767, 0.0, 1.5907707, 4.991071, 1.851301, 0.0, 1.2405844, 0.0, 4.0856805, 0.0, 3.101258, 0.6706698, 0.0, 1.3697553, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, 26, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, -0.03846154, 0.013671081, 0.024426963, 0.0005938146, -0.15384616, 1.0, 1.0, 0.022718469, 1.0, -0.018184004, 1.0, 0.017418504, 1.0, 1.2307693, 1.0, -0.02191107, 0.34615386, -0.016925706, 1.0, 0.017334925, 1.0, 1.0, -0.014280133, 1.0769231, -0.018603561, 0.006421237, 0.014070447, -0.010741228, -0.018981788, -0.0069353855, 0.009049656, -0.008305187], "split_indices": [125, 0, 15, 1, 0, 0, 0, 1, 64, 61, 0, 39, 0, 0, 0, 115, 1, 108, 0, 1, 0, 124, 0, 126, 69, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1882.0, 191.0, 1747.0, 135.0, 99.0, 92.0, 773.0, 974.0, 653.0, 120.0, 836.0, 138.0, 491.0, 162.0, 438.0, 398.0, 387.0, 104.0, 270.0, 168.0, 261.0, 137.0, 202.0, 185.0, 88.0, 182.0, 132.0, 129.0, 96.0, 106.0, 90.0, 95.0, 89.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020424174, 0.013306412, -0.13959156, 0.046102077, -0.042880625, -0.008768621, -0.018583447, 0.026247652, 0.017509257, -0.07945751, -0.0033110776, 0.0068126083, 0.021997113, -0.11422791, 0.0008673384, 0.009199134, -0.060677595, -0.032688703, 0.07314285, -0.015970489, -0.0037156388, 0.002878099, -0.014841585, -0.01182911, 0.0036083849, 0.13273682, -0.0073458287, 0.07070183, -0.009827424, 0.024380093, -0.0063185254, 0.017783677, -0.004363543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [4.374445, 3.4347765, 0.49925184, 3.0143344, 0.99431586, 0.0, 0.0, 3.8403242, 0.0, 1.093972, 1.8041654, 2.428862, 0.0, 0.897274, 0.0, 0.0, 1.6168823, 1.8052357, 3.022844, 0.0, 0.0, 0.0, 0.0, 0.0, 2.7889476, 5.3529396, 0.0, 3.013381, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, -0.008768621, -0.018583447, 5.0, 0.017509257, 1.0, 1.0, 1.0, 0.021997113, 1.0, 0.0008673384, 0.009199134, 1.0, 1.0, 1.0, -0.015970489, -0.0037156388, 0.002878099, -0.014841585, -0.01182911, -0.03846154, 0.03846154, -0.0073458287, 1.0, -0.009827424, 0.024380093, -0.0063185254, 0.017783677, -0.004363543], "split_indices": [40, 137, 17, 125, 13, 0, 0, 0, 0, 83, 97, 50, 0, 116, 0, 0, 17, 17, 106, 0, 0, 0, 0, 0, 1, 1, 0, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1864.0, 208.0, 1177.0, 687.0, 98.0, 110.0, 1020.0, 157.0, 357.0, 330.0, 927.0, 93.0, 256.0, 101.0, 124.0, 206.0, 581.0, 346.0, 161.0, 95.0, 102.0, 104.0, 173.0, 408.0, 246.0, 100.0, 246.0, 162.0, 157.0, 89.0, 127.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003195167, -0.015709372, 0.124111496, -0.027385952, 0.013419727, 0.027544329, -0.0025593056, 0.011719694, -0.058381394, -0.02185099, 0.019593533, -0.043915827, -0.018341543, -0.06903437, 0.012474388, -0.02095999, -0.01368007, -0.038819548, -0.01866029, 0.02713621, -0.07974425, 0.008718679, -0.092816785, -0.032278165, 0.010149106, -0.15409857, 0.003032355, 0.009076045, -0.0077344715, -0.017571919, -0.0013442108, 0.0041006906, -0.008649723, -0.008906994, -0.022799471], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, 25, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.2930236, 3.2942464, 4.1911874, 2.1163216, 0.0, 0.0, 0.0, 4.7742376, 1.761661, 4.5166965, 0.0, 1.8614538, 0.0, 1.7548411, 0.0, 1.9791098, 0.0, 1.0088048, 0.0, 1.7008331, 2.577966, 1.4757057, 1.2107843, 0.850318, 0.0, 0.9034071, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, 26, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, -0.03846154, 0.013419727, 0.027544329, -0.0025593056, -0.15384616, 2.0, 1.0, 0.019593533, 1.0, -0.018341543, 1.0, 0.012474388, 1.0, -0.01368007, 1.0, -0.01866029, 1.0, 1.0, 1.0, 1.0, 1.0, 0.010149106, 1.0, 0.003032355, 0.009076045, -0.0077344715, -0.017571919, -0.0013442108, 0.0041006906, -0.008649723, -0.008906994, -0.022799471], "split_indices": [125, 0, 15, 1, 0, 0, 0, 1, 0, 61, 0, 15, 0, 0, 0, 23, 0, 108, 0, 93, 50, 111, 69, 59, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1882.0, 185.0, 1746.0, 136.0, 92.0, 93.0, 772.0, 974.0, 653.0, 119.0, 873.0, 101.0, 494.0, 159.0, 700.0, 173.0, 393.0, 101.0, 385.0, 315.0, 209.0, 184.0, 214.0, 171.0, 188.0, 127.0, 107.0, 102.0, 90.0, 94.0, 91.0, 123.0, 100.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025999502, 0.0063538253, -0.015198262, -0.0041782064, 0.014588405, -0.02516366, 0.070057824, -0.010046321, -0.014047945, -0.10608733, 0.21129134, -0.031042608, 0.052608192, -0.023817321, 0.0023063286, 0.032513637, 0.011095334, -0.014002571, -0.015644262, 0.020608123, -0.02339749, 0.0151541475, -0.05393681, 0.006089045, -0.010002292, -0.023963334, -0.013969905, -0.08706284, 0.04200432, 0.002649511, -0.020899804, -0.005656606, 0.013426623], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, -1, 23, -1, 25, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [2.7673686, 2.868536, 0.0, 2.8275466, 0.0, 2.4667234, 9.95104, 1.6457056, 0.0, 3.0364983, 2.535901, 1.572435, 3.662755, 0.0, 0.0, 0.0, 0.0, 0.0, 5.2560153, 0.0, 1.3563062, 0.0, 1.7171551, 0.0, 0.0, 2.0604508, 0.0, 3.5032187, 2.2008188, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 18, 18, 20, 20, 22, 22, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, -1, 24, -1, 26, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, -0.015198262, 1.0, 0.014588405, 2.0, 1.0, 1.0, -0.014047945, -0.115384616, -0.3846154, -0.46153846, 1.0, -0.023817321, 0.0023063286, 0.032513637, 0.011095334, -0.014002571, -0.26923078, 0.020608123, 1.0, 0.0151541475, 1.0, 0.006089045, -0.010002292, 1.0, -0.013969905, 0.61538464, 1.0, 0.002649511, -0.020899804, -0.005656606, 0.013426623], "split_indices": [43, 0, 0, 113, 0, 0, 109, 83, 0, 1, 1, 1, 122, 0, 0, 0, 0, 0, 1, 0, 13, 0, 15, 0, 0, 39, 0, 1, 71, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1952.0, 117.0, 1815.0, 137.0, 1415.0, 400.0, 1251.0, 164.0, 178.0, 222.0, 937.0, 314.0, 88.0, 90.0, 104.0, 118.0, 116.0, 821.0, 104.0, 210.0, 153.0, 668.0, 100.0, 110.0, 495.0, 173.0, 253.0, 242.0, 131.0, 122.0, 117.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0016325237, 0.010930218, -0.015822254, 0.039955255, -0.038189072, 0.05275616, -0.009888531, -0.061022785, 0.010356075, 0.06806618, -0.008081051, -0.093273155, 0.015441792, 0.046573024, 0.02425286, -0.12957518, -0.0030752984, 0.010943758, -0.007459625, 0.029538674, 0.015431873, -0.009062723, -0.018430373, 0.0570282, -0.008367476, -0.032719065, 0.09947339, 0.003355798, -0.010377902, 0.024107844, 0.02147647, -0.0077687614, 0.02162611], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, -1, -1, -1, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [3.0825515, 2.794351, 0.0, 2.1896148, 2.356299, 2.306656, 0.0, 1.5461893, 0.0, 3.7947445, 0.0, 1.0008972, 1.5741549, 1.6536751, 0.0, 0.5947056, 0.0, 0.0, 0.0, 2.4212787, 0.0, 0.0, 0.0, 2.3846467, 0.0, 0.94663864, 3.692821, 0.0, 0.0, 5.0270047, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 23, 23, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, -1, -1, -1, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.015822254, 1.0, 2.9230769, 1.0, -0.009888531, 1.0, 0.010356075, 1.0, -0.008081051, 1.0, 1.0, 1.0, 0.02425286, 1.0, -0.0030752984, 0.010943758, -0.007459625, 0.5769231, 0.015431873, -0.009062723, -0.018430373, 1.0, -0.008367476, 1.0, 1.0, 0.003355798, -0.010377902, 1.0, 0.02147647, -0.0077687614, 0.02162611], "split_indices": [43, 137, 0, 40, 1, 90, 0, 93, 0, 125, 0, 115, 12, 88, 0, 39, 0, 0, 0, 1, 0, 0, 0, 5, 0, 122, 115, 0, 0, 111, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1960.0, 114.0, 1232.0, 728.0, 1128.0, 104.0, 627.0, 101.0, 1012.0, 116.0, 441.0, 186.0, 901.0, 111.0, 279.0, 162.0, 91.0, 95.0, 778.0, 123.0, 163.0, 116.0, 626.0, 152.0, 201.0, 425.0, 104.0, 97.0, 257.0, 168.0, 168.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007090375, 0.011330463, -0.10918144, 0.019926911, -0.015349706, -0.00414924, -0.017303899, 0.029366147, -0.011669358, -0.006665459, 0.0621449, -0.025909917, 0.014597805, -0.01122197, 0.091638245, -0.09449191, 0.009123298, -0.0033551645, 0.12089799, -0.0060920357, -0.0147985, 0.011797874, -0.020183928, 0.075609095, 0.0273624, 0.030390248, -0.012050322, -0.0051728366, 0.120793365, 0.011537517, -0.0033656347, 0.0016531775, 0.21187901, 0.029738072, 0.013183484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, 17, 19, 21, -1, 23, -1, -1, -1, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [2.6928766, 2.6298242, 0.8904252, 2.2748418, 0.0, 0.0, 0.0, 1.9487675, 0.0, 2.3089075, 4.443202, 1.6770482, 0.0, 0.0, 2.706974, 0.42381907, 1.4738964, 0.0, 4.143159, 0.0, 0.0, 0.0, 1.8467782, 2.6581864, 0.0, 1.3172053, 0.0, 0.0, 3.2383857, 0.0, 0.0, 0.0, 1.2455912, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 16, 16, 18, 18, 22, 22, 23, 23, 25, 25, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, 18, 20, 22, -1, 24, -1, -1, -1, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.015349706, -0.00414924, -0.017303899, 1.0, -0.011669358, 1.8076923, -1.0, 1.0, 0.014597805, -0.01122197, -0.5, 1.0, -0.34615386, -0.0033551645, 1.0, -0.0060920357, -0.0147985, 0.011797874, 1.0, 1.0, 0.0273624, 1.0, -0.012050322, -0.0051728366, -0.07692308, 0.011537517, -0.0033656347, 0.0016531775, 0.34615386, 0.029738072, 0.013183484], "split_indices": [40, 117, 111, 43, 0, 0, 0, 71, 0, 1, 0, 17, 0, 0, 1, 109, 1, 0, 42, 0, 0, 0, 93, 39, 0, 137, 0, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1856.0, 206.0, 1764.0, 92.0, 100.0, 106.0, 1650.0, 114.0, 786.0, 864.0, 698.0, 88.0, 125.0, 739.0, 236.0, 462.0, 140.0, 599.0, 145.0, 91.0, 98.0, 364.0, 462.0, 137.0, 242.0, 122.0, 121.0, 341.0, 104.0, 138.0, 159.0, 182.0, 88.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0011959901, -0.0059628743, 0.012536746, -0.021876574, 0.05513786, -0.005741546, -0.12994929, -0.050669298, 0.14864187, 0.010711597, -0.014496046, -0.0019160666, -0.025473222, -0.017980961, 0.006074585, 0.0015376067, 0.02806737, 0.052588236, -0.047551546, 0.023655862, 0.02039071, 0.0009206323, -0.13610129, -0.028835079, 0.088673055, -0.008875616, 0.06477428, -0.017512437, -0.009836466, 0.007047682, -0.012049216, 0.00055415806, 0.016573653, 0.00020508068, 0.013818922, 0.004994144, -0.006331733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, 19, 21, 23, -1, 25, 27, 29, 31, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8427457, 1.9057838, 0.0, 2.7115407, 4.006825, 3.099167, 2.7925537, 2.733756, 3.7829947, 2.9522364, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0821204, 2.171853, 2.0169733, 0.0, 1.8724643, 0.2635944, 1.0754737, 1.6912887, 0.0, 0.8795241, 0.0, 0.0, 0.7092818, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, 20, 22, 24, -1, 26, 28, 30, 32, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.012536746, 1.0, 1.0, 1.0, 1.0, -0.115384616, 1.0, 1.0, -0.014496046, -0.0019160666, -0.025473222, -0.017980961, 0.006074585, 0.0015376067, 0.02806737, 0.84615386, 1.0769231, 1.0, 0.02039071, 1.0, 1.0, 1.0, 1.0, -0.008875616, 1.0, -0.017512437, -0.009836466, 0.15384616, -0.012049216, 0.00055415806, 0.016573653, 0.00020508068, 0.013818922, 0.004994144, -0.006331733], "split_indices": [102, 113, 0, 64, 109, 119, 12, 1, 61, 108, 0, 0, 0, 0, 0, 0, 0, 1, 1, 71, 0, 126, 115, 93, 59, 0, 93, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1960.0, 113.0, 1555.0, 405.0, 1353.0, 202.0, 190.0, 215.0, 1210.0, 143.0, 107.0, 95.0, 88.0, 102.0, 107.0, 108.0, 704.0, 506.0, 591.0, 113.0, 327.0, 179.0, 327.0, 264.0, 136.0, 191.0, 88.0, 91.0, 235.0, 92.0, 127.0, 137.0, 103.0, 88.0, 146.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028534476, 0.00682491, -0.08948886, -0.091532096, 0.021120278, -0.0029979402, -0.013751402, -0.022721311, 0.00097223645, 0.0013334868, 0.11350508, -0.013297112, 0.013946019, 0.022382142, 0.002298909, 0.0042788372, -0.08463916, 0.01682483, -0.0188675, -0.012130267, -0.0047035534, 0.0027346052, -0.01628161, 0.05139552, -0.0703398, 0.11875395, -0.005978645, 9.469391e-05, -0.01688662, 0.023100791, 0.003257185], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, 11, 13, 15, -1, -1, -1, 17, 19, -1, 21, -1, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [1.7189018, 2.5927556, 0.58873737, 3.214765, 2.9430778, 0.0, 0.0, 0.0, 0.0, 2.6796823, 2.8358502, 1.5034314, 0.0, 0.0, 0.0, 3.6510718, 0.32674694, 0.0, 2.6213875, 0.0, 0.0, 2.6064508, 0.0, 3.2951791, 2.0579228, 2.6507528, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 18, 18, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, 12, 14, 16, -1, -1, -1, 18, 20, -1, 22, -1, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, 1.0, 1.0, -0.0029979402, -0.013751402, -0.022721311, 0.00097223645, 1.0, -0.03846154, 1.0, 0.013946019, 0.022382142, 0.002298909, -0.34615386, -0.15384616, 0.01682483, 1.0, -0.012130267, -0.0047035534, 1.0, -0.01628161, 1.0, 1.0769231, 1.0, -0.005978645, 9.469391e-05, -0.01688662, 0.023100791, 0.003257185], "split_indices": [40, 1, 108, 89, 42, 0, 0, 0, 0, 88, 1, 7, 0, 0, 0, 1, 1, 0, 113, 0, 0, 23, 0, 121, 1, 137, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1844.0, 206.0, 234.0, 1610.0, 92.0, 114.0, 100.0, 134.0, 1326.0, 284.0, 1199.0, 127.0, 128.0, 156.0, 962.0, 237.0, 119.0, 843.0, 120.0, 117.0, 733.0, 110.0, 440.0, 293.0, 274.0, 166.0, 170.0, 123.0, 119.0, 155.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0052203294, 0.001655728, -0.012424308, 0.036966708, -0.025482003, 0.017717209, 0.005697879, -0.13094215, -0.0033284444, -0.015220757, 0.028590273, -0.004443455, -0.020888463, -0.08695384, 0.045055848, 0.012656445, -0.009523245, -0.03505003, -0.021247631, -0.0005954382, 0.13925694, 0.017155692, -0.071926266, -0.013738217, 0.004360823, -0.009447335, 0.063338645, -0.00013636934, 0.029393965, -0.16210909, 0.009055828, -0.0037298684, 0.019160194, -0.026977299, -0.008225836], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, 17, 19, -1, 21, 23, -1, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.693285, 1.8743564, 0.0, 3.7264488, 2.5839672, 0.0, 2.512309, 1.2945817, 3.6981866, 0.0, 2.2666235, 0.0, 0.0, 2.182556, 2.4899325, 0.0, 4.9380784, 1.9076769, 0.0, 2.3407798, 4.1110497, 0.0, 4.7623277, 0.0, 0.0, 0.0, 2.9946744, 0.0, 0.0, 1.7967801, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 20, 20, 22, 22, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, 18, 20, -1, 22, 24, -1, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.012424308, 1.0, 1.0, 0.017717209, -1.0, 1.0, 1.0, -0.015220757, -0.34615386, -0.004443455, -0.020888463, 0.5769231, 0.26923078, 0.012656445, 1.0, 1.0, -0.021247631, 1.0, 1.0, 0.017155692, 1.0, -0.013738217, 0.004360823, -0.009447335, 1.0, -0.00013636934, 0.029393965, 0.1923077, 0.009055828, -0.0037298684, 0.019160194, -0.026977299, -0.008225836], "split_indices": [43, 53, 0, 81, 89, 0, 0, 93, 81, 0, 1, 0, 0, 1, 1, 0, 89, 39, 0, 15, 39, 0, 122, 0, 0, 0, 121, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1956.0, 113.0, 850.0, 1106.0, 155.0, 695.0, 192.0, 914.0, 88.0, 607.0, 91.0, 101.0, 335.0, 579.0, 170.0, 437.0, 237.0, 98.0, 390.0, 189.0, 112.0, 325.0, 103.0, 134.0, 158.0, 232.0, 99.0, 90.0, 209.0, 116.0, 130.0, 102.0, 89.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00066244346, 0.0074132415, -0.01318557, 0.014199712, -0.010369266, -0.012087936, 0.03975871, -0.027651437, 0.011572138, -0.0060980776, 0.057432305, -0.007066077, -0.097530365, -0.0037272049, 0.078995764, -0.03795398, 0.016410436, -0.01754797, -0.0015250457, 0.03969028, 0.022482479, -0.057237897, 0.0046388768, -0.0058880295, 0.08868735, 0.0025243715, -0.09582252, 0.015694179, 0.020481285, -0.052650895, -0.016150242, 0.009229505, -0.0071849646, -0.0065442375, -0.004014363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, 13, 15, 17, -1, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.8455662, 1.4801348, 0.0, 1.242989, 0.0, 1.8141141, 1.6700425, 1.1694863, 0.0, 0.0, 1.6296382, 3.320296, 1.1865276, 0.0, 3.7257214, 0.8652759, 0.0, 0.0, 0.0, 2.4727912, 0.0, 1.3780322, 0.0, 0.0, 2.8989172, 0.0, 0.8364742, 1.4082459, 0.0, 0.028477281, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 19, 19, 21, 21, 24, 24, 26, 26, 27, 27, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, 14, 16, 18, -1, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01318557, 1.0, -0.010369266, 3.1538463, -1.0, 1.0, 0.011572138, -0.0060980776, -0.5, 1.0, 1.0, -0.0037272049, 1.0, 1.0, 0.016410436, -0.01754797, -0.0015250457, 1.0, 0.022482479, 1.0, 0.0046388768, -0.0058880295, 1.0, 0.0025243715, 1.0, 1.0, 0.020481285, 1.0, -0.016150242, 0.009229505, -0.0071849646, -0.0065442375, -0.004014363], "split_indices": [117, 43, 0, 71, 0, 1, 0, 0, 0, 0, 1, 73, 122, 0, 42, 62, 0, 0, 0, 39, 0, 137, 0, 0, 122, 0, 59, 12, 0, 109, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1963.0, 100.0, 1850.0, 113.0, 912.0, 938.0, 813.0, 99.0, 140.0, 798.0, 628.0, 185.0, 148.0, 650.0, 532.0, 96.0, 95.0, 90.0, 512.0, 138.0, 433.0, 99.0, 170.0, 342.0, 138.0, 295.0, 210.0, 132.0, 178.0, 117.0, 112.0, 98.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0030863928, 0.026639635, -0.026813064, -0.008050049, 0.103322096, -0.10033634, -0.0057916143, -0.045216538, 0.09209298, 0.030927291, -0.027144464, -0.021990616, 7.544676e-05, 0.014851262, -0.012473393, -0.009817672, -0.016952416, 0.022578394, -0.009507433, -0.012930495, 0.009875024, 0.09402092, -0.04243959, 0.033316735, -0.011815528, 0.025488926, 0.00026635712, -0.010397782, 0.012778477, -0.010761127, 0.08668948, -0.006950305, 0.010153162, 0.021675711, -0.003378296], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, -1, 23, -1, -1, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [1.4591627, 3.083045, 1.4111013, 2.970128, 9.699961, 2.4537446, 1.7432716, 2.5610027, 5.4048767, 0.0, 2.8423834, 0.0, 0.0, 2.7440965, 0.0, 2.1169047, 0.0, 0.0, 0.0, 0.0, 0.0, 3.732911, 1.1927059, 2.4370356, 0.0, 0.0, 0.0, 0.0, 1.3510076, 0.0, 3.6823483, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 13, 13, 15, 15, 21, 21, 22, 22, 23, 23, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, -1, 24, -1, -1, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [0.115384616, -0.1923077, 0.3846154, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.030927291, 1.0, -0.021990616, 7.544676e-05, 1.0, -0.012473393, -0.30769232, -0.016952416, 0.022578394, -0.009507433, -0.012930495, 0.009875024, 1.0, 1.0, 1.0, -0.011815528, 0.025488926, 0.00026635712, -0.010397782, 1.0, -0.010761127, 1.0, -0.006950305, 0.010153162, 0.021675711, -0.003378296], "split_indices": [1, 1, 1, 61, 69, 122, 64, 113, 105, 0, 12, 0, 0, 108, 0, 1, 0, 0, 0, 0, 0, 115, 23, 89, 0, 0, 0, 0, 122, 0, 126, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1159.0, 913.0, 798.0, 361.0, 203.0, 710.0, 582.0, 216.0, 140.0, 221.0, 93.0, 110.0, 605.0, 105.0, 453.0, 129.0, 126.0, 90.0, 122.0, 99.0, 254.0, 351.0, 324.0, 129.0, 92.0, 162.0, 166.0, 185.0, 89.0, 235.0, 96.0, 89.0, 113.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001404468, -0.0040085493, 0.011624196, -0.012615858, 0.07819813, -0.021653153, 0.010851304, 0.015871776, -0.00031872885, 0.008581827, -0.05762442, -0.0474417, 0.035355758, -0.024730412, -0.10605468, -0.011432608, 0.0015875495, -0.025744436, 0.11407662, -0.10761821, 0.044679683, -0.14330551, -0.0022926473, 0.0156516, -0.09939255, 0.016944064, 0.0003348549, -0.0054476643, -0.015221915, 0.010539258, -0.0016033183, -0.00893503, -0.020040372, -0.004601388, -0.014739195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2842617, 1.3960515, 0.0, 1.9550943, 1.225435, 1.8075757, 0.0, 0.0, 0.0, 1.354473, 1.2091365, 1.2366004, 2.938825, 2.6004694, 0.9506538, 0.0, 0.0, 4.6175594, 1.6368034, 0.48825312, 0.9067695, 0.65311766, 0.0, 0.0, 0.6277244, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.011624196, 5.0, 1.0, 1.0, 0.010851304, 0.015871776, -0.00031872885, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.011432608, 0.0015875495, 0.0, 1.0, 1.0, 1.0, 1.0, -0.0022926473, 0.0156516, 1.0, 0.016944064, 0.0003348549, -0.0054476643, -0.015221915, 0.010539258, -0.0016033183, -0.00893503, -0.020040372, -0.004601388, -0.014739195], "split_indices": [114, 125, 0, 0, 15, 106, 0, 0, 0, 124, 15, 97, 109, 39, 137, 0, 0, 0, 53, 124, 97, 71, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1973.0, 93.0, 1786.0, 187.0, 1662.0, 124.0, 94.0, 93.0, 903.0, 759.0, 292.0, 611.0, 452.0, 307.0, 142.0, 150.0, 344.0, 267.0, 206.0, 246.0, 212.0, 95.0, 99.0, 245.0, 178.0, 89.0, 94.0, 112.0, 123.0, 123.0, 109.0, 103.0, 116.0, 129.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0038648532, -0.018445801, 0.02639254, -0.0028825798, -0.08746174, 0.009494879, 0.0134464605, -0.015508638, 0.008083612, -0.0026402487, -0.015892878, -0.017586244, 0.09928328, 0.050254606, -0.03839329, 0.024896849, -0.090245835, -0.0068236697, 0.023715373, 0.0013708886, 0.008178421, -0.011350407, -0.014558141, 0.09646188, -0.051119577, -0.015124534, -0.0056357197, 0.0066985637, -0.034063235, 0.022414332, -0.00063060824, -0.009330561, 0.00035831644, -0.008120603, -0.004965513, 0.002410595, -0.0032152745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [1.038387, 1.1149263, 1.8772973, 0.8953103, 0.8334708, 2.1616666, 0.0, 1.1076571, 0.0, 0.0, 0.0, 2.1082876, 4.7577877, 0.21893144, 1.5826764, 2.344691, 0.52093124, 0.0, 0.0, 0.0, 0.0, 0.77574545, 0.0, 2.9129875, 0.48230785, 0.0, 0.0, 0.0, 0.46365052, 0.0, 0.0, 0.0, 0.0, 0.0, 0.16518793, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 21, 21, 23, 23, 24, 24, 28, 28, 34, 34], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 0.0134464605, 0.0, 0.008083612, -0.0026402487, -0.015892878, 1.0, -0.26923078, 1.0, 1.0, 1.0, 1.0, -0.0068236697, 0.023715373, 0.0013708886, 0.008178421, -0.1923077, -0.014558141, 1.0, 1.0, -0.015124534, -0.0056357197, 0.0066985637, 1.0, 0.022414332, -0.00063060824, -0.009330561, 0.00035831644, -0.008120603, 1.0, 0.002410595, -0.0032152745], "split_indices": [71, 58, 125, 121, 39, 0, 0, 0, 0, 0, 0, 97, 1, 115, 73, 121, 69, 0, 0, 0, 0, 1, 0, 126, 39, 0, 0, 0, 97, 0, 0, 0, 0, 0, 74, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1038.0, 1028.0, 847.0, 191.0, 889.0, 139.0, 736.0, 111.0, 103.0, 88.0, 683.0, 206.0, 190.0, 546.0, 431.0, 252.0, 93.0, 113.0, 88.0, 102.0, 436.0, 110.0, 222.0, 209.0, 90.0, 162.0, 98.0, 338.0, 99.0, 123.0, 118.0, 91.0, 129.0, 209.0, 101.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00022742391, 0.0051506273, -0.009536068, -0.0067369086, 0.05801715, -0.044162206, 0.019175952, -0.008172947, 0.1102888, 0.0044961087, -0.0917915, -0.0033874752, 0.01979805, 0.0020386025, 0.024471482, 0.005922248, -0.039284974, -0.053480983, -0.014033563, -0.013709721, 0.0146814035, -0.006887262, -0.00096972886, -0.0024335568, -0.008169619, 0.017629145, -0.011450199, -0.074829295, 0.042874735, -0.16339086, 0.007117757, -0.009131334, 0.12786053, -0.022035507, -0.010826417, 0.024150373, 0.0018465639], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, 27, 29, 31, 33, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [0.9703763, 1.232396, 0.0, 1.5526445, 2.629723, 1.5180029, 3.816584, 0.0, 3.1663404, 0.7762975, 0.615577, 2.0294278, 0.0, 0.0, 0.0, 0.0, 0.15757719, 0.15213329, 0.0, 0.0, 3.1251163, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1932328, 3.8015962, 3.9115999, 0.5746641, 0.0, 0.0, 2.610716, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 16, 16, 17, 17, 20, 20, 26, 26, 27, 27, 28, 28, 29, 29, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, 28, 30, 32, 34, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009536068, 1.0, -0.5, 1.0, 1.0, -0.008172947, 1.0, 1.0, 1.0, 1.0, 0.01979805, 0.0020386025, 0.024471482, 0.005922248, 1.0, 1.0, -0.014033563, -0.013709721, -0.42307693, -0.006887262, -0.00096972886, -0.0024335568, -0.008169619, 0.017629145, 1.0, 1.0, 1.0, 1.0, 0.007117757, -0.009131334, 1.0, -0.022035507, -0.010826417, 0.024150373, 0.0018465639], "split_indices": [117, 42, 0, 39, 1, 122, 88, 0, 105, 127, 111, 89, 0, 0, 0, 0, 124, 124, 0, 0, 1, 0, 0, 0, 0, 0, 122, 115, 81, 97, 0, 0, 108, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1961.0, 101.0, 1601.0, 360.0, 655.0, 946.0, 98.0, 262.0, 324.0, 331.0, 840.0, 106.0, 157.0, 105.0, 144.0, 180.0, 185.0, 146.0, 100.0, 740.0, 90.0, 90.0, 91.0, 94.0, 103.0, 637.0, 294.0, 343.0, 183.0, 111.0, 133.0, 210.0, 90.0, 93.0, 103.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022218344, 0.023431154, -0.021537798, 0.015782448, -0.008898425, -0.09465017, -0.0055846283, 0.09134337, -0.047021277, -0.018877527, 0.0034375216, -0.07087249, 0.03178338, -0.009609172, 0.024266714, -0.080430724, 0.011622939, -0.02335503, -0.018574476, -0.012037259, 0.06715431, -0.02453654, -0.0224824, -0.012000046, 0.003936847, 0.0032232816, 0.11922903, 0.032232348, -0.013071542, -0.009732794, 0.010743094, 0.00023327046, 0.021664262, -0.0023578801, 0.009509336], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, -1, 7, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0232326, 3.853908, 1.3739884, 0.0, 2.732375, 2.562496, 2.3591683, 5.587588, 2.8252313, 0.0, 0.0, 1.9213701, 3.3098674, 0.0, 0.0, 3.4704194, 0.0, 1.5094233, 0.0, 0.0, 1.6612659, 1.868574, 0.0, 0.0, 0.0, 2.3471186, 3.1315038, 0.708686, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 17, 17, 20, 20, 21, 21, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, -1, 8, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.015782448, -0.34615386, 1.0, 1.0, 1.0, 2.0, -0.018877527, 0.0034375216, 0.5769231, 1.0, -0.009609172, 0.024266714, 1.0, 0.011622939, -0.15384616, -0.018574476, -0.012037259, 1.0, 1.0, -0.0224824, -0.012000046, 0.003936847, 1.0, 1.0, 1.0, -0.013071542, -0.009732794, 0.010743094, 0.00023327046, 0.021664262, -0.0023578801, 0.009509336], "split_indices": [53, 81, 89, 0, 1, 97, 81, 93, 0, 0, 0, 1, 5, 0, 0, 15, 0, 1, 0, 0, 93, 61, 0, 0, 0, 13, 122, 127, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 887.0, 1178.0, 172.0, 715.0, 211.0, 967.0, 197.0, 518.0, 122.0, 89.0, 352.0, 615.0, 88.0, 109.0, 430.0, 88.0, 249.0, 103.0, 116.0, 499.0, 310.0, 120.0, 98.0, 151.0, 224.0, 275.0, 202.0, 108.0, 114.0, 110.0, 125.0, 150.0, 107.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00068548496, 0.0054119076, -0.009771458, -0.00017540192, 0.009922794, 0.009451763, -0.013511227, -0.00019908954, 0.016957715, 0.009589257, -0.009581375, -0.044163484, 0.025324067, -0.10965185, 0.008221755, 0.04127812, -0.0115649225, -0.020373277, -0.0026828444, 0.069207445, -0.0597455, 0.03768316, 0.12320343, -0.010301038, -0.0007913297, 0.012151346, -0.00084619486, 0.019475093, 0.0064824163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9538793, 1.025819, 0.0, 2.3993633, 0.0, 2.664178, 0.0, 1.521789, 0.0, 1.2475392, 0.0, 2.7643466, 2.566218, 1.7142627, 0.0, 2.8920593, 0.0, 0.0, 0.0, 1.366854, 0.4978376, 1.9612579, 1.2363586, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.009771458, 1.3461539, 0.009922794, 1.1538461, -0.013511227, 1.0, 0.016957715, 1.0, -0.009581375, 1.0, 0.6923077, -0.42307693, 0.008221755, 1.0, -0.0115649225, -0.020373277, -0.0026828444, 1.0, -0.15384616, -0.34615386, 1.0, -0.010301038, -0.0007913297, 0.012151346, -0.00084619486, 0.019475093, 0.0064824163], "split_indices": [117, 1, 0, 1, 0, 1, 0, 40, 0, 89, 0, 126, 1, 1, 0, 7, 0, 0, 0, 115, 1, 1, 16, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 1957.0, 94.0, 1847.0, 110.0, 1724.0, 123.0, 1626.0, 98.0, 1475.0, 151.0, 334.0, 1141.0, 220.0, 114.0, 1025.0, 116.0, 103.0, 117.0, 803.0, 222.0, 507.0, 296.0, 121.0, 101.0, 180.0, 327.0, 133.0, 163.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.00046743033, -0.020911539, 0.020114679, -0.0020250077, -0.105801515, 0.0053262888, 0.01140891, -0.01520751, 0.008713662, -0.015400792, -0.005552168, -0.014399078, 0.07266462, -0.027342068, 0.0067690643, 0.032616902, -0.058140974, -0.00953506, 0.020917702, 0.00521793, -0.056472007, -0.008172489, 0.09345934, -0.009427406, -0.013258352, 0.00075715734, -0.08690266, 0.01905444, -0.0012960823, 0.005603283, -0.0062341127, -0.01742299, -0.03433251, -0.01025556, 0.002325026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, -1, 21, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.8756492, 1.6738211, 1.4411504, 1.0037686, 0.46052313, 1.1901331, 0.0, 0.74841356, 0.0, 0.0, 0.0, 1.4252018, 4.656041, 1.5033777, 0.0, 2.3235822, 1.3018634, 0.0, 0.0, 0.0, 0.9257219, 0.0, 2.252335, 0.75163263, 0.0, 0.0, 1.4782393, 0.0, 0.0, 0.0, 0.0, 0.0, 0.78962374, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 20, 20, 22, 22, 23, 23, 26, 26, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, -1, 22, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.115384616, 2.0, 0.01140891, 1.0, 0.008713662, -0.015400792, -0.005552168, 1.0, -0.26923078, 0.0, 0.0067690643, 1.0, 1.0, -0.00953506, 0.020917702, 0.00521793, 1.0, -0.008172489, 1.0, -0.23076923, -0.013258352, 0.00075715734, 1.0, 0.01905444, -0.0012960823, 0.005603283, -0.0062341127, -0.01742299, 1.0, -0.01025556, 0.002325026], "split_indices": [71, 58, 125, 121, 1, 0, 0, 42, 0, 0, 0, 13, 1, 0, 0, 69, 39, 0, 0, 0, 111, 0, 105, 1, 0, 0, 97, 0, 0, 0, 0, 0, 69, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2081.0, 1044.0, 1037.0, 854.0, 190.0, 896.0, 141.0, 744.0, 110.0, 97.0, 93.0, 693.0, 203.0, 649.0, 95.0, 334.0, 359.0, 91.0, 112.0, 174.0, 475.0, 116.0, 218.0, 217.0, 142.0, 153.0, 322.0, 114.0, 104.0, 97.0, 120.0, 121.0, 201.0, 92.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.007437526, -0.012063466, 0.008613042, -0.021874465, 0.041237596, -0.05088045, 0.010346193, 0.0141178435, -0.0059360727, 0.012432954, -0.07129545, 0.0123390965, -0.021372728, -0.10343207, 0.0052460455, 0.032784812, -0.15372403, -0.020759054, -0.08243888, 0.09296252, -0.006665516, -0.012969977, -0.017829426, -0.13365178, -0.03577823, 0.16116372, -0.0041940915, -0.02054274, -0.0028859314, -0.008780199, 0.0030235096, 0.0031497683, 0.02967236], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, -1, 19, 21, -1, 23, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8912168, 1.0260017, 0.0, 1.548619, 3.066434, 3.119067, 2.814742, 0.0, 0.0, 0.0, 3.1061122, 0.0, 4.393874, 1.3557024, 0.0, 2.6030707, 0.10506916, 0.0, 1.233047, 2.493356, 0.0, 0.0, 0.0, 1.8502994, 0.927251, 3.1639524, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 10, 10, 12, 12, 13, 13, 15, 15, 16, 16, 18, 18, 19, 19, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, -1, 20, 22, -1, 24, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2692307, 0.008613042, 1.0, 1.0, 1.0, 1.0, 0.0141178435, -0.0059360727, 0.012432954, 1.0, 0.0123390965, 1.0, -1.0, 0.0052460455, 1.0, -0.23076923, -0.020759054, 1.0, 1.0, -0.006665516, -0.012969977, -0.017829426, 0.115384616, 1.0, 1.0, -0.0041940915, -0.02054274, -0.0028859314, -0.008780199, 0.0030235096, 0.0031497683, 0.02967236], "split_indices": [114, 1, 0, 124, 115, 104, 81, 0, 0, 0, 62, 0, 0, 0, 0, 97, 1, 0, 13, 106, 0, 0, 0, 1, 106, 109, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1962.0, 97.0, 1657.0, 305.0, 872.0, 785.0, 153.0, 152.0, 91.0, 781.0, 172.0, 613.0, 620.0, 161.0, 435.0, 178.0, 104.0, 516.0, 271.0, 164.0, 90.0, 88.0, 246.0, 270.0, 180.0, 91.0, 146.0, 100.0, 151.0, 119.0, 92.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0011062835, -0.018910687, 0.021318546, 0.009444199, -0.06778556, 0.046381555, -0.040115353, -0.03887136, 0.0652296, -0.10064948, 0.0033633155, 0.12040382, -0.006094318, -0.018555384, 0.03371127, 0.0037082743, -0.071290806, 0.0010537776, 0.012441664, -0.016242186, -0.0060532917, 0.029909838, 0.033356737, 0.07339708, -0.07777853, -0.007634286, 0.014949742, -0.000165209, -0.011217545, 0.008164684, -0.0042992258, 0.017619995, -0.0035699818, 0.0007630118, -0.019375453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8334518, 1.4343462, 1.5782114, 1.7654235, 1.2665464, 2.8278315, 3.1889582, 0.86429906, 0.98406184, 0.71121264, 0.0, 5.8255854, 2.4274669, 0.0, 2.510321, 0.0, 0.70039964, 0.0, 0.0, 0.0, 0.0, 0.79960805, 0.0, 2.2655263, 2.2187996, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 16, 16, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.15384616, 0.3846154, 1.0, 1.0, 1.0, -0.30769232, 1.0, 1.0, 0.0033633155, 1.0, 1.0, -0.018555384, 1.0, 0.0037082743, 1.0, 0.0010537776, 0.012441664, -0.016242186, -0.0060532917, 1.0, 0.033356737, 1.0, 1.0, -0.007634286, 0.014949742, -0.000165209, -0.011217545, 0.008164684, -0.0042992258, 0.017619995, -0.0035699818, 0.0007630118, -0.019375453], "split_indices": [71, 109, 1, 1, 42, 69, 69, 1, 13, 124, 0, 0, 121, 0, 39, 0, 106, 0, 0, 0, 0, 108, 0, 61, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1035.0, 1025.0, 655.0, 380.0, 728.0, 297.0, 351.0, 304.0, 287.0, 93.0, 302.0, 426.0, 100.0, 197.0, 105.0, 246.0, 158.0, 146.0, 113.0, 174.0, 212.0, 90.0, 202.0, 224.0, 101.0, 96.0, 91.0, 155.0, 124.0, 88.0, 104.0, 98.0, 129.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.002438226, -0.02843169, 0.0135765085, -0.006000758, -0.013225483, 0.13327314, -0.0065368945, -0.10578203, 0.036951438, 0.0060474677, 0.0204506, 0.009711106, -0.009433313, -0.0019713796, -0.019273754, -0.044768296, 0.11618406, -0.018048102, 0.11084391, 0.0003043445, -0.011811589, 0.027363598, -0.004684134, 0.0049285945, -0.011666344, 0.023296472, 0.0005842824, 0.03626569, -0.04562857, -0.0015605369, 0.01357487, -0.010162359, 0.003993681, 0.004873753, -0.0072623887], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, 17, -1, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [0.8604475, 1.8351331, 3.0792012, 2.777215, 0.0, 0.9541576, 1.5620323, 1.4594002, 2.933116, 0.0, 0.0, 2.5940073, 0.0, 0.0, 0.0, 0.78203374, 5.9037924, 1.6427449, 2.55174, 0.0, 0.0, 0.0, 0.0, 0.931577, 0.0, 0.0, 0.0, 1.3659936, 1.0780282, 0.940054, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, 18, -1, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.1923077, -0.26923078, -0.03846154, 1.0, -0.013225483, -0.115384616, 1.0, 1.0, 1.0, 0.0060474677, 0.0204506, 1.0, -0.009433313, -0.0019713796, -0.019273754, 1.0, 1.0, 1.0, 1.0, 0.0003043445, -0.011811589, 0.027363598, -0.004684134, 1.0, -0.011666344, 0.023296472, 0.0005842824, 1.3846154, 1.0, 1.0, 0.01357487, -0.010162359, 0.003993681, 0.004873753, -0.0072623887], "split_indices": [1, 1, 1, 89, 0, 1, 64, 108, 69, 0, 0, 0, 0, 0, 0, 126, 53, 15, 124, 0, 0, 0, 0, 109, 0, 0, 0, 1, 93, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 788.0, 1279.0, 648.0, 140.0, 184.0, 1095.0, 195.0, 453.0, 91.0, 93.0, 924.0, 171.0, 98.0, 97.0, 223.0, 230.0, 725.0, 199.0, 135.0, 88.0, 117.0, 113.0, 588.0, 137.0, 92.0, 107.0, 363.0, 225.0, 263.0, 100.0, 136.0, 89.0, 154.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0076691993, -0.00317443, -0.0098862015, -0.009151138, 0.05583317, -0.011186399, -0.0022831734, 0.014788329, -0.003931972, -0.018935405, 0.062860735, -0.034072947, 0.014950412, 0.023322575, -0.018265443, -0.005623088, -0.16390774, -0.012144756, 0.009722277, 0.03527498, -0.0376587, -0.03133082, -0.0062406627, -0.008130073, 0.09121568, 0.016344646, -0.098387, 0.024424464, 0.0016774341, 0.011078084, -0.006510655, -0.01514026, -0.00577417, -0.006662573, 0.006854267], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, 13, 15, -1, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8464239, 0.69405687, 0.0, 1.2606001, 1.5853493, 0.0, 1.8170257, 0.0, 0.0, 3.4013803, 4.712983, 4.5211883, 0.0, 0.0, 2.7526696, 1.3154354, 3.3361487, 0.0, 0.0, 2.8759048, 1.8463767, 0.0, 0.0, 0.0, 4.0831785, 2.2921987, 0.5710304, 0.0, 0.8586162, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 24, 24, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, 14, 16, -1, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0098862015, -0.53846157, 1.0, -0.011186399, 1.0, 0.014788329, -0.003931972, 5.0, -0.23076923, 1.0, 0.014950412, 0.023322575, 1.0, 1.0, -0.07692308, -0.012144756, 0.009722277, 1.0, 1.0, -0.03133082, -0.0062406627, -0.008130073, 0.0, 1.0, 1.0, 0.024424464, 1.1538461, 0.011078084, -0.006510655, -0.01514026, -0.00577417, -0.006662573, 0.006854267], "split_indices": [117, 125, 0, 1, 15, 0, 61, 0, 0, 0, 1, 0, 0, 0, 12, 106, 1, 0, 0, 111, 12, 0, 0, 0, 1, 15, 39, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1968.0, 97.0, 1787.0, 181.0, 112.0, 1675.0, 92.0, 89.0, 1334.0, 341.0, 1224.0, 110.0, 110.0, 231.0, 1004.0, 220.0, 122.0, 109.0, 441.0, 563.0, 89.0, 131.0, 143.0, 298.0, 298.0, 265.0, 110.0, 188.0, 138.0, 160.0, 115.0, 150.0, 93.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00039468895, 0.004739739, -0.008617507, 0.030352252, -0.014937025, 0.0159308, -0.0008405773, -0.0024298022, -0.012482188, -0.05843224, 0.02241071, -0.018282816, 0.00817131, 0.0035127487, -0.015895922, -0.011322279, 0.01438495, 0.027302783, -0.03811295, 0.006436883, -0.07318522, -0.0072401376, 0.013614652, -0.07888829, 0.015221178, -0.01100457, -0.002389983, -0.014350528, -0.028669631, 0.00596863, -0.0027159618, 0.004529264, -0.010935578], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.9046501, 0.97669643, 0.0, 3.386941, 1.5062919, 0.0, 0.9078965, 1.3125757, 0.0, 1.8340294, 1.9786067, 0.748486, 0.0, 0.0, 0.0, 1.7699754, 0.0, 2.7238953, 1.2548121, 0.0, 0.3778695, 0.0, 0.0, 1.0611074, 0.4711169, 0.0, 0.0, 0.0, 1.0980626, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 18, 18, 20, 20, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008617507, 1.0, 4.0, 0.0159308, 1.0, 1.0, -0.012482188, 1.0, 1.0, 0.0, 0.00817131, 0.0035127487, -0.015895922, 1.0, 0.01438495, 1.0, 1.0, 0.006436883, 1.0, -0.0072401376, 0.013614652, 1.0, 1.0, -0.01100457, -0.002389983, -0.014350528, 1.0, 0.00596863, -0.0027159618, 0.004529264, -0.010935578], "split_indices": [43, 53, 0, 81, 0, 0, 2, 61, 0, 17, 88, 0, 0, 0, 0, 121, 0, 111, 93, 0, 119, 0, 0, 106, 111, 0, 0, 0, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1938.0, 116.0, 842.0, 1096.0, 164.0, 678.0, 984.0, 112.0, 195.0, 483.0, 828.0, 156.0, 101.0, 94.0, 378.0, 105.0, 251.0, 577.0, 170.0, 208.0, 131.0, 120.0, 327.0, 250.0, 119.0, 89.0, 143.0, 184.0, 122.0, 128.0, 96.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0026084927, 0.008347134, -0.047848225, 0.001020216, 0.074843846, -0.00080759585, -0.0078340275, -0.011684644, 0.008723432, 0.0149650965, -0.00024294014, 0.015951253, -0.010922504, 0.0055428916, 0.014232531, 0.013519604, -0.010808091, 0.021290474, -0.00617283, -0.029337043, 0.042581268, 0.046715796, -0.01677283, 0.024073297, 0.011146432, -0.0036987625, 0.012459639, -0.004248328, 0.003478729], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, -1, -1, 13, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1], "loss_changes": [0.60111195, 0.90817034, 0.25710014, 1.5244515, 1.0694095, 0.0, 0.0, 0.0, 1.343556, 0.0, 0.0, 1.9532894, 0.0, 1.243504, 0.0, 0.74963874, 0.0, 1.25252, 0.0, 3.620617, 5.095212, 1.44719, 0.0, 0.0, 0.8951038, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 24, 24], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, -1, -1, 14, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.53846157, 1.0, -0.00080759585, -0.0078340275, -0.011684644, 1.0, 0.0149650965, -0.00024294014, 5.0, -0.010922504, 3.0, 0.014232531, 1.0, -0.010808091, -0.1923077, -0.00617283, -0.30769232, -0.03846154, 1.0, -0.01677283, 0.024073297, 1.0, -0.0036987625, 0.012459639, -0.004248328, 0.003478729], "split_indices": [40, 125, 108, 1, 15, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 41, 0, 1, 0, 1, 1, 109, 0, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1864.0, 212.0, 1679.0, 185.0, 92.0, 120.0, 103.0, 1576.0, 94.0, 91.0, 1485.0, 91.0, 1372.0, 113.0, 1282.0, 90.0, 1162.0, 120.0, 344.0, 818.0, 222.0, 122.0, 112.0, 706.0, 107.0, 115.0, 216.0, 490.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.007120932, 0.011964053, -0.0073721902, 0.0073428354, 0.009329745, 0.01520515, -0.010689193, 0.0086942995, 0.012845235, 0.0014168364, 0.008074116, -0.054328363, 0.012484893, 0.0012074634, -0.009469099, 0.014901376, 0.0014053226, -0.04628423, 0.0156095745, -0.015267094, 0.0037667172, 0.065435894, -0.038329758, 0.0131256385, -0.0016117294, 0.0013310144, -0.0111095095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [0.8104702, 0.7340529, 0.0, 1.6597803, 0.0, 1.2748529, 0.0, 0.85726017, 0.0, 0.9162316, 0.0, 0.6593294, 1.8742117, 0.0, 0.0, 0.0, 0.77629393, 2.3489356, 2.3731494, 0.0, 0.0, 2.4638534, 1.5932206, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.0073721902, 1.3461539, 0.009329745, 1.1538461, -0.010689193, 1.0, 0.012845235, -1.0, 0.008074116, -0.30769232, 0.0, 0.0012074634, -0.009469099, 0.014901376, 1.0, -0.34615386, 1.0, -0.015267094, 0.0037667172, 1.0, 1.0, 0.0131256385, -0.0016117294, 0.0013310144, -0.0111095095], "split_indices": [43, 1, 0, 1, 0, 1, 0, 44, 0, 0, 0, 1, 0, 0, 0, 0, 89, 1, 111, 0, 0, 97, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1953.0, 117.0, 1848.0, 105.0, 1729.0, 119.0, 1635.0, 94.0, 1485.0, 150.0, 246.0, 1239.0, 93.0, 153.0, 93.0, 1146.0, 263.0, 883.0, 116.0, 147.0, 459.0, 424.0, 254.0, 205.0, 248.0, 176.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0041320357, -0.000893902, 0.008931078, -0.005908297, 0.009806035, -0.017967965, 0.037405014, -0.0069676163, -0.017498346, -0.0069544995, 0.016703818, -0.021210687, 0.018646244, -0.010523821, 0.006878175, -0.11981366, -0.0033164115, -0.00209673, -0.022940416, 0.010627553, -0.009978801, 0.07052883, -0.0117473295, 0.017090661, -0.00049275643, -0.08806955, 0.029882966, -0.0014312968, -0.018785784, 0.007917935, -0.0058101695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, -1, -1, -1, 17, 19, -1, -1, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.883605, 0.96708506, 0.0, 0.9689484, 0.0, 2.5062037, 2.3231878, 3.735831, 0.0, 2.2405355, 0.0, 2.2284732, 0.0, 0.0, 0.0, 2.101528, 1.4380155, 0.0, 0.0, 1.2518253, 0.0, 1.9238335, 2.1605756, 0.0, 0.0, 1.7664096, 1.9084233, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 16, 16, 19, 19, 21, 21, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, -1, -1, -1, 18, 20, -1, -1, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.008931078, 1.0, 0.009806035, 1.0, 1.0, 5.0, -0.017498346, 1.0, 0.016703818, -0.3846154, 0.018646244, -0.010523821, 0.006878175, 1.0, 1.0, -0.00209673, -0.022940416, -0.03846154, -0.009978801, 1.0, 1.0, 0.017090661, -0.00049275643, 1.0, 1.0, -0.0014312968, -0.018785784, 0.007917935, -0.0058101695], "split_indices": [102, 114, 0, 113, 0, 84, 42, 0, 0, 15, 0, 1, 0, 0, 0, 121, 42, 0, 0, 1, 0, 122, 69, 0, 0, 127, 109, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1949.0, 115.0, 1855.0, 94.0, 1451.0, 404.0, 1356.0, 95.0, 301.0, 103.0, 1263.0, 93.0, 131.0, 170.0, 194.0, 1069.0, 102.0, 92.0, 934.0, 135.0, 254.0, 680.0, 109.0, 145.0, 240.0, 440.0, 138.0, 102.0, 282.0, 158.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017772159, -0.010001801, 0.04642267, -0.015581894, 0.009258601, 0.015337157, -0.0059817946, -0.022398923, 0.008223305, -0.0992556, -0.0071453727, -0.015098137, -0.0059221247, -0.044004943, 0.03308117, -0.099504, 0.005774149, 0.06946657, -0.037127256, -0.05964681, -0.019764693, -0.056169726, 0.014062545, 0.15691367, -0.011033784, -0.012353163, 0.008095869, -0.014074157, 0.0031209327, -0.012306858, 0.0018803104, 0.0042535216, 0.029858693, -0.0075974665, 0.0065834983], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, -1, -1, 15, 17, 19, 21, 23, 25, 27, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8186156, 1.0098007, 3.4200578, 1.1155686, 0.0, 0.0, 0.0, 1.833535, 0.0, 0.5363383, 1.9349667, 0.0, 0.0, 1.8813939, 1.5940466, 1.259567, 2.9988034, 2.8932445, 2.1732693, 1.6872624, 0.0, 1.233837, 0.0, 3.1922598, 1.0682722, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, -1, -1, 16, 18, 20, 22, 24, 26, 28, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 0.009258601, 0.015337157, -0.0059817946, -1.0, 0.008223305, -0.23076923, 1.0, -0.015098137, -0.0059221247, 1.0, 1.0, 1.0, 1.0, 0.0, -0.07692308, 1.0, -0.019764693, 0.03846154, 0.014062545, 1.0, 1.0, -0.012353163, 0.008095869, -0.014074157, 0.0031209327, -0.012306858, 0.0018803104, 0.0042535216, 0.029858693, -0.0075974665, 0.0065834983], "split_indices": [1, 114, 115, 102, 0, 0, 0, 0, 0, 1, 124, 0, 0, 106, 0, 0, 0, 1, 1, 15, 0, 1, 0, 12, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1764.0, 301.0, 1673.0, 91.0, 150.0, 151.0, 1564.0, 109.0, 259.0, 1305.0, 113.0, 146.0, 681.0, 624.0, 322.0, 359.0, 411.0, 213.0, 229.0, 93.0, 246.0, 113.0, 197.0, 214.0, 123.0, 90.0, 121.0, 108.0, 130.0, 116.0, 109.0, 88.0, 116.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.003228234, -0.00069916697, 0.007261234, -0.0048638466, 0.008020838, -0.015356168, 0.026456175, -0.028085563, 0.015524394, -0.05529528, 0.10023189, -0.008819612, -0.11449781, 0.003633745, -0.012258809, 0.015225599, 0.002031857, 0.028091311, -0.04002729, -0.018701859, -0.004728341, -0.0058844835, 0.07598926, -0.07612543, 0.022886591, 0.019426681, 0.021526558, -0.0003407434, -0.14059703, 0.013659204, -0.008450187, -0.008675221, 0.012682522, -0.015222775, -0.012920365], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, -1, 25, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.56461865, 0.66076654, 0.0, 0.6128758, 0.0, 3.0337756, 2.8226357, 2.164258, 0.0, 1.3689022, 1.0227244, 1.2244742, 1.1552422, 0.0, 0.0, 0.0, 0.0, 2.0278988, 1.3081388, 0.0, 0.0, 0.0, 2.0226977, 1.7158973, 2.5642376, 0.0, 2.451346, 0.0, 0.025706768, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 18, 18, 22, 22, 23, 23, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, -1, 26, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.007261234, 1.0, 0.008020838, 5.0, 1.0, 1.0, 0.015524394, 1.0, 1.0, 1.0, 1.0, 0.003633745, -0.012258809, 0.015225599, 0.002031857, 1.0, 1.0, -0.018701859, -0.004728341, -0.0058844835, 0.0, 1.0, 1.0, 0.019426681, 1.0, -0.0003407434, 1.0, 0.013659204, -0.008450187, -0.008675221, 0.012682522, -0.015222775, -0.012920365], "split_indices": [102, 114, 0, 61, 0, 0, 17, 0, 0, 26, 105, 106, 13, 0, 0, 0, 0, 111, 83, 0, 0, 0, 0, 12, 13, 0, 97, 0, 71, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1961.0, 111.0, 1865.0, 96.0, 1397.0, 468.0, 1300.0, 97.0, 222.0, 246.0, 1063.0, 237.0, 94.0, 128.0, 149.0, 97.0, 487.0, 576.0, 114.0, 123.0, 173.0, 314.0, 366.0, 210.0, 99.0, 215.0, 172.0, 194.0, 102.0, 108.0, 106.0, 109.0, 96.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0009036402, 0.008135395, -0.05311313, -0.001730756, 0.100275226, 0.0017831561, -0.016322521, 0.009021311, -0.010564431, 0.013449085, 0.006567076, 0.00025494464, 0.011346196, 0.027938632, -0.026175728, -0.023333305, 0.095596455, 0.042018045, -0.06656979, 0.027905764, -0.011163896, 0.20928112, -0.009422562, 0.012554081, -0.007058851, -0.14397925, 0.010145392, 0.007600967, -0.0055330168, 0.0069498555, 0.03460249, -0.0015522664, -0.024263388, 0.010945078, -0.009946524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8105696, 1.6635902, 1.913908, 1.846873, 0.2095697, 0.0, 0.0, 1.3715161, 0.0, 0.0, 0.0, 1.0112072, 0.0, 2.3415399, 1.9475191, 1.7374848, 6.27974, 2.4735703, 2.6366854, 0.97296566, 0.0, 3.4788198, 0.0, 0.0, 0.0, 2.8006964, 2.4273386, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 0.88461536, 1.0, 0.5769231, 1.0, 0.0017831561, -0.016322521, 1.0, -0.010564431, 0.013449085, 0.006567076, 1.0, 0.011346196, -0.1923077, -0.30769232, 1.0, 0.23076923, 1.0, 1.0, 1.0, -0.011163896, 1.0, -0.009422562, 0.012554081, -0.007058851, 1.0, 1.0, 0.007600967, -0.0055330168, 0.0069498555, 0.03460249, -0.0015522664, -0.024263388, 0.010945078, -0.009946524], "split_indices": [1, 1, 17, 1, 97, 0, 0, 44, 0, 0, 0, 69, 0, 1, 1, 126, 1, 97, 59, 105, 0, 71, 0, 0, 0, 93, 97, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1830.0, 245.0, 1653.0, 177.0, 149.0, 96.0, 1498.0, 155.0, 89.0, 88.0, 1382.0, 116.0, 675.0, 707.0, 384.0, 291.0, 263.0, 444.0, 243.0, 141.0, 182.0, 109.0, 151.0, 112.0, 221.0, 223.0, 154.0, 89.0, 90.0, 92.0, 96.0, 125.0, 117.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0054314584, 0.0027763231, -0.05991781, -0.004646015, 0.013160693, -0.018446734, 0.009917062, -0.066427894, 0.009669841, 0.0010722145, -0.013365221, 0.023432482, -0.07820497, -0.030711483, 0.057726134, -0.000794065, -0.014339863, -0.07003586, 0.02285793, 0.020582337, 0.018316982, 0.00051427993, -0.013621427, -0.0034791355, 0.008541357, 0.07108381, -0.024496147, 0.016451498, -0.0042590755, 0.03647808, -0.012575695, 0.009393345, -0.002222628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, 13, 15, 17, 19, -1, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.9257293, 1.720248, 5.3696995, 1.5044675, 0.0, 0.0, 0.0, 1.6596352, 1.6701665, 0.0, 0.0, 2.2170124, 0.85660696, 0.9753485, 3.4060612, 0.0, 0.0, 1.32838, 0.7068325, 1.2839628, 0.0, 0.0, 0.0, 0.0, 0.0, 2.8251195, 1.8399413, 0.0, 0.0, 0.62735593, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, 14, 16, 18, 20, -1, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, -1.0, 0.013160693, -0.018446734, 0.009917062, 1.0, 1.3461539, 0.0010722145, -0.013365221, 1.0, 1.0, 1.0, 1.0, -0.000794065, -0.014339863, 1.0, 1.0, 1.0, 0.018316982, 0.00051427993, -0.013621427, -0.0034791355, 0.008541357, -0.03846154, 1.0, 0.016451498, -0.0042590755, 1.0, -0.012575695, 0.009393345, -0.002222628], "split_indices": [0, 102, 122, 0, 0, 0, 0, 127, 1, 0, 0, 17, 115, 111, 61, 0, 0, 2, 127, 106, 0, 0, 0, 0, 0, 1, 71, 0, 0, 74, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1799.0, 271.0, 1701.0, 98.0, 152.0, 119.0, 320.0, 1381.0, 149.0, 171.0, 1194.0, 187.0, 463.0, 731.0, 90.0, 97.0, 267.0, 196.0, 564.0, 167.0, 125.0, 142.0, 102.0, 94.0, 266.0, 298.0, 146.0, 120.0, 186.0, 112.0, 94.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0013147538, 0.0070649018, -0.049858794, 0.014156686, -0.05050369, 4.3740336e-05, -0.008864657, 0.0037075304, 0.0109865805, 0.0023489732, -0.012898159, -0.03377194, 0.021141555, -0.09836172, 0.07363577, 0.03419719, -0.010295421, -0.016033, -0.00074748916, 0.017736504, -0.0032450948, 0.05060736, -0.005302399, 0.07609299, -0.0061246194, 0.12854637, 0.007998947, 0.18688886, 0.0016165314, 0.010376396, -0.013349167, 0.0010252869, 0.029652497], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, 23, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [0.6088145, 0.7593711, 0.40773362, 1.6561313, 1.1845975, 0.0, 0.0, 0.97555304, 0.0, 0.0, 0.0, 3.2883472, 1.6509314, 1.6671011, 1.9587655, 1.319672, 0.0, 0.0, 0.0, 0.0, 0.0, 2.2121108, 0.0, 2.2573535, 0.0, 2.3407025, 3.726209, 4.550934, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 21, 21, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, 24, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 0.88461536, 1.0, 4.3740336e-05, -0.008864657, 1.0, 0.0109865805, 0.0023489732, -0.012898159, 1.0, 1.0, 1.0, 0.03846154, 1.0, -0.010295421, -0.016033, -0.00074748916, 0.017736504, -0.0032450948, 3.0, -0.005302399, 1.0, -0.0061246194, -0.03846154, 1.0, 1.0, 0.0016165314, 0.010376396, -0.013349167, 0.0010252869, 0.029652497], "split_indices": [40, 1, 108, 1, 12, 0, 0, 17, 0, 0, 0, 116, 90, 122, 1, 80, 0, 0, 0, 0, 0, 0, 0, 122, 0, 1, 108, 15, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1860.0, 209.0, 1656.0, 204.0, 91.0, 118.0, 1493.0, 163.0, 105.0, 99.0, 474.0, 1019.0, 296.0, 178.0, 922.0, 97.0, 176.0, 120.0, 90.0, 88.0, 776.0, 146.0, 632.0, 144.0, 357.0, 275.0, 235.0, 122.0, 164.0, 111.0, 90.0, 145.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.008132128, -0.0040189154, -0.007679112, -0.009150119, 0.008450629, -0.0029306775, -0.009627283, 0.0077120084, -0.052213974, -0.004190214, 0.0129662575, -0.0085867895, -0.0018997092, 0.006300927, -0.013778488, 0.04297585, -0.027039906, -0.0066801584, 0.10744858, 0.008857373, -0.016295634, -0.008546747, 0.0053925454, 0.023537446, -0.0035784633, -0.02501724, 0.014300086, -0.008649849, 0.0063789], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [0.5845865, 0.88713205, 0.0, 1.0002632, 0.0, 0.9037251, 0.0, 2.0567515, 0.34207064, 1.809415, 0.0, 0.0, 0.0, 1.4636588, 0.0, 1.8248317, 3.0591526, 1.5375352, 4.5441623, 2.2538533, 0.0, 0.0, 0.0, 0.0, 0.0, 2.162128, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.007679112, 1.3461539, 0.008450629, 1.0, -0.009627283, 0.84615386, 1.0, 0.5769231, 0.0129662575, -0.0085867895, -0.0018997092, 1.0, -0.013778488, -0.1923077, 2.0, 1.0, 0.15384616, 1.0, -0.016295634, -0.008546747, 0.0053925454, 0.023537446, -0.0035784633, 1.0, 0.014300086, -0.008649849, 0.0063789], "split_indices": [43, 1, 0, 1, 0, 80, 0, 1, 106, 1, 0, 0, 0, 69, 0, 1, 0, 13, 1, 116, 0, 0, 0, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1953.0, 117.0, 1846.0, 107.0, 1723.0, 123.0, 1417.0, 306.0, 1291.0, 126.0, 152.0, 154.0, 1197.0, 94.0, 570.0, 627.0, 322.0, 248.0, 496.0, 131.0, 140.0, 182.0, 131.0, 117.0, 396.0, 100.0, 234.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00037246023, 0.0056186547, -0.009059278, 0.0016555937, 0.008716961, -0.022061571, 0.020749534, 4.4330413e-06, -0.01360144, -0.006165949, 0.11656866, -0.022778893, 0.009757275, 0.017094623, -0.08416473, -0.00061889965, 0.025438154, -0.04898126, 0.010739667, 0.012235794, -0.0037536405, -0.014935993, -0.0023087093, -0.0155607825, -0.0141458735, -0.0685339, 0.037041515, 0.049861412, -0.0137893325, -0.0010052418, -0.011489757, -0.005547366, 0.07503883, 0.014500332, -0.0038937682, 0.016087597, -0.0020515688], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, -1, -1, 25, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [0.9802124, 0.62763745, 0.0, 0.83868587, 0.0, 2.076964, 2.646073, 1.5382683, 0.0, 1.4532516, 3.8064578, 1.9135194, 0.0, 1.3540423, 0.73268175, 0.0, 0.0, 1.7346137, 0.0, 0.0, 1.3610013, 0.0, 0.0, 0.0, 2.7881005, 0.53957146, 1.1108439, 1.960056, 0.0, 0.0, 0.0, 0.0, 1.8372769, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 20, 20, 24, 24, 25, 25, 26, 26, 27, 27, 32, 32], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, -1, -1, 26, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009059278, -0.07692308, 0.008716961, 3.0, 1.0, 1.0, -0.01360144, 1.0, 1.0, 1.0, 0.009757275, 1.0, 0.7692308, -0.00061889965, 0.025438154, 1.0, 0.010739667, 0.012235794, 1.0, -0.014935993, -0.0023087093, -0.0155607825, 1.0, 0.5, 0.1923077, 1.0, -0.0137893325, -0.0010052418, -0.011489757, -0.005547366, 1.0, 0.014500332, -0.0038937682, 0.016087597, -0.0020515688], "split_indices": [43, 114, 0, 1, 0, 0, 0, 64, 0, 61, 71, 23, 0, 5, 1, 0, 0, 89, 0, 0, 81, 0, 0, 0, 121, 1, 1, 122, 0, 0, 0, 0, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1942.0, 112.0, 1852.0, 90.0, 826.0, 1026.0, 692.0, 134.0, 801.0, 225.0, 561.0, 131.0, 617.0, 184.0, 119.0, 106.0, 467.0, 94.0, 102.0, 515.0, 89.0, 95.0, 115.0, 352.0, 199.0, 316.0, 232.0, 120.0, 88.0, 111.0, 92.0, 224.0, 112.0, 120.0, 118.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042698574, -0.008114325, 0.006395194, -0.0016772817, -0.0096867485, -0.008333532, 0.01293298, 0.0017964223, -0.08018995, -0.0070134853, 0.011239226, -0.01107341, -0.0036456266, 0.010221141, -0.04338923, -0.02055147, 0.042718086, -0.12630415, 0.026043579, 0.011116712, -0.010733682, -0.036780883, 0.14141972, -0.017805547, -0.008371233, 0.008614577, -0.007114291, -0.04504547, 0.008176099, -0.010365148, 0.0010920123, 0.02709867, 0.0002783014, -0.01693752, 0.0056678825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.540814, 1.1151927, 0.0, 1.5870688, 0.0, 1.260727, 0.0, 1.4790471, 0.28586233, 0.881453, 0.0, 0.0, 0.0, 0.95401514, 2.6021717, 1.346684, 3.640859, 0.4540608, 1.436916, 1.4243459, 0.0, 0.8197775, 3.7182865, 0.0, 0.0, 0.0, 0.0, 2.5294712, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 1.3461539, 0.006395194, 1.1923077, -0.0096867485, 1.0, 0.01293298, 1.0, 1.0, 1.0, 0.011239226, -0.01107341, -0.0036456266, 1.0, 1.0, 1.0, 1.0, 1.0, 0.07692308, 1.0, -0.010733682, -0.07692308, 1.0, -0.017805547, -0.008371233, 0.008614577, -0.007114291, 1.0, 0.008176099, -0.010365148, 0.0010920123, 0.02709867, 0.0002783014, -0.01693752, 0.0056678825], "split_indices": [1, 1, 0, 1, 0, 119, 0, 125, 58, 50, 0, 0, 0, 97, 109, 62, 2, 13, 1, 115, 0, 1, 69, 0, 0, 0, 0, 124, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1952.0, 110.0, 1820.0, 132.0, 1732.0, 88.0, 1518.0, 214.0, 1406.0, 112.0, 126.0, 88.0, 954.0, 452.0, 490.0, 464.0, 206.0, 246.0, 359.0, 131.0, 257.0, 207.0, 93.0, 113.0, 152.0, 94.0, 200.0, 159.0, 107.0, 150.0, 107.0, 100.0, 90.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0013003978, 0.0060495376, -0.0018198629, -0.008677945, 0.0051203645, -0.005375454, 0.061329693, -0.01389107, 0.007893517, 0.013057403, -0.0023481178, 0.0067244205, -0.06522365, -0.045123104, 0.034939125, -0.01148451, -0.015973039, -0.016254319, -0.0040260614, -0.02164383, 0.07834523, 0.011088128, -0.00782295, 0.0076241563, -0.009206154, -0.07656054, 0.009429142, -0.0044327034, 0.12089577, -0.00033253044, -0.014531197, 0.004815725, 0.024141347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.38362986, 0.0, 1.1633577, 0.0, 1.0760925, 1.1034994, 1.6854573, 1.477312, 0.0, 0.0, 0.0, 1.4570111, 2.0314841, 1.6937915, 1.5841498, 2.0826623, 0.0, 0.0, 1.8372637, 1.7826992, 1.9052167, 0.0, 0.0, 0.0, 0.0, 0.9566548, 0.0, 0.0, 2.3756611, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 25, 25, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0060495376, -0.5, -0.008677945, 1.0, 1.0, 1.0, 1.0, 0.007893517, 0.013057403, -0.0023481178, 1.0, 1.0, -0.115384616, 1.0, -0.03846154, -0.015973039, -0.016254319, 0.5769231, 1.0, 1.0, 0.011088128, -0.00782295, 0.0076241563, -0.009206154, 0.30769232, 0.009429142, -0.0044327034, 1.0, -0.00033253044, -0.014531197, 0.004815725, 0.024141347], "split_indices": [1, 0, 1, 0, 42, 88, 12, 50, 0, 0, 0, 81, 121, 1, 17, 1, 0, 0, 1, 121, 5, 0, 0, 0, 0, 1, 0, 0, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 104.0, 1973.0, 149.0, 1824.0, 1537.0, 287.0, 1396.0, 141.0, 158.0, 129.0, 996.0, 400.0, 351.0, 645.0, 255.0, 145.0, 91.0, 260.0, 280.0, 365.0, 90.0, 165.0, 136.0, 124.0, 190.0, 90.0, 94.0, 271.0, 92.0, 98.0, 169.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0034806887, -0.0033038438, 0.042442035, -0.0077341367, 0.0078096665, 0.014734971, -0.008237989, -0.014732569, 0.049209382, -0.0062307077, 0.0039441087, -0.013932426, -0.0059547196, 0.006604963, 0.0031807758, -0.013056067, 0.007435961, -0.040307265, 0.031947978, -0.01821924, -0.018101614, 0.0034186929, 0.010379178, 0.0036154578, -0.012478005, -0.034878593, 0.005751698, 0.048655976, -0.025246706, -0.009600373, 0.0026246512, 0.012229414, -0.00070074934, -0.009392395, 0.0070901415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, -1, 17, -1, 19, 21, 23, -1, 25, -1, 27, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5471726, 0.6357871, 1.6322347, 0.66631764, 0.0, 0.0, 0.5336387, 1.6284404, 0.0536274, 0.0, 0.0, 0.0, 0.79334277, 0.0, 0.0, 1.5673574, 0.0, 2.4739528, 0.9879321, 1.6007859, 0.0, 0.71477705, 0.0, 0.7422811, 0.0, 0.7547288, 0.0, 0.9140675, 2.2979095, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, -1, 18, -1, 20, 22, 24, -1, 26, -1, 28, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 0.0078096665, 0.014734971, 2.9230769, -0.53846157, 1.0, -0.0062307077, 0.0039441087, -0.013932426, 5.0, 0.006604963, 0.0031807758, 1.0, 0.007435961, 0.5769231, 1.0, 1.0, -0.018101614, 1.0, 0.010379178, -0.26923078, -0.012478005, 1.0, 0.005751698, 1.0, 1.0, -0.009600373, 0.0026246512, 0.012229414, -0.00070074934, -0.009392395, 0.0070901415], "split_indices": [1, 114, 137, 125, 0, 0, 1, 1, 53, 0, 0, 0, 0, 0, 0, 137, 0, 1, 93, 62, 0, 83, 0, 1, 0, 13, 0, 124, 124, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1763.0, 307.0, 1672.0, 91.0, 100.0, 207.0, 1489.0, 183.0, 97.0, 110.0, 98.0, 1391.0, 93.0, 90.0, 1278.0, 113.0, 796.0, 482.0, 688.0, 108.0, 345.0, 137.0, 571.0, 117.0, 202.0, 143.0, 223.0, 348.0, 101.0, 101.0, 96.0, 127.0, 203.0, 145.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.001426773, 0.01576321, -0.018516818, 0.00035094583, 0.017223118, -0.0044159754, -0.013670749, 0.07853142, -0.030454492, 0.05334395, -0.027101148, -0.0016006792, 0.01573133, -0.09791485, 0.021657735, -0.012674198, 0.01470729, -0.06354946, 0.040904135, -0.0029212201, -0.016119359, 0.014984325, -0.01898644, -0.022178767, -0.021197457, -0.0022564956, 0.011977837, 0.0060496926, -0.008480071, 0.0077693323, -0.06723283, -0.0140358675, -0.001375271], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.6045943, 2.4742246, 1.7199185, 2.249431, 0.0, 1.208091, 0.0, 1.9662454, 2.3553917, 4.388609, 1.6408849, 0.0, 0.0, 1.2694452, 1.9693782, 0.0, 0.0, 2.8884344, 1.1564039, 0.0, 0.0, 0.0, 1.5013373, 0.0, 1.5478413, 0.0, 0.0, 0.0, 0.0, 0.0, 0.90730035, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.1538461, 1.0, -0.34615386, 0.017223118, 1.0, -0.013670749, -0.5, 0.03846154, -0.30769232, 1.0, -0.0016006792, 0.01573133, 1.0, 0.1923077, -0.012674198, 0.01470729, 1.0, 1.0, -0.0029212201, -0.016119359, 0.014984325, 1.0, -0.022178767, -0.26923078, -0.0022564956, 0.011977837, 0.0060496926, -0.008480071, 0.0077693323, 1.0, -0.0140358675, -0.001375271], "split_indices": [108, 1, 62, 1, 0, 69, 0, 1, 1, 1, 23, 0, 0, 121, 1, 0, 0, 89, 59, 0, 0, 0, 126, 0, 1, 0, 0, 0, 0, 0, 137, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1026.0, 1032.0, 934.0, 92.0, 922.0, 110.0, 264.0, 670.0, 260.0, 662.0, 120.0, 144.0, 292.0, 378.0, 89.0, 171.0, 431.0, 231.0, 140.0, 152.0, 91.0, 287.0, 91.0, 340.0, 128.0, 103.0, 130.0, 157.0, 108.0, 232.0, 98.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020016264, 0.0016522687, -0.0073800692, -0.0029985586, 0.004952373, -0.014983588, 0.030016799, -0.0043939347, -0.0113103315, 0.15698804, -0.09021718, 0.023114849, -0.03974723, 0.0050982703, 0.0285204, -0.00028484518, -0.020478558, 0.0030437896, 0.011945589, -0.017492253, -0.008676127, 0.061346315, -0.06687277, -0.07824191, 0.013761659, -0.0042525367, 0.12556806, -0.00053461216, -0.013622058, -0.12584572, 0.0010301133, 0.01487128, 0.010433215, -0.0077476962, -0.017217783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, 21, -1, -1, 23, 25, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.54174525, 0.43749145, 0.0, 0.70868075, 0.0, 1.3653164, 7.2820053, 1.1534162, 0.0, 3.1532445, 2.4523747, 1.2897555, 2.1798232, 0.0, 0.0, 0.0, 0.0, 2.2501242, 0.0, 0.0, 4.2946806, 2.007918, 1.0709511, 1.205486, 0.0, 0.0, 0.09141827, 0.0, 0.0, 0.41683006, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 20, 20, 21, 21, 22, 22, 23, 23, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, 22, -1, -1, 24, 26, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0073800692, 1.0, 0.004952373, 1.0, 1.0, 1.0, -0.0113103315, 1.0, 1.0, 1.0, 1.0, 0.0050982703, 0.0285204, -0.00028484518, -0.020478558, 1.0, 0.011945589, -0.017492253, 1.0, 1.0, 1.0, 1.0, 0.013761659, -0.0042525367, 1.0, -0.00053461216, -0.013622058, 1.0, 0.0010301133, 0.01487128, 0.010433215, -0.0077476962, -0.017217783], "split_indices": [117, 125, 0, 105, 0, 88, 109, 127, 0, 39, 39, 113, 17, 0, 0, 0, 0, 23, 0, 0, 109, 115, 115, 74, 0, 0, 12, 0, 0, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1965.0, 100.0, 1791.0, 174.0, 1314.0, 477.0, 1186.0, 128.0, 232.0, 245.0, 667.0, 519.0, 127.0, 105.0, 139.0, 106.0, 552.0, 115.0, 97.0, 422.0, 301.0, 251.0, 286.0, 136.0, 115.0, 186.0, 133.0, 118.0, 186.0, 100.0, 89.0, 97.0, 91.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0005906783, 0.008584848, -0.030947171, -0.00035538588, 0.008761015, -0.06635539, 0.0016993156, 0.005321174, -0.008435642, -0.011298355, -0.0037564833, -0.00086085935, 0.006331236, 0.0231883, -0.029939638, 0.060986593, -0.012891878, -0.051230747, 0.0061990162, 0.111456074, -0.00046928436, -0.009632264, 0.027070744, -0.07792894, 0.0032201074, 0.013686742, 0.008550403, -0.0049424926, 0.0075273514, -0.13960008, 0.00052650116, -0.022000303, -0.004988731], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.5223903, 1.1678524, 0.71124494, 0.70810306, 0.0, 0.3235302, 0.0, 0.49867848, 0.0, 0.0, 0.0, 0.87904555, 0.0, 0.9382733, 1.1136965, 1.1137755, 1.1736078, 1.0290953, 0.0, 0.12529993, 0.0, 0.0, 0.8775782, 1.7957339, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4498463, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.008761015, 1.0, 0.0016993156, 1.0, -0.008435642, -0.011298355, -0.0037564833, 1.0, 0.006331236, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0061990162, 1.0, -0.00046928436, -0.009632264, 1.0, 1.0, 0.0032201074, 0.013686742, 0.008550403, -0.0049424926, 0.0075273514, 1.0, 0.00052650116, -0.022000303, -0.004988731], "split_indices": [80, 125, 23, 31, 0, 69, 0, 88, 0, 0, 0, 108, 0, 13, 42, 93, 5, 74, 0, 15, 0, 0, 81, 12, 0, 0, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1653.0, 419.0, 1485.0, 168.0, 241.0, 178.0, 1391.0, 94.0, 92.0, 149.0, 1257.0, 134.0, 688.0, 569.0, 336.0, 352.0, 462.0, 107.0, 190.0, 146.0, 114.0, 238.0, 350.0, 112.0, 96.0, 94.0, 92.0, 146.0, 201.0, 149.0, 106.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.008109706, 0.011842568, -0.0064921305, 0.017841745, -0.026390977, 0.008147984, 0.011496696, -0.011890533, 0.009121198, -0.0069256662, 0.013532193, 0.0022019478, 0.056141336, 0.013307794, -0.0075771944, -0.0013620948, 0.014399016, 0.040478334, -0.02041278, 0.05869532, -0.004681137, -0.05510373, 0.0072285966, 0.12216581, 0.012815892, -0.012053798, -0.014936153, 0.0066849417, 0.016852617, 0.010989154, -0.0037376608, 0.0062379735, -0.0077065015], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, 11, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.56622046, 0.45323467, 0.0, 1.6080961, 2.9158297, 0.6472326, 0.0, 0.0, 0.0, 0.0, 0.70098484, 0.9932628, 1.8692033, 0.9198711, 0.0, 0.0, 0.0, 0.8841264, 1.4406816, 1.339515, 0.0, 0.856837, 0.0, 0.49494553, 1.3009496, 0.0, 0.9703168, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, 12, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.0, -0.0064921305, 1.0, 1.0, -0.53846157, 0.011496696, -0.011890533, 0.009121198, -0.0069256662, 1.0, 1.0, 1.0, 1.0, -0.0075771944, -0.0013620948, 0.014399016, 1.0, 1.0, -0.03846154, -0.004681137, 1.0, 0.0072285966, 1.0, 1.0, -0.012053798, 1.0, 0.0066849417, 0.016852617, 0.010989154, -0.0037376608, 0.0062379735, -0.0077065015], "split_indices": [117, 0, 0, 125, 122, 1, 0, 0, 0, 0, 61, 0, 12, 12, 0, 0, 0, 113, 83, 1, 0, 124, 0, 111, 53, 0, 122, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1976.0, 101.0, 1708.0, 268.0, 1553.0, 155.0, 150.0, 118.0, 101.0, 1452.0, 1147.0, 305.0, 1004.0, 143.0, 170.0, 135.0, 556.0, 448.0, 460.0, 96.0, 326.0, 122.0, 193.0, 267.0, 124.0, 202.0, 88.0, 105.0, 91.0, 176.0, 90.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004507569, 0.01230403, -0.025881954, 0.0044911853, 0.008172537, -0.09625998, 0.025670666, 0.012910347, -0.006932979, -0.016614698, -0.0024784608, 0.0072579803, -0.0043735676, -0.0016223188, 0.013016708, 0.00992144, -0.013629953, -0.017921537, 0.04017391, 0.018882163, -0.07113556, -0.023546863, 0.13056852, -0.048281286, 0.0114273755, 0.00039249487, -0.013260245, -0.012911053, 0.045872703, 0.02532708, -0.0039851316, 0.005742548, -0.01653544, 0.010864566, -0.001422904], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4885511, 0.89004236, 1.5274606, 0.9167283, 0.0, 0.889145, 0.7911574, 2.2561667, 0.0, 0.0, 0.0, 0.0, 0.0, 1.831415, 0.0, 0.9139162, 0.0, 1.1065375, 2.9952078, 2.1398807, 1.0657735, 2.2350962, 4.495846, 2.4255826, 0.0, 0.0, 0.0, 0.0, 0.69418895, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.3461539, 0.008172537, 0.5769231, 1.0, 0.84615386, -0.006932979, -0.016614698, -0.0024784608, 0.0072579803, -0.0043735676, 0.5769231, 0.013016708, -0.1923077, -0.013629953, 1.0, 1.0, -0.3846154, -0.3846154, 1.0, 0.115384616, 1.0, 0.0114273755, 0.00039249487, -0.013260245, -0.012911053, 1.0, 0.02532708, -0.0039851316, 0.005742548, -0.01653544, 0.010864566, -0.001422904], "split_indices": [80, 125, 122, 1, 0, 1, 109, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 126, 124, 1, 1, 59, 1, 97, 0, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1641.0, 421.0, 1475.0, 166.0, 178.0, 243.0, 1324.0, 151.0, 90.0, 88.0, 145.0, 98.0, 1178.0, 146.0, 1085.0, 93.0, 565.0, 520.0, 334.0, 231.0, 305.0, 215.0, 196.0, 138.0, 104.0, 127.0, 121.0, 184.0, 125.0, 90.0, 103.0, 93.0, 90.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00082315505, 0.0075349035, -0.003047332, -0.0071669193, 0.0025860865, -0.0044181347, 0.010241098, 0.016670875, -0.029302087, -0.0038195697, 0.06533569, -0.05427436, 0.0064191655, 0.021575402, -0.012893061, -0.0023432744, 0.015541923, -0.033832002, -0.014793751, -0.00912001, 0.015737938, 0.028620115, -0.08542289, 0.0070540505, -0.0345538, -0.0051571405, 0.00890722, -0.016061215, -0.0033297816, -0.009320553, 0.0026994299], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.59594095, 0.0, 0.7592348, 0.0, 1.2690402, 0.89002335, 0.0, 0.9153965, 1.8164366, 2.0524662, 2.1750689, 1.1756228, 0.0, 2.2385168, 0.0, 0.0, 0.0, 1.623868, 0.0, 0.88741803, 0.0, 1.1052859, 1.0817113, 0.0, 1.198488, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 19, 19, 21, 21, 22, 22, 24, 24], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0075349035, -0.5, -0.0071669193, 1.0, 1.0, 0.010241098, 0.5, 1.0, 1.0, 1.0, 2.0, 0.0064191655, 1.0, -0.012893061, -0.0023432744, 0.015541923, 1.0, -0.014793751, -0.34615386, 0.015737938, 1.0, 1.0, 0.0070540505, 1.0, -0.0051571405, 0.00890722, -0.016061215, -0.0033297816, -0.009320553, 0.0026994299], "split_indices": [1, 0, 1, 0, 90, 12, 0, 1, 61, 74, 39, 0, 0, 64, 0, 0, 0, 106, 0, 1, 0, 17, 69, 0, 59, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 102.0, 1964.0, 149.0, 1815.0, 1696.0, 119.0, 918.0, 778.0, 646.0, 272.0, 614.0, 164.0, 537.0, 109.0, 137.0, 135.0, 504.0, 110.0, 438.0, 99.0, 228.0, 276.0, 106.0, 332.0, 98.0, 130.0, 113.0, 163.0, 170.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022470555, 0.0039899424, -0.052170288, -0.0066508874, 0.040921412, -0.008317312, -0.00024952582, -0.028745534, 0.029477946, 0.017255004, -0.062092274, -0.0014781412, -0.111923575, -0.014527445, 0.08924074, -0.0026189776, -0.010409139, 0.05264503, -0.029528286, -0.026049346, -0.00094218564, 0.003138312, -0.006846435, -0.0012176884, 0.019895619, 0.010869875, -0.00238449, -0.06377258, 0.011979934, 0.0012431203, -0.011727738, 0.0060153883, -0.0035230506], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.64204735, 0.7203352, 0.35267502, 1.1359153, 5.5594153, 0.0, 0.0, 2.0026865, 1.4201381, 0.0, 0.3468104, 1.0095782, 3.3198485, 0.7701212, 2.5481024, 0.0, 0.0, 0.97327304, 0.62258196, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9785449, 0.45031413, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, -0.008317312, -0.00024952582, 1.0, 1.0, 0.017255004, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0026189776, -0.010409139, 1.0, 1.0, -0.026049346, -0.00094218564, 0.003138312, -0.006846435, -0.0012176884, 0.019895619, 0.010869875, -0.00238449, 1.0, 1.0, 0.0012431203, -0.011727738, 0.0060153883, -0.0035230506], "split_indices": [119, 105, 58, 109, 109, 0, 0, 50, 127, 0, 12, 122, 59, 39, 39, 0, 0, 12, 124, 0, 0, 0, 0, 0, 0, 0, 0, 5, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1833.0, 229.0, 1423.0, 410.0, 141.0, 88.0, 883.0, 540.0, 180.0, 230.0, 665.0, 218.0, 311.0, 229.0, 124.0, 106.0, 227.0, 438.0, 89.0, 129.0, 168.0, 143.0, 119.0, 110.0, 131.0, 96.0, 240.0, 198.0, 99.0, 141.0, 98.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.5803202E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}