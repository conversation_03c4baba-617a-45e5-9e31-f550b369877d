{"learner": {"attributes": {"best_iteration": "107", "best_score": "1.222114"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "158"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.0035375208, 0.14232187, -0.24169423, 0.09448558, 0.04988911, -0.2827638, -0.00017049939, -0.110171154, 0.16752476, -0.25439915, -0.044693504, -0.005493017, -0.018768672, 0.27551663, 0.0746854, -0.21536203, -0.03560429, 0.36101094, 0.006100361, -0.010437224, 0.16272704, -0.28367484, -0.162295, 0.053784322, 0.0170883, 0.08571674, 0.030647958, -0.025831556, -0.030988885, -0.027270336, -0.003669281, -0.008498703, 0.023984736], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [71.975876, 21.91818, 7.756878, 16.936037, 0.0, 3.1292725, 0.0, 1.276046, 8.371622, 2.273594, 0.0, 0.0, 0.0, 7.0790997, 7.078274, 1.5008125, 0.0, 9.279327, 0.0, 0.0, 3.3321972, 0.120321274, 3.231134, 0.0, 0.0, 5.1568937, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 21, 21, 22, 22, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.04988911, 1.0, -0.00017049939, 1.0, 1.0, 1.0, -0.044693504, -0.005493017, -0.018768672, 1.0, 1.0, 1.0, -0.03560429, 1.0, 0.006100361, -0.010437224, 1.0, 1.0, 1.0, 0.053784322, 0.0170883, 1.0, 0.030647958, -0.025831556, -0.030988885, -0.027270336, -0.003669281, -0.008498703, 0.023984736], "split_indices": [137, 125, 71, 17, 0, 40, 0, 59, 53, 116, 0, 0, 0, 106, 5, 81, 0, 126, 0, 0, 97, 13, 13, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1285.0, 787.0, 1133.0, 152.0, 672.0, 115.0, 298.0, 835.0, 573.0, 99.0, 174.0, 124.0, 386.0, 449.0, 414.0, 159.0, 276.0, 110.0, 148.0, 301.0, 181.0, 233.0, 143.0, 133.0, 196.0, 105.0, 92.0, 89.0, 124.0, 109.0, 93.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00029959244, -0.20729782, 0.13209175, -0.24544768, 0.0012548873, 0.084295966, 0.047190428, -0.28944063, 0.00097112864, -0.1170605, 0.15553756, -0.32202524, -0.014703375, -0.020926261, -0.0034563844, 0.21845423, -0.02542273, -0.023251122, -0.35863245, 0.32345328, 0.076685086, -0.0147384545, 0.014366065, -0.042822704, -0.33267707, 0.0175722, 0.046865202, -0.007528146, 0.034466915, -0.029981336, -0.037475485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [56.224205, 6.6929245, 20.415697, 7.633129, 0.0, 15.808144, 0.0, 2.6913605, 0.0, 2.1906328, 9.267733, 1.5466728, 0.0, 0.0, 0.0, 8.990913, 4.3305607, 0.0, 0.60512924, 7.443287, 10.466225, 0.0, 0.0, 0.0, 0.33740807, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, 0.0012548873, 1.0, 0.047190428, 1.0, 0.00097112864, 1.0, 0.1923077, 0.0, -0.014703375, -0.020926261, -0.0034563844, 1.0, 0.7307692, -0.023251122, 1.0, 1.0, -0.1923077, -0.0147384545, 0.014366065, -0.042822704, 0.8076923, 0.0175722, 0.046865202, -0.007528146, 0.034466915, -0.029981336, -0.037475485], "split_indices": [2, 71, 125, 1, 0, 17, 0, 62, 0, 105, 1, 1, 0, 0, 0, 106, 1, 0, 26, 113, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 798.0, 1257.0, 680.0, 118.0, 1102.0, 155.0, 580.0, 100.0, 288.0, 814.0, 472.0, 108.0, 136.0, 152.0, 604.0, 210.0, 137.0, 335.0, 347.0, 257.0, 122.0, 88.0, 91.0, 244.0, 172.0, 175.0, 164.0, 93.0, 137.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0064263074, -0.15873875, 0.14427976, -0.032292485, -0.22419327, 0.09551524, 0.045369413, 0.036490176, -0.01919202, -0.19454457, -0.03961557, -0.0175371, 0.12895381, -0.0050202603, 0.012604885, -0.22660056, -0.006769055, 0.055476308, 0.19781679, -0.29556075, -0.15911384, 0.13971893, -0.021296525, 0.03392465, 0.10872722, -0.03362077, -0.024312617, -0.00934489, -0.026046623, 0.030854214, -0.002371626, -0.006110206, 0.0013717576, -0.010799414, 0.029381042], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, -1, 17, -1, -1, 19, -1, 21, 23, 25, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [47.6304, 8.541328, 15.737246, 3.8648262, 3.4669495, 8.1613035, 0.0, 1.9099665, 0.0, 2.3585281, 0.0, 0.0, 4.0580244, 0.0, 0.0, 2.1547527, 0.0, 2.509408, 5.21636, 0.48806763, 1.5573387, 5.104458, 0.2829324, 0.0, 10.188317, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, -1, 18, -1, -1, 20, -1, 22, 24, 26, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.045369413, 1.0, -0.01919202, 1.0, -0.03961557, -0.0175371, 1.0, -0.0050202603, 0.012604885, 1.0, -0.006769055, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.03392465, 1.0, -0.03362077, -0.024312617, -0.00934489, -0.026046623, 0.030854214, -0.002371626, -0.006110206, 0.0013717576, -0.010799414, 0.029381042], "split_indices": [71, 137, 125, 7, 40, 26, 0, 83, 0, 113, 0, 0, 15, 0, 0, 115, 0, 121, 69, 111, 23, 111, 12, 0, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1032.0, 1043.0, 352.0, 680.0, 901.0, 142.0, 246.0, 106.0, 580.0, 100.0, 99.0, 802.0, 125.0, 121.0, 463.0, 117.0, 388.0, 414.0, 229.0, 234.0, 185.0, 203.0, 160.0, 254.0, 129.0, 100.0, 142.0, 92.0, 91.0, 94.0, 95.0, 108.0, 117.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0012775755, 0.11419265, -0.18113472, 0.07403181, 0.04081805, -0.21387264, 0.00365883, -0.10411953, 0.13656744, -0.2572689, -0.10637326, -0.019747311, -0.0017566534, 0.18707247, -0.023322845, -0.23095553, -0.037206452, -0.019373247, -0.000868121, 0.122263506, 0.035964515, -0.015607935, 0.014413135, -0.03532359, -0.19141819, 0.0324843, 0.038094934, -0.011935158, -0.024504913, -0.010110405, 0.15263921, 0.02846757, 0.0019119125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [42.450615, 15.030054, 5.616705, 12.477705, 0.0, 3.19557, 0.0, 2.351289, 6.694395, 1.474083, 1.6812565, 0.0, 0.0, 7.0460815, 4.4238963, 1.9193516, 0.0, 0.0, 0.0, 10.636869, 0.0, 0.0, 0.0, 0.0, 1.1595001, 5.457441, 0.0, 0.0, 0.0, 0.0, 3.155684, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 19, 19, 24, 24, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 0.04081805, 1.0, 0.00365883, 1.0, 0.26923078, 1.0, 1.0, -0.019747311, -0.0017566534, -0.115384616, 0.8076923, 1.0, -0.037206452, -0.019373247, -0.000868121, 1.0, 0.035964515, -0.015607935, 0.014413135, -0.03532359, 1.0, 1.0, 0.038094934, -0.011935158, -0.024504913, -0.010110405, 1.0, 0.02846757, 0.0019119125], "split_indices": [137, 125, 1, 17, 0, 93, 0, 105, 1, 0, 13, 0, 0, 1, 1, 26, 0, 0, 0, 61, 0, 0, 0, 0, 126, 111, 0, 0, 0, 0, 93, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1273.0, 788.0, 1120.0, 153.0, 685.0, 103.0, 291.0, 829.0, 488.0, 197.0, 140.0, 151.0, 630.0, 199.0, 397.0, 91.0, 104.0, 93.0, 458.0, 172.0, 111.0, 88.0, 97.0, 300.0, 340.0, 118.0, 128.0, 172.0, 161.0, 179.0, 90.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.008933079, 0.110000245, -0.15574996, 0.0730798, 0.037696343, -0.20235485, -0.032333303, -0.07637866, 0.12504207, -0.23471175, -0.0024391836, -0.013722769, 0.007855502, -0.0010576661, -0.013823251, 0.17128682, -0.0022865268, -0.21046206, -0.034233678, 0.10744504, 0.03687707, -0.009149744, 0.013203099, -0.010109342, -0.2587599, 0.22981684, -0.05408571, -0.020080285, -0.03229584, 0.010801671, 0.039008014, -0.014378602, 0.0037426695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [34.48647, 12.655617, 4.5324326, 8.760273, 0.0, 3.293764, 2.512418, 1.1844007, 4.9284887, 1.2631779, 0.0, 0.0, 0.0, 0.0, 0.0, 7.7411423, 2.6721172, 2.0864944, 0.0, 9.1718, 0.0, 0.0, 0.0, 0.0, 1.0194855, 5.1533003, 1.6417385, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 16, 16, 17, 17, 19, 19, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.037696343, 2.3076923, 1.0, -0.03846154, 0.15384616, 1.0, -0.0024391836, -0.013722769, 0.007855502, -0.0010576661, -0.013823251, -0.115384616, 0.7307692, -0.03846154, -0.034233678, 1.0, 0.03687707, -0.009149744, 0.013203099, -0.010109342, 1.0, 1.0, 1.0, -0.020080285, -0.03229584, 0.010801671, 0.039008014, -0.014378602, 0.0037426695], "split_indices": [137, 125, 93, 17, 0, 1, 13, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 122, 0, 0, 0, 0, 23, 61, 111, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1284.0, 788.0, 1128.0, 156.0, 572.0, 216.0, 291.0, 837.0, 484.0, 88.0, 111.0, 105.0, 141.0, 150.0, 614.0, 223.0, 395.0, 89.0, 464.0, 150.0, 134.0, 89.0, 121.0, 274.0, 264.0, 200.0, 144.0, 130.0, 150.0, 114.0, 101.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0005855856, -0.11666405, 0.11637382, -0.025336001, -0.16428414, 0.018427825, 0.21992224, 0.035198692, -0.016084488, -0.18831196, -0.00602266, -0.098188415, 0.16666882, 0.17733097, 0.035432912, 0.013089785, -0.006867986, -0.24379863, -0.13519482, -0.019231645, -0.0024230646, 0.008953447, 0.026976913, 0.24703665, 0.10038805, -0.02224982, -0.02841091, -0.021503141, -0.005246558, 0.031560354, 0.018113254, -0.0007707426, 0.020499662], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [28.143312, 4.4795218, 10.578267, 2.8956556, 1.6926842, 9.265997, 2.9023495, 2.4256265, 0.0, 1.6210098, 0.0, 2.0884497, 1.8768063, 2.0648918, 0.0, 0.0, 0.0, 0.23097038, 1.8559537, 0.0, 0.0, 0.0, 0.0, 0.9128046, 2.069312, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, -0.016084488, 1.0, -0.00602266, 1.0, 1.0, 1.0, 0.035432912, 0.013089785, -0.006867986, 1.0, 1.0, -0.019231645, -0.0024230646, 0.008953447, 0.026976913, 1.0, 1.0, -0.02224982, -0.02841091, -0.021503141, -0.005246558, 0.031560354, 0.018113254, -0.0007707426, 0.020499662], "split_indices": [71, 137, 39, 7, 113, 42, 0, 93, 0, 115, 0, 15, 15, 15, 0, 0, 0, 17, 39, 0, 0, 0, 0, 109, 109, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1030.0, 1043.0, 353.0, 677.0, 536.0, 507.0, 244.0, 109.0, 550.0, 127.0, 300.0, 236.0, 385.0, 122.0, 127.0, 117.0, 269.0, 281.0, 132.0, 168.0, 135.0, 101.0, 202.0, 183.0, 176.0, 93.0, 143.0, 138.0, 99.0, 103.0, 90.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0025961024, 0.08385518, -0.1290498, 0.05618801, 0.04125076, -0.17123665, -0.016813133, 0.0054951687, 0.23303248, -0.19720827, -0.004865054, -0.011190125, 0.008467512, -0.027683148, 0.031691894, 0.006438911, 0.038690414, -0.16709504, -0.025592908, -0.12212828, 0.057208907, -0.009587589, -0.2128252, -0.05461305, -0.023298098, 0.020043114, -0.030315785, -0.027003547, -0.016346728, -0.015367231, 0.010515991, -0.012909624, 0.004636903], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, -1, -1, 21, -1, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [22.05809, 11.593426, 3.7263727, 10.542543, 0.0, 1.8211098, 2.0748205, 9.44392, 6.798752, 0.83462524, 0.0, 0.0, 0.0, 6.622572, 0.0, 0.0, 0.0, 1.01614, 0.0, 2.9263396, 5.4529357, 0.0, 0.53651714, 3.8459592, 0.0, 0.0, 2.0452397, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 17, 17, 19, 19, 20, 20, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, -1, -1, 22, -1, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.04125076, 1.0, 1.0, 5.0, 1.0, 1.0, -0.004865054, -0.011190125, 0.008467512, 1.0, 0.031691894, 0.006438911, 0.038690414, 1.0, -0.025592908, 1.0, 1.0, -0.009587589, 1.0, 1.0, -0.023298098, 0.020043114, -0.15384616, -0.027003547, -0.016346728, -0.015367231, 0.010515991, -0.012909624, 0.004636903], "split_indices": [137, 102, 93, 113, 0, 113, 13, 0, 109, 80, 0, 0, 0, 39, 0, 0, 0, 122, 0, 115, 53, 0, 74, 17, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1275.0, 787.0, 1176.0, 99.0, 572.0, 215.0, 914.0, 262.0, 472.0, 100.0, 111.0, 104.0, 826.0, 88.0, 125.0, 137.0, 312.0, 160.0, 391.0, 435.0, 122.0, 190.0, 243.0, 148.0, 165.0, 270.0, 88.0, 102.0, 150.0, 93.0, 118.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00036123666, -0.10306607, 0.10309039, -0.07617337, -0.027835935, 0.070506774, 0.031014651, -0.099110246, 0.013103159, 0.1146487, -0.045477446, -0.053185698, -0.20389317, 0.23311512, 0.040903345, -0.01625755, 0.004701302, 0.010852213, -0.08421706, -0.0232926, -0.017647328, 0.0031275915, 0.047603663, -0.08572681, 0.1466221, -0.1483094, 0.00049903616, 0.00083041744, -0.018184742, 0.006105169, 0.022468632, -0.021342017, -0.007434353, 0.006686942, -0.005073422], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [21.961874, 4.8555317, 6.9962606, 4.244102, 0.0, 4.5873113, 0.0, 3.868935, 0.0, 5.6698895, 2.6751225, 2.8050694, 0.19503784, 12.208738, 5.354874, 0.0, 0.0, 0.0, 2.5465074, 0.0, 0.0, 0.0, 0.0, 1.6449739, 1.4562368, 1.2858644, 0.686875, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 3.1538463, -0.027835935, 0.30769232, 0.031014651, 1.0, 0.013103159, 1.0, 1.0, -0.42307693, 1.0, -0.30769232, 1.0, -0.01625755, 0.004701302, 0.010852213, 1.0, -0.0232926, -0.017647328, 0.0031275915, 0.047603663, -0.1923077, 1.0, 0.34615386, -0.03846154, 0.00083041744, -0.018184742, 0.006105169, 0.022468632, -0.021342017, -0.007434353, 0.006686942, -0.005073422], "split_indices": [71, 40, 125, 1, 0, 1, 0, 23, 0, 69, 93, 1, 106, 1, 124, 0, 0, 0, 2, 0, 0, 0, 0, 1, 109, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1030.0, 1037.0, 893.0, 137.0, 896.0, 141.0, 804.0, 89.0, 649.0, 247.0, 559.0, 245.0, 249.0, 400.0, 109.0, 138.0, 90.0, 469.0, 119.0, 126.0, 136.0, 113.0, 182.0, 218.0, 267.0, 202.0, 92.0, 90.0, 104.0, 114.0, 142.0, 125.0, 88.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0024043047, -0.08915074, 0.092203744, -0.011827664, -0.12918895, 0.0585322, 0.030760607, 0.05079123, -0.010311016, -0.15373614, 0.00038334553, 0.0053412532, 0.24168722, 0.008071355, 0.0024405161, -0.18074404, -0.0031545092, -0.07171527, 0.096605085, 0.013210142, 0.0334337, -0.011002343, -0.21019302, -0.1598735, 0.019557294, 0.18251668, -0.000973301, -0.026388342, -0.17936599, -0.09960933, -0.02713622, 0.015858332, 0.020672197, -0.02365297, -0.011824041, -0.012530433, -0.007189335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, -1, -1, 21, -1, 23, 25, -1, -1, -1, 27, 29, -1, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.98581, 3.1670828, 7.564803, 1.9948874, 2.2008314, 8.787455, 0.0, 0.16343284, 0.0, 1.8777676, 0.0, 4.9156995, 2.061078, 0.0, 0.0, 0.9705162, 0.0, 8.930625, 2.9234164, 0.0, 0.0, 0.0, 0.54453087, 1.9148493, 0.0, 0.10253763, 0.0, 0.0, 0.73027945, 0.13174939, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 22, 22, 23, 23, 25, 25, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, -1, -1, 22, -1, 24, 26, -1, -1, -1, 28, 30, -1, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 2.7307692, 2.0, 0.030760607, -0.07692308, -0.010311016, 1.0, 0.00038334553, 1.0, -0.26923078, 0.008071355, 0.0024405161, 0.0, -0.0031545092, 1.0, 1.0, 0.013210142, 0.0334337, -0.011002343, 1.0, 0.23076923, 0.019557294, 0.26923078, -0.000973301, -0.026388342, 1.0, 1.0, -0.02713622, 0.015858332, 0.020672197, -0.02365297, -0.011824041, -0.012530433, -0.007189335], "split_indices": [71, 137, 125, 93, 1, 0, 0, 1, 0, 62, 0, 39, 1, 0, 0, 1, 0, 113, 15, 0, 0, 0, 97, 1, 0, 1, 0, 0, 111, 109, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1023.0, 1043.0, 349.0, 674.0, 902.0, 141.0, 207.0, 142.0, 569.0, 105.0, 699.0, 203.0, 97.0, 110.0, 466.0, 103.0, 379.0, 320.0, 93.0, 110.0, 137.0, 329.0, 285.0, 94.0, 177.0, 143.0, 120.0, 209.0, 185.0, 100.0, 89.0, 88.0, 108.0, 101.0, 96.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0017092299, -0.07931866, 0.08110881, -0.054324236, -0.024114383, 0.03633443, 0.2089884, -0.07302176, 0.011544075, -0.036406398, 0.11912234, 0.0075770053, 0.0389702, -0.028838374, -0.17411624, 0.050155737, -0.03213401, 0.056260645, 0.021765022, 0.01719925, -0.109518185, -0.022306815, -0.013429713, -0.019126136, 0.019763699, -0.0017285945, 0.011069673, 0.012290133, -0.05786453, -0.0051049967, -0.015465151, -0.018452233, 0.013016953, -0.013888247, 0.002393997], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [13.311079, 4.1418, 5.9833865, 2.8155022, 0.0, 4.661075, 6.524149, 3.5688906, 0.0, 10.161762, 2.242093, 0.0, 0.0, 2.0651548, 0.47365904, 3.228818, 0.0, 0.88479346, 0.0, 2.8087769, 0.5330503, 0.0, 0.0, 5.308981, 0.0, 0.0, 0.0, 0.0, 1.3719203, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.2692308, -0.024114383, 1.0, 1.0, 1.0, 0.011544075, 0.5769231, 1.0, 0.0075770053, 0.0389702, 1.0, 1.0, 1.0, -0.03213401, 1.0, 0.021765022, 1.0, -0.1923077, -0.022306815, -0.013429713, 1.0, 0.019763699, -0.0017285945, 0.011069673, 0.012290133, 0.07692308, -0.0051049967, -0.015465151, -0.018452233, 0.013016953, -0.013888247, 0.002393997], "split_indices": [71, 40, 113, 1, 0, 39, 61, 23, 0, 1, 122, 0, 0, 80, 26, 116, 0, 93, 0, 122, 1, 0, 0, 42, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1024.0, 1045.0, 887.0, 137.0, 774.0, 271.0, 799.0, 88.0, 412.0, 362.0, 156.0, 115.0, 556.0, 243.0, 316.0, 96.0, 221.0, 141.0, 354.0, 202.0, 109.0, 134.0, 215.0, 101.0, 94.0, 127.0, 147.0, 207.0, 88.0, 114.0, 102.0, 113.0, 104.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.001708902, -0.075334, 0.07043239, -0.054620024, -0.021483945, 0.0045524174, 0.14157222, -0.06992867, 0.0043482822, 0.059147768, -0.013516855, 0.083249226, 0.21979043, 0.0056035775, -0.0962987, -0.025221396, 0.020590259, -0.0007764717, 0.021937445, 0.033444446, 0.009389582, 0.012133324, -0.008803221, -0.11790368, 0.0017424185, -0.014902501, 0.004954311, -0.15523128, -0.004880315, -0.024431592, -0.11301106, -0.015775133, -0.0050476347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [10.957438, 2.950395, 4.8835306, 1.3351192, 0.0, 4.12681, 2.2855225, 1.5316854, 0.0, 4.816436, 0.0, 3.555728, 3.0889435, 2.156451, 1.4004793, 2.2862608, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2355118, 0.0, 0.0, 0.0, 1.169724, 0.0, 0.0, 0.5903394, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.021483945, 1.0, 1.0, 0.0, 0.0043482822, 1.0, -0.013516855, 1.0, 1.0, 1.0, 1.0, 1.0, 0.020590259, -0.0007764717, 0.021937445, 0.033444446, 0.009389582, 0.012133324, -0.008803221, 1.0, 0.0017424185, -0.014902501, 0.004954311, 1.0, -0.004880315, -0.024431592, 1.0, -0.015775133, -0.0050476347], "split_indices": [71, 40, 39, 121, 0, 106, 50, 0, 0, 50, 0, 106, 126, 106, 42, 13, 0, 0, 0, 0, 0, 0, 0, 74, 0, 0, 0, 80, 0, 0, 50, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1021.0, 1042.0, 889.0, 132.0, 541.0, 501.0, 769.0, 120.0, 389.0, 152.0, 287.0, 214.0, 199.0, 570.0, 247.0, 142.0, 172.0, 115.0, 112.0, 102.0, 89.0, 110.0, 479.0, 91.0, 93.0, 154.0, 311.0, 168.0, 100.0, 211.0, 123.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0034832007, -0.017072845, 0.2139861, 0.025042761, -0.08004998, 0.035848577, 0.0061368444, 0.059815556, -0.059602935, -0.122670725, 0.035785604, 0.11513231, -0.07884184, -0.13672484, 0.012597161, -0.1442302, -0.00097284216, -0.0055935737, 0.013909283, 0.16786976, 0.035677645, 0.010566734, -0.024336256, -0.007463203, -0.020385219, -0.16554654, -0.01080797, 0.05908819, 0.035671454, 0.02063947, -0.022902966, -0.019029342, -0.013466087, -0.006871763, 0.019541442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.90086, 4.970448, 4.035734, 3.3054018, 3.7076864, 0.0, 0.0, 6.105382, 4.6799793, 1.3368015, 1.9140466, 2.384244, 6.8907185, 0.96283627, 0.0, 0.35524464, 0.0, 0.0, 0.0, 7.025646, 10.2581415, 0.0, 0.0, 0.0, 0.0, 0.22165298, 0.0, 3.780854, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 20, 20, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.1923077, 1.0, 0.035848577, 0.0061368444, 1.0, 1.2692307, 1.8076923, 1.0, 1.0, 1.0, 0.5769231, 0.012597161, 1.0, -0.00097284216, -0.0055935737, 0.013909283, 1.0, 1.0, 0.010566734, -0.024336256, -0.007463203, -0.020385219, 1.0, -0.01080797, 1.0, 0.035671454, 0.02063947, -0.022902966, -0.019029342, -0.013466087, -0.006871763, 0.019541442], "split_indices": [125, 137, 15, 1, 93, 0, 0, 7, 1, 1, 13, 50, 69, 1, 0, 83, 0, 0, 0, 97, 121, 0, 0, 0, 0, 116, 0, 115, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1874.0, 183.0, 1123.0, 751.0, 94.0, 89.0, 796.0, 327.0, 549.0, 202.0, 569.0, 227.0, 231.0, 96.0, 461.0, 88.0, 107.0, 95.0, 342.0, 227.0, 107.0, 120.0, 120.0, 111.0, 290.0, 171.0, 217.0, 125.0, 138.0, 89.0, 161.0, 129.0, 112.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.003095627, -0.06652462, 0.05875371, -0.046838522, -0.019639406, 0.039913904, 0.025370473, -0.061978232, 0.009010699, -0.009442362, 0.17554648, -0.027384872, -0.1402386, -0.056376558, 0.013082382, 0.0007586346, 0.03145063, -0.004906509, -0.011874186, -0.017066872, -0.011566909, -0.14468701, 0.051017914, 0.012694972, -0.045357984, -0.07179363, -0.027892625, 0.017422529, -0.0066079977, 0.0032381264, -0.09808215, -0.004452684, -0.009791232, -0.0042943247, -0.016400905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, -1, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [8.089311, 2.6026402, 3.834444, 1.8328106, 0.0, 6.3729906, 0.0, 2.1550016, 0.0, 4.5951295, 5.928285, 1.1335622, 0.18242645, 4.9601607, 0.0, 0.0, 0.0, 2.362864, 0.0, 0.0, 0.0, 2.8083372, 3.4048486, 0.0, 1.3894718, 0.1324637, 0.0, 0.0, 0.0, 0.0, 0.73429704, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 21, 21, 22, 22, 24, 24, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, -1, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.3076923, -0.019639406, 1.0, 0.025370473, 1.0, 0.009010699, 1.0, 1.0, 1.0, 1.0, 1.0, 0.013082382, 0.0007586346, 0.03145063, -0.30769232, -0.011874186, -0.017066872, -0.011566909, 0.23076923, 1.0, 0.012694972, 1.0, 1.0, -0.027892625, 0.017422529, -0.0066079977, 0.0032381264, 1.0, -0.004452684, -0.009791232, -0.0042943247, -0.016400905], "split_indices": [71, 40, 102, 1, 0, 113, 0, 23, 0, 0, 109, 7, 26, 39, 0, 0, 0, 1, 0, 0, 0, 1, 53, 0, 137, 17, 0, 0, 0, 0, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1018.0, 1044.0, 884.0, 134.0, 952.0, 92.0, 796.0, 88.0, 698.0, 254.0, 552.0, 244.0, 523.0, 175.0, 115.0, 139.0, 443.0, 109.0, 109.0, 135.0, 287.0, 236.0, 104.0, 339.0, 186.0, 101.0, 115.0, 121.0, 137.0, 202.0, 91.0, 95.0, 110.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0023774859, 0.04450304, -0.066103265, 0.02630255, 0.024834849, 0.017564971, -0.09650177, -0.13272451, 0.055609655, -0.011570027, 0.015866937, -0.11821773, 0.0003644635, -0.0007496142, -0.025129175, 0.0883578, -0.06723502, -0.15973285, -0.002649171, 0.043258637, 0.21489812, 0.0042414707, -0.015514389, -0.02125745, -0.01143971, 0.09033357, -0.008079416, 0.007436314, 0.038234405, -0.030839438, 0.029049916, -0.019003898, 0.005876987], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, 17, -1, -1, -1, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [5.96863, 4.7526207, 2.0041907, 5.480892, 0.0, 3.9489057, 1.25702, 2.7171805, 3.9947746, 0.0, 0.0, 1.8088074, 0.0, 0.0, 0.0, 4.4741793, 2.0145895, 0.7833662, 0.0, 3.3753917, 4.8475943, 0.0, 0.0, 0.0, 0.0, 10.162706, 0.0, 0.0, 0.0, 3.7233639, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, 18, -1, -1, -1, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0, -1.0, 0.024834849, 1.0, 1.0, 1.0, 1.0, -0.011570027, 0.015866937, 1.0, 0.0003644635, -0.0007496142, -0.025129175, 1.0, -0.03846154, 0.34615386, -0.002649171, 0.23076923, 1.0, 0.0042414707, -0.015514389, -0.02125745, -0.01143971, -0.15384616, -0.008079416, 0.007436314, 0.038234405, 1.0, 0.029049916, -0.019003898, 0.005876987], "split_indices": [137, 102, 0, 0, 0, 13, 113, 15, 23, 0, 0, 83, 0, 0, 0, 61, 1, 1, 0, 1, 113, 0, 0, 0, 0, 1, 0, 0, 0, 5, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1281.0, 788.0, 1176.0, 105.0, 210.0, 578.0, 183.0, 993.0, 108.0, 102.0, 475.0, 103.0, 89.0, 94.0, 784.0, 209.0, 327.0, 148.0, 578.0, 206.0, 93.0, 116.0, 151.0, 176.0, 419.0, 159.0, 112.0, 94.0, 261.0, 158.0, 94.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00037767226, -0.055081975, 0.054461416, -0.06923428, 0.0068514817, -0.007059906, 0.1221472, -0.041715186, -0.12964204, 0.0300489, -0.020145949, 0.06400876, 0.20021102, -0.01725164, -0.015485905, -0.016000303, -0.010267668, 0.08663031, -0.0066805147, -0.010119687, 0.13864483, 0.030118579, 0.0056780907, -0.055444725, 0.00991179, -0.00532416, 0.018903656, 0.03385298, -0.0063279765, 0.0032827891, -0.10711651, -0.016313363, -0.004009601], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [6.2088933, 1.7876618, 4.3639975, 1.5243897, 0.0, 3.9604516, 2.2647147, 1.7437768, 0.23496485, 2.5263438, 0.0, 3.5264654, 3.0848408, 2.302257, 0.0, 0.0, 0.0, 4.168214, 0.0, 0.0, 7.9512544, 0.0, 0.0, 1.7788696, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9235563, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 20, 20, 23, 23, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 3.1538463, 1.0, 1.0, 0.0068514817, 0.6923077, -0.07692308, 2.0, 1.0, 1.0, -0.020145949, 1.0, 1.0, 1.0, -0.015485905, -0.016000303, -0.010267668, -0.42307693, -0.0066805147, -0.010119687, -0.34615386, 0.030118579, 0.0056780907, 1.0, 0.00991179, -0.00532416, 0.018903656, 0.03385298, -0.0063279765, 0.0032827891, 1.0, -0.016313363, -0.004009601], "split_indices": [71, 1, 39, 23, 0, 1, 1, 0, 106, 126, 0, 89, 109, 62, 0, 0, 0, 1, 0, 0, 1, 0, 0, 53, 0, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1022.0, 1048.0, 917.0, 105.0, 549.0, 499.0, 630.0, 287.0, 461.0, 88.0, 286.0, 213.0, 518.0, 112.0, 135.0, 152.0, 291.0, 170.0, 89.0, 197.0, 125.0, 88.0, 390.0, 128.0, 123.0, 168.0, 99.0, 98.0, 144.0, 246.0, 134.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002943939, -0.01650169, 0.01885469, -0.0328716, 0.014917307, 0.004898957, -0.07731854, 0.08248896, -0.04917892, -0.036382273, -0.1379695, 0.1688564, -0.020223882, -0.019106353, -0.008500987, -0.08514499, 0.028634666, -0.10012241, -0.020125477, -0.005263647, 0.26992598, 0.010316352, -0.04897938, -0.016262105, -0.0021940812, -0.006431325, 0.011220255, -0.017224496, -0.003726327, 0.014009307, 0.042767987, -0.106214345, 0.0024608413, -0.012396805, -0.00888508], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [5.379295, 5.247876, 0.0, 2.9563415, 0.0, 3.9944994, 2.008604, 9.615162, 3.2378516, 1.5313046, 0.7808223, 6.7158575, 0.0, 0.0, 1.9707197, 1.3515193, 1.6078645, 0.9248457, 0.0, 0.0, 4.219219, 0.0, 1.3477747, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.055487633, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 22, 22, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.01885469, 1.0, 0.014917307, 1.0, 1.0, -0.03846154, -0.07692308, 1.0, 1.0, -0.5, -0.020223882, -0.019106353, 0.0, 1.0, 1.0, 1.0, -0.020125477, -0.005263647, 1.0, 0.010316352, 1.0, -0.016262105, -0.0021940812, -0.006431325, 0.011220255, -0.017224496, -0.003726327, 0.014009307, 0.042767987, 1.0, 0.0024608413, -0.012396805, -0.00888508], "split_indices": [0, 125, 0, 106, 0, 16, 108, 1, 1, 93, 109, 1, 0, 0, 0, 83, 39, 39, 0, 0, 113, 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1935.0, 137.0, 1761.0, 174.0, 952.0, 809.0, 391.0, 561.0, 483.0, 326.0, 300.0, 91.0, 125.0, 436.0, 276.0, 207.0, 204.0, 122.0, 94.0, 206.0, 116.0, 320.0, 124.0, 152.0, 98.0, 109.0, 95.0, 109.0, 113.0, 93.0, 180.0, 140.0, 89.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001463781, -0.04828668, 0.050397877, -0.06450657, 0.006563564, -0.0008197604, 0.106375135, -0.0488658, -0.016322287, 0.043832228, -0.01509801, 0.044846117, 0.18783608, -0.013360183, -0.028534602, -0.008298214, 0.08543058, -0.0078691505, 0.013635549, 0.028154565, 0.0059766313, -0.008999192, -0.013031405, 0.16779998, -0.005325976, -0.03564576, 0.010896735, 0.023286335, 0.011418291, 0.006014545, -0.102699615, -0.0059242523, -0.015290002], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, -1, 21, -1, 23, -1, -1, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [5.002885, 1.8829155, 2.970236, 1.3772461, 0.0, 3.6273825, 2.4810443, 1.3265439, 0.0, 2.1997871, 0.0, 3.1879683, 2.5562878, 0.0, 1.2347362, 0.0, 3.587086, 0.0, 0.0, 0.0, 0.0, 1.6377131, 0.0, 0.68723536, 0.0, 2.7298474, 0.0, 0.0, 0.0, 0.0, 0.54539037, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 16, 16, 21, 21, 23, 23, 25, 25, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, -1, 22, -1, 24, -1, -1, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.006563564, 0.46153846, -0.07692308, 1.0, -0.016322287, 1.0, -0.01509801, 1.0, 1.0, -0.013360183, 1.0, -0.008298214, 1.0, -0.0078691505, 0.013635549, 0.028154565, 0.0059766313, 1.3846154, -0.013031405, 1.0, -0.005325976, 0.03846154, 0.010896735, 0.023286335, 0.011418291, 0.006014545, 0.8076923, -0.0059242523, -0.015290002], "split_indices": [71, 121, 39, 40, 0, 1, 1, 5, 0, 89, 0, 109, 109, 0, 73, 0, 97, 0, 0, 0, 0, 1, 0, 53, 0, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1019.0, 1036.0, 892.0, 127.0, 541.0, 495.0, 770.0, 122.0, 417.0, 124.0, 282.0, 213.0, 149.0, 621.0, 103.0, 314.0, 120.0, 162.0, 123.0, 90.0, 521.0, 100.0, 197.0, 117.0, 425.0, 96.0, 89.0, 108.0, 175.0, 250.0, 134.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0024773022, -0.009596433, 0.017580248, -0.02368213, 0.01314206, 0.013452125, -0.067326345, 0.07449823, -0.039903123, -0.11784105, -0.020651234, -0.010830309, 0.16134405, 0.00835035, -0.08147168, -0.08277159, -0.023434954, -0.07130583, 0.031716295, -0.0073021366, 0.26613244, -0.13930741, -0.02717689, -0.0034132847, -0.013654821, -0.017094297, 0.0017750364, 0.012844129, -0.009908224, 0.040680956, 0.013587579, -0.022040732, -0.005642505, -0.010059494, 0.0059293215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, -1, 19, -1, 21, 23, -1, 25, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.338131, 3.8494945, 0.0, 2.8556657, 0.0, 3.1007876, 1.909801, 7.0487356, 2.6059563, 1.5894098, 1.1167682, 0.0, 7.3921924, 0.0, 1.1932676, 0.7820723, 0.0, 1.8988878, 2.6188583, 0.0, 3.8114176, 1.2368016, 1.2442994, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, -1, 20, -1, 22, 24, -1, 26, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.017580248, 1.0, 0.01314206, -0.03846154, 1.0, 1.0, 0.0, 1.0, 1.0, -0.010830309, -0.5, 0.00835035, 0.65384614, 1.0, -0.023434954, 1.0, 1.0, -0.0073021366, 1.0, 1.0, 1.0, -0.0034132847, -0.013654821, -0.017094297, 0.0017750364, 0.012844129, -0.009908224, 0.040680956, 0.013587579, -0.022040732, -0.005642505, -0.010059494, 0.0059293215], "split_indices": [0, 125, 0, 106, 0, 1, 39, 15, 0, 50, 111, 0, 1, 0, 1, 126, 0, 124, 3, 0, 39, 108, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1938.0, 135.0, 1762.0, 176.0, 952.0, 810.0, 444.0, 508.0, 389.0, 421.0, 143.0, 301.0, 128.0, 380.0, 299.0, 90.0, 214.0, 207.0, 93.0, 208.0, 184.0, 196.0, 157.0, 142.0, 101.0, 113.0, 119.0, 88.0, 100.0, 108.0, 93.0, 91.0, 106.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.009307067, -0.01844904, 0.012505221, -0.030833421, 0.01072453, -0.00021351739, -0.06730378, 0.047323935, -0.057106175, -0.05476286, -0.014501265, 0.026143009, 0.004010502, -0.015810223, 0.0047585615, -0.075885005, 0.008520037, -0.012544723, 0.052762, -0.004861923, 0.008288427, -0.053245563, -0.016796475, -0.009844864, 0.13809872, -0.0890106, 0.0033757288, 0.00619025, 0.021737352, -0.017955167, -0.044527356, 0.0011609144, -0.012149805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, -1, 17, -1, 19, 21, -1, -1, 23, -1, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [2.5450556, 3.0198941, 0.0, 1.9721253, 0.0, 2.5963507, 0.7854793, 4.850129, 2.7304177, 2.051689, 0.0, 0.0, 2.7453973, 0.0, 1.1301183, 1.2570333, 0.0, 0.0, 4.0776067, 0.0, 0.0, 1.5060436, 0.0, 0.0, 1.2201679, 1.3814523, 0.0, 0.0, 0.0, 0.0, 0.9937993, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 12, 12, 14, 14, 15, 15, 18, 18, 21, 21, 24, 24, 25, 25, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, -1, 18, -1, 20, 22, -1, -1, 24, -1, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [5.0, 1.0, 0.012505221, 1.0, 0.01072453, 0.115384616, 1.3461539, 1.0, 0.65384614, 0.8076923, -0.014501265, 0.026143009, 1.0, -0.015810223, 1.0, 1.0, 0.008520037, -0.012544723, 1.0, -0.004861923, 0.008288427, 1.0, -0.016796475, -0.009844864, 1.0, 1.0, 0.0033757288, 0.00619025, 0.021737352, -0.017955167, 1.0, 0.0011609144, -0.012149805], "split_indices": [0, 125, 0, 106, 0, 1, 1, 81, 1, 1, 0, 0, 17, 0, 39, 50, 0, 0, 15, 0, 0, 124, 0, 0, 50, 115, 0, 0, 0, 0, 62, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1940.0, 132.0, 1766.0, 174.0, 960.0, 806.0, 523.0, 437.0, 694.0, 112.0, 88.0, 435.0, 166.0, 271.0, 603.0, 91.0, 119.0, 316.0, 161.0, 110.0, 484.0, 119.0, 114.0, 202.0, 343.0, 141.0, 103.0, 99.0, 113.0, 230.0, 133.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0002767043, -0.035635635, 0.035262942, -0.04953383, 0.008420345, -0.009337387, 0.08386929, 0.01214619, -0.07301323, 0.03164776, -0.015177759, 0.028115569, 0.1580347, 0.008607701, -0.005295705, -0.05301485, -0.015892629, -0.012579711, 0.08479162, 0.10570592, -0.011620245, 0.029476894, 0.0039135343, -0.101082206, 0.0034205033, -0.027750965, 0.026709203, 0.014062575, 0.006925112, -0.020042729, -0.003857293, -0.0061885538, 0.010614149, -0.004792286, -0.0010623854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, -1, -1, 23, -1, -1, 25, 27, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.5970566, 1.6988577, 2.2697487, 1.3236644, 0.0, 3.187512, 2.0716338, 1.2129105, 1.1373959, 3.5477047, 0.0, 3.2025383, 3.4953856, 0.0, 0.0, 1.4567194, 0.0, 0.0, 6.503751, 0.23677635, 0.0, 0.0, 0.0, 1.8008964, 1.6569505, 0.06771514, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, -1, -1, 24, -1, -1, 26, 28, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.1538463, 1.0, 0.0, 0.008420345, 0.46153846, -0.07692308, 1.0, 0.96153843, -0.5, -0.015177759, -0.30769232, 0.34615386, 0.008607701, -0.005295705, 1.0, -0.015892629, -0.012579711, 1.0, 1.0, -0.011620245, 0.029476894, 0.0039135343, 1.0, 1.0, 1.0, 0.026709203, 0.014062575, 0.006925112, -0.020042729, -0.003857293, -0.0061885538, 0.010614149, -0.004792286, -0.0010623854], "split_indices": [71, 1, 39, 0, 0, 1, 1, 69, 1, 1, 0, 1, 1, 0, 0, 83, 0, 0, 42, 108, 0, 0, 0, 69, 12, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1020.0, 1047.0, 914.0, 106.0, 546.0, 501.0, 252.0, 662.0, 424.0, 122.0, 286.0, 215.0, 118.0, 134.0, 537.0, 125.0, 107.0, 317.0, 186.0, 100.0, 100.0, 115.0, 290.0, 247.0, 196.0, 121.0, 95.0, 91.0, 112.0, 178.0, 151.0, 96.0, 90.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013885472, 0.0064495434, -0.016425585, -0.03059243, 0.042455383, -0.045369595, 0.009841218, 0.023751907, 0.01584438, -0.026323486, -0.014424724, -0.009044769, 0.13230892, -0.055530943, 0.00756338, -0.037003107, 0.0077956007, 0.0023161157, 0.022343226, -0.10298634, -0.017806133, 0.015246909, -0.095673695, -0.01747072, -0.0045609637, -0.08670539, 0.007875719, -0.004684202, 0.008070502, -0.005152375, -0.015894009, -0.015247017, -0.0021647715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, 15, 17, 19, -1, 21, -1, -1, -1, 23, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.6412208, 2.632778, 0.0, 1.8548512, 2.1715553, 1.644063, 0.0, 3.0689871, 0.0, 2.179832, 0.0, 1.6102469, 1.9891813, 1.0186496, 0.0, 1.535835, 0.0, 0.0, 0.0, 1.0370061, 2.1090455, 1.0770197, 0.6591964, 0.0, 0.0, 0.79152274, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, 16, 18, 20, -1, 22, -1, -1, -1, 24, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.016425585, 3.1538463, 1.0, 2.0, 0.009841218, 2.0, 0.01584438, 1.0, -0.014424724, 1.0, -0.26923078, 1.0, 0.00756338, 1.0, 0.0077956007, 0.0023161157, 0.022343226, 0.34615386, 1.0, 1.0, 1.0, -0.01747072, -0.0045609637, 1.0, 0.007875719, -0.004684202, 0.008070502, -0.005152375, -0.015894009, -0.015247017, -0.0021647715], "split_indices": [117, 71, 0, 1, 125, 0, 0, 0, 0, 62, 0, 116, 1, 17, 0, 121, 0, 0, 0, 1, 2, 109, 106, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1974.0, 95.0, 973.0, 1001.0, 873.0, 100.0, 862.0, 139.0, 732.0, 141.0, 662.0, 200.0, 569.0, 163.0, 501.0, 161.0, 91.0, 109.0, 252.0, 317.0, 265.0, 236.0, 112.0, 140.0, 185.0, 132.0, 136.0, 129.0, 139.0, 97.0, 92.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0062503195, -0.025064833, 0.036613826, -0.0104319975, -0.012028583, 0.059364833, -0.05258286, 0.005640914, -0.011913567, 0.087447576, -0.006798092, 0.0019989244, -0.01105186, -0.0064532086, 0.024453746, 0.04872929, 0.020492839, -0.010757799, 0.010414447, 0.017009335, -0.0042154603, -0.02479075, 0.019982567, -0.009964484, 0.046373595, 0.009000774, -0.011241802, 0.0043514543, 0.01628895, 0.006155865, -0.006533728], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, -1, -1, 19, 21, -1, -1, -1, -1, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1], "loss_changes": [1.9720378, 1.4226131, 2.1368682, 1.5462584, 0.0, 1.5588927, 0.8997673, 1.0178378, 0.0, 2.6791573, 2.7951949, 0.0, 0.0, 0.0, 2.5386267, 4.9211154, 0.0, 0.0, 0.0, 0.0, 2.4524624, 2.9977248, 0.0, 0.0, 1.6255534, 0.0, 0.0, 0.972754, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 20, 20, 21, 21, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, -1, -1, 20, 22, -1, -1, -1, -1, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.012028583, 0.1923077, 1.0, 1.0, -0.011913567, -0.115384616, 1.0, 0.0019989244, -0.01105186, -0.0064532086, -0.30769232, 1.0, 0.020492839, -0.010757799, 0.010414447, 0.017009335, 1.0, 1.0, 0.019982567, -0.009964484, 1.0, 0.009000774, -0.011241802, 1.0, 0.01628895, 0.006155865, -0.006533728], "split_indices": [71, 40, 119, 64, 0, 1, 13, 5, 0, 1, 39, 0, 0, 0, 1, 61, 0, 0, 0, 0, 126, 124, 0, 0, 2, 0, 0, 17, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1021.0, 1053.0, 885.0, 136.0, 839.0, 214.0, 771.0, 114.0, 589.0, 250.0, 95.0, 119.0, 163.0, 608.0, 443.0, 146.0, 131.0, 119.0, 100.0, 508.0, 298.0, 145.0, 176.0, 332.0, 129.0, 169.0, 244.0, 88.0, 134.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023787816, -0.010851498, 0.011856137, -0.018291118, 0.013422108, 0.015281791, -0.053171095, 0.09465072, -0.040377654, -0.029338263, -0.021246402, -0.076076135, 0.24300078, -0.103970155, 0.027774299, 0.035734233, -0.07139565, -0.020058194, 0.0049828575, 0.012198923, 0.040208336, -0.016253231, -0.0030535038, 0.016247494, -0.0047499575, 0.011595263, -0.004293153, -0.019120552, -0.032239903, -0.01254378, 0.021543887, 0.01610265, -0.010064746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [2.1129136, 2.0797815, 0.0, 2.146484, 0.0, 4.126067, 3.4129648, 9.751027, 2.3793406, 2.1401615, 0.0, 2.805981, 3.9656696, 1.2213469, 2.6869516, 1.937306, 2.2283406, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7944881, 0.0, 3.8688898, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 28, 28, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [5.0, 1.0, 0.011856137, 1.0, 0.013422108, -0.23076923, 2.0, -0.5, 0.42307693, 1.0, -0.021246402, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, -0.020058194, 0.0049828575, 0.012198923, 0.040208336, -0.016253231, -0.0030535038, 0.016247494, -0.0047499575, 0.011595263, -0.004293153, -0.019120552, -0.07692308, -0.01254378, 0.5769231, 0.01610265, -0.010064746], "split_indices": [0, 114, 0, 59, 0, 1, 0, 1, 1, 122, 0, 113, 61, 12, 124, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1927.0, 135.0, 1833.0, 94.0, 934.0, 899.0, 385.0, 549.0, 782.0, 117.0, 179.0, 206.0, 284.0, 265.0, 307.0, 475.0, 90.0, 89.0, 117.0, 89.0, 158.0, 126.0, 95.0, 170.0, 152.0, 155.0, 117.0, 358.0, 131.0, 227.0, 106.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017744867, -0.01046498, 0.01325143, -0.00082779414, -0.09511723, -0.0132291205, 0.02331063, -0.017791707, -0.0023924827, -0.031480577, 0.05950264, -0.015485201, -0.014650903, 0.019810528, -0.008162003, -0.025922026, 0.010206639, -0.072809845, 0.0017369033, -0.030832369, -0.01796275, -0.045374036, 0.08564111, 0.0031307135, -0.008916743, -0.008176793, -0.018435271, -0.0011071571, 0.019977953, 0.0028707834, -0.006023384], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [2.4192653, 1.5883809, 0.0, 5.07111, 1.1730484, 2.2035842, 0.0, 0.0, 0.0, 2.4415772, 6.513472, 1.429298, 0.0, 0.0, 0.0, 1.3876476, 0.0, 1.7801218, 2.6602383, 1.0331001, 0.0, 2.2281072, 2.6713495, 0.0, 0.0, 0.6528357, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.01325143, 1.0, 1.0, 1.0, 0.02331063, -0.017791707, -0.0023924827, 1.0, 1.0, 3.1923077, -0.014650903, 0.019810528, -0.008162003, 1.0, 0.010206639, 1.0, 1.0, 1.0, -0.01796275, 1.0, 1.0, 0.0031307135, -0.008916743, 0.1923077, -0.018435271, -0.0011071571, 0.019977953, 0.0028707834, -0.006023384], "split_indices": [102, 119, 0, 125, 93, 105, 0, 0, 0, 64, 109, 1, 0, 0, 0, 17, 0, 2, 109, 12, 0, 113, 93, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1947.0, 126.0, 1748.0, 199.0, 1660.0, 88.0, 92.0, 107.0, 1327.0, 333.0, 1165.0, 162.0, 168.0, 165.0, 1070.0, 95.0, 397.0, 673.0, 285.0, 112.0, 431.0, 242.0, 138.0, 147.0, 340.0, 91.0, 131.0, 111.0, 199.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010972522, -0.009243747, 0.011265634, -0.01832529, 0.012890342, -0.038492784, 0.059611954, -0.024025373, -0.13614778, -0.007131051, 0.17145528, -0.011874235, -0.012366467, -0.008996998, -0.018437792, 0.03580284, 0.0014250105, 0.005133078, -0.01571695, -0.028315468, 0.10167477, 0.05597038, -0.07545841, 0.0021194748, 0.023276592, 0.01618187, -0.0013929425, -0.04638665, -0.015616158, -0.012000459, 0.0010997575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, -1, -1, 19, -1, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.8997202, 2.4000294, 0.0, 2.821379, 0.0, 2.014674, 5.4031944, 1.5037272, 0.4097967, 0.0, 5.83672, 2.7354882, 0.0, 0.0, 0.0, 0.0, 0.0, 3.2001169, 0.0, 2.9244833, 2.690305, 1.9532771, 1.1073978, 0.0, 0.0, 0.0, 0.0, 1.4659047, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, -1, -1, 20, -1, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.011265634, 1.0, 0.012890342, 1.0, 1.0, 1.0, 1.0, -0.007131051, -0.3846154, 2.0, -0.012366467, -0.008996998, -0.018437792, 0.03580284, 0.0014250105, 1.0, -0.01571695, 1.0, 1.0, 0.15384616, 1.0, 0.0021194748, 0.023276592, 0.01618187, -0.0013929425, 1.0, -0.015616158, -0.012000459, 0.0010997575], "split_indices": [0, 102, 0, 113, 0, 64, 109, 119, 126, 0, 1, 0, 0, 0, 0, 0, 0, 121, 0, 122, 111, 1, 109, 0, 0, 0, 0, 137, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1913.0, 137.0, 1795.0, 118.0, 1426.0, 369.0, 1242.0, 184.0, 170.0, 199.0, 1107.0, 135.0, 94.0, 90.0, 91.0, 108.0, 991.0, 116.0, 736.0, 255.0, 264.0, 472.0, 158.0, 97.0, 105.0, 159.0, 347.0, 125.0, 152.0, 195.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.001984048, 0.007380652, -0.08232105, 0.014755747, -0.013542061, -0.003225831, -0.0123898545, 0.0046289614, 0.010897629, -0.0045715957, 0.012612564, -0.011771662, 0.0041635605, 0.04089454, -0.022302993, 0.09327754, -0.017867189, 0.008383082, -0.041642938, -0.0010571711, 0.16007788, -0.01108818, 0.005716458, -0.019353941, -0.01760734, 0.008214479, 0.023078002, 0.019096093, -0.07366463, 0.008108164, -0.0052759754, -0.0005100013, -0.013168082], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, -1, 13, 15, 17, 19, 21, -1, 23, -1, 25, -1, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [1.5565748, 1.9515291, 0.44959998, 1.6812143, 0.0, 0.0, 0.0, 1.7784793, 0.0, 1.4617546, 0.0, 0.0, 1.3347515, 1.7699168, 1.6379923, 2.1088989, 1.8913231, 0.0, 2.0225155, 0.0, 1.0193553, 0.0, 0.0, 1.2090957, 0.0, 0.0, 0.0, 1.5099143, 0.9546852, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 20, 20, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, -1, 14, 16, 18, 20, 22, -1, 24, -1, 26, -1, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.07692308, 1.0, -0.013542061, -0.003225831, -0.0123898545, 5.0, 0.010897629, -0.53846157, 0.012612564, -0.011771662, -0.03846154, 1.0, 1.0, 1.0, 1.0, 0.008383082, 1.0, -0.0010571711, 1.0, -0.01108818, 0.005716458, 1.0, -0.01760734, 0.008214479, 0.023078002, 1.0, 1.0, 0.008108164, -0.0052759754, -0.0005100013, -0.013168082], "split_indices": [40, 117, 1, 125, 0, 0, 0, 0, 0, 1, 0, 0, 1, 122, 89, 13, 115, 0, 64, 0, 113, 0, 0, 23, 0, 0, 0, 13, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1853.0, 216.0, 1762.0, 91.0, 98.0, 118.0, 1591.0, 171.0, 1479.0, 112.0, 106.0, 1373.0, 575.0, 798.0, 304.0, 271.0, 123.0, 675.0, 119.0, 185.0, 121.0, 150.0, 579.0, 96.0, 88.0, 97.0, 339.0, 240.0, 182.0, 157.0, 110.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0048072687, -0.0022022217, 0.010571257, -0.020701027, 0.03320259, -0.1284567, 0.000707381, 0.1140015, -0.039261755, -0.00072395796, -0.021589197, 0.033891547, -0.069882594, 0.21736021, -0.0050005703, -0.10171099, 0.0100092525, 0.052844476, -0.008620811, -0.022962224, -0.015850995, 0.03389507, 0.009827671, -0.020846397, -0.0033447158, 0.069187105, -0.0024782987, 0.0031507802, -0.00993387, 0.01091448, 0.116174534, 0.010392856, -0.006647321, 0.02215908, -0.0017914929], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, -1, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [1.4591494, 1.2633922, 0.0, 2.922814, 3.8760364, 2.2257166, 2.4759903, 5.3058443, 3.0371964, 0.0, 0.0, 1.6366167, 1.4055482, 2.7800474, 0.0, 1.7562544, 0.0, 0.7878237, 0.0, 0.9194107, 0.0, 0.0, 0.0, 0.0, 0.0, 1.404635, 0.0, 0.0, 0.0, 1.6483754, 4.014401, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 25, 25, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, -1, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.010571257, -0.46153846, 1.0, -0.5769231, 1.0, 1.0, 1.0, -0.00072395796, -0.021589197, 2.0, 1.0, -0.26923078, -0.0050005703, 1.0, 0.0100092525, 1.0, -0.008620811, 1.0, -0.015850995, 0.03389507, 0.009827671, -0.020846397, -0.0033447158, 1.0, -0.0024782987, 0.0031507802, -0.00993387, 1.0, 1.0, 0.010392856, -0.006647321, 0.02215908, -0.0017914929], "split_indices": [0, 50, 0, 1, 126, 1, 23, 97, 23, 0, 0, 0, 115, 1, 0, 39, 0, 80, 0, 127, 0, 0, 0, 0, 0, 81, 0, 0, 0, 39, 105, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1929.0, 134.0, 1267.0, 662.0, 210.0, 1057.0, 313.0, 349.0, 88.0, 122.0, 719.0, 338.0, 192.0, 121.0, 241.0, 108.0, 621.0, 98.0, 221.0, 117.0, 95.0, 97.0, 94.0, 147.0, 513.0, 108.0, 129.0, 92.0, 229.0, 284.0, 104.0, 125.0, 159.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0003344093, 0.012751719, -0.06054931, 0.019513069, 0.0023332618, -0.020307837, -0.00053707045, -0.014235809, 0.014471155, 0.009716981, -0.008991238, 0.0026945563, 0.01761107, -0.016421383, 0.06542057, -0.0020348039, -0.011987294, 0.028856425, -0.020622106, 0.03314654, -0.03797112, -0.012486533, 0.011398319, 0.101654835, -0.043925274, -0.10856692, 0.012826587, 0.021820648, 0.0011368325, 0.0018474177, -0.011094694, -0.017541861, -0.0036736892, 0.011373847, -0.007357055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, -1, 7, -1, 9, -1, 11, -1, -1, 13, -1, 15, 17, 19, -1, -1, 21, 23, 25, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5664567, 3.2700844, 3.0022736, 0.0, 2.859172, 0.0, 2.156948, 0.0, 2.8591528, 0.0, 0.0, 1.6786934, 0.0, 1.5969605, 6.2783613, 1.1909592, 0.0, 0.0, 3.311479, 2.513308, 1.671125, 0.0, 0.0, 2.651806, 0.9367941, 0.93638134, 2.362713, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, -1, 8, -1, 10, -1, 12, -1, -1, 14, -1, 16, 18, 20, -1, -1, 22, 24, 26, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.34615386, 0.019513069, -0.5, -0.020307837, 1.0, -0.014235809, 5.0, 0.009716981, -0.008991238, 1.0, 0.01761107, 2.0, -0.15384616, 1.0, -0.011987294, 0.028856425, 1.0, 1.0, 1.0, -0.012486533, 0.011398319, 1.0, 1.0, 1.0, 1.0, 0.021820648, 0.0011368325, 0.0018474177, -0.011094694, -0.017541861, -0.0036736892, 0.011373847, -0.007357055], "split_indices": [7, 1, 1, 0, 1, 0, 69, 0, 0, 0, 0, 61, 0, 0, 1, 97, 0, 0, 53, 13, 81, 0, 0, 122, 39, 13, 122, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1721.0, 351.0, 93.0, 1628.0, 104.0, 247.0, 126.0, 1502.0, 118.0, 129.0, 1400.0, 102.0, 1073.0, 327.0, 942.0, 131.0, 91.0, 236.0, 476.0, 466.0, 133.0, 103.0, 252.0, 224.0, 195.0, 271.0, 110.0, 142.0, 116.0, 108.0, 101.0, 94.0, 125.0, 146.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017459497, -0.007459486, 0.011423292, -0.0022800926, -0.009584176, 0.0061106007, -0.06856257, 0.013338823, -0.011925885, -0.011688768, -0.0025069946, 0.0013052736, 0.011944595, -0.030621467, 0.035789948, 0.014150224, -0.057712484, -0.010040165, 0.07021757, -0.0067555485, -0.12909108, -0.03943037, 0.13509801, 0.06345865, -0.008635887, -0.003691385, -0.028334683, 0.004189229, -0.013882476, 0.19576678, 0.0033020389, -0.0077454695, 0.020877557, 0.013718354, 0.02626345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, -1, 19, -1, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.3690335, 0.90134215, 0.0, 1.0344503, 0.0, 1.496133, 0.4392727, 1.9931551, 0.0, 0.0, 0.0, 1.5435785, 0.0, 3.3946686, 3.1602192, 0.0, 2.28782, 0.0, 3.827336, 2.0512671, 3.7253423, 1.6166036, 2.093207, 3.9930344, 0.0, 0.0, 0.0, 0.0, 0.0, 0.830472, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 16, 16, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, -1, 20, -1, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.011423292, 1.0, -0.009584176, 1.0, 1.0, 1.0, -0.011925885, -0.011688768, -0.0025069946, 1.0, 0.011944595, -1.0, -1.0, 0.014150224, 1.0, -0.010040165, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.008635887, -0.003691385, -0.028334683, 0.004189229, -0.013882476, 1.0, 0.0033020389, -0.0077454695, 0.020877557, 0.013718354, 0.02626345], "split_indices": [114, 43, 0, 40, 0, 117, 137, 125, 0, 0, 0, 122, 0, 0, 0, 0, 53, 0, 81, 97, 121, 15, 109, 15, 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1969.0, 97.0, 1860.0, 109.0, 1651.0, 209.0, 1561.0, 90.0, 99.0, 110.0, 1402.0, 159.0, 728.0, 674.0, 99.0, 629.0, 136.0, 538.0, 367.0, 262.0, 200.0, 338.0, 195.0, 172.0, 164.0, 98.0, 110.0, 90.0, 212.0, 126.0, 99.0, 96.0, 113.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005880987, -0.012015993, 0.008938337, -0.05851584, 0.0010199114, -0.022215081, 3.7246213e-05, 0.015835991, -0.081398524, 0.016128927, -0.07346366, -0.0039371676, 0.07596381, -0.01902704, 0.0010924799, -0.015075917, 0.0015426137, 0.04219587, -0.057828013, 0.020474268, -0.0036149544, 0.09424604, -0.0072122873, -0.016011678, -0.016286273, 0.02328324, 0.035167374, 0.0046273046, -0.008444294, 0.014085635, -0.0060112807], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [1.207468, 1.1765714, 0.0, 4.0720673, 1.8512148, 0.0, 3.7097297, 1.5277585, 2.321877, 0.0, 1.4772185, 2.4041054, 4.5912313, 0.0, 0.0, 0.0, 0.0, 3.100111, 1.9589057, 0.0, 0.0, 2.931123, 0.0, 1.3596492, 0.0, 0.0, 2.5275862, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 17, 17, 18, 18, 21, 21, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.008938337, -0.3846154, 1.0, -0.022215081, 1.0, 1.0, 1.0, 0.016128927, 1.0, 1.0, 1.0, -0.01902704, 0.0010924799, -0.015075917, 0.0015426137, 1.0, 1.0, 0.020474268, -0.0036149544, 0.0, -0.0072122873, 1.0, -0.016286273, 0.02328324, 0.42307693, 0.0046273046, -0.008444294, 0.014085635, -0.0060112807], "split_indices": [102, 5, 0, 1, 64, 0, 53, 0, 108, 0, 97, 106, 124, 0, 0, 0, 0, 121, 15, 0, 0, 0, 0, 108, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1941.0, 125.0, 425.0, 1516.0, 112.0, 313.0, 1285.0, 231.0, 98.0, 215.0, 967.0, 318.0, 106.0, 125.0, 115.0, 100.0, 521.0, 446.0, 148.0, 170.0, 358.0, 163.0, 319.0, 127.0, 107.0, 251.0, 167.0, 152.0, 119.0, 132.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038574485, 0.010712601, -0.010051612, -0.014393428, 0.00036640896, 0.008021121, -0.01204079, 0.014217566, -0.008929948, 0.04591374, -0.011722206, 0.01129866, 0.028860388, 0.009754532, -0.017758219, 0.019907134, -0.03370775, -0.01275412, 0.02811385, -0.071778424, 0.009970804, 0.072295174, -0.060441714, -0.099692054, 0.0019165938, 0.006165545, 0.13699958, -0.013558722, 0.005996185, -0.004239902, -0.01621217, -0.008182459, 0.008189478, 0.005185549, 0.020264499], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, -1, 19, -1, 21, 23, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.4175211, 0.0, 2.72403, 0.0, 1.6751803, 1.027583, 0.0, 1.3171511, 0.0, 6.0569324, 3.1382387, 5.3325653, 0.0, 1.9661113, 0.0, 0.0, 2.5853274, 0.0, 2.6918013, 1.0052807, 0.0, 1.9640048, 2.0719433, 1.0837648, 0.0, 1.5125977, 1.2967215, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 16, 16, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25, 26, 26], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, -1, 20, -1, 22, 24, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010712601, -0.5, -0.014393428, 1.0, 1.0, -0.01204079, 1.0, -0.008929948, 0.65384614, 1.0, -0.34615386, 0.028860388, -0.34615386, -0.017758219, 0.019907134, 1.0, -0.01275412, 1.0, 0.26923078, 0.009970804, 1.0, 2.3076923, -0.07692308, 0.0019165938, 1.0, 1.0, -0.013558722, 0.005996185, -0.004239902, -0.01621217, -0.008182459, 0.008189478, 0.005185549, 0.020264499], "split_indices": [1, 0, 1, 0, 84, 43, 0, 124, 0, 1, 62, 1, 0, 1, 0, 0, 62, 0, 1, 1, 0, 13, 1, 1, 0, 93, 2, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 109.0, 1953.0, 141.0, 1812.0, 1704.0, 108.0, 1602.0, 102.0, 721.0, 881.0, 631.0, 90.0, 780.0, 101.0, 122.0, 509.0, 92.0, 688.0, 396.0, 113.0, 459.0, 229.0, 303.0, 93.0, 227.0, 232.0, 141.0, 88.0, 158.0, 145.0, 105.0, 122.0, 101.0, 131.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017739515, -0.007650454, 0.011617582, -0.01602066, 0.07494172, -0.015537525, -0.007667653, 0.019475026, -0.0051674102, 0.015758194, -0.02518844, 0.012361292, -0.011863132, -0.10047893, -0.00023026217, -0.043477226, 0.01344295, -0.017840385, -0.0018557834, -0.02129311, 0.011486312, 0.02928389, -0.10280553, -0.08088725, 0.021561097, 0.014759377, -0.013742544, -0.0032302644, -0.020203179, -0.00086365436, -0.01882111, 0.09888502, -0.0074242735, -0.00039056325, 0.022109171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, 13, -1, 15, 17, 19, 21, -1, -1, -1, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.4313176, 1.3598133, 0.0, 2.0789566, 2.7457085, 0.0, 0.69159025, 0.0, 0.0, 2.1479247, 1.811465, 0.0, 2.6546977, 1.5320861, 1.7551165, 2.037527, 0.0, 0.0, 0.0, 1.5629624, 0.0, 4.181352, 1.8188908, 1.9850805, 2.6372225, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.474657, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 31, 31], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, 14, -1, 16, 18, 20, 22, -1, -1, -1, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.011617582, -0.53846157, 1.0, -0.015537525, 1.0, 0.019475026, -0.0051674102, -0.30769232, 1.0, 0.012361292, 1.0, 1.0, 1.0, 1.0, 0.01344295, -0.017840385, -0.0018557834, 1.0, 0.011486312, 1.0, 1.0, 1.0, 0.1923077, 0.014759377, -0.013742544, -0.0032302644, -0.020203179, -0.00086365436, -0.01882111, 1.0, -0.0074242735, -0.00039056325, 0.022109171], "split_indices": [114, 125, 0, 1, 15, 0, 53, 0, 0, 1, 5, 0, 58, 97, 61, 17, 0, 0, 0, 106, 0, 61, 113, 109, 1, 0, 0, 0, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1967.0, 98.0, 1786.0, 181.0, 101.0, 1685.0, 93.0, 88.0, 721.0, 964.0, 147.0, 574.0, 240.0, 724.0, 472.0, 102.0, 123.0, 117.0, 612.0, 112.0, 212.0, 260.0, 256.0, 356.0, 124.0, 88.0, 152.0, 108.0, 153.0, 103.0, 197.0, 159.0, 107.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007408494, 0.0042652013, -0.010158441, -0.0012493202, 0.0087073445, 0.008164457, -0.07763675, -0.010359736, 0.07850192, -0.014684728, -0.0023738262, -0.021514332, 0.013849211, 0.13427605, -0.0063257306, -0.007908481, -0.011757709, 0.0032475737, 0.021923719, -0.05207669, 0.0256239, -0.10410354, 0.002775137, 0.10672591, -0.021239784, -0.0037827601, -0.02075231, 0.0052505396, -0.0064413585, 0.02120083, -0.001856011, -0.09919922, 0.053892095, -0.0030850766, -0.016903348, -0.0015472496, 0.0136656705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, 17, -1, 19, -1, -1, -1, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [1.0354025, 0.8922902, 0.0, 1.3173809, 0.0, 2.125103, 0.74979854, 2.1435528, 2.6882095, 0.0, 0.0, 1.5697252, 0.0, 2.1103725, 0.0, 1.5580808, 0.0, 0.0, 0.0, 1.2956111, 2.272842, 1.5970349, 0.7384301, 2.8887007, 2.2198937, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.88778937, 1.1080005, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 31, 31, 32, 32], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, 18, -1, 20, -1, -1, -1, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010158441, 1.0, 0.0087073445, 1.0, -0.23076923, 3.5, 1.0, -0.014684728, -0.0023738262, 1.1153846, 0.013849211, 1.0, -0.0063257306, -0.15384616, -0.011757709, 0.0032475737, 0.021923719, 1.0, 1.0, -0.34615386, -0.30769232, 1.0, 1.0, -0.0037827601, -0.02075231, 0.0052505396, -0.0064413585, 0.02120083, -0.001856011, 1.0, 0.42307693, -0.0030850766, -0.016903348, -0.0015472496, 0.0136656705], "split_indices": [117, 102, 0, 119, 0, 61, 1, 1, 126, 0, 0, 1, 0, 13, 0, 1, 0, 0, 0, 69, 16, 1, 1, 69, 124, 0, 0, 0, 0, 0, 0, 93, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 1954.0, 97.0, 1832.0, 122.0, 1631.0, 201.0, 1291.0, 340.0, 88.0, 113.0, 1201.0, 90.0, 244.0, 96.0, 1052.0, 149.0, 111.0, 133.0, 454.0, 598.0, 233.0, 221.0, 219.0, 379.0, 142.0, 91.0, 127.0, 94.0, 119.0, 100.0, 186.0, 193.0, 94.0, 92.0, 105.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0003598195, -0.0055957576, 0.009355272, -0.012298925, 0.00870298, -0.024534527, 0.055457354, -0.06594752, 0.015977023, 0.015925027, -0.0033804544, -0.08801299, 0.0016048879, 0.05415552, -0.029133318, -0.023813812, -0.06093159, -0.0057167965, 0.08907376, -0.008890064, 0.015985137, -0.016399218, -0.019989708, -0.001695844, 0.17703232, -0.0062824003, 0.011004769, 0.0052064885, -0.06482369, 0.032733407, 0.0026730546, -0.014362176, -0.0007240465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, 17, 19, -1, 21, -1, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.1549917, 1.21445, 0.0, 1.512167, 0.0, 2.5920532, 2.5848665, 1.3822973, 1.3450733, 0.0, 0.0, 2.4474902, 0.0, 1.6442945, 0.9653864, 0.0, 2.1519413, 0.0, 3.003114, 0.0, 1.5122501, 0.0, 1.1791304, 0.0, 3.9759474, 0.0, 0.0, 0.0, 1.0209252, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, 18, 20, -1, 22, -1, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.009355272, 1.2692307, 0.00870298, 1.0, 1.4615384, 1.0, 1.0, 0.015925027, -0.0033804544, -0.46153846, 0.0016048879, 1.0, -0.115384616, -0.023813812, 0.0, -0.0057167965, 1.0, -0.008890064, 1.0, -0.016399218, -0.07692308, -0.001695844, -0.15384616, -0.0062824003, 0.011004769, 0.0052064885, 1.0, 0.032733407, 0.0026730546, -0.014362176, -0.0007240465], "split_indices": [102, 0, 0, 1, 0, 124, 1, 62, 59, 0, 0, 1, 0, 89, 1, 0, 0, 0, 93, 0, 93, 0, 1, 0, 1, 0, 0, 0, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2081.0, 1956.0, 125.0, 1824.0, 132.0, 1545.0, 279.0, 764.0, 781.0, 129.0, 150.0, 602.0, 162.0, 423.0, 358.0, 92.0, 510.0, 101.0, 322.0, 154.0, 204.0, 145.0, 365.0, 146.0, 176.0, 111.0, 93.0, 140.0, 225.0, 88.0, 88.0, 95.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0018653393, 0.010524076, -0.0038571327, -0.011552516, 0.005108461, 0.015532553, -0.051225986, 0.0036892926, 0.08923018, 0.009075946, -0.014476935, -0.048425544, 0.03104807, -0.0019863602, 0.01820556, 0.008516497, -0.12985879, 0.067499064, -0.044989478, -0.009227301, 0.014085754, -0.021278093, -0.0043291706, 0.0021074403, 0.10552571, -0.01090501, 0.0046366504, -0.0057793944, 0.0048147375, 0.023583619, 0.05690239, 0.013589511, 0.0014228147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [1.2180285, 0.0, 1.9532834, 0.0, 1.0605474, 1.3301778, 3.745467, 1.8720729, 2.1367295, 0.0, 0.0, 2.0959134, 2.386386, 0.0, 0.0, 3.5480647, 1.3351681, 1.4472151, 1.6327977, 0.0, 0.0, 0.0, 0.0, 0.5901811, 2.3316941, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9034152, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 24, 24, 30, 30], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.010524076, -0.5, -0.011552516, 1.0, 1.0, -0.03846154, -0.115384616, 1.0, 0.009075946, -0.014476935, -0.26923078, 1.0, -0.0019863602, 0.01820556, 1.0, 1.0, 1.0, 1.0, -0.009227301, 0.014085754, -0.021278093, -0.0043291706, 0.34615386, 1.0, -0.01090501, 0.0046366504, -0.0057793944, 0.0048147375, 0.023583619, 0.30769232, 0.013589511, 0.0014228147], "split_indices": [1, 0, 1, 0, 64, 42, 1, 1, 71, 0, 0, 1, 109, 0, 0, 109, 111, 59, 93, 0, 0, 0, 0, 1, 53, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 108.0, 1951.0, 145.0, 1806.0, 1524.0, 282.0, 1313.0, 211.0, 112.0, 170.0, 452.0, 861.0, 97.0, 114.0, 266.0, 186.0, 582.0, 279.0, 151.0, 115.0, 95.0, 91.0, 214.0, 368.0, 164.0, 115.0, 93.0, 121.0, 100.0, 268.0, 94.0, 174.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0039867265, 0.009008709, -0.00091688463, -0.008842123, 0.0059594926, 0.010860366, -0.007348129, 0.018584702, -0.04586048, 0.010467499, 0.013582386, -0.00045517445, -0.007979263, -0.018252257, 0.0372462, 0.008842666, -0.017998384, 0.013085546, 0.008352363, 0.088726684, -0.051433448, 0.014556492, -0.024492694, -0.004732709, 0.022809885, -0.12621786, 0.004880947, 0.08056144, -0.09597691, -0.020902194, -0.004253286, 0.00011220995, 0.016267847, -0.01486206, -0.0007173891], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, 21, 23, 25, -1, 27, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.869738, 0.0, 1.1727387, 0.0, 0.7035176, 0.74569875, 0.0, 1.4255774, 0.28594533, 1.0774779, 0.0, 0.0, 0.0, 2.9623034, 1.9609292, 2.7879422, 0.0, 0.0, 2.4967422, 4.7215657, 2.4738812, 0.0, 3.3568416, 0.0, 0.0, 1.3096671, 0.0, 1.1807209, 1.2435277, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 22, 22, 25, 25, 27, 27, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, 22, 24, 26, -1, 28, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009008709, -0.5, -0.008842123, 1.0, 1.0, -0.007348129, 1.0, 1.0, 1.0, 0.013582386, -0.00045517445, -0.007979263, 1.0, 0.0, 1.0, -0.017998384, 0.013085546, 1.0, 0.115384616, 1.0, 0.014556492, 1.0, -0.004732709, 0.022809885, 1.0, 0.004880947, 1.0, 1.0, -0.020902194, -0.004253286, 0.00011220995, 0.016267847, -0.01486206, -0.0007173891], "split_indices": [1, 0, 1, 0, 43, 40, 0, 125, 69, 13, 0, 0, 0, 64, 0, 124, 0, 0, 89, 1, 50, 0, 39, 0, 0, 122, 0, 93, 109, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 111.0, 1949.0, 142.0, 1807.0, 1702.0, 105.0, 1498.0, 204.0, 1401.0, 97.0, 92.0, 112.0, 676.0, 725.0, 579.0, 97.0, 171.0, 554.0, 249.0, 330.0, 107.0, 447.0, 126.0, 123.0, 189.0, 141.0, 181.0, 266.0, 95.0, 94.0, 92.0, 89.0, 167.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0022714743, 0.00951641, -0.053938158, 0.0006682518, 0.017870093, 0.0031656686, -0.018097896, -0.007385124, 0.0110370135, -0.040221047, 0.027154937, -0.023057057, -0.018331854, -0.0029101358, 0.109124824, 0.01000619, -0.04005201, -0.062074006, 0.057914462, 0.023499195, -0.005504964, -0.087732166, 0.0034334022, 0.0020563651, -0.016859913, -0.0037181717, 0.015368036, -0.034073118, -0.016343715, -0.04932371, 0.011130288, -0.0042265016, -0.0025881187, -0.011195766, 0.0034401224], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, 19, -1, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.8417549, 2.740954, 2.5662735, 1.5372391, 0.0, 0.0, 0.0, 1.838465, 0.0, 2.0410385, 1.9469006, 1.5525615, 0.0, 2.0800018, 4.3808036, 0.0, 1.3518512, 2.5792756, 2.5954869, 0.0, 0.0, 1.2633617, 1.9405911, 0.0, 0.0, 0.0, 0.0, 0.012213364, 0.0, 1.2008815, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, 20, -1, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.1923077, 1.0, 5.0, 0.017870093, 0.0031656686, -0.018097896, 1.0, 0.0110370135, 0.5769231, 1.0, 1.0, -0.018331854, 1.0, 1.0, 0.01000619, 1.0, 1.0, -0.07692308, 0.023499195, -0.005504964, 1.0, 1.0, 0.0020563651, -0.016859913, -0.0037181717, 0.015368036, -0.07692308, -0.016343715, 1.0, 0.011130288, -0.0042265016, -0.0025881187, -0.011195766, 0.0034401224], "split_indices": [1, 1, 17, 0, 0, 0, 0, 124, 0, 1, 61, 104, 0, 39, 39, 0, 59, 93, 1, 0, 0, 39, 105, 0, 0, 0, 0, 1, 0, 62, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1831.0, 236.0, 1740.0, 91.0, 141.0, 95.0, 1621.0, 119.0, 831.0, 790.0, 742.0, 89.0, 578.0, 212.0, 90.0, 652.0, 293.0, 285.0, 120.0, 92.0, 311.0, 341.0, 165.0, 128.0, 143.0, 142.0, 182.0, 129.0, 229.0, 112.0, 91.0, 91.0, 131.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0040120347, 0.009523006, -0.010779815, 0.0032206704, 0.07329407, -0.008383286, 0.009193916, 0.020604031, -0.006096061, 0.01750539, -0.045242485, 0.005374777, 0.01122712, -0.017333686, 0.007831755, 0.022078592, -0.039615434, 0.094273865, -0.002123228, -0.101163805, 0.009958739, 0.01918572, -0.0031731774, -0.050824024, 0.049133286, -0.016611783, -0.000892908, 0.006816981, -0.11081676, 0.103643775, -0.004242728, -0.0074870246, -0.014333977, 0.003025389, 0.018647986], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [1.2724168, 0.79095215, 0.0, 0.9313057, 3.1544602, 0.0, 0.7583007, 0.0, 0.0, 1.6714706, 3.5136712, 0.968694, 0.0, 0.0, 0.0, 1.6424217, 2.9901302, 2.9018688, 1.7573481, 1.4498255, 0.0, 0.0, 0.0, 2.5770946, 1.7119169, 0.0, 0.0, 0.0, 0.28058052, 1.3070555, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010779815, -0.53846157, 1.0, -0.008383286, 1.3461539, 0.020604031, -0.006096061, 0.88461536, 3.1923077, 1.0, 0.01122712, -0.017333686, 0.007831755, -0.30769232, 1.0, 1.0, 1.0, -0.07692308, 0.009958739, 0.01918572, -0.0031731774, 1.0, 1.0, -0.016611783, -0.000892908, 0.006816981, 1.0, 1.0, -0.004242728, -0.0074870246, -0.014333977, 0.003025389, 0.018647986], "split_indices": [117, 125, 0, 1, 15, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 105, 126, 124, 1, 0, 0, 0, 5, 15, 0, 0, 0, 115, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1968.0, 97.0, 1791.0, 177.0, 115.0, 1676.0, 89.0, 88.0, 1454.0, 222.0, 1289.0, 165.0, 109.0, 113.0, 940.0, 349.0, 236.0, 704.0, 242.0, 107.0, 133.0, 103.0, 361.0, 343.0, 142.0, 100.0, 121.0, 240.0, 215.0, 128.0, 114.0, 126.0, 114.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0028579745, 0.007777486, -0.008244282, 0.018042134, -0.03293982, 0.009387164, 0.009543753, -0.011382496, 0.022494378, 0.069060266, -0.009397768, -0.0026402832, 0.008591554, 0.016867498, -0.0182425, -0.020430733, 0.009546998, 0.0006042447, -0.0043079415, -0.0052933055, -0.010835662, 0.057996318, -0.05927564, 0.1672544, -0.049536623, -0.013096514, -0.014896409, 0.021663299, 0.012044757, -0.0048028077, -0.005111002, 0.007040014, -0.01252802], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, -1, 21, -1, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.86193675, 0.81165665, 0.0, 1.0389445, 1.7531672, 1.5637327, 0.0, 0.0, 0.71945953, 2.9046783, 1.227579, 0.0, 0.0, 0.0, 0.10736316, 1.2777324, 0.0, 0.0, 0.0, 2.7981312, 0.0, 4.4293146, 1.4062337, 0.4322033, 0.00045067072, 0.0, 2.5703926, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, -1, 22, -1, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008244282, 1.0, 1.0, 0.0, 0.009543753, -0.011382496, 1.0, 1.0, 1.0, -0.0026402832, 0.008591554, 0.016867498, 1.0, 1.0, 0.009546998, 0.0006042447, -0.0043079415, 1.0, -0.010835662, 1.0, 1.0, 1.0, 1.0, -0.013096514, 1.0, 0.021663299, 0.012044757, -0.0048028077, -0.005111002, 0.007040014, -0.01252802], "split_indices": [43, 80, 0, 125, 122, 0, 0, 0, 13, 122, 88, 0, 0, 0, 71, 64, 0, 0, 0, 13, 0, 124, 15, 15, 115, 0, 93, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1942.0, 112.0, 1551.0, 391.0, 1395.0, 156.0, 159.0, 232.0, 334.0, 1061.0, 131.0, 101.0, 156.0, 178.0, 960.0, 101.0, 90.0, 88.0, 819.0, 141.0, 377.0, 442.0, 187.0, 190.0, 169.0, 273.0, 91.0, 96.0, 97.0, 93.0, 154.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0072187027, 0.0081234295, -0.012262867, 0.012103454, -0.04277666, -0.016292084, 0.124015294, -0.018145055, -0.023380436, 0.03596542, -0.072925225, 0.004858386, 0.022223327, 0.021497013, -0.08783818, 0.0105218645, -0.008036614, -0.015134837, -0.035922747, -0.053265993, 0.015747225, -0.002929228, -0.015695486, 0.0028357692, -0.0052292114, 0.015107761, -0.014072738, 0.00095935975, -0.011484276, -0.008448645, 0.01038551], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, -1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, 19, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [0.92625177, 0.0, 1.4602515, 3.4701545, 2.345461, 2.5777307, 1.6373267, 0.0, 2.212915, 1.3804194, 1.2129729, 0.0, 0.0, 4.584829, 1.2705998, 0.0, 0.44614965, 0.0, 1.5188988, 1.1263711, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6881958, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 16, 16, 18, 18, 19, 19, 25, 25], "right_children": [2, -1, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, 20, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0081234295, 1.0, 1.0, -0.5, 1.0, 1.0, -0.018145055, 1.0, 1.0, 1.0, 0.004858386, 0.022223327, 1.0, 1.0, 0.0105218645, 1.0, -0.015134837, 0.26923078, 1.0, 0.015747225, -0.002929228, -0.015695486, 0.0028357692, -0.0052292114, -0.1923077, -0.014072738, 0.00095935975, -0.011484276, -0.008448645, 0.01038551], "split_indices": [1, 0, 109, 105, 1, 127, 39, 0, 105, 97, 111, 0, 0, 2, 97, 0, 17, 0, 1, 59, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 112.0, 1964.0, 1092.0, 872.0, 871.0, 221.0, 107.0, 765.0, 453.0, 418.0, 125.0, 96.0, 451.0, 314.0, 176.0, 277.0, 134.0, 284.0, 291.0, 160.0, 170.0, 144.0, 152.0, 125.0, 191.0, 93.0, 144.0, 147.0, 90.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023610208, -0.0067704516, 0.008271503, -0.025988635, 0.0142897265, 0.0030185739, -0.06272685, 0.02909935, -0.0052702026, -0.014188311, 0.03768256, -0.1178746, 0.015301348, 0.0118751535, 0.014703234, 0.020910135, -0.0030008196, -0.020654911, -0.006614779, 0.0037652317, -0.0008523278, 0.039799005, -0.059338037, -0.043956272, 0.009440671, -0.0106002465, 0.019881735, -0.012681111, 0.0008852792, -0.012606715, 0.0019417877, -0.013692679, 0.037546836, 0.023185436, -0.0065784315], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, -1, -1, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.7765333, 0.7965251, 0.0, 1.0965776, 0.9316032, 2.88815, 1.9535984, 1.5620701, 0.0, 0.0, 3.235888, 1.2201014, 0.100110754, 1.3343147, 0.0, 0.0, 1.4960135, 0.0, 0.0, 0.0, 0.0, 3.8629441, 0.8695977, 1.373779, 0.0, 2.2261052, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.320676, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 21, 21, 22, 22, 23, 23, 25, 25, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, -1, -1, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 0.008271503, 1.0, 1.0, -0.5, 0.34615386, 1.2307693, -0.0052702026, -0.014188311, -0.26923078, 1.0, 1.0, 0.1923077, 0.014703234, 0.020910135, 1.0, -0.020654911, -0.006614779, 0.0037652317, -0.0008523278, -0.07692308, 0.53846157, 1.0, 0.009440671, 1.0, 0.019881735, -0.012681111, 0.0008852792, -0.012606715, 0.0019417877, -0.013692679, -0.42307693, 0.023185436, -0.0065784315], "split_indices": [114, 39, 0, 59, 80, 1, 1, 1, 0, 0, 1, 106, 106, 1, 0, 0, 42, 0, 0, 0, 0, 1, 1, 116, 0, 89, 0, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1968.0, 102.0, 1029.0, 939.0, 575.0, 454.0, 769.0, 170.0, 111.0, 464.0, 266.0, 188.0, 671.0, 98.0, 89.0, 375.0, 98.0, 168.0, 97.0, 91.0, 482.0, 189.0, 264.0, 111.0, 366.0, 116.0, 95.0, 94.0, 115.0, 149.0, 101.0, 265.0, 92.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0053592473, 0.010106553, -0.008987749, 0.014849063, -0.0070690117, 0.009891572, 0.009841135, 0.017956225, -0.011797968, -0.014222111, 0.044605028, 0.00963801, -0.009520619, 0.012161708, 0.09848581, -0.011448174, 0.012681014, -0.006805976, 0.045992486, -0.00047173348, 0.18857476, -0.0320892, 0.007090527, 0.09180007, -0.0056886827, 0.024978355, 0.012936189, -0.07798044, 0.0036309191, 0.0022107572, 0.01736611, -0.013088057, -0.00019364638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.93317103, 0.7533297, 0.0, 0.76927906, 0.0, 1.8077589, 0.0, 1.414041, 0.0, 1.4434208, 1.5767603, 1.4256017, 0.0, 1.5279562, 3.1518412, 0.8312311, 0.0, 0.0, 1.8662109, 0.0, 0.6560054, 1.2273049, 0.0, 1.5631964, 0.0, 0.0, 0.0, 0.94132006, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 20, 20, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008987749, 3.1923077, -0.0070690117, 1.3461539, 0.009841135, 1.0, -0.011797968, 1.0, -0.07692308, 1.0, -0.009520619, 1.0, 1.0, 1.0, 0.012681014, -0.006805976, -0.26923078, -0.00047173348, 1.0, 1.0, 0.007090527, 1.0, -0.0056886827, 0.024978355, 0.012936189, 1.0, 0.0036309191, 0.0022107572, 0.01736611, -0.013088057, -0.00019364638], "split_indices": [117, 43, 0, 1, 0, 1, 0, 71, 0, 116, 1, 121, 0, 89, 39, 58, 0, 0, 1, 0, 12, 62, 0, 39, 0, 0, 0, 81, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1966.0, 98.0, 1857.0, 109.0, 1753.0, 104.0, 1649.0, 104.0, 747.0, 902.0, 577.0, 170.0, 563.0, 339.0, 489.0, 88.0, 167.0, 396.0, 158.0, 181.0, 391.0, 98.0, 274.0, 122.0, 89.0, 92.0, 234.0, 157.0, 148.0, 126.0, 138.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003460986, -0.0031871458, 0.043349788, 0.0022993467, -0.0108054, 0.010309644, -0.002370399, -0.04612825, 0.015767815, -0.11915656, 0.0060228403, 0.11098644, 0.00029057838, -0.023572676, -0.0009871968, 0.019582909, 0.002796834, -0.010280442, 0.00956169, -0.011411601, -0.00022101993, 0.04687143, -0.037099566, -0.01142374, 0.021949308, -0.09471462, 0.028307669, 0.040956926, -0.008685193, 0.0014411082, -0.028807128, -0.008259026, 0.011068902, -0.0020535036, 0.010109745, 0.019314157, -0.017247267], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, 17, -1, -1, -1, -1, 19, -1, -1, 21, 23, 25, 27, -1, 29, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.54760927, 1.0183719, 1.1818407, 1.0970768, 0.0, 0.0, 0.0, 2.8427393, 1.9394166, 2.7644336, 0.0, 1.2959988, 1.1407127, 0.0, 0.0, 0.0, 0.0, 1.0643715, 0.0, 0.0, 1.6133953, 4.105708, 1.963358, 1.2050487, 0.0, 5.1500816, 2.2291648, 0.66566896, 0.0, 6.16778, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 17, 17, 20, 20, 21, 21, 22, 22, 23, 23, 25, 25, 26, 26, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, 18, -1, -1, -1, -1, 20, -1, -1, 22, 24, 26, 28, -1, 30, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.96153843, 1.0, 1.0, -0.0108054, 0.010309644, -0.002370399, 1.0, -0.46153846, 1.0, 0.0060228403, 1.0, 1.0, -0.023572676, -0.0009871968, 0.019582909, 0.002796834, 1.0, 0.00956169, -0.011411601, 1.0, 1.0, 1.0, 1.0, 0.021949308, 1.0, 1.0, -0.07692308, -0.008685193, -0.03846154, -0.028807128, -0.008259026, 0.011068902, -0.0020535036, 0.010109745, 0.019314157, -0.017247267], "split_indices": [1, 1, 115, 89, 0, 0, 0, 97, 1, 88, 0, 53, 44, 0, 0, 0, 0, 26, 0, 0, 126, 61, 106, 121, 0, 0, 39, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1770.0, 295.0, 1682.0, 88.0, 156.0, 139.0, 366.0, 1316.0, 217.0, 149.0, 184.0, 1132.0, 105.0, 112.0, 91.0, 93.0, 1019.0, 113.0, 90.0, 929.0, 408.0, 521.0, 305.0, 103.0, 277.0, 244.0, 180.0, 125.0, 185.0, 92.0, 104.0, 140.0, 89.0, 91.0, 88.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.005912513, 0.0017358974, -0.06513123, -0.004311718, 0.009389516, -0.012857104, -0.00032773888, -0.010629111, 0.010388191, -0.015984787, 0.0063230502, 0.020795096, -0.031643894, 0.019211726, -0.0416446, -0.0038135706, -0.10592206, 0.004373391, -0.07388297, -0.020793235, 0.009250208, -0.019474138, -0.0025283452, -0.014745901, -0.00032977914, 0.0003863171, -0.011384441, -0.04575473, 0.0508531, -0.014268041, 0.00032291954, 0.015920153, -0.006448553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, -1, 17, 19, 21, -1, 23, 25, -1, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.93846923, 1.0227262, 0.9299867, 1.1769897, 0.0, 0.0, 0.0, 0.64358914, 0.0, 0.8737013, 0.0, 4.8458796, 2.1994848, 0.0, 0.9138183, 1.2658051, 2.0770566, 0.0, 1.251604, 1.2967749, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2481245, 0.0, 1.3293843, 3.1991708, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, -1, 18, 20, 22, -1, 24, 26, -1, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.009389516, -0.012857104, -0.00032773888, 5.0, 0.010388191, 1.0, 0.0063230502, 1.0, 1.0, 0.019211726, 0.0, 1.0, 1.0, 0.004373391, 1.0, 1.0, 0.009250208, -0.019474138, -0.0025283452, -0.014745901, -0.00032977914, 1.0, -0.011384441, 1.0, 1.0, -0.014268041, 0.00032291954, 0.015920153, -0.006448553], "split_indices": [119, 125, 39, 114, 0, 0, 0, 0, 0, 81, 0, 53, 121, 0, 0, 42, 53, 0, 137, 0, 0, 0, 0, 0, 0, 12, 0, 69, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1835.0, 237.0, 1722.0, 113.0, 117.0, 120.0, 1627.0, 95.0, 1517.0, 110.0, 453.0, 1064.0, 121.0, 332.0, 774.0, 290.0, 91.0, 241.0, 658.0, 116.0, 138.0, 152.0, 118.0, 123.0, 536.0, 122.0, 280.0, 256.0, 94.0, 186.0, 132.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0052976636, -0.012832128, 0.033422347, -0.0836283, -0.0026184502, 0.111346066, -0.007951057, -0.016492818, -0.001778209, 0.03734999, -0.02781875, -0.0037476623, 0.023559256, -0.025995342, 0.083368525, -0.07722295, 0.0026300123, 0.0048522754, -0.013739616, 0.019417414, 0.025232825, -0.017545562, 0.008084353, 0.026760425, -0.011363467, 0.012182641, -0.0051886225, -0.0066205524, 0.009239088, 0.01427631, -0.018558394, 0.010765508, -0.07814417, -0.014423854, -0.00016460335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, 17, 19, 21, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1], "loss_changes": [0.6053492, 1.2560059, 2.9744518, 1.1723695, 1.5289551, 3.6981401, 0.0, 0.0, 0.0, 1.7111399, 1.4005003, 0.0, 0.0, 2.0504403, 2.1901984, 2.9748878, 1.6159763, 0.0, 0.0, 0.0, 1.6611732, 0.0, 1.1899931, 2.5076392, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5795407, 0.0, 1.1780698, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 20, 20, 22, 22, 23, 23, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, 18, 20, 22, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1], "split_conditions": [1.0, -0.5, 1.0, 1.0, -0.03846154, -0.15384616, -0.007951057, -0.016492818, -0.001778209, 1.0, 0.42307693, -0.0037476623, 0.023559256, 1.0, 1.0, 1.0, 1.0, 0.0048522754, -0.013739616, 0.019417414, -0.30769232, -0.017545562, 1.0, 1.0, -0.011363467, 0.012182641, -0.0051886225, -0.0066205524, 0.009239088, 0.01427631, 1.0, 0.010765508, 1.4230769, -0.014423854, -0.00016460335], "split_indices": [62, 1, 124, 89, 1, 1, 0, 0, 0, 13, 1, 0, 0, 105, 69, 93, 64, 0, 0, 0, 1, 0, 108, 124, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1737.0, 338.0, 219.0, 1518.0, 200.0, 138.0, 98.0, 121.0, 587.0, 931.0, 91.0, 109.0, 247.0, 340.0, 355.0, 576.0, 148.0, 99.0, 117.0, 223.0, 165.0, 190.0, 477.0, 99.0, 99.0, 124.0, 101.0, 89.0, 134.0, 343.0, 110.0, 233.0, 125.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0029998114, 0.008253476, -0.0014638829, -0.028785115, 0.01334676, -0.055021867, 0.004886962, 0.096792616, -0.00068870815, -0.015060575, -0.021486701, 0.0032256276, 0.016495457, 0.013511239, -0.007790619, -0.008463847, 0.013870432, -0.0110463025, 0.107734665, -0.036273178, 0.009401069, 0.018549854, -0.0133390995, 0.0009249371, 0.020416815, -0.0075368136, 0.00023873603, 0.010895237, -0.017697452, 0.03261133, -0.08320945, 0.013525048, -0.0034857746, -0.015828382, -0.0006466728], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, 17, -1, -1, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1], "loss_changes": [0.73489094, 0.0, 0.79310435, 1.4037741, 1.4885976, 3.2896233, 0.0, 0.8050021, 1.1929749, 0.8293392, 0.0, 0.0, 0.0, 2.1264706, 0.0, 0.0, 1.16939, 2.6396601, 1.8044825, 0.2705466, 0.0, 1.9235097, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3809525, 1.6412181, 1.0485764, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 28, 28, 29, 29, 30, 30], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, 18, -1, -1, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008253476, -0.1923077, 1.0, -0.03846154, 1.0, 0.004886962, -0.115384616, 1.0, 1.0, -0.021486701, 0.0032256276, 0.016495457, 1.0, -0.007790619, -0.008463847, 1.0, 1.0, 1.0, 1.0, 0.009401069, 0.0, -0.0133390995, 0.0009249371, 0.020416815, -0.0075368136, 0.00023873603, 0.010895237, 1.0, 1.0, 1.0, 0.013525048, -0.0034857746, -0.015828382, -0.0006466728], "split_indices": [1, 0, 1, 61, 1, 113, 0, 1, 64, 81, 0, 0, 0, 0, 0, 0, 97, 15, 39, 115, 0, 0, 0, 0, 0, 0, 0, 0, 13, 137, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 110.0, 1960.0, 689.0, 1271.0, 515.0, 174.0, 183.0, 1088.0, 412.0, 103.0, 94.0, 89.0, 919.0, 169.0, 121.0, 291.0, 729.0, 190.0, 179.0, 112.0, 587.0, 142.0, 94.0, 96.0, 89.0, 90.0, 168.0, 419.0, 237.0, 182.0, 94.0, 143.0, 92.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.006145675, 0.0018120722, 0.0095210215, -0.0049148817, 0.04007591, 0.01736286, -0.044630595, -0.004756562, 0.0850203, -0.022296673, 0.080684006, -0.06676227, 0.007225229, 0.013367902, 0.0036858064, -0.004258867, -0.009192789, 0.025125226, 0.034641024, -0.10463307, 0.0016124742, 0.0054550343, -0.027129123, 0.012969243, -0.03425866, -0.0036134874, -0.16783305, 0.0032561305, -0.008375621, -0.001290126, -0.0058255703, -0.026093736, -0.0073694177, 0.010182532, -0.0055629336], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, 21, -1, -1, 23, 25, -1, -1, 27, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.79857236, 0.5078485, 0.0, 1.4846548, 1.1620036, 2.6996336, 1.5598488, 0.0, 0.45698464, 0.830212, 3.2513363, 1.5914719, 0.0, 0.0, 0.0, 0.7061153, 0.0, 0.0, 2.134978, 1.506521, 0.0, 0.0, 0.65039784, 0.0, 0.09686501, 0.0, 1.5864158, 1.4278561, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 18, 18, 19, 19, 22, 22, 24, 24, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, 22, -1, -1, 24, 26, -1, -1, 28, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2692307, 0.0095210215, 1.0, 1.0, 1.0, 1.0, -0.004756562, 1.0, 1.0, 1.0, 1.0, 0.007225229, 0.013367902, 0.0036858064, 1.0, -0.009192789, 0.025125226, 1.0, 1.0, 0.0016124742, 0.0054550343, 1.0, 0.012969243, -0.30769232, -0.0036134874, 1.0, 0.115384616, -0.008375621, -0.001290126, -0.0058255703, -0.026093736, -0.0073694177, 0.010182532, -0.0055629336], "split_indices": [114, 1, 0, 121, 81, 71, 108, 0, 80, 7, 108, 122, 0, 0, 0, 53, 0, 0, 15, 93, 0, 0, 15, 0, 1, 0, 119, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1973.0, 96.0, 1678.0, 295.0, 1075.0, 603.0, 100.0, 195.0, 661.0, 414.0, 507.0, 96.0, 97.0, 98.0, 525.0, 136.0, 88.0, 326.0, 348.0, 159.0, 147.0, 378.0, 137.0, 189.0, 167.0, 181.0, 246.0, 132.0, 100.0, 89.0, 91.0, 90.0, 92.0, 154.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0048187873, -0.01202868, 0.033751916, 0.0047683236, -0.042328104, -0.006220178, 0.1125519, 0.013513996, -0.0088701015, 0.014732344, -0.08223925, 0.01975971, 0.0024607384, 0.041850563, -0.034860026, 0.010722472, -0.004958353, -0.01812218, -0.04704544, 0.08977762, -0.02857659, -0.073371895, 0.0027290138, 0.0045280433, -0.014647332, -0.005101941, 0.13785467, -0.008193961, 0.004178701, -0.013229223, -0.0027045195, 0.0034879975, 0.18489893, 0.028393602, 0.00945155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, 19, 21, -1, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.5756476, 0.8875907, 2.464935, 0.91718125, 1.4165103, 0.0, 1.3387868, 1.4063934, 0.0, 1.5228746, 1.2749867, 0.0, 0.0, 2.1838622, 0.9071438, 0.0, 0.0, 0.0, 2.4785373, 2.606106, 0.98376155, 0.6387222, 0.0, 0.0, 0.0, 0.0, 1.3903332, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7634072, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 18, 18, 19, 19, 20, 20, 21, 21, 26, 26, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, 20, 22, -1, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, -0.15384616, 1.0, 1.0, -0.006220178, 0.46153846, 1.0, -0.0088701015, 1.0, 1.0, 0.01975971, 0.0024607384, 1.0, 1.0, 0.010722472, -0.004958353, -0.01812218, 1.0, 1.0, 1.0, 1.0, 0.0027290138, 0.0045280433, -0.014647332, -0.005101941, -0.42307693, -0.008193961, 0.004178701, -0.013229223, -0.0027045195, 0.0034879975, 1.0, 0.028393602, 0.00945155], "split_indices": [62, 121, 1, 73, 109, 0, 1, 80, 0, 93, 89, 0, 0, 122, 111, 0, 0, 0, 97, 5, 93, 106, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 93, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1744.0, 326.0, 1122.0, 622.0, 147.0, 179.0, 1026.0, 96.0, 256.0, 366.0, 91.0, 88.0, 647.0, 379.0, 105.0, 151.0, 96.0, 270.0, 385.0, 262.0, 234.0, 145.0, 140.0, 130.0, 98.0, 287.0, 149.0, 113.0, 103.0, 131.0, 90.0, 197.0, 94.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.002725669, 0.0030009316, -0.048140112, -0.0034481671, 0.01302241, 0.00456145, -0.014865184, -0.007928714, 0.007570813, 0.015103386, -0.03416972, -0.08656448, 0.052217714, -0.015375304, -0.018095735, -0.018053615, 0.005093089, -0.021143021, 0.11716355, 0.008016608, -0.03633699, -0.014773483, 0.061628535, 0.02365188, 0.029473962, -0.0074717426, -0.012273085, -0.0007273346, 0.012756692, -0.009434389, 0.015329184, 0.028130118, -0.05206777, -0.0070687146, 0.011022448, 0.0002656623, -0.010344173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, -1, 21, 23, -1, 25, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [0.5341847, 1.4965464, 2.1673908, 0.61569566, 0.0, 0.0, 0.0, 0.9930053, 0.0, 3.3016677, 1.476234, 3.0234373, 3.054028, 0.0, 1.2134672, 0.0, 0.0, 3.1539392, 3.5585113, 0.0, 1.4239478, 0.0, 0.8268773, 0.0, 3.0048494, 0.67953634, 0.0, 0.0, 0.0, 0.0, 0.0, 1.930737, 0.53416795, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 17, 17, 18, 18, 20, 20, 22, 22, 24, 24, 25, 25, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, -1, 22, 24, -1, 26, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.1923077, 1.0, 1.0, 0.01302241, 0.00456145, -0.014865184, 1.0, 0.007570813, 1.0, 1.0, 1.0, 1.0, -0.015375304, 1.0, -0.018053615, 0.005093089, 1.0, -0.30769232, 0.008016608, 1.0, -0.014773483, 1.0, 0.02365188, 0.115384616, 1.0, -0.012273085, -0.0007273346, 0.012756692, -0.009434389, 0.015329184, 1.0, 1.0, -0.0070687146, 0.011022448, 0.0002656623, -0.010344173], "split_indices": [1, 1, 12, 114, 0, 0, 0, 126, 0, 89, 111, 97, 93, 0, 89, 0, 0, 17, 1, 0, 50, 0, 16, 0, 1, 16, 0, 0, 0, 0, 0, 39, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1824.0, 230.0, 1736.0, 88.0, 119.0, 111.0, 1643.0, 93.0, 875.0, 768.0, 234.0, 641.0, 91.0, 677.0, 139.0, 95.0, 301.0, 340.0, 106.0, 571.0, 119.0, 182.0, 144.0, 196.0, 428.0, 143.0, 89.0, 93.0, 98.0, 98.0, 238.0, 190.0, 108.0, 130.0, 92.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042772046, -0.042337853, 0.006227156, 0.0051276777, -0.07335644, -0.0008814164, 0.012132556, -0.018312927, -0.026644588, 0.021145687, -0.033366922, 0.0047663804, -0.01155369, 0.09060422, -0.0042736223, -0.010520069, -0.005924802, 0.026635367, -0.0014614174, 0.009135128, -0.035638597, 0.011452324, -0.040388267, -0.10063541, 0.0035261435, 0.0019946867, -0.08339947, -0.004795895, -0.01500535, 0.012917334, -0.08388058, -0.013799329, -0.0024303033, -0.011895217, -0.004725022], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, 11, 13, 15, -1, -1, 17, 19, -1, 21, -1, -1, -1, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.82439345, 1.2950921, 1.322188, 0.0, 1.7177765, 1.0890849, 0.0, 0.0, 1.5522797, 1.6013886, 1.2123312, 0.0, 0.0, 4.493575, 1.9915168, 0.0, 1.8472203, 0.0, 0.0, 0.0, 1.2727921, 0.0, 0.8979001, 0.48939526, 3.426512, 0.0, 0.6517122, 0.0, 0.0, 0.0, 0.23638153, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 16, 16, 20, 20, 22, 22, 23, 23, 24, 24, 26, 26, 30, 30], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, 12, 14, 16, -1, -1, 18, 20, -1, 22, -1, -1, -1, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [-0.3846154, -0.5769231, 1.0, 0.0051276777, 1.0, 1.0, 0.012132556, -0.018312927, 1.0, 0.0, -0.115384616, 0.0047663804, -0.01155369, 1.0, 1.0, -0.010520069, 0.15384616, 0.026635367, -0.0014614174, 0.009135128, 0.07692308, 0.011452324, 1.0, 1.0, 0.6923077, 0.0019946867, 1.0, -0.004795895, -0.01500535, 0.012917334, 1.0, -0.013799329, -0.0024303033, -0.011895217, -0.004725022], "split_indices": [1, 1, 90, 0, 89, 109, 0, 0, 97, 0, 1, 0, 0, 122, 126, 0, 1, 0, 0, 0, 1, 0, 97, 69, 1, 0, 50, 0, 0, 0, 122, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 446.0, 1616.0, 111.0, 335.0, 1522.0, 94.0, 100.0, 235.0, 907.0, 615.0, 128.0, 107.0, 243.0, 664.0, 170.0, 445.0, 91.0, 152.0, 164.0, 500.0, 99.0, 346.0, 188.0, 312.0, 144.0, 202.0, 91.0, 97.0, 128.0, 184.0, 105.0, 97.0, 94.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002554322, -0.0021372177, 0.0075198743, -0.050493248, 0.0075280387, 0.0050193584, -0.013573383, 0.018209059, -0.067344315, -0.039047208, 0.051280584, -0.014501251, 0.0008061674, -0.09008453, 0.029155117, -0.046823386, 0.08427259, -6.870678e-05, -0.016312879, -0.0049362644, 0.010558836, 0.0048597786, -0.01106714, -0.020857776, 0.13442232, 0.0037774176, -0.008877798, 0.23667862, 0.06487299, 0.033323225, 0.013476083, -0.0031698656, 0.023097623], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.7078713, 0.9118457, 0.0, 2.7893465, 1.3003333, 0.0, 0.0, 2.6945248, 1.1888998, 1.8135302, 2.9194558, 0.0, 0.0, 1.9459307, 1.338305, 1.3829868, 3.558775, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.86814034, 3.2501183, 0.0, 0.0, 1.8204975, 4.3631153, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 23, 23, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.0075198743, 1.0, 1.3461539, 0.0050193584, -0.013573383, 1.0, 3.1923077, 1.0, 1.0, -0.014501251, 0.0008061674, 1.0, 1.0, 1.0, 1.0, -6.870678e-05, -0.016312879, -0.0049362644, 0.010558836, 0.0048597786, -0.01106714, 1.0, 1.0, 0.0037774176, -0.008877798, 0.07692308, 1.0, 0.033323225, 0.013476083, -0.0031698656, 0.023097623], "split_indices": [102, 0, 0, 127, 1, 0, 0, 17, 1, 111, 5, 0, 0, 2, 13, 53, 127, 0, 0, 0, 0, 0, 0, 111, 109, 0, 0, 1, 61, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1951.0, 126.0, 325.0, 1626.0, 149.0, 176.0, 1423.0, 203.0, 521.0, 902.0, 100.0, 103.0, 298.0, 223.0, 227.0, 675.0, 134.0, 164.0, 110.0, 113.0, 91.0, 136.0, 218.0, 457.0, 117.0, 101.0, 185.0, 272.0, 95.0, 90.0, 172.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020126551, 0.00766551, -0.006427684, -0.054022312, 0.0034548715, -0.020002428, 0.004986801, -0.003525587, 0.01182947, -0.006215959, 0.0076764002, -0.05049798, 0.008603795, 0.0066441544, -0.10046306, 0.008425375, -0.0037752863, -0.017214209, -0.0023374266, 0.011842203, -0.008530232, -0.0046517574, 0.01597256, 0.05890462, -0.024951544, 0.015491228, -0.003610293, 0.03613529, -0.10017041, 0.018535791, -0.00584216, -0.0010982228, -0.020068405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, 5, 7, -1, 9, 11, -1, -1, -1, 13, 15, -1, 17, -1, 19, -1, -1, 21, -1, 23, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.718953, 0.0, 0.921899, 2.9034061, 1.3010535, 0.0, 1.1566992, 0.87171173, 0.0, 0.0, 0.0, 1.8346686, 1.1387563, 0.0, 1.2156425, 0.0, 1.3305441, 0.0, 0.0, 2.139164, 0.0, 1.0179526, 0.0, 1.742198, 2.74774, 0.0, 0.0, 4.6563087, 2.40252, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 16, 16, 19, 19, 21, 21, 23, 23, 24, 24, 27, 27, 28, 28], "right_children": [2, -1, 4, 6, 8, -1, 10, 12, -1, -1, -1, 14, 16, -1, 18, -1, 20, -1, -1, 22, -1, 24, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.00766551, -0.3846154, 1.0, 1.0, -0.020002428, 1.0, -0.1923077, 0.01182947, -0.006215959, 0.0076764002, 1.0, -0.03846154, 0.0066441544, 1.0, 0.008425375, 1.0, -0.017214209, -0.0023374266, 3.0, -0.008530232, 0.0, 0.01597256, 1.0, 1.0, 0.015491228, -0.003610293, 1.0, 1.0, 0.018535791, -0.00584216, -0.0010982228, -0.020068405], "split_indices": [1, 0, 1, 89, 90, 0, 93, 1, 0, 0, 0, 5, 1, 0, 106, 0, 64, 0, 0, 0, 0, 0, 0, 97, 13, 0, 0, 124, 127, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 110.0, 1960.0, 337.0, 1623.0, 97.0, 240.0, 1530.0, 93.0, 124.0, 116.0, 314.0, 1216.0, 94.0, 220.0, 171.0, 1045.0, 114.0, 106.0, 877.0, 168.0, 789.0, 88.0, 191.0, 598.0, 95.0, 96.0, 330.0, 268.0, 128.0, 202.0, 142.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0010937462, 0.0066185268, -0.0025818518, 0.0024389464, -0.0083355075, -0.0036499416, 0.008788304, -0.013279722, 0.044364266, -0.005935021, -0.0073945015, 0.015183292, -0.0041875476, -0.013209213, 0.0058517675, -0.0056419666, 0.00579106, 0.00045195312, -0.014150001, -0.0086520435, 0.010292582, -0.006856838, 0.009784491, 0.0011890178, -0.008381829], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, -1, 19, -1, -1, 21, 23, -1, -1, -1], "loss_changes": [0.49237725, 0.0, 0.79000396, 0.9541562, 0.0, 0.79157096, 0.0, 0.6353804, 1.4922907, 0.5963659, 0.0, 0.0, 0.638973, 2.0032244, 0.0, 0.0, 0.0, 0.8841063, 0.0, 0.0, 1.3933659, 1.1196111, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 17, 17, 20, 20, 21, 21], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, -1, 20, -1, -1, 22, 24, -1, -1, -1], "split_conditions": [-0.5769231, 0.0066185268, 1.0, 5.0, -0.0083355075, 1.0, 0.008788304, 1.0, -0.03846154, 1.0, -0.0073945015, 0.015183292, 0.84615386, 1.3461539, 0.0058517675, -0.0056419666, 0.00579106, -0.5, -0.014150001, -0.0086520435, 1.0, 1.0, 0.009784491, 0.0011890178, -0.008381829], "split_indices": [1, 0, 84, 0, 0, 74, 0, 88, 1, 31, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 42, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 110.0, 1948.0, 1834.0, 114.0, 1712.0, 122.0, 1426.0, 286.0, 1272.0, 154.0, 89.0, 197.0, 1143.0, 129.0, 107.0, 90.0, 1033.0, 110.0, 105.0, 928.0, 776.0, 152.0, 624.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.003605304, 0.0073194634, -0.0059373896, 0.015231053, -0.024125062, 0.007427451, 0.0084867, -0.05683808, 0.0025894179, 0.04880467, -0.0056779245, -0.009774566, -0.00062824605, -0.0032491728, 0.012229296, -0.030149652, 0.043196585, 0.029613579, -0.060925167, 0.013641447, -0.036843885, 0.007367184, -0.00055672525, -0.11907649, -0.0037594452, -0.014132266, 0.005241951, -0.016887506, -0.004191601, 0.006330257, -0.008460994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, -1, -1, 17, 19, 21, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.48303393, 0.48511356, 0.0, 0.8466344, 0.641422, 0.759712, 0.0, 0.49014133, 0.0, 2.0133507, 1.2725904, 0.0, 0.0, 0.0, 0.0, 1.3040242, 2.6487274, 0.37355176, 1.5557543, 0.0, 1.7812912, 0.0, 0.0, 0.8914554, 1.2795916, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, -1, -1, 18, 20, 22, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0059373896, 1.0, 1.0, 0.0, 0.0084867, 1.0, 0.0025894179, 1.0, 1.0, -0.009774566, -0.00062824605, -0.0032491728, 0.012229296, 1.0, 1.0, 1.0, 1.0, 0.013641447, 1.0, 0.007367184, -0.00055672525, 1.0, 1.0, -0.014132266, 0.005241951, -0.016887506, -0.004191601, 0.006330257, -0.008460994], "split_indices": [43, 80, 0, 125, 23, 0, 0, 12, 0, 13, 105, 0, 0, 0, 0, 126, 109, 13, 97, 0, 93, 0, 0, 12, 12, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1950.0, 115.0, 1558.0, 392.0, 1401.0, 157.0, 237.0, 155.0, 337.0, 1064.0, 131.0, 106.0, 160.0, 177.0, 709.0, 355.0, 241.0, 468.0, 164.0, 191.0, 107.0, 134.0, 232.0, 236.0, 88.0, 103.0, 141.0, 91.0, 129.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.002782522, 0.0060466845, -0.0064404793, 0.011794784, -0.03592892, 0.003791018, 0.013502255, 0.0016881928, -0.011137302, -0.0068444526, 0.047396455, 0.019233037, -0.038268927, 0.016912386, -0.008218431, 0.04848562, -0.01488024, -0.08851399, 0.06401565, 0.016172666, 0.010998923, 0.014859526, -0.008164897, -0.018541044, -0.039517283, 0.007029101, 0.005868748, 0.006697009, -0.0045749596, 0.005785025, -0.0044700513, -0.009678709, 0.0017321898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.45441076, 0.47676906, 0.0, 1.7141652, 0.9482558, 0.7568636, 0.0, 0.0, 0.0, 1.0751467, 5.047529, 0.71549547, 3.0578809, 0.0, 0.0, 1.6385822, 0.6572623, 1.8942943, 0.006553054, 0.0, 0.9211214, 0.5863613, 0.0, 0.0, 0.86261994, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0064404793, 1.0, 1.0, 1.0, 0.013502255, 0.0016881928, -0.011137302, 1.0, 1.0, 1.0, 1.0, 0.016912386, -0.008218431, 1.0, 1.0, 1.0, 1.0, 0.016172666, 1.0, 1.0, -0.008164897, -0.018541044, 1.0, 0.007029101, 0.005868748, 0.006697009, -0.0045749596, 0.005785025, -0.0044700513, -0.009678709, 0.0017321898], "split_indices": [117, 119, 0, 125, 61, 105, 0, 0, 0, 127, 109, 80, 109, 0, 0, 59, 58, 111, 50, 0, 108, 116, 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1976.0, 96.0, 1738.0, 238.0, 1632.0, 106.0, 140.0, 98.0, 1312.0, 320.0, 717.0, 595.0, 165.0, 155.0, 386.0, 331.0, 399.0, 196.0, 96.0, 290.0, 229.0, 102.0, 134.0, 265.0, 90.0, 106.0, 146.0, 144.0, 133.0, 96.0, 132.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015472593, -0.009450206, 0.046003904, -0.030003846, 0.011927459, 0.013175647, 0.0042048967, -0.09163714, 0.02280668, 0.08290538, -0.0066375714, -0.0040628165, 0.004353959, -0.05028672, -0.13823505, 0.08769237, -0.04966304, 0.01928259, -0.0023430733, 0.04160431, -0.060485553, -0.010287995, 0.0029798392, -0.022654193, -0.0062424405, -0.0021886686, 0.02156881, 0.0006465398, -0.008887043, -0.0036636074, 0.09935318, -0.013759936, 0.0024907144, 0.019086275, 0.0019282276, 0.0061596134, -0.0055315653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1, -1], "loss_changes": [0.7805246, 0.7825515, 1.0609739, 2.9554372, 1.1503588, 0.0, 0.3509355, 0.8073468, 2.2993996, 2.115623, 1.7976278, 0.0, 0.0, 0.9350498, 1.3188355, 3.618619, 0.5083503, 0.0, 0.0, 1.6491773, 1.588024, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5387228, 0.0, 0.61500055, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.013175647, 2.7307692, 1.0, 1.0, 0.07692308, 1.0, -0.0040628165, 0.004353959, 0.26923078, 1.0, 1.0, -0.26923078, 0.01928259, -0.0023430733, 1.0, -0.34615386, -0.010287995, 0.0029798392, -0.022654193, -0.0062424405, -0.0021886686, 0.02156881, 0.0006465398, -0.008887043, -0.0036636074, 1.0, -0.013759936, 1.0, 0.019086275, 0.0019282276, 0.0061596134, -0.0055315653], "split_indices": [1, 124, 137, 15, 81, 0, 1, 39, 69, 1, 15, 0, 0, 1, 122, 71, 1, 0, 0, 93, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 50, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1781.0, 296.0, 908.0, 873.0, 97.0, 199.0, 419.0, 489.0, 181.0, 692.0, 93.0, 106.0, 222.0, 197.0, 258.0, 231.0, 89.0, 92.0, 365.0, 327.0, 134.0, 88.0, 91.0, 106.0, 139.0, 119.0, 95.0, 136.0, 155.0, 210.0, 147.0, 180.0, 98.0, 112.0, 89.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031107375, 0.0005784523, -0.0077045145, 0.0070237075, -0.050254475, -0.0019127913, 0.008709475, -0.012702504, 0.006577372, 0.004342777, -0.010712012, -0.0036535033, 0.010393284, 0.008422886, -0.07073815, -0.008226181, 0.07845351, -0.012262839, -0.0029136466, -0.083106674, 0.021726012, 0.020280099, -0.011230676, -0.018398533, -0.0028608995, 0.055825662, -0.009349958, -0.009297062, 0.00910669], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [0.5624274, 0.6434676, 0.0, 1.2472123, 1.9685684, 1.0319507, 0.0, 0.0, 0.0, 1.1785984, 0.0, 1.1098925, 0.0, 1.3536619, 0.45117235, 2.1037796, 5.2896843, 0.0, 0.0, 1.4733702, 2.632532, 0.0, 0.0, 0.0, 0.0, 2.7110262, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0, 1.3461539, -0.0077045145, 0.88461536, 1.0, 0.6923077, 0.008709475, -0.012702504, 0.006577372, 1.0, -0.010712012, 1.0, 0.010393284, 1.0, 1.0, 1.0, 1.0, -0.012262839, -0.0029136466, 1.0, 1.0, 0.020280099, -0.011230676, -0.018398533, -0.0028608995, 1.0, -0.009349958, -0.009297062, 0.00910669], "split_indices": [14, 1, 0, 1, 80, 1, 0, 0, 0, 44, 0, 116, 0, 64, 137, 5, 12, 0, 0, 59, 58, 0, 0, 0, 0, 17, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1964.0, 98.0, 1743.0, 221.0, 1568.0, 175.0, 133.0, 88.0, 1480.0, 88.0, 1370.0, 110.0, 1161.0, 209.0, 938.0, 223.0, 93.0, 116.0, 268.0, 670.0, 135.0, 88.0, 94.0, 174.0, 517.0, 153.0, 99.0, 418.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.001658765, 0.0072531933, -0.041721746, 0.0011074907, 0.010152386, -0.00039664988, -0.008964184, -0.01077367, 0.0077631907, -0.009583258, 0.03368932, -0.040020403, 0.023200214, 0.081541546, -0.007444387, -0.0037472788, -0.020424595, 0.019492023, -0.017231213, 0.0069118547, 0.024258452, 0.010049216, -0.06919189, 0.034619264, -0.010403879, -0.009996376, 0.019398456, -0.010263001, 0.014688204, -0.017131282, 0.002081297, -0.038763497, 0.014500176, 0.0070279962, -0.0060415645, -0.0039991927, -0.003757553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, 9, 11, 13, 15, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [0.5013957, 1.0602274, 0.42697912, 1.2445778, 0.0, 0.0, 0.0, 0.0, 0.7281069, 0.9679003, 1.2774523, 2.996357, 3.242328, 3.6055756, 2.3260195, 1.5853097, 0.0, 0.0, 1.1455164, 3.1431825, 0.0, 0.0, 2.0404868, 2.4138515, 0.0, 0.0, 1.0639983, 0.0, 0.0, 0.0, 0.0, 0.00026097894, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 22, 22, 23, 23, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, 10, 12, 14, 16, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.53846157, 0.010152386, -0.00039664988, -0.008964184, -0.01077367, 1.0, 0.65384614, 1.0, 2.0, 1.0, 1.0, -0.30769232, 1.0, -0.020424595, 0.019492023, 1.0, 1.0, 0.024258452, 0.010049216, 1.0, 1.0, -0.010403879, -0.009996376, 1.0, -0.010263001, 0.014688204, -0.017131282, 0.002081297, 0.115384616, 0.014500176, 0.0070279962, -0.0060415645, -0.0039991927, -0.003757553], "split_indices": [119, 125, 97, 1, 0, 0, 0, 0, 15, 1, 69, 0, 124, 0, 1, 50, 0, 0, 81, 7, 0, 0, 111, 2, 0, 0, 17, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1830.0, 236.0, 1718.0, 112.0, 132.0, 104.0, 99.0, 1619.0, 970.0, 649.0, 503.0, 467.0, 300.0, 349.0, 412.0, 91.0, 89.0, 378.0, 205.0, 95.0, 127.0, 222.0, 298.0, 114.0, 116.0, 262.0, 115.0, 90.0, 104.0, 118.0, 179.0, 119.0, 160.0, 102.0, 88.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.003055927, 0.006930801, -0.0063777054, 0.013825483, -0.020630239, 0.0072966916, 0.0070516453, -0.008855783, 0.02563078, -0.014156502, 0.031842835, -0.0016348459, 0.008837397, 0.007925232, -0.012821508, 0.0005466147, 0.014526387, -0.011123285, 0.011689098, 0.065473676, -0.08884133, -0.04808582, 0.039310023, -0.0021670437, 0.015144809, -0.0020344423, -0.015422471, 0.0035095494, -0.010917946, 0.0115858475, -0.0036560975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, 17, -1, 19, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5339947, 0.3703579, 0.0, 0.5770223, 1.225536, 0.7361773, 0.0, 0.0, 0.61106765, 1.8788843, 2.314371, 0.0, 0.0, 1.2972727, 0.0, 2.9656892, 0.0, 0.9917241, 0.0, 2.217681, 0.9628897, 1.560128, 1.306757, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, 18, -1, 20, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0063777054, 1.0, 1.0, 1.0, 0.0070516453, -0.008855783, 1.0, 3.0, 1.0, -0.0016348459, 0.008837397, 1.0, -0.012821508, 1.0, 0.014526387, 1.0, 0.011689098, 1.0, 1.0, 1.0, 1.0, -0.0021670437, 0.015144809, -0.0020344423, -0.015422471, 0.0035095494, -0.010917946, 0.0115858475, -0.0036560975], "split_indices": [43, 80, 0, 125, 122, 97, 0, 0, 23, 0, 62, 0, 0, 64, 0, 12, 0, 12, 0, 53, 39, 53, 106, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1949.0, 113.0, 1559.0, 390.0, 1398.0, 161.0, 158.0, 232.0, 746.0, 652.0, 139.0, 93.0, 625.0, 121.0, 511.0, 141.0, 532.0, 93.0, 296.0, 215.0, 307.0, 225.0, 147.0, 149.0, 105.0, 110.0, 130.0, 177.0, 112.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039429334, -0.009171546, 0.02785996, -0.027094204, 0.0095799025, 0.008124149, -0.0034684043, -0.012465085, -0.015330925, -0.05062697, 0.033507034, 0.0064853407, -0.009331205, -0.013084273, 0.0043786247, 0.00923536, 0.014219254, -0.008774641, 0.032936364, 0.041685615, -0.005547312, -0.022816256, 0.07933052, -0.005418928, 0.08877463, -0.011318562, 0.00566213, 0.017674457, -0.00038907418, 0.016618446, -0.0007029599], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, -1, 21, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.34271342, 0.59485424, 0.97156024, 1.6710051, 1.2460997, 0.0, 0.0, 1.2425207, 0.0, 1.8630638, 1.6329086, 1.6375895, 0.0, 0.0, 0.0, 1.0625021, 0.0, 0.0, 1.3269236, 1.5214385, 0.0, 1.6726425, 2.2699385, 0.0, 1.6760595, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 18, 18, 19, 19, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, -1, 22, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 0.5769231, -0.34615386, 0.008124149, -0.0034684043, 1.0, -0.015330925, 1.0, 2.0, 1.0, -0.009331205, -0.013084273, 0.0043786247, 1.0, 0.014219254, -0.008774641, 1.0, 1.0, -0.005547312, 0.03846154, 1.0, -0.005418928, 0.3846154, -0.011318562, 0.00566213, 0.017674457, -0.00038907418, 0.016618446, -0.0007029599], "split_indices": [1, 124, 115, 1, 1, 0, 0, 74, 0, 50, 0, 89, 0, 0, 0, 113, 0, 0, 15, 69, 0, 1, 69, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1770.0, 291.0, 905.0, 865.0, 157.0, 134.0, 811.0, 94.0, 246.0, 619.0, 657.0, 154.0, 133.0, 113.0, 506.0, 113.0, 144.0, 513.0, 337.0, 169.0, 233.0, 280.0, 111.0, 226.0, 109.0, 124.0, 129.0, 151.0, 125.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0012266772, 0.0050090477, -0.0076627084, -0.0019131824, 0.06613821, 0.01250585, -0.017498588, 0.016412562, -0.0014924079, 0.056978777, -0.020533578, 0.0035030984, -0.016084958, -0.025780449, 0.12620263, -0.055365764, 0.008529242, -0.009970273, 0.022372212, -0.0003973879, -0.0046868087, 0.023818878, 0.0035095238, -0.100706115, 0.010435591, -0.0025296768, 0.011097244, -0.16925745, 0.0022192185, -0.060379878, 0.06258309, -0.024201328, -0.009866266, -0.016680416, 0.003969068, 0.015829368, 0.00025954464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, 27, -1, 29, -1, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.61014533, 0.83614486, 0.0, 0.3988895, 1.5965602, 1.3547503, 2.5680532, 0.0, 0.0, 2.251463, 1.9499738, 1.4488672, 0.0, 0.08231283, 2.1833906, 2.8822513, 0.0, 0.0, 1.3877712, 0.0, 0.0, 0.0, 0.0, 2.6117013, 0.0, 1.8494922, 0.0, 1.0220995, 0.0, 2.7689838, 1.3262752, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 23, 23, 25, 25, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, 28, -1, 30, -1, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.2692307, -0.0076627084, 1.0, 1.0, 1.0, 1.0, 0.016412562, -0.0014924079, -0.3846154, 1.0, -1.0, -0.016084958, -0.53846157, 1.0, 1.0, 0.008529242, -0.009970273, 1.0, -0.0003973879, -0.0046868087, 0.023818878, 0.0035095238, 1.0, 0.010435591, 1.0, 0.011097244, 1.0, 0.0022192185, -0.07692308, -0.03846154, -0.024201328, -0.009866266, -0.016680416, 0.003969068, 0.015829368, 0.00025954464], "split_indices": [1, 1, 0, 126, 23, 69, 61, 0, 0, 1, 116, 0, 0, 1, 53, 42, 0, 0, 105, 0, 0, 0, 0, 115, 0, 69, 0, 50, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1976.0, 96.0, 1775.0, 201.0, 922.0, 853.0, 91.0, 110.0, 393.0, 529.0, 744.0, 109.0, 179.0, 214.0, 398.0, 131.0, 115.0, 629.0, 88.0, 91.0, 96.0, 118.0, 310.0, 88.0, 491.0, 138.0, 199.0, 111.0, 260.0, 231.0, 98.0, 101.0, 126.0, 134.0, 89.0, 142.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0013599718, -0.006420143, 0.048925992, -0.0012753502, -0.010376652, 0.00012758277, 0.009218731, -0.005873967, 0.0058126594, -0.030852921, 0.021412276, 0.009269699, -0.04693837, 0.008018477, 0.0113336, -0.0810524, 0.052084494, -0.0063415193, 0.032417636, -0.026024772, -0.108336285, 0.0005553708, 0.009616626, -0.007060787, 0.061853506, 0.00024340837, -0.005385124, -0.08696333, -0.017415522, -0.0024199518, 0.09948126, -0.0042537954, -0.015611603, 0.01630056, 0.003041112], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, -1, 15, 17, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.76345265, 0.8879658, 0.5978082, 0.46001267, 0.0, 0.0, 0.0, 1.0653124, 0.0, 1.6216824, 0.919712, 0.0, 2.4389658, 1.1363846, 0.0, 0.80623364, 0.37946415, 0.0, 1.4738656, 0.14096007, 0.5050211, 0.0, 0.0, 0.0, 1.2239575, 0.0, 0.0, 0.8325479, 0.0, 0.0, 1.1539476, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 24, 24, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, -1, 16, 18, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.96153843, 1.0, 5.0, -0.010376652, 0.00012758277, 0.009218731, 1.0, 0.0058126594, 1.0, 1.0, 0.009269699, 1.0, 1.0, 0.0113336, 1.0, 0.0, -0.0063415193, 1.0, 1.0, 0.23076923, 0.0005553708, 0.009616626, -0.007060787, 1.0, 0.00024340837, -0.005385124, 1.0, -0.017415522, -0.0024199518, 1.0, -0.0042537954, -0.015611603, 0.01630056, 0.003041112], "split_indices": [1, 1, 39, 0, 0, 0, 0, 124, 0, 104, 105, 0, 83, 5, 0, 121, 1, 0, 89, 93, 1, 0, 0, 0, 17, 0, 0, 61, 0, 0, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1773.0, 290.0, 1684.0, 89.0, 138.0, 152.0, 1563.0, 121.0, 816.0, 747.0, 94.0, 722.0, 652.0, 95.0, 537.0, 185.0, 166.0, 486.0, 178.0, 359.0, 90.0, 95.0, 108.0, 378.0, 88.0, 90.0, 271.0, 88.0, 115.0, 263.0, 165.0, 106.0, 137.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0044855266, 0.010388892, -0.04144923, 0.0020137057, 0.086841695, 0.0031603684, -0.014947431, 0.008555611, -0.011021627, 0.006177028, 0.011108655, -0.0342533, 0.020965546, -0.0888141, 0.008648772, 0.04320979, -0.059594672, -0.017177602, 0.004142714, -0.027597237, 0.067419924, -0.0010567572, -0.010676467, -0.01053215, 0.0037571546, -0.007997676, 0.10275213, -0.0051044035, 0.0048949034, 0.019770907, 0.062381197, -0.00468778, 0.11247813, 0.02495256, 0.002210622], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, 31, -1, 33, -1, -1], "loss_changes": [0.5548131, 1.1608753, 1.8387307, 1.1996789, 0.10880518, 0.0, 0.0, 0.82025903, 0.0, 0.0, 0.0, 2.2859414, 2.1450253, 2.5824103, 0.0, 1.6079642, 0.5989651, 0.0, 0.0, 1.210582, 1.8626046, 0.0, 0.0, 0.0, 0.0, 0.54665077, 1.8247457, 0.0, 0.0, 0.0, 1.8281628, 0.0, 2.8362203, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 20, 20, 25, 25, 26, 26, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, 32, -1, 34, -1, -1], "split_conditions": [1.3461539, 0.88461536, 1.0, 0.6923077, 1.0, 0.0031603684, -0.014947431, 1.0, -0.011021627, 0.006177028, 0.011108655, 1.0, 1.0, 1.0, 0.008648772, 0.0, 1.0, -0.017177602, 0.004142714, 1.0, 1.0, -0.0010567572, -0.010676467, -0.01053215, 0.0037571546, 1.0, -0.3846154, -0.0051044035, 0.0048949034, 0.019770907, -0.15384616, -0.00468778, 1.0, 0.02495256, 0.002210622], "split_indices": [1, 1, 17, 1, 97, 0, 0, 89, 0, 0, 0, 111, 7, 97, 0, 0, 69, 0, 0, 106, 17, 0, 0, 0, 0, 122, 1, 0, 0, 0, 1, 0, 126, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2046.0, 1813.0, 233.0, 1634.0, 179.0, 139.0, 94.0, 1544.0, 90.0, 88.0, 91.0, 347.0, 1197.0, 239.0, 108.0, 938.0, 259.0, 146.0, 93.0, 239.0, 699.0, 127.0, 132.0, 109.0, 130.0, 223.0, 476.0, 127.0, 96.0, 142.0, 334.0, 105.0, 229.0, 91.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0038617156, 0.008742594, -0.000646073, -0.005481975, 0.003702115, -0.0132477535, 0.030177336, -0.009581442, 0.00036880616, 0.04931564, -0.006782395, 0.008655479, -0.007899907, 0.08932901, 0.0035241493, -0.003001393, 0.009748087, 0.017285345, -0.00024925414, -0.01142721, 0.06210392, 0.030791724, -0.032014027, -0.009049845, 0.020667462, -0.019821933, 0.010220554, 0.025532948, -0.10063958, 0.0054929773, -0.010888782, 0.010968623, -0.0033567038, -0.0043100542, -0.015402629], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, -1, 3, -1, 5, 7, 9, -1, 11, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, 25, 27, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.780125, 0.0, 0.46287033, 0.0, 0.816279, 1.2468201, 1.3316605, 0.0, 0.6261261, 1.0883693, 0.0, 0.89253783, 0.0, 2.4311824, 1.9114321, 0.7470857, 0.0, 0.0, 0.0, 0.0, 4.081439, 1.2723093, 1.6191695, 0.0, 0.0, 1.3715127, 0.0, 1.1090813, 0.57442975, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 20, 20, 21, 21, 22, 22, 25, 25, 27, 27, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, 10, -1, 12, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, 26, 28, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008742594, -0.5, -0.005481975, 1.0, -0.23076923, 0.1923077, -0.009581442, 3.0, 1.0, -0.006782395, 1.0, -0.007899907, -0.23076923, 1.0, 1.0, 0.009748087, 0.017285345, -0.00024925414, -0.01142721, 1.0, 1.0, 1.0, -0.009049845, 0.020667462, 1.0, 0.010220554, 0.46153846, 1.0, 0.0054929773, -0.010888782, 0.010968623, -0.0033567038, -0.0043100542, -0.015402629], "split_indices": [1, 0, 1, 0, 15, 1, 1, 0, 0, 106, 0, 0, 0, 1, 81, 12, 0, 0, 0, 0, 115, 106, 106, 0, 0, 127, 0, 1, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 106.0, 1965.0, 146.0, 1819.0, 1109.0, 710.0, 157.0, 952.0, 594.0, 116.0, 862.0, 90.0, 317.0, 277.0, 762.0, 100.0, 166.0, 151.0, 92.0, 185.0, 352.0, 410.0, 90.0, 95.0, 206.0, 146.0, 223.0, 187.0, 112.0, 94.0, 92.0, 131.0, 90.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0033017609, 0.018700454, -0.010649311, -0.0042679207, 0.061365556, 0.0054326826, -0.012478349, 0.02245716, -0.010404157, -0.005873198, 0.14579058, 0.022298258, -0.008607443, 0.011141321, -0.015236071, 0.0054374686, 0.025955254, 0.009756842, 0.0011697867, 0.010729599, -0.07728423, -0.024286548, 0.011822349, -0.013800544, -0.00021055763, 0.030948881, -0.1244951, -0.0036407255, 0.06387185, -0.016436664, -0.00859097, 0.0010048383, 0.01449094], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, 15, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, 25, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.44426495, 0.96328914, 1.9915234, 1.7038676, 3.4878979, 1.4676973, 0.0, 1.6899328, 0.0, 0.0, 2.100729, 1.2770454, 0.0, 0.0, 2.691423, 0.0, 0.0, 0.0, 1.8683087, 0.0, 1.0727601, 2.8505576, 0.0, 0.0, 0.0, 0.7362313, 0.28153706, 0.0, 0.9726634, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 11, 11, 14, 14, 18, 18, 20, 20, 21, 21, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, 16, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, 26, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.012478349, -0.34615386, -0.010404157, -0.005873198, -0.34615386, 0.0, -0.008607443, 0.011141321, 1.0, 0.0054374686, 0.025955254, 0.009756842, 1.0, 0.010729599, 1.0, 1.0, 0.011822349, -0.013800544, -0.00021055763, 1.0, -0.07692308, -0.0036407255, 1.0, -0.016436664, -0.00859097, 0.0010048383, 0.01449094], "split_indices": [126, 61, 64, 113, 17, 61, 0, 1, 0, 0, 1, 0, 0, 0, 53, 0, 0, 0, 105, 0, 59, 62, 0, 0, 0, 108, 1, 0, 127, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 983.0, 1085.0, 639.0, 344.0, 951.0, 134.0, 504.0, 135.0, 142.0, 202.0, 803.0, 148.0, 150.0, 354.0, 112.0, 90.0, 176.0, 627.0, 119.0, 235.0, 515.0, 112.0, 130.0, 105.0, 332.0, 183.0, 109.0, 223.0, 90.0, 93.0, 134.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00066354964, 0.0034198174, -0.0053772707, -0.0015337557, 0.053739183, 0.015145558, -0.01609341, 0.0035628844, 0.007205528, 0.032402992, -0.006944015, -0.04539374, 0.012010981, 0.013730547, 0.013345998, -0.022125423, -0.011559641, 0.05734689, -0.009275171, 0.008437677, -0.013832433, -0.0072826743, 0.015338594, 0.00013321823, 0.011017893, -0.009867611, 0.023018854, -0.0056284103, 0.007353206, -0.022593398, 0.011153375, -0.00927344, 0.0048993775], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, -1, 19, 21, -1, 23, -1, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.31133473, 0.49228996, 0.0, 0.43663538, 0.058712423, 1.2232554, 0.7905294, 0.0, 0.0, 1.3913914, 0.0, 0.7677436, 2.327261, 0.0, 1.1370676, 0.67051494, 0.0, 1.0121049, 0.0, 0.0, 1.3319314, 0.0, 0.8460985, 0.0, 0.0, 0.0, 1.1990973, 0.0, 0.0, 0.9841544, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 17, 17, 20, 20, 22, 22, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, -1, 20, 22, -1, 24, -1, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0053772707, 1.0, 1.0, 1.0, 1.0, 0.0035628844, 0.007205528, 1.0, -0.006944015, 1.0, 1.0, 0.013730547, 0.0, 1.0, -0.011559641, 1.0, -0.009275171, 0.008437677, 1.0, -0.0072826743, 1.0, 0.00013321823, 0.011017893, -0.009867611, 1.0, -0.0056284103, 0.007353206, 1.0, 0.011153375, -0.00927344, 0.0048993775], "split_indices": [117, 125, 0, 108, 53, 50, 50, 0, 0, 53, 0, 0, 106, 0, 0, 17, 0, 93, 0, 0, 59, 0, 126, 0, 0, 0, 121, 0, 0, 137, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1975.0, 100.0, 1798.0, 177.0, 838.0, 960.0, 89.0, 88.0, 696.0, 142.0, 470.0, 490.0, 107.0, 589.0, 353.0, 117.0, 342.0, 148.0, 163.0, 426.0, 150.0, 203.0, 166.0, 176.0, 129.0, 297.0, 91.0, 112.0, 196.0, 101.0, 99.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007613235, -0.00720903, 0.037558313, -0.0024994544, -0.009760073, 0.07302277, -0.0034087026, 0.003548575, -0.005873877, 0.017039554, -0.0011609613, -0.049462534, 0.013400565, 0.0024083585, -0.010612031, -0.005678113, 0.021905584, 0.0042619067, 0.01219206, 0.063426346, -0.03067696, 0.13910471, -0.004600045, -0.08378028, 0.03124563, 0.004352657, 0.025223797, -0.016788263, -0.013694978, -0.0028344428, 0.010765549, 0.0018748165, -0.004472758], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, 15, -1, -1, -1, 17, 19, -1, 21, 23, 25, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.512925, 0.7564802, 0.75971824, 0.5744916, 0.0, 1.6481785, 0.0, 0.7964539, 0.0, 0.0, 0.0, 0.99590355, 0.767609, 0.0, 0.0, 0.0, 2.0240335, 2.01546, 0.0, 2.9978094, 2.0157251, 2.3139963, 0.0, 1.9451313, 1.2885749, 0.0, 0.0, 0.0, 0.18122323, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 12, 12, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, 16, -1, -1, -1, 18, 20, -1, 22, 24, 26, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.96153843, 3.6538463, 1.0, -0.009760073, 1.0, -0.0034087026, -0.5, -0.005873877, 0.017039554, -0.0011609613, -0.5769231, 1.0, 0.0024083585, -0.010612031, -0.005678113, 1.0, 1.0, 0.01219206, 1.0, 1.0, 1.0, -0.004600045, 1.0, 1.0, 0.004352657, 0.025223797, -0.016788263, -0.15384616, -0.0028344428, 0.010765549, 0.0018748165, -0.004472758], "split_indices": [1, 1, 1, 40, 0, 23, 0, 1, 0, 0, 0, 1, 26, 0, 0, 0, 23, 39, 0, 106, 115, 15, 0, 93, 97, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1777.0, 299.0, 1689.0, 88.0, 200.0, 99.0, 1525.0, 164.0, 93.0, 107.0, 239.0, 1286.0, 104.0, 135.0, 139.0, 1147.0, 975.0, 172.0, 362.0, 613.0, 214.0, 148.0, 330.0, 283.0, 116.0, 98.0, 150.0, 180.0, 159.0, 124.0, 88.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0033584773, -0.022452813, 0.010208943, 0.0064364895, -0.06861294, -0.0047198613, 0.056604233, -0.002072968, -0.013796109, 0.03293118, -0.02278158, -0.008348323, 0.09624314, 0.07738787, -0.007667754, -0.06575192, 0.0030992106, 0.0130364215, 0.020907646, 0.017224569, 0.0001620914, -0.012317213, -0.02487121, 0.010320014, -0.041420806, 0.007818434, -0.004996374, -0.0110091865, 0.0064136996, -0.0014282073, -0.009423153, 0.0055485587, -0.0056173885], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, -1, 7, 9, 11, -1, -1, 13, 15, -1, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.36672333, 1.7432697, 1.1352144, 0.0, 0.9430543, 0.8432527, 2.215613, 0.0, 0.0, 1.9588821, 0.9319453, 0.0, 2.9198205, 2.0555067, 0.0, 0.7394241, 2.3307471, 0.7346751, 0.0, 0.0, 0.0, 0.0, 1.3957024, 0.0, 0.7645575, 0.0, 0.0, 0.0, 0.0, 0.6418515, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, 6, -1, 8, 10, 12, -1, -1, 14, 16, -1, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.0064364895, 1.0, 0.0, 1.0, -0.002072968, -0.013796109, 1.0, 1.0, -0.008348323, 1.0, 1.0, -0.007667754, -0.1923077, -0.03846154, 3.0, 0.020907646, 0.017224569, 0.0001620914, -0.012317213, 1.0, 0.010320014, 1.0, 0.007818434, -0.004996374, -0.0110091865, 0.0064136996, 1.0, -0.009423153, 0.0055485587, -0.0056173885], "split_indices": [5, 53, 0, 0, 93, 0, 89, 0, 0, 121, 69, 0, 106, 122, 0, 1, 1, 0, 0, 0, 0, 0, 64, 0, 13, 0, 0, 0, 0, 106, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 435.0, 1639.0, 151.0, 284.0, 1240.0, 399.0, 168.0, 116.0, 402.0, 838.0, 88.0, 311.0, 286.0, 116.0, 315.0, 523.0, 179.0, 132.0, 127.0, 159.0, 131.0, 184.0, 161.0, 362.0, 88.0, 91.0, 94.0, 90.0, 206.0, 156.0, 101.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-6.518339e-05, 0.013231045, -0.013080957, -0.005696126, 0.015014778, -0.024861634, 0.006290438, -0.04760716, 0.03238049, -0.003110875, -0.09040391, -0.019312534, -0.01366116, 0.013347504, 0.0067698644, -0.025953464, 0.006154525, -0.017101252, 0.0014058254, 0.008629668, -0.06444072, 0.0385879, -0.007614057, -0.06621705, 0.023039827, -0.010932474, -0.0031548582, -0.008700277, 0.010860111, -0.0147872465, 0.0027559055, 0.009518205, -0.0027405464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.35719728, 2.6458673, 0.9336503, 1.43146, 0.0, 1.2873112, 0.0, 1.0753337, 1.2168741, 1.0013471, 1.8946242, 1.5441685, 0.0, 0.0, 0.9892677, 0.9882958, 0.0, 0.0, 0.0, 0.0, 0.33512682, 2.3829048, 0.0, 2.1057649, 0.8224674, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 20, 20, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.84615386, 1.0, 1.0, 0.015014778, 1.0769231, 0.006290438, 1.0, -0.3846154, 1.0, 1.0, -0.34615386, -0.01366116, 0.013347504, 1.0, 1.0, 0.006154525, -0.017101252, 0.0014058254, 0.008629668, 1.0, 1.0, -0.007614057, 1.0, 1.0, -0.010932474, -0.0031548582, -0.008700277, 0.010860111, -0.0147872465, 0.0027559055, 0.009518205, -0.0027405464], "split_indices": [108, 1, 88, 106, 0, 1, 0, 64, 1, 61, 80, 1, 0, 0, 74, 12, 0, 0, 0, 0, 109, 2, 0, 97, 53, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1021.0, 1043.0, 897.0, 124.0, 903.0, 140.0, 427.0, 470.0, 678.0, 225.0, 324.0, 103.0, 95.0, 375.0, 501.0, 177.0, 127.0, 98.0, 97.0, 227.0, 271.0, 104.0, 275.0, 226.0, 96.0, 131.0, 97.0, 174.0, 147.0, 128.0, 93.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0038960394, 0.018024301, -0.010150315, 0.0035611745, 0.017078087, 0.012922938, -0.05690402, -0.0081314705, 0.011399174, -0.042549137, 0.06277183, -0.014796418, -0.0010652326, 0.03152568, -0.05860421, 0.005218918, -0.017282568, -0.0031943645, 0.11902207, -0.005849232, 0.0065737674, -0.007195728, 0.08450274, -0.10338834, 0.007671124, -0.0063717715, 0.0053621256, 0.022971848, 0.0009288224, -0.0058667464, 0.0053671617, 0.012034629, 0.0043702074, -0.006333638, -0.014489144], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, 19, 21, 23, 25, -1, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.409602, 2.273408, 1.116517, 1.2137522, 0.0, 1.9162982, 1.7389631, 1.7013644, 0.0, 2.0411632, 1.944636, 0.0, 0.81329477, 0.976441, 2.2664342, 0.8008066, 0.0, 0.0, 2.7816951, 0.0, 0.0, 0.86156076, 0.29394996, 0.46710038, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, 20, 22, 24, 26, -1, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1538461, 1.0, 1.0, 0.017078087, 1.0, 1.0, -0.03846154, 0.011399174, 1.0, 1.0, -0.014796418, 1.0, 1.0, 1.0, 1.0, -0.017282568, -0.0031943645, 1.0, -0.005849232, 0.0065737674, 1.0, 1.0, 1.0, 0.007671124, -0.0063717715, 0.0053621256, 0.022971848, 0.0009288224, -0.0058667464, 0.0053671617, 0.012034629, 0.0043702074, -0.006333638, -0.014489144], "split_indices": [108, 1, 106, 44, 0, 50, 137, 1, 0, 0, 71, 0, 23, 115, 0, 17, 0, 0, 61, 0, 0, 97, 62, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1029.0, 1035.0, 940.0, 89.0, 693.0, 342.0, 850.0, 90.0, 328.0, 365.0, 130.0, 212.0, 476.0, 374.0, 240.0, 88.0, 136.0, 229.0, 114.0, 98.0, 275.0, 201.0, 281.0, 93.0, 99.0, 141.0, 114.0, 115.0, 149.0, 126.0, 107.0, 94.0, 143.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004960649, -0.010444692, 0.027613457, -0.014566265, 0.006414243, -0.003999768, 0.062101983, 0.008931131, -0.04100084, -0.0017890076, 0.013440253, 0.0411589, -0.038164135, -0.023243574, -0.015592046, -0.0078515345, 0.014201276, -0.012919569, 0.0075404933, -0.041344844, 0.008536401, -0.064257875, 0.007234521, 0.009610682, -0.0119494, -0.021870326, -0.015199547, -0.013805774, 0.0017741941, 0.030569535, -0.07066573, -0.011968353, 0.011854668, -0.012658748, -0.00078683376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, 17, 19, -1, 21, -1, -1, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.37067342, 0.54597104, 0.6972107, 1.0453849, 0.0, 0.0, 1.1451273, 1.3523378, 1.6162007, 0.0, 0.0, 2.6147902, 1.5061245, 1.3486314, 0.0, 1.6104033, 0.0, 0.0, 2.711486, 1.2670611, 0.0, 1.2647798, 0.0, 0.0, 0.0, 1.2794123, 0.0, 0.0, 0.0, 3.1857393, 0.9095402, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 19, 19, 21, 21, 25, 25, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, 18, 20, -1, 22, -1, -1, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 0.006414243, -0.003999768, 1.0, 1.0, 1.0, -0.0017890076, 0.013440253, 1.0, -0.3846154, 1.0, -0.015592046, 1.0, 0.014201276, -0.012919569, 1.0, 1.0, 0.008536401, 1.0, 0.007234521, 0.009610682, -0.0119494, 0.0, -0.015199547, -0.013805774, 0.0017741941, -0.34615386, 1.0, -0.011968353, 0.011854668, -0.012658748, -0.00078683376], "split_indices": [1, 114, 81, 126, 0, 0, 39, 105, 42, 0, 0, 61, 1, 73, 0, 12, 0, 0, 39, 61, 0, 137, 0, 0, 0, 1, 0, 0, 0, 1, 122, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1776.0, 299.0, 1683.0, 93.0, 101.0, 198.0, 891.0, 792.0, 94.0, 104.0, 529.0, 362.0, 686.0, 106.0, 356.0, 173.0, 121.0, 241.0, 588.0, 98.0, 209.0, 147.0, 142.0, 99.0, 500.0, 88.0, 110.0, 99.0, 241.0, 259.0, 89.0, 152.0, 137.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033520756, 0.0034970443, -0.05119015, -0.0043664733, 0.0081846295, -0.01808667, 0.011901031, -0.008681432, 0.0010872736, 0.0519953, -0.01085512, 0.012718304, -0.0047658267, -0.044204094, 0.019098135, -0.0103705805, -0.0132295, 0.011389509, -0.010113644, 0.05927832, -0.07173526, 0.01783908, -0.0072290557, -0.0009484963, 0.012667999, -0.0014835072, -0.014663446, 0.06303833, -0.0032596649, 0.00074366323, 0.012176367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, 9, 11, 13, -1, -1, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.67757773, 1.1145264, 5.716391, 0.73922443, 0.0, 0.0, 0.0, 0.0, 0.9374801, 2.195369, 1.2476392, 0.0, 0.0, 1.7614307, 1.8221259, 1.8249909, 0.0, 0.0, 0.87422127, 0.9269524, 0.9674232, 0.7910411, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5975365, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 21, 21, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, 10, 12, 14, -1, -1, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, -0.53846157, 0.0081846295, -0.01808667, 0.011901031, -0.008681432, -0.30769232, 1.0, 1.0, 0.012718304, -0.0047658267, 1.0, -0.03846154, 1.0, -0.0132295, 0.011389509, 1.0, 0.46153846, 1.0, 1.0, -0.0072290557, -0.0009484963, 0.012667999, -0.0014835072, -0.014663446, 1.0, -0.0032596649, 0.00074366323, 0.012176367], "split_indices": [0, 125, 122, 1, 0, 0, 0, 0, 1, 126, 111, 0, 0, 15, 1, 12, 0, 0, 71, 1, 39, 106, 0, 0, 0, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1809.0, 259.0, 1644.0, 165.0, 147.0, 112.0, 102.0, 1542.0, 293.0, 1249.0, 167.0, 126.0, 591.0, 658.0, 427.0, 164.0, 155.0, 503.0, 200.0, 227.0, 347.0, 156.0, 99.0, 101.0, 129.0, 98.0, 183.0, 164.0, 94.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015832636, 0.0054103946, -0.0047637685, -0.02828056, 0.007946011, -0.04146535, 0.0057133916, 0.018471757, -0.03806526, -0.01468018, -0.09870101, 0.0089531895, 0.012101266, 0.0025500369, -0.013307917, -0.08165447, 0.045408886, -0.0030562608, -0.016133325, -0.0009975815, 0.008554677, -0.016902162, 0.0019455807, 0.013010288, -0.0039285077, -0.038431473, 0.03969547, -0.08738836, 0.012235828, 0.007742731, 0.0145860985, -0.0018086523, -0.015075003, -0.005887997, 0.006898598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.36715603, 0.0, 0.58612967, 0.7748062, 0.61651766, 0.9137076, 0.0, 1.0111797, 1.4313902, 1.6339155, 0.81085515, 0.72253287, 0.0, 0.0, 0.0, 1.6960741, 1.5350372, 0.0, 0.0, 1.2780482, 0.0, 0.0, 0.0, 0.0, 0.0, 3.4399617, 1.3636959, 1.4710114, 0.0, 1.2607789, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 19, 19, 25, 25, 26, 26, 27, 27, 29, 29], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0054103946, -0.1923077, 1.0, 1.3461539, -0.30769232, 0.0057133916, 1.1923077, 1.0, 1.0, 1.0, 1.0, 0.012101266, 0.0025500369, -0.013307917, 1.0, 1.0, -0.0030562608, -0.016133325, 1.0, 0.008554677, -0.016902162, 0.0019455807, 0.013010288, -0.0039285077, 1.0, 1.0, 1.0, 0.012235828, 1.0, 0.0145860985, -0.0018086523, -0.015075003, -0.005887997, 0.006898598], "split_indices": [1, 0, 1, 90, 1, 1, 0, 1, 17, 93, 93, 44, 0, 0, 0, 106, 53, 0, 0, 124, 0, 0, 0, 0, 0, 62, 58, 39, 0, 53, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 112.0, 1961.0, 688.0, 1273.0, 596.0, 92.0, 1036.0, 237.0, 406.0, 190.0, 948.0, 88.0, 142.0, 95.0, 192.0, 214.0, 91.0, 99.0, 839.0, 109.0, 103.0, 89.0, 107.0, 107.0, 437.0, 402.0, 335.0, 102.0, 309.0, 93.0, 160.0, 175.0, 148.0, 161.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00060751755, 0.0075129317, -0.026572317, 0.013826361, -0.0057029063, 0.0067232097, -0.05419571, 0.0014291212, 0.013078788, -0.023868144, -0.0122735975, 0.012002197, -0.057203375, 0.0024622546, -0.0073224767, -0.0020451874, 0.016933292, 0.0058948332, -0.017904883, -0.055168234, 0.010940443, 0.0007408671, -0.0117149195, -0.0014619066, 0.01078578, 0.025493914, -0.04261447, 0.0038456852, 0.0114011085, -0.013475156, 0.003909199, -0.0029120918, 0.0067162514], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, -1, -1, 21, 23, -1, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [0.3909541, 0.67682636, 1.0934846, 2.1938498, 0.0, 0.0, 0.6776426, 0.8480584, 0.0, 0.54089427, 0.0, 2.5614896, 2.9578848, 0.0, 0.0, 0.7339859, 0.0, 0.0, 0.0, 0.8106228, 1.0277128, 0.0, 0.0, 0.8408503, 0.0, 0.8776376, 2.258459, 0.7681415, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 19, 19, 20, 20, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, -1, -1, 22, 24, -1, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 0.84615386, -0.0057029063, 0.0067232097, 0.84615386, 3.0, 0.013078788, 1.0, -0.0122735975, 1.0, 1.0, 0.0024622546, -0.0073224767, -1.0, 0.016933292, 0.0058948332, -0.017904883, 1.0, 1.0, 0.0007408671, -0.0117149195, -0.03846154, 0.01078578, -0.1923077, 1.0, 1.0, 0.0114011085, -0.013475156, 0.003909199, -0.0029120918, 0.0067162514], "split_indices": [80, 1, 26, 1, 0, 0, 1, 0, 0, 93, 0, 102, 69, 0, 0, 0, 0, 0, 0, 15, 73, 0, 0, 1, 0, 1, 93, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2083.0, 1661.0, 422.0, 1513.0, 148.0, 96.0, 326.0, 1368.0, 145.0, 226.0, 100.0, 1159.0, 209.0, 114.0, 112.0, 1064.0, 95.0, 107.0, 102.0, 209.0, 855.0, 104.0, 105.0, 758.0, 97.0, 458.0, 300.0, 368.0, 90.0, 141.0, 159.0, 242.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037121093, -0.00761606, 0.0075131557, -0.026807075, 0.006433593, -0.007931092, -0.012723881, -0.009429366, 0.06501466, 0.015071993, -0.009253306, -0.04516478, 0.0075790244, 0.015524151, -0.00094908196, 0.089004464, -0.029807827, 0.018392779, -0.01622114, 0.010601244, -0.012225301, 0.0048672813, 0.014143556, 0.016738327, -0.014689303, -0.0030713617, 0.006443006, 0.04935784, -0.07000409, 0.0084370775, -0.0024916073, 0.012103815, -0.0009926605, -0.013118354, 0.0011568468], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, -1, 17, 19, -1, -1, 21, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6328403, 0.5281996, 0.0, 1.5696788, 1.0510036, 1.3564364, 0.0, 0.54094374, 1.6200976, 1.8183057, 0.0, 2.1350498, 1.1754929, 0.0, 0.0, 0.4377283, 1.8584044, 0.42049494, 0.0, 0.0, 1.7862161, 0.0, 0.0, 0.6873944, 0.0, 0.0, 0.0, 1.0326356, 1.2925564, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 20, 20, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, -1, 18, 20, -1, -1, 22, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.115384616, 0.0075131557, 3.0, 1.0, 1.0, -0.012723881, 0.26923078, 1.0, -0.42307693, -0.009253306, 1.0, 0.46153846, 0.015524151, -0.00094908196, 1.0, 1.0, 1.0, -0.01622114, 0.010601244, 1.0, 0.0048672813, 0.014143556, 0.0, -0.014689303, -0.0030713617, 0.006443006, 1.0, 1.0, 0.0084370775, -0.0024916073, 0.012103815, -0.0009926605, -0.013118354, 0.0011568468], "split_indices": [114, 1, 0, 0, 0, 42, 0, 1, 124, 1, 0, 121, 1, 0, 0, 97, 50, 108, 0, 0, 12, 0, 0, 0, 0, 0, 0, 111, 50, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1959.0, 97.0, 828.0, 1131.0, 697.0, 131.0, 890.0, 241.0, 548.0, 149.0, 287.0, 603.0, 109.0, 132.0, 207.0, 341.0, 186.0, 101.0, 101.0, 502.0, 117.0, 90.0, 244.0, 97.0, 90.0, 96.0, 243.0, 259.0, 93.0, 151.0, 110.0, 133.0, 148.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026786004, 0.004575738, -0.039523013, 0.029903261, -0.020606387, -0.010725949, 0.002901035, 0.0102662295, 0.016486319, -0.034614865, 0.006878666, 0.024575818, -0.009301163, -0.059430275, 0.017466854, -0.004020695, 0.07223669, -0.11123074, -0.006396458, 0.0064040385, -0.0058974456, 0.030465262, -0.011265149, 0.019150943, -0.0020625652, -0.01705819, -0.004297687, -0.0099707125, 0.0057054777, -0.0049845856, 0.010781041, 0.006421392, -0.008830526], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.55567914, 1.10786, 1.5876356, 2.295083, 1.0907187, 0.0, 0.0, 1.1172647, 0.0, 0.97319937, 0.0, 0.90498877, 0.0, 1.4010594, 0.86511457, 1.5546886, 2.7579112, 1.0451434, 1.4920107, 0.0, 0.0, 0.86369073, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.245384, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.84615386, 1.0, -0.010725949, 0.002901035, 0.5, 0.016486319, 1.0, 0.006878666, 1.0, -0.009301163, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 0.0064040385, -0.0058974456, 1.0, -0.011265149, 0.019150943, -0.0020625652, -0.01705819, -0.004297687, -0.0099707125, 0.0057054777, 1.0, 0.010781041, 0.006421392, -0.008830526], "split_indices": [64, 108, 93, 1, 74, 0, 0, 1, 0, 23, 0, 12, 0, 109, 109, 0, 69, 1, 113, 0, 0, 116, 0, 0, 0, 0, 0, 0, 0, 105, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2079.0, 1737.0, 342.0, 866.0, 871.0, 172.0, 170.0, 756.0, 110.0, 753.0, 118.0, 664.0, 92.0, 510.0, 243.0, 415.0, 249.0, 258.0, 252.0, 151.0, 92.0, 315.0, 100.0, 109.0, 140.0, 138.0, 120.0, 102.0, 150.0, 216.0, 99.0, 118.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031586858, -0.02552872, 0.0031281346, -0.05845521, 0.0054188026, 0.021867394, -0.018692577, -0.010105226, -0.014317097, 0.013333405, -0.0037951227, -0.010322873, -0.0012399477, -0.005190898, 0.003465432, 0.08356502, -0.03313495, 0.058650643, -0.050883777, 0.006262529, 0.010498061, 0.019685728, -0.08675899, 0.012619691, -0.0030666715, -0.020762354, 0.006629591, -0.004743571, 0.006457321, -0.019614402, 0.0026865727, 0.0071993074, -0.0078100823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, 15, -1, 17, -1, -1, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.29167992, 1.1942809, 0.6620156, 1.3189131, 0.0, 2.491509, 1.1035824, 0.3835791, 0.0, 0.0, 1.8146969, 0.0, 1.843383, 0.0, 0.0, 0.07982123, 1.5012033, 1.6952885, 3.055961, 0.0, 0.0, 0.8044476, 3.2687857, 0.0, 0.0, 0.0, 1.3734921, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, 16, -1, 18, -1, -1, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.3846154, 1.0, 1.0, 1.0, 0.0054188026, 1.0, -0.1923077, 1.0, -0.014317097, 0.013333405, 0.0, -0.010322873, 0.46153846, -0.005190898, 0.003465432, 0.03846154, 1.0, 1.0, 0.8076923, 0.006262529, 0.010498061, 1.0, 1.0, 0.012619691, -0.0030666715, -0.020762354, 1.3461539, -0.004743571, 0.006457321, -0.019614402, 0.0026865727, 0.0071993074, -0.0078100823], "split_indices": [1, 12, 12, 113, 0, 89, 1, 97, 0, 0, 0, 0, 1, 0, 0, 1, 39, 97, 1, 0, 0, 53, 97, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 455.0, 1619.0, 322.0, 133.0, 871.0, 748.0, 205.0, 117.0, 163.0, 708.0, 128.0, 620.0, 106.0, 99.0, 178.0, 530.0, 281.0, 339.0, 90.0, 88.0, 267.0, 263.0, 160.0, 121.0, 91.0, 248.0, 107.0, 160.0, 134.0, 129.0, 140.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0026934992, -0.0002647271, 0.0062925457, -0.036325734, 0.009357708, 0.012118998, -0.020458005, -0.004154505, 0.05452389, -0.010613167, 0.07488714, 0.02083484, -0.041899923, 0.017349958, -0.013016893, -3.6171998e-05, 0.01308574, 0.011675931, 0.0041455394, -0.08620045, -0.010029029, -0.012100266, 0.00692184, -0.015706414, 0.00574011, 0.0017381049, -0.018374812, -0.06359412, 0.008835579, 0.0093741985, -0.008504696, -0.009564999, -0.0032931943, 0.045870632, -0.0077916207, -0.0022869701, 0.015631862], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, 15, 17, 19, -1, 21, -1, -1, -1, 23, 25, 27, -1, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [0.36918864, 0.68531466, 0.0, 3.3908315, 0.9514501, 2.397427, 0.0, 1.13188, 2.8848214, 0.0, 0.88866854, 1.1558583, 0.6748867, 0.0, 2.0335755, 0.0, 0.0, 0.0, 0.6501947, 2.0208273, 1.4650577, 0.0, 0.0, 0.77911854, 0.0, 0.0, 0.0, 0.17692214, 0.0, 1.0481243, 0.0, 0.0, 0.0, 1.7613981, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 14, 14, 18, 18, 19, 19, 20, 20, 23, 23, 27, 27, 29, 29, 33, 33], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, 16, 18, 20, -1, 22, -1, -1, -1, 24, 26, 28, -1, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.0, -0.3846154, 0.0062925457, 2.0, 1.0, 1.0, -0.020458005, 1.0, 1.0, -0.010613167, 1.0, -0.23076923, 0.115384616, 0.017349958, 0.07692308, -3.6171998e-05, 0.01308574, 0.011675931, 1.2692307, 1.0, 1.0, -0.012100266, 0.00692184, 1.0, 0.00574011, 0.0017381049, -0.018374812, 0.84615386, 0.008835579, 1.0, -0.008504696, -0.009564999, -0.0032931943, 1.0, -0.0077916207, -0.0022869701, 0.015631862], "split_indices": [114, 1, 0, 0, 0, 109, 0, 109, 126, 0, 93, 1, 1, 0, 1, 0, 0, 0, 1, 93, 93, 0, 0, 50, 0, 0, 0, 1, 0, 74, 0, 0, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1975.0, 97.0, 416.0, 1559.0, 323.0, 93.0, 1200.0, 359.0, 112.0, 211.0, 722.0, 478.0, 130.0, 229.0, 90.0, 121.0, 107.0, 615.0, 200.0, 278.0, 99.0, 130.0, 448.0, 167.0, 97.0, 103.0, 180.0, 98.0, 329.0, 119.0, 88.0, 92.0, 232.0, 97.0, 143.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0037844866, -0.0023935004, 0.03378689, 0.0031915614, -0.005481235, 0.009815046, -0.0011932995, -0.02618646, 0.01868703, 0.0047973585, -0.0061816573, 0.004720254, -0.052502725, -0.0050103916, 0.03148535, -0.11390888, 0.002665086, -0.001968593, 0.064322636, -0.017817369, -0.00442386, -0.044143863, 0.008699966, 0.009927192, 0.018351262, -0.014486789, 0.007399857, -0.011679928, 0.070153646, 0.019328324, -0.0026824493], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [0.38535196, 0.5047227, 0.7992648, 0.7092424, 0.0, 0.0, 0.68555135, 1.0390525, 0.89801645, 0.0, 0.0, 0.0, 1.9247652, 0.0, 0.9447419, 0.99844766, 0.0, 1.5984627, 2.8137918, 0.0, 0.0, 3.439037, 0.0, 2.274421, 0.0, 0.0, 0.0, 0.0, 2.4120576, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 12, 12, 14, 14, 15, 15, 17, 17, 18, 18, 21, 21, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 1.0, -0.005481235, 0.009815046, 1.0, 0.0, -1.0, 0.0047973585, -0.0061816573, 0.004720254, 0.26923078, -0.0050103916, 1.0, 1.0, 0.002665086, 1.0, 1.0, -0.017817369, -0.00442386, 1.0, 0.008699966, -0.3846154, 0.018351262, -0.014486789, 0.007399857, -0.011679928, 1.0, 0.019328324, -0.0026824493], "split_indices": [74, 1, 122, 127, 0, 0, 127, 0, 0, 0, 0, 0, 1, 0, 105, 80, 0, 61, 111, 0, 0, 69, 0, 1, 0, 0, 0, 0, 13, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2079.0, 1724.0, 355.0, 1558.0, 166.0, 125.0, 230.0, 538.0, 1020.0, 127.0, 103.0, 142.0, 396.0, 160.0, 860.0, 223.0, 173.0, 426.0, 434.0, 116.0, 107.0, 289.0, 137.0, 298.0, 136.0, 156.0, 133.0, 96.0, 202.0, 89.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.001163495, 0.0052401307, -0.004228287, -0.03541543, 0.0023282093, -0.012535154, 0.001994957, -0.010116891, 0.044992223, 0.005883659, -0.0064148004, 0.007694123, -0.017829938, 0.01313366, -0.0039736256, -0.0032541493, -0.071916945, -0.014128703, 0.008052691, 0.031531494, -0.024955867, -0.016024588, 0.002402652, 0.082229964, -0.0076778843, -0.07647049, 0.01227587, 0.016373383, 0.0005441179, -0.013660907, -0.003476853, 0.014733084, -0.043649964, -0.010290057, -0.0002528245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, -1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.33670223, 0.0, 0.39668816, 1.1338519, 0.8511257, 0.0, 0.8948024, 0.8333107, 1.5305095, 0.0, 0.0, 0.0, 0.898731, 0.0, 2.6803062, 0.6779075, 2.0508497, 0.0, 0.0, 1.8944533, 1.0606422, 0.0, 0.0, 1.4707667, 0.0, 0.58183146, 2.4245331, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5530822, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 25, 25, 26, 26, 32, 32], "right_children": [2, -1, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [-0.5769231, 0.0052401307, -0.3846154, 1.0, 1.0, -0.012535154, -0.46153846, -0.30769232, 1.0, 0.005883659, -0.0064148004, 0.007694123, 1.0, 0.01313366, 0.03846154, 1.0, 1.0, -0.014128703, 0.008052691, 1.0, 1.0, -0.016024588, 0.002402652, 0.7307692, -0.0076778843, 1.0, -0.03846154, 0.016373383, 0.0005441179, -0.013660907, -0.003476853, 0.014733084, 0.42307693, -0.010290057, -0.0002528245], "split_indices": [1, 0, 1, 89, 0, 0, 1, 1, 126, 0, 0, 0, 61, 0, 1, 53, 116, 0, 0, 121, 111, 0, 0, 1, 0, 69, 1, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 111.0, 1940.0, 337.0, 1603.0, 99.0, 238.0, 1241.0, 362.0, 128.0, 110.0, 101.0, 1140.0, 131.0, 231.0, 898.0, 242.0, 88.0, 143.0, 345.0, 553.0, 126.0, 116.0, 235.0, 110.0, 232.0, 321.0, 114.0, 121.0, 95.0, 137.0, 94.0, 227.0, 93.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0049611726, 0.0005527646, -0.048690766, -0.062207762, 0.010516645, -0.009875177, 0.0009323835, -0.013452043, -0.0008475412, 0.0021026277, 0.0117777055, 0.009002735, -0.0065927296, 0.0019714015, 0.007868207, 0.02882816, -0.021826783, 0.04892584, -0.007933796, 0.0010668053, -0.016569221, -0.0014066439, 0.018404922, -0.0359159, 0.0088788, 0.009338233, -0.03634847, -0.014649475, 0.0093573155, 0.0020717974, -0.008248732, -0.00750493, 0.011435094], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, 23, -1, 25, -1, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.4974353, 1.1456199, 0.67088646, 0.9752675, 1.4268379, 0.0, 0.0, 0.0, 0.0, 0.6881605, 0.0, 0.65210813, 0.0, 0.7727228, 0.0, 1.2347684, 2.1111956, 3.2577255, 0.0, 1.7940246, 0.0, 1.1559225, 0.0, 1.9474356, 0.0, 0.0, 0.67141014, 0.0, 2.4459553, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, 24, -1, 26, -1, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.0, 3.5, 1.0, 0.88461536, -0.009875177, 0.0009323835, -0.013452043, -0.0008475412, 1.0, 0.0117777055, 0.53846157, -0.0065927296, 1.0, 0.007868207, 0.23076923, 3.0, -0.1923077, -0.007933796, 1.0, -0.016569221, 1.0, 0.018404922, -0.34615386, 0.0088788, 0.009338233, 1.0, -0.014649475, 1.0, 0.0020717974, -0.008248732, -0.00750493, 0.011435094], "split_indices": [1, 26, 1, 116, 1, 0, 0, 0, 0, 88, 0, 1, 0, 69, 0, 1, 0, 1, 0, 50, 0, 89, 0, 1, 0, 0, 59, 0, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1832.0, 231.0, 251.0, 1581.0, 124.0, 107.0, 107.0, 144.0, 1466.0, 115.0, 1331.0, 135.0, 1209.0, 122.0, 568.0, 641.0, 479.0, 89.0, 553.0, 88.0, 349.0, 130.0, 389.0, 164.0, 94.0, 255.0, 113.0, 276.0, 114.0, 141.0, 153.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013391954, 0.00481294, -0.025333993, 0.009750716, -0.0071327533, -0.0073703574, 0.008407679, 0.015882652, -0.0045917626, -0.0028445998, 0.005618099, 0.009296075, 0.007195875, 0.035176687, -0.025406357, 0.07523408, 0.0005721286, -0.04621579, 0.006106388, 0.0005836739, 0.12449538, 0.049563397, -0.045434885, -0.014916634, -0.01621258, 0.0057031084, 0.018855235, -7.189332e-05, 0.010039231, -0.010565431, 0.0010651803, -0.008797292, 0.046562407, 0.0040672715, 0.0052648387], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, 23, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.30453858, 0.6173336, 0.68710196, 0.52636886, 0.0, 0.0, 0.43663442, 0.51302624, 0.0, 0.0, 0.0, 1.1163634, 0.0, 0.98695153, 0.9554796, 1.1281388, 0.8610058, 1.5527346, 0.0, 0.0, 0.83405995, 0.4728226, 0.6653694, 1.513612, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0065592527, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 22, 22, 23, 23, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, 24, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.0071327533, -0.0073703574, 1.0, 1.0, -0.0045917626, -0.0028445998, 0.005618099, 1.0, 0.007195875, 1.0, 1.0, 1.0, 1.0, 1.0, 0.006106388, 0.0005836739, 1.0, 1.0, 1.0, 1.0, -0.01621258, 0.0057031084, 0.018855235, -7.189332e-05, 0.010039231, -0.010565431, 0.0010651803, -0.008797292, 1.0, 0.0040672715, 0.0052648387], "split_indices": [80, 31, 122, 41, 0, 0, 93, 88, 0, 0, 0, 108, 0, 13, 74, 39, 39, 115, 0, 0, 122, 17, 106, 93, 0, 0, 0, 0, 0, 0, 0, 0, 109, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1642.0, 421.0, 1542.0, 100.0, 173.0, 248.0, 1389.0, 153.0, 140.0, 108.0, 1243.0, 146.0, 712.0, 531.0, 330.0, 382.0, 428.0, 103.0, 137.0, 193.0, 185.0, 197.0, 337.0, 91.0, 94.0, 99.0, 93.0, 92.0, 95.0, 102.0, 154.0, 183.0, 93.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.003724957, -0.009564429, 0.035532903, -0.003714338, -0.009582758, 0.009121848, -0.0029659935, 0.0011426647, -0.004864163, -0.014155937, 0.028901543, -0.033337437, 0.032485384, 0.045401983, -0.0052331365, -0.077674754, 0.018343234, -0.024384297, 0.015372408, 0.009515352, 0.015179153, -0.17766044, 0.005255349, 0.009822218, -0.007218621, -0.013966634, 0.00774668, -0.03885079, 0.010979747, -0.0100382, -0.026848247, -0.015248706, 0.0037748448], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.47270346, 0.9058416, 0.9692905, 0.36681446, 0.0, 0.0, 0.0, 0.6442275, 0.0, 0.8749682, 0.7224642, 1.587928, 1.9650201, 1.7104466, 0.0, 4.8568187, 2.3140473, 2.2778711, 0.0, 1.624837, 0.0, 1.4809208, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9672065, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.3076923, 0.96153843, 1.0, 1.0, -0.009582758, 0.009121848, -0.0029659935, 0.03846154, -0.004864163, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0052331365, 1.0, 1.0, 1.0, 0.015372408, 1.0, 0.015179153, 1.0, 0.005255349, 0.009822218, -0.007218621, -0.013966634, 0.00774668, 1.0, 0.010979747, -0.0100382, -0.026848247, -0.015248706, 0.0037748448], "split_indices": [1, 1, 115, 40, 0, 0, 0, 1, 0, 50, 64, 97, 0, 0, 0, 122, 69, 15, 0, 116, 0, 105, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1795.0, 267.0, 1681.0, 114.0, 144.0, 123.0, 1517.0, 164.0, 978.0, 539.0, 693.0, 285.0, 448.0, 91.0, 373.0, 320.0, 194.0, 91.0, 335.0, 113.0, 211.0, 162.0, 170.0, 150.0, 91.0, 103.0, 226.0, 109.0, 114.0, 97.0, 91.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003770441, 0.006774789, -0.004660416, 0.0030488914, 0.0068929507, 0.01127441, -0.051616527, 0.0066178683, 0.0071202065, 0.0013565448, -0.011679852, 0.013491843, -0.0061867265, 0.0060346033, 0.008318833, -0.0026441875, 0.009778185, -0.07838314, 0.014821111, -0.015815023, -1.3540634e-05, 0.027913041, -0.0054093334, -0.0003533434, 0.0084100505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, -1, 17, -1, 19, 21, -1, -1, 23, -1, -1, -1], "loss_changes": [0.31191745, 0.45042703, 0.0, 0.8251103, 0.0, 0.44509357, 1.0196862, 0.6967322, 0.0, 0.0, 0.0, 0.69905454, 0.0, 0.96745026, 0.0, 1.4683121, 0.0, 1.2982509, 0.8138052, 0.0, 0.0, 1.3393086, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, -1, 18, -1, 20, 22, -1, -1, 24, -1, -1, -1], "split_conditions": [1.0, 3.1923077, -0.004660416, 0.96153843, 0.0068929507, 5.0, 1.0, 1.0, 0.0071202065, 0.0013565448, -0.011679852, 1.0, -0.0061867265, 1.0, 0.008318833, -1.0, 0.009778185, 1.0, 2.0, -0.015815023, -1.3540634e-05, 0.115384616, -0.0054093334, -0.0003533434, 0.0084100505], "split_indices": [43, 1, 0, 1, 0, 0, 23, 88, 0, 0, 0, 41, 0, 102, 0, 0, 0, 13, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1945.0, 116.0, 1835.0, 110.0, 1595.0, 240.0, 1480.0, 115.0, 120.0, 120.0, 1345.0, 135.0, 1215.0, 130.0, 1110.0, 105.0, 208.0, 902.0, 103.0, 105.0, 758.0, 144.0, 486.0, 272.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.005511614, 0.011214416, -0.034385964, 0.0041191033, 0.01220705, 0.004627546, -0.01132002, -0.0032604896, 0.00989139, 0.016801255, -0.025405882, -0.0125290435, 0.04091012, -0.060020547, -0.0009395974, -0.038590353, 0.005696775, 0.012065375, -0.0027589966, -0.09700951, 0.0018026133, 0.025381224, -0.008491558, -0.010957085, 0.001498845, 0.06524899, -0.010861493, -0.004222015, -0.017359007, -0.004647112, 0.009347387, 0.0025562043, 0.010449492], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, -1, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.47121033, 1.4252436, 1.6465327, 1.1913289, 0.0, 0.0, 0.0, 0.7019548, 0.0, 0.58620274, 0.636016, 0.6773803, 1.5844624, 0.89781547, 0.97253954, 1.0344299, 0.0, 0.0, 2.1165204, 0.88531303, 0.0, 1.6390264, 0.0, 0.0, 0.0, 0.27880126, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, -1, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.01220705, 0.004627546, -0.01132002, 1.0, 0.00989139, 1.0, 1.0, 0.53846157, 0.0, 0.0, 1.3461539, 1.0, 0.005696775, 0.012065375, 1.0, 1.0, 0.0018026133, 1.0, -0.008491558, -0.010957085, 0.001498845, 1.0, -0.010861493, -0.004222015, -0.017359007, -0.004647112, 0.009347387, 0.0025562043, 0.010449492], "split_indices": [0, 102, 109, 125, 0, 0, 0, 106, 0, 111, 16, 1, 1, 1, 1, 61, 0, 0, 71, 93, 0, 39, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1812.0, 259.0, 1703.0, 109.0, 128.0, 131.0, 1580.0, 123.0, 829.0, 751.0, 374.0, 455.0, 311.0, 440.0, 272.0, 102.0, 161.0, 294.0, 211.0, 100.0, 335.0, 105.0, 117.0, 155.0, 179.0, 115.0, 123.0, 88.0, 163.0, 172.0, 89.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.001428537, 0.0044818306, -0.006029158, -0.0010122459, 0.0061180717, 0.003981364, -0.0089960955, 0.019837813, -0.013269056, -0.03614953, 0.040714458, -0.009865206, 0.0018201778, 0.0038349114, -0.01226212, -0.0022917676, 0.13319872, -0.019317998, 0.0068254466, 0.031361505, -0.013805217, 0.026685018, -0.0011252796, -0.06705672, 0.040203035, 0.013734332, -0.0046323054, -0.017967263, -0.016323794, -0.0025587743, 0.008979163, -0.009046014, 0.0052586235, 0.006439445, -0.010030342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, 17, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3917866, 0.61709756, 0.0, 0.80218065, 0.0, 0.46773705, 0.0, 1.0414257, 1.0551704, 1.5589697, 2.5813327, 0.0, 0.9773928, 0.0, 0.0, 2.0239708, 3.977069, 1.50029, 0.0, 1.3542151, 0.0, 0.0, 0.0, 1.6740066, 0.7666812, 0.0, 1.3014003, 0.0, 1.3692952, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, 18, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.3076923, -0.006029158, 1.0384616, 0.0061180717, 1.0, -0.0089960955, 1.0, -1.0, 1.0, 1.0, -0.009865206, 1.0, 0.0038349114, -0.01226212, 1.0, 1.0, 0.07692308, 0.0068254466, -0.34615386, -0.013805217, 0.026685018, -0.0011252796, 1.0, 1.0, 0.013734332, 0.0, -0.017967263, 1.0, -0.0025587743, 0.008979163, -0.009046014, 0.0052586235, 0.006439445, -0.010030342], "split_indices": [1, 1, 0, 1, 0, 126, 0, 89, 0, 69, 61, 0, 105, 0, 0, 113, 105, 1, 0, 1, 0, 0, 0, 53, 69, 0, 1, 0, 62, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2079.0, 1981.0, 98.0, 1806.0, 175.0, 1710.0, 96.0, 891.0, 819.0, 242.0, 649.0, 123.0, 696.0, 130.0, 112.0, 443.0, 206.0, 528.0, 168.0, 355.0, 88.0, 107.0, 99.0, 293.0, 235.0, 90.0, 265.0, 91.0, 202.0, 101.0, 134.0, 106.0, 159.0, 103.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004044204, 0.007003579, -0.006008639, 0.025006138, -0.0095279515, 0.0015766027, 0.070693724, 0.0011553235, -0.008464475, 0.043544255, -0.052734464, -0.0046879235, 0.1490757, -0.030994015, 0.030831642, 0.010664741, 0.016353454, 0.002345165, -0.015068807, 0.0073710666, 0.021557426, 3.5179353e-05, -0.01301067, 0.100592755, -0.025462061, -0.0028955943, 0.0062405653, 0.06690019, -0.007878582, 0.01784241, 0.003697407, -0.005714135, 0.00064627745, 0.009485163, 0.003956987], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.39152935, 0.5868867, 0.0, 1.010494, 0.8249635, 1.4222882, 2.9489932, 0.85866666, 0.0, 0.60397, 2.029856, 0.0, 0.96223927, 1.3285675, 1.8378882, 0.0, 0.5133031, 0.0, 0.0, 0.0, 0.0, 1.7339507, 0.0, 1.0348685, 0.2619413, 0.0, 0.0, 0.13597763, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 16, 16, 21, 21, 23, 23, 24, 24, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.006008639, 1.0, 1.0, 1.0, 1.0, 1.0, -0.008464475, 0.0, 1.0, -0.0046879235, 1.0, 1.0, 1.0, 0.010664741, 1.0, 0.002345165, -0.015068807, 0.0073710666, 0.021557426, 1.0, -0.01301067, 1.0, 1.0, -0.0028955943, 0.0062405653, 1.0, -0.007878582, 0.01784241, 0.003697407, -0.005714135, 0.00064627745, 0.009485163, 0.003956987], "split_indices": [117, 126, 0, 61, 64, 15, 17, 39, 0, 0, 124, 0, 50, 0, 124, 0, 124, 0, 0, 0, 0, 122, 0, 13, 53, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1972.0, 91.0, 944.0, 1028.0, 624.0, 320.0, 900.0, 128.0, 352.0, 272.0, 128.0, 192.0, 432.0, 468.0, 106.0, 246.0, 153.0, 119.0, 90.0, 102.0, 329.0, 103.0, 209.0, 259.0, 124.0, 122.0, 178.0, 151.0, 94.0, 115.0, 130.0, 129.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005605297, 0.0297456, -0.001971136, 0.07187407, -0.019404272, 0.0044725654, -0.009399723, 0.019554041, 0.000648725, 0.002357614, -0.006018059, -0.019337455, 0.036345445, -0.04134257, 0.04523829, 0.098181225, -0.0052621323, -0.013241382, -0.014205557, 0.011077157, -0.0015571847, 0.0017157613, 0.018114937, -0.08396012, 0.07096366, -0.03766138, 0.006309673, -0.01591598, -0.0006281312, 0.015519741, -0.0021600872, -0.012203475, 0.009291153, 0.014668136, -0.007077729], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, -1, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.37823188, 1.022881, 0.9333646, 2.1509151, 0.399589, 1.116333, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1964793, 1.6183147, 1.7773367, 0.85280913, 1.7007616, 2.255555, 0.9153106, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0806623, 1.4892384, 1.4736946, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.6291485, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 23, 23, 24, 24, 25, 25, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, -1, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [0.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.009399723, 0.019554041, 0.000648725, 0.002357614, -0.006018059, 1.0, 1.0, 2.0, 0.6923077, 1.0, 1.0, 1.0, -0.014205557, 0.011077157, -0.0015571847, 0.0017157613, 0.018114937, 1.0, 1.0, 0.1923077, 0.006309673, -0.01591598, -0.0006281312, 0.015519741, -0.0021600872, -0.012203475, 0.7692308, 0.014668136, -0.007077729], "split_indices": [0, 97, 102, 53, 108, 15, 0, 0, 0, 0, 0, 61, 53, 0, 1, 39, 5, 83, 0, 0, 0, 0, 0, 97, 69, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 494.0, 1574.0, 266.0, 228.0, 1471.0, 103.0, 92.0, 174.0, 111.0, 117.0, 842.0, 629.0, 628.0, 214.0, 253.0, 376.0, 491.0, 137.0, 103.0, 111.0, 128.0, 125.0, 185.0, 191.0, 372.0, 119.0, 94.0, 91.0, 100.0, 91.0, 133.0, 239.0, 88.0, 151.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044710655, -0.007667057, 0.0046796845, -0.0027716618, -0.008612218, -0.0067451885, 0.007575061, -0.024859922, 0.0086167175, -0.007979022, -0.01282554, -0.0104568545, 0.09152175, 0.010228294, -0.024842622, -0.042096578, 0.0101124635, 0.010551503, 0.007752844, -0.0062228055, -0.06392482, -0.08178089, 0.0059846938, -0.03564094, 0.0059967972, -0.0019970378, -0.0115870945, -0.13454875, 0.0018406736, 0.0010862285, -0.006682875, -0.023580717, 0.00013062496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.33786234, 0.74547744, 0.0, 0.5700427, 0.0, 0.48392344, 0.0, 1.3928356, 1.4879988, 1.2755575, 0.0, 2.700761, 0.034462452, 0.0, 0.43298334, 2.4111526, 0.0, 0.0, 0.0, 0.78472525, 0.43838602, 2.2679896, 0.0, 0.4046433, 0.0, 0.0, 0.0, 3.8655672, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [3.1923077, 1.3461539, 0.0046796845, 1.1923077, -0.008612218, 1.0, 0.007575061, 1.0, 1.0, -0.42307693, -0.01282554, 1.0, -0.03846154, 0.010228294, 1.0, 0.0, 0.0101124635, 0.010551503, 0.007752844, 1.0, 1.0, 1.0, 0.0059846938, 1.0, 0.0059967972, -0.0019970378, -0.0115870945, 1.0, 0.0018406736, 0.0010862285, -0.006682875, -0.023580717, 0.00013062496], "split_indices": [1, 1, 0, 1, 0, 71, 0, 42, 116, 1, 0, 64, 1, 0, 93, 1, 0, 0, 0, 2, 127, 111, 0, 59, 0, 0, 0, 50, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1941.0, 121.0, 1827.0, 114.0, 1739.0, 88.0, 798.0, 941.0, 686.0, 112.0, 765.0, 176.0, 91.0, 595.0, 596.0, 169.0, 88.0, 88.0, 403.0, 192.0, 429.0, 167.0, 279.0, 124.0, 104.0, 88.0, 281.0, 148.0, 112.0, 167.0, 161.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0052386303, -0.0019583814, -0.0071858093, -0.00670231, 0.004827143, 0.006969206, -0.03545964, -0.0021142713, 0.008583688, -0.08107319, 0.005383851, 0.013643111, -0.06263379, -0.1195946, 3.470344e-05, 0.0039349534, -0.00343632, -0.020898357, 0.051521927, -0.017264254, 0.002948734, -0.007243796, -0.016882403, 0.010645488, -0.009465531, 0.013573517, -0.0071512237, 0.006723415, -0.0040521487, 0.008762357, -0.00930409], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, 25, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [0.45169806, 0.46942458, 0.0, 0.7076814, 0.0, 0.8739994, 1.0805497, 1.0432706, 0.0, 0.85937977, 0.41311094, 1.1356826, 2.290314, 0.4317975, 0.0, 0.0, 0.0, 1.0562667, 2.045598, 0.0, 0.0, 0.0, 0.0, 0.9207598, 0.0, 0.0, 1.9862032, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, 26, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.3076923, -0.0071858093, 0.1923077, 0.004827143, 4.0, 1.0, 1.0, 0.008583688, 1.0, 0.7307692, 1.0, 1.0, 1.0, 3.470344e-05, 0.0039349534, -0.00343632, -0.115384616, 1.0, -0.017264254, 0.002948734, -0.007243796, -0.016882403, 1.0, -0.009465531, 0.013573517, 1.0, 0.006723415, -0.0040521487, 0.008762357, -0.00930409], "split_indices": [1, 1, 0, 1, 0, 0, 137, 42, 0, 124, 1, 124, 53, 115, 0, 0, 0, 1, 69, 0, 0, 0, 0, 122, 0, 0, 113, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1970.0, 97.0, 1800.0, 170.0, 1220.0, 580.0, 1094.0, 126.0, 274.0, 306.0, 868.0, 226.0, 186.0, 88.0, 165.0, 141.0, 454.0, 414.0, 103.0, 123.0, 95.0, 91.0, 318.0, 136.0, 170.0, 244.0, 151.0, 167.0, 116.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016864148, -0.0067568403, 0.022976242, -0.014729181, 0.017328212, 0.006612103, -0.0020908148, -0.023273448, 0.0060864715, -0.015873028, 0.012449264, 0.0151419435, -0.0326813, -0.056040104, 0.0048715607, 0.007886524, -0.004022417, -0.047741245, 0.0018022327, -0.019193856, 0.009559394, -0.03195091, -0.012092598, 0.0057017123, -0.007653681, 0.02985454, -0.07886595, 0.012057564, -0.007085419, -6.810542e-05, -0.01596745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.25885382, 0.32968843, 0.66836417, 0.83320343, 1.5192627, 0.0, 0.0, 0.41887015, 0.0, 0.8457537, 0.0, 0.80440956, 0.48348713, 4.1419735, 0.0, 0.0, 0.0, 0.74883556, 1.2241113, 0.0, 0.0, 1.5454897, 0.0, 0.0, 0.0, 2.1013737, 1.9143555, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.006612103, -0.0020908148, 1.0, 0.0060864715, 1.0, 0.012449264, 1.0, 1.0, 1.0, 0.0048715607, 0.007886524, -0.004022417, 1.0, 1.0, -0.019193856, 0.009559394, 1.0, -0.012092598, 0.0057017123, -0.007653681, 1.0, 1.0, 0.012057564, -0.007085419, -6.810542e-05, -0.01596745], "split_indices": [74, 0, 127, 31, 42, 0, 0, 5, 0, 122, 0, 111, 58, 50, 0, 0, 0, 64, 121, 0, 0, 108, 0, 0, 0, 12, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1717.0, 353.0, 1290.0, 427.0, 178.0, 175.0, 1159.0, 131.0, 326.0, 101.0, 228.0, 931.0, 201.0, 125.0, 106.0, 122.0, 648.0, 283.0, 106.0, 95.0, 533.0, 115.0, 166.0, 117.0, 230.0, 303.0, 121.0, 109.0, 154.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0053560273, -0.008574424, 0.0046643666, -0.004681922, -0.0068630143, -0.011063175, 0.052610617, -0.005089082, -0.007315252, 0.00060290814, 0.01034268, 0.004299288, -0.06372127, -0.0032049483, 0.009728996, 0.006718705, -0.020240635, -0.025055226, 0.022871837, -0.00914752, -0.008564485, 0.051179502, -0.01192886, -0.011259335, 0.013488854, 0.013181736, 0.02712559, -0.0039122915, 0.005851903, 0.0070203687, -0.005939876], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [0.34743017, 0.45701432, 0.0, 0.6712382, 0.0, 0.61277205, 0.4355452, 0.82954437, 0.0, 0.0, 0.0, 0.90647364, 3.7762473, 0.68488157, 0.0, 0.0, 0.0, 0.6303524, 2.205278, 1.2129691, 0.0, 0.8864224, 0.0, 0.0, 1.0068748, 0.0, 1.312011, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 21, 21, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [3.1923077, 1.3461539, 0.0046643666, 0.88461536, -0.0068630143, 1.0, 1.0, 3.0, -0.007315252, 0.00060290814, 0.01034268, 1.0, 1.0, 1.0, 0.009728996, 0.006718705, -0.020240635, 1.0, 1.0, -1.0, -0.008564485, -0.3846154, -0.01192886, -0.011259335, 1.0, 0.013181736, 1.0, -0.0039122915, 0.005851903, 0.0070203687, -0.005939876], "split_indices": [1, 1, 0, 1, 0, 88, 122, 0, 0, 0, 0, 102, 69, 124, 0, 0, 0, 74, 0, 0, 0, 1, 0, 0, 17, 0, 113, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1955.0, 121.0, 1836.0, 119.0, 1652.0, 184.0, 1507.0, 145.0, 96.0, 88.0, 1299.0, 208.0, 1202.0, 97.0, 107.0, 101.0, 654.0, 548.0, 518.0, 136.0, 457.0, 91.0, 93.0, 425.0, 105.0, 352.0, 196.0, 229.0, 235.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040429663, -0.007864916, 0.0061911005, 0.006548713, -0.032165583, -0.00037215478, 0.0067943535, -0.04745202, 0.0073177866, 0.0139420675, -0.056666218, -0.013433418, -0.022148248, 0.054302413, -0.016916944, -0.01446624, 0.0014875372, 0.053314343, -0.0654425, 0.10889217, -0.0076477397, -0.04439331, 0.009081214, 0.00075810857, 0.010060666, -0.020272423, -0.018042086, -0.0002763714, 0.020415824, -0.0039320737, 0.0023321377, 0.010093605, -0.014185584, 0.004984226, -0.00810385, -0.003901663, 0.009228866], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, -1, 17, 19, 21, -1, -1, 23, 25, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [0.5200259, 0.6830086, 0.0, 0.5200845, 1.1690965, 0.8863864, 0.0, 1.3938144, 0.0, 1.0922863, 1.4038715, 0.0, 1.6041446, 1.2851009, 1.4711215, 0.0, 0.0, 0.3871466, 1.6203966, 2.1486778, 0.17459755, 2.1029317, 0.0, 0.0, 0.0, 0.9543733, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0253013, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 25, 25, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, -1, 18, 20, 22, -1, -1, 24, 26, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [3.1923077, 0.1923077, 0.0061911005, 4.0, 1.0, 1.0, 0.0067943535, 0.42307693, 0.0073177866, 1.0, 1.0, -0.013433418, 1.0, 1.0, 1.0, -0.01446624, 0.0014875372, 0.6923077, 1.0, -0.3846154, 1.0, 1.0, 0.009081214, 0.00075810857, 0.010060666, 1.0, -0.018042086, -0.0002763714, 0.020415824, -0.0039320737, 0.0023321377, 1.0, -0.014185584, 0.004984226, -0.00810385, -0.003901663, 0.009228866], "split_indices": [1, 1, 0, 0, 42, 42, 0, 1, 0, 69, 53, 0, 124, 122, 116, 0, 0, 1, 2, 1, 111, 105, 0, 0, 0, 80, 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1950.0, 113.0, 1224.0, 726.0, 1100.0, 124.0, 634.0, 92.0, 877.0, 223.0, 143.0, 491.0, 380.0, 497.0, 100.0, 123.0, 179.0, 312.0, 202.0, 178.0, 396.0, 101.0, 91.0, 88.0, 224.0, 88.0, 93.0, 109.0, 88.0, 90.0, 254.0, 142.0, 104.0, 120.0, 159.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.004607442, -0.009472567, 0.027941681, -0.0058817673, -0.006186698, 0.009167771, -0.0044972314, -0.036362242, 0.0004336095, 0.0008923222, -0.009798019, -0.011640991, 0.01279653, 0.0033280582, 0.0107730385, -0.035430763, 0.023716547, -0.0940094, 0.03211071, -0.021047167, 0.066722535, -0.014678675, -0.0025743013, -0.0027811097, 0.008884522, -0.009980311, 0.03653381, 0.013338233, 0.011544789, -0.0040629916, 0.014113356, -0.005455392, 0.009725524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, 13, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.3263707, 0.3373309, 1.2454629, 0.32300788, 0.0, 0.0, 0.0, 0.80363476, 2.0078921, 0.0, 0.0, 0.0, 1.12989, 0.90323716, 0.0, 1.5588561, 1.4419059, 0.7602155, 0.62213326, 1.6642879, 1.4050477, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7111174, 0.0, 1.1840583, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, 14, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.3076923, 0.96153843, 1.0, -1.0, -0.006186698, 0.009167771, -0.0044972314, 1.0, 1.0, 0.0008923222, -0.009798019, -0.011640991, 1.0, 1.0, 0.0107730385, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.014678675, -0.0025743013, -0.0027811097, 0.008884522, -0.009980311, 0.1923077, 0.013338233, -0.1923077, -0.0040629916, 0.014113356, -0.005455392, 0.009725524], "split_indices": [1, 1, 115, 0, 0, 0, 0, 126, 104, 0, 0, 0, 90, 17, 0, 122, 15, 97, 13, 69, 53, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1793.0, 268.0, 1678.0, 115.0, 143.0, 125.0, 288.0, 1390.0, 166.0, 122.0, 133.0, 1257.0, 1143.0, 114.0, 394.0, 749.0, 211.0, 183.0, 367.0, 382.0, 119.0, 92.0, 89.0, 94.0, 155.0, 212.0, 173.0, 209.0, 122.0, 90.0, 118.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025732534, 0.0196286, -0.009620114, 0.056206428, -0.022309758, -0.011215611, -0.0004345961, 0.016196085, -0.00025460226, -0.006544281, 0.0015257712, -0.009581142, 0.042893056, 0.008299067, -0.018081771, 0.0109955445, -0.00444726, 0.017415596, -0.04574884, -0.051059797, 0.07804204, 0.0054435395, -0.067216896, -0.014645996, 0.00106697, 0.00092774146, 0.012925982, -0.10241469, 0.0018263434, -0.0024310814, -0.15163082, -0.023185244, -0.0036646463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, -1, -1, 13, 15, -1, 17, -1, -1, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [0.32338908, 0.7639391, 1.4777565, 1.6527467, 0.37593284, 0.0, 0.5706696, 0.0, 0.0, 0.0, 0.0, 0.93564606, 1.4705966, 0.0, 1.0695158, 0.0, 0.0, 1.9802272, 1.3162657, 1.3191372, 0.8910583, 0.0, 1.5163944, 0.0, 0.0, 0.0, 0.0, 1.3722966, 0.0, 0.0, 2.0201063, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 11, 11, 12, 12, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, -1, -1, 14, 16, -1, 18, -1, -1, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [0.0, 1.0, 1.0, 1.0, 1.0, -0.011215611, 1.0, 0.016196085, -0.00025460226, -0.006544281, 0.0015257712, -0.5, 1.0, 0.008299067, 1.0, 0.0109955445, -0.00444726, 0.03846154, 1.0, 1.0, 1.0, 0.0054435395, 1.0, -0.014645996, 0.00106697, 0.00092774146, 0.012925982, 1.0, 0.0018263434, -0.0024310814, 1.0, -0.023185244, -0.0036646463], "split_indices": [0, 97, 104, 53, 13, 0, 42, 0, 0, 0, 0, 1, 12, 0, 124, 0, 0, 1, 89, 5, 39, 0, 80, 0, 0, 0, 0, 81, 0, 0, 53, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 498.0, 1569.0, 266.0, 232.0, 129.0, 1440.0, 95.0, 171.0, 108.0, 124.0, 1189.0, 251.0, 100.0, 1089.0, 142.0, 109.0, 477.0, 612.0, 224.0, 253.0, 108.0, 504.0, 88.0, 136.0, 108.0, 145.0, 357.0, 147.0, 138.0, 219.0, 129.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0017131561, -0.0010594375, 0.004928505, 0.0043319566, -0.008817304, -0.0008042525, 0.051756304, 0.0032467293, -0.007240197, 0.008258363, 0.002226926, -0.03321861, 0.01093842, 0.0048955726, -0.008271545, -0.010281759, 0.024022806, -0.0067288694, 0.039319593, 0.032361306, -0.0072693564, -0.012770919, 0.057587452, -0.0013455445, 0.010756057, -0.008919059, 0.0065177116, 0.027609052, 0.01691464, -0.0025584537, 0.007941499], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.27302805, 0.9186626, 0.0, 0.44867703, 0.0, 0.48204845, 0.16362086, 0.44119522, 0.0, 0.0, 0.0, 1.1144595, 1.9334671, 0.0, 0.0, 0.0, 0.5480183, 0.9979073, 0.7403312, 0.83722913, 0.0, 1.2032663, 1.92635, 0.0, 0.0, 0.0, 0.0, 1.2511082, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [3.1538463, 1.3461539, 0.004928505, 0.88461536, -0.008817304, 0.6923077, 1.0, -1.0, -0.007240197, 0.008258363, 0.002226926, -0.30769232, 1.0, 0.0048955726, -0.008271545, -0.010281759, 1.0, 1.0, 1.0, 1.0, -0.0072693564, 1.0, 1.0, -0.0013455445, 0.010756057, -0.008919059, 0.0065177116, 1.0, 0.01691464, -0.0025584537, 0.007941499], "split_indices": [1, 1, 0, 1, 0, 1, 59, 0, 0, 0, 0, 1, 104, 0, 0, 0, 17, 12, 127, 137, 0, 12, 42, 0, 0, 0, 0, 111, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1956.0, 114.0, 1842.0, 114.0, 1662.0, 180.0, 1573.0, 89.0, 88.0, 92.0, 274.0, 1299.0, 103.0, 171.0, 134.0, 1165.0, 387.0, 778.0, 243.0, 144.0, 202.0, 576.0, 151.0, 92.0, 102.0, 100.0, 454.0, 122.0, 224.0, 230.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0047071227, 0.0009214806, 0.0045215613, -0.0034499962, 0.008103551, -0.009665619, 0.0056782328, -0.018297197, 0.03905875, -0.008931542, -0.06889774, 0.009517446, -0.0055906274, 0.043656047, -0.02803755, -0.013575077, -0.00044756927, 0.013510024, 0.009229499, -0.0110670915, -0.016726827, -0.0030817245, 0.006069715, -0.05456914, 0.02619013, 0.0059377095, -0.013147141, -0.0045970376, 0.077091746, -0.0042752814, 0.005716423, -9.4999144e-05, 0.015448853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.317589, 0.6633105, 0.0, 0.6723888, 0.0, 0.684685, 0.0, 0.6554135, 1.3056126, 1.1725303, 0.93027043, 0.0, 0.0, 0.4560098, 2.0225642, 0.0, 0.0, 0.40160197, 0.0, 1.2366443, 0.0, 0.0, 0.0, 1.6378968, 1.5096385, 0.4913665, 0.0, 0.0, 1.4556837, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 19, 19, 23, 23, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0045215613, 4.0, 0.008103551, 1.3076923, 0.0056782328, 0.6923077, 3.6538463, 1.0, 1.0, 0.009517446, -0.0055906274, 1.0, 2.0, -0.013575077, -0.00044756927, 0.0, 0.009229499, 1.0, -0.016726827, -0.0030817245, 0.006069715, 1.0, 1.0, -0.1923077, -0.013147141, -0.0045970376, -0.1923077, -0.0042752814, 0.005716423, -9.4999144e-05, 0.015448853], "split_indices": [90, 102, 0, 0, 0, 1, 0, 1, 1, 81, 23, 0, 0, 124, 0, 0, 0, 1, 0, 53, 0, 0, 0, 121, 71, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1894.0, 177.0, 1796.0, 98.0, 1628.0, 168.0, 1383.0, 245.0, 1167.0, 216.0, 154.0, 91.0, 311.0, 856.0, 106.0, 110.0, 192.0, 119.0, 763.0, 93.0, 99.0, 93.0, 352.0, 411.0, 197.0, 155.0, 170.0, 241.0, 101.0, 96.0, 120.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072912447, 0.02473608, -0.008800686, 0.048600823, -0.0051069544, -0.012952614, 0.0016571636, 0.119749956, -0.028837724, 0.022471601, -0.024902578, 0.020633716, 0.0043883044, -0.0009936831, -0.004753088, 0.007744083, 0.012932122, -0.012789051, -0.0058449926, 0.0573288, -0.036935188, -0.057076465, 0.044437736, -0.0053826007, 0.10356548, -0.082307056, 0.0084056435, -0.00013431272, -0.016729068, 0.013754266, -0.0044619124, 0.0027301067, 0.020016704, -0.016385665, 0.006019874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, 19, -1, -1, 21, 23, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4244483, 0.89911366, 1.9796453, 2.0826616, 0.0, 0.0, 0.79772806, 1.2941124, 0.06395027, 1.2730658, 1.2443526, 0.0, 0.0, 0.0, 0.0, 1.575156, 0.0, 0.0, 1.3781911, 1.7319875, 2.0531163, 1.62779, 2.238741, 0.0, 1.7534072, 3.1609907, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, 20, -1, -1, 22, 24, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.0, 1.0, 1.0, 1.0, -0.0051069544, -0.012952614, 1.0, 1.0, 1.0, 4.0, 1.0, 0.020633716, 0.0043883044, -0.0009936831, -0.004753088, 1.0, 0.012932122, -0.012789051, 1.0, -0.46153846, 1.0, 1.0, 1.0, -0.0053826007, 1.0, 1.0, 0.0084056435, -0.00013431272, -0.016729068, 0.013754266, -0.0044619124, 0.0027301067, 0.020016704, -0.016385665, 0.006019874], "split_indices": [0, 105, 104, 97, 0, 0, 12, 115, 106, 0, 89, 0, 0, 0, 0, 126, 0, 0, 39, 1, 7, 115, 108, 0, 15, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 497.0, 1568.0, 378.0, 119.0, 125.0, 1443.0, 197.0, 181.0, 809.0, 634.0, 92.0, 105.0, 90.0, 91.0, 711.0, 98.0, 99.0, 535.0, 337.0, 374.0, 265.0, 270.0, 99.0, 238.0, 272.0, 102.0, 176.0, 89.0, 132.0, 138.0, 133.0, 105.0, 173.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001992016, -0.0033172106, 0.038311966, 0.0010248341, -0.006543055, 0.009350426, -0.0024957224, 0.0049911067, -0.003994222, 0.019653013, -0.016392786, 0.031532075, -0.0042247376, 0.0030843886, -0.011884275, 0.017525839, 0.009051069, -0.025877366, 0.010107168, 0.03666981, -0.004449536, -0.056707334, 0.00450566, 0.06283398, -0.0068821856, -0.010029669, -0.0010906906, 0.033427395, 0.015326709, 0.010705846, -0.0014666875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.39915997, 0.48707646, 0.9218806, 0.2742771, 0.0, 0.0, 0.0, 0.48252058, 0.0, 0.6713458, 1.2491432, 0.632768, 0.0, 1.4927272, 0.0, 0.73495847, 0.0, 0.887878, 0.0, 1.3055282, 0.0, 0.564984, 0.0, 1.0078851, 0.0, 0.0, 0.0, 1.0127928, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.3076923, 0.96153843, 1.0, 1.0, -0.006543055, 0.009350426, -0.0024957224, -0.03846154, -0.003994222, 3.0, 1.0, -0.1923077, -0.0042247376, 1.0, -0.011884275, 1.0, 0.009051069, 1.0, 0.010107168, -0.26923078, -0.004449536, 0.30769232, 0.00450566, 1.0, -0.0068821856, -0.010029669, -0.0010906906, 1.0, 0.015326709, 0.010705846, -0.0014666875], "split_indices": [1, 1, 115, 88, 0, 0, 0, 1, 0, 0, 64, 1, 0, 0, 0, 42, 0, 116, 0, 1, 0, 1, 0, 58, 0, 0, 0, 53, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1806.0, 264.0, 1688.0, 118.0, 141.0, 123.0, 1539.0, 149.0, 913.0, 626.0, 766.0, 147.0, 526.0, 100.0, 619.0, 147.0, 406.0, 120.0, 473.0, 146.0, 283.0, 123.0, 379.0, 94.0, 145.0, 138.0, 286.0, 93.0, 113.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00427042, 0.009288094, -0.00042656812, -0.033916537, 0.006593522, 0.0046577426, -0.011457188, 0.0021699234, 0.00785005, 0.0077502253, -0.005761254, -0.005532976, 0.04586455, -0.055770114, 0.01263454, 0.014872054, -0.0056097014, -0.123441964, 0.0015526991, 0.0022862437, 0.01084397, -0.010475403, -0.014191988, 0.035089508, -0.020073788, -0.028106462, 0.010556261, 0.0074284584, -0.051526587, -0.011275374, 0.0052938773, -0.096613854, -0.006439301, -0.00021556181, -0.017654004, -0.00689156, 0.0056036967], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, -1, -1, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [0.8598747, 0.0, 0.46127144, 1.0578158, 0.5159379, 1.0432912, 0.0, 0.5142872, 0.0, 0.0, 0.0, 1.1855767, 2.4016051, 1.6645586, 0.9458146, 0.0, 0.0, 0.061120033, 0.0, 0.63152814, 0.0, 0.0, 0.0, 1.5543122, 1.5195314, 1.2622879, 0.0, 0.0, 0.7806188, 0.0, 0.0, 1.4495393, 0.74943095, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 23, 23, 24, 24, 25, 25, 28, 28, 31, 31, 32, 32], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, -1, -1, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009288094, -0.3846154, 1.0, 1.0, 1.0, -0.011457188, 1.0, 0.00785005, 0.0077502253, -0.005761254, -0.115384616, 0.30769232, 1.0, 3.0, 0.014872054, -0.0056097014, 1.0, 0.0015526991, 1.0, 0.01084397, -0.010475403, -0.014191988, 0.3846154, 0.0, 1.0, 0.010556261, 0.0074284584, 1.0, -0.011275374, 0.0052938773, 0.7307692, 1.0, -0.00021556181, -0.017654004, -0.00689156, 0.0056036967], "split_indices": [1, 0, 1, 0, 90, 13, 0, 64, 0, 0, 0, 1, 1, 106, 0, 0, 0, 39, 0, 124, 0, 0, 0, 1, 0, 15, 0, 0, 97, 0, 0, 1, 23, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 104.0, 1962.0, 340.0, 1622.0, 230.0, 110.0, 1528.0, 94.0, 106.0, 124.0, 1299.0, 229.0, 345.0, 954.0, 114.0, 115.0, 177.0, 168.0, 861.0, 93.0, 88.0, 89.0, 349.0, 512.0, 184.0, 165.0, 128.0, 384.0, 90.0, 94.0, 192.0, 192.0, 88.0, 104.0, 96.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00020376804, 0.012309528, -0.011522041, 0.0004455579, 0.0070421644, -0.04008143, 0.0062000765, -0.013948403, 0.009712956, 0.019055787, -0.11073486, -0.036229134, 0.041673686, 0.032229964, -0.058596008, -0.004077294, 0.008615345, -0.0052195946, -0.016805418, 0.0027283672, -0.009891712, 0.013062695, -0.018034657, -0.0059910454, 0.07849792, -0.004605381, -0.011823679, -0.007131455, 0.004088058, 0.015979065, -7.280133e-05, 0.009801871, -0.00969671], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2937383, 0.6790985, 0.55117875, 1.1383828, 0.0, 1.7423296, 1.011439, 1.4679686, 0.0, 0.9112616, 0.63752747, 1.2183361, 1.9439187, 1.4921021, 1.1656553, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6874406, 0.0, 1.5006349, 1.8009223, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.03846154, 1.0, 0.42307693, 1.0, 0.0070421644, 1.0, 1.0, 1.0, 0.009712956, 1.0, 0.1923077, 1.0, 0.96153843, 1.0, 1.0, -0.004077294, 0.008615345, -0.0052195946, -0.016805418, 0.0027283672, -0.009891712, 0.013062695, 1.0, -0.0059910454, -0.34615386, -0.3846154, -0.011823679, -0.007131455, 0.004088058, 0.015979065, -7.280133e-05, 0.009801871, -0.00969671], "split_indices": [1, 64, 1, 90, 0, 97, 97, 108, 0, 59, 1, 127, 1, 5, 53, 0, 0, 0, 0, 0, 0, 0, 50, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 985.0, 1089.0, 818.0, 167.0, 417.0, 672.0, 712.0, 106.0, 227.0, 190.0, 306.0, 366.0, 350.0, 362.0, 120.0, 107.0, 94.0, 96.0, 152.0, 154.0, 147.0, 219.0, 117.0, 233.0, 190.0, 172.0, 115.0, 104.0, 115.0, 118.0, 90.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034183322, 0.0007723795, -0.047630355, -0.008161373, 0.027858159, 0.0001761386, -0.009702213, 0.0043879175, -0.06875653, -0.004499075, 0.06463174, -0.0070797307, 0.04784428, -0.0008325786, -0.01563811, 0.012094944, -0.0005866654, 0.04427685, -0.024198595, 0.014464241, -0.0043618507, 0.0118466085, -0.00064280065, 0.012635417, -0.0674187, -0.004724082, 0.05980209, -0.00049982814, -0.09706126, 0.012406038, 0.0012398408, -0.0033230619, -0.018354146], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, -1, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.38519692, 0.4595157, 0.43911862, 1.085889, 1.2617689, 0.0, 0.0, 0.58953905, 1.2973275, 0.0, 1.2427067, 0.82289964, 2.186797, 0.0, 0.0, 0.0, 0.0, 0.88025093, 1.117563, 0.0, 0.0, 0.0, 0.0, 1.0703578, 0.59764695, 0.0, 0.6457684, 0.0, 1.2088983, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 17, 17, 18, 18, 23, 23, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, -1, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 0.65384614, 1.0, 0.23076923, 1.0, 0.0001761386, -0.009702213, 2.0, 1.0, -0.004499075, 1.0, 1.0, 1.0, -0.0008325786, -0.01563811, 0.012094944, -0.0005866654, 1.0, -0.26923078, 0.014464241, -0.0043618507, 0.0118466085, -0.00064280065, -0.5, 0.0, -0.004724082, 1.0, -0.00049982814, 1.0, 0.012406038, 0.0012398408, -0.0033230619, -0.018354146], "split_indices": [44, 1, 71, 1, 81, 0, 0, 0, 93, 0, 106, 81, 53, 0, 0, 0, 0, 106, 1, 0, 0, 0, 0, 1, 0, 0, 39, 0, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2079.0, 1899.0, 180.0, 1428.0, 471.0, 90.0, 90.0, 1183.0, 245.0, 158.0, 313.0, 936.0, 247.0, 145.0, 100.0, 174.0, 139.0, 234.0, 702.0, 120.0, 127.0, 95.0, 139.0, 379.0, 323.0, 167.0, 212.0, 104.0, 219.0, 90.0, 122.0, 126.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.009745618, -0.004905608, -0.043513987, -0.011358201, 0.009505089, 0.006249127, -0.014951927, -0.019136729, 0.023758445, -0.06389151, -0.00878743, 0.0674594, -0.003562145, -0.0017239817, -0.014590025, -0.030081255, 0.01836433, 0.008975257, 0.0045166183, -0.04665543, 0.006774237, -0.016410155, 0.011680289, -0.015030965, -0.09001822, -0.07836376, 0.008805173, 0.04970822, -0.013179274, 0.0008616356, -0.021725684, -0.017778318, 0.008026046, -0.003061594, 0.011824171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, -1, 23, -1, 25, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3389729, 1.1699909, 2.9216506, 0.4654575, 0.0, 0.0, 0.0, 0.6461367, 0.80184215, 1.0023714, 0.6550609, 0.08846319, 0.0, 0.0, 0.0, 1.0295546, 1.7047292, 0.0, 0.0, 0.74462914, 0.0, 2.3816192, 0.0, 2.373546, 2.8739803, 3.6429462, 0.0, 1.111989, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, -1, 24, -1, 26, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.009505089, 0.006249127, -0.014951927, -1.0, 1.0, 1.0, 1.0, 1.0, -0.003562145, -0.0017239817, -0.014590025, 1.0, 1.0, 0.008975257, 0.0045166183, 1.0, 0.006774237, 1.0, 0.011680289, 1.0, 1.0, 1.0, 0.008805173, 1.0, -0.013179274, 0.0008616356, -0.021725684, -0.017778318, 0.008026046, -0.003061594, 0.011824171], "split_indices": [0, 102, 109, 74, 0, 0, 0, 0, 12, 115, 111, 127, 0, 0, 0, 125, 121, 0, 0, 39, 0, 23, 0, 116, 12, 115, 0, 109, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1814.0, 260.0, 1704.0, 110.0, 130.0, 130.0, 1395.0, 309.0, 262.0, 1133.0, 178.0, 131.0, 167.0, 95.0, 635.0, 498.0, 89.0, 89.0, 543.0, 92.0, 368.0, 130.0, 314.0, 229.0, 231.0, 137.0, 202.0, 112.0, 129.0, 100.0, 142.0, 89.0, 93.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007649743, -0.0032821756, 0.0050863842, 0.012390969, -0.01789404, -0.03938323, 0.03058191, -0.03413273, 0.03481402, -0.010578281, 0.00550083, 0.08503116, -0.033844057, -0.055603124, -0.0033361206, -0.003383828, 0.008912108, 0.020162528, 0.027419932, -0.015823, 0.020574775, 0.0021325822, -0.08283462, -0.0088919625, 0.050808527, 0.01065128, -0.0060170493, -0.008796965, 0.013715955, -0.015308301, -0.03460436, -0.002467633, 0.012943863, -0.0098483665, 0.0016156848], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, -1, -1, -1, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.26836753, 0.4509284, 0.0, 0.8947302, 0.872172, 1.5480869, 2.4660864, 0.5150866, 0.89479333, 0.0, 0.0, 2.5592263, 2.1795983, 0.9615549, 1.4828446, 0.0, 0.0, 0.0, 1.7665836, 0.0, 2.834637, 0.0, 1.1485648, 0.0, 1.1633351, 0.0, 0.0, 0.0, 0.0, 0.0, 0.65176105, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 20, 20, 22, 22, 24, 24, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, -1, -1, -1, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0050863842, 1.0, 1.2307693, 1.0, 1.0, 1.0, 1.0, -0.010578281, 0.00550083, -0.34615386, -0.30769232, 1.0, -0.07692308, -0.003383828, 0.008912108, 0.020162528, 1.0, -0.015823, 1.0, 0.0021325822, 1.0, -0.0088919625, 1.0, 0.01065128, -0.0060170493, -0.008796965, 0.013715955, -0.015308301, -0.15384616, -0.002467633, 0.012943863, -0.0098483665, 0.0016156848], "split_indices": [114, 126, 0, 89, 1, 97, 97, 108, 39, 0, 0, 1, 1, 53, 1, 0, 0, 0, 39, 0, 17, 0, 15, 0, 93, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1969.0, 96.0, 950.0, 1019.0, 247.0, 703.0, 779.0, 240.0, 145.0, 102.0, 381.0, 322.0, 459.0, 320.0, 106.0, 134.0, 126.0, 255.0, 98.0, 224.0, 120.0, 339.0, 124.0, 196.0, 134.0, 121.0, 116.0, 108.0, 138.0, 201.0, 100.0, 96.0, 89.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029700438, -0.010522459, 0.020434957, 0.026196092, -0.020258665, -0.008378486, 0.049796388, 0.0078157075, -0.0062151975, -0.0027256429, -0.06434026, -0.0061117797, 0.081693284, -0.007208129, 0.0069460706, 0.024883505, -0.077780776, -0.016284382, -0.028970249, 0.016475547, 0.00078602176, 0.070156634, -0.02780896, -0.014645511, -0.0022633183, 0.0056165145, -0.009590431, 0.013859794, 0.01887821, -0.010035625, 0.017533077, -0.0050246045, 0.0058595547, 0.0065375986, -0.003465915], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, -1, 21, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.36590204, 0.5594866, 1.5453215, 0.5523924, 0.9560568, 0.0, 1.3939008, 0.0, 1.0068643, 1.8339044, 1.2263925, 0.0, 1.8766165, 0.0, 0.0, 1.5434527, 0.9013587, 0.0, 1.4759007, 0.0, 0.0, 2.3240266, 0.9835434, 0.0, 0.0, 0.0, 0.0, 0.6768063, 0.0, 0.0, 0.45945325, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 16, 16, 18, 18, 21, 21, 22, 22, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, -1, 22, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.26923078, 1.0, -0.008378486, 1.0, 0.0078157075, 0.07692308, 1.0, -0.46153846, -0.0061117797, 1.0, -0.007208129, 0.0069460706, 0.7692308, 1.0, -0.016284382, -0.23076923, 0.016475547, 0.00078602176, 1.0, 1.0, -0.014645511, -0.0022633183, 0.0056165145, -0.009590431, 1.0, 0.01887821, -0.010035625, 1.0, -0.0050246045, 0.0058595547, 0.0065375986, -0.003465915], "split_indices": [0, 5, 5, 1, 15, 0, 89, 0, 1, 121, 1, 0, 124, 0, 0, 1, 17, 0, 1, 0, 0, 71, 97, 0, 0, 0, 0, 97, 0, 0, 122, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1565.0, 505.0, 328.0, 1237.0, 111.0, 394.0, 126.0, 202.0, 885.0, 352.0, 88.0, 306.0, 108.0, 94.0, 647.0, 238.0, 93.0, 259.0, 144.0, 162.0, 348.0, 299.0, 106.0, 132.0, 114.0, 145.0, 236.0, 112.0, 115.0, 184.0, 97.0, 139.0, 96.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-3.397706e-05, 0.002653039, -0.0055313767, 0.008217278, -0.016477933, 0.002741715, 0.00979418, -0.066499524, 0.020793045, -0.0057739443, 0.0382389, -0.011752933, -0.002153263, 0.007888254, -0.008212635, -0.037074298, 0.029232275, -0.004315238, 0.011345569, 0.044824973, -0.0699836, -0.0023904524, 0.053475067, 0.011721974, -0.0027569758, -0.015095839, -0.03654884, 0.024790844, 0.013627486, 0.0020173232, -0.008088335, 0.010002993, -0.017662482, -0.0021304798, -0.0013979207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, -1, -1, 19, 21, -1, -1, 23, 25, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.30762172, 0.21023732, 0.0, 0.7516769, 0.8296375, 0.43589067, 0.0, 0.4359836, 1.5245266, 1.2743074, 1.7080357, 0.0, 0.0, 0.0, 0.0, 1.6548824, 0.7072134, 0.0, 0.0, 0.9224163, 1.1858284, 0.0, 0.8953924, 0.0, 0.0, 0.0, 0.7795714, 0.8943621, 0.0, 0.0, 0.0, 0.0, 0.0024012923, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 19, 19, 20, 20, 22, 22, 26, 26, 27, 27, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, -1, -1, 20, 22, -1, -1, 24, 26, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, -0.0055313767, 2.3076923, 1.0, 1.0, 0.00979418, 0.42307693, 1.0, 1.0, 1.0, -0.011752933, -0.002153263, 0.007888254, -0.008212635, 1.0, 1.0, -0.004315238, 0.011345569, 1.0, 1.0, -0.0023904524, 1.0, 0.011721974, -0.0027569758, -0.015095839, 1.0, -0.03846154, 0.013627486, 0.0020173232, -0.008088335, 0.010002993, 0.5769231, -0.0021304798, -0.0013979207], "split_indices": [117, 116, 0, 1, 53, 42, 0, 1, 111, 106, 93, 0, 0, 0, 0, 5, 5, 0, 0, 59, 39, 0, 62, 0, 0, 0, 93, 1, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1975.0, 96.0, 1530.0, 445.0, 1442.0, 88.0, 190.0, 255.0, 1163.0, 279.0, 89.0, 101.0, 163.0, 92.0, 614.0, 549.0, 134.0, 145.0, 176.0, 438.0, 172.0, 377.0, 88.0, 88.0, 128.0, 310.0, 280.0, 97.0, 136.0, 174.0, 101.0, 179.0, 90.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0027918078, 0.00487542, -0.0038965454, -0.0002358546, 0.0057127266, -0.003715639, 0.0065454706, -0.0074078687, 0.0061527244, -0.0267371, 0.018903723, -0.011360061, -0.012101015, 0.0035430358, 0.01197722, -0.04547285, 0.040023655, -0.030319275, 0.035295904, -0.01409019, -0.008790576, -0.004210916, 0.0822634, 0.0026283578, -0.0066753314, 0.008838057, 0.0036118457, -0.009551965, 0.06325419, 0.0029250819, 0.012440158, 0.005177531, -0.005043823, -0.0013034431, 0.013561045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, 21, 23, 25, -1, 27, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.17940617, 0.5245327, 0.0, 0.40894553, 0.0, 0.40927488, 0.0, 0.81780124, 0.0, 1.343816, 1.0551472, 1.397015, 0.0, 0.63545823, 0.0, 1.676765, 1.1032276, 0.5898095, 0.5129906, 0.0, 2.161938, 0.0, 0.46910882, 0.0, 0.0, 0.0, 0.49721867, 0.0, 1.0432727, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 22, 22, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, 22, 24, 26, -1, 28, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.3076923, -0.0038965454, 1.0, 0.0057127266, 1.0, 0.0065454706, 1.0, 0.0061527244, 3.0, 3.0, 1.0, -0.012101015, 1.0, 0.01197722, 1.0, 1.0, 1.0, 0.07692308, -0.01409019, 1.0, -0.004210916, 1.0, 0.0026283578, -0.0066753314, 0.008838057, 1.0, -0.009551965, 1.0, 0.0029250819, 0.012440158, 0.005177531, -0.005043823, -0.0013034431, 0.013561045], "split_indices": [1, 1, 0, 114, 0, 84, 0, 106, 0, 0, 0, 12, 0, 16, 0, 89, 2, 13, 1, 0, 108, 0, 69, 0, 0, 0, 93, 0, 16, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1964.0, 98.0, 1789.0, 175.0, 1699.0, 90.0, 1608.0, 91.0, 927.0, 681.0, 797.0, 130.0, 591.0, 90.0, 479.0, 318.0, 286.0, 305.0, 133.0, 346.0, 108.0, 210.0, 112.0, 174.0, 114.0, 191.0, 157.0, 189.0, 93.0, 117.0, 101.0, 90.0, 92.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0049281646, 0.0076866937, -0.0051679118, 0.014779871, -0.021515079, 0.0074448627, 0.010854038, 0.006222843, -0.052132912, -0.010980625, 0.02987227, -0.0061636646, 0.007967902, 0.0026550833, -0.012996144, 0.0079031335, -0.010667729, -0.05429484, 0.069162674, 0.031199114, -0.046148114, 0.007369461, -0.019636316, 0.022862773, 0.028410485, 0.08619974, -0.005338396, -0.007984666, -0.00046729489, 0.06853096, -0.0057063545, -0.0003567681, 0.018394653, 0.031816065, -0.0068324083, 0.013567844, 0.0012908771, -0.0018295695, 0.008192786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, -1, -1, 19, -1, 21, 23, 25, 27, -1, -1, -1, 29, 31, 33, -1, -1, 35, -1, -1, -1, 37, -1, -1, -1, -1, -1], "loss_changes": [0.32261157, 0.40805268, 0.0, 1.0900583, 0.32697105, 0.60745686, 0.0, 1.0069094, 1.1206629, 1.4583402, 2.1925144, 0.0, 0.0, 0.0, 0.0, 0.84868515, 0.0, 3.8366652, 2.9373443, 0.9465153, 0.2837232, 0.0, 0.0, 0.0, 1.2345333, 1.649602, 0.66227657, 0.0, 0.0, 0.91504765, 0.0, 0.0, 0.0, 0.44699207, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 24, 24, 25, 25, 26, 26, 29, 29, 33, 33], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, -1, -1, 20, -1, 22, 24, 26, 28, -1, -1, -1, 30, 32, 34, -1, -1, 36, -1, -1, -1, 38, -1, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.0, -0.0051679118, 1.3076923, 1.0, 1.0, 0.010854038, 1.0, 1.0, 1.0, -0.34615386, -0.0061636646, 0.007967902, 0.0026550833, -0.012996144, 0.1923077, -0.010667729, -0.5, 1.0, 1.0, 0.53846157, 0.007369461, -0.019636316, 0.022862773, 1.0, 1.0, -0.15384616, -0.007984666, -0.00046729489, 1.0, -0.0057063545, -0.0003567681, 0.018394653, -0.3846154, -0.0068324083, 0.013567844, 0.0012908771, -0.0018295695, 0.008192786], "split_indices": [1, 80, 0, 1, 13, 124, 0, 109, 109, 64, 1, 0, 0, 0, 0, 1, 0, 1, 89, 69, 1, 0, 0, 0, 113, 71, 1, 0, 0, 115, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1970.0, 96.0, 1585.0, 385.0, 1470.0, 115.0, 202.0, 183.0, 807.0, 663.0, 105.0, 97.0, 91.0, 92.0, 674.0, 133.0, 211.0, 452.0, 471.0, 203.0, 111.0, 100.0, 92.0, 360.0, 188.0, 283.0, 112.0, 91.0, 245.0, 115.0, 98.0, 90.0, 178.0, 105.0, 111.0, 134.0, 89.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021565172, -0.047493357, 0.00430939, 0.0025771877, -0.010590063, 0.008429383, -0.0072268704, 0.025564253, -0.0035905973, 0.039425008, -0.00462928, 0.0073328037, -0.008730531, 0.018307194, 0.011285739, 0.02093835, -0.0052366666, -0.03254681, 0.012101892, 0.048287112, -0.04555412, -0.01454763, 0.0135574145, 0.013988192, 0.019399505, -0.008419848, -0.001073935, 0.011055387, -0.006565632, 0.0811569, -0.03867087, -0.0015413606, 0.017274959, -0.012782417, 0.007128483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, 3, 5, -1, -1, 7, -1, 9, 11, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.6035832, 1.0997603, 0.568533, 0.0, 0.0, 0.35219303, 0.0, 0.70217514, 0.9190218, 0.91648185, 0.0, 0.7220847, 0.0, 2.3974967, 0.0, 1.3165843, 0.0, 1.5984043, 0.0, 1.3573745, 0.28387797, 0.0, 1.6749926, 0.0, 1.3986475, 0.0, 0.0, 0.0, 0.0, 1.6717341, 1.9703858, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22, 24, 24, 29, 29, 30, 30], "right_children": [2, 4, 6, -1, -1, 8, -1, 10, 12, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [-0.5, -0.5769231, 3.6538463, 0.0025771877, -0.010590063, 1.0, -0.0072268704, 0.1923077, 1.0, 1.0, -0.00462928, 1.0, -0.008730531, -0.15384616, 0.011285739, 1.0, -0.0052366666, 1.0, 0.012101892, 0.0, 1.0, -0.01454763, -0.34615386, 0.013988192, 1.0, -0.008419848, -0.001073935, 0.011055387, -0.006565632, 1.0, 1.0, -0.0015413606, 0.017274959, -0.012782417, 0.007128483], "split_indices": [1, 1, 1, 0, 0, 16, 0, 1, 62, 61, 0, 64, 0, 1, 0, 80, 0, 53, 0, 0, 97, 0, 1, 0, 124, 0, 0, 0, 0, 12, 3, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 257.0, 1802.0, 114.0, 143.0, 1710.0, 92.0, 705.0, 1005.0, 591.0, 114.0, 889.0, 116.0, 459.0, 132.0, 724.0, 165.0, 307.0, 152.0, 513.0, 211.0, 89.0, 218.0, 123.0, 390.0, 100.0, 111.0, 98.0, 120.0, 189.0, 201.0, 92.0, 97.0, 111.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002672603, 0.00010393328, 0.0043644966, -0.00722409, 0.028161358, 0.00017921494, -0.011520768, -0.00037390206, 0.012740068, -0.00829393, 0.011915633, 0.008887095, -0.03752562, -0.090714954, 0.007693847, -0.012388241, 0.007914788, -0.015388575, -0.0009777333, 0.018715875, -0.008764195, 0.0060770633, 0.01256142, -0.034160864, 0.029837955, -0.074895635, 0.006917678, 0.010600114, 0.008833301, -0.014511615, -0.00029048216, 0.0054341075, -0.003586543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, 15, 17, 19, -1, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.21764542, 0.4001083, 0.0, 1.2335291, 1.1412239, 1.455712, 0.0, 1.0377825, 0.0, 1.776299, 0.0, 0.0, 2.2266965, 1.1197233, 1.186346, 0.0, 0.0, 0.0, 0.0, 1.3672801, 0.0, 0.8652606, 0.0, 1.4143701, 0.91027534, 1.2183106, 0.0, 0.0, 0.9072263, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, 16, 18, 20, -1, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0043644966, 1.0, 1.0, 5.0, -0.011520768, 1.0, 0.012740068, -0.3846154, 0.011915633, 0.008887095, 1.0, 1.0, 1.4615384, -0.012388241, 0.007914788, -0.015388575, -0.0009777333, 1.2692307, -0.008764195, 1.0, 0.01256142, 1.0, -0.1923077, 1.0, 0.006917678, 0.010600114, 1.0, -0.014511615, -0.00029048216, 0.0054341075, -0.003586543], "split_indices": [102, 113, 0, 84, 42, 0, 0, 69, 0, 1, 0, 0, 126, 97, 1, 0, 0, 0, 0, 1, 0, 69, 0, 74, 1, 13, 0, 0, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1946.0, 122.0, 1543.0, 403.0, 1444.0, 99.0, 313.0, 90.0, 1348.0, 96.0, 92.0, 221.0, 219.0, 1129.0, 127.0, 94.0, 123.0, 96.0, 1012.0, 117.0, 905.0, 107.0, 336.0, 569.0, 241.0, 95.0, 123.0, 446.0, 122.0, 119.0, 221.0, 225.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [2.7306418e-05, -0.0061361184, 0.02218011, 0.009626321, -0.036745578, 0.05453324, -0.004679494, -0.025517827, 0.04386269, -0.06985484, 0.0043456974, -0.022724014, 0.017168565, -0.08270395, 0.03166828, -0.015846647, 0.09691455, -0.015293531, -0.002928898, 0.0068437564, -0.011290539, -0.0044271303, -0.014675832, -0.0074521676, 0.014112565, 0.0047617713, -0.0071377987, 0.13982902, 0.0016664466, -0.0071830708, 0.007850048, 0.0088801645, 0.01925203], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, -1, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.282905, 0.7820998, 1.006433, 1.2874327, 1.4631517, 2.7786188, 0.0, 1.7266929, 1.7168893, 2.1684928, 0.0, 1.5208994, 0.0, 0.649909, 3.0685444, 0.8986866, 0.9883957, 0.0, 1.2118964, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.50278616, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, -1, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.004679494, 1.0, 1.0, 1.0, 0.0043456974, -0.115384616, 0.017168565, 1.0, 1.0, 1.0, 1.0, -0.015293531, 1.0, 0.0068437564, -0.011290539, -0.0044271303, -0.014675832, -0.0074521676, 0.014112565, 0.0047617713, -0.0071377987, -0.115384616, 0.0016664466, -0.0071830708, 0.007850048, 0.0088801645, 0.01925203], "split_indices": [58, 50, 80, 106, 23, 108, 0, 97, 81, 93, 0, 1, 0, 105, 13, 15, 137, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1621.0, 451.0, 1070.0, 551.0, 307.0, 144.0, 528.0, 542.0, 390.0, 161.0, 185.0, 122.0, 264.0, 264.0, 255.0, 287.0, 174.0, 216.0, 92.0, 93.0, 165.0, 99.0, 134.0, 130.0, 119.0, 136.0, 187.0, 100.0, 117.0, 99.0, 95.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013299319, 0.0018038301, -0.0063973228, -0.0013640589, 0.0054045743, 0.0030396674, -0.007369322, -0.0020642206, 0.005245033, -0.018667666, 0.011139198, 0.04094532, -0.042061612, -0.007116502, 0.06966227, -0.00032836603, 0.00781473, -0.016408922, -0.013676077, -0.029026117, 0.007891093, 0.0017227866, 0.013899217, 0.014837645, -0.008550704, 0.03146263, -0.08996968, 0.00787472, -0.019464074, 0.013114417, -0.0037935898, 0.0024261288, -0.01668421, -0.0031653945, -0.00074111327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, -1, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4038082, 0.32420796, 0.0, 0.5883026, 0.0, 0.4390569, 0.0, 0.34593272, 0.0, 0.9748135, 0.9391059, 0.32414508, 1.2195022, 1.2628347, 0.75977147, 0.0, 0.0, 0.8528365, 0.0, 1.9685376, 0.0, 0.0, 0.0, 0.59628063, 0.0, 1.8539579, 2.3358023, 0.0, 0.026005395, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 23, 23, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, -1, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.1923077, -0.0063973228, 1.3461539, 0.0054045743, 0.88461536, -0.007369322, 1.0, 0.005245033, -0.30769232, 1.0, 1.0, 1.0, 1.0, -0.26923078, -0.00032836603, 0.00781473, 1.0, -0.013676077, 1.0, 0.007891093, 0.0017227866, 0.013899217, 1.0, -0.008550704, 1.0, 1.0, 0.00787472, 1.0, 0.013114417, -0.0037935898, 0.0024261288, -0.01668421, -0.0031653945, -0.00074111327], "split_indices": [117, 1, 0, 1, 0, 1, 0, 71, 0, 1, 42, 81, 0, 116, 1, 0, 0, 93, 0, 121, 0, 0, 0, 59, 0, 113, 69, 0, 15, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1959.0, 98.0, 1847.0, 112.0, 1741.0, 106.0, 1578.0, 163.0, 699.0, 879.0, 197.0, 502.0, 670.0, 209.0, 90.0, 107.0, 395.0, 107.0, 534.0, 136.0, 119.0, 90.0, 272.0, 123.0, 268.0, 266.0, 95.0, 177.0, 110.0, 158.0, 107.0, 159.0, 88.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0049783997, -0.0018095848, -0.005882067, -0.009845368, 0.02233069, -0.021452647, 0.032739293, -0.009102906, 0.05265811, -0.028453521, 0.005734441, 0.007812618, -0.0008773086, 0.013572721, 0.016110089, 0.0024302825, -0.04346167, -0.003820855, 0.0074515315, -0.0073673427, 0.048233457, -0.011531706, -0.02263401, 0.016192881, -0.004103148, -0.061177295, 0.044148896, 0.0003430736, -0.12013655, -0.0047666263, 0.013416379, -0.01626287, -0.0077177435], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 112, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, 17, -1, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.35300472, 0.3790486, 0.0, 0.7246321, 1.6776996, 0.63549876, 0.5916131, 0.0, 1.6318328, 0.49039233, 0.0, 0.0, 0.0, 0.893059, 0.0, 1.2060838, 1.0655643, 0.0, 0.0, 0.0, 2.1921864, 0.0, 1.420866, 0.0, 0.0, 1.333235, 1.6694763, 0.0, 0.33405185, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 20, 20, 22, 22, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, 18, -1, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.005882067, 1.0, 1.0, 1.0, 1.0, -0.009102906, 1.0, 0.0, 0.005734441, 0.007812618, -0.0008773086, 1.0, 0.016110089, 1.0, 1.0, -0.003820855, 0.0074515315, -0.0073673427, 1.0, -0.011531706, 1.0, 0.016192881, -0.004103148, 1.0, 1.0, 0.0003430736, 0.30769232, -0.0047666263, 0.013416379, -0.01626287, -0.0077177435], "split_indices": [43, 0, 0, 83, 5, 31, 39, 0, 61, 0, 0, 0, 0, 97, 0, 53, 89, 0, 0, 0, 93, 0, 15, 0, 0, 109, 109, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1954.0, 115.0, 1466.0, 488.0, 1152.0, 314.0, 103.0, 385.0, 1058.0, 94.0, 150.0, 164.0, 283.0, 102.0, 346.0, 712.0, 153.0, 130.0, 130.0, 216.0, 160.0, 552.0, 95.0, 121.0, 350.0, 202.0, 167.0, 183.0, 100.0, 102.0, 92.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039985175, 0.004265684, -0.0065429243, -0.003512375, -0.00556697, -0.007364458, 0.0056767496, 0.001811797, -0.042835705, -0.0025066396, 0.005768978, 0.00023165678, -0.08481513, -0.013247898, 0.03790284, -0.013082184, -0.0037291683, -0.029220127, 0.038695388, 0.00045216977, 0.009522396, -0.09088257, 0.007118927, 0.0079806065, -0.000755409, -0.01436866, -0.0010750125, 0.034162, -0.005422708, -0.0027101422, 0.008667353], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 113, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, 11, 13, -1, -1, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.24561131, 0.0, 0.2921046, 0.42911112, 0.0, 0.5653818, 0.0, 0.33300176, 0.6766818, 0.55601627, 0.0, 0.0, 0.40448296, 0.8396058, 0.5147163, 0.0, 0.0, 1.7343445, 0.4525209, 0.0, 0.0, 1.2143874, 0.80792534, 0.0, 0.0, 0.0, 0.0, 1.0873582, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 22, 22, 27, 27], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, 12, 14, -1, -1, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [-0.5769231, 0.004265684, 1.0, 3.1923077, -0.00556697, 1.0, 0.0056767496, 5.0, 1.0, 1.0, 0.005768978, 0.00023165678, -0.1923077, 1.0, 0.1923077, -0.013082184, -0.0037291683, 1.0, 1.0, 0.00045216977, 0.009522396, 0.03846154, 0.61538464, 0.0079806065, -0.000755409, -0.01436866, -0.0010750125, 1.0, -0.005422708, -0.0027101422, 0.008667353], "split_indices": [1, 0, 84, 1, 0, 113, 0, 0, 39, 58, 0, 0, 1, 74, 1, 0, 0, 69, 69, 0, 0, 1, 1, 0, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 107.0, 1962.0, 1848.0, 114.0, 1737.0, 111.0, 1380.0, 357.0, 1281.0, 99.0, 172.0, 185.0, 1012.0, 269.0, 94.0, 91.0, 774.0, 238.0, 170.0, 99.0, 287.0, 487.0, 126.0, 112.0, 173.0, 114.0, 338.0, 149.0, 156.0, 182.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0057102405, -0.0054037985, 0.015858738, 0.009324209, -0.008516459, 0.0048216186, 0.0120764915, 0.0141628785, -0.010777312, -0.015084004, 0.009756113, 0.034718428, -0.046813548, -0.04097852, 0.052195482, -0.012933386, 0.013804269, -0.013629474, -0.021166064, -0.07000186, 0.023840267, -0.0041443366, 0.016830768, -0.006604911, 0.004799344, 0.0106430845, -0.07243269, -0.014009344, -0.018115878, 0.009894417, -0.00512636, -0.0024625498, -0.014361225, -0.0047881044, 0.0014625777], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.23347694, 1.160621, 1.2528063, 2.2180429, 0.0, 1.8072711, 0.0, 0.0, 1.1869947, 1.4041888, 0.0, 1.5755486, 0.9271662, 1.0948925, 2.4354656, 0.70872176, 0.0, 0.0, 2.0540195, 1.4619812, 1.015307, 0.0, 0.0, 0.0, 0.0, 0.0, 0.76224804, 0.0, 0.22512364, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.42307693, -0.008516459, 1.0, 0.0120764915, 0.0141628785, 1.0, 0.46153846, 0.009756113, 0.42307693, -0.115384616, 1.0, 1.0, 1.0, 0.013804269, -0.013629474, 0.42307693, 1.0, -0.23076923, -0.0041443366, 0.016830768, -0.006604911, 0.004799344, 0.0106430845, 1.0, -0.014009344, -0.23076923, 0.009894417, -0.00512636, -0.0024625498, -0.014361225, -0.0047881044, 0.0014625777], "split_indices": [13, 64, 90, 1, 0, 64, 0, 0, 124, 1, 0, 1, 1, 50, 115, 71, 0, 0, 1, 122, 1, 0, 0, 0, 0, 0, 17, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 988.0, 1082.0, 834.0, 154.0, 979.0, 103.0, 110.0, 724.0, 806.0, 173.0, 320.0, 404.0, 582.0, 224.0, 219.0, 101.0, 90.0, 314.0, 402.0, 180.0, 124.0, 100.0, 117.0, 102.0, 90.0, 224.0, 171.0, 231.0, 90.0, 90.0, 134.0, 90.0, 121.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0022916, -0.029497871, 0.0066908034, 0.0040005804, -0.014832677, 0.02365143, -0.0045543215, -0.02040038, 0.056110665, -0.0123331025, 0.0058648246, 0.002793806, -0.007840653, -0.00141958, 0.08184376, -0.0017309374, -0.0073516406, 0.016215885, -0.0006172743, -0.018758431, 0.006546683, 0.0031234487, -0.01027721, 0.0066828183, -0.011463143, 0.0045230407, -0.049185324, -0.0096934335, 0.0040947613], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, 13, 15, -1, -1, -1, -1, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, 25, -1, 27, -1, -1], "loss_changes": [0.28990558, 2.0812798, 0.34730914, 0.0, 0.0, 1.0380988, 0.53834456, 0.8636097, 0.7562473, 0.6324584, 0.0, 0.0, 0.0, 0.0, 2.16313, 0.950838, 0.0, 0.0, 0.0, 1.2188443, 0.0, 0.4887778, 0.0, 0.0, 0.9153227, 0.0, 1.1060662, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 14, 14, 15, 15, 19, 19, 21, 21, 24, 24, 26, 26], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, 14, 16, -1, -1, -1, -1, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, 26, -1, 28, -1, -1], "split_conditions": [-0.5, 1.0, -0.03846154, 0.0040005804, -0.014832677, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0058648246, 0.002793806, -0.007840653, -0.00141958, 1.0, 1.0, -0.0073516406, 0.016215885, -0.0006172743, 1.0, 0.006546683, -1.0, -0.01027721, 0.0066828183, 0.6923077, 0.0045230407, 1.0, -0.0096934335, 0.0040947613], "split_indices": [1, 42, 1, 0, 0, 13, 88, 106, 15, 64, 0, 0, 0, 0, 122, 0, 0, 0, 0, 15, 0, 0, 0, 0, 1, 0, 80, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 252.0, 1821.0, 159.0, 93.0, 726.0, 1095.0, 308.0, 418.0, 975.0, 120.0, 168.0, 140.0, 112.0, 306.0, 831.0, 144.0, 160.0, 146.0, 663.0, 168.0, 526.0, 137.0, 98.0, 428.0, 171.0, 257.0, 168.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0046095788, 0.0072389655, -0.0042050052, -0.013457236, 0.012724869, -0.094262674, 0.010129604, 0.03578826, -0.012107848, -0.017836988, -0.00028417786, 0.015446819, 0.013336818, -0.048159577, 0.020877616, 0.039991826, -0.009770979, -0.09321216, 0.0075142197, -0.03667516, 0.08807612, 0.059976578, -0.003215649, -0.00421209, -0.01318758, 0.0009111722, -0.009841031, 0.011131189, 0.0064048204, 0.11528014, 0.005701901, 0.0022563373, 0.0184243, 0.006174572, -0.005192803], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, 15, -1, 17, 19, 21, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.2529789, 0.22162494, 0.0, 3.7925296, 0.8837173, 1.845397, 0.0, 1.5879326, 0.8835631, 0.0, 0.0, 1.8386582, 0.0, 1.9720476, 1.500575, 0.7843752, 0.0, 0.5135968, 0.0, 0.5907722, 0.099936485, 1.2786739, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3491364, 0.6944074, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, 16, -1, 18, 20, 22, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0042050052, 1.0, 1.0, 1.0, 0.010129604, 1.0, 1.0, -0.017836988, -0.00028417786, 2.0, 0.013336818, 1.0, 1.0, 1.0, -0.009770979, 1.0, 0.0075142197, 1.0, 1.0, 1.0, -0.003215649, -0.00421209, -0.01318758, 0.0009111722, -0.009841031, 0.011131189, 0.0064048204, 1.0, 1.0, 0.0022563373, 0.0184243, 0.006174572, -0.005192803], "split_indices": [43, 89, 0, 97, 97, 50, 0, 42, 111, 0, 0, 0, 0, 115, 81, 80, 0, 71, 0, 127, 12, 13, 0, 0, 0, 0, 0, 0, 0, 126, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1952.0, 110.0, 409.0, 1543.0, 240.0, 169.0, 800.0, 743.0, 125.0, 115.0, 662.0, 138.0, 355.0, 388.0, 544.0, 118.0, 260.0, 95.0, 209.0, 179.0, 426.0, 118.0, 112.0, 148.0, 120.0, 89.0, 91.0, 88.0, 211.0, 215.0, 90.0, 121.0, 109.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012817694, -0.0062719947, 0.01758314, -0.012205576, 0.008992773, -0.0038728942, 0.0550515, -0.0064483806, -0.009507425, 0.01265973, -0.00010920642, -0.018410614, 0.039373044, -0.0358961, 0.042201124, -0.0022903439, 0.07859692, -0.01641885, -0.020494457, -0.0017706743, 0.010859904, 0.0024032977, 0.013138157, -0.00081783044, -0.008936263, -0.06268189, 0.034169547, 0.00043320074, -0.012848836, 0.05928615, -0.0023513474, -0.00028851858, 0.018857433], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, 17, 19, -1, 21, -1, 23, -1, -1, -1, -1, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.19261071, 0.9235684, 0.90304637, 0.72708654, 0.0, 0.0, 1.0323274, 0.78108025, 0.0, 0.0, 0.0, 1.1976031, 0.7206042, 1.7328761, 1.0063729, 0.0, 0.5213047, 0.0, 1.0610378, 0.0, 0.0, 0.0, 0.0, 1.3181571, 0.0, 0.970189, 0.56358385, 0.0, 0.0, 2.1783035, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 18, 18, 23, 23, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, 18, 20, -1, 22, -1, 24, -1, -1, -1, -1, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.34615386, 1.0, 0.008992773, -0.0038728942, 1.0, 1.0, -0.009507425, 0.01265973, -0.00010920642, 1.0, 1.0, -0.46153846, 1.0, -0.0022903439, 1.0, -0.01641885, 1.0769231, -0.0017706743, 0.010859904, 0.0024032977, 0.013138157, 1.0, -0.008936263, 1.0, 1.0, 0.00043320074, -0.012848836, 1.0, -0.0023513474, -0.00028851858, 0.018857433], "split_indices": [113, 102, 1, 84, 0, 0, 39, 74, 0, 0, 0, 0, 97, 1, 122, 0, 124, 0, 1, 0, 0, 0, 0, 69, 0, 115, 80, 0, 0, 124, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2046.0, 1618.0, 428.0, 1524.0, 94.0, 171.0, 257.0, 1425.0, 99.0, 113.0, 144.0, 1130.0, 295.0, 877.0, 253.0, 114.0, 181.0, 94.0, 783.0, 133.0, 120.0, 89.0, 92.0, 609.0, 174.0, 220.0, 389.0, 109.0, 111.0, 271.0, 118.0, 183.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0043041697, 0.008143363, -0.007419929, 0.014230527, -0.03155147, 0.008525206, 0.0068466286, -0.009885578, 0.0019039672, 0.009733272, 0.00314293, -0.00613186, 0.0072164643, 0.03491203, -0.021761307, 0.09346363, -0.008784085, -0.07940057, -0.006406581, 0.016501179, 0.004543127, -0.01304293, -0.0037432984, 0.04422806, -0.024537358, -0.0003737491, 0.009370835, -0.06647741, 0.032237377, -0.012185377, 0.0038582373, -0.0052462, 0.010523035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, 11, 13, -1, 15, 17, 19, -1, 21, 23, -1, -1, -1, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.62056166, 0.4743177, 0.0, 0.52665377, 0.88870573, 0.7360989, 0.0, 0.0, 0.0, 0.0, 0.9295153, 0.82111144, 0.0, 2.5371442, 0.8204273, 0.8213532, 0.0, 0.4176023, 0.6720091, 0.0, 0.0, 0.0, 0.0, 0.45805663, 1.2834321, 0.0, 0.0, 1.8035268, 1.4157836, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 23, 23, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, 12, 14, -1, 16, 18, 20, -1, 22, 24, -1, -1, -1, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [3.6538463, 3.0, -0.007419929, 1.0, 1.0, 1.0, 0.0068466286, -0.009885578, 0.0019039672, 0.009733272, 1.0, 1.0, 0.0072164643, 0.5, -0.34615386, 1.0, -0.008784085, 1.0, -0.115384616, 0.016501179, 0.004543127, -0.01304293, -0.0037432984, 1.0, 1.0, -0.0003737491, 0.009370835, 1.0, 1.0, -0.012185377, 0.0038582373, -0.0052462, 0.010523035], "split_indices": [1, 0, 0, 125, 15, 104, 0, 0, 0, 0, 40, 81, 0, 1, 1, 106, 0, 93, 1, 0, 0, 0, 0, 59, 109, 0, 0, 116, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1963.0, 96.0, 1702.0, 261.0, 1540.0, 162.0, 112.0, 149.0, 88.0, 1452.0, 1280.0, 172.0, 353.0, 927.0, 239.0, 114.0, 195.0, 732.0, 96.0, 143.0, 88.0, 107.0, 193.0, 539.0, 98.0, 95.0, 310.0, 229.0, 203.0, 107.0, 106.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031577202, -0.009719931, 0.022870269, -0.0062755817, -0.0070131454, 0.059552103, -0.0062222066, -0.023096472, 0.001839337, 0.0035066535, 0.008403764, 0.0055737467, -0.009399844, -0.08565566, 0.02318018, -0.018609857, 0.0298879, -0.015780991, -0.0033127347, 0.013675074, -0.039646074, 0.03177519, -0.053696945, -0.006814799, 0.010110575, -0.0088335015, 0.0015682227, 0.014162942, -0.0078079007, -0.025970839, -0.0128585985, -0.0079241, 0.0083996495, -0.009898904, 0.008496062], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, -1, -1, 17, 19, 21, 23, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.3528751, 0.343329, 0.44394094, 0.21307684, 0.0, 0.11031544, 1.2617525, 1.4706753, 0.6039698, 0.0, 0.0, 0.0, 0.0, 0.81867003, 2.0834827, 1.0766296, 1.1605661, 0.0, 0.0, 0.0, 0.506449, 3.0169873, 0.74542046, 1.9270952, 0.0, 0.0, 0.0, 0.0, 0.0, 2.122204, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 13, 13, 14, 14, 15, 15, 16, 16, 20, 20, 21, 21, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, -1, -1, 18, 20, 22, 24, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [0.88461536, 0.6923077, 1.3461539, 1.0, -0.0070131454, 1.0, 1.0, 1.0, 1.0, 0.0035066535, 0.008403764, 0.0055737467, -0.009399844, 1.0, 1.0, 1.0, 1.0, -0.015780991, -0.0033127347, 0.013675074, 1.0, 1.0, -0.23076923, 1.0, 0.010110575, -0.0088335015, 0.0015682227, 0.014162942, -0.0078079007, -0.42307693, -0.0128585985, -0.0079241, 0.0083996495, -0.009898904, 0.008496062], "split_indices": [1, 1, 1, 127, 0, 97, 17, 122, 111, 0, 0, 0, 0, 97, 97, 15, 105, 0, 0, 0, 12, 39, 1, 69, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1650.0, 416.0, 1561.0, 89.0, 184.0, 232.0, 508.0, 1053.0, 92.0, 92.0, 136.0, 96.0, 216.0, 292.0, 609.0, 444.0, 91.0, 125.0, 104.0, 188.0, 250.0, 359.0, 293.0, 151.0, 100.0, 88.0, 125.0, 125.0, 262.0, 97.0, 163.0, 130.0, 158.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0009883536, 0.004479075, -0.0014960935, 0.00174715, -0.005442126, 0.008950765, -0.026566518, 0.0016082827, 0.009230952, -0.07708002, 0.027295928, -0.009775197, 0.05651396, -0.0047187107, -0.010989721, -0.0009821733, 0.0064005735, 0.0067042187, -0.019960502, -0.0048250267, 0.015429392, -0.002205852, -0.083042674, 0.07729051, -0.03061741, 0.0035935764, -0.018102495, -0.003323589, 0.016186727, 0.0055844067, -0.09671726, -0.003418381, 0.004735216, -0.015532135, -0.003512314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.22504969, 0.0, 0.33591768, 0.37610364, 0.0, 0.8997283, 1.0175723, 0.8443991, 0.0, 0.18933284, 0.24662755, 0.87551486, 2.3765717, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1065617, 0.0, 0.0, 1.7413926, 2.5297382, 1.8976372, 1.3591874, 0.0, 0.0, 0.0, 0.0, 0.6095978, 0.7255424, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 18, 18, 21, 21, 22, 22, 23, 23, 24, 24, 29, 29, 30, 30], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.004479075, 1.0, 1.0, -0.005442126, 1.4230769, 1.0, 1.0, 0.009230952, 1.0, 1.0, -0.46153846, 1.0, -0.0047187107, -0.010989721, -0.0009821733, 0.0064005735, 0.0067042187, 1.0, -0.0048250267, 0.015429392, 1.0, 1.0, 1.0, 1.0, 0.0035935764, -0.018102495, -0.003323589, 0.016186727, 1.0, 1.0, -0.003418381, 0.004735216, -0.015532135, -0.003512314], "split_indices": [1, 0, 43, 116, 0, 1, 71, 64, 0, 13, 93, 1, 93, 0, 0, 0, 0, 0, 113, 0, 0, 53, 39, 39, 71, 0, 0, 0, 0, 13, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 111.0, 1957.0, 1844.0, 113.0, 1470.0, 374.0, 1351.0, 119.0, 193.0, 181.0, 1119.0, 232.0, 101.0, 92.0, 90.0, 91.0, 131.0, 988.0, 112.0, 120.0, 771.0, 217.0, 203.0, 568.0, 98.0, 119.0, 88.0, 115.0, 367.0, 201.0, 188.0, 179.0, 103.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0033717016, -0.00392035, 0.0067510405, -0.005326732, 0.053525526, 0.00021000634, -0.00884944, 0.013130478, 0.018510504, -0.006973853, 0.010445408, -0.0046835104, 0.013572842, -0.059766434, 0.0030482656, -0.010938266, -4.3153264e-05, 0.012747426, -0.007947676, 0.00031130345, 0.011810246, 0.018012986, -0.04408118, -0.021342304, 0.04230026, 0.0031442982, -0.013240607, 0.003546613, -0.011259844, -0.001276817, 0.009284618], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 121, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, 15, 17, -1, -1, 19, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.29739174, 0.0, 1.0818442, 0.7008472, 1.070312, 1.0686445, 0.0, 0.0, 2.0757723, 0.70634, 0.0, 0.0, 0.0, 0.6270662, 0.89807487, 0.0, 0.0, 1.3154486, 0.0, 0.7056678, 0.0, 0.6136447, 1.7076905, 1.2701093, 1.1050436, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, 16, 18, -1, -1, 20, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.00392035, 1.0, 1.0, 1.0, 5.0, -0.00884944, 0.013130478, 1.0, -0.3846154, 0.010445408, -0.0046835104, 0.013572842, 1.0, 1.4615384, -0.010938266, -4.3153264e-05, 1.2692307, -0.007947676, 0.46153846, 0.011810246, 1.0, 1.0, 1.0, 1.0, 0.0031442982, -0.013240607, 0.003546613, -0.011259844, -0.001276817, 0.009284618], "split_indices": [104, 0, 113, 84, 69, 0, 0, 0, 111, 1, 0, 0, 0, 97, 1, 0, 0, 1, 0, 1, 0, 2, 127, 39, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 152.0, 1915.0, 1522.0, 393.0, 1427.0, 95.0, 122.0, 271.0, 1335.0, 92.0, 174.0, 97.0, 213.0, 1122.0, 116.0, 97.0, 1004.0, 118.0, 898.0, 106.0, 642.0, 256.0, 245.0, 397.0, 138.0, 118.0, 151.0, 94.0, 190.0, 207.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018272039, -0.0059662494, 0.04011956, -0.0077773393, -0.0012575828, 0.013203057, -0.0057918825, 0.020193622, -0.008467393, 0.06844368, -0.014138138, -0.030364087, 0.015076697, 0.013544648, 0.00049673056, -0.0045102495, 0.00274266, -0.08608858, 0.0009888692, 0.059190072, -0.020993205, -0.01883671, -0.00079185533, 0.01194742, -0.02914025, 0.019335594, 0.014366421, -0.06239292, 0.0045246314, -0.010546121, 0.04631341, 0.008392449, -0.0044594198, -0.0020546282, -0.009228336, 0.0033707586, 0.005891919], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, 25, 27, -1, -1, -1, 29, 31, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.359566, 0.63734823, 1.6760105, 0.0, 0.2735919, 0.0, 0.0, 0.7371468, 0.68257207, 0.78682214, 0.33462662, 1.1985298, 1.0151637, 0.0, 0.0, 0.0, 0.0, 1.974793, 1.5671684, 0.96623456, 0.96254647, 0.0, 0.0, 0.0, 2.0155435, 0.8051853, 0.0, 0.2701754, 0.0, 0.0, 0.027967244, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 18, 18, 19, 19, 20, 20, 24, 24, 25, 25, 27, 27, 30, 30], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, 26, 28, -1, -1, -1, 30, 32, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 1.0, -0.0077773393, 0.0, 0.013203057, -0.0057918825, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.013544648, 0.00049673056, -0.0045102495, 0.00274266, -0.07692308, -0.30769232, 1.0, 1.0, -0.01883671, -0.00079185533, 0.01194742, 1.0, 1.0, 0.014366421, 1.0, 0.0045246314, -0.010546121, 1.0, 0.008392449, -0.0044594198, -0.0020546282, -0.009228336, 0.0033707586, 0.005891919], "split_indices": [125, 1, 15, 0, 0, 0, 0, 69, 97, 97, 39, 69, 53, 0, 0, 0, 0, 1, 1, 23, 2, 0, 0, 0, 53, 13, 0, 126, 0, 0, 59, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1885.0, 186.0, 116.0, 1769.0, 96.0, 90.0, 445.0, 1324.0, 185.0, 260.0, 686.0, 638.0, 90.0, 95.0, 149.0, 111.0, 247.0, 439.0, 287.0, 351.0, 107.0, 140.0, 89.0, 350.0, 195.0, 92.0, 216.0, 135.0, 174.0, 176.0, 97.0, 98.0, 90.0, 126.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026915933, -0.014860367, 0.010307144, -0.0052996227, -0.011719182, -0.0004685682, 0.009809463, -0.008279105, 0.0072765476, 0.009262006, -0.021554947, 0.05797721, -0.044155367, -0.06486021, 0.021038441, 0.01876117, -0.011318302, -0.07573857, 0.0038510219, -0.12337459, 0.0060894354, 0.061624683, -0.00794608, 0.0034416914, -0.006701201, -0.048535883, -0.013382763, 3.4297933e-05, -0.024409903, 0.0016129572, 0.014064247, 0.0018124288, -0.0114548905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, 11, -1, 13, 15, 17, 19, 21, -1, 23, 25, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.32616448, 1.0419583, 0.943135, 0.9492073, 0.0, 1.7430568, 0.0, 0.0, 2.1851957, 0.0, 1.335431, 3.7908638, 1.0861115, 2.6416843, 1.4887934, 0.0, 0.70047015, 0.47563314, 0.0, 3.6592555, 0.0, 0.93467957, 0.0, 0.0, 0.0, 0.9020903, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, 12, -1, 14, 16, 18, 20, 22, -1, 24, 26, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.5, -0.011719182, -0.42307693, 0.009809463, -0.008279105, 1.0, 0.009262006, 1.0, -0.07692308, 1.0, 0.53846157, 0.6923077, 0.01876117, 1.0, 0.30769232, 0.0038510219, 1.0, 0.0060894354, 1.0, -0.00794608, 0.0034416914, -0.006701201, 1.0, -0.013382763, 3.4297933e-05, -0.024409903, 0.0016129572, 0.014064247, 0.0018124288, -0.0114548905], "split_indices": [39, 88, 88, 1, 0, 1, 0, 0, 122, 0, 106, 1, 80, 1, 1, 0, 13, 1, 0, 69, 0, 71, 0, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1065.0, 997.0, 974.0, 91.0, 888.0, 109.0, 136.0, 838.0, 164.0, 724.0, 422.0, 416.0, 359.0, 365.0, 147.0, 275.0, 301.0, 115.0, 245.0, 114.0, 260.0, 105.0, 151.0, 124.0, 205.0, 96.0, 121.0, 124.0, 165.0, 95.0, 102.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00057618495, -0.004589155, 0.018902998, 0.001371357, -0.044316545, -0.008981277, 0.008095535, 0.0053498214, -0.006045234, -0.0003012819, -0.00923637, 0.008791458, -0.058588263, -0.009294006, 0.005044349, -0.011312548, 0.0010768821, 0.06844997, -0.007985154, 0.014664714, -0.0005674404, -0.06159212, 0.008598291, -0.015397022, 0.0021793398, -0.01288705, 0.008535187, 0.032336354, -0.055052657, 0.008955615, -0.004194896, -0.015908755, -0.013048618, -0.008267085, 0.005610426], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, -1, -1, 15, -1, 17, -1, -1, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [0.19718562, 0.38479298, 0.7924708, 0.546271, 0.4207194, 1.5189207, 0.0, 0.0, 1.1920074, 0.0, 0.0, 0.0, 0.7905516, 0.0, 0.9062798, 0.0, 0.0, 1.0839117, 0.8089794, 0.0, 0.0, 1.6561446, 1.1461086, 0.0, 0.0, 1.0354317, 0.0, 1.1136549, 0.8297258, 0.0, 0.0, 0.8894321, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 12, 12, 14, 14, 17, 17, 18, 18, 21, 21, 22, 22, 25, 25, 27, 27, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, -1, -1, 16, -1, 18, -1, -1, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.46153846, 1.0, -0.34615386, 0.008095535, 0.0053498214, -0.34615386, -0.0003012819, -0.00923637, 0.008791458, 1.0, -0.009294006, 1.0, -0.011312548, 0.0010768821, 0.26923078, -0.115384616, 0.014664714, -0.0005674404, 1.0, 1.0, -0.015397022, 0.0021793398, 1.0, 0.008535187, 1.0, 1.0, 0.008955615, -0.004194896, 1.0, -0.013048618, -0.008267085, 0.005610426], "split_indices": [58, 64, 50, 1, 50, 1, 0, 0, 1, 0, 0, 0, 12, 0, 89, 0, 0, 1, 1, 0, 0, 106, 0, 0, 0, 122, 0, 23, 74, 0, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2083.0, 1625.0, 458.0, 1413.0, 212.0, 316.0, 142.0, 176.0, 1237.0, 114.0, 98.0, 107.0, 209.0, 140.0, 1097.0, 117.0, 92.0, 187.0, 910.0, 91.0, 96.0, 215.0, 695.0, 102.0, 113.0, 543.0, 152.0, 262.0, 281.0, 148.0, 114.0, 185.0, 96.0, 96.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0037766537, 0.008301209, -0.027737996, 0.0023800852, 0.010037737, -0.010374756, 0.008197389, -0.0033572472, 0.007444101, -0.011361347, 0.031092817, -0.063900284, 0.0011479221, 0.010265014, 0.00081854727, -0.001767828, -0.014350481, -0.027544001, 0.03435116, 0.005714044, -0.0062399874, -0.010704266, -0.010365956, 0.0019274666, 0.013189468, -0.051282078, 0.03323943, -0.05305189, 0.0066236667, -0.011454246, 0.00206047, 0.011524233, -0.006450136, 0.0037054797, -0.013016245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2941626, 0.9835302, 2.1598406, 0.70077676, 0.0, 0.0, 0.0, 0.43291447, 0.0, 0.83730316, 0.6412381, 0.90147233, 0.980292, 0.0, 0.74060094, 0.0, 0.0, 0.70753443, 1.5086176, 0.0, 0.0, 0.80597895, 0.0, 1.2657729, 0.0, 1.0686829, 1.7392614, 1.3409985, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 17, 17, 18, 18, 21, 21, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.010037737, -0.010374756, 0.008197389, 1.0, 0.007444101, -1.0, -0.07692308, 1.0, 1.0, 0.010265014, 1.0, -0.001767828, -0.014350481, 1.0, 1.0, 0.005714044, -0.0062399874, 1.0, -0.010365956, 1.0, 0.013189468, 0.53846157, 1.0, 1.0, 0.0066236667, -0.011454246, 0.00206047, 0.011524233, -0.006450136, 0.0037054797, -0.013016245], "split_indices": [0, 102, 122, 125, 0, 0, 0, 74, 0, 0, 1, 115, 111, 0, 12, 0, 0, 0, 121, 0, 0, 93, 0, 12, 0, 1, 12, 39, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1804.0, 259.0, 1695.0, 109.0, 153.0, 106.0, 1570.0, 125.0, 1274.0, 296.0, 245.0, 1029.0, 88.0, 208.0, 155.0, 90.0, 552.0, 477.0, 110.0, 98.0, 452.0, 100.0, 358.0, 119.0, 235.0, 217.0, 193.0, 165.0, 125.0, 110.0, 118.0, 99.0, 89.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0061949682, 0.005872886, 0.0032823551, -0.006020125, 0.008299721, 0.002196345, 0.042487465, 0.03808796, -0.010079084, 0.009978092, -0.001822647, -0.016728783, 0.089142784, -0.016654799, 0.005549015, 0.005212847, -0.0090076765, 0.016065016, 0.0034895781, -0.03182807, 0.04104537, 0.0028837563, -0.070337884, 0.011194805, -0.0023637738, -0.03982045, 0.0117968, -0.036484, -0.014923463, -0.08941038, 0.0037763768, 0.0025535652, -0.012277224, -0.015434756, -0.0024473171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 126, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, 19, -1, -1, -1, -1, -1, 21, 23, 25, 27, -1, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.31749746, 0.0, 0.6262112, 0.0, 0.38017967, 0.6811445, 0.9600694, 1.1026721, 0.49670142, 0.0, 0.0, 0.9596029, 0.7913281, 0.91664875, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1081626, 0.99979293, 2.142758, 1.049687, 0.0, 0.0, 1.2234721, 0.0, 1.4716811, 0.0, 0.8180661, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 19, 19, 20, 20, 21, 21, 22, 22, 25, 25, 27, 27, 29, 29], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, 20, -1, -1, -1, -1, -1, 22, 24, 26, 28, -1, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.005872886, -0.5, -0.006020125, 1.0, 0.0, 1.0, 1.0, 1.0, 0.009978092, -0.001822647, 1.0, 1.0, 1.0, 0.005549015, 0.005212847, -0.0090076765, 0.016065016, 0.0034895781, 1.0, 0.42307693, 0.42307693, 1.0, 0.011194805, -0.0023637738, 1.0, 0.0117968, 1.0, -0.014923463, 1.0, 0.0037763768, 0.0025535652, -0.012277224, -0.015434756, -0.0024473171], "split_indices": [1, 0, 1, 0, 42, 0, 126, 13, 88, 0, 0, 97, 127, 80, 0, 0, 0, 0, 0, 124, 1, 1, 1, 0, 0, 12, 0, 113, 0, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 109.0, 1966.0, 144.0, 1822.0, 1546.0, 276.0, 394.0, 1152.0, 142.0, 134.0, 190.0, 204.0, 1047.0, 105.0, 98.0, 92.0, 88.0, 116.0, 829.0, 218.0, 436.0, 393.0, 104.0, 114.0, 318.0, 118.0, 275.0, 118.0, 194.0, 124.0, 160.0, 115.0, 97.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012681361, 0.0033449607, -0.019435637, 0.031538237, -0.005038014, -0.0071887313, 0.0019660438, -0.012433152, 0.009199891, -0.015397134, 0.007783492, -0.029213352, 0.0051853107, 0.0043670735, -0.009501306, -0.0063824453, -0.009543187, -0.00046420042, -0.0056018424, -0.015097603, 0.0073884125, -0.0040902933, -0.011193848, 0.010920911, -0.006765041, 0.03564593, -0.056762077, -0.0064686215, 0.079910114, -0.012205659, 0.0010758357, 0.0043596653, 0.011842438], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, -1, 17, -1, -1, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.17423777, 0.39185765, 0.4725955, 1.0102453, 1.0971509, 0.0, 0.46507916, 1.0192721, 0.0, 0.81961024, 0.0, 0.121189, 0.0, 0.0, 0.0, 0.7142259, 0.0, 0.0, 0.0, 0.98174655, 0.0, 0.78905237, 0.0, 1.1195467, 0.0, 2.1761494, 0.78916025, 0.0, 0.47551847, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 19, 19, 21, 21, 23, 23, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, -1, 18, -1, -1, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 0.0, 0.0, 1.0, 1.0, -0.0071887313, 1.0, 1.0, 0.009199891, 1.0, 0.007783492, 0.3846154, 0.0051853107, 0.0043670735, -0.009501306, 1.0, -0.009543187, -0.00046420042, -0.0056018424, 1.3461539, 0.0073884125, 1.0, -0.011193848, 1.0, -0.006765041, 1.0, 1.0, -0.0064686215, 1.0, -0.012205659, 0.0010758357, 0.0043596653, 0.011842438], "split_indices": [80, 0, 0, 111, 125, 0, 59, 59, 0, 41, 0, 1, 0, 0, 0, 88, 0, 0, 0, 1, 0, 113, 0, 137, 0, 81, 39, 0, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2079.0, 1658.0, 421.0, 380.0, 1278.0, 122.0, 299.0, 220.0, 160.0, 1136.0, 142.0, 184.0, 115.0, 131.0, 89.0, 1021.0, 115.0, 96.0, 88.0, 921.0, 100.0, 827.0, 94.0, 669.0, 158.0, 490.0, 179.0, 150.0, 340.0, 91.0, 88.0, 175.0, 165.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0011730216, -0.0046156286, 0.0049316725, -0.0044506583, 0.040669404, 0.013580221, -0.030579416, 0.017374592, 0.012038939, 0.026935602, -0.006115097, -0.050814204, 0.0038652436, -0.004784733, 0.0068945433, 0.07687809, 0.01123692, -0.0047654584, -0.017982377, -0.0010785854, 0.016848203, -0.006543442, 0.006254939, -0.041890763, 0.008174348, 0.023882376, -0.04088547, -0.011628076, 0.0031899293, 0.009611741, -0.0048352624, 0.0009327011, -0.009417222], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, -1, 3, 5, 7, 9, 11, 13, -1, 15, -1, 17, -1, -1, -1, 19, 21, 23, -1, -1, -1, 25, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3675296, 0.0, 0.6417705, 0.71422476, 0.73911023, 0.8952629, 0.86715204, 1.0359733, 0.0, 0.5966475, 0.0, 2.8456082, 0.0, 0.0, 0.0, 1.4615268, 0.5282534, 1.1337198, 0.0, 0.0, 0.0, 0.44930032, 0.0, 1.3558432, 0.0, 1.1896808, 0.5404836, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23, 25, 25, 26, 26], "right_children": [2, -1, 4, 6, 8, 10, 12, 14, -1, 16, -1, 18, -1, -1, -1, 20, 22, 24, -1, -1, -1, 26, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.0046156286, 1.0, 1.0, 1.0, 1.3461539, 0.7692308, 1.0, 0.012038939, 1.0, -0.006115097, 0.23076923, 0.0038652436, -0.004784733, 0.0068945433, 1.0, 0.42307693, -0.115384616, -0.017982377, -0.0010785854, 0.016848203, 1.0, 0.006254939, 1.0, 0.008174348, -0.115384616, 1.0, -0.011628076, 0.0031899293, 0.009611741, -0.0048352624, 0.0009327011, -0.009417222], "split_indices": [104, 0, 113, 109, 42, 1, 1, 15, 0, 53, 0, 1, 0, 0, 0, 39, 1, 1, 0, 0, 0, 39, 0, 39, 0, 1, 69, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 152.0, 1914.0, 1516.0, 398.0, 897.0, 619.0, 308.0, 90.0, 761.0, 136.0, 479.0, 140.0, 136.0, 172.0, 182.0, 579.0, 353.0, 126.0, 93.0, 89.0, 430.0, 149.0, 247.0, 106.0, 228.0, 202.0, 123.0, 124.0, 114.0, 114.0, 104.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0021491242, -0.0026290743, 0.027162714, -0.07239879, 0.007587861, -0.0035613924, 0.074080005, -0.013571172, -0.0026754562, -9.878946e-05, 0.047464333, 0.01015843, 0.0044157733, 0.0074126506, -0.009415164, -0.0014241531, 0.01257394, 0.05022521, -0.0028172946, 0.010856017, -0.0040226704, 0.01522323, -0.0482527, 0.03655767, -0.02879105, -0.009736999, 0.00095789175, -0.027344016, 0.08268221, 0.0029894542, -0.00695574, -0.01329729, 0.00805086, 0.016938942, 0.003210298], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, 13, 15, -1, -1, 17, -1, -1, -1, 19, 21, -1, -1, 23, 25, 27, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.24740626, 1.2389034, 0.9778431, 0.64155066, 0.4646792, 0.0, 0.15636766, 0.0, 0.0, 0.89792657, 1.1833577, 0.0, 0.0, 0.515491, 0.0, 0.0, 0.0, 1.1977675, 0.7786948, 0.0, 0.0, 0.6385336, 0.76694393, 1.3499258, 0.5311124, 0.0, 0.0, 2.1873314, 1.1665647, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 13, 13, 17, 17, 18, 18, 21, 21, 22, 22, 23, 23, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, 14, 16, -1, -1, 18, -1, -1, -1, 20, 22, -1, -1, 24, 26, 28, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, 1.0, 1.0, -0.0035613924, 1.0, -0.013571172, -0.0026754562, 4.0, 1.0, 0.01015843, 0.0044157733, -0.26923078, -0.009415164, -0.0014241531, 0.01257394, 1.0, 1.0, 0.010856017, -0.0040226704, 1.0, 1.0, 0.42307693, 0.8076923, -0.009736999, 0.00095789175, 1.0, 0.96153843, 0.0029894542, -0.00695574, -0.01329729, 0.00805086, 0.016938942, 0.003210298], "split_indices": [62, 1, 15, 93, 42, 0, 7, 0, 0, 0, 53, 0, 0, 1, 0, 0, 0, 59, 15, 0, 0, 80, 124, 1, 1, 0, 0, 59, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1738.0, 332.0, 222.0, 1516.0, 142.0, 190.0, 93.0, 129.0, 1271.0, 245.0, 99.0, 91.0, 1177.0, 94.0, 137.0, 108.0, 227.0, 950.0, 138.0, 89.0, 680.0, 270.0, 458.0, 222.0, 146.0, 124.0, 192.0, 266.0, 91.0, 131.0, 97.0, 95.0, 98.0, 168.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005087479, -0.010523015, 0.030955529, 0.0069664135, -0.031643547, 0.0070172744, -0.0013511208, -0.0041067665, 0.04628647, -0.018907737, -0.01018433, 0.016083838, -0.007834419, 0.016470125, -0.005966358, -0.03189629, 0.002816485, 0.06709277, -0.03509455, -0.01155002, -0.009174696, 0.013601291, -0.00055781024, 0.01818606, -0.012373842, -0.04349079, 0.004064076, -0.0019876787, 0.0056248936, 0.0021105495, -0.010216988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, 17, -1, -1, -1, 19, -1, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [0.40514815, 0.6637867, 0.47258663, 0.42799646, 0.72775686, 0.0, 0.0, 1.1496553, 2.7099478, 0.4212578, 0.0, 1.5741649, 0.0, 0.0, 0.0, 0.65757823, 0.0, 1.5125631, 1.4216233, 0.6718066, 0.0, 0.0, 0.0, 0.27237105, 0.0, 0.9476131, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, 18, -1, -1, -1, 20, -1, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.3076923, -0.03846154, 1.0, 2.0, 1.0, 0.0070172744, -0.0013511208, 1.0, 1.0, 1.0, -0.01018433, 1.0, -0.007834419, 0.016470125, -0.005966358, 1.0, 0.002816485, 1.0, -0.26923078, 1.0, -0.009174696, 0.013601291, -0.00055781024, 1.0, -0.012373842, 0.5, 0.004064076, -0.0019876787, 0.0056248936, 0.0021105495, -0.010216988], "split_indices": [1, 1, 115, 0, 64, 0, 0, 42, 53, 0, 0, 93, 0, 0, 0, 15, 0, 122, 1, 116, 0, 0, 0, 124, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1797.0, 271.0, 983.0, 814.0, 144.0, 127.0, 767.0, 216.0, 689.0, 125.0, 603.0, 164.0, 102.0, 114.0, 540.0, 149.0, 302.0, 301.0, 403.0, 137.0, 155.0, 147.0, 188.0, 113.0, 250.0, 153.0, 94.0, 94.0, 119.0, 131.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008364959, 0.002943359, -0.0075904443, -0.0019073501, 0.030971529, -0.016455274, 0.012284086, 0.006426194, -0.00074592954, -0.059907563, 0.020282526, -0.033575628, 0.030853515, -0.010025638, -0.012795635, -0.007273238, 0.049400248, 0.0032739283, -0.013967951, 0.010540336, 0.012406615, 0.001581984, -0.0046947785, 0.014785481, 0.0045348615, -0.026633156, 0.054602806, -0.012184673, 0.008053462, -0.006627921, 0.0032447204, 0.009505819, 0.002019978], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5918943, 0.2700097, 0.0, 0.34952992, 0.37485796, 1.3345417, 0.7298115, 0.0, 0.0, 1.3000562, 1.226897, 1.7379588, 0.83888, 0.2108939, 0.0, 0.0, 1.5239344, 0.0, 0.0, 0.0, 0.8055443, 0.0, 0.0, 0.0, 2.276378, 0.5949451, 0.32706988, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 20, 20, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2692307, -0.0075904443, 1.0, 1.0, 1.0, -0.34615386, 0.006426194, -0.00074592954, 1.0, 1.0, -0.46153846, -0.23076923, 0.23076923, -0.012795635, -0.007273238, -0.34615386, 0.0032739283, -0.013967951, 0.010540336, 1.0, 0.001581984, -0.0046947785, 0.014785481, -0.15384616, 0.5769231, 0.115384616, -0.012184673, 0.008053462, -0.006627921, 0.0032447204, 0.009505819, 0.002019978], "split_indices": [52, 1, 0, 124, 115, 15, 1, 0, 0, 111, 89, 1, 1, 1, 0, 0, 1, 0, 0, 0, 39, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2086.0, 1986.0, 100.0, 1693.0, 293.0, 836.0, 857.0, 157.0, 136.0, 383.0, 453.0, 247.0, 610.0, 221.0, 162.0, 108.0, 345.0, 152.0, 95.0, 121.0, 489.0, 130.0, 91.0, 108.0, 237.0, 254.0, 235.0, 89.0, 148.0, 152.0, 102.0, 108.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003632447, -0.031865373, 0.0041481415, 0.0032508615, -0.050897688, -8.9241745e-05, 0.00746177, -0.011257228, -0.017582383, -0.00864877, 0.04707921, -0.010431498, 0.0060329926, -0.05378571, 0.008068613, 0.0131319035, -0.0032287203, 0.0007242507, -0.108843796, -7.4147174e-05, 0.0085424865, -0.013276155, -0.008594378, -0.012421461, 0.035132367, -0.008427634, 0.0043866932, 0.01123228, -0.004934015, -0.033883385, 0.045280136, 0.00044552074, -0.010362702, -0.0057269232, 0.01089546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, 21, 23, -1, -1, -1, 25, 27, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.45449477, 0.54765826, 0.48433995, 0.0, 0.7088738, 0.6177217, 0.0, 0.0, 1.5136884, 0.97717035, 1.5711662, 0.0, 0.0, 1.1760342, 0.5952495, 0.0, 0.0, 0.0, 0.100779295, 0.37167346, 0.0, 0.0, 0.0, 0.7645048, 1.4475446, 0.0, 0.8028426, 0.0, 0.0, 0.7085767, 1.6193845, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 18, 18, 19, 19, 23, 23, 24, 24, 26, 26, 29, 29, 30, 30], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, 22, 24, -1, -1, -1, 26, 28, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [-0.3846154, 1.0, 1.0, 0.0032508615, 1.0, 1.0, 0.00746177, -0.011257228, 1.0, -0.115384616, 1.0, -0.010431498, 0.0060329926, 1.0, 3.0, 0.0131319035, -0.0032287203, 0.0007242507, 1.0, 1.0, 0.0085424865, -0.013276155, -0.008594378, 1.0, 0.5, -0.008427634, 1.0, 0.01123228, -0.004934015, 1.0, 0.42307693, 0.00044552074, -0.010362702, -0.0057269232, 0.01089546], "split_indices": [1, 127, 90, 0, 89, 64, 0, 0, 69, 1, 59, 0, 0, 93, 0, 0, 0, 0, 126, 83, 0, 0, 0, 16, 1, 0, 53, 0, 0, 106, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 447.0, 1622.0, 102.0, 345.0, 1530.0, 92.0, 121.0, 224.0, 1295.0, 235.0, 106.0, 118.0, 350.0, 945.0, 114.0, 121.0, 166.0, 184.0, 855.0, 90.0, 90.0, 94.0, 633.0, 222.0, 120.0, 513.0, 116.0, 106.0, 265.0, 248.0, 171.0, 94.0, 95.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039641787, 0.002100519, -0.046089254, 0.0071495143, -0.009490823, 0.0024248953, -0.011481668, 0.0019730234, 0.009049099, 0.03600773, -0.0075156013, 0.011825927, 0.0101171285, -0.033778165, 0.018539185, 0.00971798, -0.009967236, -0.018843396, -0.012062707, 0.06530207, -0.033211723, -0.011787988, 0.002266417, 0.012746214, 0.0010868082, -0.010782756, 0.0021564048, 0.0065061576, -0.021749277, -0.008320627, 0.003011614], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 133, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, 19, -1, -1, 21, -1, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [0.5257694, 0.88114417, 1.252049, 0.73772174, 0.0, 0.0, 0.0, 0.5199376, 0.0, 0.5530958, 0.86149025, 2.4363036, 0.0, 0.8132615, 1.5294536, 0.0, 0.0, 1.1184936, 0.0, 1.1233617, 1.2261423, 0.0, 0.6650587, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0168103, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 22, 22, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, 20, -1, -1, 22, -1, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, -0.009490823, 0.0024248953, -0.011481668, 1.0, 0.009049099, 1.0, 1.0, -0.115384616, 0.0101171285, 1.0, 1.0, 0.00971798, -0.009967236, -0.42307693, -0.012062707, 0.15384616, 1.0, -0.011787988, -0.07692308, 0.012746214, 0.0010868082, -0.010782756, 0.0021564048, 0.0065061576, 0.65384614, -0.008320627, 0.003011614], "split_indices": [0, 52, 109, 102, 0, 0, 0, 5, 0, 121, 12, 1, 0, 0, 106, 0, 0, 1, 0, 1, 137, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1799.0, 259.0, 1710.0, 89.0, 128.0, 131.0, 1610.0, 100.0, 351.0, 1259.0, 256.0, 95.0, 627.0, 632.0, 145.0, 111.0, 535.0, 92.0, 332.0, 300.0, 94.0, 441.0, 155.0, 177.0, 127.0, 173.0, 122.0, 319.0, 146.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00026574644, -0.0024920553, 0.004470968, 0.0018899087, -0.008711877, -0.00993262, 0.03262849, 0.004975279, -0.101771675, 0.0038982546, 0.015185893, 0.015136843, -0.010192434, -0.01617748, -0.0046098647, 0.03171117, -0.0042755646, 0.053589106, 1.0583762e-05, 0.0106449155, -0.0044185524, -0.00066479493, 0.09058253, 0.03040659, -0.039347153, 0.014505786, 0.003842527, -0.004060296, 0.015382031, 0.009905282, -0.016816124, 0.033069853, -0.007634911, -0.010700701, 0.007849385, -0.004981595, 0.011747653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, 17, -1, -1, -1, 19, -1, 21, 23, -1, -1, -1, 25, 27, 29, -1, -1, 31, -1, 33, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [0.25383756, 0.7231211, 0.0, 0.6737581, 0.0, 1.8332618, 1.7641417, 1.2513796, 0.62468326, 0.538496, 0.0, 0.61188394, 0.0, 0.0, 0.0, 1.4748154, 0.0, 0.66182756, 0.9032201, 0.0, 0.0, 0.0, 0.5227959, 1.812071, 2.0873108, 0.0, 0.0, 0.89380354, 0.0, 1.9084853, 0.0, 1.5391455, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 17, 17, 18, 18, 22, 22, 23, 23, 24, 24, 27, 27, 29, 29, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, 18, -1, -1, -1, 20, -1, 22, 24, -1, -1, -1, 26, 28, 30, -1, -1, 32, -1, 34, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.004470968, 0.53846157, -0.008711877, 3.0, 1.0, 1.0, 1.0, 1.0, 0.015185893, 1.0, -0.010192434, -0.01617748, -0.0046098647, 1.0, -0.0042755646, 1.0, 1.0, 0.0106449155, -0.0044185524, -0.00066479493, 1.0, 0.115384616, 0.115384616, 0.014505786, 0.003842527, 1.0, 0.015382031, 1.0, -0.016816124, 1.0, -0.007634911, -0.010700701, 0.007849385, -0.004981595, 0.011747653], "split_indices": [84, 52, 0, 1, 0, 0, 0, 88, 108, 80, 0, 5, 0, 0, 0, 53, 0, 13, 97, 0, 0, 0, 69, 1, 1, 0, 0, 105, 0, 53, 0, 71, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1950.0, 121.0, 1854.0, 96.0, 1339.0, 515.0, 1152.0, 187.0, 415.0, 100.0, 1052.0, 100.0, 90.0, 97.0, 260.0, 155.0, 297.0, 755.0, 131.0, 129.0, 113.0, 184.0, 426.0, 329.0, 90.0, 94.0, 333.0, 93.0, 238.0, 91.0, 220.0, 113.0, 88.0, 150.0, 111.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00019919971, -0.0046996265, 0.022820672, 0.007821915, -0.023418883, 0.064522095, -0.004506534, -0.003826154, 0.012772108, -0.010343895, -0.0012074251, -0.0021359886, 0.01354681, 0.011831392, -0.03421736, -0.043096405, 0.03548153, -0.0051876553, 0.010225586, -0.003629037, -0.009743321, 0.0033487398, -0.010954414, 0.01686161, -0.029378882, 0.0207052, -0.011025293, -0.005879475, 0.004096322, -0.00731873, 0.0017691831, 0.08356693, -0.034333717, 0.00092345086, 0.016501626, -0.01079147, 0.001472025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, 15, -1, -1, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [0.21476117, 0.40643913, 0.9596898, 1.4510611, 1.2352666, 1.279527, 0.0, 0.45063162, 0.0, 0.0, 0.8360535, 0.0, 0.0, 0.9618366, 0.6226404, 1.2925606, 2.504198, 1.4309518, 0.0, 0.5338122, 0.0, 0.0, 0.0, 0.0, 0.40210843, 1.4600532, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1927013, 0.8121241, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 24, 24, 25, 25, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, 16, -1, -1, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.1923077, 1.0, -0.5, 1.0, -0.004506534, 0.96153843, 0.012772108, -0.010343895, 1.0, -0.0021359886, 0.01354681, 0.65384614, 1.0, 1.0, -0.30769232, 1.0, 0.010225586, 1.4230769, -0.009743321, 0.0033487398, -0.010954414, 0.01686161, 1.0, 0.07692308, -0.011025293, -0.005879475, 0.004096322, -0.00731873, 0.0017691831, 1.0, 0.26923078, 0.00092345086, 0.016501626, -0.01079147, 0.001472025], "split_indices": [62, 15, 1, 125, 1, 5, 0, 1, 0, 0, 93, 0, 0, 1, 71, 71, 1, 113, 0, 1, 0, 0, 0, 0, 111, 1, 0, 0, 0, 0, 0, 106, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1734.0, 339.0, 1039.0, 695.0, 210.0, 129.0, 947.0, 92.0, 151.0, 544.0, 95.0, 115.0, 625.0, 322.0, 254.0, 290.0, 526.0, 99.0, 217.0, 105.0, 118.0, 136.0, 95.0, 195.0, 422.0, 104.0, 97.0, 120.0, 101.0, 94.0, 197.0, 225.0, 103.0, 94.0, 90.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0014904052, 0.004398767, -0.005721649, -0.0031542236, 0.028513353, 0.037978433, -0.012916831, -0.029225899, 0.0756933, -0.0012022196, 0.011565801, -0.0020262029, -0.062501796, -0.0066429065, 0.0014113844, -0.00016882921, 0.013089219, -0.031135956, 0.015028463, -0.0015994752, -0.010225139, -0.058934037, 0.0015126526, 0.031704262, -0.007014887, -0.007672604, -0.004552158, 0.008943935, 0.009439392, 0.058493663, -0.0075161434, -0.0024616115, 0.015853325], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1], "loss_changes": [0.3508724, 0.35662478, 0.0, 0.59872884, 1.2721713, 1.1108322, 0.6507136, 0.3385991, 1.0977445, 0.0, 0.0, 0.49049962, 0.40115362, 0.0, 0.0, 0.0, 0.0, 0.46939313, 0.88490915, 0.0, 0.0, 0.05440831, 0.0, 0.66972643, 0.0, 0.0, 0.0, 0.0, 1.5604119, 1.9787962, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 17, 17, 18, 18, 21, 21, 23, 23, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.005721649, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0012022196, 0.011565801, 1.0, 1.0, -0.0066429065, 0.0014113844, -0.00016882921, 0.013089219, 1.0, 1.0, -0.0015994752, -0.010225139, 1.0, 0.0015126526, 1.0, -0.007014887, -0.007672604, -0.004552158, 0.008943935, 1.0, 1.0, -0.0075161434, -0.0024616115, 0.015853325], "split_indices": [14, 61, 0, 89, 17, 97, 116, 12, 93, 0, 0, 81, 59, 0, 0, 0, 0, 62, 7, 0, 0, 106, 0, 17, 0, 0, 0, 0, 50, 111, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1958.0, 97.0, 1491.0, 467.0, 286.0, 1205.0, 210.0, 257.0, 174.0, 112.0, 988.0, 217.0, 113.0, 97.0, 107.0, 150.0, 365.0, 623.0, 100.0, 117.0, 228.0, 137.0, 521.0, 102.0, 98.0, 130.0, 145.0, 376.0, 238.0, 138.0, 130.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0019523647, 0.0043366468, -0.004537199, 0.00036007602, 0.004632454, -0.0027674045, 0.0057596364, -0.043641344, 0.0036191465, 0.00026899416, -0.011571228, 0.01574031, -0.031966507, 0.026227932, -0.0071360255, 0.0026018682, -0.06993912, -0.004200285, 0.061050605, -0.013578533, 0.0004603718, 0.008368335, -0.033569567, 0.027051779, 0.015985059, -0.0020984341, -0.0118482215, 0.06961345, -0.0032294758, -0.0058057695, 0.0043880814, 0.013602005, 0.00045349525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, -1, 13, 15, 17, -1, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.23289053, 0.32809162, 0.0, 0.32131484, 0.0, 0.44429618, 0.0, 0.7680023, 0.6349318, 0.0, 0.0, 1.0029985, 0.8234918, 1.0384002, 0.0, 0.0, 1.1092904, 1.3499042, 1.5351002, 0.0, 0.0, 0.0, 1.04754, 0.858802, 0.0, 0.735868, 0.0, 0.8556846, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 18, 18, 22, 22, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, -1, 14, 16, 18, -1, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.3076923, -0.004537199, 1.0, 0.004632454, 1.0, 0.0057596364, 1.0, 1.0, 0.00026899416, -0.011571228, 1.0, 1.0, 1.0, -0.0071360255, 0.0026018682, -0.34615386, 1.0, 1.0, -0.013578533, 0.0004603718, 0.008368335, 1.0, 1.0, 0.015985059, -0.1923077, -0.0118482215, -0.115384616, -0.0032294758, -0.0058057695, 0.0043880814, 0.013602005, 0.00045349525], "split_indices": [1, 1, 0, 84, 0, 26, 0, 15, 113, 0, 0, 125, 15, 97, 0, 0, 1, 127, 62, 0, 0, 0, 0, 12, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1965.0, 99.0, 1795.0, 170.0, 1702.0, 93.0, 230.0, 1472.0, 140.0, 90.0, 1098.0, 374.0, 980.0, 118.0, 148.0, 226.0, 523.0, 457.0, 120.0, 106.0, 131.0, 392.0, 340.0, 117.0, 286.0, 106.0, 198.0, 142.0, 129.0, 157.0, 98.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020914283, 0.013731465, -0.016544756, -0.0053135124, 0.049289435, -0.024821993, 0.007617887, -0.045509256, 0.021414578, -0.006173389, 0.09570084, -0.013241268, -0.010071081, -0.012344957, 0.0028888294, 0.0118585015, -0.0070887404, 0.015855614, -0.0015724417, 0.01487478, -0.034860156, 0.005284551, -0.03572729, -0.031366907, 0.0067986674, -0.01315409, -0.012488318, 0.0013039558, -0.008401605, -0.008561342, 0.0019228371, 0.027940346, -0.0068435906, -0.0029599196, 0.011488902], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, -1, -1, -1, -1, 21, -1, -1, 23, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.475225, 0.6717833, 0.8335, 0.69403374, 1.782851, 0.8762106, 0.0, 1.496031, 1.0746362, 0.0, 1.708895, 0.52577966, 0.0, 0.0, 0.0, 0.0, 0.51492894, 0.0, 0.0, 0.92345, 0.95552814, 0.0, 0.4780431, 0.5516684, 0.0, 0.8950796, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1306752, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 16, 16, 19, 19, 20, 20, 22, 22, 23, 23, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, -1, -1, -1, -1, 22, -1, -1, 24, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.007617887, 1.0, -0.34615386, -0.006173389, 1.0, 1.0, -0.010071081, -0.012344957, 0.0028888294, 0.0118585015, 1.0, 0.015855614, -0.0015724417, 1.0, 1.0, 0.005284551, 0.07692308, 1.0, 0.0067986674, 0.9230769, -0.012488318, 0.0013039558, -0.008401605, -0.008561342, 0.0019228371, 0.30769232, -0.0068435906, -0.0029599196, 0.011488902], "split_indices": [126, 61, 31, 122, 89, 61, 0, 16, 1, 0, 23, 16, 0, 0, 0, 0, 53, 0, 0, 71, 62, 0, 1, 74, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 992.0, 1086.0, 646.0, 346.0, 997.0, 89.0, 258.0, 388.0, 102.0, 244.0, 865.0, 132.0, 126.0, 132.0, 88.0, 300.0, 156.0, 88.0, 376.0, 489.0, 97.0, 203.0, 201.0, 175.0, 394.0, 95.0, 101.0, 102.0, 97.0, 104.0, 226.0, 168.0, 136.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.002803146, -0.006019425, 0.005517837, -0.0018151002, -0.006844006, -0.005208301, 0.005603561, -0.013954994, 0.024262333, -0.03029185, 0.006039362, 0.05301084, -0.0053922827, -0.06061444, 0.019593691, 0.02820023, -0.008223779, 0.0010625666, 0.009872626, 0.0058714272, -0.13606818, -0.007126488, 0.0075264275, -0.0040318104, 0.06037043, -0.006414607, 0.010172066, -0.0054556187, -0.01936441, -0.00018401035, 0.10669744, 0.01739568, 0.003651373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.38676924, 0.51568776, 0.0, 0.36138642, 0.0, 0.44826302, 0.0, 0.43803087, 0.8945873, 1.1163423, 1.1796477, 0.56385756, 0.0, 2.3026233, 1.4112239, 1.0624478, 0.0, 0.0, 0.0, 1.6375145, 1.009022, 0.0, 0.0, 0.0, 0.94530547, 0.0, 0.0, 0.0, 0.0, 0.0, 0.88745546, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 24, 24, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [3.5, 1.3461539, 0.005517837, 1.0, -0.006844006, 0.42307693, 0.005603561, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0053922827, 1.0, 1.0, 1.0, -0.008223779, 0.0010625666, 0.009872626, 1.0, 1.0, -0.007126488, 0.0075264275, -0.0040318104, 1.0, -0.006414607, 0.010172066, -0.0054556187, -0.01936441, -0.00018401035, 1.0, 0.01739568, 0.003651373], "split_indices": [1, 1, 0, 84, 0, 1, 0, 122, 80, 50, 50, 97, 0, 69, 69, 5, 0, 0, 0, 97, 17, 0, 0, 0, 115, 0, 0, 0, 0, 0, 69, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1965.0, 109.0, 1841.0, 124.0, 1739.0, 102.0, 1341.0, 398.0, 738.0, 603.0, 291.0, 107.0, 459.0, 279.0, 482.0, 121.0, 151.0, 140.0, 244.0, 215.0, 106.0, 173.0, 154.0, 328.0, 141.0, 103.0, 89.0, 126.0, 140.0, 188.0, 96.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002952339, 0.006570894, -0.0005018549, -0.008637115, 0.026822397, -0.013996818, 0.007621477, 0.08730967, -0.015265696, -0.008472255, -0.009041021, 0.012013251, 0.0051875897, -0.007618545, 0.004162258, 0.013365894, -0.026655488, -0.015467322, 0.010837032, 0.009740025, -0.06568074, -0.07722342, 0.052521028, 0.0054695727, -0.016276877, -0.10063057, 0.001133829, -0.0019111639, -0.015193854, 0.008210263, 0.0008820894, -0.00468802, 0.0017910589, -0.0019662264, -0.01603225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 140, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, -1, -1, 17, 19, 21, -1, 23, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4446026, 0.0, 0.4321315, 0.68126154, 1.1354245, 0.5948097, 0.0, 0.21283507, 0.91145813, 0.5217739, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6353526, 1.0183871, 1.9230027, 0.0, 0.43392467, 0.9313636, 1.0420383, 0.28181273, 0.0, 0.24586885, 1.1502905, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, -1, -1, 18, 20, 22, -1, 24, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.006570894, 1.0, 3.4615386, -0.15384616, 1.0, 0.007621477, 1.0, 1.0, -0.03846154, -0.009041021, 0.012013251, 0.0051875897, -0.007618545, 0.004162258, -0.1923077, 1.0, 1.0, 0.010837032, 1.0, 1.0, -0.34615386, -0.30769232, 0.0054695727, 0.65384614, 1.0, 0.001133829, -0.0019111639, -0.015193854, 0.008210263, 0.0008820894, -0.00468802, 0.0017910589, -0.0019662264, -0.01603225], "split_indices": [1, 0, 61, 1, 1, 125, 0, 39, 12, 1, 0, 0, 0, 0, 0, 1, 2, 69, 0, 69, 15, 1, 1, 0, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 107.0, 1944.0, 1498.0, 446.0, 1409.0, 89.0, 183.0, 263.0, 1314.0, 95.0, 95.0, 88.0, 127.0, 136.0, 597.0, 717.0, 458.0, 139.0, 371.0, 346.0, 240.0, 218.0, 136.0, 235.0, 238.0, 108.0, 135.0, 105.0, 130.0, 88.0, 124.0, 111.0, 101.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0044065644, -0.0011836807, 0.025952032, 0.013125102, -0.018981459, 0.0009551544, 0.0077384436, 0.066357285, -0.0030892952, -0.00038553015, -0.013218189, 0.0037649774, -0.02164208, 0.002069037, 0.012049845, -0.046496764, 0.046176843, -0.029954087, 0.010060565, 0.004752764, -0.009003464, -0.07890494, 0.0014774919, 0.10010908, -0.0033087176, -0.051818594, 0.005422424, -0.0011526581, -0.014409211, -0.0026379402, 0.022148693, -0.0064240284, -0.011774189, 0.003297258, -0.0034564484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, 19, -1, -1, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.24847695, 0.41714054, 0.5464008, 0.78372025, 1.5366986, 0.23715037, 0.0, 0.5241611, 1.4884088, 1.8723241, 0.0, 0.0, 0.8373331, 0.0, 0.0, 0.7347105, 1.3936129, 0.8926512, 0.0, 0.0, 0.0, 1.062913, 0.0, 2.9784632, 0.0, 1.1521353, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2527696, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, 20, -1, -1, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 0.0077384436, 1.0, 1.0, 1.0, -0.013218189, 0.0037649774, 1.0, 0.002069037, 0.012049845, 1.0, 1.0, 1.0, 0.010060565, 0.004752764, -0.009003464, 1.0, 0.0014774919, 1.0, -0.0033087176, 1.0, 0.005422424, -0.0011526581, -0.014409211, -0.0026379402, 0.022148693, 1.0, -0.011774189, 0.003297258, -0.0034564484], "split_indices": [113, 12, 137, 0, 64, 69, 0, 111, 97, 0, 0, 0, 61, 0, 0, 23, 109, 58, 0, 0, 0, 126, 0, 2, 0, 71, 0, 0, 0, 0, 0, 106, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1638.0, 425.0, 908.0, 730.0, 286.0, 139.0, 212.0, 696.0, 627.0, 103.0, 109.0, 177.0, 115.0, 97.0, 370.0, 326.0, 485.0, 142.0, 88.0, 89.0, 242.0, 128.0, 194.0, 132.0, 385.0, 100.0, 119.0, 123.0, 95.0, 99.0, 228.0, 157.0, 95.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0044044107, 0.00029801956, 0.029224321, 0.013128273, -0.013475412, 0.0076560737, 0.005916528, 0.003228187, 0.0054686246, -0.002471458, -0.0095841605, 0.005714604, -0.0049087754, -0.017913204, 0.06182312, 0.0066166692, -0.014533843, 0.023198007, -0.06215075, -0.0023094085, 0.016073769, 0.0019645365, -0.008358757, 0.0690233, -0.007507176, -0.011167882, 0.0013570016, 0.030630805, -0.011522988, 0.020119399, -0.0073095686, -0.00036863456, 0.011106903, 0.00511497, -0.0062872516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, -1, -1, -1, 17, 19, -1, 21, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.21107678, 0.3140254, 0.32437205, 0.3785131, 0.77674496, 0.0, 0.55511504, 0.9204125, 0.0, 0.62592226, 0.0, 0.0, 0.0, 0.992988, 1.6547118, 0.0, 0.7325537, 1.2744174, 0.98633015, 0.0, 0.0, 1.743594, 0.0, 3.625306, 0.0, 0.0, 0.0, 1.0398064, 0.0, 0.0, 0.0, 0.9692488, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 21, 21, 23, 23, 27, 27, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, -1, -1, -1, 18, 20, -1, 22, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 2.0, 1.0, 0.0076560737, 1.0, 0.1923077, 0.0054686246, 1.0, -0.0095841605, 0.005714604, -0.0049087754, 1.0, 1.0, 0.0066166692, 2.0, 1.0, 1.0, -0.0023094085, 0.016073769, 0.5, -0.008358757, -0.26923078, -0.007507176, -0.011167882, 0.0013570016, 0.115384616, -0.011522988, 0.020119399, -0.0073095686, 1.0, 0.011106903, 0.00511497, -0.0062872516], "split_indices": [1, 126, 137, 0, 64, 0, 106, 1, 0, 89, 0, 0, 0, 108, 71, 0, 0, 125, 12, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 115, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1777.0, 294.0, 920.0, 857.0, 97.0, 197.0, 743.0, 177.0, 756.0, 101.0, 102.0, 95.0, 546.0, 197.0, 113.0, 643.0, 283.0, 263.0, 106.0, 91.0, 519.0, 124.0, 193.0, 90.0, 159.0, 104.0, 417.0, 102.0, 100.0, 93.0, 301.0, 116.0, 165.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0023416646, 0.004418119, 4.208622e-05, 0.011218033, -0.013966293, -0.0034109806, 0.06797078, 0.014236034, -0.05684036, 0.0091595305, -0.011088197, 0.019156562, -0.0013510233, -0.02012843, 0.062409062, -0.011473904, -0.00526939, -0.009715406, 0.012881952, 0.0013917921, -0.005576363, 0.004111422, 0.009180517, -0.0034099508, 0.0023247325, 0.006596866, -0.009305632, 0.00975821, -0.012382842, -0.027743392, 0.0044404618, 0.004218692, -0.012722092, -0.0029720548, 0.0055744657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 143, "left_children": [1, -1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, -1, -1, -1, -1, -1, -1, -1, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1], "loss_changes": [0.19944997, 0.0, 0.3076343, 0.90744895, 1.0543776, 1.1739892, 2.2558222, 0.8707618, 1.0331179, 1.7571716, 0.0, 0.0, 0.0, 0.3724674, 0.13709038, 0.0, 0.15045169, 0.9135705, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.970503, 0.0, 0.0, 0.40561318, 1.1637008, 0.0, 0.48440436, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 25, 25, 28, 28, 29, 29, 31, 31], "right_children": [2, -1, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, -1, -1, -1, -1, -1, -1, -1, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1], "split_conditions": [-0.5769231, 0.004418119, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.3846154, -0.011088197, 0.019156562, -0.0013510233, 1.0, -0.30769232, -0.011473904, -0.07692308, 1.0, 0.012881952, 0.0013917921, -0.005576363, 0.004111422, 0.009180517, -0.0034099508, 0.0023247325, 1.0, -0.009305632, 0.00975821, 0.53846157, 0.1923077, 0.0044404618, 1.0, -0.012722092, -0.0029720548, 0.0055744657], "split_indices": [1, 0, 109, 105, 105, 64, 93, 2, 93, 1, 0, 0, 0, 59, 1, 0, 1, 113, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 1, 1, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 108.0, 1965.0, 1093.0, 872.0, 869.0, 224.0, 526.0, 346.0, 778.0, 91.0, 89.0, 135.0, 307.0, 219.0, 163.0, 183.0, 672.0, 106.0, 157.0, 150.0, 127.0, 92.0, 91.0, 92.0, 562.0, 110.0, 97.0, 465.0, 366.0, 99.0, 277.0, 89.0, 167.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00038357542, -0.0063827652, 0.022407796, -0.011919452, 0.008395261, -0.002792222, 0.055575483, -0.0073690536, -0.007790019, 0.01416622, -0.002354832, -0.041413423, 0.003916542, -0.010316963, 0.00774574, 0.011970653, -0.0059922165, 0.008487095, -0.0052316342, 0.03355715, -0.010950613, -0.013992774, 0.069978386, 0.034955516, -0.05824785, 0.005002172, -0.010027322, 0.014149745, 0.035903834, 0.010991896, -0.0032811405, 0.0015301922, -0.011148913, -0.003971175, 0.011393271], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 144, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, -1, 17, 19, -1, -1, -1, 21, 23, 25, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2841244, 0.82276076, 0.72282004, 0.46536958, 0.0, 0.0, 1.7778041, 0.557106, 0.0, 0.0, 0.0, 1.0959544, 0.5599244, 0.0, 0.93109256, 0.47846186, 0.0, 0.0, 0.0, 0.86245006, 1.0183084, 1.1930113, 0.68722785, 1.2090502, 0.90456957, 0.0, 0.0, 0.0, 1.1269383, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, -1, 18, 20, -1, -1, -1, 22, 24, 26, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.34615386, 1.0, 0.008395261, -0.002792222, 1.0, -0.26923078, -0.007790019, 0.01416622, -0.002354832, 1.0, 1.4615384, -0.010316963, 1.0, 1.0, -0.0059922165, 0.008487095, -0.0052316342, 1.0, 1.0, 1.0, -0.03846154, 1.0, 1.0, 0.005002172, -0.010027322, 0.014149745, 1.0, 0.010991896, -0.0032811405, 0.0015301922, -0.011148913, -0.003971175, 0.011393271], "split_indices": [113, 102, 1, 84, 0, 0, 93, 1, 0, 0, 0, 59, 1, 0, 13, 12, 0, 0, 0, 59, 115, 122, 1, 137, 81, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1645.0, 433.0, 1550.0, 95.0, 172.0, 261.0, 1450.0, 100.0, 125.0, 136.0, 361.0, 1089.0, 160.0, 201.0, 967.0, 122.0, 88.0, 113.0, 498.0, 469.0, 216.0, 282.0, 238.0, 231.0, 124.0, 92.0, 91.0, 191.0, 113.0, 125.0, 97.0, 134.0, 97.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025308933, -0.00029078906, 0.0048839706, 0.0034578436, -0.0073606186, -0.0015156885, 0.0059279217, -0.005731185, 0.0061480342, 0.007597641, -0.05347054, 0.03125847, -0.012819077, 0.001640542, -0.014189506, 0.010404556, 0.0099747395, -0.048763845, 0.030574763, 0.005088139, -0.0040383944, -0.03420723, 0.0106172385, 0.003134998, -0.09115743, 0.002044665, 0.009054616, -0.003934161, -0.010074488, -0.013637329, -0.0028929606, 0.006195156, -0.005023769, 0.0041913087, -0.006801614], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 145, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, -1, -1, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.2707458, 0.536748, 0.0, 0.5158355, 0.0, 0.45304438, 0.0, 1.0174592, 0.0, 0.60384566, 1.7007375, 0.8969754, 1.0466136, 0.4449041, 0.0, 0.0, 1.9040905, 1.2464466, 0.52014065, 0.0, 0.0, 0.6183895, 0.0, 0.0, 0.67528415, 0.6452072, 0.0, 0.6199144, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, -1, -1, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0048839706, 1.0, -0.0073606186, 3.1923077, 0.0059279217, 1.0, 0.0061480342, 1.0, 1.0, -1.0, 1.0, -0.30769232, -0.014189506, 0.010404556, 1.0, 1.0, 0.0, 0.005088139, -0.0040383944, 0.5769231, 0.0106172385, 0.003134998, 1.0, 1.0, 0.009054616, 1.0, -0.010074488, -0.013637329, -0.0028929606, 0.006195156, -0.005023769, 0.0041913087, -0.006801614], "split_indices": [84, 52, 0, 125, 0, 1, 0, 113, 0, 111, 97, 0, 15, 1, 0, 0, 59, 53, 1, 0, 0, 1, 0, 0, 106, 122, 0, 137, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1953.0, 119.0, 1858.0, 95.0, 1706.0, 152.0, 1599.0, 107.0, 1250.0, 349.0, 579.0, 671.0, 215.0, 134.0, 131.0, 448.0, 367.0, 304.0, 99.0, 116.0, 307.0, 141.0, 127.0, 240.0, 206.0, 98.0, 211.0, 96.0, 139.0, 101.0, 96.0, 110.0, 123.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [6.271013e-05, -0.003467453, 0.03628527, 0.016106108, -0.018238582, 0.011760292, -0.0048646457, -0.0017721023, 0.06332705, -0.03153953, 0.043391436, 0.016149146, -0.008534718, -0.0023972548, 0.016321797, -0.07009577, -0.0017144532, 0.007848958, 0.0007923818, 0.05959879, -0.009949308, -0.040263925, -0.014788158, -0.03785781, 0.011498568, 0.009880507, 0.0017719314, -0.032860257, 0.0040213577, 0.009273938, -0.013775869, 0.0031044993, -0.07698781, 0.0015894208, -0.008161475, -0.0069894358, 0.008427552, -0.0126787815, -0.0009298441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 146, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, 25, 27, 29, -1, 31, -1, -1, -1, 33, -1, 35, -1, -1, 37, -1, -1, -1, -1, -1, -1], "loss_changes": [0.26494995, 0.5458654, 1.2707868, 0.6855116, 0.8820375, 0.0, 0.0, 0.8821867, 1.944658, 1.0176991, 0.23776555, 0.5499749, 0.0, 0.0, 0.0, 0.89571035, 2.1047487, 0.0, 0.0, 0.29883248, 0.3482316, 1.3474817, 0.0, 1.0272397, 0.0, 0.0, 0.0, 0.49441597, 0.0, 1.0984833, 0.0, 0.0, 0.8191358, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 23, 23, 27, 27, 29, 29, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, 26, 28, 30, -1, 32, -1, -1, -1, 34, -1, 36, -1, -1, 38, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.011760292, -0.0048646457, 1.0, 1.0, 1.0, 1.0, 1.0, -0.008534718, -0.0023972548, 0.016321797, 1.0, 2.0, 0.007848958, 0.0007923818, 1.0, 1.0, 1.0, -0.014788158, 1.0, 0.011498568, 0.009880507, 0.0017719314, 1.0, 0.0040213577, 1.0, -0.013775869, 0.0031044993, 1.0, 0.0015894208, -0.008161475, -0.0069894358, 0.008427552, -0.0126787815, -0.0009298441], "split_indices": [125, 127, 15, 93, 58, 0, 0, 0, 13, 15, 69, 53, 0, 0, 0, 0, 0, 0, 0, 13, 15, 61, 0, 13, 0, 0, 0, 111, 0, 69, 0, 0, 7, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1888.0, 184.0, 812.0, 1076.0, 94.0, 90.0, 589.0, 223.0, 885.0, 191.0, 485.0, 104.0, 119.0, 104.0, 386.0, 499.0, 96.0, 95.0, 182.0, 303.0, 279.0, 107.0, 381.0, 118.0, 94.0, 88.0, 208.0, 95.0, 185.0, 94.0, 138.0, 243.0, 104.0, 104.0, 90.0, 95.0, 140.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.0012289708, -0.0040204427, 0.0045159645, -0.0061430037, 0.04569097, -0.0003589671, -0.009302529, 0.019355057, 0.013594322, -0.013609413, 0.046227243, -0.0034078886, 0.0067779585, -0.009040211, -0.00031056823, 0.007290372, 0.013680667, 0.011768702, -0.008770149, -0.004720349, 0.008513878, 0.024109982, -0.008629799, 0.008781592, 0.011882545, -0.010978851, 0.053742923, 0.007407638, -0.0035946686, 0.0011189709, 0.008950191], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 147, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, -1, 17, 19, -1, 21, -1, -1, -1, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1], "loss_changes": [0.2816435, 0.0, 0.84090006, 0.76485085, 0.9364884, 0.88087004, 0.0, 0.7891917, 0.0, 1.1346133, 1.1144935, 0.0, 0.0, 0.0, 0.99967116, 0.9375397, 0.0, 1.0069431, 0.0, 0.0, 0.0, 0.5756514, 0.0, 0.0, 0.59333193, 0.8515815, 0.33324313, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 17, 17, 21, 21, 24, 24, 25, 25, 26, 26], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, -1, 18, 20, -1, 22, -1, -1, -1, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, -0.0040204427, 1.0, 1.0, 1.0, 1.0, -0.009302529, 1.0, 0.013594322, -0.3846154, 0.53846157, -0.0034078886, 0.0067779585, -0.009040211, 1.0, 1.0, 0.013680667, 1.4615384, -0.008770149, -0.004720349, 0.008513878, -0.23076923, -0.008629799, 0.008781592, 1.0, 1.0, 0.42307693, 0.007407638, -0.0035946686, 0.0011189709, 0.008950191], "split_indices": [104, 0, 113, 84, 53, 0, 0, 15, 0, 1, 1, 0, 0, 0, 42, 12, 0, 1, 0, 0, 0, 1, 0, 0, 116, 5, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 152.0, 1916.0, 1522.0, 394.0, 1427.0, 95.0, 305.0, 89.0, 1111.0, 316.0, 145.0, 160.0, 164.0, 947.0, 221.0, 95.0, 832.0, 115.0, 130.0, 91.0, 739.0, 93.0, 119.0, 620.0, 401.0, 219.0, 91.0, 310.0, 100.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021337066, -0.008389711, 0.021604428, -0.014337195, 0.008745148, -0.005771322, 0.008982306, -0.008657503, -0.00831192, -0.006500419, 0.024278512, 0.005132596, -0.012833, 0.007401965, -0.0036816546, -0.009141078, -0.006061254, 0.0023802074, -0.0065482543, 0.007269632, -0.005295905, -0.0122662755, 0.0055415994, 0.0039307396, -0.045919448, 0.004945493, -0.0017633341, -0.011537208, 0.010547164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 148, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, 13, -1, 15, -1, -1, -1, 17, 19, -1, -1, 21, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.30829817, 0.9365326, 0.8086434, 0.60435176, 0.0, 0.55000097, 0.0, 0.35790837, 0.0, 0.0, 0.6229823, 0.0, 0.7108971, 0.0, 0.0, 0.0, 0.6169714, 0.5813157, 0.0, 0.0, 0.41091198, 0.47476563, 0.0, 0.5772323, 2.9756064, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 10, 10, 12, 12, 16, 16, 17, 17, 20, 20, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, 14, -1, 16, -1, -1, -1, 18, 20, -1, -1, 22, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.07692308, 1.0, 0.008745148, 1.0, 0.008982306, -0.5, -0.00831192, -0.006500419, -0.30769232, 0.005132596, -0.3846154, 0.007401965, -0.0036816546, -0.009141078, 1.0, -0.26923078, -0.0065482543, 0.007269632, 1.0, 1.0, 0.0055415994, 0.1923077, 0.7692308, 0.004945493, -0.0017633341, -0.011537208, 0.010547164], "split_indices": [113, 102, 1, 125, 0, 50, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 42, 1, 0, 0, 73, 121, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1643.0, 433.0, 1547.0, 96.0, 309.0, 124.0, 1429.0, 118.0, 104.0, 205.0, 93.0, 1336.0, 113.0, 92.0, 106.0, 1230.0, 1077.0, 153.0, 106.0, 971.0, 871.0, 100.0, 588.0, 283.0, 189.0, 399.0, 194.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036390456, 0.0016981411, -0.023390274, -0.00422734, 0.010442871, -0.04549211, 0.0015426055, 0.0005489088, -0.007542795, -0.0009935371, -0.009319738, -0.041648515, 0.009199384, -0.0124563, 0.0016105691, -0.011257887, 0.021858947, 0.009110412, 0.07416983, 0.020296918, -0.007747312, 0.00031368446, 0.014068251, 0.057145536, -0.019937782, -0.009020376, 0.10978421, 0.0046673007, -0.08236362, 0.0025161637, -0.004282262, 0.023770934, 0.001630044, -0.003628676, -0.012701538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 149, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, -1, 25, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.21852742, 0.9934446, 0.3783393, 0.5247311, 0.0, 0.47664422, 0.0, 0.52783, 0.0, 0.0, 0.0, 1.1780108, 1.8499913, 0.0, 0.0, 0.0, 0.724906, 0.8465275, 1.006338, 1.1475272, 0.0, 0.0, 0.0, 1.4070864, 1.5385468, 0.20682181, 2.690757, 0.0, 0.39296544, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, -1, 26, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.3846154, 1.0, 1.3461539, 0.010442871, 1.0, 0.0015426055, -1.0, -0.007542795, -0.0009935371, -0.009319738, 1.0, 1.0, -0.0124563, 0.0016105691, -0.011257887, 1.0, 4.0, 1.0, -0.03846154, -0.007747312, 0.00031368446, 0.014068251, 1.0, 1.0, -0.34615386, 1.0, 0.0046673007, 1.0, 0.0025161637, -0.004282262, 0.023770934, 0.001630044, -0.003628676, -0.012701538], "split_indices": [116, 1, 61, 1, 0, 109, 0, 0, 0, 0, 0, 106, 104, 0, 0, 0, 42, 0, 93, 1, 0, 0, 0, 124, 124, 1, 69, 0, 50, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1632.0, 441.0, 1543.0, 89.0, 281.0, 160.0, 1446.0, 97.0, 161.0, 120.0, 246.0, 1200.0, 101.0, 145.0, 113.0, 1087.0, 874.0, 213.0, 774.0, 100.0, 103.0, 110.0, 404.0, 370.0, 179.0, 225.0, 179.0, 191.0, 89.0, 90.0, 95.0, 130.0, 94.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0051817037, -0.0017812907, -0.02915526, 0.0015816867, -0.0067416066, -0.010664852, 0.002453286, -0.0037932207, 0.0060594487, -0.01035603, 0.04434549, -0.0181296, 0.034482807, -0.0017047344, 0.010139745, -0.0037307048, -0.07477318, 0.010318692, -0.00513973, 0.005134096, -0.0146895535, -0.0015139868, -0.011676844, 0.0092869485, -0.058947265, -0.024127541, 0.046111092, 0.001537777, -0.013065524, 0.0024570418, -0.010065294, 0.0074407007, 0.0014644057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 150, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, -1, 23, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.16996908, 0.40305, 1.0775614, 0.5509563, 0.0, 0.0, 0.0, 0.5029528, 0.0, 0.4883295, 0.66899335, 0.9738322, 1.2213657, 0.0, 0.0, 0.5745529, 0.6060443, 0.0, 0.0, 0.0, 0.8425494, 0.0, 0.0, 0.63368696, 1.4869863, 1.0061904, 0.2181449, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 20, 20, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, -1, 24, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, -0.0067416066, -0.010664852, 0.002453286, 1.0, 0.0060594487, 1.0, 1.0, 1.0, 1.0, -0.0017047344, 0.010139745, 1.0, 1.0, 0.010318692, -0.00513973, 0.005134096, 1.0, -0.0015139868, -0.011676844, 1.0, 1.0, 1.0, 1.0, 0.001537777, -0.013065524, 0.0024570418, -0.010065294, 0.0074407007, 0.0014644057], "split_indices": [0, 52, 13, 125, 0, 0, 0, 40, 0, 64, 97, 113, 59, 0, 0, 89, 71, 0, 0, 0, 23, 0, 0, 12, 59, 83, 71, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2085.0, 1826.0, 259.0, 1737.0, 89.0, 106.0, 153.0, 1592.0, 145.0, 1401.0, 191.0, 1194.0, 207.0, 92.0, 99.0, 952.0, 242.0, 115.0, 92.0, 158.0, 794.0, 100.0, 142.0, 515.0, 279.0, 270.0, 245.0, 137.0, 142.0, 165.0, 105.0, 129.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018646996, 0.00880852, -0.011628722, -0.0050343154, 0.034956105, -0.054186765, 0.0029807524, 0.019700816, -0.037366677, -0.0072554597, 0.07875678, -0.011894741, 0.0017494835, 0.006736748, -0.00776594, 0.0063584493, -0.014763911, 0.0019622257, -0.011117204, -0.0052379826, 0.017523587, -0.009683023, 0.006131671, 0.008413032, -0.009215943, -0.010541677, 0.009085952, 0.0075948606, -0.029362308, 0.013972563, -0.0839499, 0.0072694295, -0.0054815724, -0.014071493, -0.0025271398], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 151, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1, -1, -1, 25, -1, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1], "loss_changes": [0.21551365, 0.35761338, 0.6714909, 0.5166355, 1.610492, 1.2812328, 0.5563235, 0.55355287, 1.1777052, 0.0, 3.0744221, 0.0, 0.0, 0.0, 0.8528313, 0.0, 1.5690638, 0.0, 0.0, 0.0, 0.0, 0.0, 0.84196764, 0.0, 0.0, 0.81064516, 0.0, 0.0, 0.9675087, 0.9209759, 0.6028899, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 14, 14, 16, 16, 22, 22, 25, 25, 28, 28, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1, -1, -1, 26, -1, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.1923077, 1.0, 1.0, 1.0, -0.03846154, 0.07692308, 1.0, -0.0072554597, 1.0, -0.011894741, 0.0017494835, 0.006736748, 0.07692308, 0.0063584493, 1.0, 0.0019622257, -0.011117204, -0.0052379826, 0.017523587, -0.009683023, 1.0, 0.008413032, -0.009215943, 1.0, 0.009085952, 0.0075948606, 1.0, 1.0, 1.1153846, 0.0072694295, -0.0054815724, -0.014071493, -0.0025271398], "split_indices": [126, 61, 1, 15, 89, 97, 1, 1, 124, 0, 17, 0, 0, 0, 1, 0, 53, 0, 0, 0, 0, 0, 15, 0, 0, 26, 0, 0, 127, 39, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 988.0, 1080.0, 646.0, 342.0, 276.0, 804.0, 366.0, 280.0, 99.0, 243.0, 145.0, 131.0, 115.0, 689.0, 161.0, 205.0, 158.0, 122.0, 103.0, 140.0, 93.0, 596.0, 90.0, 115.0, 498.0, 98.0, 89.0, 409.0, 228.0, 181.0, 123.0, 105.0, 92.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047214786, -0.006913461, 0.00030078925, -0.008179684, 0.033374645, 0.010170328, -0.029588038, 0.009525358, 0.0045288187, -0.003987245, 0.060073834, -0.05088988, 0.00481425, 0.00778212, -0.0042523057, 0.014951624, -0.0086586205, -0.004974049, 0.01735898, -0.011570603, -0.024132434, -0.011587254, 0.047274627, -0.060395375, 0.0045145997, 0.007183004, -0.009559202, -0.0015688327, 0.009677988, -0.0010164765, -0.011732338], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 152, "left_children": [1, -1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, 21, -1, -1, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.66641015, 0.0, 0.5360011, 0.5975151, 0.6961298, 0.57863396, 1.162374, 0.0, 0.91731274, 0.99804324, 2.2562885, 0.9556072, 0.0, 0.0, 0.0, 0.4452067, 0.0, 0.0, 0.0, 0.0, 0.9797735, 1.9971236, 0.7293775, 0.7320388, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 20, 20, 21, 21, 22, 22, 23, 23], "right_children": [2, -1, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, 22, -1, -1, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.006913461, 1.0, 1.0, 1.0, 1.0, 1.0, 0.009525358, -0.30769232, 0.8076923, 0.23076923, -0.07692308, 0.00481425, 0.00778212, -0.0042523057, 1.0, -0.0086586205, -0.004974049, 0.01735898, -0.011570603, 1.0, 1.0, -0.23076923, 1.0, 0.0045145997, 0.007183004, -0.009559202, -0.0015688327, 0.009677988, -0.0010164765, -0.011732338], "split_indices": [104, 0, 113, 12, 69, 74, 0, 0, 1, 1, 1, 1, 0, 0, 0, 59, 0, 0, 0, 0, 116, 17, 1, 97, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 149.0, 1911.0, 1521.0, 390.0, 819.0, 702.0, 124.0, 266.0, 638.0, 181.0, 551.0, 151.0, 104.0, 162.0, 519.0, 119.0, 92.0, 89.0, 161.0, 390.0, 285.0, 234.0, 256.0, 134.0, 143.0, 142.0, 103.0, 131.0, 136.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0025091406, -0.0008090099, 0.005649185, -0.01773621, 0.011681255, 0.006098814, -0.010847658, -0.020461516, 0.02556297, -0.017538302, 0.009992556, 0.00942875, -0.006179914, 0.008596465, 0.009778524, 0.009697074, -0.045480706, -0.0050660297, 0.00824916, -0.0088848965, 0.010583657, -0.006315944, -0.010040139, -0.04021696, 0.008190945, 0.011691578, -0.009045349, -0.076700054, 0.004399816, -0.015586785, -0.0011594938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 153, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, 21, -1, -1, 23, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.3680974, 0.4093192, 0.0, 1.777821, 0.49706322, 1.4437839, 0.0, 0.41515929, 0.95332927, 1.6638219, 0.0, 0.85610414, 0.0, 1.0709306, 0.0, 0.0, 0.89909905, 0.0, 0.0, 1.5191092, 0.0, 2.5298932, 0.0, 1.2197541, 0.0, 0.0, 0.0, 1.4277208, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 16, 16, 19, 19, 21, 21, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, 22, -1, -1, 24, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.005649185, 1.0, 1.0, 1.0, -0.010847658, 1.0, 0.65384614, 1.0, 0.009992556, 0.03846154, -0.006179914, 1.0, 0.009778524, 0.009697074, 1.0, -0.0050660297, 0.00824916, 1.0, 0.010583657, 1.0, -0.010040139, 1.0, 0.008190945, 0.011691578, -0.009045349, 1.0, 0.004399816, -0.015586785, -0.0011594938], "split_indices": [84, 53, 0, 116, 81, 23, 0, 124, 1, 5, 0, 1, 0, 64, 0, 0, 97, 0, 0, 116, 0, 39, 0, 97, 0, 0, 0, 122, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1936.0, 119.0, 822.0, 1114.0, 651.0, 171.0, 336.0, 778.0, 520.0, 131.0, 195.0, 141.0, 630.0, 148.0, 102.0, 418.0, 107.0, 88.0, 534.0, 96.0, 244.0, 174.0, 397.0, 137.0, 99.0, 145.0, 277.0, 120.0, 125.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.00079030055, 0.0045212065, -0.037378427, -0.0013379942, 0.042204875, -0.008239354, 0.0024005799, 0.007974972, -0.047120832, -0.003822228, 0.013180357, 0.0067923903, -0.0017011825, -0.013031879, 0.001403791, 0.029510932, -0.027797408, 0.08317792, -0.023943922, -0.0798793, 0.005835849, 0.020859474, 0.0012290132, -0.008459545, 0.0010125748, -0.016501082, 0.0030874293, 0.047167182, -0.006275018, -0.0035103157, 0.012059859], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 154, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, 15, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.39858755, 0.39544612, 0.71843696, 0.66087985, 1.7366872, 0.0, 0.0, 0.7471369, 1.3331304, 0.0, 0.0, 0.0, 0.9033009, 0.0, 0.0, 1.4487244, 1.0580173, 2.2404108, 0.52279377, 2.2345839, 1.0403539, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3834412, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, 16, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 1.0, -0.008239354, 0.0024005799, -0.42307693, -0.26923078, -0.003822228, 0.013180357, 0.0067923903, 1.0, -0.013031879, 0.001403791, 1.0, 1.0, 0.07692308, 0.0, 1.0, 1.0, 0.020859474, 0.0012290132, -0.008459545, 0.0010125748, -0.016501082, 0.0030874293, 1.0, -0.006275018, -0.0035103157, 0.012059859], "split_indices": [0, 0, 122, 42, 109, 0, 0, 1, 1, 0, 0, 0, 39, 0, 0, 122, 59, 1, 1, 122, 83, 0, 0, 0, 0, 0, 0, 115, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 1791.0, 260.0, 1550.0, 241.0, 150.0, 110.0, 1288.0, 262.0, 127.0, 114.0, 179.0, 1109.0, 111.0, 151.0, 505.0, 604.0, 252.0, 253.0, 237.0, 367.0, 91.0, 161.0, 91.0, 162.0, 134.0, 103.0, 229.0, 138.0, 108.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029361828, -0.009968406, 0.022065539, 0.0030383153, -0.027036222, -0.003299296, 0.008290302, 0.0066996547, -0.010673098, -0.049740195, 0.004766263, -0.0657865, 0.008078272, 0.01813064, -0.032292694, -0.074164346, 0.0020169502, -0.010023162, -0.003387941, -0.0047288905, 0.0071054334, -0.010791018, 0.0059140213, -0.11245493, -0.0023485625, -0.031057216, 0.008473186, -0.0020406381, -0.020050133, -0.010427502, 0.0031003563], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 155, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [0.36200854, 0.3567481, 0.6974963, 0.79978573, 1.1786928, 1.6760422, 0.0, 0.0, 0.4676667, 0.91008973, 0.0, 0.20112455, 0.0, 1.1148428, 1.2394223, 0.76650405, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.83048815, 1.8235219, 0.0, 0.8815274, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 0.008290302, 0.0066996547, 1.0, 1.0, 0.004766263, 1.0, 0.008078272, 1.0, 1.0, 1.0, 0.0020169502, -0.010023162, -0.003387941, -0.0047288905, 0.0071054334, -0.010791018, 1.0, 1.0, -0.0023485625, 1.0, 0.008473186, -0.0020406381, -0.020050133, -0.010427502, 0.0031003563], "split_indices": [58, 15, 64, 26, 0, 12, 0, 0, 108, 62, 0, 15, 0, 39, 59, 121, 0, 0, 0, 0, 0, 0, 23, 5, 0, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1607.0, 452.0, 912.0, 695.0, 319.0, 133.0, 161.0, 751.0, 533.0, 162.0, 183.0, 136.0, 322.0, 429.0, 395.0, 138.0, 88.0, 95.0, 144.0, 178.0, 144.0, 285.0, 225.0, 170.0, 194.0, 91.0, 110.0, 115.0, 89.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015067479, 0.0035338316, -0.0036008623, -0.008897186, 0.015907891, 0.00017169114, -0.048028305, -0.0028078866, 0.008390858, -0.026117124, 0.010924859, 0.0008176028, -0.011125821, -0.0075852606, 0.025357647, 0.0004229853, -0.005925107, 0.021704594, -0.0059834397, 0.0062050926, -0.0027223413, 0.004902608, -0.0037427188, 0.0017643777, 0.009132042, 0.023144383, -0.0118676275, -0.0063672937, 0.05401793, -0.0011615654, 0.111119166, 0.017878048, 0.004866253], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 156, "left_children": [1, -1, 3, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, -1, 27, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [0.15925357, 0.0, 0.20179307, 0.5450886, 0.5307102, 0.35251194, 1.0270469, 0.6727515, 0.0, 0.31833515, 0.6750477, 0.0, 0.0, 0.0, 0.4553318, 0.3697668, 0.0, 1.0661024, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5372877, 0.0, 1.3589418, 0.0, 0.0, 1.4016619, 0.0, 0.8451791, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 17, 17, 23, 23, 25, 25, 28, 28, 30, 30], "right_children": [2, -1, 4, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, -1, 28, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.0035338316, 0.88461536, 0.3846154, 1.0, 1.0, 1.0, 1.0, 0.008390858, 1.0, 1.0, 0.0008176028, -0.011125821, -0.0075852606, 1.0, 1.0, -0.005925107, 0.0, -0.0059834397, 0.0062050926, -0.0027223413, 0.004902608, -0.0037427188, 1.0, 0.009132042, 0.0, -0.0118676275, -0.0063672937, -0.3846154, -0.0011615654, -0.23076923, 0.017878048, 0.004866253], "split_indices": [1, 0, 1, 1, 121, 2, 12, 126, 0, 126, 74, 0, 0, 0, 17, 109, 0, 1, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 111.0, 1953.0, 1536.0, 417.0, 1247.0, 289.0, 327.0, 90.0, 362.0, 885.0, 153.0, 136.0, 91.0, 236.0, 201.0, 161.0, 768.0, 117.0, 139.0, 97.0, 88.0, 113.0, 597.0, 171.0, 507.0, 90.0, 133.0, 374.0, 174.0, 200.0, 96.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-8.615981e-05, 0.004368425, -0.0025565282, -0.021951793, 0.0050106663, 0.0062666433, -0.008506761, 0.011579012, -0.00769673, -0.006363862, 0.030733498, -0.000658894, 0.08250762, -0.0038247537, 0.06287651, 0.009190305, -0.007945251, 0.017075572, -0.00066693657, -0.0015474821, 0.013269456, 0.017651215, -0.0063179205, 0.0049275225, 0.010968986, -0.033999283, 0.028251441, -0.07347588, 0.0041006226, 0.047978193, -0.005147748, -0.0017876588, -0.012246928, 0.0011285896, 0.01279794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 157, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, -1, -1, 13, 15, 17, -1, 19, 21, -1, -1, -1, -1, -1, 23, -1, 25, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.22263756, 0.0, 0.2860504, 0.9742235, 0.7549202, 0.6465168, 0.0, 1.1266872, 0.0, 0.0, 0.6208324, 0.85909194, 1.5031129, 0.0, 1.0448347, 0.60251474, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0317135, 0.0, 0.7027344, 0.0, 0.8586792, 0.76123166, 0.5175594, 0.0, 1.1389457, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 21, 21, 23, 23, 25, 25, 26, 26, 27, 27, 29, 29], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, -1, -1, 14, 16, 18, -1, 20, 22, -1, -1, -1, -1, -1, 24, -1, 26, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.004368425, -0.26923078, 1.0, 1.0, 1.0, -0.008506761, 1.0, -0.00769673, -0.006363862, -0.46153846, 1.4615384, 1.0, -0.0038247537, 1.0, 4.0, -0.007945251, 0.017075572, -0.00066693657, -0.0015474821, 0.013269456, 1.2692307, -0.0063179205, 1.0, 0.010968986, 1.0, 0.7307692, 1.0, 0.0041006226, 0.26923078, -0.005147748, -0.0017876588, -0.012246928, 0.0011285896, 0.01279794], "split_indices": [1, 0, 1, 113, 41, 81, 0, 113, 0, 0, 1, 1, 93, 0, 109, 0, 0, 0, 0, 0, 0, 1, 0, 69, 0, 83, 1, 39, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 110.0, 1949.0, 547.0, 1402.0, 378.0, 169.0, 1298.0, 104.0, 98.0, 280.0, 1107.0, 191.0, 89.0, 191.0, 984.0, 123.0, 96.0, 95.0, 90.0, 101.0, 881.0, 103.0, 774.0, 107.0, 290.0, 484.0, 190.0, 100.0, 388.0, 96.0, 89.0, 101.0, 266.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "3.3844025E-9", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}