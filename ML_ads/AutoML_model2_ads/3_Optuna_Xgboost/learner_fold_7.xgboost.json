{"learner": {"attributes": {"best_iteration": "14", "best_score": "0.773952"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "65"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.0012107533, 0.14750831, -0.23140235, 0.101970896, 0.047809802, -0.26532215, -0.0011716158, -0.09702392, 0.17461981, -0.23414767, -0.045918837, -0.001896965, -0.016483355, 0.29978192, 0.07045805, -0.19164321, -0.035326842, 0.39042586, 0.008107213, 0.15524736, -0.015288939, -0.012147814, -0.22867475, 0.057669025, 0.019096158, 0.002557294, 0.028653264, -0.032742996, -0.17773785, -0.013980562, -0.021900473], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [70.511665, 19.14894, 5.9613686, 16.162653, 0.0, 4.1882706, 0.0, 1.582556, 10.677387, 3.022705, 0.0, 0.0, 0.0, 7.374794, 8.465052, 1.1432629, 0.0, 9.771263, 0.0, 5.515887, 0.0, 0.0, 1.448719, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2974143, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 19, 19, 22, 22, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.047809802, 1.0, -0.0011716158, 1.0, 1.0, 1.0, -0.045918837, -0.001896965, -0.016483355, 1.0, 1.0, 1.0, -0.035326842, 1.0, 0.008107213, 1.0, -0.015288939, -0.012147814, 1.0, 0.057669025, 0.019096158, 0.002557294, 0.028653264, -0.032742996, 1.0, -0.013980562, -0.021900473], "split_indices": [137, 125, 71, 17, 0, 40, 0, 53, 53, 116, 0, 0, 0, 106, 7, 97, 0, 126, 0, 15, 0, 0, 108, 0, 0, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1272.0, 800.0, 1118.0, 154.0, 693.0, 107.0, 299.0, 819.0, 597.0, 96.0, 139.0, 160.0, 372.0, 447.0, 440.0, 157.0, 263.0, 109.0, 324.0, 123.0, 152.0, 288.0, 136.0, 127.0, 163.0, 161.0, 98.0, 190.0, 99.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00638251, -0.16297388, 0.17856428, -0.23171718, -0.020387635, 0.1241741, 0.052290495, -0.2681166, -0.002808647, 0.05846353, -0.021268794, 0.040966596, 0.333857, -0.21288097, -0.34603024, 0.013702064, -0.0033662505, 0.12672882, -0.11555906, 0.017468262, 0.047164973, -0.013279702, -0.26901823, -0.030365685, -0.040388618, -0.010600994, 0.22289519, -0.027269099, 0.0012190447, -0.032620974, -0.022203946, 0.03424142, 0.013629759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [59.923977, 10.154715, 19.084599, 5.181015, 5.1099663, 15.353527, 0.0, 2.5520363, 0.0, 1.7296804, 0.0, 8.457112, 5.483267, 1.5600033, 0.60308075, 0.0, 0.0, 9.109327, 4.4763975, 0.0, 0.0, 0.0, 0.54810333, 0.0, 0.0, 0.0, 2.9808168, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, 1.0, 1.0, 0.052290495, 1.0, -0.002808647, 1.0, -0.021268794, 0.1923077, 1.0, 1.0, 1.0, 0.013702064, -0.0033662505, 1.0, 1.0, 0.017468262, 0.047164973, -0.013279702, 1.0, -0.030365685, -0.040388618, -0.010600994, 1.0, -0.027269099, 0.0012190447, -0.032620974, -0.022203946, 0.03424142, 0.013629759], "split_indices": [71, 2, 125, 1, 7, 113, 0, 23, 0, 93, 0, 1, 109, 122, 116, 0, 0, 5, 93, 0, 0, 0, 12, 0, 0, 0, 69, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1036.0, 1019.0, 699.0, 337.0, 880.0, 139.0, 593.0, 106.0, 239.0, 98.0, 630.0, 250.0, 347.0, 246.0, 129.0, 110.0, 407.0, 223.0, 116.0, 134.0, 143.0, 204.0, 142.0, 104.0, 119.0, 288.0, 100.0, 123.0, 92.0, 112.0, 121.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-6.4712185e-05, 0.11952431, -0.19261056, -0.055869345, 0.18868826, -0.2310351, -0.09466227, -0.022519369, -0.0014877921, 0.32692567, 0.07158976, -0.29134458, -0.17578512, -0.02279192, 0.0056362227, -0.0076566623, 0.006530646, 0.41231042, 0.009135522, 0.13085109, -0.0102901915, -0.033928182, -0.023374256, -0.0068254396, -0.24335907, 0.5778864, 0.017502828, 0.004170848, 0.21218222, -0.02927578, -0.019449724, 0.071729295, 0.04473781, 0.039112013, 0.0063629993], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, -1, -1, 25, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [47.779716, 15.527575, 2.9920769, 3.3333392, 14.860027, 1.902628, 4.5080147, 0.0, 1.3740644, 8.468044, 5.139283, 0.75382614, 2.165349, 0.0, 0.0, 0.0, 0.0, 12.140057, 0.0, 2.6897755, 0.0, 0.0, 0.0, 0.0, 0.44170856, 3.3112526, 0.0, 0.0, 5.1568336, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 19, 19, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, -1, -1, 26, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.022519369, 1.0, 1.0, 1.0, 1.0, 1.0, -0.02279192, 0.0056362227, -0.0076566623, 0.006530646, 1.0, 0.009135522, 1.0, -0.0102901915, -0.033928182, -0.023374256, -0.0068254396, 1.0, 1.0, 0.017502828, 0.004170848, 1.0, -0.02927578, -0.019449724, 0.071729295, 0.04473781, 0.039112013, 0.0063629993], "split_indices": [137, 17, 93, 71, 53, 111, 13, 0, 109, 106, 7, 17, 53, 0, 0, 0, 0, 126, 0, 13, 0, 0, 0, 0, 108, 121, 0, 0, 93, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1280.0, 795.0, 362.0, 918.0, 571.0, 224.0, 88.0, 274.0, 421.0, 497.0, 273.0, 298.0, 119.0, 105.0, 129.0, 145.0, 309.0, 112.0, 371.0, 126.0, 149.0, 124.0, 115.0, 183.0, 182.0, 127.0, 177.0, 194.0, 91.0, 92.0, 88.0, 94.0, 88.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056109596, 0.104294896, -0.18063085, -0.05252184, 0.16733418, -0.20935468, -0.00036299016, -0.11429115, 0.012424349, 0.23275036, -0.034538332, -0.16529295, -0.30291045, 0.0011844776, -0.17561692, 0.34063417, 0.06087462, -0.012681457, 0.009013274, -0.2402759, -0.11065851, -0.033639085, -0.025400644, -0.017983785, -0.017125366, 0.18802911, 0.5318007, -0.008714234, 0.03314433, -0.03140522, -0.016342554, -0.0006866066, -0.021679211, 0.008832223, 0.024854084, 0.04881986, 0.057267763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [39.6448, 12.51519, 4.0418987, 3.963478, 11.924772, 2.819603, 0.0, 2.0808187, 0.0, 12.646061, 2.5424235, 1.9049416, 0.3585701, 0.0, 0.003332615, 12.223476, 10.532824, 0.0, 0.0, 1.1112671, 2.9632673, 0.0, 0.0, 0.0, 0.0, 1.4057903, 0.33150864, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 0.23076923, 0.7307692, -0.00036299016, -0.30769232, 0.012424349, 1.0, 0.8076923, 1.0, 1.0, 0.0011844776, 0.23076923, 1.0, -0.1923077, -0.012681457, 0.009013274, 1.0, 1.0, -0.033639085, -0.025400644, -0.017983785, -0.017125366, -0.46153846, -0.34615386, -0.008714234, 0.03314433, -0.03140522, -0.016342554, -0.0006866066, -0.021679211, 0.008832223, 0.024854084, 0.04881986, 0.057267763], "split_indices": [137, 17, 1, 39, 1, 1, 0, 1, 0, 106, 1, 124, 39, 0, 1, 50, 1, 0, 0, 15, 80, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1266.0, 795.0, 363.0, 903.0, 684.0, 111.0, 269.0, 94.0, 682.0, 221.0, 465.0, 219.0, 88.0, 181.0, 419.0, 263.0, 127.0, 94.0, 196.0, 269.0, 130.0, 89.0, 92.0, 89.0, 233.0, 186.0, 170.0, 93.0, 100.0, 96.0, 136.0, 133.0, 88.0, 145.0, 90.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0005637665, 0.09849262, -0.15482646, 0.059260745, 0.037686404, -0.19420007, -0.054030012, 0.1184963, -0.082076356, -0.22089776, -0.0046149143, -0.015685719, 0.0059368154, 0.20647418, -0.0015458177, -0.02115997, 0.010343934, -0.19455211, -0.03342986, 0.09263887, 0.34919307, -0.120692566, 0.021739904, -0.0056684106, 0.008630908, -0.008537255, -0.24491167, 0.023367414, -0.0075377016, 0.031777762, 0.038495317, -0.023390831, -0.0027625356, -0.01896938, -0.030220538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [31.530012, 13.88063, 3.1789436, 9.32661, 0.0, 2.2767086, 2.6235933, 8.290425, 3.9383235, 1.4579563, 0.0, 0.0, 0.0, 7.35964, 8.66074, 0.0, 0.97762483, 2.1773005, 0.0, 5.971435, 0.22580719, 2.265384, 0.0, 0.0, 0.0, 0.0, 0.8573437, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.1923077, 0.037686404, 2.7307692, 1.0, 1.0, 1.0, 1.0, -0.0046149143, -0.015685719, 0.0059368154, 1.0, -0.1923077, -0.02115997, 1.0, 0.0, -0.03342986, 1.0, 1.0, 1.0, 0.021739904, -0.0056684106, 0.008630908, -0.008537255, 1.0, 0.023367414, -0.0075377016, 0.031777762, 0.038495317, -0.023390831, -0.0027625356, -0.01896938, -0.030220538], "split_indices": [137, 125, 93, 1, 0, 1, 13, 106, 69, 0, 0, 0, 0, 50, 1, 0, 50, 1, 0, 69, 61, 69, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1271.0, 801.0, 1114.0, 157.0, 576.0, 225.0, 785.0, 329.0, 488.0, 88.0, 118.0, 107.0, 453.0, 332.0, 137.0, 192.0, 396.0, 92.0, 252.0, 201.0, 215.0, 117.0, 102.0, 90.0, 125.0, 271.0, 137.0, 115.0, 107.0, 94.0, 97.0, 118.0, 138.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0054951776, -0.117012344, 0.13147894, -0.1654536, -0.016152224, 0.031306613, 0.24152452, -0.13546553, -0.028030587, 0.048606437, -0.017003423, -0.11092823, 0.21849014, 0.16301794, 0.33920547, -0.17151123, -0.0012966454, 0.012546142, -0.0037770371, -0.056700412, -0.022665149, 0.0083672255, 0.032140456, 0.022732666, 0.0034400483, 0.04117019, 0.025279561, -0.1411284, -0.028797869, 0.0046317345, -0.014239663, -0.02271366, -0.08912344, -0.0016179396, -0.016993396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [31.994595, 5.134967, 11.266039, 2.4453812, 3.3981311, 14.243852, 3.7346077, 2.485962, 0.0, 1.5932374, 0.0, 1.9077265, 3.2050543, 2.2332287, 1.3593712, 1.5392942, 0.0, 0.0, 0.0, 1.8274442, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5431333, 0.0, 0.0, 0.0, 0.0, 1.2673486, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.028030587, 1.0, -0.017003423, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0012966454, 0.012546142, -0.0037770371, 1.0, -0.022665149, 0.0083672255, 0.032140456, 0.022732666, 0.0034400483, 0.04117019, 0.025279561, 1.0, -0.028797869, 0.0046317345, -0.014239663, -0.02271366, 1.0, -0.0016179396, -0.016993396], "split_indices": [71, 2, 39, 58, 7, 42, 50, 93, 0, 93, 0, 106, 13, 108, 121, 0, 0, 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 106, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1051.0, 1022.0, 710.0, 341.0, 535.0, 487.0, 563.0, 147.0, 240.0, 101.0, 304.0, 231.0, 270.0, 217.0, 435.0, 128.0, 127.0, 113.0, 207.0, 97.0, 100.0, 131.0, 180.0, 90.0, 118.0, 99.0, 345.0, 90.0, 94.0, 113.0, 130.0, 215.0, 113.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025077916, 0.078087874, -0.11893631, 0.045413554, 0.0313343, -0.15191258, -0.034942925, -0.099382535, 0.098890394, -0.12896939, -0.025799641, -0.012193428, 0.005936543, -0.14833188, -0.0023469543, 0.15672748, -0.03725285, -0.0015972423, -0.17026943, -0.02276582, -0.0066361316, 0.08088839, 0.041458037, -0.013118672, 0.006225334, -0.025390962, -0.1041455, -0.0061840005, 0.035349827, -0.0151459025, -0.005323199, 0.05048885, -0.012694487, -0.005033008, 0.01772612], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [18.926594, 9.769925, 2.1909008, 8.641453, 0.0, 1.3824558, 1.8294947, 1.1184828, 6.4174156, 2.1793857, 0.0, 0.0, 0.0, 1.1899424, 0.0, 11.185644, 2.2713215, 0.0, 1.8914709, 0.0, 0.0, 10.491663, 0.0, 0.0, 0.0, 0.0, 0.46009874, 2.2926943, 0.0, 0.0, 0.0, 2.9140801, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 18, 18, 21, 21, 26, 26, 27, 27, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.0313343, 1.0, 1.0, 1.0, 0.115384616, 0.0, -0.025799641, -0.012193428, 0.005936543, 1.0, -0.0023469543, -0.115384616, 1.0, -0.0015972423, 1.0, -0.02276582, -0.0066361316, 1.0, 0.041458037, -0.013118672, 0.006225334, -0.025390962, 1.3076923, 1.0, 0.035349827, -0.0151459025, -0.005323199, 1.0, -0.012694487, -0.005033008, 0.01772612], "split_indices": [137, 125, 93, 17, 0, 0, 13, 124, 1, 1, 0, 0, 0, 93, 0, 1, 122, 0, 111, 0, 0, 61, 0, 0, 0, 0, 1, 7, 0, 0, 0, 111, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1271.0, 791.0, 1116.0, 155.0, 568.0, 223.0, 301.0, 815.0, 467.0, 101.0, 116.0, 107.0, 183.0, 118.0, 572.0, 243.0, 125.0, 342.0, 93.0, 90.0, 442.0, 130.0, 125.0, 118.0, 151.0, 191.0, 335.0, 107.0, 99.0, 92.0, 228.0, 107.0, 127.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00048143373, -0.09998283, 0.10284693, -0.075563766, -0.026889658, 0.065231636, 0.033963386, -0.04336161, -0.20011283, 0.11742507, -0.043382164, -0.065614305, 0.009869597, -0.023736082, -0.016166326, 0.24369918, 0.034657158, -0.024888476, 0.050411314, 0.005642297, -0.11318139, -0.00266352, 0.046756987, -0.06802795, 0.014634974, -0.0017978821, 0.012093867, 0.0102121765, -0.013486174, -0.07006028, -0.021999512, 0.0039388044, -0.016448317, -0.014775931, -0.0017456573], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, 21, 23, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [21.251482, 4.3433228, 9.031506, 3.689887, 0.0, 4.960311, 0.0, 2.310811, 0.270679, 6.1768045, 5.474045, 2.1421442, 0.0, 0.0, 0.0, 14.161667, 4.094493, 0.0, 0.9405586, 3.4296057, 1.7456455, 0.0, 0.0, 1.9271154, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1035589, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, 22, 24, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.026889658, 0.115384616, 0.033963386, 1.0, 0.42307693, 1.0, 1.0, 1.0, 0.009869597, -0.023736082, -0.016166326, -0.3846154, 1.0, -0.024888476, 1.0, 1.0, 1.0, -0.00266352, 0.046756987, 1.0, 0.014634974, -0.0017978821, 0.012093867, 0.0102121765, -0.013486174, 0.15384616, -0.021999512, 0.0039388044, -0.016448317, -0.014775931, -0.0017456573], "split_indices": [71, 40, 125, 116, 0, 1, 0, 121, 1, 69, 69, 122, 0, 0, 0, 1, 109, 0, 12, 113, 127, 0, 0, 121, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1053.0, 1014.0, 920.0, 133.0, 875.0, 139.0, 731.0, 189.0, 591.0, 284.0, 632.0, 99.0, 96.0, 93.0, 234.0, 357.0, 89.0, 195.0, 253.0, 379.0, 106.0, 128.0, 186.0, 171.0, 99.0, 96.0, 150.0, 103.0, 270.0, 109.0, 88.0, 98.0, 109.0, 161.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005075366, -0.085034795, 0.09820103, -0.045460247, -0.16185597, 0.06400797, 0.031215189, 0.0010572217, -0.13382399, -0.20140707, -0.004958185, -0.013033478, 0.14958896, 0.08259988, -0.05869922, -0.00786034, -0.017154505, -0.014775968, -0.023010217, 0.075860254, -0.02345944, 0.055504445, 0.25781047, 0.021839228, -0.004232909, 0.006856292, -0.013599343, -0.02130254, 0.021374572, 0.013962211, -0.0043457476, 0.03859128, 0.014818444, 0.012298834, -0.015273585], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.33698, 3.192171, 7.432684, 2.8485472, 1.5852804, 5.775717, 0.0, 2.2122056, 0.49783134, 0.40640545, 0.0, 9.07957, 4.2255154, 3.2571669, 2.5771961, 0.0, 0.0, 0.0, 0.0, 4.4077244, 0.0, 1.8480273, 2.7103653, 0.0, 0.0, 0.0, 0.0, 3.660173, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 19, 19, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.4230769, 1.0, 0.031215189, 1.0, -0.1923077, 0.07692308, -0.004958185, 0.1923077, -0.07692308, 0.15384616, 1.0, -0.00786034, -0.017154505, -0.014775968, -0.023010217, 1.0, -0.02345944, 1.0, 0.3846154, 0.021839228, -0.004232909, 0.006856292, -0.013599343, 1.0, 0.021374572, 0.013962211, -0.0043457476, 0.03859128, 0.014818444, 0.012298834, -0.015273585], "split_indices": [71, 23, 125, 80, 1, 39, 0, 106, 1, 1, 0, 1, 1, 1, 111, 0, 0, 0, 0, 42, 0, 59, 1, 0, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1050.0, 1016.0, 693.0, 357.0, 876.0, 140.0, 454.0, 239.0, 264.0, 93.0, 461.0, 415.0, 192.0, 262.0, 97.0, 142.0, 92.0, 172.0, 329.0, 132.0, 222.0, 193.0, 92.0, 100.0, 99.0, 163.0, 193.0, 136.0, 120.0, 102.0, 89.0, 104.0, 92.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025925522, -0.07832444, 0.075443245, -0.056864627, -0.023019383, -0.0035356372, 0.1607082, -0.02548485, -0.1815356, 0.054557092, -0.021501359, 0.11127751, 0.02920315, -0.004129468, -0.012039764, -0.0228828, -0.013372913, -0.007967257, 0.14050654, 0.02557961, 0.02345416, 0.019198073, -0.038737155, 0.029488912, -0.009183158, 0.009244284, -0.005853861, -0.109692425, 0.010132685, -0.016032275, -0.0056054336, -0.040810738, 0.012113575, 0.00803836, -0.018650183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, -1, -1, -1, -1, 23, 25, -1, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [12.227371, 3.422042, 6.8620806, 3.5991755, 0.0, 6.498941, 3.1807842, 1.4897703, 0.41826153, 4.7878404, 0.0, 3.7605958, 0.0, 4.072152, 0.0, 0.0, 0.0, 0.0, 9.074846, 1.1811274, 0.0, 0.0, 1.7684627, 0.0, 0.0, 0.0, 0.0, 0.5648675, 1.7077729, 0.0, 0.0, 3.6549861, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 18, 18, 19, 19, 22, 22, 27, 27, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, -1, -1, -1, -1, 24, 26, -1, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.023019383, 0.46153846, 1.0, 1.0, 0.42307693, 1.0, -0.021501359, 0.03846154, 0.02920315, -0.34615386, -0.012039764, -0.0228828, -0.013372913, -0.007967257, 1.0, -0.30769232, 0.02345416, 0.019198073, 1.0, 0.029488912, -0.009183158, 0.009244284, -0.005853861, 0.53846157, 1.0, -0.016032275, -0.0056054336, 0.5769231, 0.012113575, 0.00803836, -0.018650183], "split_indices": [71, 40, 39, 116, 0, 1, 113, 7, 1, 93, 0, 1, 0, 1, 0, 0, 0, 0, 126, 1, 0, 0, 111, 0, 0, 0, 0, 1, 81, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1050.0, 1019.0, 920.0, 130.0, 529.0, 490.0, 735.0, 185.0, 415.0, 114.0, 356.0, 134.0, 600.0, 135.0, 93.0, 92.0, 162.0, 253.0, 210.0, 146.0, 90.0, 510.0, 152.0, 101.0, 117.0, 93.0, 208.0, 302.0, 107.0, 101.0, 207.0, 95.0, 113.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028427641, 0.05652344, -0.09772401, 0.03359359, 0.022011904, -0.13482401, -0.054118805, 0.009269459, 0.027999703, -0.16473252, -0.093450546, 0.008838843, -0.10789513, -0.057881556, 0.08066016, -0.013079307, -0.021284087, -0.018028477, -0.00085247384, -0.019166542, -0.005712523, -0.030554045, -0.01690488, 0.13161363, -0.006384065, -0.11226524, 0.04697656, 0.0750331, 0.027639318, -0.01704061, -0.005525328, -0.0056192926, 0.013020575, -0.0034397498, 0.014082573], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1, -1, 25, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [11.620341, 4.7602997, 1.2844954, 6.6708198, 0.0, 0.53085184, 2.797183, 4.8562794, 0.0, 0.4065585, 1.3274035, 0.0, 1.1270466, 1.5857956, 3.6151428, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.6544144, 0.0, 2.9735875, 0.0, 0.676203, 1.8461435, 1.8791293, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 21, 21, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1, -1, 26, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 5.0, 0.022011904, 1.0, 0.0, 1.0, 0.027999703, 1.0, 1.0, 0.008838843, 1.0, 1.0, 1.0, -0.013079307, -0.021284087, -0.018028477, -0.00085247384, -0.019166542, -0.005712523, 1.0, -0.01690488, 1.0, -0.006384065, 1.0, 1.0, 1.0, 0.027639318, -0.01704061, -0.005525328, -0.0056192926, 0.013020575, -0.0034397498, 0.014082573], "split_indices": [137, 125, 13, 0, 0, 115, 0, 124, 0, 106, 83, 0, 69, 0, 115, 0, 0, 0, 0, 0, 0, 39, 0, 0, 0, 59, 71, 50, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1269.0, 794.0, 1113.0, 156.0, 429.0, 365.0, 1013.0, 100.0, 249.0, 180.0, 100.0, 265.0, 522.0, 491.0, 146.0, 103.0, 89.0, 91.0, 100.0, 165.0, 419.0, 103.0, 363.0, 128.0, 204.0, 215.0, 261.0, 102.0, 101.0, 103.0, 96.0, 119.0, 98.0, 163.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033058135, 0.048356634, -0.08514796, 0.023595491, 0.023037562, -0.105665416, 0.004416842, -0.069696, 0.05705254, -0.07721834, -0.17396654, -0.015927536, -0.028041577, 0.097403914, -0.04911638, -0.11192847, -0.000117571086, -0.014100559, -0.021259816, -0.010501842, 0.0032440193, 0.035392888, 0.028512433, -0.018783327, 0.009083902, -0.06662794, -0.019129796, 0.14460094, -0.10983667, -0.0006664734, -0.011259971, 0.004542086, 0.029733825, -0.008071351, -0.013805908], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [8.697337, 5.683324, 2.1119814, 3.4645963, 0.0, 1.3348184, 0.0, 1.0932926, 3.5000784, 1.2801335, 0.25721312, 0.0, 0.93113923, 6.891314, 4.3681903, 1.1972938, 0.0, 0.0, 0.0, 0.0, 0.0, 7.057806, 0.0, 0.0, 0.0, 0.5844021, 0.0, 3.8477178, 0.15698719, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 21, 21, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 0.023037562, 0.8076923, 0.004416842, 1.0, 0.15384616, 1.0, 1.2692307, -0.015927536, 1.0, -0.115384616, 1.0, 1.0, -0.000117571086, -0.014100559, -0.021259816, -0.010501842, 0.0032440193, 1.0, 0.028512433, -0.018783327, 0.009083902, 1.0, -0.019129796, 1.0, -0.34615386, -0.0006664734, -0.011259971, 0.004542086, 0.029733825, -0.008071351, -0.013805908], "split_indices": [137, 125, 1, 17, 0, 1, 0, 69, 1, 83, 1, 0, 93, 1, 69, 126, 0, 0, 0, 0, 0, 122, 0, 0, 0, 106, 0, 61, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1261.0, 796.0, 1110.0, 151.0, 687.0, 109.0, 293.0, 817.0, 485.0, 202.0, 93.0, 200.0, 592.0, 225.0, 333.0, 152.0, 109.0, 93.0, 88.0, 112.0, 445.0, 147.0, 113.0, 112.0, 212.0, 121.0, 254.0, 191.0, 92.0, 120.0, 154.0, 100.0, 94.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0095140245, -0.07733815, 0.059772972, -0.060863275, -0.01929157, -0.0032411148, 0.12901068, -0.025740152, -0.13042082, 0.051399995, -0.019175297, 0.089580715, 0.026067857, 0.029911006, -0.06784147, -0.017007811, -0.008214237, 0.11443767, -0.010124493, 0.03839222, 0.017916055, -0.0044835843, 0.017193004, -0.10483865, -0.0017413797, -0.0027591747, 0.020619826, -0.008729023, 0.015010998, -0.01575425, -0.0039699026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [9.690019, 1.9840989, 4.45021, 2.2280872, 0.0, 5.5004663, 2.5231457, 1.4198506, 0.58586407, 3.9836662, 0.0, 1.7149603, 0.0, 2.7706397, 0.6436597, 0.0, 0.0, 3.8185825, 0.0, 3.3417487, 0.0, 0.0, 0.0, 0.6831882, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 19, 19, 23, 23], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.01929157, 0.46153846, 1.0, 1.0, 1.0, 1.0, -0.019175297, 0.03846154, 0.026067857, 1.0, 0.15384616, -0.017007811, -0.008214237, -0.42307693, -0.010124493, 1.0, 0.017916055, -0.0044835843, 0.017193004, 1.0, -0.0017413797, -0.0027591747, 0.020619826, -0.008729023, 0.015010998, -0.01575425, -0.0039699026], "split_indices": [71, 40, 39, 23, 0, 1, 58, 122, 13, 106, 0, 1, 0, 74, 1, 0, 0, 1, 0, 124, 0, 0, 0, 81, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1042.0, 1020.0, 912.0, 130.0, 534.0, 486.0, 606.0, 306.0, 414.0, 120.0, 374.0, 112.0, 261.0, 345.0, 168.0, 138.0, 293.0, 121.0, 238.0, 136.0, 171.0, 90.0, 199.0, 146.0, 115.0, 178.0, 112.0, 126.0, 110.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0021848409, 0.013260408, -0.018600347, 0.050739612, -0.0487636, 0.100962445, 0.005975779, -0.06851116, 0.007539184, 0.18728644, -0.03692157, -0.062315423, 0.12643796, -0.09191375, 0.004697076, 0.03183469, 0.111809626, 0.0034349218, -0.014463767, -0.14771187, 0.025184369, -0.0022782052, 0.024673939, -0.059764422, -0.021269094, 0.0045059673, 0.018479785, -0.022383122, -0.0060606203, -0.005812664, 0.010294135, -0.09512151, 0.006513831, -0.01670297, -0.0012260393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [4.3124003, 4.5422883, 0.0, 2.7382667, 1.8045006, 6.83215, 5.2978706, 1.7161362, 0.0, 3.4918842, 1.69662, 3.071063, 4.1826725, 2.0501733, 0.0, 0.0, 1.0913188, 0.0, 0.0, 1.379128, 1.3150365, 0.0, 0.0, 1.8415546, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9364777, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 19, 19, 20, 20, 23, 23, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.018600347, 1.0, 2.7307692, 1.0, 1.0, 1.0, 0.007539184, -0.30769232, -0.03846154, 1.0, 1.0, 0.96153843, 0.004697076, 0.03183469, 1.0, 0.0034349218, -0.014463767, -0.15384616, 1.0, -0.0022782052, 0.024673939, 0.5, -0.021269094, 0.0045059673, 0.018479785, -0.022383122, -0.0060606203, -0.005812664, 0.010294135, 1.0, 0.006513831, -0.01670297, -0.0012260393], "split_indices": [43, 137, 0, 53, 1, 97, 97, 62, 0, 1, 1, 122, 13, 1, 0, 0, 111, 0, 0, 1, 69, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 109, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1954.0, 115.0, 1218.0, 736.0, 574.0, 644.0, 635.0, 101.0, 353.0, 221.0, 411.0, 233.0, 528.0, 107.0, 129.0, 224.0, 133.0, 88.0, 208.0, 203.0, 104.0, 129.0, 417.0, 111.0, 117.0, 107.0, 111.0, 97.0, 98.0, 105.0, 325.0, 92.0, 174.0, 151.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0029638428, -0.047897752, 0.055119377, -0.059347745, 0.0065647946, 0.031580053, 0.020465335, -0.09939399, -0.006408542, 0.00020858581, 0.1394096, 0.0036643413, -0.1397496, 0.01023513, -0.0634165, -0.06803502, 0.045538288, 0.0009855869, 0.025307462, -0.18047771, -0.0042068353, -0.014150411, 0.0054809563, 0.00418187, -0.014679809, -0.011462889, 0.09044498, -0.024120782, -0.14789851, 0.01926387, 0.008345523, -0.01811198, -0.01173349, -0.013743437, 0.015412544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [5.4911175, 1.3625019, 3.5973666, 2.0182557, 0.0, 2.9869864, 0.0, 2.9755106, 2.5420723, 2.1159284, 2.930419, 0.0, 1.66296, 0.0, 2.4834056, 2.3621097, 2.9561498, 0.0, 0.0, 0.5836668, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.693205, 0.0, 0.19494867, 0.0, 3.782817, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 26, 26, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 3.6153846, 1.0, 1.0, 0.0065647946, 2.0, 0.020465335, -0.30769232, 1.0, 1.0, -0.26923078, 0.0036643413, 0.9230769, 0.01023513, 1.0, -0.23076923, 1.0, 0.0009855869, 0.025307462, 1.0, -0.0042068353, -0.014150411, 0.0054809563, 0.00418187, -0.014679809, -0.011462889, 1.0, -0.024120782, 1.0, 0.01926387, 1.0, -0.01811198, -0.01173349, -0.013743437, 0.015412544], "split_indices": [71, 1, 125, 83, 0, 0, 0, 1, 122, 93, 1, 0, 1, 0, 62, 1, 89, 0, 0, 69, 0, 0, 0, 0, 0, 0, 126, 0, 17, 0, 12, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1048.0, 1022.0, 952.0, 96.0, 883.0, 139.0, 542.0, 410.0, 684.0, 199.0, 124.0, 418.0, 141.0, 269.0, 273.0, 411.0, 93.0, 106.0, 295.0, 123.0, 162.0, 107.0, 114.0, 159.0, 90.0, 321.0, 103.0, 192.0, 143.0, 178.0, 92.0, 100.0, 89.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0015829763, -0.010444935, 0.017146628, 0.04628291, -0.059978835, 0.13608979, -0.08225469, -0.116025075, 0.0031020097, 0.08555926, 0.03650562, -0.015383261, -0.03986088, -0.19485283, -0.077999756, 0.014127572, -0.035907447, -0.009285069, 0.1738446, -0.016026681, 0.0062388587, -0.016897773, -0.022131596, 0.0007217352, -0.14617346, -0.01449983, 0.057675373, 0.004699083, 0.032043114, -0.010431469, -0.018603893, -0.0055780895, 0.015852543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [4.2338033, 5.4372563, 0.0, 10.412292, 3.652114, 6.1435604, 1.1257846, 1.6396041, 2.6195803, 6.851678, 0.0, 0.0, 2.8685668, 0.12188244, 2.1437304, 0.0, 3.8692222, 0.0, 5.4111614, 0.0, 0.0, 0.0, 0.0, 0.0, 0.3420868, 0.0, 2.3341827, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 16, 16, 18, 18, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, -0.03846154, 0.017146628, 1.0, 1.0, -0.1923077, 1.0, 0.34615386, 0.0, 1.0, 0.03650562, -0.015383261, 1.0, 1.0, 1.0, 0.014127572, 1.0, -0.009285069, 1.0, -0.016026681, 0.0062388587, -0.016897773, -0.022131596, 0.0007217352, 1.0, -0.01449983, 1.0, 0.004699083, 0.032043114, -0.010431469, -0.018603893, -0.0055780895, 0.015852543], "split_indices": [0, 1, 0, 122, 39, 1, 5, 1, 0, 109, 0, 0, 115, 13, 23, 0, 122, 0, 61, 0, 0, 0, 0, 0, 116, 0, 111, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1935.0, 137.0, 902.0, 1033.0, 531.0, 371.0, 547.0, 486.0, 435.0, 96.0, 138.0, 233.0, 178.0, 369.0, 107.0, 379.0, 144.0, 291.0, 107.0, 126.0, 90.0, 88.0, 164.0, 205.0, 175.0, 204.0, 156.0, 135.0, 100.0, 105.0, 96.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.000625101, 0.012825114, -0.024300165, -0.03826778, 0.06491983, -0.05258014, 0.007957512, 0.10358347, -0.04276444, -0.019707654, -0.12776981, 0.21048325, 0.029008145, -0.023653513, 0.005873448, 0.018713389, -0.091343164, -0.01718315, -0.008370811, 0.0003542997, 0.039726697, -0.08914429, 0.13641946, 0.013854845, -0.05503125, -0.012789878, -0.004966973, -0.01715726, -0.00050507305, 0.0014741849, 0.024751639, 0.008375816, -0.013321011], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.107972, 5.208888, 0.0, 1.666371, 4.0343995, 2.177542, 0.0, 5.684096, 5.0348845, 1.6871669, 0.52030325, 11.325348, 5.330182, 0.0, 0.0, 3.5260406, 0.3260069, 0.0, 0.0, 0.0, 0.0, 1.3863375, 2.9739614, 0.0, 2.6800485, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.024300165, 3.1538463, 0.23076923, 1.0, 0.007957512, 1.0, 1.0, 1.0, 1.0, -0.3846154, 1.0, -0.023653513, 0.005873448, 1.0, 1.0, -0.01718315, -0.008370811, 0.0003542997, 0.039726697, 1.0, 1.0, 0.013854845, 1.0, -0.012789878, -0.004966973, -0.01715726, -0.00050507305, 0.0014741849, 0.024751639, 0.008375816, -0.013321011], "split_indices": [117, 71, 0, 1, 1, 23, 0, 69, 69, 80, 80, 1, 109, 0, 0, 122, 12, 0, 0, 0, 0, 122, 108, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1957.0, 98.0, 988.0, 969.0, 881.0, 107.0, 713.0, 256.0, 613.0, 268.0, 293.0, 420.0, 88.0, 168.0, 399.0, 214.0, 134.0, 134.0, 139.0, 154.0, 200.0, 220.0, 152.0, 247.0, 114.0, 100.0, 101.0, 99.0, 105.0, 115.0, 89.0, 158.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0033509263, 0.046070848, -0.03429067, 0.021677066, 0.028196672, -0.0036992684, -0.020436428, 0.058761183, -0.089575276, -0.07177417, 0.0724026, 0.022068653, 0.032864507, -0.018896563, -0.0016793335, -0.014599899, -0.036766637, 0.15175405, -0.0074013607, 0.09808581, -0.05400812, 0.009640415, -0.097562015, 0.066296816, 0.032356806, -0.010143278, 0.1768657, 0.0037887797, -0.014590407, -0.0027850852, -0.01423763, -0.0011216254, 0.014797728, 0.029948164, 0.00709255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, -1, 25, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.3334796, 5.587514, 5.7334743, 3.6306117, 0.0, 4.8387055, 0.0, 2.7675958, 1.5914404, 1.2810245, 5.1236873, 0.0, 3.2239237, 0.0, 0.0, 0.0, 2.7122164, 4.199265, 0.0, 5.1083674, 2.0605466, 0.0, 0.71853256, 1.2092795, 0.0, 0.0, 3.026658, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 16, 16, 17, 17, 19, 19, 20, 20, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, -1, 26, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.03846154, -0.115384616, 1.0, 1.0, 0.028196672, 1.0, -0.020436428, -0.5769231, -0.3846154, 0.34615386, 1.0, 0.022068653, 1.0, -0.018896563, -0.0016793335, -0.014599899, 1.0, 1.0, -0.0074013607, -0.5, 1.0, 0.009640415, 1.0769231, 0.88461536, 0.032356806, -0.010143278, -0.34615386, 0.0037887797, -0.014590407, -0.0027850852, -0.01423763, -0.0011216254, 0.014797728, 0.029948164, 0.00709255], "split_indices": [1, 1, 64, 7, 0, 39, 0, 1, 1, 1, 109, 0, 59, 0, 0, 0, 124, 71, 0, 1, 93, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 971.0, 1102.0, 880.0, 91.0, 934.0, 168.0, 660.0, 220.0, 493.0, 441.0, 91.0, 569.0, 93.0, 127.0, 158.0, 335.0, 286.0, 155.0, 325.0, 244.0, 105.0, 230.0, 191.0, 95.0, 92.0, 233.0, 122.0, 122.0, 90.0, 140.0, 98.0, 93.0, 108.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035167085, -0.016073478, 0.12609932, -0.028609743, 0.01563981, 0.009506349, 0.01588787, 0.010526161, -0.059441686, 0.08400164, -0.074378826, -0.040341493, -0.022847835, -0.0060267434, 0.13586308, -0.029349247, -0.014431837, -0.019101506, -0.017317845, 0.032188322, 2.8907642e-05, 0.008907113, -0.017288914, 0.0017548591, -0.01265791, -0.02580654, 0.012472114, 0.021203507, -0.08456911, 0.018538153, -0.051492218, -0.01656634, -0.0035011475, 0.0032153667, -0.013767286], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, -1, -1, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, -1, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [3.3723013, 4.0842996, 0.18617153, 2.124886, 0.0, 0.0, 0.0, 4.8410254, 3.1802025, 3.112513, 1.1337649, 2.4969873, 0.0, 0.0, 7.7171655, 3.7225728, 0.0, 1.7103348, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1656494, 0.0, 1.4419895, 0.0, 3.461162, 0.9323704, 0.0, 1.44894, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 17, 17, 23, 23, 25, 25, 27, 27, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, -1, -1, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, -1, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, -0.3846154, -0.03846154, 0.01563981, 0.009506349, 0.01588787, 1.0, 2.0, 1.0, 1.0, 1.0, -0.022847835, -0.0060267434, 1.0, 1.0, -0.014431837, 1.0, -0.017317845, 0.032188322, 2.8907642e-05, 0.008907113, -0.017288914, 1.0, -0.01265791, 1.0, 0.012472114, 0.46153846, 0.53846157, 0.018538153, 1.0, -0.01656634, -0.0035011475, 0.0032153667, -0.013767286], "split_indices": [125, 0, 1, 1, 0, 0, 0, 122, 0, 17, 108, 64, 0, 0, 39, 126, 0, 58, 0, 0, 0, 0, 0, 105, 0, 13, 0, 1, 1, 0, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1889.0, 183.0, 1761.0, 128.0, 94.0, 89.0, 776.0, 985.0, 416.0, 360.0, 885.0, 100.0, 110.0, 306.0, 219.0, 141.0, 763.0, 122.0, 129.0, 177.0, 120.0, 99.0, 639.0, 124.0, 522.0, 117.0, 290.0, 232.0, 89.0, 201.0, 88.0, 144.0, 102.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00024389446, -0.008647514, 0.012648883, 0.032127168, -0.04401975, 0.1027206, -0.06935088, -0.02514513, -0.020585211, 0.013603716, 0.23916748, -0.11777046, -0.023493163, 0.0176159, -0.08523271, -0.10094521, 0.016276456, 0.016480643, 0.03233927, -0.008048132, -0.015547854, -0.007900248, 0.0032606653, 0.043151356, -0.011383363, -0.14037497, 0.0008084943, -0.011836442, -0.008253625, 0.0056374935, 0.01690392, -0.020040741, -0.004585581, 0.055968504, -0.0078119435, -0.002717415, 0.0161629], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [2.3201978, 2.7850654, 0.0, 6.425824, 3.1583772, 6.432492, 0.81711113, 2.379271, 0.0, 5.4675894, 1.3089819, 0.25169182, 0.5885581, 1.8159333, 1.981113, 0.058040738, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1393096, 0.0, 1.3731589, 0.0, 0.0, 0.0, 1.4712342, 0.0, 0.0, 0.0, 1.9151075, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 23, 23, 25, 25, 29, 29, 33, 33], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [5.0, -0.03846154, 0.012648883, 1.0, 2.0, 1.0, 1.0, 1.0, -0.020585211, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.42307693, 0.016276456, 0.016480643, 0.03233927, -0.008048132, -0.015547854, -0.007900248, 0.0032606653, 1.2307693, -0.011383363, 1.0, 0.0008084943, -0.011836442, -0.008253625, 0.46153846, 0.01690392, -0.020040741, -0.004585581, 1.0, -0.0078119435, -0.002717415, 0.0161629], "split_indices": [0, 1, 0, 122, 0, 50, 39, 23, 0, 97, 61, 71, 93, 113, 50, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 31, 0, 0, 0, 1, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1931.0, 136.0, 897.0, 1034.0, 529.0, 368.0, 926.0, 108.0, 320.0, 209.0, 179.0, 189.0, 541.0, 385.0, 181.0, 139.0, 111.0, 98.0, 90.0, 89.0, 95.0, 94.0, 453.0, 88.0, 242.0, 143.0, 93.0, 88.0, 349.0, 104.0, 148.0, 94.0, 218.0, 131.0, 122.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0011772722, 0.009431877, -0.01596649, 0.018160976, -0.013660356, 0.030668832, -0.08663967, 0.019004721, 0.014070761, -0.002625448, -0.014347278, 0.0063504516, 0.017432746, -0.02411389, 0.056832556, 0.008226299, -0.12626536, 0.13358518, -0.04060991, -0.034805182, 0.06616513, -0.0075304, -0.019178709, 0.004708951, 0.020593692, -0.011081714, 0.0024877486, 0.0045688185, -0.065878004, 0.0009646043, 0.0129534975, -0.020444177, 0.0002642532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [2.7469873, 2.5087235, 0.0, 2.4342139, 0.0, 2.129334, 0.6795112, 2.9482427, 0.0, 0.0, 0.0, 2.133073, 0.0, 2.8576124, 3.9040203, 1.6380281, 0.6945269, 1.8273678, 1.0574689, 0.9429358, 1.0028495, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5824947, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01596649, 1.0, -0.013660356, 1.0, 1.0, 5.0, 0.014070761, -0.002625448, -0.014347278, 1.0, 0.017432746, 1.0, 1.0, 1.0, 1.0, 1.0, 0.53846157, 0.0, 1.0, -0.0075304, -0.019178709, 0.004708951, 0.020593692, -0.011081714, 0.0024877486, 0.0045688185, 1.0, 0.0009646043, 0.0129534975, -0.020444177, 0.0002642532], "split_indices": [117, 43, 0, 40, 0, 125, 111, 0, 0, 0, 0, 50, 0, 23, 97, 124, 12, 13, 1, 0, 97, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1968.0, 101.0, 1857.0, 111.0, 1659.0, 198.0, 1500.0, 159.0, 96.0, 102.0, 1387.0, 113.0, 865.0, 522.0, 657.0, 208.0, 292.0, 230.0, 377.0, 280.0, 117.0, 91.0, 133.0, 159.0, 111.0, 119.0, 105.0, 272.0, 148.0, 132.0, 90.0, 182.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.001409144, -0.02571977, 0.033196848, -0.010895122, -0.016593043, -0.052592833, 0.06088244, 0.009268687, -0.013648688, -0.020338038, 0.008939875, 0.19332536, 0.0069242087, -0.020737564, 0.013227679, 0.031385433, 0.007620757, -0.01517911, 0.052784167, 0.03482928, -0.061587457, -0.04629275, 0.15908545, 0.015931556, -0.04495196, 0.0032340095, -0.09207542, -0.011672873, 0.0034415114, 0.0311544, 0.0027349414, -0.010351558, 0.0016939108, -0.002122363, -0.12820281, -0.017402792, -0.008052614], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, 25, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [1.7885468, 2.325923, 2.2682571, 2.5627973, 0.0, 4.988662, 5.15969, 3.2185628, 0.0, 0.0, 0.0, 2.9502606, 3.7339616, 1.5911998, 0.0, 0.0, 0.0, 0.0, 4.191738, 2.9497063, 1.156919, 1.1710563, 3.8561807, 0.0, 0.6560465, 0.0, 0.780705, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.44132638, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 26, 26, 34, 34], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, 26, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.016593043, 1.0, -0.30769232, 1.0, -0.013648688, -0.020338038, 0.008939875, 1.0, -0.115384616, 1.0, 0.013227679, 0.031385433, 0.007620757, -0.01517911, 1.0, 0.0, 1.0, 0.3846154, 0.3846154, 0.015931556, 1.0769231, 0.0032340095, 1.0, -0.011672873, 0.0034415114, 0.0311544, 0.0027349414, -0.010351558, 0.0016939108, -0.002122363, 1.0, -0.017402792, -0.008052614], "split_indices": [93, 119, 89, 64, 0, 50, 1, 121, 0, 0, 0, 53, 1, 122, 0, 0, 0, 0, 59, 1, 53, 1, 1, 0, 1, 0, 115, 0, 0, 0, 0, 0, 0, 0, 62, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1119.0, 955.0, 1012.0, 107.0, 233.0, 722.0, 872.0, 140.0, 113.0, 120.0, 209.0, 513.0, 701.0, 171.0, 103.0, 106.0, 115.0, 398.0, 297.0, 404.0, 206.0, 192.0, 116.0, 181.0, 99.0, 305.0, 110.0, 96.0, 89.0, 103.0, 93.0, 88.0, 103.0, 202.0, 103.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0014349989, 0.0099707395, -0.01457876, 0.018751651, -0.015238828, 0.031189635, -0.083799526, 0.023396183, 0.012787911, -0.0027237632, -0.013814327, -0.0034918978, 0.07057166, 0.022424739, -0.018576512, 0.15106711, 0.003403264, 0.051320728, -0.06305361, 0.0026739968, 0.023151407, -0.0070564644, 0.010532758, 0.107123636, -0.0034391305, 0.0013242401, -0.015762892, 0.02354402, 0.00963171, -0.009960934, 0.008635833, -0.001998113, 0.003957731], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [2.59122, 2.778611, 0.0, 2.358455, 0.0, 1.2425952, 0.61475646, 1.9356673, 0.0, 0.0, 0.0, 4.5916405, 2.9953392, 2.101954, 0.0, 2.5204377, 2.2768168, 1.9434634, 1.5513799, 0.0, 0.0, 0.0, 0.0, 3.940595, 2.7721052, 0.0, 0.0, 0.0, 0.15873268, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01457876, 1.0, -0.015238828, 5.0, 1.0, 1.0, 0.012787911, -0.0027237632, -0.013814327, 1.0, -0.03846154, 1.0, -0.018576512, 1.0, 1.0, 1.0, 0.46153846, 0.0026739968, 0.023151407, -0.0070564644, 0.010532758, 1.0, 1.0, 0.0013242401, -0.015762892, 0.02354402, 1.0, -0.009960934, 0.008635833, -0.001998113, 0.003957731], "split_indices": [43, 117, 0, 40, 0, 0, 111, 50, 0, 0, 0, 119, 1, 23, 0, 109, 122, 15, 1, 0, 0, 0, 0, 126, 111, 0, 0, 0, 122, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1949.0, 113.0, 1849.0, 100.0, 1649.0, 200.0, 1526.0, 123.0, 98.0, 102.0, 972.0, 554.0, 851.0, 121.0, 252.0, 302.0, 636.0, 215.0, 99.0, 153.0, 175.0, 127.0, 315.0, 321.0, 119.0, 96.0, 136.0, 179.0, 155.0, 166.0, 90.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0031212275, 0.012872014, -0.084307685, 0.0025810592, 0.011225322, -0.0020245824, -0.013221948, -0.024778277, 0.03450029, -0.012226208, -0.007053945, 0.0071246806, 0.01381245, 0.016394746, -0.03831227, -0.06304335, 0.06542453, 0.038054474, -0.090076335, 0.002354526, -0.016735878, -0.009788405, 0.1403627, -0.0077936696, 0.01622655, -0.04200233, -0.020142721, 0.00061544206, 0.023925297, -0.011595131, 0.0013340111], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, 13, 15, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.767234, 1.9073865, 0.63841784, 1.4758582, 0.0, 0.0, 0.0, 1.5723298, 2.2126846, 0.0, 4.115817, 2.524015, 0.0, 0.0, 2.5734377, 2.529108, 4.124221, 3.7891424, 2.0769951, 0.0, 0.0, 0.0, 3.0658064, 0.0, 0.0, 1.1090724, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 10, 10, 11, 11, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 22, 22, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, 14, 16, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.011225322, -0.0020245824, -0.013221948, -0.42307693, 1.0, -0.012226208, 1.0, -0.07692308, 0.01381245, 0.016394746, 0.15384616, -0.30769232, 1.0, -0.1923077, 1.0, 0.002354526, -0.016735878, -0.009788405, 1.0, -0.0077936696, 0.01622655, 1.0, -0.020142721, 0.00061544206, 0.023925297, -0.011595131, 0.0013340111], "split_indices": [40, 125, 108, 93, 0, 0, 0, 1, 42, 0, 89, 1, 0, 0, 1, 1, 39, 1, 127, 0, 0, 0, 71, 0, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1865.0, 208.0, 1690.0, 175.0, 89.0, 119.0, 910.0, 780.0, 140.0, 770.0, 617.0, 163.0, 119.0, 651.0, 280.0, 337.0, 263.0, 388.0, 153.0, 127.0, 106.0, 231.0, 136.0, 127.0, 271.0, 117.0, 98.0, 133.0, 116.0, 155.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005996284, -0.007896891, 0.012332579, -0.0009394964, -0.014124693, -0.0150021305, 0.071827546, 0.015685223, -0.06821531, 0.018936116, 0.019025832, -0.07530675, 0.05347784, 0.010230797, -0.10145291, -0.00798754, 0.012367636, -0.042701747, -0.013722328, -0.02537579, 0.11877199, -0.049530793, -0.18200234, -0.0014397384, -0.00728723, 0.020791223, -0.008785749, 0.050774653, 0.034285408, -0.014223243, -0.0022041455, -0.009988003, -0.026324157, 0.01028458, -0.0057695736, -0.0014364022, 0.012556352, 0.0051684594, -0.005497023], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, 17, 19, -1, 21, -1, -1, 23, -1, 25, 27, 29, 31, -1, -1, 33, -1, 35, -1, -1, 37, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8538482, 1.7961601, 0.0, 1.8828652, 0.0, 2.5180428, 1.8666636, 3.3631704, 3.1966302, 2.132006, 0.0, 0.57939184, 3.5577388, 0.0, 1.9740438, 0.0, 0.0, 0.16054383, 0.0, 0.9028779, 5.7595797, 1.2591426, 1.2342362, 0.0, 0.0, 1.1592389, 0.0, 1.4127786, 0.0, 0.0, 0.54026455, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 25, 25, 27, 27, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, 18, 20, -1, 22, -1, -1, 24, -1, 26, 28, 30, 32, -1, -1, 34, -1, 36, -1, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.012332579, 1.0, -0.014124693, 1.0, 1.0, -0.1923077, -0.46153846, 1.0, 0.019025832, -0.30769232, 1.0, 0.010230797, 1.0, -0.00798754, 0.012367636, 1.0, -0.013722328, 1.0, 1.0, 1.0, 1.0, -0.0014397384, -0.00728723, 1.0, -0.008785749, 0.53846157, 0.034285408, -0.014223243, 1.0, -0.009988003, -0.026324157, 0.01028458, -0.0057695736, -0.0014364022, 0.012556352, 0.0051684594, -0.005497023], "split_indices": [102, 117, 0, 42, 0, 109, 0, 1, 1, 50, 0, 1, 39, 0, 105, 0, 0, 71, 0, 115, 121, 39, 50, 0, 0, 122, 0, 1, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1936.0, 114.0, 1840.0, 96.0, 1542.0, 298.0, 978.0, 564.0, 206.0, 92.0, 287.0, 691.0, 92.0, 472.0, 106.0, 100.0, 188.0, 99.0, 313.0, 378.0, 287.0, 185.0, 97.0, 91.0, 180.0, 133.0, 290.0, 88.0, 97.0, 190.0, 92.0, 93.0, 88.0, 92.0, 155.0, 135.0, 94.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.010574866, -0.0044277725, -0.011798433, 0.0016276283, -0.011570329, -0.0082603805, 0.010097859, -0.015274335, 0.009235795, -0.0045513553, -0.016570238, -0.046222012, 0.013955316, -0.007643811, -0.10637783, -0.05544715, 0.039957862, 0.010611341, -0.066397555, -0.014816922, -0.0065055974, 0.00036637776, -0.011716594, 0.014803178, 0.015104239, -0.0028496494, -0.010347465, -0.019667657, 0.013367689, 0.0018183602, -0.01389159], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.3660692, 1.3186619, 0.0, 1.8233035, 0.0, 1.191276, 0.0, 2.5453718, 0.0, 1.1359558, 0.0, 1.0512782, 1.8407335, 1.8446909, 0.3056605, 1.0142152, 2.073367, 0.0, 0.25575715, 0.0, 0.0, 0.0, 0.0, 2.479094, 0.0, 0.0, 0.0, 2.1169238, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.011798433, 1.0, -0.011570329, 3.1923077, 0.010097859, 1.3461539, 0.009235795, 1.0, -0.016570238, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.010611341, 1.0, -0.014816922, -0.0065055974, 0.00036637776, -0.011716594, 1.0, 0.015104239, -0.0028496494, -0.010347465, 1.0, 0.013367689, 0.0018183602, -0.01389159], "split_indices": [43, 117, 0, 125, 0, 1, 0, 1, 0, 17, 0, 109, 5, 53, 3, 106, 62, 0, 15, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1957.0, 112.0, 1856.0, 101.0, 1688.0, 168.0, 1578.0, 110.0, 1473.0, 105.0, 453.0, 1020.0, 276.0, 177.0, 278.0, 742.0, 94.0, 182.0, 88.0, 89.0, 142.0, 136.0, 605.0, 137.0, 90.0, 92.0, 469.0, 136.0, 356.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.002239417, -0.003962487, 0.012025626, -0.012558763, 0.08052796, -0.0128860595, -0.0049678027, 0.022625143, -0.0073475214, -0.029263563, 0.025714645, -0.014679762, -0.014660415, 0.012703366, -0.0023321344, 0.0102771185, -0.03414977, -0.011161679, 0.038779706, 0.020791762, -0.066209204, -0.040370148, 0.1157, 0.01960676, -0.010020508, -0.09004372, 0.003156647, 0.0024302118, -0.01181145, 0.0015162706, 0.018926386, -0.060035482, -0.018040184, -0.0029927508, -0.008223045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, 13, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.5099703, 1.423554, 0.0, 1.5705764, 4.0619793, 0.0, 1.2449074, 0.0, 0.0, 1.5996537, 2.0971541, 0.0, 1.8973039, 0.0, 2.5968926, 0.0, 1.2523515, 0.0, 2.557057, 5.55645, 1.0463662, 1.0407765, 1.5753286, 0.0, 0.0, 0.9788463, 0.0, 0.0, 0.0, 0.0, 0.0, 0.18109411, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 25, 25, 31, 31], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, 14, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.012025626, -0.53846157, 1.0, -0.0128860595, 1.0, 0.022625143, -0.0073475214, -0.3846154, -0.30769232, -0.014679762, 1.0, 0.012703366, -0.07692308, 0.0102771185, 0.15384616, -0.011161679, 1.0, 1.0, 2.3076923, 1.0, 1.0, 0.01960676, -0.010020508, 1.0, 0.003156647, 0.0024302118, -0.01181145, 0.0015162706, 0.018926386, 1.0, -0.018040184, -0.0029927508, -0.008223045], "split_indices": [114, 125, 0, 1, 15, 0, 93, 0, 0, 1, 1, 0, 89, 0, 1, 0, 1, 0, 39, 69, 1, 12, 71, 0, 0, 71, 0, 0, 0, 0, 0, 122, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1960.0, 103.0, 1779.0, 181.0, 109.0, 1670.0, 93.0, 88.0, 932.0, 738.0, 103.0, 829.0, 160.0, 578.0, 118.0, 711.0, 158.0, 420.0, 262.0, 449.0, 207.0, 213.0, 107.0, 155.0, 361.0, 88.0, 113.0, 94.0, 90.0, 123.0, 271.0, 90.0, 115.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002701272, -0.0042389412, 0.01022805, -0.013835773, 0.0092997905, 0.019333055, -0.040520217, -0.013154438, 0.019615026, -0.021844223, -0.015600924, -0.05826918, 0.012779409, 0.06427286, -0.047493298, -0.01871945, -0.018730855, 0.017952494, -0.004291155, -0.020061873, -0.023430724, 0.008899518, -0.07112117, -0.082560994, 0.03444589, 0.000539871, -0.011399076, -0.018897196, -0.025217287, 0.015991291, -0.028954983, 0.0025187917, -0.007562253, 0.0013513222, -0.0070529147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, -1, 25, 27, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [1.4319618, 1.8075416, 0.0, 1.5604167, 0.0, 4.515058, 2.1072636, 4.2222805, 0.0, 1.8576204, 0.0, 2.5670462, 0.0, 2.3841734, 2.3876147, 2.1731064, 0.0, 0.0, 0.0, 0.0, 1.9164655, 0.0, 0.8496177, 1.6902533, 2.251186, 0.0, 0.0, 0.0, 0.45732373, 0.0, 0.3319293, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 20, 20, 22, 22, 23, 23, 24, 24, 28, 28, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, -1, 26, 28, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.01022805, -0.03846154, 0.0092997905, -0.15384616, 1.0, 1.0, 0.019615026, 0.0, -0.015600924, 1.0, 0.012779409, 1.0, 0.115384616, 1.0, -0.018730855, 0.017952494, -0.004291155, -0.020061873, 1.0, 0.008899518, -0.42307693, 0.65384614, 0.5769231, 0.000539871, -0.011399076, -0.018897196, 1.3076923, 0.015991291, 1.0, 0.0025187917, -0.007562253, 0.0013513222, -0.0070529147], "split_indices": [0, 125, 0, 1, 0, 1, 64, 61, 0, 0, 0, 113, 0, 106, 1, 122, 0, 0, 0, 0, 122, 0, 1, 1, 1, 0, 0, 0, 1, 0, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1937.0, 135.0, 1763.0, 174.0, 786.0, 977.0, 664.0, 122.0, 841.0, 136.0, 503.0, 161.0, 193.0, 648.0, 385.0, 118.0, 93.0, 100.0, 88.0, 560.0, 126.0, 259.0, 277.0, 283.0, 93.0, 166.0, 97.0, 180.0, 95.0, 188.0, 90.0, 90.0, 93.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044505056, 0.0053108563, -0.09118185, 0.011913459, -0.0118523035, -0.0014879091, -0.014667476, 0.0020248657, 0.01064176, -0.0046291817, 0.00893519, -0.0372821, 0.018250328, -0.016739696, -0.012494148, 0.04481732, -0.031387992, -0.052579112, 0.005207556, 0.017100291, 0.019399097, -0.014267179, 0.037276465, 0.0038918208, -0.014604415, -0.045554258, 0.011346492, -0.0043425173, 0.048905082, 0.0026810064, 0.0047522495, 0.0029887024, -0.010373358, 0.015530397, 0.0054866886, 0.009714779, -0.008700016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, 23, 25, 27, -1, -1, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [1.7491091, 1.5183313, 0.88496006, 1.647546, 0.0, 0.0, 0.0, 0.9274012, 0.0, 1.1079239, 0.0, 1.1002483, 1.1499423, 0.3893556, 0.0, 2.3484812, 2.3229382, 1.6077389, 1.6870695, 0.92207485, 0.0, 0.0, 0.020160705, 0.0, 0.0, 0.91732675, 0.0, 0.0, 1.4505754, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8904707, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 22, 22, 25, 25, 28, 28, 34, 34], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, 24, 26, 28, -1, -1, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.0118523035, -0.0014879091, -0.014667476, 5.0, 0.01064176, 1.0, 0.00893519, 1.0, 1.0, 1.0, -0.012494148, 2.0, 1.0, 1.0, 1.0, 1.0, 0.019399097, -0.014267179, 1.0, 0.0038918208, -0.014604415, 1.0, 0.011346492, -0.0043425173, 0.0, 0.0026810064, 0.0047522495, 0.0029887024, -0.010373358, 0.015530397, 1.0, 0.009714779, -0.008700016], "split_indices": [40, 117, 126, 125, 0, 0, 0, 0, 0, 124, 0, 0, 106, 122, 0, 0, 39, 69, 121, 69, 0, 0, 97, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1857.0, 209.0, 1763.0, 94.0, 88.0, 121.0, 1596.0, 167.0, 1483.0, 113.0, 611.0, 872.0, 495.0, 116.0, 568.0, 304.0, 188.0, 307.0, 479.0, 89.0, 116.0, 188.0, 95.0, 93.0, 209.0, 98.0, 165.0, 314.0, 93.0, 95.0, 91.0, 118.0, 91.0, 223.0, 112.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003479855, -0.006392856, 0.011725764, -0.06372129, 0.008444227, 0.0017617493, -0.027671356, -0.0030125007, 0.016731089, 0.011492756, -0.05238456, -0.010895601, 0.011386299, 0.002804257, -0.015872713, 0.03454248, -0.026760729, -0.018445741, 0.0153158335, -0.039528966, 0.009086177, 0.013107585, -0.011214596, -0.0044246763, -0.101147056, -0.035633184, 0.014568822, 0.001104432, -0.021961002, -0.09369782, 0.004991536, -0.0023997086, -0.017906163, -0.009187791, 0.0069381245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [1.468741, 1.6714033, 0.0, 5.6347427, 2.8411639, 1.8934013, 0.0, 1.3414724, 0.0, 0.0, 1.7875416, 0.9832816, 0.0, 0.0, 0.0, 2.2186916, 1.5183518, 3.4184902, 0.0, 1.9727098, 0.0, 0.0, 0.0, 2.7218683, 4.399164, 1.1346118, 0.0, 0.0, 0.0, 1.1780834, 1.7651829, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24, 25, 25, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, -0.3846154, 0.011725764, 2.0, 5.0, 1.0, -0.027671356, 1.0, 0.016731089, 0.011492756, 1.0, 0.0, 0.011386299, 0.002804257, -0.015872713, -1.0, 1.0, 1.0, 0.0153158335, 1.0, 0.009086177, 0.013107585, -0.011214596, 1.0, 1.0, 1.0, 0.014568822, 0.001104432, -0.021961002, 1.0, 1.0, -0.0023997086, -0.017906163, -0.009187791, 0.0069381245], "split_indices": [114, 1, 0, 0, 0, 15, 0, 84, 0, 0, 124, 0, 0, 0, 0, 0, 119, 122, 0, 50, 0, 0, 0, 61, 93, 81, 0, 0, 0, 124, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1965.0, 101.0, 404.0, 1561.0, 309.0, 95.0, 1456.0, 105.0, 100.0, 209.0, 1364.0, 92.0, 119.0, 90.0, 353.0, 1011.0, 244.0, 109.0, 912.0, 99.0, 94.0, 150.0, 581.0, 331.0, 481.0, 100.0, 170.0, 161.0, 198.0, 283.0, 109.0, 89.0, 113.0, 170.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025369728, 0.010640002, -0.0034848445, -0.011142777, 0.005515398, -0.0075021456, 0.07859551, 0.021828998, -0.057866123, -0.00016695142, 0.019680403, -0.06560426, 0.052070912, 0.0088010095, -0.09959274, -0.015422421, -0.00016009557, 0.025689075, 0.01945799, -0.05128724, -0.01726062, -0.0016673515, 0.089270234, -0.012855236, -0.00023366463, -0.014043755, 0.038525227, 0.0101751145, 0.007706062, -0.0015213317, 0.009359067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, -1, 19, -1, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [1.2896665, 0.0, 1.8934783, 0.0, 1.7114313, 2.255735, 2.5807395, 2.5516038, 3.4208496, 0.0, 0.0, 1.4066479, 2.6956677, 0.0, 1.5412769, 0.0, 0.0, 1.052309, 0.0, 0.9947113, 0.0, 2.3592963, 0.027734041, 0.0, 0.0, 0.0, 0.970597, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22, 26, 26], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, -1, 20, -1, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010640002, -0.5, -0.011142777, 1.0, 1.0, 1.0, -0.1923077, -0.30769232, -0.00016695142, 0.019680403, 1.0, 1.0, 0.0088010095, 1.0, -0.015422421, -0.00016009557, 1.0, 0.01945799, 1.0, -0.01726062, 1.0, 1.0, -0.012855236, -0.00023366463, -0.014043755, 1.0, 0.0101751145, 0.007706062, -0.0015213317, 0.009359067], "split_indices": [1, 0, 1, 0, 42, 109, 105, 1, 1, 0, 0, 13, 105, 0, 105, 0, 0, 74, 0, 97, 0, 5, 13, 0, 0, 0, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 113.0, 1949.0, 150.0, 1799.0, 1527.0, 272.0, 965.0, 562.0, 162.0, 110.0, 248.0, 717.0, 125.0, 437.0, 104.0, 144.0, 605.0, 112.0, 263.0, 174.0, 423.0, 182.0, 102.0, 161.0, 95.0, 328.0, 90.0, 92.0, 166.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.008673097, -0.030215746, 0.013312206, -0.043229133, 0.006280961, 0.028953543, -0.012185253, -0.09536601, -0.025004288, 0.008210552, 0.019449072, -0.002364118, -0.015644416, 0.0076493854, -0.045088734, 0.04230282, -0.055757616, -0.0297826, -0.012822881, 0.011006862, 0.01593832, -0.014544082, 0.0060903444, 0.0037448776, -0.050596707, 0.009214653, -0.03962679, -0.002961116, -0.012804736, -0.013609621, 0.0011176616, 0.008013651, -0.0061067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, -1, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [0.97802866, 1.2626297, 2.1606686, 0.86942005, 0.0, 3.1453018, 0.0, 1.0382538, 1.3821259, 1.7751874, 0.0, 0.0, 0.0, 0.0, 0.72026443, 1.9456592, 2.9608984, 0.66889465, 0.0, 1.7214186, 0.0, 0.0, 0.0, 0.0, 1.3466334, 0.0, 1.2644519, 1.0912322, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 24, 24, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, -1, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.23076923, 0.006280961, 1.0, -0.012185253, -0.42307693, 0.03846154, 1.0, 0.019449072, -0.002364118, -0.015644416, 0.0076493854, 1.0, 1.0, 1.0, 1.0, -0.012822881, 1.0, 0.01593832, -0.014544082, 0.0060903444, 0.0037448776, 1.0, 0.009214653, 1.0, 1.0, -0.012804736, -0.013609621, 0.0011176616, 0.008013651, -0.0061067], "split_indices": [71, 121, 90, 1, 0, 125, 0, 1, 1, 97, 0, 0, 0, 0, 40, 58, 59, 26, 0, 126, 0, 0, 0, 0, 50, 0, 69, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1043.0, 1022.0, 915.0, 128.0, 916.0, 106.0, 237.0, 678.0, 814.0, 102.0, 109.0, 128.0, 112.0, 566.0, 531.0, 283.0, 478.0, 88.0, 419.0, 112.0, 160.0, 123.0, 113.0, 365.0, 161.0, 258.0, 226.0, 139.0, 89.0, 169.0, 93.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003434067, -0.0021441223, 0.00973417, 0.008885386, -0.10349952, 0.00055579375, 0.016582245, -0.019216904, -0.0021995178, -0.01747515, 0.07284301, -0.026371066, 0.008605632, 0.1467207, -0.0033084569, -0.0010539228, -0.051605467, -0.00016734946, 0.027656568, 0.0073335045, -0.02750334, -0.076931365, 0.0012145916, -0.015991747, 0.037388343, -0.0009210768, -0.121141404, -0.0038474612, 0.011426283, -0.00537549, -0.018505946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, -1, 19, 21, -1, -1, -1, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [1.0743848, 2.1642551, 0.0, 2.2824087, 1.3731201, 2.1610487, 0.0, 0.0, 0.0, 1.2221762, 2.5903027, 0.7806904, 0.0, 3.7573075, 0.0, 1.2002021, 0.98811173, 0.0, 0.0, 0.0, 3.8666594, 1.311341, 0.0, 0.0, 1.7612417, 0.0, 1.1414111, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 16, 16, 20, 20, 21, 21, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, -1, 20, 22, -1, -1, -1, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.00973417, 1.0, -0.15384616, 1.0, 0.016582245, -0.019216904, -0.0021995178, 3.5, 1.0, 1.0, 0.008605632, 1.0, -0.0033084569, 1.0, 1.0, -0.00016734946, 0.027656568, 0.0073335045, 1.0, 0.0, 0.0012145916, -0.015991747, 1.0, -0.0009210768, 1.0, -0.0038474612, 0.011426283, -0.00537549, -0.018505946], "split_indices": [102, 119, 0, 125, 1, 105, 0, 0, 0, 1, 12, 106, 0, 93, 0, 81, 62, 0, 0, 0, 69, 1, 0, 0, 93, 0, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2051.0, 1936.0, 115.0, 1746.0, 190.0, 1658.0, 88.0, 91.0, 99.0, 1327.0, 331.0, 1222.0, 105.0, 195.0, 136.0, 610.0, 612.0, 91.0, 104.0, 160.0, 450.0, 438.0, 174.0, 148.0, 302.0, 173.0, 265.0, 152.0, 150.0, 129.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0036799205, 0.009942437, -0.010641731, 0.024416836, -0.04701196, 0.032625012, -0.0055040536, -0.0107281115, -0.0040734094, 0.020858297, 0.01603919, -0.0054228525, 0.006750209, 0.010800548, 0.010787643, 0.012557082, -0.0040729656, 0.080151945, -0.03107227, 0.016201226, -0.004981184, -0.051106624, 0.00774745, -0.12549783, -0.015245383, -0.020672923, -0.0065884455, -0.0061069643, 0.013929914], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, 15, -1, -1, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [1.4348195, 1.6232054, 0.0, 1.0239542, 1.0325598, 2.1393332, 0.0, 0.0, 0.83644146, 1.1403937, 0.0, 0.0, 0.0, 1.99382, 0.0, 0.0, 2.3513303, 2.6703587, 1.7027624, 0.0, 0.0, 1.76339, 0.0, 1.0411317, 3.1585217, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, 16, -1, -1, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010641731, 1.0, 1.0, 1.0, -0.0055040536, -0.0107281115, 1.0, 1.0, 0.01603919, -0.0054228525, 0.006750209, -0.46153846, 0.010787643, 0.012557082, 0.0, 0.1923077, 1.0, 0.016201226, -0.004981184, -0.115384616, 0.00774745, 1.0, 1.0, -0.020672923, -0.0065884455, -0.0061069643, 0.013929914], "split_indices": [43, 80, 0, 90, 122, 125, 0, 0, 13, 88, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, 0, 1, 0, 111, 62, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2081.0, 1969.0, 112.0, 1570.0, 399.0, 1423.0, 147.0, 166.0, 233.0, 1303.0, 120.0, 137.0, 96.0, 1168.0, 135.0, 134.0, 1034.0, 251.0, 783.0, 154.0, 97.0, 661.0, 122.0, 215.0, 446.0, 91.0, 124.0, 344.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0071788477, -0.05195904, 0.005238488, -0.09143829, 0.0020252978, -0.0034091098, 0.012687062, -0.046723112, -0.018419847, 0.016214194, -0.046488725, 0.0014552939, -0.011821187, -0.004941426, 0.012005808, -0.09199177, 0.0078102923, 0.036655273, -0.054496687, -0.042624384, -0.02339923, 0.019843642, -0.0063109803, 0.003913074, -0.089648046, -0.01641809, 0.002441585, 0.06066469, -0.06872811, -0.016129661, -0.0032869924, 0.0065730526, 0.00557114, 0.00092867546, -0.013972168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1449085, 1.274342, 1.6955434, 1.19871, 0.0, 1.2722734, 0.0, 0.8542074, 0.0, 2.2715762, 2.67024, 0.0, 0.0, 1.7706872, 0.0, 2.4185166, 0.0, 3.246178, 1.2901237, 2.08619, 0.0, 0.0, 1.5425786, 0.0, 1.1593995, 0.0, 0.0, 0.0044661164, 1.0578642, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 22, 22, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.07692308, 5.0, -0.26923078, 0.0020252978, 1.0, 0.012687062, 1.0, -0.018419847, 1.0, 1.0, 0.0014552939, -0.011821187, 1.0, 0.012005808, -0.07692308, 0.0078102923, 0.115384616, 1.0, -0.46153846, -0.02339923, 0.019843642, 1.0, 0.003913074, 1.0, -0.01641809, 0.002441585, 1.2692307, 1.0, -0.016129661, -0.0032869924, 0.0065730526, 0.00557114, 0.00092867546, -0.013972168], "split_indices": [5, 1, 0, 1, 0, 15, 0, 122, 0, 113, 0, 0, 0, 109, 0, 1, 0, 1, 81, 1, 0, 0, 12, 0, 12, 0, 0, 1, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 447.0, 1612.0, 289.0, 158.0, 1505.0, 107.0, 195.0, 94.0, 1034.0, 471.0, 105.0, 90.0, 859.0, 175.0, 345.0, 126.0, 467.0, 392.0, 256.0, 89.0, 98.0, 369.0, 107.0, 285.0, 91.0, 165.0, 178.0, 191.0, 126.0, 159.0, 88.0, 90.0, 91.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0070977216, 0.0018707635, 0.01106337, -0.020127496, 0.025377346, -0.09695003, -0.00075567805, -0.024995293, 0.06648606, -0.01754911, -0.00050402567, -0.02660727, 0.011375471, 0.033730563, -0.01197493, 0.13916524, -0.052443504, 0.039318155, -0.08324743, -0.010439462, 0.010558761, 0.043515354, 0.030839194, 0.0007440652, -0.010548378, -0.005723668, 0.012233178, -0.13661037, -0.0019673042, -0.0011407344, 0.010826635, -0.01916693, -0.0086457655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, 17, -1, 19, -1, 21, 23, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.1148268, 1.0140408, 0.0, 1.5075384, 1.9630753, 1.4726125, 2.3948627, 2.3704815, 4.5120134, 0.0, 0.0, 2.4644578, 0.0, 2.6103456, 0.0, 5.2444305, 0.62890226, 2.4446871, 1.2043431, 0.0, 0.0, 0.73615444, 0.0, 0.0, 0.0, 0.0, 0.0, 0.53294086, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, 18, -1, 20, -1, 22, 24, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.01106337, 1.0, -0.07692308, 1.0, 1.0, -0.30769232, 1.0, -0.01754911, -0.00050402567, 1.0, 0.011375471, 1.0, -0.01197493, 1.0, 1.0, 1.0, 1.0, -0.010439462, 0.010558761, 1.0769231, 0.030839194, 0.0007440652, -0.010548378, -0.005723668, 0.012233178, 1.0, -0.0019673042, -0.0011407344, 0.010826635, -0.01916693, -0.0086457655], "split_indices": [114, 39, 0, 89, 1, 90, 113, 1, 109, 0, 0, 126, 0, 89, 0, 71, 2, 106, 137, 0, 0, 1, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1961.0, 99.0, 1013.0, 948.0, 204.0, 809.0, 426.0, 522.0, 110.0, 94.0, 660.0, 149.0, 263.0, 163.0, 324.0, 198.0, 305.0, 355.0, 90.0, 173.0, 207.0, 117.0, 93.0, 105.0, 141.0, 164.0, 193.0, 162.0, 112.0, 95.0, 92.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0037016664, 0.009079608, -0.0012408063, -0.010593237, 0.007203926, -0.0057509867, 0.080774456, 0.017743068, -0.045914244, 0.019213786, -0.0001320348, -0.0035365706, 0.11189657, 0.008921145, -0.07271133, 0.065404594, -0.028898353, 0.019437924, 0.0026601956, -0.020944526, -0.15286636, -0.0053181895, 0.016456746, -0.011593063, -0.001404055, -0.0109, 0.02377473, -0.012893452, -0.01795177, -0.0053130514, 0.024283225, 0.009519585, -0.0039867824, -0.0035698414, 0.007734393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, -1, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.88976467, 0.0, 1.7292846, 0.0, 1.7251108, 1.4521971, 2.477579, 1.9454498, 2.0567136, 0.0, 0.0, 1.3847888, 1.2593231, 0.0, 1.9668009, 2.5047474, 1.3854837, 0.0, 0.0, 1.1340796, 0.11863279, 0.0, 0.0, 0.0, 0.58463335, 0.0, 0.8681759, 0.0, 0.0, 0.0, 0.9357044, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 24, 24, 26, 26, 30, 30], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, -1, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009079608, -0.5, -0.010593237, 1.0, 1.0, -0.03846154, 1.0, -0.34615386, 0.019213786, -0.0001320348, 0.0, 1.0, 0.008921145, 1.0, 1.0, -0.15384616, 0.019437924, 0.0026601956, 1.0, 1.0, -0.0053181895, 0.016456746, -0.011593063, 1.0, -0.0109, 0.26923078, -0.012893452, -0.01795177, -0.0053130514, 1.0, 0.009519585, -0.0039867824, -0.0035698414, 0.007734393], "split_indices": [1, 0, 1, 0, 42, 109, 1, 105, 1, 0, 0, 0, 53, 0, 105, 111, 1, 0, 0, 39, 50, 0, 0, 0, 59, 0, 1, 0, 0, 0, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 111.0, 1956.0, 146.0, 1810.0, 1539.0, 271.0, 971.0, 568.0, 115.0, 156.0, 792.0, 179.0, 94.0, 474.0, 213.0, 579.0, 91.0, 88.0, 288.0, 186.0, 97.0, 116.0, 139.0, 440.0, 97.0, 191.0, 98.0, 88.0, 146.0, 294.0, 90.0, 101.0, 138.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0013098874, 0.0058156857, -0.008365038, -0.0038315305, 0.051484827, 0.0048807645, -0.06820322, 0.113639496, -0.0076154205, -0.0025943236, 0.010294507, -0.018355628, 0.006943391, 0.020240886, 0.0011596176, 0.0059858114, -0.0060111177, 0.024452098, -0.03113961, 0.07034337, -0.03420182, 0.008124888, -0.07527765, 0.017997595, 0.0155884, 0.0028233794, -0.009517698, -0.031389404, -0.016401868, -0.002751991, 0.0053266147, -0.007459186, 0.0011813019], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, -1, -1, -1, -1, -1, 17, -1, 19, 21, 23, 25, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.79051036, 0.8639777, 0.0, 0.90797615, 2.71321, 1.0453144, 3.0642352, 2.0834136, 0.0, 0.65389085, 0.0, 0.0, 0.0, 0.0, 0.0, 0.79046065, 0.0, 2.072611, 1.8999127, 1.9343615, 1.2867733, 0.0, 1.0710386, 0.4302302, 0.0, 0.0, 0.0, 0.34342715, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, -1, -1, -1, -1, -1, 18, -1, 20, 22, 24, 26, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008365038, 3.0, 1.0, 1.0, 1.0, 1.0, -0.0076154205, 1.0, 0.010294507, -0.018355628, 0.006943391, 0.020240886, 0.0011596176, 1.0, -0.0060111177, 1.0, -0.30769232, 1.0, 1.0, 0.008124888, 1.0, 0.65384614, 0.0155884, 0.0028233794, -0.009517698, 1.0, -0.016401868, -0.002751991, 0.0053266147, -0.007459186, 0.0011813019], "split_indices": [117, 42, 0, 0, 58, 125, 106, 12, 0, 0, 0, 0, 0, 0, 0, 109, 0, 15, 1, 2, 69, 0, 105, 1, 0, 0, 0, 53, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1961.0, 104.0, 1619.0, 342.0, 1426.0, 193.0, 230.0, 112.0, 1325.0, 101.0, 105.0, 88.0, 123.0, 107.0, 1153.0, 172.0, 770.0, 383.0, 432.0, 338.0, 108.0, 275.0, 268.0, 164.0, 167.0, 171.0, 184.0, 91.0, 117.0, 151.0, 92.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0023418148, -0.0030441189, 0.009661994, -0.06379457, 0.009248822, 0.0030387451, -0.12754856, -0.006030749, 0.08310929, -0.01689637, -0.008212547, 0.0054300576, -0.076634236, -0.0011239576, 0.01677648, -0.026411003, 0.030902911, -0.019638797, 0.0058088694, 0.0006719902, -0.064428926, -0.017791355, 0.0780989, 0.007761399, -0.0076786377, -0.014597763, 0.0014854509, -0.0089740595, 0.02708419, 0.00018863768, 0.017389378, -0.0091718985, 0.014346284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.0429717, 1.4510357, 0.0, 1.9634653, 1.8237472, 0.0, 0.36683393, 1.0834827, 2.2124414, 0.0, 0.0, 0.93436724, 3.0169792, 0.0, 0.0, 0.5271754, 1.4708315, 0.0, 0.0, 1.7819806, 1.3771439, 1.0170602, 2.3727498, 0.0, 0.0, 0.0, 0.0, 0.0, 2.6822736, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 22, 22, 28, 28], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.009661994, 1.0, 1.0, 0.0030387451, 1.0, 3.0, 1.0, -0.01689637, -0.008212547, 1.0, 1.0, -0.0011239576, 0.01677648, 1.0, 1.0, -0.019638797, 0.0058088694, 1.0, 1.0, 1.0, 1.0, 0.007761399, -0.0076786377, -0.014597763, 0.0014854509, -0.0089740595, 1.0, 0.00018863768, 0.017389378, -0.0091718985, 0.014346284], "split_indices": [102, 0, 0, 122, 42, 0, 137, 0, 93, 0, 0, 2, 122, 0, 0, 17, 111, 0, 0, 12, 12, 17, 12, 0, 0, 0, 0, 0, 124, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1943.0, 111.0, 327.0, 1616.0, 132.0, 195.0, 1339.0, 277.0, 102.0, 93.0, 1152.0, 187.0, 131.0, 146.0, 512.0, 640.0, 99.0, 88.0, 299.0, 213.0, 315.0, 325.0, 150.0, 149.0, 105.0, 108.0, 121.0, 194.0, 181.0, 144.0, 96.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004968886, 0.0039574755, -0.008670196, 0.013362961, -0.03293735, 0.022726094, -0.007481169, -0.011772247, -0.00875184, 0.010914343, 0.013815909, -0.0066204714, 0.030167831, -0.029631555, 0.061809376, 0.0062783184, -0.000244749, -0.09978264, 0.021587178, 0.14830832, -0.0011702091, -0.015083606, -0.003874046, -0.010309097, 0.060131222, 0.025959605, -7.956176e-05, -0.010943164, 0.08322712, 0.017863752, -0.0064190933, 0.017763859, -0.0003397796, 0.015883759, -0.01800221], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, -1, -1, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [0.797161, 0.685005, 0.0, 1.2986547, 0.82227874, 1.938848, 0.0, 0.0, 0.69764674, 2.6620243, 0.0, 0.0, 0.19785962, 2.5798097, 3.1160662, 0.0, 0.0, 0.9442723, 1.9943241, 3.9990191, 3.024339, 0.0, 0.0, 0.0, 2.500063, 0.0, 0.0, 0.0, 1.5211803, 0.0, 5.823879, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 24, 24, 28, 28, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, -1, -1, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008670196, 1.3461539, 1.0, 0.84615386, -0.007481169, -0.011772247, 1.0, 1.0, 0.013815909, -0.0066204714, 1.0, 1.0, 1.0, 0.0062783184, -0.000244749, 1.0, 1.0, 1.0, -0.34615386, -0.015083606, -0.003874046, -0.010309097, -0.34615386, 0.025959605, -7.956176e-05, -0.010943164, 1.0, 0.017863752, 1.0, 0.017763859, -0.0003397796, 0.015883759, -0.01800221], "split_indices": [117, 80, 0, 1, 89, 1, 0, 0, 122, 124, 0, 0, 39, 15, 15, 0, 0, 106, 89, 122, 1, 0, 0, 0, 1, 0, 0, 0, 50, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1974.0, 102.0, 1573.0, 401.0, 1422.0, 151.0, 89.0, 312.0, 1290.0, 132.0, 126.0, 186.0, 718.0, 572.0, 93.0, 93.0, 303.0, 415.0, 241.0, 331.0, 165.0, 138.0, 98.0, 317.0, 138.0, 103.0, 145.0, 186.0, 114.0, 203.0, 89.0, 97.0, 104.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019945705, 0.005213519, -0.06110092, 0.014149501, -0.0016121882, -0.016797967, 0.0010151571, -0.014951504, 0.049084492, -0.02151397, 0.007770764, 0.018686423, -0.06401826, 0.02955729, -0.039048266, 0.00050289126, -0.013804825, -0.0037761207, 0.010550639, -0.084953114, -0.0073482324, -0.022453874, -0.0004073366, 0.017392337, -0.04954077, -0.007669834, 0.007969819, -0.014428148, -0.01666922, 0.0053549707, -0.059852615, -0.0149386795, 0.000638809], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, 11, 13, -1, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, 25, -1, 27, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [0.8819109, 1.7162517, 1.7134607, 0.0, 1.1881871, 0.0, 0.0, 0.8458289, 5.703476, 1.1632526, 0.0, 0.0, 1.0274242, 1.6974429, 1.4071639, 0.0, 0.0, 0.0, 0.0, 4.6615424, 4.3748317, 0.0, 1.5033876, 0.0, 1.9086607, 0.0, 0.0, 1.1023654, 0.0, 0.0, 1.2691932, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 19, 19, 20, 20, 22, 22, 24, 24, 27, 27, 30, 30], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, 12, 14, -1, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, 26, -1, 28, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, -0.5769231, -0.30769232, 0.014149501, 1.0, -0.016797967, 0.0010151571, 1.0, 1.0, 0.0, 0.007770764, 0.018686423, 0.07692308, 1.0, 1.0, 0.00050289126, -0.013804825, -0.0037761207, 0.010550639, -0.15384616, -0.30769232, -0.022453874, 1.0, 0.017392337, 1.0, -0.007669834, 0.007969819, 0.6923077, -0.01666922, 0.0053549707, 1.0, -0.0149386795, 0.000638809], "split_indices": [119, 1, 1, 0, 105, 0, 0, 90, 109, 0, 0, 0, 1, 111, 69, 0, 0, 0, 0, 1, 1, 0, 83, 0, 15, 0, 0, 1, 0, 0, 23, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1845.0, 225.0, 88.0, 1757.0, 90.0, 135.0, 1391.0, 366.0, 1299.0, 92.0, 165.0, 201.0, 332.0, 967.0, 104.0, 97.0, 176.0, 156.0, 395.0, 572.0, 149.0, 246.0, 108.0, 464.0, 126.0, 120.0, 357.0, 107.0, 143.0, 214.0, 91.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039558313, 0.002247511, -0.06113715, 0.0076465416, -0.010162511, 0.0001478902, -0.012132155, 0.015848426, -0.05136144, 0.008280498, 0.011617552, -0.012693181, 0.0015009022, -0.020275572, 0.048611782, 0.008467239, -0.013541691, 0.11123336, -0.026672237, -0.035838462, 0.04247737, 0.17738226, -0.0038924615, 0.0069603864, -0.009699503, 0.004971858, -0.011256384, 0.010772341, -0.003339836, 0.007988008, 0.026587162, -0.0037381803, 0.004760742, -0.009030579, 0.0052521485], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, 21, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7321326, 1.0442308, 0.76123923, 0.85663855, 0.0, 0.0, 0.0, 1.1799023, 1.0833786, 1.6642109, 0.0, 0.0, 0.0, 2.7998257, 2.8239279, 1.0201325, 0.0, 3.2480216, 0.6433184, 1.9299324, 1.144937, 1.9585323, 0.0, 0.3316377, 0.0, 0.0, 0.0, 0.0, 1.0930575, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, 22, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.010162511, 0.0001478902, -0.012132155, 5.0, 1.0, 1.0, 0.011617552, -0.012693181, 0.0015009022, 1.0, 0.0, 1.0, -0.013541691, 1.0, 1.0, 1.0, 1.0, -0.5, -0.0038924615, 1.0, -0.009699503, 0.004971858, -0.011256384, 0.010772341, 0.07692308, 0.007988008, 0.026587162, -0.0037381803, 0.004760742, -0.009030579, 0.0052521485], "split_indices": [40, 117, 111, 119, 0, 0, 0, 0, 93, 109, 0, 0, 0, 113, 1, 69, 0, 122, 105, 127, 111, 1, 0, 126, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1862.0, 202.0, 1770.0, 92.0, 99.0, 103.0, 1554.0, 216.0, 1445.0, 109.0, 101.0, 115.0, 846.0, 599.0, 677.0, 169.0, 327.0, 272.0, 294.0, 383.0, 227.0, 100.0, 184.0, 88.0, 139.0, 155.0, 158.0, 225.0, 108.0, 119.0, 88.0, 96.0, 88.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0062166215, -0.010729057, 0.008153011, 0.010063039, -0.030169157, -0.075807065, 0.04110934, -0.13524835, 0.0020172582, 0.0047444375, -0.02050708, -0.009977142, 0.1428438, -0.022956235, -0.0039335773, 0.103535265, -0.043353915, 0.029062979, -0.016161723, -0.010137213, 0.035023354, 0.026132196, -0.0011115471, -0.11992755, 0.0035560539, 0.103560746, -0.0059100045, -0.0050394624, -0.018946044, -0.0047592106, 0.0055634202, 0.018136365, 0.0027298473], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.81763947, 0.7938497, 0.0, 2.5299854, 3.432855, 4.014849, 3.6224878, 2.1529236, 3.5788553, 0.0, 0.0, 2.7469025, 11.800957, 0.0, 0.0, 4.341687, 1.92894, 2.4235733, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9863038, 0.88701266, 1.1866846, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.008153011, 1.0, -0.1923077, 1.0, 1.0, 1.0, 0.1923077, 0.0047444375, -0.02050708, 1.0, 1.0, -0.022956235, -0.0039335773, 1.0, 1.0, 1.0, -0.016161723, -0.010137213, 0.035023354, 0.026132196, -0.0011115471, 1.0, 1.2692307, 1.0, -0.0059100045, -0.0050394624, -0.018946044, -0.0047592106, 0.0055634202, 0.018136365, 0.0027298473], "split_indices": [114, 126, 0, 89, 1, 69, 61, 69, 1, 0, 0, 113, 17, 0, 0, 69, 69, 97, 0, 0, 0, 0, 0, 127, 1, 39, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1964.0, 101.0, 949.0, 1015.0, 252.0, 697.0, 238.0, 777.0, 129.0, 123.0, 464.0, 233.0, 120.0, 118.0, 240.0, 537.0, 369.0, 95.0, 107.0, 126.0, 101.0, 139.0, 204.0, 333.0, 200.0, 169.0, 102.0, 102.0, 168.0, 165.0, 99.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0018120022, -0.00253992, 0.0074530044, -0.07462802, 0.0115163755, -0.019488828, -0.0156906, -0.00024926668, 0.055222187, -0.005557202, 0.0022744872, 0.0151113635, -0.010941603, 0.016159078, 0.009698165, 0.031331953, -0.007648725, 0.012281687, -0.005684223, 0.064666376, -0.019105986, 0.0041232677, 0.09694532, -0.045933075, 0.007052084, 0.010452504, -0.011970555, 0.025445456, 0.037735634, -0.0010194088, -0.009631916, -0.0063313553, 0.010247975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, 15, -1, -1, 17, 19, -1, -1, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.6557121, 1.9809853, 0.0, 1.44722, 0.8412756, 0.2910701, 0.0, 2.1614861, 1.6802868, 0.0, 0.0, 1.6789353, 0.0, 0.0, 1.8290524, 1.614067, 0.0, 0.0, 0.0, 1.1295664, 0.91849107, 2.4989593, 3.5159285, 0.52941954, 0.0, 0.0, 0.0, 0.0, 1.7926012, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, 16, -1, -1, 18, 20, -1, -1, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.0074530044, 1.0, 1.0, 1.0, -0.0156906, 1.0, 1.0, -0.005557202, 0.0022744872, 1.0, -0.010941603, 0.016159078, 1.0, 1.0, -0.007648725, 0.012281687, -0.005684223, 1.0, 1.0, 1.0, 1.0, 1.0, 0.007052084, 0.010452504, -0.011970555, 0.025445456, 1.0, -0.0010194088, -0.009631916, -0.0063313553, 0.010247975], "split_indices": [102, 0, 0, 15, 113, 39, 0, 64, 69, 0, 0, 58, 0, 0, 2, 137, 0, 0, 0, 59, 93, 15, 115, 17, 0, 0, 0, 0, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1955.0, 117.0, 319.0, 1636.0, 191.0, 128.0, 1289.0, 347.0, 103.0, 88.0, 1130.0, 159.0, 104.0, 243.0, 960.0, 170.0, 90.0, 153.0, 578.0, 382.0, 201.0, 377.0, 294.0, 88.0, 111.0, 90.0, 103.0, 274.0, 172.0, 122.0, 107.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0024709373, 0.008432956, -0.002111478, -0.010795606, 0.0065098736, 0.0013050259, 0.008806838, -0.022814026, 0.024700787, -0.10662925, 0.0040278947, -0.0033174136, 0.13315327, -0.0053068693, -0.015423861, 0.009507681, -0.024754092, 0.012080442, -0.030290892, 0.011217582, 0.015460746, 0.0071552023, -0.07502711, 0.0009386977, -0.016425712, -0.015503108, -0.037247438, -0.028360803, 0.010886852, -0.00858038, 0.0013141214, -0.076029874, 0.0077617723, -0.016374595, -0.0023737568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.778354, 0.0, 1.7931087, 0.0, 0.7713155, 0.9637963, 0.0, 1.89205, 2.6345046, 0.5201962, 1.6693025, 2.30677, 0.08010888, 0.0, 0.0, 0.0, 2.3433337, 0.0, 2.3679805, 0.0, 0.0, 0.0, 0.9611621, 1.4514912, 0.0, 0.0, 0.5284852, 1.823735, 0.0, 0.0, 0.0, 1.1421314, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 16, 16, 18, 18, 22, 22, 23, 23, 26, 26, 27, 27, 31, 31], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [-0.5769231, 0.008432956, -0.5, -0.010795606, 1.0, 1.0, 0.008806838, -0.1923077, 2.0, -0.34615386, 0.115384616, -0.30769232, 1.0, -0.0053068693, -0.015423861, 0.009507681, 1.0, 0.012080442, 1.0, 0.011217582, 0.015460746, 0.0071552023, 1.0, 1.0, -0.016425712, -0.015503108, 1.0, 1.0, 0.010886852, -0.00858038, 0.0013141214, 1.0, 0.0077617723, -0.016374595, -0.0023737568], "split_indices": [1, 0, 1, 0, 125, 13, 0, 1, 0, 1, 1, 1, 111, 0, 0, 0, 124, 0, 113, 0, 0, 0, 81, 7, 0, 0, 122, 50, 0, 0, 0, 81, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 110.0, 1965.0, 148.0, 1817.0, 1708.0, 109.0, 841.0, 867.0, 204.0, 637.0, 689.0, 178.0, 96.0, 108.0, 153.0, 484.0, 123.0, 566.0, 90.0, 88.0, 166.0, 318.0, 459.0, 107.0, 102.0, 216.0, 361.0, 98.0, 110.0, 106.0, 249.0, 112.0, 93.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0015834174, 0.008912747, -0.048774358, 0.0004362885, 0.015360586, 0.0073181684, -0.017354479, -0.056324452, 0.013531775, 0.003047857, -0.11419315, 0.0054997816, 0.009119496, -0.0075534494, -0.015800625, 0.018866748, -0.07660071, -0.035179608, 0.05401751, -0.012700743, -0.0026193955, -0.09914906, 0.025284657, 0.019253764, 0.01842556, -0.0028059434, -0.015276748, -0.007063929, 0.015770143, -0.04652999, 0.09943655, 0.012986012, -0.016832318, 0.019858276, 0.001076918], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.7640135, 2.2162583, 4.0019417, 1.2688293, 0.0, 0.0, 0.0, 1.6074172, 0.86519676, 0.0, 0.32520032, 1.379475, 0.0, 0.0, 0.0, 2.053652, 0.4471873, 1.6477114, 2.9655535, 0.0, 0.0, 0.78902435, 2.7817254, 2.7270331, 0.0, 0.0, 0.0, 0.0, 0.0, 6.101205, 2.0483103, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 23, 23, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, -1.0, 0.015360586, 0.0073181684, -0.017354479, 1.0, 1.0, 0.003047857, 1.0, 1.3461539, 0.009119496, -0.0075534494, -0.015800625, 1.0, 3.2692308, 1.0, 1.0, -0.012700743, -0.0026193955, 1.0, 1.0, 1.0, 0.01842556, -0.0028059434, -0.015276748, -0.007063929, 0.015770143, 1.0, 1.0, 0.012986012, -0.016832318, 0.019858276, 0.001076918], "split_indices": [0, 102, 109, 0, 0, 0, 0, 122, 88, 0, 93, 1, 0, 0, 0, 17, 1, 97, 61, 0, 0, 26, 111, 12, 0, 0, 0, 0, 0, 39, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1807.0, 263.0, 1707.0, 100.0, 133.0, 130.0, 320.0, 1387.0, 128.0, 192.0, 1257.0, 130.0, 102.0, 90.0, 1081.0, 176.0, 426.0, 655.0, 88.0, 88.0, 207.0, 219.0, 517.0, 138.0, 89.0, 118.0, 127.0, 92.0, 284.0, 233.0, 116.0, 168.0, 110.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0046534818, 0.010559549, -0.039113823, 0.022788849, -0.043365482, -0.008365503, 0.004085784, -0.012833762, 0.0521972, -0.007422837, -0.0011572978, 0.025070982, -0.078473695, 0.015975198, 0.025267197, 0.083232544, -0.04717179, -0.015354517, 0.00016445805, -0.0013345939, 0.012062153, -4.4342753e-05, 0.01990266, -0.012682283, 0.004515098, -0.051683936, 0.05690849, -0.011278466, 0.00609705, 0.02216846, -0.00845135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, 15, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.5348212, 1.2022054, 0.8762603, 1.556737, 0.33066696, 0.0, 0.0, 1.6719799, 2.3577101, 0.0, 0.0, 1.7899468, 1.4795916, 0.0, 1.6513246, 2.2866473, 1.3971853, 0.0, 0.0, 1.492643, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8791323, 5.4995003, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, 16, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.0, 1.0, -0.1923077, 1.0, -0.008365503, 0.004085784, 1.0, -0.03846154, -0.007422837, -0.0011572978, 1.0, 1.0, 0.015975198, 0.84615386, 1.0, -0.42307693, -0.015354517, 0.00016445805, 1.0, 0.012062153, -4.4342753e-05, 0.01990266, -0.012682283, 0.004515098, 0.42307693, 1.0, -0.011278466, 0.00609705, 0.02216846, -0.00845135], "split_indices": [1, 80, 80, 1, 106, 0, 0, 126, 1, 0, 0, 105, 97, 0, 1, 61, 1, 0, 0, 93, 0, 0, 0, 0, 0, 1, 121, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1823.0, 246.0, 1486.0, 337.0, 158.0, 88.0, 672.0, 814.0, 171.0, 166.0, 426.0, 246.0, 163.0, 651.0, 236.0, 190.0, 127.0, 119.0, 509.0, 142.0, 137.0, 99.0, 102.0, 88.0, 273.0, 236.0, 177.0, 96.0, 109.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0006262435, 0.004707033, -0.006939148, 0.013802284, -0.031350784, 0.0058857445, 0.008618206, -0.0077194455, 0.0013377301, -0.037383858, 0.017700775, -0.006020136, 0.008571611, 0.003809696, -0.009520837, 0.13040654, -0.0055286554, 0.0004763611, 0.025212308, 0.072868705, -0.031169474, -0.0038318548, 0.017463332, -0.008547551, -0.01100524, 0.016145805, -0.012957217, -0.04003184, 0.013641345, -0.014178884, 0.0045690704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, 17, 19, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [0.5914559, 0.64147973, 0.0, 0.8950216, 0.59043336, 0.7198142, 0.0, 0.0, 1.194291, 1.3181218, 2.8956087, 0.0, 0.0, 0.0, 0.0, 2.890344, 1.8433279, 0.0, 0.0, 2.5571747, 1.2330775, 0.0, 0.0, 1.6048269, 0.0, 3.0133343, 0.0, 2.6517525, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, 18, 20, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.006939148, 1.0, 1.0, -0.3846154, 0.008618206, -0.0077194455, 1.0, 1.0, 1.0, -0.006020136, 0.008571611, 0.003809696, -0.009520837, 1.0, 0.0, 0.0004763611, 0.025212308, 1.0, 1.0384616, -0.0038318548, 0.017463332, 1.0, -0.01100524, 1.0, -0.012957217, 0.03846154, 0.013641345, -0.014178884, 0.0045690704], "split_indices": [43, 80, 0, 125, 122, 1, 0, 0, 13, 108, 89, 0, 0, 0, 0, 50, 0, 0, 0, 13, 1, 0, 0, 113, 0, 124, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1956.0, 114.0, 1562.0, 394.0, 1408.0, 154.0, 164.0, 230.0, 302.0, 1106.0, 133.0, 97.0, 131.0, 171.0, 189.0, 917.0, 93.0, 96.0, 226.0, 691.0, 108.0, 118.0, 537.0, 154.0, 446.0, 91.0, 304.0, 142.0, 139.0, 165.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0058287703, 0.0075197066, 0.0019789354, -0.00943258, 0.009674765, -0.0009820843, 0.070406936, 0.016671725, -0.03226732, -0.0006093413, 0.018444914, -0.033125266, 0.042888835, 0.019284308, -0.11029775, 0.0043274146, -0.072524086, 0.017619992, 0.018583495, 0.011382482, -0.032603033, -0.00211652, -0.020791909, 1.2872537e-05, -0.016188707, 0.053159844, -0.008695177, 0.0033783522, -0.009033049, 0.017138882, 0.007471355, 0.0084432755, -0.006692464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, 19, 21, -1, 23, -1, 25, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.54853386, 0.0, 1.4422677, 0.0, 1.1662791, 0.8466814, 2.346829, 1.2794228, 2.2244956, 0.0, 0.0, 1.0173957, 2.0801897, 1.6335171, 1.9142718, 0.0, 1.4478216, 0.0, 1.9814199, 0.0, 0.82395065, 0.0, 0.0, 0.0, 0.0, 2.2092957, 0.0, 0.0, 0.0, 0.0, 1.689058, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 18, 18, 20, 20, 25, 25, 30, 30], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, 20, 22, -1, 24, -1, 26, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.0075197066, -0.5, -0.00943258, 1.0, 1.0, 1.0, -0.07692308, 1.0, -0.0006093413, 0.018444914, 1.0, 1.0, 1.0, 1.0, 0.0043274146, 1.0, 0.017619992, 1.0, 0.011382482, 1.0, -0.00211652, -0.020791909, 1.2872537e-05, -0.016188707, 0.1923077, -0.008695177, 0.0033783522, -0.009033049, 0.017138882, 1.0, 0.0084432755, -0.006692464], "split_indices": [1, 0, 1, 0, 42, 109, 105, 1, 105, 0, 0, 59, 89, 3, 50, 0, 93, 0, 50, 0, 59, 0, 0, 0, 0, 1, 0, 0, 0, 0, 12, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 108.0, 1946.0, 144.0, 1802.0, 1533.0, 269.0, 980.0, 553.0, 161.0, 108.0, 338.0, 642.0, 333.0, 220.0, 115.0, 223.0, 99.0, 543.0, 118.0, 215.0, 115.0, 105.0, 123.0, 100.0, 409.0, 134.0, 100.0, 115.0, 114.0, 295.0, 145.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0022075307, -0.048280463, 0.009332291, 0.0069128857, -0.01387835, 0.015052676, -0.03768576, 0.02009512, -0.0055824257, 0.0004093308, -0.0071727983, 0.008211399, 0.055245902, -0.005457174, 0.016524816, 0.0073134773, 0.02096948, 0.07257895, -0.022573592, -0.011817231, 0.010508463, 0.012102314, 0.002617448, 0.0089847185, -0.042559512, -0.084538884, -0.0132531505, 0.0031710023, -0.15428825, 0.03351968, -0.011018817, -0.024671922, -0.006859701, 0.009389953, -0.0051215035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [0.74173194, 2.709605, 0.4860131, 0.0, 0.0, 0.5757599, 0.2787617, 0.62825376, 0.0, 0.0, 0.0, 2.412631, 2.8131816, 1.3811126, 0.0, 3.557978, 0.0, 0.4181326, 1.905314, 0.0, 0.0, 0.0, 0.0, 0.0, 0.8857888, 2.4000533, 1.9223847, 0.0, 1.4652958, 1.4632531, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 24, 24, 25, 25, 26, 26, 28, 28, 29, 29], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5, -0.5769231, 1.0, 0.0069128857, -0.01387835, 1.0, 1.0, 1.0, -0.0055824257, 0.0004093308, -0.0071727983, 1.0, 1.0, -0.30769232, 0.016524816, -0.07692308, 0.02096948, 1.0, 1.0, -0.011817231, 0.010508463, 0.012102314, 0.002617448, 0.0089847185, 1.0, 1.0, 1.1923077, 0.0031710023, 0.115384616, 1.0, -0.011018817, -0.024671922, -0.006859701, 0.009389953, -0.0051215035], "split_indices": [1, 1, 40, 0, 0, 43, 106, 0, 0, 0, 0, 73, 61, 1, 0, 1, 0, 59, 26, 0, 0, 0, 0, 0, 124, 39, 1, 0, 1, 109, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 255.0, 1807.0, 111.0, 144.0, 1611.0, 196.0, 1504.0, 107.0, 88.0, 108.0, 1124.0, 380.0, 1034.0, 90.0, 290.0, 90.0, 186.0, 848.0, 127.0, 163.0, 91.0, 95.0, 128.0, 720.0, 296.0, 424.0, 111.0, 185.0, 286.0, 138.0, 89.0, 96.0, 167.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044514085, -0.008646907, 0.0066474206, -0.003402612, -0.007846875, -0.011416864, 0.0068462878, -0.07621477, 0.0073149716, -0.02152998, -0.019805629, 0.023856664, -0.051889993, -0.009875689, 0.0070343385, 0.10518799, -0.004764002, -0.013555774, -0.012490882, 0.018623017, -0.0021590844, -0.010158854, 0.021066979, -0.0065290094, 0.004086999, 0.017418934, -0.0103834625, 0.0077652214, -0.00916401, -0.0035134598, 0.007830522], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, -1, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, -1, -1, -1], "loss_changes": [0.6180494, 0.718052, 0.0, 1.0505292, 0.0, 1.9918194, 0.0, 2.4519386, 1.2467129, 1.8021545, 0.0, 2.3161182, 0.9164085, 0.0, 0.0, 2.6610785, 1.8407896, 0.0, 0.5324909, 0.0, 0.0, 0.0, 2.7979605, 0.0, 0.0, 0.0, 0.7108062, 1.1923044, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 22, 22, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, -1, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, -1, -1, -1], "split_conditions": [3.5, 1.3461539, 0.0066474206, 0.88461536, -0.007846875, 1.0, 0.0068462878, 1.0, 1.0, -0.30769232, -0.019805629, -0.34615386, 1.0, -0.009875689, 0.0070343385, 1.0, -0.1923077, -0.013555774, 1.0, 0.018623017, -0.0021590844, -0.010158854, -0.03846154, -0.0065290094, 0.004086999, 0.017418934, 1.0, 1.0, -0.00916401, -0.0035134598, 0.007830522], "split_indices": [1, 1, 0, 1, 0, 89, 0, 106, 7, 1, 0, 1, 59, 0, 0, 97, 1, 0, 97, 0, 0, 0, 1, 0, 0, 0, 64, 108, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1961.0, 116.0, 1824.0, 137.0, 1641.0, 183.0, 368.0, 1273.0, 254.0, 114.0, 995.0, 278.0, 138.0, 116.0, 259.0, 736.0, 89.0, 189.0, 158.0, 101.0, 155.0, 581.0, 95.0, 94.0, 99.0, 482.0, 394.0, 88.0, 245.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015260834, 0.006669098, -0.058577165, -0.00030510625, 0.012720311, -0.023392733, 0.007834007, -0.008538281, 0.01069286, -0.031276986, 0.013944291, -0.06476279, 0.041905694, -0.0063303537, 0.030497402, -0.020149741, -0.12174752, 0.011956698, -0.0025245019, 0.0024955983, 0.014208033, 0.0064067463, -0.0075834356, -0.0041332874, -0.01831009, -0.036921494, 0.049109183, 0.008732306, -0.014411288, 0.014966716, -0.0052286745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.9678162, 1.5215386, 6.2422, 1.5105968, 0.0, 0.0, 0.0, 0.81233597, 0.0, 1.9359592, 1.0216746, 1.3779058, 1.2933228, 0.0, 2.0559359, 1.4256392, 1.1742225, 0.0, 0.0, 0.96645766, 0.0, 0.0, 0.0, 0.0, 0.0, 3.795615, 2.457277, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.012720311, -0.023392733, 0.007834007, 1.0, 0.01069286, 0.53846157, -1.0, -0.1923077, 1.0, -0.0063303537, 1.0, 1.0, 1.0, 0.011956698, -0.0025245019, 1.0, 0.014208033, 0.0064067463, -0.0075834356, -0.0041332874, -0.01831009, 1.0, 0.42307693, 0.008732306, -0.014411288, 0.014966716, -0.0052286745], "split_indices": [0, 102, 59, 125, 0, 0, 0, 111, 0, 1, 0, 1, 12, 0, 105, 108, 17, 0, 0, 12, 0, 0, 0, 0, 0, 39, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1810.0, 260.0, 1711.0, 99.0, 114.0, 146.0, 1589.0, 122.0, 790.0, 799.0, 542.0, 248.0, 141.0, 658.0, 304.0, 238.0, 115.0, 133.0, 526.0, 132.0, 121.0, 183.0, 103.0, 135.0, 285.0, 241.0, 132.0, 153.0, 121.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0050739422, 0.0005857798, 0.00845552, 0.0058160946, -0.0069100447, 0.00091935665, 0.008542303, -0.020251252, 0.024199218, -0.006368877, -0.0135648465, 0.00370165, 0.011784219, 0.020298313, -0.046621248, -0.07292862, 0.034450356, -0.013033183, 0.010636325, -0.011398542, -0.00793686, -0.0026444772, -0.012089596, 0.07508917, -0.009918177, 0.0030850384, -0.0056412364, 0.0060683247, -0.006419301, 0.0088122105, 0.01948975, 0.013052085, -0.0075245537], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.73413926, 0.7100086, 0.0, 0.7063433, 0.0, 0.8412929, 0.0, 1.4321762, 1.5605149, 0.8565874, 0.0, 1.5716397, 0.0, 1.3769634, 0.82868904, 0.42587352, 2.5849903, 0.6586571, 0.0, 0.0, 0.7797813, 0.0, 0.0, 2.8982942, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4041805, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 23, 23, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [3.5, 1.3461539, 0.00845552, 1.0, -0.0069100447, 1.0, 0.008542303, 1.0, 0.53846157, 0.07692308, -0.0135648465, 1.0, 0.011784219, 1.0, 0.42307693, 1.0, 1.0, -0.34615386, 0.010636325, -0.011398542, 1.0, -0.0026444772, -0.012089596, 1.0, -0.009918177, 0.0030850384, -0.0056412364, 0.0060683247, -0.006419301, 1.0, 0.01948975, 0.013052085, -0.0075245537], "split_indices": [1, 1, 0, 84, 0, 39, 0, 119, 1, 1, 0, 2, 0, 105, 1, 126, 61, 1, 0, 0, 108, 0, 0, 105, 0, 0, 0, 0, 0, 13, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1948.0, 110.0, 1812.0, 136.0, 1707.0, 105.0, 894.0, 813.0, 798.0, 96.0, 667.0, 146.0, 480.0, 318.0, 191.0, 476.0, 346.0, 134.0, 116.0, 202.0, 97.0, 94.0, 365.0, 111.0, 172.0, 174.0, 91.0, 111.0, 235.0, 130.0, 96.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047707064, -0.0015052654, -0.006826895, 0.0021175046, -0.00743892, 0.012271931, -0.06623604, 0.0030218086, 0.01163184, 0.001613082, -0.021037808, 0.0135915885, -0.032374654, -0.0044766557, 0.033027165, -0.08754476, 0.0056231855, -0.105401956, 0.039088216, 0.012447237, -0.005995716, -0.014061219, -0.0028109208, -0.011386164, -0.009712815, -0.03670666, 0.105664805, 0.068104334, -0.008855285, -0.009025124, 0.0015215328, 0.0031802692, 0.021003515, -3.6358455e-05, 0.013999566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, 23, 25, -1, 27, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42817706, 0.5185782, 0.0, 1.2986443, 0.0, 1.5678186, 2.8731523, 0.55970263, 0.0, 0.0, 0.0, 0.40454412, 1.6816202, 2.6248884, 1.9804932, 0.6686671, 0.0, 0.012598515, 2.104251, 0.0, 2.3797028, 0.0, 0.0, 0.0, 0.0, 0.5421278, 1.7114005, 1.0090616, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, 24, 26, -1, 28, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.006826895, 3.0, -0.00743892, 1.0, 1.0, 1.0, 0.01163184, 0.001613082, -0.021037808, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0056231855, 1.0, 1.0, 0.012447237, 1.0, -0.014061219, -0.0028109208, -0.011386164, -0.009712815, 1.0, 1.0, 1.0, -0.008855285, -0.009025124, 0.0015215328, 0.0031802692, 0.021003515, -3.6358455e-05, 0.013999566], "split_indices": [117, 52, 0, 0, 0, 125, 12, 113, 0, 0, 0, 13, 12, 0, 0, 39, 0, 39, 39, 0, 39, 0, 0, 0, 0, 97, 71, 15, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1964.0, 101.0, 1871.0, 93.0, 1629.0, 242.0, 1496.0, 133.0, 154.0, 88.0, 1152.0, 344.0, 597.0, 555.0, 212.0, 132.0, 180.0, 417.0, 166.0, 389.0, 112.0, 100.0, 89.0, 91.0, 195.0, 222.0, 205.0, 184.0, 96.0, 99.0, 130.0, 92.0, 105.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.003668557, -0.0010072588, 0.0055982703, 0.0093628485, -0.046041496, 0.00030900698, 0.009488492, -0.020686062, -0.008906888, 0.013021555, -0.07452592, -0.005663268, 0.0034867765, 0.025896916, -0.006303982, 0.0020524191, -0.015813485, 0.06390965, -0.015862443, 0.0034779129, 0.12190639, -0.008828209, 0.01134606, 0.008456494, -0.006718361, 0.0035525076, 0.019067045, 0.0096968, -0.034815323, -0.009473648, 0.005345559], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, 17, -1, -1, -1, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [0.50683475, 0.8882529, 0.0, 1.1970726, 0.3883878, 1.3299768, 0.0, 0.44732177, 0.0, 1.1702845, 1.6132491, 0.0, 0.0, 1.6223102, 0.0, 0.0, 0.0, 1.8750908, 0.95959973, 1.5011898, 1.6215997, 0.0, 1.3991594, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2165381, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, 18, -1, -1, -1, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 0.0055982703, 1.0, 1.0, 1.0, 0.009488492, 1.0, -0.008906888, 1.0, 1.0, -0.005663268, 0.0034867765, 1.0, -0.006303982, 0.0020524191, -0.015813485, 1.0, 1.0, 1.0, 1.0, -0.008828209, 1.0, 0.008456494, -0.006718361, 0.0035525076, 0.019067045, 0.0096968, 1.0, -0.009473648, 0.005345559], "split_indices": [31, 80, 0, 88, 50, 119, 0, 59, 0, 64, 69, 0, 0, 108, 0, 0, 0, 81, 17, 13, 115, 0, 81, 0, 0, 0, 0, 0, 59, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1902.0, 170.0, 1546.0, 356.0, 1398.0, 148.0, 224.0, 132.0, 1195.0, 203.0, 136.0, 88.0, 1022.0, 173.0, 95.0, 108.0, 535.0, 487.0, 262.0, 273.0, 133.0, 354.0, 122.0, 140.0, 121.0, 152.0, 124.0, 230.0, 137.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0044847187, 0.009628232, -0.037642006, 0.030213859, -0.017297354, -0.010566079, 0.00651419, -0.02023608, 0.054092556, 0.027556336, -0.090386815, -0.06697769, 0.005419092, 0.017394805, 0.16175279, -0.0069992603, 0.04971561, -0.01824519, -0.0025581205, -0.012651555, -0.0011334796, -0.017302714, 0.010976202, 0.014005478, 0.018274322, 0.11839048, -0.017949322, -0.08657721, 0.06449596, 0.015922649, 0.0065953285, 0.0007063164, -0.004963184, -0.002493921, -0.014880784, 0.009081769, 0.0037875087], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, -1, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, -1, -1, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4500429, 1.0259724, 1.5800195, 1.2637081, 2.6292226, 0.0, 0.0, 1.1723683, 2.8130324, 1.074322, 1.8197315, 0.68576145, 0.0, 1.701809, 0.08243561, 0.0, 1.8819869, 0.0, 0.0, 0.0, 0.0, 2.1872928, 0.0, 0.0, 0.0, 0.4304061, 0.16166168, 0.8016757, 0.12402487, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 21, 21, 25, 25, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, -1, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, -1, -1, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.1923077, 1.0, 1.0, 1.0, -0.010566079, 0.00651419, 1.0, -0.115384616, 0.42307693, 1.0, 1.0, 0.005419092, 1.0, 1.0, -0.0069992603, 1.0, -0.01824519, -0.0025581205, -0.012651555, -0.0011334796, 1.0, 0.010976202, 0.014005478, 0.018274322, 1.0, 1.0, 1.0, -0.34615386, 0.015922649, 0.0065953285, 0.0007063164, -0.004963184, -0.002493921, -0.014880784, 0.009081769, 0.0037875087], "split_indices": [119, 1, 50, 127, 127, 0, 0, 115, 1, 1, 53, 97, 0, 61, 122, 0, 12, 0, 0, 0, 0, 69, 0, 0, 0, 80, 39, 115, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2077.0, 1851.0, 226.0, 1049.0, 802.0, 136.0, 90.0, 337.0, 712.0, 497.0, 305.0, 207.0, 130.0, 531.0, 181.0, 92.0, 405.0, 126.0, 179.0, 100.0, 107.0, 386.0, 145.0, 89.0, 92.0, 201.0, 204.0, 209.0, 177.0, 113.0, 88.0, 114.0, 90.0, 105.0, 104.0, 89.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030147724, -0.04476364, 0.008277741, -0.012846879, -0.017027896, 0.0005832806, 0.01183956, -0.07801026, 0.0143418675, -0.0072427616, 0.01218019, -0.015244645, 0.004959461, 0.003266305, -0.053674467, 0.010389746, -0.0057961605, -0.011678573, 0.005087345, 0.012520747, -0.048307374, 0.0126805175, -0.010964883, 0.0035361138, -0.011899286, 0.048673816, -0.04583059, -0.0042490866, 0.014820226, -0.011311898, -0.015563311, 0.0048506274, -0.006197271], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, -1, -1, 15, 17, -1, 19, -1, -1, 21, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [0.9721293, 1.758652, 1.3751634, 3.5639768, 0.0, 1.4391207, 0.0, 2.3461103, 0.0, 0.6953344, 0.0, 0.0, 0.0, 1.0597053, 1.7353138, 0.0, 0.83006656, 0.0, 0.0, 1.9996115, 1.898442, 0.0, 1.2850355, 0.0, 0.0, 2.0687535, 0.79428786, 0.0, 0.0, 0.0, 0.7998529, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 13, 13, 14, 14, 16, 16, 19, 19, 20, 20, 22, 22, 25, 25, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, -1, -1, 16, 18, -1, 20, -1, -1, 22, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [-0.3846154, 1.0, 5.0, 1.0, -0.017027896, 1.0, 0.01183956, 1.0, 0.0143418675, 1.0, 0.01218019, -0.015244645, 0.004959461, -0.30769232, 0.26923078, 0.010389746, 1.0, -0.011678573, 0.005087345, 0.07692308, 1.0, 0.0126805175, 1.0, 0.0035361138, -0.011899286, 0.53846157, 1.0, -0.0042490866, 0.014820226, -0.011311898, 1.0769231, 0.0048506274, -0.006197271], "split_indices": [1, 64, 0, 12, 0, 84, 0, 59, 0, 113, 0, 0, 0, 1, 1, 0, 15, 0, 0, 1, 5, 0, 124, 0, 0, 1, 81, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 439.0, 1623.0, 350.0, 89.0, 1517.0, 106.0, 247.0, 103.0, 1425.0, 92.0, 156.0, 91.0, 1162.0, 263.0, 96.0, 1066.0, 164.0, 99.0, 745.0, 321.0, 127.0, 618.0, 147.0, 174.0, 228.0, 390.0, 119.0, 109.0, 121.0, 269.0, 113.0, 156.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018281359, -0.006196233, 0.006065181, -0.00041172135, -0.009196819, -0.0059395344, 0.009405858, -0.0122101605, 0.00742132, 0.0024416172, -0.03519707, 0.02919144, -0.025901806, 0.0037436453, -0.10026685, -0.007017403, 0.014843093, 0.017323403, -0.08580274, -0.035544764, 0.0056926757, -0.019265352, -0.0025924442, 0.007944206, -0.060505047, 0.0053613293, -0.00425725, -8.77882e-05, -0.016652336, 0.002564446, -0.007572876, -0.013145921, 0.0037684005], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5638494, 0.95806324, 0.0, 0.9446851, 0.0, 0.85895663, 0.0, 0.53382677, 0.0, 0.73391974, 1.5633944, 2.1501276, 1.2169387, 0.8065394, 1.5865641, 1.7665639, 0.0, 0.5933972, 1.3504715, 0.5458598, 0.0, 0.0, 0.0, 0.0, 1.6441939, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 0.006065181, 1.0, -0.009196819, 1.0, 0.009405858, 1.0, 0.00742132, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.014843093, 1.0, 1.0, -0.03846154, 0.0056926757, -0.019265352, -0.0025924442, 0.007944206, 1.0, 0.0053613293, -0.00425725, -8.77882e-05, -0.016652336, 0.002564446, -0.007572876, -0.013145921, 0.0037684005], "split_indices": [0, 0, 0, 102, 0, 125, 0, 115, 0, 97, 108, 58, 50, 62, 39, 126, 0, 39, 13, 1, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1931.0, 135.0, 1809.0, 122.0, 1709.0, 100.0, 1585.0, 124.0, 968.0, 617.0, 498.0, 470.0, 386.0, 231.0, 382.0, 116.0, 273.0, 197.0, 222.0, 164.0, 103.0, 128.0, 146.0, 236.0, 170.0, 103.0, 96.0, 101.0, 88.0, 134.0, 137.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0026211562, 0.009396438, -0.052972432, 0.003898177, 0.008811316, -0.0140575245, 0.008241371, -0.0029972931, 0.01286296, 0.0038481436, -0.009655162, -0.022017341, 0.022169532, -0.047312085, 0.010867214, 0.058161795, -0.005888559, 0.036183413, -0.11554807, 0.021823775, -0.03801551, -0.0074358485, 0.03207918, 0.011526804, -0.0031162074, 0.00375316, -0.18667601, 0.00073151966, -0.010986953, -0.0043941163, 0.010395298, -0.025916178, -0.01184541], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, 23, 25, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.77667725, 0.7954955, 2.6566868, 1.4776211, 0.0, 0.0, 0.0, 1.0426044, 0.0, 0.71889365, 0.0, 2.0793207, 0.89676845, 3.002529, 0.0, 5.988918, 1.2972246, 1.2622607, 3.1575913, 0.0, 0.7914984, 0.0, 1.7539026, 0.0, 0.0, 0.0, 0.9791312, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 22, 22, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, 24, 26, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, 1.0, 0.008811316, -0.0140575245, 0.008241371, 3.0, 0.01286296, 1.0, -0.009655162, 1.0, 1.0, 1.0, 0.010867214, 1.0, 1.0, 1.0, 1.0, 0.021823775, 1.0, -0.0074358485, 1.0, 0.011526804, -0.0031162074, 0.00375316, 1.0, 0.00073151966, -0.010986953, -0.0043941163, 0.010395298, -0.025916178, -0.01184541], "split_indices": [119, 0, 50, 84, 0, 0, 0, 0, 0, 16, 0, 0, 126, 5, 0, 122, 69, 106, 127, 0, 58, 0, 12, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1838.0, 224.0, 1718.0, 120.0, 136.0, 88.0, 1628.0, 90.0, 1517.0, 111.0, 629.0, 888.0, 527.0, 102.0, 389.0, 499.0, 237.0, 290.0, 146.0, 243.0, 178.0, 321.0, 109.0, 128.0, 92.0, 198.0, 149.0, 94.0, 156.0, 165.0, 96.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.007485094, 0.011099502, -0.0062655867, 0.006779418, 0.008591365, 0.012205283, -0.0066342573, -0.016673574, 0.027607342, 0.006505625, -0.0072965934, 0.0022420383, 0.12795113, 0.058308296, -0.00682212, 0.027541904, -0.07166891, 0.0048946943, 0.01971614, 0.0010138638, 0.011089348, -0.035145603, 0.14798953, -0.015846891, 0.0008566362, 0.016342197, -0.08952059, 0.04017475, -0.0063137077, -0.005112735, 0.008501659, 0.00065402413, -0.015661072], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, 19, -1, 21, 23, -1, -1, -1, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.52250063, 0.63347846, 0.0, 0.73517764, 0.0, 0.7672696, 0.0, 0.7828873, 2.8634067, 1.6451958, 0.0, 1.6792033, 1.2412126, 0.6357853, 0.0, 5.0513253, 1.5948529, 0.0, 0.0, 0.0, 0.0, 1.2318456, 12.26869, 0.0, 0.0, 1.0471553, 1.3791733, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 21, 21, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, 20, -1, 22, 24, -1, -1, -1, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.0062655867, 1.3461539, 0.008591365, 1.0, -0.0066342573, 1.0, 1.0, 1.0, -0.0072965934, 0.26923078, 1.0, 1.0, -0.00682212, -0.15384616, 1.0, 0.0048946943, 0.01971614, 0.0010138638, 0.011089348, 1.0, 1.0, -0.015846891, 0.0008566362, 1.0, 1.0, 0.04017475, -0.0063137077, -0.005112735, 0.008501659, 0.00065402413, -0.015661072], "split_indices": [117, 1, 0, 1, 0, 17, 0, 61, 61, 12, 0, 1, 113, 97, 0, 1, 124, 0, 0, 0, 0, 109, 69, 0, 0, 5, 15, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 1853.0, 107.0, 1725.0, 128.0, 600.0, 1125.0, 425.0, 175.0, 898.0, 227.0, 251.0, 174.0, 669.0, 229.0, 106.0, 121.0, 131.0, 120.0, 440.0, 229.0, 110.0, 119.0, 226.0, 214.0, 104.0, 125.0, 114.0, 112.0, 88.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0039874725, -0.010367009, 0.032689653, -0.035044353, 0.01467655, 0.011206626, -0.009170629, -0.09581398, 0.016502433, 0.10133676, -0.010301977, 0.0027807732, -0.0050430726, -0.14531778, -0.047271404, 0.09006666, -0.032795805, 0.023590557, -0.0027849264, 0.046812892, -0.07305783, -0.020595862, -0.008096422, 0.0048585073, -0.013425231, -0.00019048109, 0.017384268, -0.013135097, 0.0060432022, 0.013567942, 0.00963764, -0.121555366, 0.004384672, 0.00940626, -0.005838821, -0.019511718, -0.0066524367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1], "loss_changes": [0.4848128, 1.0907853, 1.0200777, 2.7847743, 1.896229, 0.0, 0.30667198, 0.9804406, 1.7443885, 3.4073448, 2.437319, 0.0, 0.0, 0.7882948, 1.717563, 1.4870663, 2.6461682, 0.0, 0.0, 1.1760938, 1.8369449, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4415133, 0.927032, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 30, 30, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.011206626, 1.0, 1.0, -0.34615386, 0.03846154, 1.0, 0.0027807732, -0.0050430726, 1.0, 1.0, -0.5, 1.0, 0.023590557, -0.0027849264, 0.0, 1.0, -0.020595862, -0.008096422, 0.0048585073, -0.013425231, -0.00019048109, 0.017384268, -0.013135097, 0.0060432022, 0.013567942, 1.0, 1.0, 0.004384672, 0.00940626, -0.005838821, -0.019511718, -0.0066524367], "split_indices": [1, 124, 137, 15, 81, 0, 106, 106, 1, 1, 15, 0, 0, 12, 12, 1, 115, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1765.0, 307.0, 889.0, 876.0, 106.0, 201.0, 408.0, 481.0, 196.0, 680.0, 106.0, 95.0, 202.0, 206.0, 193.0, 288.0, 96.0, 100.0, 356.0, 324.0, 104.0, 98.0, 98.0, 108.0, 92.0, 101.0, 140.0, 148.0, 105.0, 251.0, 229.0, 95.0, 112.0, 139.0, 98.0, 131.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.004222834, -0.0025918782, 0.043053426, -0.03270849, 0.027904639, 0.019631319, -0.018250464, -0.079539634, 0.007151976, 0.05261558, -0.016227668, -0.008704393, 0.0058480683, -0.12660593, -0.03200732, 0.083674885, -0.04306617, 0.020657519, 0.019930305, -0.009096891, 0.034132723, -0.018050635, -0.0070549445, 0.005560393, -0.010979304, -0.0003569956, 0.01664226, -0.015980188, 0.0219272, -0.023896625, 0.01891279, -0.00034225892, 0.0073751546, -0.002885288, 0.0072161295, -0.074055724, 0.007895057, 0.002604432, -0.0152236065], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1, 37, -1, -1, -1], "loss_changes": [0.5459097, 1.6118828, 2.89379, 1.6483061, 0.9509604, 0.0, 1.1612926, 0.90829015, 1.8330342, 2.6205056, 1.1781316, 0.0, 0.0, 0.6163788, 1.3766108, 1.3644503, 2.1850698, 3.4452794, 0.0, 0.0, 0.27823704, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.47191504, 1.8726163, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9095093, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 28, 28, 29, 29, 35, 35], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1, 38, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 0.115384616, 0.019631319, 1.0, 1.0, -0.34615386, 1.0, 0.53846157, -0.008704393, 0.0058480683, 1.0, 1.0, -0.5, 1.0, -0.115384616, 0.019930305, -0.009096891, 1.0, -0.018050635, -0.0070549445, 0.005560393, -0.010979304, -0.0003569956, 0.01664226, -0.015980188, 1.0, 1.0, 0.01891279, -0.00034225892, 0.0073751546, -0.002885288, 0.0072161295, 1.0, 0.007895057, 0.002604432, -0.0152236065], "split_indices": [1, 124, 108, 15, 1, 0, 122, 106, 1, 64, 1, 0, 0, 12, 12, 1, 59, 1, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 39, 61, 0, 0, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1755.0, 308.0, 883.0, 872.0, 88.0, 220.0, 406.0, 477.0, 559.0, 313.0, 116.0, 104.0, 204.0, 202.0, 189.0, 288.0, 459.0, 100.0, 126.0, 187.0, 104.0, 100.0, 95.0, 107.0, 92.0, 97.0, 103.0, 185.0, 363.0, 96.0, 96.0, 91.0, 92.0, 93.0, 244.0, 119.0, 107.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006330553, 0.0077149295, -0.0050100707, -0.00027662225, -0.0084393015, -0.0124114, 0.036664642, 0.006944982, -0.054995447, -0.011059559, 0.07690195, -0.016289622, 0.060029607, -0.0120368125, -0.011881259, 0.024178265, 0.023448057, 0.03596202, -0.042714372, -0.0031266504, 0.108591385, 0.0093854265, -0.008172, -0.0037057118, 0.011517293, 0.012792935, -0.0032108263, 0.0010635245, -0.0648139, 0.01889476, 0.0021092347, -0.034997925, -0.011307532, -0.010142684, 0.0022964529], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, 13, -1, 15, 17, 19, 21, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [0.6965698, 0.0, 0.7278374, 0.81944484, 0.0, 1.1342, 2.6782608, 1.1667967, 1.1788435, 0.0, 2.9493837, 0.9085246, 1.2768488, 1.8963586, 0.0, 1.482177, 0.0, 1.3835139, 0.51522374, 0.0, 1.3218446, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.4446385, 0.0, 0.0, 0.73542345, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 20, 20, 28, 28, 31, 31], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, 14, -1, 16, 18, 20, 22, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [-0.5769231, 0.0077149295, 1.0, 1.0, -0.0084393015, 1.0, 1.0, 1.0, 1.0, -0.011059559, 1.0, 1.0, 1.0, 1.0, -0.011881259, 1.0, 0.023448057, 0.61538464, -1.0, -0.0031266504, 1.0, 0.0093854265, -0.008172, -0.0037057118, 0.011517293, 0.012792935, -0.0032108263, 0.0010635245, 1.0, 0.01889476, 0.0021092347, 1.0, -0.011307532, -0.010142684, 0.0022964529], "split_indices": [1, 0, 43, 0, 0, 121, 5, 71, 12, 0, 61, 53, 69, 69, 0, 15, 0, 1, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 17, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2046.0, 109.0, 1937.0, 1828.0, 109.0, 1376.0, 452.0, 946.0, 430.0, 97.0, 355.0, 658.0, 288.0, 257.0, 173.0, 266.0, 89.0, 221.0, 437.0, 100.0, 188.0, 102.0, 155.0, 159.0, 107.0, 94.0, 127.0, 128.0, 309.0, 98.0, 90.0, 191.0, 118.0, 89.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010301893, -0.0052241734, 0.0044408618, 0.002265542, -0.06409195, -0.0074224994, 0.010653057, -0.0014690876, -0.012597327, -0.06692651, 0.010028674, -0.01740882, -0.017595626, 0.04098187, -0.021343509, -0.011208142, 0.00983021, 0.0092789205, 0.014024488, -0.010185509, 0.0031831914, 0.067788884, -0.078970894, -0.029346528, 0.01257689, -0.0018805823, 0.014536333, -0.008080318, -0.0077076824, -0.010175361, 0.019944668, 0.0108221965, -0.004464845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, 19, -1, -1, 21, -1, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.3946696, 0.83595216, 0.0, 1.6990288, 0.6541983, 1.5981206, 0.0, 0.0, 0.0, 1.8842162, 1.1555725, 2.629117, 0.0, 1.8850105, 1.1670382, 0.0, 0.0, 2.3442261, 0.0, 0.0, 1.8064188, 1.833888, 0.0006276369, 1.2777138, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2145487, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 20, 20, 21, 21, 22, 22, 23, 23, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, 20, -1, -1, 22, -1, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.3461539, 0.0044408618, 0.88461536, 1.0, 1.0, 0.010653057, -0.0014690876, -0.012597327, 1.0, 1.0, -0.30769232, -0.017595626, 1.0, 1.0, -0.011208142, 0.00983021, 1.0, 0.014024488, -0.010185509, 1.0, 1.0, -0.1923077, -0.15384616, 0.01257689, -0.0018805823, 0.014536333, -0.008080318, -0.0077076824, -0.010175361, 0.1923077, 0.0108221965, -0.004464845], "split_indices": [31, 1, 0, 1, 50, 89, 0, 0, 0, 106, 126, 1, 0, 0, 59, 0, 0, 105, 0, 0, 105, 93, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1896.0, 175.0, 1682.0, 214.0, 1539.0, 143.0, 119.0, 95.0, 349.0, 1190.0, 240.0, 109.0, 599.0, 591.0, 132.0, 108.0, 454.0, 145.0, 138.0, 453.0, 273.0, 181.0, 358.0, 95.0, 129.0, 144.0, 92.0, 89.0, 145.0, 213.0, 90.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00080786226, 0.0058845286, -0.008702293, -0.0021938086, 0.05361238, -0.02114081, 0.016844062, -0.0020944437, 0.088949226, -0.06545769, 0.017253984, 0.109693214, -0.009439386, 0.01237031, 0.0056301616, -0.011893359, -0.032258052, 0.08307633, -0.027613671, 0.023629962, -0.0014190438, 0.04794321, -0.06971827, 0.0025075306, -0.0068530194, -0.0010001101, 0.017215047, -0.013324556, 0.006398319, -0.0030760164, 0.01243181, -0.013132237, -0.001508129], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.92209536, 0.7537733, 0.0, 0.6031081, 0.7455927, 1.4258885, 2.0352907, 0.0, 0.21784854, 0.69062257, 1.3260293, 2.885942, 2.2483232, 0.0, 0.0, 0.0, 0.49910504, 1.5089248, 2.583372, 0.0, 0.0, 2.0016503, 1.0669779, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2692307, -0.008702293, 1.0, 1.0, 1.0, 1.0, -0.0020944437, 1.0, 0.03846154, -0.34615386, 0.03846154, 1.0, 0.01237031, 0.0056301616, -0.011893359, 1.0, -0.5, 1.0, 0.023629962, -0.0014190438, 1.0, -0.34615386, 0.0025075306, -0.0068530194, -0.0010001101, 0.017215047, -0.013324556, 0.006398319, -0.0030760164, 0.01243181, -0.013132237, -0.001508129], "split_indices": [43, 1, 0, 124, 69, 15, 81, 0, 12, 1, 1, 1, 15, 0, 0, 0, 17, 1, 115, 0, 0, 71, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1955.0, 113.0, 1672.0, 283.0, 838.0, 834.0, 91.0, 192.0, 389.0, 449.0, 184.0, 650.0, 93.0, 99.0, 149.0, 240.0, 182.0, 267.0, 91.0, 93.0, 333.0, 317.0, 93.0, 147.0, 89.0, 93.0, 124.0, 143.0, 164.0, 169.0, 149.0, 168.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "2.2236364E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}