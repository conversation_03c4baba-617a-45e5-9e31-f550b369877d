{"learner": {"attributes": {"best_iteration": "34", "best_score": "0.973029"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "85"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.003705224, 0.1515607, -0.23360746, -0.026279945, 0.22391263, -0.27031472, -0.0016555766, -0.024330286, 0.04579986, 0.15135463, 0.46774936, -0.21792927, -0.40640602, -0.003111367, 0.011283937, 0.24820451, 0.049092516, 0.05467251, 0.040005587, -0.28408182, -0.15415819, -0.04624855, -0.034339536, 0.314147, 0.011849946, -0.012295197, 0.120777726, -0.03412865, -0.02099881, -0.009700197, -0.022690244, 0.03865051, 0.019726079, 0.020678602, -0.00060179795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [72.6672, 16.418438, 6.3340607, 5.7722564, 16.046925, 4.8478584, 0.0, 0.0, 1.4282804, 6.922947, 1.1119919, 2.0713406, 0.6678486, 0.0, 0.0, 3.070551, 4.1932354, 0.0, 0.0, 1.0214806, 1.0394459, 0.0, 0.0, 2.0129242, 0.0, 0.0, 2.6173162, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0016555766, -0.024330286, 1.0, 1.0, 1.0, 1.0, 1.0, -0.003111367, 0.011283937, 1.0, 1.0, 0.05467251, 0.040005587, 1.0, 1.0, -0.04624855, -0.034339536, 1.0, 0.011849946, -0.012295197, 1.0, -0.03412865, -0.02099881, -0.009700197, -0.022690244, 0.03865051, 0.019726079, 0.020678602, -0.00060179795], "split_indices": [137, 17, 71, 71, 61, 116, 0, 0, 109, 106, 39, 39, 111, 0, 0, 115, 5, 0, 0, 109, 109, 0, 0, 97, 0, 0, 109, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1276.0, 795.0, 369.0, 907.0, 680.0, 115.0, 92.0, 277.0, 699.0, 208.0, 491.0, 189.0, 129.0, 148.0, 359.0, 340.0, 96.0, 112.0, 241.0, 250.0, 100.0, 89.0, 238.0, 121.0, 100.0, 240.0, 136.0, 105.0, 140.0, 110.0, 147.0, 91.0, 143.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025964463, -0.17679897, 0.18025354, -0.2511295, -0.022316838, 0.108931944, 0.37953448, -0.28954172, -0.0027121552, 0.047084387, -0.018802999, 0.18454365, -0.031489804, 0.0149473725, 0.055060524, -0.23258044, -0.3706471, -0.00044720163, 0.012810163, -0.0015706121, 0.25021696, 0.007456722, -0.015858296, -0.014649819, -0.29467955, -0.032398105, -0.039907936, 0.18869014, 0.042961624, -0.022941414, -0.036327478, 0.033258352, 0.11358093, 0.020770991, 0.0019451905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [65.46276, 11.735359, 14.667854, 5.9372025, 3.8182318, 8.069323, 10.705009, 2.7211037, 0.0, 0.9774085, 0.0, 6.496628, 3.585447, 0.0, 0.0, 1.8495884, 0.3224144, 0.0, 0.0, 0.0, 4.1060867, 0.0, 0.0, 0.0, 0.89985466, 0.0, 0.0, 2.9937382, 0.0, 0.0, 0.0, 0.0, 1.6125681, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 20, 20, 24, 24, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 3.5, 1.0, 0.15384616, 1.0, 1.0, -0.0027121552, 1.0, -0.018802999, 1.0, 1.0, 0.0149473725, 0.055060524, 1.0, 0.30769232, -0.00044720163, 0.012810163, -0.0015706121, 1.0, 0.007456722, -0.015858296, -0.014649819, 1.0, -0.032398105, -0.039907936, 1.0, 0.042961624, -0.022941414, -0.036327478, 0.033258352, -0.30769232, 0.020770991, 0.0019451905], "split_indices": [71, 2, 113, 1, 7, 1, 109, 23, 0, 124, 0, 5, 124, 0, 0, 81, 1, 0, 0, 0, 0, 0, 0, 0, 106, 0, 0, 121, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1022.0, 1032.0, 690.0, 332.0, 760.0, 272.0, 589.0, 101.0, 234.0, 98.0, 494.0, 266.0, 116.0, 156.0, 346.0, 243.0, 143.0, 91.0, 122.0, 372.0, 145.0, 121.0, 145.0, 201.0, 92.0, 151.0, 277.0, 95.0, 103.0, 98.0, 95.0, 182.0, 91.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023338485, 0.12119738, -0.2014774, 0.07792531, 0.042848386, -0.24133146, -0.0920667, -0.10299693, 0.14577115, -0.2783706, -0.01558889, -0.021435982, 0.0042334627, -0.1517369, 4.511557e-05, 0.08106022, 0.30964947, -0.03681162, -0.24666047, -0.022463575, -0.0074507403, -0.0049006892, 0.12591095, 0.014609146, 0.053559553, -0.031448726, -0.018731201, 0.0040286966, 0.17236651, 0.0086640995, 0.028978884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [51.021328, 17.020058, 3.4622078, 13.77235, 0.0, 1.8418655, 3.4845085, 1.5428698, 8.653448, 1.155407, 0.0, 0.0, 0.0, 1.1710272, 0.0, 3.4126596, 8.536669, 0.0, 1.2076244, 0.0, 0.0, 0.0, 1.7303033, 0.0, 0.0, 0.0, 0.0, 0.0, 2.8386364, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 18, 18, 22, 22, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.042848386, 1.0, 1.0, 1.0, 1.0, 1.0, -0.01558889, -0.021435982, 0.0042334627, 1.0, 4.511557e-05, 1.0, 1.0, -0.03681162, 1.0, -0.022463575, -0.0074507403, -0.0049006892, 1.0, 0.014609146, 0.053559553, -0.031448726, -0.018731201, 0.0040286966, 1.0, 0.0086640995, 0.028978884], "split_indices": [137, 125, 93, 17, 0, 50, 13, 50, 113, 26, 0, 0, 0, 105, 0, 5, 61, 0, 69, 0, 0, 0, 59, 0, 0, 0, 0, 0, 15, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1280.0, 794.0, 1122.0, 158.0, 582.0, 212.0, 306.0, 816.0, 406.0, 176.0, 111.0, 101.0, 208.0, 98.0, 585.0, 231.0, 106.0, 300.0, 107.0, 101.0, 150.0, 435.0, 134.0, 97.0, 140.0, 160.0, 153.0, 282.0, 163.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019281867, 0.1064394, -0.17685658, 0.07066718, 0.036234833, -0.20390695, 0.00010516287, 0.038404096, 0.04174104, -0.17216118, -0.034180265, -0.10312598, 0.09188375, -0.056149628, -0.22815606, -0.017473718, 0.00092181563, 0.17840028, -0.021939553, -0.0143687725, 0.003433916, -0.274578, -0.010390909, 0.29406333, -0.013307824, -0.013329237, 0.009809009, -0.021346485, -0.031336837, 0.013502769, 0.36863503, 0.025139729, 0.046823524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [39.05053, 11.644436, 3.7922363, 12.484697, 0.0, 2.9942837, 0.0, 7.727928, 0.0, 3.611805, 0.0, 2.2526276, 7.29707, 1.4337405, 2.162918, 0.0, 0.0, 15.16718, 4.2770042, 0.0, 0.0, 0.6471729, 0.0, 3.6408844, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4404697, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 23, 23, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 3.5, 5.0, 0.036234833, 1.0, 0.00010516287, 1.0, 0.04174104, 0.0, -0.034180265, 1.0, 1.0, 1.0, 1.0, -0.017473718, 0.00092181563, 0.115384616, -0.15384616, -0.0143687725, 0.003433916, 1.0, -0.010390909, 1.0, -0.013307824, -0.013329237, 0.009809009, -0.021346485, -0.031336837, 0.013502769, 1.0, 0.025139729, 0.046823524], "split_indices": [137, 125, 1, 0, 0, 0, 0, 17, 0, 1, 0, 124, 122, 109, 93, 0, 0, 1, 1, 0, 0, 53, 0, 124, 0, 0, 0, 0, 0, 0, 109, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1272.0, 788.0, 1116.0, 156.0, 684.0, 104.0, 1021.0, 95.0, 556.0, 128.0, 280.0, 741.0, 181.0, 375.0, 171.0, 109.0, 421.0, 320.0, 92.0, 89.0, 273.0, 102.0, 307.0, 114.0, 166.0, 154.0, 106.0, 167.0, 98.0, 209.0, 96.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00066245365, -0.15563136, 0.103731215, -0.1818752, 0.0014476393, -0.045137558, 0.16314521, -0.15865418, -0.033478804, 0.008163909, -0.10034675, 0.10096515, 0.3282791, -0.12678342, -0.028460745, -0.020174118, -0.003630815, -0.00593361, 0.13777056, 0.0144966785, 0.058347862, -0.075038135, -0.023380935, 0.0012379702, 0.19800045, -0.008649824, -0.017487016, 0.26660356, 0.0017392231, -0.010810668, 0.010127614, 0.008177367, 0.045725483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, 15, 17, 19, 21, -1, -1, -1, -1, 23, -1, -1, 25, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [33.361763, 3.6741028, 11.03842, 2.5317116, 0.0, 2.4917283, 9.159086, 2.484805, 0.0, 0.0, 1.6103022, 3.8231707, 11.414616, 2.7358136, 0.0, 0.0, 0.0, 0.0, 3.9800491, 0.0, 0.0, 2.2070172, 0.0, 0.0, 4.410941, 2.186578, 0.0, 9.091415, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 18, 18, 21, 21, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, 16, 18, 20, 22, -1, -1, -1, -1, 24, -1, -1, 26, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.5, 1.0, 1.0, 0.0014476393, -0.30769232, 1.0, 1.0, -0.033478804, 0.008163909, 1.0, -1.0, 1.0, 0.7307692, -0.028460745, -0.020174118, -0.003630815, -0.00593361, 1.0, 0.0144966785, 0.058347862, 1.0, -0.023380935, 0.0012379702, 1.0, 1.0, -0.017487016, -0.15384616, 0.0017392231, -0.010810668, 0.010127614, 0.008177367, 0.045725483], "split_indices": [2, 1, 17, 40, 0, 1, 113, 0, 0, 0, 105, 0, 61, 1, 0, 0, 0, 0, 81, 0, 0, 80, 0, 0, 108, 108, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 823.0, 1248.0, 713.0, 110.0, 356.0, 892.0, 619.0, 94.0, 108.0, 248.0, 648.0, 244.0, 494.0, 125.0, 96.0, 152.0, 121.0, 527.0, 142.0, 102.0, 333.0, 161.0, 171.0, 356.0, 200.0, 133.0, 258.0, 98.0, 105.0, 95.0, 131.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0013330015, -0.120818235, 0.12277884, -0.086811885, -0.2502005, 0.032571625, 0.2192754, -0.1481784, -0.03409247, -0.019842321, -0.030650202, -0.11800495, 0.23052791, 0.11752916, 0.35732543, -0.22948724, 0.0014439288, 0.0139883, -0.096379995, -0.048755214, -0.026210034, 0.00908363, 0.03420646, 0.18929656, -0.0024525854, 0.027378682, 0.047371633, -0.01548802, -0.030291935, -0.038377702, -0.019241655, -0.015404329, 0.0036214085, 0.028559534, 0.008678494, -0.008888529, 0.0024476147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, 21, 23, 25, 27, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [30.737621, 4.5450125, 9.044168, 2.646398, 0.6267538, 16.00667, 7.0511265, 4.9980145, 4.7680597, 0.0, 0.0, 3.0434623, 3.61473, 2.946332, 2.0710258, 1.3805933, 0.0, 0.0, 1.80479, 1.8429285, 0.0, 0.0, 0.0, 1.8953738, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6412686, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, 22, 24, 26, 28, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, -0.019842321, -0.030650202, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0014439288, 0.0139883, 1.0, 1.0, -0.026210034, 0.00908363, 0.03420646, 1.0, -0.0024525854, 0.027378682, 0.047371633, -0.01548802, -0.030291935, 1.0, -0.019241655, -0.015404329, 0.0036214085, 0.028559534, 0.008678494, -0.008888529, 0.0024476147], "split_indices": [71, 116, 39, 39, 12, 42, 50, 42, 0, 0, 0, 106, 13, 108, 15, 108, 0, 0, 109, 108, 0, 0, 0, 13, 0, 0, 0, 0, 0, 108, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1033.0, 1039.0, 818.0, 215.0, 537.0, 502.0, 378.0, 440.0, 112.0, 103.0, 305.0, 232.0, 289.0, 213.0, 252.0, 126.0, 116.0, 324.0, 206.0, 99.0, 103.0, 129.0, 192.0, 97.0, 124.0, 89.0, 125.0, 127.0, 202.0, 122.0, 92.0, 114.0, 99.0, 93.0, 112.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.004506907, 0.07299115, -0.12817194, 0.03824851, 0.031862378, -0.15260844, 0.0035766058, 0.088357784, -0.08384436, -0.005285305, -0.1861818, 0.033635724, 0.27157086, -0.02260456, 0.008221611, -0.23192587, -0.113677435, 0.09684661, -0.04244719, 0.0072415657, 0.046001878, -0.19295275, -0.032871623, -0.0029139405, -0.021700164, 0.018226046, 0.02215339, -0.010985012, 0.008124064, -0.029378464, -0.012244622, 0.010065074, -0.008526271], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [19.75221, 10.812482, 3.1808205, 6.790964, 0.0, 2.3142452, 0.0, 7.8903017, 7.6273246, 0.0, 1.7147026, 2.914417, 6.7929964, 0.0, 0.0, 1.1957932, 1.7469645, 3.2447875, 2.2926538, 0.0, 0.0, 1.6067019, 0.0, 0.0, 0.0, 1.7315961, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 3.5, 0.1923077, 0.031862378, 0.0, 0.0035766058, 2.0, 0.84615386, -0.005285305, 1.0, 1.0, -0.30769232, -0.02260456, 0.008221611, 0.84615386, 1.0, 1.0, -0.1923077, 0.0072415657, 0.046001878, -0.1923077, -0.032871623, -0.0029139405, -0.021700164, -0.30769232, 0.02215339, -0.010985012, 0.008124064, -0.029378464, -0.012244622, 0.010065074, -0.008526271], "split_indices": [137, 125, 1, 1, 0, 0, 0, 0, 1, 0, 115, 122, 1, 0, 0, 1, 23, 12, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1267.0, 794.0, 1110.0, 157.0, 691.0, 103.0, 787.0, 323.0, 174.0, 517.0, 606.0, 181.0, 174.0, 149.0, 317.0, 200.0, 331.0, 275.0, 88.0, 93.0, 226.0, 91.0, 110.0, 90.0, 203.0, 128.0, 178.0, 97.0, 93.0, 133.0, 113.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.005135743, -0.09813075, 0.10701211, -0.065219775, -0.2201127, 0.058741476, 0.24063082, -0.041351486, -0.024651714, -0.015416418, -0.03139061, 0.018499725, 0.19025779, 0.008148138, 0.036487002, -0.013051072, -0.015808566, 0.07325051, -0.08298952, 0.030578926, 0.0078535, -0.057415176, 0.0076062656, 0.00038061192, 0.025917087, -0.022469703, 0.00346783, -0.093493514, 0.0057956167, 0.0071631223, -0.0048439233, 0.0024062793, -0.14958638, -0.017897578, -0.012221388], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, -1, 21, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [21.735176, 4.118922, 6.7078524, 3.496423, 1.3484459, 4.043429, 5.4572363, 1.6260539, 0.0, 0.0, 0.0, 3.2506201, 2.310441, 0.0, 0.0, 0.0, 2.12146, 5.148239, 3.4182553, 0.0, 0.0, 1.590039, 0.0, 0.9496151, 0.0, 0.0, 0.0, 1.9188747, 0.0, 0.0, 0.0, 0.0, 0.15847826, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 16, 16, 17, 17, 18, 18, 21, 21, 23, 23, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, -1, 22, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.024651714, -0.015416418, -0.03139061, 0.1923077, -0.1923077, 0.008148138, 0.036487002, -0.013051072, 1.0, -0.115384616, 0.84615386, 0.030578926, 0.0078535, 1.4230769, 0.0076062656, 1.0, 0.025917087, -0.022469703, 0.00346783, -0.115384616, 0.0057956167, 0.0071631223, -0.0048439233, 0.0024062793, 1.0, -0.017897578, -0.012221388], "split_indices": [71, 116, 113, 40, 109, 58, 109, 5, 0, 0, 0, 1, 1, 0, 0, 0, 127, 1, 1, 0, 0, 1, 0, 121, 0, 0, 0, 1, 0, 0, 0, 0, 111, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1026.0, 1040.0, 808.0, 218.0, 764.0, 276.0, 714.0, 94.0, 128.0, 90.0, 585.0, 179.0, 121.0, 155.0, 159.0, 555.0, 380.0, 205.0, 88.0, 91.0, 382.0, 173.0, 273.0, 107.0, 93.0, 112.0, 291.0, 91.0, 111.0, 162.0, 94.0, 197.0, 95.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018830104, -0.09014055, 0.08663134, -0.06048334, -0.20115103, 0.011621386, 0.19797142, -0.08410157, 0.0104844235, -0.014734747, -0.028063355, -0.039902624, 0.1024238, 0.28302544, -0.0069948784, -0.02583188, -0.12830615, -0.09376855, 0.012447173, 0.0017883122, 0.023211686, 0.39816466, 0.0028074307, -0.0150722405, 0.04962279, -0.16881903, -0.0038277505, 0.00183073, -0.16198865, 0.062136065, 0.01600889, 0.014813155, -0.0050959806, -0.02346702, -0.011571324, -0.011420811, -0.020874163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, 31, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.131903, 3.4041977, 8.610514, 3.1862717, 0.9322634, 2.8819587, 9.456894, 1.8391109, 0.0, 0.0, 0.0, 3.4796913, 2.4450467, 9.246784, 0.0, 2.9024608, 1.4808116, 2.2631648, 0.0, 0.0, 0.0, 11.530849, 0.0, 0.0, 1.9023877, 0.9791813, 0.0, 0.0, 0.41103363, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, 32, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0104844235, -0.014734747, -0.028063355, 1.0, 1.0, 1.0, -0.0069948784, 1.0, 1.0, 1.0, 0.012447173, 0.0017883122, 0.023211686, 1.0, 0.0028074307, -0.0150722405, 0.115384616, 1.0, -0.0038277505, 0.00183073, -0.115384616, 0.062136065, 0.01600889, 0.014813155, -0.0050959806, -0.02346702, -0.011571324, -0.011420811, -0.020874163], "split_indices": [71, 116, 50, 121, 109, 111, 106, 106, 0, 0, 0, 125, 106, 97, 0, 17, 93, 121, 0, 0, 0, 126, 0, 0, 1, 39, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1034.0, 1031.0, 816.0, 218.0, 616.0, 415.0, 714.0, 102.0, 130.0, 88.0, 393.0, 223.0, 315.0, 100.0, 308.0, 406.0, 296.0, 97.0, 135.0, 88.0, 217.0, 98.0, 116.0, 192.0, 280.0, 126.0, 112.0, 184.0, 112.0, 105.0, 97.0, 95.0, 125.0, 155.0, 91.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034883479, -0.08218647, 0.07460323, -0.05796987, -0.17238772, 0.0017600212, 0.1520806, -0.078611515, 0.008571215, -0.012104827, -0.021670638, -0.11990997, 0.16187985, 0.07810776, 0.25036857, -0.052155092, -0.026561027, -0.00023774919, -0.025050161, 0.0031734612, 0.026660606, 0.1806051, -0.011356224, 0.042021655, 0.009265257, 0.0260301, -0.105405554, 0.012713379, 0.023129141, 0.01304804, -0.013539306, -0.16035339, -2.691095e-05, -0.020733712, -0.009540528], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [12.709226, 2.2498975, 5.85816, 2.408257, 0.4960127, 10.422752, 3.6571302, 3.5125947, 0.0, 0.0, 0.0, 4.666022, 3.1484413, 5.6383066, 5.786151, 2.589634, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5068178, 0.0, 0.0, 0.0, 4.2488956, 2.137497, 0.0, 0.0, 0.0, 0.0, 0.7415147, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 21, 21, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.07692308, 1.0, 0.008571215, -0.012104827, -0.021670638, -0.07692308, 1.0, -0.30769232, 0.34615386, 1.0, -0.026561027, -0.00023774919, -0.025050161, 0.0031734612, 0.026660606, 1.0, -0.011356224, 0.042021655, 0.009265257, 1.0, 1.0, 0.012713379, 0.023129141, 0.01304804, -0.013539306, 1.0, -2.691095e-05, -0.020733712, -0.009540528], "split_indices": [71, 116, 39, 121, 108, 42, 1, 40, 0, 0, 0, 1, 13, 1, 1, 122, 0, 0, 0, 0, 0, 69, 0, 0, 0, 113, 80, 0, 0, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1030.0, 1038.0, 812.0, 218.0, 535.0, 503.0, 710.0, 102.0, 101.0, 117.0, 304.0, 231.0, 287.0, 216.0, 622.0, 88.0, 160.0, 144.0, 103.0, 128.0, 187.0, 100.0, 104.0, 112.0, 252.0, 370.0, 91.0, 96.0, 153.0, 99.0, 243.0, 127.0, 141.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035280492, -0.078908, 0.071851894, -0.04719745, -0.1398138, 0.03769926, 0.19012941, 0.0019521748, -0.14293239, -0.02074427, -0.08045865, -0.00036966242, 0.026445765, 0.032659743, 0.008777837, 0.011364559, -0.03752857, -0.017043179, -0.010780933, -0.014909305, -0.0018756986, -0.0926079, 0.060378034, 0.00867081, -0.0853519, -0.016697, 0.001045532, 0.104949236, -0.00755204, -0.002111472, -0.01479964, 0.17551056, -0.0005849511, 0.033480804, 0.002596592], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, -1, -1, -1, -1, 23, -1, -1, -1, -1, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [11.716565, 1.9912281, 4.164713, 3.1902184, 1.4169846, 6.9059587, 3.2265253, 1.9755635, 0.22214794, 0.0, 0.79615235, 3.8382337, 0.0, 0.0, 0.0, 0.0, 1.966607, 0.0, 0.0, 0.0, 0.0, 2.0846074, 2.5016055, 0.0, 0.96176076, 0.0, 0.0, 2.431432, 0.0, 0.0, 0.0, 4.5261955, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 16, 16, 21, 21, 22, 22, 24, 24, 27, 27, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, -1, -1, -1, -1, 24, -1, -1, -1, -1, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, -0.02074427, 1.0, 1.0, 0.026445765, 0.032659743, 0.008777837, 0.011364559, 1.0, -0.017043179, -0.010780933, -0.014909305, -0.0018756986, 1.0, 1.0, 0.00867081, 1.0, -0.016697, 0.001045532, 1.0, -0.00755204, -0.002111472, -0.01479964, 1.0, -0.0005849511, 0.033480804, 0.002596592], "split_indices": [71, 23, 0, 80, 80, 125, 69, 0, 12, 0, 126, 93, 0, 0, 0, 0, 111, 0, 0, 0, 0, 59, 23, 0, 106, 0, 0, 105, 0, 0, 0, 126, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1031.0, 1031.0, 678.0, 353.0, 800.0, 231.0, 448.0, 230.0, 165.0, 188.0, 685.0, 115.0, 99.0, 132.0, 117.0, 331.0, 129.0, 101.0, 89.0, 99.0, 272.0, 413.0, 92.0, 239.0, 158.0, 114.0, 311.0, 102.0, 118.0, 121.0, 190.0, 121.0, 92.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020720495, -0.06645458, 0.06281347, -0.050207432, -0.017440783, -0.016199734, 0.14529632, -0.06643928, 0.009901484, 0.03809851, -0.026725614, 0.054413658, 0.2681799, -0.12160975, 0.0034124022, 0.089810275, -0.0092452485, -0.006089628, 0.023041306, 0.03761344, 0.01906964, -0.16925862, -0.07914441, 0.011067723, -0.05654347, 0.014392984, 0.021463886, -0.022935512, -0.008389427, -0.01588807, -0.0009998059, -0.015049048, 0.0038227614, 0.012665962, -0.008261408], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.588927, 1.8100581, 6.6736474, 2.172671, 0.0, 7.129496, 5.5951614, 3.1176844, 0.0, 2.9029396, 0.0, 5.844811, 1.7816772, 0.9145875, 2.295923, 2.8995836, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0927105, 1.3177198, 0.0, 2.038893, 2.091006, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 3.3846154, -0.017440783, 0.61538464, 1.0, 1.0, 0.009901484, 1.0, -0.026725614, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0092452485, -0.006089628, 0.023041306, 0.03761344, 0.01906964, 1.0, 1.0, 0.011067723, 0.07692308, 1.0, 0.021463886, -0.022935512, -0.008389427, -0.01588807, -0.0009998059, -0.015049048, 0.0038227614, 0.012665962, -0.008261408], "split_indices": [71, 40, 39, 1, 0, 1, 50, 83, 0, 106, 0, 122, 109, 17, 122, 50, 0, 0, 0, 0, 0, 106, 109, 0, 1, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1032.0, 1024.0, 897.0, 135.0, 523.0, 501.0, 809.0, 88.0, 430.0, 93.0, 288.0, 213.0, 452.0, 357.0, 308.0, 122.0, 174.0, 114.0, 89.0, 124.0, 213.0, 239.0, 128.0, 229.0, 192.0, 116.0, 125.0, 88.0, 111.0, 128.0, 115.0, 114.0, 89.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010369498, -0.065313414, 0.06367762, -0.12497819, -0.03078982, -0.013331173, 0.1470908, -0.0054281927, -0.16927712, 0.05944827, -0.07995256, 0.056166667, -0.027817432, 0.22684431, 0.044814296, -0.02154551, -0.011298395, 0.017134707, -0.0023003458, -0.1309633, -0.030831099, -0.05621556, 0.18882404, 0.031997018, 0.01655039, -0.007840462, 0.0210892, -0.010498708, -0.015548288, -0.011479301, 0.0028008327, -0.013315502, 0.003306323, 0.0010130596, 0.03434626], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, -1, 23, 25, -1, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.572984, 2.1298766, 6.5969853, 1.1869402, 2.9058106, 9.828818, 4.0213547, 0.0, 0.6056838, 2.131264, 1.0624259, 6.306225, 0.0, 1.5823288, 4.420206, 0.0, 0.0, 0.0, 0.0, 0.13247967, 1.0670987, 1.5730155, 5.3607817, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 19, 19, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, -1, 24, 26, -1, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 1.0, -0.0054281927, 1.0, 1.0, 0.15384616, 1.0, -0.027817432, 1.0, 1.0, -0.02154551, -0.011298395, 0.017134707, -0.0023003458, -0.26923078, 1.0, 1.0, -0.46153846, 0.031997018, 0.01655039, -0.007840462, 0.0210892, -0.010498708, -0.015548288, -0.011479301, 0.0028008327, -0.013315502, 0.003306323, 0.0010130596, 0.03434626], "split_indices": [71, 59, 39, 53, 122, 1, 53, 0, 12, 137, 1, 42, 0, 109, 122, 0, 0, 0, 0, 1, 39, 124, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1034.0, 1027.0, 379.0, 655.0, 534.0, 493.0, 146.0, 233.0, 231.0, 424.0, 423.0, 111.0, 277.0, 216.0, 128.0, 105.0, 98.0, 133.0, 208.0, 216.0, 229.0, 194.0, 110.0, 167.0, 124.0, 92.0, 101.0, 107.0, 89.0, 127.0, 123.0, 106.0, 90.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019244982, 0.041103628, -0.07138963, 0.017832356, 0.020711523, 0.025514364, -0.10462326, -0.00067840907, 0.020873634, 0.016487276, -0.01058041, -0.022380909, -0.06952237, -0.019501, 0.019643003, -0.12459305, -0.029318675, 0.03205909, -0.1759669, -0.0037177783, -0.022790197, -0.009196223, 0.007788572, 0.102601826, -0.068507016, -0.02824339, -0.010025702, 0.2156503, 0.011369701, -0.12431524, 0.0025023408, 0.0028958034, 0.040234253, -0.015489806, 0.01747459, -0.008251758, -0.016565355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.1811576, 4.9334345, 2.5473928, 3.9578328, 0.0, 3.6966674, 2.4640965, 3.7880032, 0.0, 0.0, 0.0, 0.0, 1.00739, 7.518814, 0.0, 1.7339082, 1.7662197, 4.9730396, 1.8619981, 0.0, 0.0, 0.0, 0.0, 4.2492237, 1.5085129, 0.0, 0.0, 6.4131355, 6.193436, 0.31273913, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 24, 24, 27, 27, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0, 5.0, 0.020711523, 1.0, -0.15384616, 1.2692307, 0.020873634, 0.016487276, -0.01058041, -0.022380909, 1.0, 0.115384616, 0.019643003, 0.7307692, 1.0, 1.0, 1.0, -0.0037177783, -0.022790197, -0.009196223, 0.007788572, 1.0, -0.23076923, -0.02824339, -0.010025702, -0.34615386, 1.0, 1.0, 0.0025023408, 0.0028958034, 0.040234253, -0.015489806, 0.01747459, -0.008251758, -0.016565355], "split_indices": [137, 125, 0, 0, 0, 106, 1, 1, 0, 0, 0, 0, 59, 1, 0, 1, 50, 122, 69, 0, 0, 0, 0, 69, 1, 0, 0, 1, 50, 126, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1277.0, 791.0, 1120.0, 157.0, 202.0, 589.0, 1021.0, 99.0, 98.0, 104.0, 134.0, 455.0, 932.0, 89.0, 192.0, 263.0, 701.0, 231.0, 104.0, 88.0, 166.0, 97.0, 412.0, 289.0, 96.0, 135.0, 184.0, 228.0, 181.0, 108.0, 92.0, 92.0, 113.0, 115.0, 90.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.005657831, -0.01142297, 0.17756195, -0.026013426, 0.019906228, 0.032194436, 0.0034715496, 0.02389921, -0.06610719, 0.09785075, -0.07167557, -0.039575554, -0.02219338, 0.002611528, 0.226525, -0.01595941, -0.037860736, -0.10123003, 0.02994301, -0.01055761, 0.013760674, 0.023493594, 0.021912333, -0.013713442, 0.0025253636, -0.018627366, -0.065795176, -0.040339764, 0.011799846, 0.0051062265, -0.11243337, -0.013886605, 0.0058186497, -0.004795875, -0.016390905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [6.0751224, 5.7797647, 3.8567843, 3.5220866, 0.0, 0.0, 0.0, 5.5412354, 4.03511, 5.4166355, 1.0167484, 3.5746334, 0.0, 3.7096224, 0.011703491, 0.0, 1.5476028, 1.3319702, 2.4260023, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7004061, 2.1162193, 0.0, 0.0, 0.74010825, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 26, 26, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, -0.03846154, 0.019906228, 0.032194436, 0.0034715496, 1.0, 1.0, 1.0, 1.0, 1.0, -0.02219338, 1.0, 1.0, -0.01595941, 1.0, 0.34615386, 1.0, -0.01055761, 0.013760674, 0.023493594, 0.021912333, -0.013713442, 0.0025253636, -0.018627366, 1.0, 0.84615386, 0.011799846, 0.0051062265, 1.1923077, -0.013886605, 0.0058186497, -0.004795875, -0.016390905], "split_indices": [125, 0, 53, 1, 0, 0, 0, 106, 64, 50, 53, 39, 0, 111, 39, 0, 5, 1, 93, 0, 0, 0, 0, 0, 0, 0, 124, 1, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1882.0, 187.0, 1760.0, 122.0, 93.0, 94.0, 784.0, 976.0, 442.0, 342.0, 834.0, 142.0, 254.0, 188.0, 95.0, 247.0, 442.0, 392.0, 141.0, 113.0, 88.0, 100.0, 96.0, 151.0, 130.0, 312.0, 218.0, 174.0, 89.0, 223.0, 109.0, 109.0, 99.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0043456014, -0.044964254, 0.053560358, -0.023456302, -0.12510346, 0.032833066, 0.018746762, -0.0076666344, -0.013913974, -0.00806688, -0.019125048, -0.0029968694, 0.15707916, 0.011315852, -0.009949251, -0.036885053, 0.011649198, 0.00075468933, 0.028078312, 0.042428628, -0.0072553353, -0.12789148, 0.014519147, -0.024670037, 0.13446015, -0.020417763, -0.0043402514, -0.055474788, 0.010431401, 0.0053099724, -0.0072029843, 0.026668524, 0.00036722256, -0.020420503, 0.010108332], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, -1, 21, -1, -1, -1, 23, -1, 25, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.0282726, 1.7839568, 2.8782291, 1.4905076, 0.6436887, 3.9976537, 0.0, 1.2515341, 0.0, 0.0, 0.0, 2.822334, 3.7180433, 1.5525951, 0.0, 2.5402157, 0.0, 0.0, 0.0, 2.6800337, 0.0, 1.2632849, 2.1809287, 0.92447335, 3.1646998, 0.0, 0.0, 4.5405602, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, -1, 22, -1, -1, -1, 24, -1, 26, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 0.018746762, 2.0, -0.013913974, -0.00806688, -0.019125048, 1.0, -0.26923078, 1.0, -0.009949251, 1.0, 0.011649198, 0.00075468933, 0.028078312, 1.0, -0.0072553353, 1.0, 1.0, 1.0, 0.115384616, -0.020417763, -0.0043402514, 1.0, 0.010431401, 0.0053099724, -0.0072029843, 0.026668524, 0.00036722256, -0.020420503, 0.010108332], "split_indices": [71, 116, 125, 40, 109, 0, 0, 0, 0, 0, 0, 116, 1, 23, 0, 93, 0, 0, 0, 12, 0, 59, 109, 39, 1, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1035.0, 1037.0, 816.0, 219.0, 898.0, 139.0, 718.0, 98.0, 131.0, 88.0, 697.0, 201.0, 595.0, 123.0, 543.0, 154.0, 91.0, 110.0, 434.0, 161.0, 196.0, 347.0, 251.0, 183.0, 103.0, 93.0, 195.0, 152.0, 95.0, 156.0, 91.0, 92.0, 100.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0026743198, 0.020898009, -0.0082172705, -0.045533787, 0.031021968, -0.02486179, -0.1219134, -0.02253921, 0.08819271, -0.041955147, 0.009256953, -0.007702446, -0.018445527, 0.028005648, -0.021294123, -0.014186267, 0.21114546, -0.06244567, 0.009752009, -0.059941962, 0.015069802, 0.0066673146, -0.013833408, 0.033120915, 0.01071617, -0.038342863, -0.015477495, -0.01270794, 0.0021676058, 0.009392744, -0.09140706, -0.12916702, -0.00095349783, -0.008098158, -0.017414007], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, -1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1], "loss_changes": [4.6153336, 0.0, 2.856794, 1.5789187, 2.9120882, 1.5797411, 0.5979836, 4.7253075, 5.7903776, 1.9633918, 0.0, 0.0, 0.0, 4.1867146, 0.0, 2.5196686, 2.6092958, 1.3330112, 0.0, 1.2383953, 0.0, 0.0, 0.0, 0.0, 0.0, 3.3339381, 0.0, 0.0, 0.0, 0.0, 1.0480134, 0.5027542, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 25, 25, 30, 30, 31, 31], "right_children": [2, -1, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1], "split_conditions": [-0.5769231, 0.020898009, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5769231, -0.07692308, 3.3846154, 0.009256953, -0.007702446, -0.018445527, 1.0, -0.021294123, -0.30769232, 1.0, 1.0, 0.009752009, 1.0, 0.015069802, 0.0066673146, -0.013833408, 0.033120915, 0.01071617, 1.0, -0.015477495, -0.01270794, 0.0021676058, 0.009392744, 1.0, 0.07692308, -0.00095349783, -0.008098158, -0.017414007], "split_indices": [1, 0, 71, 116, 39, 121, 109, 1, 1, 1, 0, 0, 0, 42, 0, 1, 121, 113, 0, 108, 0, 0, 0, 0, 0, 122, 0, 0, 0, 0, 93, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 103.0, 1951.0, 1000.0, 951.0, 787.0, 213.0, 491.0, 460.0, 687.0, 100.0, 124.0, 89.0, 388.0, 103.0, 251.0, 209.0, 599.0, 88.0, 226.0, 162.0, 152.0, 99.0, 97.0, 112.0, 475.0, 124.0, 124.0, 102.0, 136.0, 339.0, 232.0, 107.0, 112.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0058299447, 0.039069504, -0.0475624, 0.021300526, 0.026015297, -0.07891885, 0.035431504, -0.081093155, 0.06402595, -0.0571909, -0.018010294, -0.010156359, 0.014940224, -0.15393852, 0.009956333, -0.012301062, 0.10371686, -0.11512718, -0.00804649, -0.022327717, -0.0027868215, 0.065181874, 0.02480237, -0.0051915655, -0.01579165, 0.009224804, -0.0072980784, 0.005255074, 0.13438304, -0.012930791, 0.012170383, 0.02961683, 0.0044502327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, -1, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [3.6772573, 5.0166, 2.068904, 5.171029, 0.0, 1.268547, 3.403728, 4.579676, 6.191325, 1.3524406, 0.0, 0.0, 0.0, 2.167902, 0.0, 0.0, 3.825873, 0.5896411, 1.6737264, 0.0, 0.0, 2.2518227, 0.0, 0.0, 0.0, 0.0, 0.0, 4.5598807, 3.6644254, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, -1, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.026015297, 1.0, 0.15384616, 1.0, -1.0, 1.0, -0.018010294, -0.010156359, 0.014940224, 1.0, 0.009956333, -0.012301062, 1.0, 0.15384616, 1.0, -0.022327717, -0.0027868215, -0.07692308, 0.02480237, -0.0051915655, -0.01579165, 0.009224804, -0.0072980784, 1.0, 0.1923077, -0.012930791, 0.012170383, 0.02961683, 0.0044502327], "split_indices": [137, 102, 93, 17, 0, 0, 1, 39, 0, 111, 0, 0, 0, 116, 0, 0, 42, 1, 53, 0, 0, 1, 0, 0, 0, 0, 0, 108, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1277.0, 795.0, 1182.0, 95.0, 577.0, 218.0, 348.0, 834.0, 475.0, 102.0, 99.0, 119.0, 248.0, 100.0, 146.0, 688.0, 218.0, 257.0, 160.0, 88.0, 543.0, 145.0, 88.0, 130.0, 101.0, 156.0, 291.0, 252.0, 135.0, 156.0, 90.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0013206856, 0.017181477, -0.008059528, 0.0047490024, -0.11219587, 0.01471074, -0.018094223, -0.006667472, -0.01456025, -0.026482679, 0.055953853, -0.05634961, 0.012319501, 0.022697475, 0.16480848, -0.123346776, 0.046500076, 0.10720766, -0.085234046, 0.082437955, -0.025027204, 0.00553807, 0.025565416, -0.093222104, -0.018423704, -0.0034910848, 0.0123626245, 0.005031882, 0.016107047, -0.0022666005, -0.014641165, -0.007120685, 0.17220797, 0.00523339, -0.075275786, -0.015552424, -0.003832217, 0.002434885, 0.03234275, -0.0037799925, -0.012403467], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1, 37, -1, 39, -1, -1, -1, -1, -1, -1], "loss_changes": [3.3120906, 0.0, 2.6183157, 3.2334628, 0.32695174, 2.8185499, 0.0, 0.0, 0.0, 0.9618886, 3.001071, 3.2317095, 3.3416605, 1.8104457, 1.9285607, 0.5209398, 1.1615994, 0.5607469, 0.6813413, 3.8895411, 1.3722118, 0.0, 0.0, 0.6498722, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.9799333, 0.0, 0.39103782, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 32, 32, 34, 34], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, 38, -1, 40, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.017181477, 1.0, 1.0, 1.0, 1.0, -0.018094223, -0.006667472, -0.01456025, 0.42307693, 2.0, 1.0, 1.0, 1.0, -0.26923078, -0.03846154, 1.0, 1.0, 1.2307693, 1.0, 1.0, 0.00553807, 0.025565416, 1.0, -0.018423704, -0.0034910848, 0.0123626245, 0.005031882, 0.016107047, -0.0022666005, -0.014641165, -0.007120685, 1.0, 0.00523339, 1.0, -0.015552424, -0.003832217, 0.002434885, 0.03234275, -0.0037799925, -0.012403467], "split_indices": [1, 0, 40, 117, 108, 71, 0, 0, 0, 1, 0, 12, 12, 121, 1, 1, 124, 81, 1, 69, 69, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 15, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 108.0, 1963.0, 1748.0, 215.0, 1659.0, 89.0, 91.0, 124.0, 830.0, 829.0, 469.0, 361.0, 635.0, 194.0, 284.0, 185.0, 183.0, 178.0, 282.0, 353.0, 88.0, 106.0, 190.0, 94.0, 90.0, 95.0, 89.0, 94.0, 88.0, 90.0, 104.0, 178.0, 139.0, 214.0, 89.0, 101.0, 90.0, 88.0, 121.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "41", "size_leaf_vector": "1"}}, {"base_weights": [0.0004409976, -0.01103243, 0.11505324, -0.02276049, 0.015204342, 0.022829965, 0.0006525395, 0.015058899, -0.05353279, -0.025838573, 0.017507028, -0.025839968, -0.021161264, -0.07319855, 0.01257769, 0.07709431, -0.055318803, -0.023484252, -0.025597168, 0.021088086, -0.0049575916, -0.1334078, 0.019188128, 0.029465092, -0.008530951, -0.026941767, -0.07997534, -0.009076361, 0.011949499, 0.013081125, -0.007390795, -0.017172046, 0.0015105953], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [2.7167804, 3.5917935, 2.3105924, 2.0389595, 0.0, 0.0, 0.0, 5.143632, 4.228836, 4.4949965, 0.0, 2.4942622, 0.0, 4.3342295, 0.0, 3.1012602, 3.717812, 1.227603, 0.0, 0.0, 0.0, 2.2674103, 3.6064548, 2.116245, 0.0, 0.0, 1.9540075, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, -0.03846154, 0.015204342, 0.022829965, 0.0006525395, -0.1923077, 1.0, 1.0, 0.017507028, 0.0, -0.021161264, 1.0, 0.01257769, 1.0, 1.0, 1.0, -0.025597168, 0.021088086, -0.0049575916, 0.42307693, 1.0, 1.0, -0.008530951, -0.026941767, 1.0, -0.009076361, 0.011949499, 0.013081125, -0.007390795, -0.017172046, 0.0015105953], "split_indices": [125, 0, 15, 1, 0, 0, 0, 1, 64, 61, 0, 0, 0, 0, 0, 106, 122, 108, 0, 0, 0, 1, 39, 111, 0, 0, 109, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1878.0, 188.0, 1752.0, 126.0, 92.0, 96.0, 786.0, 966.0, 626.0, 160.0, 822.0, 144.0, 477.0, 149.0, 183.0, 639.0, 375.0, 102.0, 89.0, 94.0, 312.0, 327.0, 202.0, 173.0, 88.0, 224.0, 156.0, 171.0, 102.0, 100.0, 114.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022983872, -0.010252812, 0.013278847, -0.025844611, 0.06843025, -0.07619101, 0.010192803, -0.0028312034, 0.023204077, -0.1193207, 0.006013435, 0.054612402, -0.025919307, 0.010750736, -0.017460832, -0.14816114, -0.0039153993, 0.015751883, -0.009039908, 0.0015187826, 0.02232627, -0.10509687, 0.06494018, -0.108740844, -0.021925243, -0.09446664, 0.014293245, -0.023332065, -0.00060886075, -0.0020551316, 0.01253223, -0.015260765, -0.005517714, -0.0024208487, -0.017487317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, -1, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.2221448, 2.395961, 0.0, 2.9573972, 3.765898, 2.4109082, 1.5238813, 4.2645693, 0.0, 1.0311708, 3.4180417, 3.8145118, 3.769673, 0.0, 0.0, 0.91919947, 0.0, 0.0, 0.0, 4.3978634, 0.0, 3.5546596, 1.2595669, 0.4957795, 0.0, 1.0902978, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, -1, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.013278847, 1.0, 1.0, 1.0, 1.0, 0.07692308, 0.023204077, 1.0, 0.1923077, 1.0, 1.0, 0.010750736, -0.017460832, 1.0, -0.0039153993, 0.015751883, -0.009039908, 1.0, 0.02232627, -0.1923077, 1.0, 1.0, -0.021925243, 1.0, 0.014293245, -0.023332065, -0.00060886075, -0.0020551316, 0.01253223, -0.015260765, -0.005517714, -0.0024208487, -0.017487317], "split_indices": [102, 42, 0, 39, 113, 116, 53, 1, 0, 62, 1, 23, 12, 0, 0, 121, 0, 0, 0, 15, 0, 1, 122, 12, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1953.0, 115.0, 1630.0, 323.0, 680.0, 950.0, 225.0, 98.0, 446.0, 234.0, 426.0, 524.0, 137.0, 88.0, 328.0, 118.0, 91.0, 143.0, 324.0, 102.0, 280.0, 244.0, 211.0, 117.0, 193.0, 131.0, 122.0, 158.0, 101.0, 143.0, 116.0, 95.0, 103.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0015929099, -0.009925245, 0.11708081, -0.03657462, 0.02329912, 0.023732478, -6.3167296e-05, -0.057821624, 0.051658005, -0.025616158, 0.09403376, -0.016473332, -0.03955754, 0.018880982, -0.012757443, -0.017891435, 0.024935251, 0.023494922, 0.040836748, 0.013243437, -0.06826932, 0.07564787, -0.031221077, 0.011020906, -0.006787035, -0.015779084, -0.12823212, -0.008145108, 0.024611695, -0.015113227, 0.0087342765, -0.06571123, 0.0053329878, -0.007724631, -0.019662768, -0.0139246825, 0.0011777856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, 29, -1, -1, 31, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [2.7575204, 1.6689951, 2.6609933, 1.9609146, 2.9029443, 0.0, 0.0, 1.6460786, 4.9901567, 3.8437228, 2.5712233, 0.0, 3.5554996, 0.0, 0.0, 0.0, 1.0622423, 0.0, 1.8777747, 0.0, 1.9419835, 5.248981, 2.5164325, 0.0, 0.0, 1.1352997, 1.004314, 0.0, 0.0, 0.0, 0.0, 1.0883577, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 16, 16, 18, 18, 20, 20, 21, 21, 22, 22, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, 30, -1, -1, 32, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.023732478, -6.3167296e-05, -0.42307693, 0.15384616, 1.0, -0.30769232, -0.016473332, 1.0, 0.018880982, -0.012757443, -0.017891435, 0.07692308, 0.023494922, 1.0, 0.013243437, 1.0, 1.0, 0.65384614, 0.011020906, -0.006787035, 1.0, 0.46153846, -0.008145108, 0.024611695, -0.015113227, 0.0087342765, 1.0, 0.0053329878, -0.007724631, -0.019662768, -0.0139246825, 0.0011777856], "split_indices": [125, 93, 15, 62, 50, 0, 0, 1, 1, 5, 1, 0, 89, 0, 0, 0, 1, 0, 113, 0, 106, 115, 1, 0, 0, 59, 1, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1885.0, 188.0, 1046.0, 839.0, 93.0, 95.0, 843.0, 203.0, 496.0, 343.0, 123.0, 720.0, 115.0, 88.0, 123.0, 373.0, 94.0, 249.0, 103.0, 617.0, 196.0, 177.0, 152.0, 97.0, 329.0, 288.0, 102.0, 94.0, 88.0, 89.0, 191.0, 138.0, 165.0, 123.0, 98.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.004709507, 0.017527478, -0.014274335, -0.044548422, 0.016910482, -0.02379569, -0.140222, -0.009274604, 0.035385948, -0.039728902, 0.00985849, -0.011174791, -0.016901962, 0.0056739766, 0.018076235, -0.055269208, 0.0072231903, -0.065505296, 0.053416178, -0.019388141, -0.14169241, 0.0015466806, -0.020284036, -0.051413406, 0.15037125, 0.006021876, -0.050538678, -0.011474431, -0.017106278, 0.0053056753, -0.015274948, 0.029698635, -0.005788879, -0.012229295, -0.0067685507, -0.006982113, 0.0056283996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, -1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, 23, 25, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [3.5480497, 0.0, 1.8475878, 1.971589, 1.9530205, 1.5911311, 0.1451366, 0.0, 3.563521, 1.2562118, 0.0, 0.0, 0.0, 2.3278046, 0.0, 1.966006, 0.0, 3.058086, 4.1671424, 1.1109494, 0.1472137, 0.0, 0.0, 2.0855598, 6.5037565, 0.0, 1.011303, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7951251, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 26, 26, 34, 34], "right_children": [2, -1, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, 24, 26, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [-0.5769231, 0.017527478, 1.0, 1.0, -0.46153846, 1.0, 1.0, -0.009274604, 1.0, 1.0, 0.00985849, -0.011174791, -0.016901962, 1.0, 0.018076235, 1.0, 0.0072231903, 1.0, -0.07692308, 0.0, 0.8076923, 0.0015466806, -0.020284036, -0.30769232, 1.0, 0.006021876, 1.0, -0.011474431, -0.017106278, 0.0053056753, -0.015274948, 0.029698635, -0.005788879, -0.012229295, 1.0, -0.006982113, 0.0056283996], "split_indices": [1, 0, 71, 58, 1, 121, 17, 0, 42, 42, 0, 0, 0, 39, 0, 109, 0, 122, 1, 0, 1, 0, 0, 1, 109, 0, 115, 0, 0, 0, 0, 0, 0, 0, 124, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 104.0, 1957.0, 993.0, 964.0, 816.0, 177.0, 139.0, 825.0, 722.0, 94.0, 89.0, 88.0, 685.0, 140.0, 634.0, 88.0, 275.0, 410.0, 448.0, 186.0, 173.0, 102.0, 197.0, 213.0, 126.0, 322.0, 97.0, 89.0, 97.0, 100.0, 125.0, 88.0, 122.0, 200.0, 100.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028657613, -0.013203337, 0.1001271, -0.0013188132, -0.09379218, 0.021744683, -0.001114519, -0.010389942, 0.014967154, -0.01926421, 0.0010083977, -0.1141282, 0.004211389, -0.0081094615, -0.015279247, 0.044268202, -0.027066566, 0.098515585, -0.010072633, -0.051587205, 0.0077272966, -0.015517981, 0.034228303, -0.114562064, 0.013252033, 0.0076140547, -0.008106451, 0.0032809933, -0.17403957, 0.0818618, -0.010436468, -0.009605197, -0.027351353, -0.0020852736, 0.016877413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, -1, -1, 17, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [2.206051, 1.8034617, 2.467289, 2.2476, 2.484893, 0.0, 0.0, 2.3447816, 0.0, 0.0, 0.0, 0.24394798, 1.7001787, 0.0, 0.0, 4.6800165, 1.9495559, 12.036394, 0.0, 2.5193605, 0.0, 1.77233, 0.0, 2.743545, 2.4531753, 0.0, 0.0, 0.0, 1.7299743, 1.7140154, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, -1, -1, 18, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 1.1923077, 3.3846154, 0.021744683, -0.001114519, 1.0, 0.014967154, -0.01926421, 0.0010083977, 1.0, 1.0, -0.0081094615, -0.015279247, 0.1923077, 1.0, -0.1923077, -0.010072633, 1.0, 0.0077272966, 1.0, 0.034228303, -0.3846154, 1.0, 0.0076140547, -0.008106451, 0.0032809933, 1.0, 1.0, -0.010436468, -0.009605197, -0.027351353, -0.0020852736, 0.016877413], "split_indices": [125, 1, 15, 1, 1, 0, 0, 26, 0, 0, 0, 15, 69, 0, 0, 1, 116, 1, 0, 59, 0, 59, 0, 1, 83, 0, 0, 0, 15, 124, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1883.0, 189.0, 1641.0, 242.0, 92.0, 97.0, 1548.0, 93.0, 124.0, 118.0, 191.0, 1357.0, 103.0, 88.0, 595.0, 762.0, 433.0, 162.0, 617.0, 145.0, 295.0, 138.0, 313.0, 304.0, 123.0, 172.0, 90.0, 223.0, 192.0, 112.0, 125.0, 98.0, 88.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0025406785, 0.013427503, -0.0047175987, -0.03221151, 0.023406377, -0.062201682, 0.008253693, 0.0013517214, 0.101695165, -0.10896594, 0.005185747, 0.047975514, -0.011480603, -0.011701827, 0.030501075, 0.00048141656, 0.023710834, -0.1287763, -0.007007891, -0.0026741894, 0.09433864, 0.09062665, -0.06648407, -0.016745223, -0.008132513, 0.017086657, 0.002737668, 0.14041518, -0.004806995, 0.001370065, -0.015395834, 0.021000458, 0.0074305246], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, -1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, -1, -1, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.9591815, 0.0, 1.5016283, 1.1917144, 1.6575665, 3.0083156, 2.0432494, 2.584356, 2.7681003, 0.308146, 0.0, 1.0946661, 0.0, 0.0, 3.5046039, 0.0, 0.0, 0.48633242, 0.0, 0.0, 0.999269, 2.561941, 1.6132435, 0.0, 0.0, 0.0, 0.0, 1.2559495, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 14, 14, 17, 17, 20, 20, 21, 21, 22, 22, 27, 27], "right_children": [2, -1, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, -1, -1, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.013427503, 1.0, 1.0, 2.0, 1.1538461, 1.0, 1.0, -0.115384616, 1.0, 0.005185747, 1.0, -0.011480603, -0.011701827, 1.0, 0.00048141656, 0.023710834, 1.0, -0.007007891, -0.0026741894, 0.15384616, 1.0, -0.03846154, -0.016745223, -0.008132513, 0.017086657, 0.002737668, 1.0, -0.004806995, 0.001370065, -0.015395834, 0.021000458, 0.0074305246], "split_indices": [1, 0, 71, 83, 0, 1, 23, 89, 1, 115, 0, 97, 0, 0, 97, 0, 0, 122, 0, 0, 1, 23, 1, 0, 0, 0, 0, 108, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 107.0, 1942.0, 982.0, 960.0, 564.0, 418.0, 749.0, 211.0, 400.0, 164.0, 316.0, 102.0, 148.0, 601.0, 123.0, 88.0, 265.0, 135.0, 121.0, 195.0, 371.0, 230.0, 146.0, 119.0, 91.0, 104.0, 273.0, 98.0, 120.0, 110.0, 133.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038789038, -0.011271231, 0.01182824, -0.028343556, 0.053252112, -0.007945454, -0.16137898, -0.034843627, 0.12779467, -0.019052979, 0.014063259, -0.005414378, -0.02635077, -0.014339897, 0.008468919, 0.023131106, 0.004224391, 0.00026774633, -0.016435545, -0.023578076, 0.062266894, -0.099760205, 0.035390634, 0.13254263, -0.007355444, -0.044924863, -0.01854658, -0.054912772, 0.10526828, 0.023695396, 0.004957287, 0.007588092, -0.013478043, 0.0067022764, -0.01638261, 0.0024698765, 0.022375284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, 19, -1, 21, 23, 25, 27, 29, -1, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8675202, 2.1480489, 0.0, 4.1844788, 2.6792881, 2.2064965, 2.2451172, 2.4264984, 1.9571548, 3.4923422, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.623306, 0.0, 3.562443, 2.9112082, 1.6260939, 2.8206546, 1.7412584, 0.0, 2.29042, 0.0, 2.5896788, 2.4056528, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, 20, -1, 22, 24, 26, 28, 30, -1, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.01182824, 1.0, 1.0, 5.0, 1.0, -0.07692308, -0.3846154, 2.0, 0.014063259, -0.005414378, -0.02635077, -0.014339897, 0.008468919, 0.023131106, 0.004224391, 1.0, -0.016435545, 1.0, 1.0, 1.0, 1.0, 0.15384616, -0.007355444, 0.115384616, -0.01854658, 1.0, 1.0, 0.023695396, 0.004957287, 0.007588092, -0.013478043, 0.0067022764, -0.01638261, 0.0024698765, 0.022375284], "split_indices": [102, 113, 0, 64, 109, 0, 121, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 116, 0, 111, 80, 15, 39, 1, 0, 1, 0, 69, 93, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1950.0, 118.0, 1542.0, 408.0, 1337.0, 205.0, 187.0, 221.0, 1244.0, 93.0, 100.0, 105.0, 98.0, 89.0, 100.0, 121.0, 1098.0, 146.0, 793.0, 305.0, 346.0, 447.0, 201.0, 104.0, 211.0, 135.0, 195.0, 252.0, 89.0, 112.0, 90.0, 121.0, 92.0, 103.0, 150.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0059708515, -0.012029111, 0.00965981, -0.08206844, 0.0011834346, 0.0009939912, -0.015729882, -0.018330926, 0.09461793, 0.010901372, -0.030591458, 0.017299643, -0.0007337814, 0.018645564, -0.06866715, 0.022409996, -0.034638163, -0.13399558, -0.027607922, -0.08302389, 0.014966249, -0.19879341, -0.0005119861, 0.009625259, -0.015990457, 0.0023227371, -0.14199823, -0.014695624, -0.024892163, -0.00873095, 0.009664625, -0.025359286, -0.005086259], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, -1, 13, -1, -1, 15, 17, -1, 19, 21, 23, 25, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2813046, 1.8017502, 0.0, 2.1388438, 2.9865897, 0.0, 0.0, 2.115579, 2.2614927, 0.0, 2.3171713, 0.0, 0.0, 5.9006367, 1.869587, 0.0, 3.8166986, 2.2463827, 2.1082537, 2.1242077, 0.0, 0.4651308, 0.0, 2.8174098, 0.0, 0.0, 2.217113, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, -1, 14, -1, -1, 16, 18, -1, 20, 22, 24, 26, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.00965981, 1.0, 1.0, 0.0009939912, -0.015729882, 0.0, 1.0, 0.010901372, 1.0, 0.017299643, -0.0007337814, -0.34615386, 1.0, 0.022409996, 1.0, 0.3846154, 0.96153843, 1.0, 0.014966249, 1.0, -0.0005119861, 1.0, -0.015990457, 0.0023227371, 1.0, -0.014695624, -0.024892163, -0.00873095, 0.009664625, -0.025359286, -0.005086259], "split_indices": [102, 0, 0, 127, 42, 0, 0, 0, 12, 0, 53, 0, 0, 1, 59, 0, 88, 1, 1, 124, 0, 93, 0, 97, 0, 0, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1947.0, 115.0, 309.0, 1638.0, 139.0, 170.0, 1355.0, 283.0, 119.0, 1236.0, 160.0, 123.0, 539.0, 697.0, 111.0, 428.0, 269.0, 428.0, 339.0, 89.0, 179.0, 90.0, 334.0, 94.0, 121.0, 218.0, 88.0, 91.0, 158.0, 176.0, 98.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-6.19128e-05, 0.005196614, -0.010824518, -0.018979128, 0.029347891, -0.040008504, 0.032564886, 0.045242105, -0.010431483, -0.081323214, 0.008899868, 0.013303441, -0.005477134, 0.086043246, -0.04036871, -0.0017808091, -0.110282205, 0.0110505195, -0.040491596, 0.047136463, 0.027085045, 0.013374486, -0.015681235, -0.016186787, -0.0071506384, 0.00299923, -0.0125359995, 0.08943002, -0.013766794, 0.010849033, -0.007885902, 0.018838922, 0.02306122, 0.009206149, -0.0044213827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.1781604, 1.1531533, 0.0, 1.0698472, 2.09897, 1.4164655, 2.5095441, 3.0843358, 0.0, 0.6989465, 1.610918, 0.0, 0.0, 4.2997704, 1.7835453, 0.0, 0.52207184, 0.0, 1.292081, 3.8611217, 0.0, 1.7107092, 0.0, 0.0, 0.0, 0.0, 0.0, 4.0064135, 0.0, 0.0, 0.0, 1.237325, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 16, 16, 18, 18, 19, 19, 21, 21, 27, 27, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010824518, 0.88461536, 1.0, 1.0, 1.0, 0.15384616, -0.010431483, -0.30769232, 1.0, 0.013303441, -0.005477134, -0.07692308, 1.0, -0.0017808091, 1.0, 0.0110505195, 1.0, 3.0, 0.027085045, 1.0, -0.015681235, -0.016186787, -0.0071506384, 0.00299923, -0.0125359995, 1.0, -0.013766794, 0.010849033, -0.007885902, 1.0, 0.02306122, 0.009206149, -0.0044213827], "split_indices": [117, 71, 0, 1, 90, 83, 12, 1, 0, 1, 122, 0, 0, 1, 115, 0, 108, 0, 127, 0, 0, 13, 0, 0, 0, 0, 0, 61, 0, 0, 0, 108, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1975.0, 96.0, 987.0, 988.0, 701.0, 286.0, 883.0, 105.0, 380.0, 321.0, 133.0, 153.0, 598.0, 285.0, 119.0, 261.0, 105.0, 216.0, 494.0, 104.0, 195.0, 90.0, 112.0, 149.0, 118.0, 98.0, 402.0, 92.0, 96.0, 99.0, 268.0, 134.0, 124.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [7.661662e-05, -0.023476856, 0.024967292, -0.011512611, -0.014237923, -0.029576672, 0.055722617, -0.06790804, 0.037044067, 0.00959351, -0.014622638, 0.014346793, 0.097879104, 0.00043240885, -0.11032194, 0.108532965, -0.012880958, 0.008302236, -0.005052942, 0.013557752, -0.064824276, 0.03909985, 0.021058425, -0.016153669, -0.00629658, 0.0014711538, 0.021455115, -0.055013176, 0.005770896, 0.004823626, -0.018502552, 0.016537277, -0.0062136166, 0.0040934333, -0.014606543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2106307, 1.5093539, 1.6842276, 2.6397936, 0.0, 1.6540471, 1.1198143, 1.3663867, 1.8487865, 1.1963993, 0.0, 3.1097414, 2.1066618, 0.0, 0.6815181, 2.118663, 0.9071036, 0.0, 0.0, 0.0, 2.6636438, 2.671724, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6686217, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 20, 20, 21, 21, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.014237923, 1.0, 1.0, 1.0, 1.0, 1.0, -0.014622638, 1.0, 1.0, 0.00043240885, 1.0, 1.0, 1.0, 0.008302236, -0.005052942, 0.013557752, 1.0, 1.0, 0.021058425, -0.016153669, -0.00629658, 0.0014711538, 0.021455115, 1.0, 0.005770896, 0.004823626, -0.018502552, 0.016537277, -0.0062136166, 0.0040934333, -0.014606543], "split_indices": [39, 88, 2, 13, 0, 15, 111, 53, 69, 111, 0, 121, 121, 0, 69, 71, 137, 0, 0, 0, 69, 13, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1061.0, 1004.0, 964.0, 97.0, 362.0, 642.0, 446.0, 518.0, 271.0, 91.0, 324.0, 318.0, 165.0, 281.0, 213.0, 305.0, 122.0, 149.0, 128.0, 196.0, 209.0, 109.0, 135.0, 146.0, 113.0, 100.0, 191.0, 114.0, 101.0, 95.0, 93.0, 116.0, 93.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016858854, -0.007161437, 0.0110619925, -0.03388835, 0.021506693, -0.14025113, -0.010294688, 0.0070741973, 0.014282537, -0.00806298, -0.019434055, 0.035057515, -0.06293145, 0.035163756, -0.01217312, 0.020692917, -0.048879795, -0.014041314, -0.016826626, 0.021297768, -0.0036941734, -0.013074732, -0.012842401, 0.0064945254, -0.006524421, 0.03816791, -0.09412554, 0.0024457742, -0.0055090557, -0.009867122, 0.014535354, -0.018600872, -0.0007182273, 0.008341506, -0.007110146], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, 21, -1, 23, -1, 25, -1, 27, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.2698431, 1.5086689, 0.0, 2.5571673, 1.6633842, 0.59660244, 1.990919, 3.071756, 0.0, 0.0, 0.0, 6.4630475, 1.3788992, 4.815909, 0.0, 0.0, 0.88803756, 0.0, 0.9581256, 0.0, 2.1653893, 0.0, 0.3293553, 0.0, 0.0, 2.0131285, 1.4459406, 0.0, 0.0, 1.5422596, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 16, 16, 18, 18, 20, 20, 22, 22, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, 22, -1, 24, -1, 26, -1, 28, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0110619925, -0.42307693, 1.0, 1.0, 1.0, 1.0, 0.014282537, -0.00806298, -0.019434055, -0.03846154, 0.0, -0.42307693, -0.01217312, 0.020692917, 0.42307693, -0.014041314, 1.0, 0.021297768, 1.0, -0.013074732, 1.0, 0.0064945254, -0.006524421, 1.0, 1.0, 0.0024457742, -0.0055090557, 1.0, 0.014535354, -0.018600872, -0.0007182273, 0.008341506, -0.007110146], "split_indices": [114, 39, 0, 1, 88, 124, 122, 7, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 69, 0, 121, 0, 53, 0, 0, 71, 53, 0, 0, 122, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1969.0, 96.0, 1019.0, 950.0, 185.0, 834.0, 849.0, 101.0, 88.0, 97.0, 448.0, 386.0, 697.0, 152.0, 147.0, 301.0, 144.0, 242.0, 125.0, 572.0, 92.0, 209.0, 90.0, 152.0, 391.0, 181.0, 111.0, 98.0, 270.0, 121.0, 88.0, 93.0, 107.0, 163.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0026774823, -0.020777315, 0.029840842, -0.06344655, 0.011820071, -0.06859566, 0.061969426, -0.017114613, -0.030195033, -0.046043493, 0.08983837, -0.018186152, 0.0036314502, 0.025342394, 0.023136942, 0.0065469295, -0.068680696, 0.022978805, -0.118203185, 0.020829944, 0.029603912, -0.01430059, 0.013908927, -0.012591382, -0.0036465626, -0.005148319, 0.009123567, -0.018553158, -0.005087474, -0.004553459, 0.01038982, 0.08195932, -0.118316054, -0.0006565086, 0.017860526, -0.021147816, -0.0020631496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, -1, -1, 21, -1, -1, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [1.3130859, 1.5383416, 3.0203073, 1.7153817, 2.8305392, 2.7924447, 4.467325, 0.0, 1.3475041, 1.7930263, 1.9051616, 0.0, 0.0, 2.6694858, 0.0, 0.0, 0.4812231, 0.9351879, 0.79782796, 0.0, 0.98807824, 4.395496, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9506601, 1.9202044, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 13, 13, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, -1, -1, 22, -1, -1, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.42307693, 1.0, -0.26923078, 1.0, -0.017114613, -0.03846154, 1.0, 1.0, -0.018186152, 0.0036314502, 1.0, 0.023136942, 0.0065469295, 0.42307693, 0.88461536, 1.0, 0.020829944, 0.0, 1.0, 0.013908927, -0.012591382, -0.0036465626, -0.005148319, 0.009123567, -0.018553158, -0.005087474, -0.004553459, 0.01038982, 1.0, 1.0, -0.0006565086, 0.017860526, -0.021147816, -0.0020631496], "split_indices": [93, 59, 89, 1, 2, 1, 42, 0, 1, 17, 115, 0, 0, 61, 0, 0, 1, 1, 74, 0, 1, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 111, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1106.0, 955.0, 479.0, 627.0, 235.0, 720.0, 113.0, 366.0, 360.0, 267.0, 113.0, 122.0, 592.0, 128.0, 105.0, 261.0, 184.0, 176.0, 90.0, 177.0, 439.0, 153.0, 94.0, 167.0, 88.0, 96.0, 88.0, 88.0, 88.0, 89.0, 228.0, 211.0, 119.0, 109.0, 108.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006814634, 0.011751662, -0.0070803394, -0.008247193, -0.0009607553, -0.014679109, 0.077677526, 0.039431978, -0.032189738, 0.019132042, -0.0013846933, 0.015206239, -0.050135996, 0.038980864, -0.04756794, -0.011517175, 0.0011286624, -0.0055281622, 0.011007718, -0.010109328, -0.08613761, -0.06961738, 0.06810124, 0.0036407753, -0.11824843, -0.014803604, -0.017574614, -0.0068510133, 0.0168584, -0.05347722, -0.020029195, -0.002747704, -0.00029390785, -0.014178658, 0.0030705498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, 25, 27, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.5610747, 0.0, 0.90335274, 0.0, 1.9536846, 1.4610745, 2.7978978, 3.8032055, 1.2750647, 0.0, 0.0, 0.0, 0.83888024, 1.3872551, 1.3840859, 0.0, 0.0, 0.0, 0.0, 2.2619212, 1.8573358, 1.6055794, 2.8826892, 0.0, 1.9874568, 0.027366206, 0.0, 0.0, 0.0, 1.5537318, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 12, 12, 13, 13, 14, 14, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 25, 25, 29, 29], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, 26, 28, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.011751662, -0.5, -0.008247193, 1.0, 0.0, -0.03846154, 1.0, 1.0, 0.019132042, -0.0013846933, 0.015206239, 1.0, 1.0, 1.0, -0.011517175, 0.0011286624, -0.0055281622, 0.011007718, 1.0, -0.26923078, 1.0384616, 0.03846154, 0.0036407753, 1.0, 1.0, -0.017574614, -0.0068510133, 0.0168584, 0.46153846, -0.020029195, -0.002747704, -0.00029390785, -0.014178658, 0.0030705498], "split_indices": [1, 0, 1, 0, 42, 0, 1, 122, 89, 0, 0, 0, 13, 13, 13, 0, 0, 0, 0, 93, 1, 1, 1, 0, 71, 137, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 106.0, 1958.0, 147.0, 1811.0, 1542.0, 269.0, 377.0, 1165.0, 120.0, 149.0, 167.0, 210.0, 207.0, 958.0, 102.0, 108.0, 89.0, 118.0, 486.0, 472.0, 276.0, 210.0, 98.0, 374.0, 182.0, 94.0, 89.0, 121.0, 209.0, 165.0, 88.0, 94.0, 102.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012037688, 0.005106482, -0.013251073, -0.042308394, 0.017719762, -0.13085541, 0.009241343, 0.10835968, -0.0068984847, -0.022557594, -0.0052850232, 0.19246657, -0.005758092, -0.08941944, 0.011044445, 0.03492957, -0.0036609636, -0.01927268, -0.0017588519, 0.0137741165, -0.0108408835, 0.026089195, -0.02067071, 0.07757599, -0.051773276, 0.13348228, 0.012398792, -0.09627666, 0.0024518217, 0.005169119, 0.02226419, 0.013724382, -0.0116268005, -0.0013238176, -0.01830896], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, 15, -1, 17, 19, -1, -1, -1, -1, -1, 21, 23, -1, 25, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6985892, 1.1697994, 0.0, 4.9029074, 3.447507, 1.8323946, 0.0, 4.605727, 1.7990115, 0.0, 0.0, 7.867755, 0.0, 1.6102831, 2.767254, 0.0, 0.0, 0.0, 0.0, 0.0, 6.155586, 2.8703647, 0.0, 1.5704839, 0.96764076, 1.6918507, 3.1966188, 1.297586, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 20, 20, 21, 21, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, 16, -1, 18, 20, -1, -1, -1, -1, -1, 22, 24, -1, 26, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.013251073, 1.0, -0.34615386, 1.0, 0.009241343, 1.0, -0.1923077, -0.022557594, -0.0052850232, 1.0, -0.005758092, 1.0, -0.03846154, 0.03492957, -0.0036609636, -0.01927268, -0.0017588519, 0.0137741165, 1.0, 1.0, -0.02067071, 1.0, 1.0, 1.0, 0.6923077, 0.9230769, 0.0024518217, 0.005169119, 0.02226419, 0.013724382, -0.0116268005, -0.0013238176, -0.01830896], "split_indices": [117, 89, 0, 97, 1, 88, 0, 126, 1, 0, 0, 97, 0, 13, 1, 0, 0, 0, 0, 0, 64, 23, 0, 108, 80, 81, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1956.0, 94.0, 411.0, 1545.0, 248.0, 163.0, 330.0, 1215.0, 112.0, 136.0, 219.0, 111.0, 217.0, 998.0, 130.0, 89.0, 89.0, 128.0, 147.0, 851.0, 716.0, 135.0, 431.0, 285.0, 232.0, 199.0, 180.0, 105.0, 121.0, 111.0, 101.0, 98.0, 92.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007276444, -0.0069876695, 0.0103358505, -0.07136833, 0.0054188143, -0.1361336, 0.001594482, 0.010661686, -0.003921508, -0.010632171, -0.016660793, -0.02927229, 0.040396094, -0.07232407, 0.011846547, 0.12810837, -0.019334344, -0.028709196, -0.016211938, -0.025837611, 0.014202821, 0.0069195144, 0.020130357, -0.09627168, 0.012182891, 0.008353657, -0.013607472, 0.0029446704, -0.07303155, 0.003126387, -0.019873617, -0.014067254, 0.0005289558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, -1, -1, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.3552903, 1.5671234, 0.0, 1.7925906, 1.5548911, 0.1653459, 0.0, 0.0, 1.6919695, 0.0, 0.0, 1.6958892, 2.8710232, 1.8328795, 2.4038358, 0.95729995, 3.5405965, 3.7961679, 0.0, 0.99145246, 0.0, 0.0, 0.0, 2.75732, 0.0, 0.0, 0.0, 0.0, 1.0860325, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, -1, -1, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.0103358505, 1.0, 0.0, 1.0, 0.001594482, 0.010661686, 1.0, -0.010632171, -0.016660793, 1.0, -0.03846154, 1.0, 1.0, 1.0, 1.2692307, -0.03846154, -0.016211938, 1.0, 0.014202821, 0.0069195144, 0.020130357, 1.0, 0.012182891, 0.008353657, -0.013607472, 0.0029446704, 0.115384616, 0.003126387, -0.019873617, -0.014067254, 0.0005289558], "split_indices": [102, 0, 0, 137, 0, 15, 0, 0, 50, 0, 0, 122, 1, 0, 121, 61, 1, 1, 0, 111, 0, 0, 0, 126, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1962.0, 118.0, 317.0, 1645.0, 182.0, 135.0, 139.0, 1506.0, 92.0, 90.0, 958.0, 548.0, 468.0, 490.0, 222.0, 326.0, 315.0, 153.0, 380.0, 110.0, 123.0, 99.0, 211.0, 115.0, 154.0, 161.0, 175.0, 205.0, 94.0, 117.0, 110.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019688976, -0.008435895, 0.009239044, -0.019664127, 0.10047794, -0.012194018, -0.012452755, 0.01795135, 0.0017849818, -0.026675511, 0.03776147, -0.005186649, -0.12821521, 0.027105352, -0.051966235, -0.013596726, 0.008665518, -0.029365031, -0.0013294623, -0.020780247, 0.0069714086, 0.004933044, -0.011299485, 0.057265807, -0.033116996, 0.01573068, 0.0015351942, 0.0012190533, -0.010907966, -0.0070221215, 0.0057540224, 0.011411579, -0.0052393996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, -1, -1, -1, -1, -1, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1], "loss_changes": [1.2558357, 2.3553243, 0.0, 1.2877631, 1.1755006, 0.0, 1.1648351, 0.0, 0.0, 2.7732868, 7.5357943, 0.8102441, 4.22064, 0.0, 4.930173, 1.7699934, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6129239, 0.0, 1.9011924, 1.2232733, 0.0, 0.88009965, 1.9550347, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 21, 21, 23, 23, 24, 24, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, -1, -1, -1, -1, -1, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.009239044, -0.53846157, 1.0, -0.012194018, 1.0, 0.01795135, 0.0017849818, 1.0, -0.23076923, 3.1538463, -0.07692308, 0.027105352, 0.53846157, 1.0, 0.008665518, -0.029365031, -0.0013294623, -0.020780247, 0.0069714086, -0.03846154, -0.011299485, 1.0, 1.0, 0.01573068, 1.0, 1.0, -0.010907966, -0.0070221215, 0.0057540224, 0.011411579, -0.0052393996], "split_indices": [0, 125, 0, 1, 15, 0, 61, 0, 0, 0, 1, 1, 1, 0, 1, 113, 0, 0, 0, 0, 0, 1, 0, 106, 109, 0, 13, 115, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1926.0, 132.0, 1746.0, 180.0, 115.0, 1631.0, 92.0, 88.0, 1271.0, 360.0, 1049.0, 222.0, 100.0, 260.0, 961.0, 88.0, 91.0, 131.0, 114.0, 146.0, 810.0, 151.0, 341.0, 469.0, 122.0, 219.0, 323.0, 146.0, 96.0, 123.0, 104.0, 219.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0012042185, 0.012101932, -0.005234156, -0.00842093, 0.0011432603, -0.011717711, 0.074723326, 0.046689354, -0.030335905, 0.0127228275, -0.0024640851, 0.01535192, -0.036627688, -0.07955942, 0.0028564218, 0.0034338813, -0.0105585735, -0.04416104, -0.018280467, -0.04443158, 0.050008938, -0.0057167206, -0.013763348, -0.011745262, 0.0121969655, -0.0014774078, 0.10743822, 0.0055481116, -0.011504772, -0.009918293, 0.014071226, 0.020118019, 0.002053158], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, 21, -1, 23, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5883428, 0.0, 0.98414665, 0.0, 1.7109307, 1.6735611, 1.4034032, 3.3110785, 1.9066954, 0.0, 0.0, 0.0, 1.0227859, 1.7177155, 1.5541345, 0.0, 0.0, 1.257719, 0.0, 1.4390066, 1.2984346, 1.6593237, 0.0, 0.0, 2.8055482, 0.0, 1.5071573, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 21, 21, 24, 24, 26, 26], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, 22, -1, 24, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.012101932, -0.5, -0.00842093, 1.0, 0.0, 1.0, 1.0, 1.0, 0.0127228275, -0.0024640851, 0.01535192, 1.0, 1.0, 1.0, 0.0034338813, -0.0105585735, 1.0, -0.018280467, 1.0, -0.07692308, 1.0, -0.013763348, -0.011745262, 0.42307693, -0.0014774078, 0.34615386, 0.0055481116, -0.011504772, -0.009918293, 0.014071226, 0.020118019, 0.002053158], "split_indices": [1, 0, 1, 0, 42, 0, 106, 122, 39, 0, 0, 0, 124, 0, 71, 0, 0, 121, 0, 115, 1, 115, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 105.0, 1954.0, 146.0, 1808.0, 1539.0, 269.0, 372.0, 1167.0, 176.0, 93.0, 163.0, 209.0, 470.0, 697.0, 103.0, 106.0, 350.0, 120.0, 348.0, 349.0, 248.0, 102.0, 152.0, 196.0, 164.0, 185.0, 159.0, 89.0, 105.0, 91.0, 89.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-5.13787e-05, -0.00585303, 0.00829337, 0.0014500907, -0.010872435, -0.00828118, 0.017075436, -0.014878076, 0.0077316216, -0.04005461, 0.009454027, -0.07069553, 0.01474759, -0.00873747, 0.025756111, -0.009335311, -0.12940882, -0.01087939, 0.012099329, 0.011616918, -0.0033981928, 0.004316551, -0.0072241724, -0.020868806, -0.0061124484, -0.045261603, 0.016050098, 0.040670842, -0.127546, 0.010504587, -0.0013755279, -0.02289219, -0.0051304577], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.99467635, 1.4507252, 0.0, 2.9705253, 0.0, 0.9627744, 0.0, 0.9697429, 0.0, 1.3064098, 1.2707003, 1.7977269, 3.6620853, 0.0, 1.816156, 0.8058438, 1.3804498, 0.0, 0.0, 0.0, 3.574778, 0.0, 0.0, 0.0, 0.0, 2.9344232, 0.0, 0.7112479, 1.6385567, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 20, 20, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 0.00829337, 1.0, -0.010872435, 1.0, 0.017075436, 1.0, 0.0077316216, 1.0, -0.34615386, 1.0, 1.0, -0.00873747, 0.0, 1.0, 1.0, -0.01087939, 0.012099329, 0.011616918, 1.0, 0.004316551, -0.0072241724, -0.020868806, -0.0061124484, 1.0, 0.016050098, 0.46153846, 1.0, 0.010504587, -0.0013755279, -0.02289219, -0.0051304577], "split_indices": [0, 0, 0, 102, 0, 125, 0, 111, 0, 50, 1, 2, 93, 0, 0, 23, 106, 0, 0, 0, 105, 0, 0, 0, 0, 13, 0, 1, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1931.0, 135.0, 1803.0, 128.0, 1705.0, 98.0, 1583.0, 122.0, 778.0, 805.0, 499.0, 279.0, 116.0, 689.0, 244.0, 255.0, 129.0, 150.0, 168.0, 521.0, 133.0, 111.0, 118.0, 137.0, 415.0, 106.0, 203.0, 212.0, 93.0, 110.0, 91.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0007632771, 0.010466425, -0.004805686, 0.002764718, -0.06686168, -0.008525052, 0.010377737, -0.011517023, 4.445615e-05, -0.0031697948, 0.077762865, -0.026633853, 0.031938735, 0.018029299, -0.0003577666, -0.0743692, 0.030805925, 0.08086696, -0.025309267, -0.023208385, -0.025877822, 0.01041011, -0.026680468, 0.02254742, 0.0007887538, -0.015172827, 0.0047160215, -0.09374402, 0.0055829487, 0.0020245318, -0.008731495, -0.009714644, 0.010302884, -0.013441781, -0.0052623227], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, 5, 7, -1, 9, -1, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, 25, -1, 27, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1942732, 0.0, 0.9203168, 1.1699274, 0.69256276, 0.0, 1.4670341, 0.0, 0.0, 1.1022291, 2.2434213, 2.19901, 1.5013595, 0.0, 0.0, 3.3497362, 1.533705, 3.3465874, 2.2628958, 0.0, 1.8576303, 0.0, 0.5804455, 0.0, 1.8623999, 0.0, 0.0, 0.30607402, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 22, 22, 24, 24, 27, 27], "right_children": [2, -1, 4, 6, 8, -1, 10, -1, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, 26, -1, 28, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010466425, 1.0, -0.5, 0.34615386, -0.008525052, 1.0, -0.011517023, 4.445615e-05, 1.0, -0.03846154, 1.0, 1.0, 0.018029299, -0.0003577666, 1.0, 1.0, -0.26923078, 1.0, -0.023208385, 0.65384614, 0.01041011, 0.96153843, 0.02254742, 1.0, -0.015172827, 0.0047160215, 0.03846154, 0.0055829487, 0.0020245318, -0.008731495, -0.009714644, 0.010302884, -0.013441781, -0.0052623227], "split_indices": [1, 0, 40, 1, 1, 0, 42, 0, 0, 15, 1, 106, 106, 0, 0, 81, 137, 1, 115, 0, 1, 0, 1, 0, 108, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 105.0, 1959.0, 1746.0, 213.0, 139.0, 1607.0, 124.0, 89.0, 1338.0, 269.0, 802.0, 536.0, 119.0, 150.0, 438.0, 364.0, 289.0, 247.0, 103.0, 335.0, 160.0, 204.0, 103.0, 186.0, 90.0, 157.0, 183.0, 152.0, 115.0, 89.0, 95.0, 91.0, 92.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0020255698, -0.0042670825, 0.009360275, -0.011333582, 0.012046004, -0.0019379368, -0.014428194, -0.008893637, 0.011286694, -0.061808664, 0.0031085433, -0.014802958, 0.00012183052, -0.0077284877, 0.010541014, -0.016590139, 0.009121134, -0.0012006157, -0.011775201, -0.04046616, 0.035323594, -0.007887869, 0.002531815, 0.00085519813, 0.007406842], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1], "loss_changes": [1.183069, 1.6931385, 0.0, 2.2709281, 0.0, 1.3559346, 0.0, 1.0167885, 0.0, 1.6085366, 1.4467828, 0.0, 0.0, 1.0345886, 0.0, 1.6860496, 0.0, 1.3480945, 0.0, 1.1447048, 0.5051461, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.009360275, 3.0, 0.012046004, 1.0, -0.014428194, -1.0, 0.011286694, 1.0, 1.0, -0.014802958, 0.00012183052, 1.0, 0.010541014, 1.0, 0.009121134, 1.0, -0.011775201, 1.0, 1.0, -0.007887869, 0.002531815, 0.00085519813, 0.007406842], "split_indices": [0, 84, 0, 0, 0, 102, 0, 0, 0, 124, 73, 0, 0, 125, 0, 64, 0, 59, 0, 122, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1921.0, 132.0, 1818.0, 103.0, 1698.0, 120.0, 1601.0, 97.0, 296.0, 1305.0, 125.0, 171.0, 1180.0, 125.0, 1083.0, 97.0, 940.0, 143.0, 453.0, 487.0, 286.0, 167.0, 288.0, 199.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [0.0030094124, 0.010860039, -0.055629946, 0.0020072595, 0.01725397, -0.013535092, 0.0024744454, -0.026835175, 0.03162467, -0.058342393, 0.09135946, 0.07476227, -0.017321978, 0.03532537, -0.10849196, -0.00019712318, 0.017603064, 0.11418314, -0.0070729153, -0.011621359, 0.053254504, -0.009802093, 0.0175452, -0.049256522, -0.18180312, 0.1790956, -0.0021237636, -0.00043089004, 0.014210239, -0.011868064, 0.0036283887, -0.026660535, -0.010651139, 0.028674394, 0.003625448], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.95523787, 2.6193047, 1.569844, 1.4821033, 0.0, 0.0, 0.0, 3.2733822, 1.8073936, 3.2599938, 1.4619482, 2.6096063, 2.7987485, 4.5218596, 1.962863, 0.0, 0.0, 3.146998, 0.0, 0.0, 1.1967671, 0.0, 0.0, 1.4846421, 1.2897506, 3.721139, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 20, 20, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.1923077, 3.3846154, 1.0, 0.01725397, -0.013535092, 0.0024744454, 1.0, 1.0, -0.30769232, 1.0, 1.0, -0.34615386, -0.46153846, 1.0, -0.00019712318, 0.017603064, 1.0, -0.0070729153, -0.011621359, 1.0, -0.009802093, 0.0175452, 1.0, 1.0, 1.0, -0.0021237636, -0.00043089004, 0.014210239, -0.011868064, 0.0036283887, -0.026660535, -0.010651139, 0.028674394, 0.003625448], "split_indices": [1, 1, 1, 124, 0, 0, 0, 62, 15, 1, 97, 0, 1, 1, 39, 0, 0, 80, 0, 0, 97, 0, 0, 97, 106, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1830.0, 245.0, 1735.0, 95.0, 123.0, 122.0, 879.0, 856.0, 694.0, 185.0, 455.0, 401.0, 242.0, 452.0, 88.0, 97.0, 358.0, 97.0, 167.0, 234.0, 124.0, 118.0, 250.0, 202.0, 242.0, 116.0, 142.0, 92.0, 138.0, 112.0, 95.0, 107.0, 138.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0030961225, 0.01211629, -0.03242481, 0.020905392, -0.0072656013, 0.007036271, -0.063388765, 0.10425924, 0.007957846, -0.024967637, -0.01280643, 0.0021785328, 0.020597702, -0.0099037895, 0.09759835, -0.0076262453, 0.0029467645, -0.10623647, 0.026343837, 0.02380324, -0.004153535, 0.0013954955, -0.15733236, 0.053377893, -0.014106887, -0.026012704, -0.005353964, 0.12035165, 0.017373774, 0.0016241443, 0.027487308, -0.011983591, 0.053253327, 0.022915734, -0.04290752, -0.015972918, 0.005054977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, 33, -1, 35, -1, -1], "loss_changes": [0.6629174, 1.2293696, 1.3335547, 1.6134453, 0.0, 0.0, 0.80013967, 1.6862011, 2.0718575, 0.56403416, 0.0, 0.0, 0.0, 3.7676864, 4.2009087, 0.0, 0.0, 1.8116803, 3.548262, 0.0, 0.0, 0.0, 2.2085524, 1.6276482, 0.0, 0.0, 0.0, 3.796592, 2.1612062, 0.0, 0.0, 0.0, 5.8864484, 0.0, 2.4565132, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 23, 23, 27, 27, 28, 28, 32, 32, 34, 34], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, 34, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.46153846, -0.0072656013, 0.007036271, 1.0, 1.0, 1.0, 0.0, -0.01280643, 0.0021785328, 0.020597702, -0.1923077, 1.0, -0.0076262453, 0.0029467645, 1.0, 1.0, 0.02380324, -0.004153535, 0.0013954955, 1.0, 0.1923077, -0.014106887, -0.026012704, -0.005353964, 1.0, 0.42307693, 0.0016241443, 0.027487308, -0.011983591, 1.0, 0.022915734, 1.0, -0.015972918, 0.005054977], "split_indices": [80, 90, 26, 1, 0, 0, 50, 69, 58, 1, 0, 0, 0, 1, 69, 0, 0, 59, 64, 0, 0, 0, 69, 1, 0, 0, 0, 12, 1, 0, 0, 0, 124, 0, 97, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1650.0, 419.0, 1495.0, 155.0, 97.0, 322.0, 201.0, 1294.0, 202.0, 120.0, 111.0, 90.0, 1079.0, 215.0, 104.0, 98.0, 295.0, 784.0, 107.0, 108.0, 88.0, 207.0, 675.0, 109.0, 104.0, 103.0, 236.0, 439.0, 141.0, 95.0, 91.0, 348.0, 123.0, 225.0, 100.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00040276308, -0.021481872, 0.023711326, -0.09751028, -0.0007468481, 0.010047982, 0.007407334, -0.0016256202, -0.017459745, 0.038336057, -0.053961433, -0.075980045, 0.050559927, -0.004439449, 0.01236214, -0.101004966, 0.0057598907, 0.0037205778, -0.018063819, 0.113405, -0.050656877, 0.00778076, -0.068055965, -0.0052157, -0.016374545, 0.028192524, 0.050555218, -0.012368093, 0.0037904175, -0.008575079, -0.0050164503, -0.010974346, 0.015100907], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0523351, 1.6773434, 1.2503808, 1.4281108, 1.7386968, 0.0, 2.9650664, 0.0, 0.0, 1.758396, 1.8578604, 3.3286753, 3.4540105, 1.679559, 0.0, 0.7631209, 0.0, 0.0, 0.0, 3.5481381, 1.3451544, 0.0, 0.05730164, 0.0, 0.0, 0.0, 3.9290395, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 20, 20, 22, 22, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.42307693, 1.0, 1.0, 0.010047982, -0.07692308, -0.0016256202, -0.017459745, 1.0, 1.0, 1.0, 1.0, 1.0, 0.01236214, 1.0, 0.0057598907, 0.0037205778, -0.018063819, 0.1923077, 1.0, 0.00778076, 1.0, -0.0052157, -0.016374545, 0.028192524, 1.0, -0.012368093, 0.0037904175, -0.008575079, -0.0050164503, -0.010974346, 0.015100907], "split_indices": [39, 89, 1, 71, 12, 0, 1, 0, 0, 42, 50, 93, 109, 126, 0, 71, 0, 0, 0, 1, 93, 0, 122, 0, 0, 0, 69, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1064.0, 999.0, 228.0, 836.0, 175.0, 824.0, 111.0, 117.0, 482.0, 354.0, 281.0, 543.0, 321.0, 161.0, 249.0, 105.0, 135.0, 146.0, 335.0, 208.0, 140.0, 181.0, 140.0, 109.0, 91.0, 244.0, 114.0, 94.0, 91.0, 90.0, 94.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010601851, -0.0053615887, 0.008616515, -0.02543372, 0.016479101, -0.11894671, -0.0047025876, 0.003787085, 0.012704355, -0.005134435, -0.017965084, 0.030430049, -0.05144852, 0.025033904, -0.009709916, 0.016075954, -0.033719663, -0.0070346957, -0.014139582, 0.016516915, -0.005536607, 0.004030562, -0.011248699, -0.00836687, 0.009056518, -0.039733555, 0.013367218, -0.0060723275, 0.005445366, 0.005484986, -0.07779762, -0.018971978, 0.016518775, 0.01461813, -0.0110262325], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, 31, -1, 33, -1, -1], "loss_changes": [0.77439475, 0.8623116, 0.0, 1.9870954, 1.3218949, 0.76329565, 1.3778963, 1.8112673, 0.0, 0.0, 0.0, 4.0047274, 1.438165, 2.9902368, 0.0, 0.0, 0.95448875, 1.8025517, 0.0, 0.0, 2.727775, 0.70852435, 0.0, 0.0, 0.0, 1.6561055, 0.0, 0.0, 0.0, 0.0, 3.4623988, 0.0, 2.9260993, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 20, 20, 21, 21, 25, 25, 30, 30, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, 32, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 0.008616515, -0.42307693, 1.0, 1.0, 1.0, 1.0, 0.012704355, -0.005134435, -0.017965084, -0.03846154, 1.0, -0.42307693, -0.009709916, 0.016075954, 1.0, 0.3846154, -0.014139582, 0.016516915, 2.0, 1.0, -0.011248699, -0.00836687, 0.009056518, 0.0, 0.013367218, -0.0060723275, 0.005445366, 0.005484986, 1.0, -0.018971978, 1.0, 0.01461813, -0.0110262325], "split_indices": [114, 39, 0, 1, 88, 124, 106, 7, 0, 0, 0, 1, 71, 1, 0, 0, 17, 1, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 97, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1967.0, 97.0, 1025.0, 942.0, 186.0, 839.0, 845.0, 97.0, 88.0, 98.0, 479.0, 360.0, 698.0, 147.0, 158.0, 321.0, 241.0, 119.0, 125.0, 573.0, 217.0, 104.0, 135.0, 106.0, 460.0, 113.0, 95.0, 122.0, 132.0, 328.0, 150.0, 178.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013180234, -0.0057865977, 0.009061355, -0.023785613, 0.013594043, -0.0012502266, -0.0731388, 0.0056760465, 0.008097616, 0.042644147, -0.043310583, -0.027377216, -0.01699934, -0.024085911, 0.042216256, 0.011479324, -0.0029504916, -0.01903115, -0.010982608, 0.0038528447, -0.011763935, 0.0034303681, -0.009899136, -0.008369641, 0.080462776, -0.011174896, 0.006887904, -0.053752635, 0.008634574, 0.13562535, -0.0022831813, -0.012143173, 0.00046127973, 0.008618473, 0.018254345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, 19, -1, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.8507729, 0.68894416, 0.0, 1.1388859, 0.50738806, 1.2978878, 1.422742, 0.9254695, 0.0, 1.7906884, 0.57976973, 1.2968357, 0.0, 0.96666515, 1.8396056, 0.0, 0.0, 2.1436708, 0.0, 0.0, 0.0, 1.6262832, 0.0, 0.0, 1.669513, 0.0, 0.0, 0.8018747, 0.0, 0.4430542, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 21, 21, 24, 24, 27, 27, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, 20, -1, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.009061355, 1.0, 1.0, 1.0, 1.0, 1.0, 0.008097616, 1.0, 1.0, 1.0, -0.01699934, 1.0, 1.0, 0.011479324, -0.0029504916, 1.0, -0.010982608, 0.0038528447, -0.011763935, 1.0, -0.009899136, -0.008369641, 1.0, -0.011174896, 0.006887904, 1.0, 0.008634574, 1.0, -0.0022831813, -0.012143173, 0.00046127973, 0.008618473, 0.018254345], "split_indices": [114, 39, 0, 105, 88, 97, 111, 12, 0, 126, 2, 97, 0, 83, 17, 0, 0, 111, 0, 0, 0, 59, 0, 0, 121, 0, 0, 15, 0, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1975.0, 96.0, 1024.0, 951.0, 703.0, 321.0, 851.0, 100.0, 344.0, 359.0, 218.0, 103.0, 469.0, 382.0, 172.0, 172.0, 263.0, 96.0, 126.0, 92.0, 343.0, 126.0, 89.0, 293.0, 128.0, 135.0, 203.0, 140.0, 191.0, 102.0, 94.0, 109.0, 93.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0009444357, -0.0058814255, 0.03920405, -0.0124071315, 0.008492541, 0.011051478, -0.0067185475, -0.044060893, 0.018863408, -0.0045121717, 0.002896922, -0.021225518, -0.019537188, 0.06077473, -0.03105219, -0.07557158, 0.015734952, 0.094123125, -0.0038385082, 0.0053097997, -0.08916489, -0.013045022, -0.001464013, -0.010312146, 0.05362044, 0.0003313507, 0.15587366, 0.0017179921, -0.01680927, -0.0013422577, 0.0150279, 0.024981532, 0.008055103, 0.009642367, -0.010708911], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.54163516, 1.0429384, 1.0282793, 1.6253012, 0.0, 0.0, 0.26177004, 2.8194776, 1.7280159, 0.0, 0.0, 1.4241374, 0.0, 1.4847627, 1.8436034, 0.9596803, 1.9002376, 1.8841338, 0.0, 0.0, 1.8717651, 0.0, 0.0, 0.0, 1.7000377, 0.0, 1.4151855, 0.0, 0.0, 2.1090417, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 5.0, 1.0, 1.0, 0.008492541, 0.011051478, 1.0, 0.5, 1.0, -0.0045121717, 0.002896922, 1.0, -0.019537188, 0.7692308, 1.0, 1.0, 1.0, -0.1923077, -0.0038385082, 0.0053097997, 1.0, -0.013045022, -0.001464013, -0.010312146, 1.0, 0.0003313507, 0.15384616, 0.0017179921, -0.01680927, 1.0, 0.0150279, 0.024981532, 0.008055103, 0.009642367, -0.010708911], "split_indices": [1, 0, 97, 124, 0, 0, 39, 1, 15, 0, 0, 15, 0, 1, 5, 13, 89, 1, 0, 0, 93, 0, 0, 0, 109, 0, 1, 0, 0, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1760.0, 314.0, 1642.0, 118.0, 123.0, 191.0, 816.0, 826.0, 92.0, 99.0, 709.0, 107.0, 449.0, 377.0, 287.0, 422.0, 336.0, 113.0, 154.0, 223.0, 151.0, 136.0, 102.0, 320.0, 136.0, 200.0, 95.0, 128.0, 204.0, 116.0, 89.0, 111.0, 106.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.002004124, -0.010897144, 0.03260086, -0.07701255, 0.007742865, 0.018607838, -0.02960597, -0.026393477, -0.019764675, 0.06521906, -0.013041208, -0.012651938, 0.031161347, 0.0059093516, -0.01611003, -0.003798299, 0.017149262, 0.031306863, -0.044591926, -0.0021145705, 0.008881802, 0.11457874, -0.013271349, -0.01488554, -0.019280093, -0.005006934, 0.02174838, -0.084974885, 0.08614749, -0.0154846925, -0.003268353, 0.015220393, 0.0018670437], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, 19, -1, -1, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.6367199, 2.0285165, 4.0385303, 2.2105129, 1.5338529, 0.0, 1.7726401, 2.9364989, 0.0, 3.7399693, 1.3194584, 0.0, 0.55793256, 0.0, 0.0, 0.0, 0.0, 5.354047, 2.4259167, 0.0, 0.0, 4.4052124, 0.0, 3.2503347, 0.0, 0.0, 0.0, 0.9901532, 0.83797085, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 17, 17, 18, 18, 21, 21, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, 20, -1, -1, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [0.88461536, 1.0, 1.0, 1.0, -0.34615386, 0.018607838, 1.0, 1.0, -0.019764675, 1.0, 1.0, -0.012651938, 1.0, 0.0059093516, -0.01611003, -0.003798299, 0.017149262, 0.30769232, 1.0, -0.0021145705, 0.008881802, -0.1923077, -0.013271349, 1.0, -0.019280093, -0.005006934, 0.02174838, 1.0, 1.0, -0.0154846925, -0.003268353, 0.015220393, 0.0018670437], "split_indices": [1, 89, 108, 106, 1, 0, 23, 61, 0, 69, 69, 0, 122, 0, 0, 0, 0, 1, 113, 0, 0, 1, 0, 124, 0, 0, 0, 122, 59, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1646.0, 423.0, 362.0, 1284.0, 122.0, 301.0, 255.0, 107.0, 341.0, 943.0, 116.0, 185.0, 156.0, 99.0, 173.0, 168.0, 392.0, 551.0, 97.0, 88.0, 260.0, 132.0, 459.0, 92.0, 100.0, 160.0, 271.0, 188.0, 116.0, 155.0, 95.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0016131276, 0.009911064, -0.030946387, 0.01793216, -0.00696093, 0.0052158535, -0.055238605, 0.0023869993, 0.016287735, -0.009708919, -0.02550265, 0.011415562, -0.011188356, 0.002321261, -0.007631322, 0.046042874, -0.02040126, -0.046782386, 0.09750559, 0.028466422, -0.0855, 0.003800826, -0.014527659, 0.023491023, 0.026917309, 0.01663456, -0.01931841, 0.00033842074, -0.014047465, -0.01115166, 0.010373127, -0.01694572, 0.016325032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5587256, 1.0511609, 0.8478972, 3.373034, 0.0, 0.0, 0.40445143, 1.3948566, 0.0, 0.0, 0.47029775, 1.3804691, 0.0, 0.0, 0.0, 2.8662243, 2.0773401, 1.7871972, 3.7438743, 2.4575233, 1.3681862, 0.0, 0.0, 0.0, 2.7115824, 0.0, 7.5927496, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 0.84615386, -0.00696093, 0.0052158535, 1.0, 0.5769231, 0.016287735, -0.009708919, 1.0, 1.0, -0.011188356, 0.002321261, -0.007631322, -0.3846154, 1.0, 1.0, 1.0, -0.3846154, 1.0, 0.003800826, -0.014527659, 0.023491023, -0.15384616, 0.01663456, 1.0, 0.00033842074, -0.014047465, -0.01115166, 0.010373127, -0.01694572, 0.016325032], "split_indices": [80, 1, 26, 1, 0, 0, 122, 1, 0, 0, 97, 69, 0, 0, 0, 1, 97, 108, 59, 1, 81, 0, 0, 0, 1, 0, 59, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1648.0, 420.0, 1497.0, 151.0, 95.0, 325.0, 1352.0, 145.0, 135.0, 190.0, 1253.0, 99.0, 97.0, 93.0, 600.0, 653.0, 214.0, 386.0, 373.0, 280.0, 115.0, 99.0, 131.0, 255.0, 96.0, 277.0, 107.0, 173.0, 91.0, 164.0, 152.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0017552546, 0.009244536, -0.0030446276, -0.0077040815, 0.0031149213, -0.0020798352, 0.007915543, -0.017070044, 0.035195604, -0.03703759, 0.021089079, 0.16728592, -0.0545347, -0.060497545, 0.009237674, -0.038183134, 0.013669093, 0.011104637, 0.023017742, 0.00197089, -0.014982375, -0.02895221, -0.019879228, 0.0037231036, -0.07862874, -0.013279808, -0.005772322, -0.003051055, -0.012838729, 0.008578339, -0.036655303, -0.008280612, 0.0028420638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [0.9006398, 0.0, 0.89561415, 0.0, 0.71655196, 0.94878566, 0.0, 0.92271423, 5.7721696, 2.4136634, 2.8504229, 0.6967864, 2.0516353, 2.9359982, 0.0, 0.83879745, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3191099, 0.0, 0.0, 0.42857766, 0.0, 1.2667259, 0.0, 0.0, 0.0, 1.0061083, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 21, 21, 24, 24, 26, 26, 30, 30], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.009244536, -0.5, -0.0077040815, 1.0, 1.0, 0.007915543, 1.0, 1.0, 1.3846154, 1.0, -0.07692308, 1.0, 1.0, 0.009237674, 1.0, 0.013669093, 0.011104637, 0.023017742, 0.00197089, -0.014982375, -0.26923078, -0.019879228, 0.0037231036, 1.0, -0.013279808, 0.0, -0.003051055, -0.012838729, 0.008578339, 1.0, -0.008280612, 0.0028420638], "split_indices": [1, 0, 1, 0, 90, 105, 0, 109, 109, 1, 2, 1, 97, 113, 0, 69, 0, 0, 0, 0, 0, 1, 0, 0, 59, 0, 0, 0, 0, 0, 83, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 104.0, 1965.0, 151.0, 1814.0, 1698.0, 116.0, 1211.0, 487.0, 795.0, 416.0, 197.0, 290.0, 673.0, 122.0, 275.0, 141.0, 104.0, 93.0, 163.0, 127.0, 548.0, 125.0, 96.0, 179.0, 100.0, 448.0, 91.0, 88.0, 113.0, 335.0, 196.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004837827, 0.008064902, 0.00091521704, -0.006231608, 0.006140656, -0.003458179, 0.059475284, 0.037763786, -0.016723381, -0.0025858274, 0.10451245, 0.0114780655, -0.02362646, -0.049681034, 0.0050589703, 0.01889643, 0.0020060565, -0.009800601, 0.004196275, -0.08485389, 0.0049975375, -0.023183856, 0.078665935, -0.044032823, -0.019997843, -0.06582886, 0.055360027, -0.0028608881, 0.020143604, 0.00043019853, -0.008623563, -0.022460813, 0.0064724013, 0.011084635, -7.5678625e-05, -0.008915844, 0.006951795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [0.61051625, 0.0, 0.64496267, 0.0, 0.9230466, 0.8355373, 1.0568752, 1.7588477, 0.8298869, 0.0, 1.283781, 0.0, 1.0098487, 1.6123924, 1.4468929, 0.0, 0.0, 0.0, 0.0, 1.5978315, 0.0, 1.6848004, 2.541838, 0.5120063, 0.0, 3.742461, 0.5511281, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3505182, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 12, 12, 13, 13, 14, 14, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25, 26, 26, 32, 32], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008064902, -0.5, -0.006231608, 1.0, 0.0, 1.0, 1.0, 1.0, -0.0025858274, 1.0, 0.0114780655, 1.0, 1.0, 1.0, 0.01889643, 0.0020060565, -0.009800601, 0.004196275, 1.0, 0.0049975375, 1.0, 1.0, 1.0, -0.019997843, -0.07692308, 0.03846154, -0.0028608881, 0.020143604, 0.00043019853, -0.008623563, -0.022460813, 1.0, 0.011084635, -7.5678625e-05, -0.008915844, 0.006951795], "split_indices": [1, 0, 1, 0, 42, 0, 109, 122, 39, 0, 69, 0, 13, 44, 105, 0, 0, 0, 0, 0, 0, 109, 126, 122, 0, 1, 1, 0, 0, 0, 0, 0, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 101.0, 1952.0, 149.0, 1803.0, 1528.0, 275.0, 372.0, 1156.0, 95.0, 180.0, 165.0, 207.0, 460.0, 696.0, 90.0, 90.0, 97.0, 110.0, 340.0, 120.0, 503.0, 193.0, 251.0, 89.0, 326.0, 177.0, 103.0, 90.0, 117.0, 134.0, 102.0, 224.0, 89.0, 88.0, 89.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0016229887, 0.010711859, -0.042961948, 0.044491235, -0.021904044, 0.0073499084, -0.015875751, 0.016453598, 0.022698617, -0.07647982, 0.016544642, 0.044795033, -0.011172696, -0.022831075, -0.022193981, 0.08069682, -0.058800034, 0.08585979, -0.0055034794, -0.009625147, 0.005822502, -0.022657875, 0.026548252, -0.012774991, 0.005637756, -0.0023243353, 0.1370019, -0.0035924248, -0.00092407095, 0.02428629, 0.0063521895], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [0.8351722, 1.8861871, 4.7064996, 4.303167, 1.8276777, 0.0, 0.0, 2.6483266, 0.0, 2.8093474, 2.4699316, 2.4473944, 0.0, 1.5651581, 0.0, 5.2711782, 1.8662483, 2.3602412, 0.0, 0.0, 0.0, 0.031505354, 0.0, 0.0, 0.0, 0.0, 2.2402554, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.03846154, 0.84615386, -0.115384616, 0.0073499084, -0.015875751, 1.0, 0.022698617, -0.26923078, 1.0769231, 0.1923077, -0.011172696, 1.0, -0.022193981, 1.0, 3.0384614, 1.0, -0.0055034794, -0.009625147, 0.005822502, 1.0, 0.026548252, -0.012774991, 0.005637756, -0.0023243353, 1.0, -0.0035924248, -0.00092407095, 0.02428629, 0.0063521895], "split_indices": [64, 108, 1, 1, 1, 0, 0, 119, 0, 1, 1, 1, 0, 50, 0, 71, 1, 127, 0, 0, 0, 59, 0, 0, 0, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1712.0, 349.0, 841.0, 871.0, 174.0, 175.0, 729.0, 112.0, 360.0, 511.0, 597.0, 132.0, 263.0, 97.0, 276.0, 235.0, 423.0, 174.0, 138.0, 125.0, 177.0, 99.0, 147.0, 88.0, 135.0, 288.0, 89.0, 88.0, 118.0, 170.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046187663, -0.008482126, 0.0075942413, 0.0027075466, -0.030726455, 0.013346082, -0.008139437, -0.015117452, 0.004492269, 0.06651483, -0.012730306, 0.016757777, -0.030206766, 0.028996358, 0.017639032, -0.07123182, 0.016075566, -0.09038008, 0.007930865, 0.06595979, -0.004689661, -0.018561212, -0.00016090092, -0.036810853, 0.064346455, -0.0016527988, -0.017654082, 0.0018770155, 0.012226556, -0.012439265, 0.007915392, -0.0038730302, 0.016374191], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, 15, -1, 17, 19, -1, 21, 23, 25, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6461274, 0.49308443, 0.0, 1.1792423, 2.8124642, 1.6221454, 0.0, 0.0, 2.9030209, 1.5871079, 1.322872, 0.0, 2.78753, 0.80511093, 0.0, 2.06254, 1.342812, 1.7371399, 0.0, 0.51281, 0.0, 0.0, 0.0, 2.549257, 2.8174748, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, 16, -1, 18, 20, -1, 22, 24, 26, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0075942413, 1.0, -0.3846154, 1.0, -0.008139437, -0.015117452, 1.0, 1.0, 1.0, 0.016757777, 1.0, 1.0, 0.017639032, -0.30769232, 1.0, 1.0, 0.007930865, 0.34615386, -0.004689661, -0.018561212, -0.00016090092, 1.0, 1.0, -0.0016527988, -0.017654082, 0.0018770155, 0.012226556, -0.012439265, 0.007915392, -0.0038730302, 0.016374191], "split_indices": [114, 121, 0, 73, 1, 108, 0, 0, 5, 71, 69, 0, 122, 127, 0, 1, 50, 39, 0, 1, 0, 0, 0, 111, 71, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1981.0, 95.0, 1318.0, 663.0, 1170.0, 148.0, 150.0, 513.0, 385.0, 785.0, 90.0, 423.0, 287.0, 98.0, 259.0, 526.0, 273.0, 150.0, 193.0, 94.0, 98.0, 161.0, 251.0, 275.0, 147.0, 126.0, 105.0, 88.0, 143.0, 108.0, 135.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025075541, -0.010034688, 0.026455158, -0.019844577, 0.016125254, -0.0004462068, 0.011558294, -0.0035941314, -0.12442279, -0.012240873, 0.056179233, -0.013170623, 0.012099999, -0.005927263, -0.018305792, -0.005269185, 0.014557746, -0.033208758, 0.055857528, -0.061052885, 0.024785047, 0.016156543, 0.0010036844, -0.113887, -0.009495715, -0.0047666044, 0.0089380015, 0.0050165597, -0.0045570727, -0.020547053, -0.057563115, -0.010889446, 0.07287892, -0.0014648209, -0.010313252, 0.0041835243, 0.010225355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, 15, 17, -1, -1, -1, -1, -1, 19, 21, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1, -1], "loss_changes": [0.45127285, 2.760747, 1.0238007, 2.6409333, 0.0, 2.2652278, 0.0, 1.6048195, 0.79839754, 0.0, 2.180166, 1.727611, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5631137, 1.6293738, 1.7814806, 1.4695126, 0.0, 0.42359218, 1.6661434, 2.710207, 0.0, 0.0, 0.0, 0.0, 0.0, 0.39112085, 0.0, 0.16505289, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23, 24, 24, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, 16, 18, -1, -1, -1, -1, -1, 20, 22, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.016125254, 1.0, 0.011558294, 5.0, 1.0, -0.012240873, 1.0, 1.0, 0.012099999, -0.005927263, -0.018305792, -0.005269185, 0.014557746, 1.0, 1.0, 1.0, 1.0, 0.016156543, 1.0, 1.0, 1.0, -0.0047666044, 0.0089380015, 0.0050165597, -0.0045570727, -0.020547053, 1.0, -0.010889446, 0.0, -0.0014648209, -0.010313252, 0.0041835243, 0.010225355], "split_indices": [113, 102, 64, 64, 0, 89, 0, 0, 71, 0, 109, 74, 0, 0, 0, 0, 0, 116, 122, 111, 71, 0, 13, 122, 39, 0, 0, 0, 0, 0, 12, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1643.0, 427.0, 1554.0, 89.0, 328.0, 99.0, 1345.0, 209.0, 104.0, 224.0, 1249.0, 96.0, 99.0, 110.0, 101.0, 123.0, 968.0, 281.0, 654.0, 314.0, 96.0, 185.0, 323.0, 331.0, 148.0, 166.0, 90.0, 95.0, 123.0, 200.0, 150.0, 181.0, 103.0, 97.0, 88.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0023334958, -0.0027577109, 0.05354667, -0.00863582, 0.0028188308, 0.013976618, -0.0036462669, -0.0025878148, 0.0073278258, -0.015711462, 0.04367303, -0.00023035906, -0.09203184, 0.01956777, -0.014790296, -0.026526166, 0.02513505, -0.02179139, -0.0004114187, -0.011828485, 0.01241348, -0.08414209, 0.008257431, 0.09489025, -0.0165935, -0.014826873, -0.0007766504, 0.006245383, -0.00582733, 0.002664528, 0.017103724, 0.008247963, -0.07235095, 0.0025039765, -0.016359994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, -1, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.5363359, 0.87226343, 1.4434645, 0.0, 0.6681851, 0.0, 0.0, 0.98898363, 0.0, 1.499353, 3.1992123, 0.70368916, 2.368392, 0.0, 3.738278, 1.0381184, 1.5630909, 0.0, 0.0, 0.0, 0.0, 0.9550531, 1.1646496, 1.0445259, 1.856086, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9106632, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 21, 21, 22, 22, 23, 23, 24, 24, 32, 32], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, -1, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, -0.53846157, 1.0, -0.00863582, 5.0, 0.013976618, -0.0036462669, 1.0, 0.0073278258, 1.0, -0.23076923, 1.0, -0.07692308, 0.01956777, 1.0, 1.0, 1.0, -0.02179139, -0.0004114187, -0.011828485, 0.01241348, 0.8076923, 0.115384616, 1.0, 0.0, -0.014826873, -0.0007766504, 0.006245383, -0.00582733, 0.002664528, 0.017103724, 0.008247963, -0.03846154, 0.0025039765, -0.016359994], "split_indices": [125, 1, 53, 0, 0, 0, 0, 61, 0, 0, 1, 13, 1, 0, 53, 17, 17, 0, 0, 0, 0, 1, 1, 108, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1871.0, 186.0, 117.0, 1754.0, 95.0, 91.0, 1629.0, 125.0, 1269.0, 360.0, 1055.0, 214.0, 100.0, 260.0, 518.0, 537.0, 88.0, 126.0, 149.0, 111.0, 195.0, 323.0, 201.0, 336.0, 106.0, 89.0, 178.0, 145.0, 106.0, 95.0, 121.0, 215.0, 104.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0022956613, 0.006013979, -0.0071507283, 0.00038401523, 0.06218779, -0.0050245584, 0.00748078, 0.014125119, -0.0017763934, -0.022860128, 0.0112734595, 0.0016919392, -0.02201137, 0.09113587, -0.017767413, -0.06884412, 0.030299013, 0.020032668, 0.0016759493, -0.050796375, 0.0115128625, -0.011198471, -0.002895938, -0.049901634, 0.09214389, -0.019431343, -0.014367573, -0.013362874, 0.0015446324, 0.014825455, 0.005108729, -0.050385196, 0.007955292, 0.00012457847, -0.013985968, -0.008681451, 0.008895367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1], "loss_changes": [0.56640846, 0.62144387, 0.0, 0.7189126, 1.1315053, 0.48398963, 0.0, 0.0, 0.0, 3.850172, 2.0177686, 1.4266058, 0.0, 1.8841218, 2.8004487, 0.35101247, 2.4948797, 0.0, 0.0, 1.4886262, 0.0, 0.0, 0.0, 1.1982359, 0.65425396, 1.1704266, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3151271, 0.0, 1.4364262, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 23, 23, 24, 24, 25, 25, 31, 31, 33, 33], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0071507283, 5.0, 1.0, 1.0, 0.00748078, 0.014125119, -0.0017763934, 2.0, 0.0, 0.0, -0.02201137, 1.0, 2.0, 1.0, 1.0, 0.020032668, 0.0016759493, 1.0, 0.0115128625, -0.011198471, -0.002895938, 1.0, 1.0, 1.0, -0.014367573, -0.013362874, 0.0015446324, 0.014825455, 0.005108729, 1.0, 0.007955292, 1.0, -0.013985968, -0.008681451, 0.008895367], "split_indices": [117, 125, 0, 0, 15, 13, 0, 0, 0, 0, 0, 0, 0, 127, 0, 2, 17, 0, 0, 113, 0, 0, 0, 97, 126, 7, 0, 0, 0, 0, 0, 116, 0, 124, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1965.0, 99.0, 1786.0, 179.0, 1665.0, 121.0, 90.0, 89.0, 795.0, 870.0, 707.0, 88.0, 232.0, 638.0, 204.0, 503.0, 94.0, 138.0, 511.0, 127.0, 98.0, 106.0, 219.0, 284.0, 382.0, 129.0, 96.0, 123.0, 120.0, 164.0, 291.0, 91.0, 186.0, 105.0, 94.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018737438, 0.002533685, -0.00896662, 0.010665733, -0.051490843, 0.0018995653, 0.016346095, 0.009200273, -0.020898381, -0.0067490973, 0.01120601, -0.0664999, 0.007597008, 0.0018361852, -0.013793416, 0.052869275, -0.002892905, -0.0068876194, 0.0129395025, 0.033659626, -0.070897624, 0.0036939692, 0.017963909, -0.11672336, 0.0027900948, 0.053090923, -0.04369793, -0.0038451634, -0.017370054, 0.0099334335, -0.003425772, 8.808925e-05, -0.009276922], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, -1, -1, 17, 19, -1, -1, 21, 23, 25, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8013509, 0.8663588, 0.0, 2.29578, 5.8306017, 1.544394, 0.0, 0.0, 0.0, 1.2883586, 0.0, 1.7640533, 0.57558155, 0.0, 0.0, 2.1241994, 2.445973, 0.0, 0.0, 2.7995963, 1.5574661, 1.2430793, 0.0, 1.0480292, 0.0, 1.0502183, 0.59282374, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, -1, -1, 18, 20, -1, -1, 22, 24, 26, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.0, -0.00896662, 1.0, 1.0, 1.0, 0.016346095, 0.009200273, -0.020898381, -1.0, 0.01120601, 1.0, 1.0, 0.0018361852, -0.013793416, 1.0, 1.0, -0.0068876194, 0.0129395025, 1.0, 1.0, 1.0, 0.017963909, 1.0, 0.0027900948, 1.0, 1.0, -0.0038451634, -0.017370054, 0.0099334335, -0.003425772, 8.808925e-05, -0.009276922], "split_indices": [117, 0, 0, 102, 109, 125, 0, 0, 0, 0, 0, 127, 89, 0, 0, 124, 23, 0, 0, 61, 80, 13, 0, 124, 0, 109, 108, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1972.0, 99.0, 1714.0, 258.0, 1621.0, 93.0, 135.0, 123.0, 1503.0, 118.0, 291.0, 1212.0, 133.0, 158.0, 228.0, 984.0, 88.0, 140.0, 640.0, 344.0, 531.0, 109.0, 235.0, 109.0, 260.0, 271.0, 99.0, 136.0, 170.0, 90.0, 142.0, 129.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00019729961, 0.0067518274, -0.0034250456, -0.012920597, 0.025808664, -0.009992966, -0.004161725, -0.011326704, 0.01735845, 0.07719809, -0.018382007, -0.01581236, 0.040696044, 0.019283062, -0.0030651453, 0.00047313294, -0.06130298, -0.005438965, 0.019196877, -0.0238295, 0.023880098, -0.020874526, -0.018031705, -0.07213751, 0.0051632025, -0.009566112, 0.060278878, -0.009993479, 0.007863234, -0.014859519, 0.0010303777, 0.011826117, -0.0007367103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, -1, 3, 5, 7, -1, 9, 11, -1, 13, 15, -1, 17, -1, -1, 19, 21, -1, -1, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5062532, 0.0, 0.5468526, 1.1332443, 2.6505644, 0.0, 1.5630523, 2.9477966, 0.0, 2.5066547, 0.93067324, 0.0, 4.0994043, 0.0, 0.0, 0.45451182, 1.6888548, 0.0, 0.0, 1.4289956, 1.7709198, 2.0611644, 0.0, 1.506482, 0.0, 0.0, 1.2237483, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 23, 23, 26, 26], "right_children": [2, -1, 4, 6, 8, -1, 10, 12, -1, 14, 16, -1, 18, -1, -1, 20, 22, -1, -1, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0067518274, 1.0, -0.46153846, 1.0, -0.009992966, -0.30769232, -0.30769232, 0.01735845, 1.0, 1.0, -0.01581236, 1.0, 0.019283062, -0.0030651453, 1.0, 1.0, -0.005438965, 0.019196877, 1.0, -1.0, 1.0, -0.018031705, 1.0, 0.0051632025, -0.009566112, 1.0, -0.009993479, 0.007863234, -0.014859519, 0.0010303777, 0.011826117, -0.0007367103], "split_indices": [1, 0, 0, 1, 42, 0, 1, 1, 0, 59, 15, 0, 15, 0, 0, 17, 50, 0, 0, 50, 0, 7, 0, 116, 0, 0, 137, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 106.0, 1970.0, 1487.0, 483.0, 136.0, 1351.0, 386.0, 97.0, 201.0, 1150.0, 101.0, 285.0, 97.0, 104.0, 799.0, 351.0, 175.0, 110.0, 392.0, 407.0, 262.0, 89.0, 239.0, 153.0, 95.0, 312.0, 146.0, 116.0, 124.0, 115.0, 168.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003018646, 0.009195196, -0.02809774, 0.017548395, -0.007290107, -0.0132599985, 0.004635445, 0.0711794, 0.00053248263, 0.09850445, -0.05227263, 0.01776999, -0.0006189251, 0.020335404, -0.07191721, 0.025089288, -0.005388397, -0.11920651, 0.006078373, 0.00731571, -0.0078586815, 0.06512182, -0.015236575, -0.013300019, -0.0015972018, -0.004280128, -0.018369531, 0.1579749, -0.006869584, -0.08349666, 0.04738734, 0.0066979737, 0.025671428, -0.01689068, 0.0010904007, 0.012381203, -0.001502613], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.63130933, 0.95047224, 2.3089669, 1.148026, 0.0, 0.0, 2.745739, 2.3173428, 1.3701532, 4.5051136, 2.4215364, 0.0, 1.041141, 1.1948563, 0.700546, 0.0, 0.0, 0.9903827, 0.0, 0.0, 0.0, 4.125227, 1.7868303, 0.0, 0.0, 0.0, 0.0, 1.7610211, 0.0, 1.6125557, 1.039845, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 17, 17, 21, 21, 22, 22, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.3846154, 0.0, -0.007290107, -0.0132599985, 1.0, 1.0, 1.0384616, 0.15384616, 0.3846154, 0.01776999, -0.03846154, 1.0, 1.4230769, 0.025089288, -0.005388397, -0.15384616, 0.006078373, 0.00731571, -0.0078586815, 1.0, 1.0, -0.013300019, -0.0015972018, -0.004280128, -0.018369531, 1.0, -0.006869584, 1.0, 1.0, 0.0066979737, 0.025671428, -0.01689068, 0.0010904007, 0.012381203, -0.001502613], "split_indices": [121, 88, 1, 0, 0, 0, 69, 122, 1, 1, 1, 0, 1, 126, 1, 0, 0, 1, 0, 0, 0, 58, 97, 0, 0, 0, 0, 39, 0, 69, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1386.0, 675.0, 1258.0, 128.0, 161.0, 514.0, 303.0, 955.0, 194.0, 320.0, 122.0, 181.0, 750.0, 205.0, 97.0, 97.0, 201.0, 119.0, 93.0, 88.0, 332.0, 418.0, 98.0, 107.0, 92.0, 109.0, 196.0, 136.0, 200.0, 218.0, 102.0, 94.0, 105.0, 95.0, 98.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035533262, 0.0005770982, -0.008304378, -0.03763505, 0.010821314, -0.11591953, 0.0076317484, 0.08650196, -0.002928299, -0.018897735, -0.0035368577, -0.002256119, 0.019556515, -0.037483033, 0.019816587, 0.04024154, -0.096301645, 0.045643125, -0.011889689, -0.011618112, 0.014336466, -0.03144125, -0.024241567, 0.12334869, -0.0053873924, 0.0072434307, -0.015902756, 0.0030980331, 0.018244147, 0.072007954, -0.0720334, 0.0014912336, 0.013034473, -0.017616011, 0.00037708343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, -1, -1, 15, 17, 19, 21, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.6780003, 0.76842314, 0.0, 3.7020965, 1.6108177, 1.4476786, 0.0, 2.8309565, 1.029586, 0.0, 0.0, 0.0, 0.0, 2.3772588, 2.8301659, 3.6132975, 2.8051953, 2.640927, 0.0, 0.0, 0.0, 2.7168856, 0.0, 1.4409914, 2.0735521, 0.0, 0.0, 0.0, 0.0, 0.6195235, 1.7049419, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, -1, -1, 16, 18, 20, 22, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008304378, 1.0, -0.42307693, 1.0, 0.0076317484, 1.0, 0.03846154, -0.018897735, -0.0035368577, -0.002256119, 0.019556515, 1.0, 1.0, 1.0, 1.0, 1.0, -0.011889689, -0.011618112, 0.014336466, 1.0, -0.024241567, 1.0, 1.0384616, 0.0072434307, -0.015902756, 0.0030980331, 0.018244147, 0.42307693, 1.3846154, 0.0014912336, 0.013034473, -0.017616011, 0.00037708343], "split_indices": [43, 89, 0, 97, 1, 50, 0, 69, 1, 0, 0, 0, 0, 39, 64, 17, 121, 124, 0, 0, 0, 93, 0, 122, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1963.0, 102.0, 415.0, 1548.0, 246.0, 169.0, 238.0, 1310.0, 129.0, 117.0, 119.0, 119.0, 520.0, 790.0, 224.0, 296.0, 666.0, 124.0, 89.0, 135.0, 205.0, 91.0, 264.0, 402.0, 113.0, 92.0, 103.0, 161.0, 186.0, 216.0, 94.0, 92.0, 91.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.008894431, 0.006003138, 0.0068075554, -0.012903145, 0.02616189, -0.0043241126, -0.009686811, 0.0075855805, 0.010359682, 0.024301648, -0.044102777, 0.025034394, -0.010122256, -0.015607698, 0.07563804, -0.08591482, 0.0063137347, -0.008922567, 0.043742474, -0.010964195, 0.03187959, 0.015870815, -0.0033751265, -0.014445397, -0.0024340278, -0.0037024578, 0.067954116, -0.0075284517, 0.016024103, 0.033790022, 0.020344576, 0.01472147, -0.008021416, 0.0049686944, -0.01102656], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, 17, -1, 19, 21, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, 33, -1, -1], "loss_changes": [0.35265747, 0.7489147, 0.0, 0.7304226, 1.3679707, 1.047599, 0.0, 1.4562052, 0.0, 1.0961093, 1.7263126, 1.4129443, 0.0, 1.3440952, 2.1263537, 0.9984524, 0.0, 0.0, 1.1107256, 0.0, 2.7511482, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0228503, 0.0, 0.0, 1.6551151, 0.0, 0.0, 1.5045881, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 20, 20, 26, 26, 29, 29, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, 18, -1, 20, 22, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 0.0068075554, 1.0, 1.0, 1.0, -0.009686811, 1.0, 0.010359682, 1.0, 1.0, -1.0, -0.010122256, 1.0, 1.0, 1.0, 0.0063137347, -0.008922567, 1.0, -0.010964195, 1.0, 0.015870815, -0.0033751265, -0.014445397, -0.0024340278, -0.0037024578, 1.0, -0.0075284517, 0.016024103, 1.0, 0.020344576, 0.01472147, 1.0, 0.0049686944, -0.01102656], "split_indices": [114, 39, 0, 88, 58, 12, 0, 64, 0, 16, 50, 0, 0, 53, 23, 122, 0, 0, 5, 0, 97, 0, 0, 0, 0, 0, 62, 0, 0, 81, 0, 0, 121, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1965.0, 96.0, 1014.0, 951.0, 920.0, 94.0, 767.0, 184.0, 535.0, 385.0, 661.0, 106.0, 301.0, 234.0, 277.0, 108.0, 93.0, 568.0, 101.0, 200.0, 133.0, 101.0, 142.0, 135.0, 131.0, 437.0, 109.0, 91.0, 349.0, 88.0, 94.0, 255.0, 163.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0023538019, -0.00059446006, 0.006267036, -0.007425319, 0.036705613, -0.030530578, 0.017115047, 0.011410302, -0.0050434093, 0.010696369, -0.04731138, 0.071337715, -0.046315238, -0.023355812, -0.018489687, 0.10621578, -0.0063863746, -0.015676089, 0.02864786, -0.052689705, 0.006555684, 0.15449204, -0.00285333, -0.0039209, 0.010361357, -0.0047390917, -0.12987223, 0.021730611, 0.005111052, -0.0065062544, 0.0049134665, -0.007268678, -0.018767254], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, -1, 21, -1, -1, 23, 25, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3663279, 0.5004105, 0.0, 0.9412391, 2.050294, 1.9727106, 2.7686844, 0.0, 0.0, 0.0, 2.5115044, 2.0465555, 3.0716388, 1.692692, 0.0, 2.244288, 0.0, 0.0, 1.1242133, 1.8060629, 0.0, 1.6494274, 0.0, 0.0, 0.0, 0.97820526, 0.61809707, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 19, 19, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, -1, 22, -1, -1, 24, 26, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2692307, 0.006267036, 1.0, 1.0, 1.0, 1.0, 0.011410302, -0.0050434093, 0.010696369, 1.0, 1.0, -0.34615386, 1.0, -0.018489687, 0.7307692, -0.0063863746, -0.015676089, 1.0, 1.0, 0.006555684, 1.0, -0.00285333, -0.0039209, 0.010361357, 1.0, 1.0, 0.021730611, 0.005111052, -0.0065062544, 0.0049134665, -0.007268678, -0.018767254], "split_indices": [114, 1, 0, 124, 115, 104, 15, 0, 0, 0, 64, 0, 1, 0, 0, 1, 0, 0, 39, 12, 0, 111, 0, 0, 0, 59, 71, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1964.0, 96.0, 1660.0, 304.0, 855.0, 805.0, 161.0, 143.0, 93.0, 762.0, 434.0, 371.0, 649.0, 113.0, 345.0, 89.0, 150.0, 221.0, 488.0, 161.0, 254.0, 91.0, 116.0, 105.0, 301.0, 187.0, 158.0, 96.0, 142.0, 159.0, 94.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035538299, 0.0015232028, -0.055006567, 0.0076564536, -0.012080104, -0.013872541, 0.003822573, 0.0011516691, 0.012405782, -0.04909309, 0.012401578, 0.00030172404, -0.013809279, 0.024553804, -0.062904425, -0.0052153226, 0.004589344, 0.007818021, 0.0150789395, -0.0137443235, 0.0010865907, 0.043224905, -0.040048294, 0.0047555887, 0.02382643, -0.014007387, -0.006805365, -0.05314329, 0.05095151, 0.004969857, -0.046960462, -0.012437931, 0.0010310626, 0.013398564, -0.0007072313, -0.017588807, 0.009721662], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, 19, -1, -1, 21, -1, -1, -1, 23, 25, 27, -1, -1, 29, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.54100174, 1.414212, 1.4517858, 1.3591123, 0.0, 0.0, 0.0, 0.96092314, 0.0, 1.3671945, 1.2711232, 0.47830224, 0.0, 2.5267305, 1.0612597, 0.0, 0.0, 1.7897058, 0.0, 0.0, 0.0, 4.55434, 1.4929888, 1.3560688, 0.0, 0.0, 0.7646264, 1.0170465, 1.3586644, 0.0, 3.6619167, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 21, 21, 22, 22, 23, 23, 26, 26, 27, 27, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, 20, -1, -1, 22, -1, -1, -1, 24, 26, 28, -1, -1, 30, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [4.0, 1.0, 0.0, 1.0, -0.012080104, -0.013872541, 0.003822573, -1.0, 0.012405782, 1.0, 1.3461539, 1.0, -0.013809279, 0.88461536, 1.0, -0.0052153226, 0.004589344, -0.03846154, 0.0150789395, -0.0137443235, 0.0010865907, -0.15384616, 0.115384616, 1.0, 0.02382643, -0.014007387, 1.0, 1.0, 1.0, 0.004969857, 1.0, -0.012437931, 0.0010310626, 0.013398564, -0.0007072313, -0.017588807, 0.009721662], "split_indices": [0, 52, 1, 102, 0, 0, 0, 0, 0, 109, 1, 13, 0, 1, 50, 0, 0, 1, 0, 0, 0, 1, 1, 124, 0, 0, 126, 69, 69, 0, 122, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1885.0, 186.0, 1795.0, 90.0, 98.0, 88.0, 1700.0, 95.0, 311.0, 1389.0, 200.0, 111.0, 1196.0, 193.0, 93.0, 107.0, 1056.0, 140.0, 96.0, 97.0, 607.0, 449.0, 507.0, 100.0, 112.0, 337.0, 225.0, 282.0, 140.0, 197.0, 106.0, 119.0, 116.0, 166.0, 104.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0009316019, -0.006308335, 0.041694082, 0.00046576466, -0.0070138113, 0.009514741, -0.00062183215, -0.01921705, 0.020424042, -0.04612028, 0.008705904, -0.05113503, 0.049844205, 0.032947548, -0.09000389, 0.0033769791, -0.010511026, 0.013579295, 0.019076834, -0.008536802, 0.01633138, -0.018016405, -0.16025254, 0.07124776, -0.009251586, -0.010592, 0.008501034, -0.022270406, -0.009596418, 0.016601425, -0.0006124863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.608533, 0.7571137, 0.79649514, 0.62185794, 0.0, 0.0, 0.0, 2.278758, 1.6547498, 2.2067816, 0.0, 1.0494517, 2.8466043, 3.5013294, 2.0683222, 0.0, 0.0, 2.7104268, 0.0, 0.0, 0.0, 1.8293971, 0.83108425, 2.1043797, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.4615384, 1.0, -0.0070138113, 0.009514741, -0.00062183215, 1.0, -0.34615386, -0.30769232, 0.008705904, 1.0, 2.0, -0.46153846, 1.0, 0.0033769791, -0.010511026, 1.0, 0.019076834, -0.008536802, 0.01633138, 1.0, 0.115384616, 0.115384616, -0.009251586, -0.010592, 0.008501034, -0.022270406, -0.009596418, 0.016601425, -0.0006124863], "split_indices": [1, 40, 1, 124, 0, 0, 0, 62, 1, 1, 0, 13, 0, 1, 39, 0, 0, 113, 0, 0, 0, 97, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1751.0, 311.0, 1583.0, 168.0, 147.0, 164.0, 797.0, 786.0, 636.0, 161.0, 229.0, 557.0, 227.0, 409.0, 89.0, 140.0, 443.0, 114.0, 119.0, 108.0, 202.0, 207.0, 287.0, 156.0, 109.0, 93.0, 105.0, 102.0, 129.0, 158.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0028796373, 0.011315378, -0.030353917, 0.01909822, -0.0063867867, -0.0952693, 0.019580979, 0.006660761, 0.013986773, -0.018559083, -0.00029180304, 0.0079937065, -0.007526426, -0.008144509, 0.016207386, 0.026632104, -0.008108995, 0.04389083, -0.061988536, 0.0018887118, 0.084270544, -0.011431939, -0.00084683107, 0.039519366, -0.008176502, 0.1504242, 0.0063699726, 0.084752314, -0.0068840855, 0.028024942, 0.0054622097, -0.010131998, 0.013383974, 0.017296948, -0.00018608638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, -1, 15, 17, -1, 19, 21, 23, 25, -1, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.573315, 0.95436215, 1.341999, 2.220053, 0.0, 1.5014349, 1.3395301, 1.1270921, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2262851, 1.6701916, 0.0, 1.5501745, 0.4985348, 1.410279, 2.401487, 0.0, 0.0, 1.5145489, 0.0, 3.1342568, 2.9376235, 1.6656882, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, -1, 16, 18, -1, 20, 22, 24, 26, -1, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 0.84615386, -0.0063867867, 0.61538464, 1.0, 1.0, 0.013986773, -0.018559083, -0.00029180304, 0.0079937065, -0.007526426, -0.008144509, 1.0, 3.0, -0.008108995, 1.0, 5.0, 1.0, 1.0, -0.011431939, -0.00084683107, 1.0, -0.008176502, -0.30769232, -0.115384616, -0.07692308, -0.0068840855, 0.028024942, 0.0054622097, -0.010131998, 0.013383974, 0.017296948, -0.00018608638], "split_indices": [80, 1, 122, 1, 0, 1, 109, 26, 0, 0, 0, 0, 0, 0, 90, 0, 0, 93, 0, 50, 59, 0, 0, 108, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1631.0, 414.0, 1478.0, 153.0, 180.0, 234.0, 1340.0, 138.0, 91.0, 89.0, 143.0, 91.0, 131.0, 1209.0, 1092.0, 117.0, 914.0, 178.0, 448.0, 466.0, 90.0, 88.0, 309.0, 139.0, 252.0, 214.0, 218.0, 91.0, 107.0, 145.0, 116.0, 98.0, 108.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [9.280842e-05, 0.02680673, -0.007949911, 0.056204155, -0.0065186904, -0.017237578, 0.005222017, 0.011973357, -0.0029322503, 0.0072875256, -0.0044352305, 0.00645183, -0.007958064, 0.07981842, -0.022060528, -0.00065063627, 0.019401895, 0.022355055, -0.056953177, -0.058005158, 0.09582727, -0.099631935, 0.0052030063, -0.014761231, 0.006591958, 0.0008902472, 0.017792288, -0.04416589, -0.021412969, 0.035730176, -0.017610436, 0.0014730766, 0.0057684067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, -1, 9, -1, 11, -1, 13, -1, -1, 15, 17, -1, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [0.44474488, 1.2953962, 3.445797, 1.3637556, 0.0, 0.0, 0.962376, 0.0, 0.9719555, 0.0, 1.9141592, 0.0, 0.0, 2.1984096, 1.6520629, 0.0, 0.0, 2.7690897, 2.7768078, 2.4874172, 1.7483547, 2.7244654, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0464551, 0.0, 0.082983166, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 8, 8, 10, 10, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, -1, 12, -1, 14, -1, -1, 16, 18, -1, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [0.0, 1.0, 1.0, 1.0, -0.0065186904, -0.017237578, -0.46153846, 0.011973357, 1.0, 0.0072875256, 1.0, 0.00645183, -0.007958064, 1.0, 1.0, -0.00065063627, 0.019401895, 0.03846154, 1.0, 1.0, 1.0, 1.0384616, 0.0052030063, -0.014761231, 0.006591958, 0.0008902472, 0.017792288, 1.0, -0.021412969, 0.115384616, -0.017610436, 0.0014730766, 0.0057684067], "split_indices": [0, 80, 104, 122, 0, 0, 1, 0, 97, 0, 89, 0, 0, 97, 124, 0, 0, 1, 80, 109, 39, 1, 0, 0, 0, 0, 0, 113, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 479.0, 1591.0, 363.0, 116.0, 118.0, 1473.0, 175.0, 188.0, 184.0, 1289.0, 100.0, 88.0, 223.0, 1066.0, 127.0, 96.0, 469.0, 597.0, 224.0, 245.0, 429.0, 168.0, 130.0, 94.0, 119.0, 126.0, 289.0, 140.0, 180.0, 109.0, 92.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00043320467, -0.007362202, 0.038395733, -0.05793709, -3.34628e-05, 0.009972913, -0.0030865087, -0.010984353, 0.0016785328, -0.010618736, 0.055302545, 0.021568108, -0.04181977, -0.0020776093, 0.01895087, 0.05319702, -0.008208066, -0.015546027, -0.013843382, -0.0010349021, 0.021455923, 0.011954166, -0.06013383, 0.0129943285, -0.068161234, -0.013085295, -0.0034930278, 0.002623037, -0.015214749, 0.007643485, -0.008195437], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, -1, -1, 21, 23, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [0.5561172, 0.6501202, 1.3296247, 0.8610438, 0.89736384, 0.0, 0.0, 0.0, 0.0, 1.2914821, 2.5117154, 2.0751626, 2.0760503, 0.0, 0.0, 4.2442274, 0.0, 0.0, 3.2354136, 3.1915264, 0.0, 0.0, 1.5581733, 0.0, 1.9026237, 0.0, 1.3545897, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 22, 22, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, -1, -1, 22, 24, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.009972913, -0.0030865087, -0.010984353, 0.0016785328, 1.0, 1.0, 0.23076923, 1.0, -0.0020776093, 0.01895087, 1.0, -0.008208066, -0.015546027, -0.34615386, 0.0, 0.021455923, 0.011954166, 1.0, 0.0129943285, 1.0, -0.013085295, 1.0, 0.002623037, -0.015214749, 0.007643485, -0.008195437], "split_indices": [1, 26, 115, 93, 23, 0, 0, 0, 0, 69, 105, 1, 89, 0, 0, 0, 0, 0, 1, 0, 0, 0, 53, 0, 13, 0, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1754.0, 313.0, 222.0, 1532.0, 166.0, 147.0, 131.0, 91.0, 1286.0, 246.0, 633.0, 653.0, 157.0, 89.0, 485.0, 148.0, 129.0, 524.0, 363.0, 122.0, 135.0, 389.0, 123.0, 240.0, 173.0, 216.0, 113.0, 127.0, 107.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.004970241, 0.0009103656, -0.045640707, -0.0052117547, 0.0062430226, 0.0032204078, -0.012713449, 0.022800308, -0.01357785, -0.008289412, 0.010723026, -0.020738909, 0.0068353075, 0.0045353114, -0.008851159, 0.007814031, -0.046721622, -0.01645027, 0.012263173, -0.01949266, -0.014649185, -0.04428445, 0.0053991596, 0.014721605, -0.045879316, 0.00027245257, -0.0769969, -0.002671777, 0.006845621, 0.000685689, -0.010223865, -0.0011484752, -0.014387386], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.49603257, 0.6824573, 1.6620934, 0.38621134, 0.0, 0.0, 0.0, 0.9948388, 0.7445377, 1.1920199, 0.0, 0.86577755, 0.0, 0.0, 0.0, 1.5490001, 1.6598666, 0.89995754, 0.0, 0.43334413, 0.0, 0.50592935, 0.0, 0.46538645, 0.8054604, 0.0, 0.8499627, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.0062430226, 0.0032204078, -0.012713449, 1.0, 1.0, 1.0, 0.010723026, 1.0, 0.0068353075, 0.0045353114, -0.008851159, 1.0, 1.0, 1.0, 0.012263173, 1.0, -0.014649185, 1.0, 0.0053991596, 1.0, 1.0, 0.00027245257, 1.0, -0.002671777, 0.006845621, 0.000685689, -0.010223865, -0.0011484752, -0.014387386], "split_indices": [0, 125, 109, 5, 0, 0, 0, 105, 73, 93, 0, 2, 0, 0, 0, 113, 113, 93, 0, 121, 0, 126, 0, 12, 12, 0, 122, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1812.0, 262.0, 1648.0, 164.0, 134.0, 128.0, 379.0, 1269.0, 277.0, 102.0, 1167.0, 102.0, 166.0, 111.0, 556.0, 611.0, 459.0, 97.0, 480.0, 131.0, 329.0, 130.0, 209.0, 271.0, 135.0, 194.0, 118.0, 91.0, 140.0, 131.0, 98.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0024758966, -0.0010281929, 0.0062752413, 0.005753067, -0.009348614, -0.0005942181, 0.0124099245, -0.03753461, 0.009703322, -0.012332019, 0.013502372, 0.063078664, -0.00836947, -0.009609184, 0.010218169, 0.122965306, -0.0055653094, -0.06294092, 0.007660453, 0.023142217, -0.004031585, -0.0007041555, -0.01382339, -0.035564553, 0.0480038, -0.08788613, 0.0057537067, 0.12509567, -0.015544886, -0.0007329899, -0.0162049, 0.02809153, -0.0029030258, -0.007028635, 0.006325872], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, 13, 15, 17, -1, -1, 19, -1, 21, 23, -1, -1, -1, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4382699, 1.2295107, 0.0, 1.3724006, 0.0, 0.6596053, 0.0, 1.654974, 1.3080539, 0.0, 2.3033412, 2.4388833, 0.8861484, 0.0, 0.0, 4.037644, 0.0, 0.9680305, 1.3654279, 0.0, 0.0, 0.0, 0.0, 1.8413228, 1.9841303, 1.4457753, 0.0, 4.3949013, 0.9576692, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 23, 23, 24, 24, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, 14, 16, 18, -1, -1, 20, -1, 22, 24, -1, -1, -1, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 1.3461539, 0.0062752413, 1.1923077, -0.009348614, 1.0, 0.0124099245, -0.42307693, -0.34615386, -0.012332019, 1.0, 1.0, -0.1923077, -0.009609184, 0.010218169, 1.0, -0.0055653094, 1.0, 1.0, 0.023142217, -0.004031585, -0.0007041555, -0.01382339, 1.0, 0.1923077, 1.0, 0.0057537067, 1.0, 1.0, -0.0007329899, -0.0162049, 0.02809153, -0.0029030258, -0.007028635, 0.006325872], "split_indices": [1, 1, 0, 1, 0, 89, 0, 1, 1, 0, 13, 126, 1, 0, 0, 97, 0, 109, 59, 0, 0, 0, 0, 116, 1, 39, 0, 69, 108, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1961.0, 114.0, 1827.0, 134.0, 1734.0, 93.0, 378.0, 1356.0, 141.0, 237.0, 343.0, 1013.0, 106.0, 131.0, 228.0, 115.0, 230.0, 783.0, 137.0, 91.0, 132.0, 98.0, 378.0, 405.0, 242.0, 136.0, 183.0, 222.0, 116.0, 126.0, 91.0, 92.0, 131.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0022822863, 0.0065961466, -0.0012176366, -0.008959675, 0.022472356, -0.007958127, -0.0015868, -0.0074673686, 0.013890465, 0.062912814, -0.013025716, -0.010967742, 0.04051673, -0.004724435, 0.014794645, -0.020876123, 0.006428206, -0.004871363, 0.012193131, 0.0054138503, -0.06187287, 0.009538311, -0.016192935, 0.008188814, -0.14564228, 0.011157692, -0.05109749, 0.0046546166, -0.004721621, -0.018366862, -0.011001758, -0.013475648, -0.0007005006, -0.007014373, 0.005425455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, -1, 3, 5, 7, -1, 9, 11, -1, 13, 15, -1, 17, -1, -1, 19, -1, -1, -1, 21, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.46201432, 0.0, 0.36039832, 0.77113307, 1.6872005, 0.0, 0.98939735, 1.8882163, 0.0, 1.8921472, 0.69125605, 0.0, 1.9033395, 0.0, 0.0, 1.1144488, 0.0, 0.0, 0.0, 1.2246865, 2.371087, 0.0, 2.2655528, 0.46754184, 0.24925995, 0.0, 1.6822476, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9502478, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 15, 15, 19, 19, 20, 20, 22, 22, 23, 23, 24, 24, 26, 26, 32, 32], "right_children": [2, -1, 4, 6, 8, -1, 10, 12, -1, 14, 16, -1, 18, -1, -1, 20, -1, -1, -1, 22, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [-0.5769231, 0.0065961466, 1.0, -0.46153846, 1.0, -0.007958127, -0.30769232, 1.0, 0.013890465, 1.0, 1.0, -0.010967742, -0.07692308, -0.004724435, 0.014794645, 1.0, 0.006428206, -0.004871363, 0.012193131, 1.0, 1.0, 0.009538311, -0.115384616, 1.0, 0.26923078, 0.011157692, 0.30769232, 0.0046546166, -0.004721621, -0.018366862, -0.011001758, -0.013475648, 1.0, -0.007014373, 0.005425455], "split_indices": [1, 0, 0, 1, 42, 0, 1, 39, 0, 13, 73, 0, 1, 0, 0, 109, 0, 0, 0, 26, 39, 0, 1, 116, 1, 0, 1, 0, 0, 0, 0, 0, 137, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 108.0, 1965.0, 1481.0, 484.0, 140.0, 1341.0, 385.0, 99.0, 202.0, 1139.0, 123.0, 262.0, 88.0, 114.0, 1034.0, 105.0, 125.0, 137.0, 630.0, 404.0, 122.0, 508.0, 220.0, 184.0, 109.0, 399.0, 130.0, 90.0, 89.0, 95.0, 150.0, 249.0, 110.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0032670477, 0.010157976, -0.044309385, 0.0033469019, 0.012486722, 0.005608644, -0.0153540075, -0.0045454185, 0.010285331, -0.0416079, 0.003989655, 0.0048104315, -0.0103994, 0.0072848136, -0.003854221, 0.025131026, -0.023459407, -0.020656502, 0.019179765, -0.01713321, 0.0050864466, -0.012055564, 0.049366243, 0.0127128335, -0.047869377, 0.010033041, -0.0007653045, -0.013483815, -0.015812617, -0.011005017, 0.0024640565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1], "loss_changes": [0.67634577, 1.4078898, 2.862206, 1.3358576, 0.0, 0.0, 0.0, 0.49853784, 0.0, 1.6510547, 0.6918906, 0.0, 0.0, 0.0, 0.6535003, 3.5409017, 2.895711, 2.5462573, 0.0, 0.0, 3.7161267, 0.0, 0.6218717, 0.0, 1.1179624, 0.0, 0.0, 0.0, 1.1169775, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 22, 22, 24, 24, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.012486722, 0.005608644, -0.0153540075, -1.0, 0.010285331, 1.0, 0.0, 0.0048104315, -0.0103994, 0.0072848136, -0.03846154, -0.1923077, 0.15384616, 1.0, 0.019179765, -0.01713321, 1.0, -0.012055564, 1.0, 0.0127128335, 1.0, 0.010033041, -0.0007653045, -0.013483815, 1.0, -0.011005017, 0.0024640565], "split_indices": [0, 102, 109, 125, 0, 0, 0, 0, 0, 122, 0, 0, 0, 0, 1, 1, 1, 69, 0, 0, 124, 0, 97, 0, 137, 0, 0, 0, 81, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1802.0, 261.0, 1701.0, 101.0, 136.0, 125.0, 1576.0, 125.0, 295.0, 1281.0, 121.0, 174.0, 131.0, 1150.0, 464.0, 686.0, 364.0, 100.0, 111.0, 575.0, 150.0, 214.0, 174.0, 401.0, 113.0, 101.0, 108.0, 293.0, 88.0, 205.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.002243527, 0.004822009, -0.03004929, 0.011646985, -0.0062215403, 0.005222353, -0.05424719, 0.0014222885, 0.0108173825, -0.021027755, -0.01119589, 0.01955757, -0.058165055, -0.006468805, 0.0023060553, 0.0041905507, 0.013245556, -0.008241181, -0.017733814, 0.019963445, -0.009229518, 0.008667364, -0.0074174404, 0.005342825, 0.009841553, 0.024236226, -0.0083260685, -0.0051723467, 0.014026644, 0.003703164, -0.00726482], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, -1, 23, -1, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [0.40530246, 0.7526347, 0.8321667, 1.4735276, 0.0, 0.0, 0.61923915, 1.4588548, 0.0, 0.3946064, 0.0, 1.7956271, 1.874118, 0.0, 0.0, 1.3864135, 0.0, 1.3892848, 0.0, 0.89811486, 0.0, 0.0, 0.0, 1.1048541, 0.0, 1.8562825, 0.0, 1.2359236, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 19, 19, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, -1, 24, -1, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, 0.84615386, -0.0062215403, 0.005222353, 1.0, 0.1923077, 0.0108173825, 0.0, -0.01119589, 0.03846154, 1.0, -0.006468805, 0.0023060553, 3.0, 0.013245556, 1.0, -0.017733814, 1.0, -0.009229518, 0.008667364, -0.0074174404, -0.115384616, 0.009841553, 1.0, -0.0083260685, 1.0, 0.014026644, 0.003703164, -0.00726482], "split_indices": [80, 1, 26, 1, 0, 0, 50, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 126, 0, 0, 0, 0, 0, 1, 0, 64, 0, 105, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1645.0, 418.0, 1493.0, 152.0, 95.0, 323.0, 1350.0, 143.0, 205.0, 118.0, 1035.0, 315.0, 103.0, 102.0, 911.0, 124.0, 222.0, 93.0, 783.0, 128.0, 91.0, 131.0, 660.0, 123.0, 544.0, 116.0, 434.0, 110.0, 267.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025958251, -0.00886483, 0.032941964, -0.059387848, -0.0014736189, 0.007627333, -0.0016028399, -0.01090041, 0.0011035184, -0.006137447, 0.007445766, -0.045987852, 0.007927399, -0.10432951, 0.008256154, -0.017927896, 0.0824019, -0.024055209, -0.0030978878, 0.0013024715, -0.00930688, 0.017689537, -0.0049888934, -0.03861281, 0.07172071, 0.015969558, -0.011374385, 0.015923264, -0.0029826122, 0.009354659, -0.005899982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, 11, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [0.46205926, 0.65835035, 0.6599275, 0.7861789, 0.5446524, 0.0, 0.0, 0.0, 0.0, 0.8121498, 0.0, 2.8349187, 2.0622752, 2.597922, 0.0, 1.1487648, 3.4501715, 0.0, 0.0, 1.7792135, 0.0, 0.0, 0.0, 1.6567355, 2.0350227, 1.360921, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, 12, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.007627333, -0.0016028399, -0.01090041, 0.0011035184, -0.3846154, 0.007445766, 1.0, 1.0, 1.0, 0.008256154, 1.0, 0.1923077, -0.024055209, -0.0030978878, 1.0, -0.00930688, 0.017689537, -0.0049888934, 1.0, 1.0, 1.0, -0.011374385, 0.015923264, -0.0029826122, 0.009354659, -0.005899982], "split_indices": [1, 26, 115, 93, 114, 0, 0, 0, 0, 1, 0, 12, 0, 89, 0, 113, 1, 0, 0, 124, 0, 0, 0, 12, 122, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1763.0, 311.0, 225.0, 1538.0, 165.0, 146.0, 132.0, 93.0, 1449.0, 89.0, 378.0, 1071.0, 260.0, 118.0, 795.0, 276.0, 91.0, 169.0, 633.0, 162.0, 161.0, 115.0, 404.0, 229.0, 234.0, 170.0, 123.0, 106.0, 115.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031417727, 0.003993751, -0.03875735, 0.030043928, -0.020931529, -0.01634262, 0.06657781, 0.010353389, 0.016530134, -0.060328737, 0.007517723, 0.020161789, -0.0067025647, 0.08280628, -0.018242814, -0.105624795, 0.0032509435, 0.065663904, -0.060237348, 0.015907234, 0.0017435357, 0.0048052287, -0.011913682, -0.021687856, 0.0016942899, -0.0019138992, 0.021642465, -0.016079051, 0.00040049275, -0.03499771, 0.035271686, 0.0062249615, -0.012232187, -0.0041600796, 0.015503095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, 19, 21, 23, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.52529883, 1.1181083, 4.5305448, 2.2424908, 0.98632276, 0.0, 3.3738208, 1.5228302, 0.0, 1.5517204, 2.0131862, 0.0, 0.0, 1.0370007, 1.2254913, 3.3817577, 0.0, 3.515861, 1.5245049, 0.0, 0.0, 0.5202288, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.57952, 2.2371054, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 21, 21, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, 20, 22, 24, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.84615386, -0.115384616, -0.01634262, 0.03846154, -0.34615386, 0.016530134, 1.0, 1.0769231, 0.020161789, -0.0067025647, 1.0, 2.0, 1.0, 0.0032509435, 1.0, 1.0, 0.015907234, 0.0017435357, 1.0, -0.011913682, -0.021687856, 0.0016942899, -0.0019138992, 0.021642465, -0.016079051, 0.00040049275, 1.0, 1.0, 0.0062249615, -0.012232187, -0.0041600796, 0.015503095], "split_indices": [64, 108, 124, 1, 1, 0, 1, 1, 0, 61, 1, 0, 0, 69, 0, 111, 0, 71, 59, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 93, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1722.0, 345.0, 842.0, 880.0, 158.0, 187.0, 735.0, 107.0, 369.0, 511.0, 93.0, 94.0, 208.0, 527.0, 248.0, 121.0, 275.0, 236.0, 96.0, 112.0, 429.0, 98.0, 130.0, 118.0, 176.0, 99.0, 92.0, 144.0, 186.0, 243.0, 88.0, 98.0, 148.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00035906216, 0.0024079673, -0.005609497, -0.0009797205, 0.0059813904, 0.0061853398, -0.010239738, 0.00069162133, 0.010696451, -0.04648933, 0.0072318795, -0.00991107, 0.0003577567, -0.008452612, 0.026321877, 0.044768047, -0.054444242, 0.06172039, -0.008860834, 0.013990382, -0.013393545, -0.016396027, -0.00962579, 0.019845761, 0.13813566, -0.0075803683, 0.0041343193, -0.007574973, 0.004567785, -0.006077188, 0.06813654, 0.014344007, 0.013289079, -0.0017932488, 0.015768314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, -1, -1, 15, 17, 19, 21, 23, -1, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.31955, 0.38389066, 0.0, 1.3545014, 0.0, 0.9639085, 0.0, 0.5094582, 0.0, 0.5295525, 0.43415457, 0.0, 0.0, 1.948373, 2.6607068, 2.0417686, 2.0958605, 1.5999297, 0.0, 0.0, 0.7822933, 0.0, 1.1080391, 1.2574677, 0.004923582, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5568527, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 22, 22, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, -1, -1, 16, 18, 20, 22, 24, -1, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.005609497, 1.3461539, 0.0059813904, 1.1923077, -0.010239738, 1.0, 0.010696451, 1.0, 1.0, -0.00991107, 0.0003577567, 1.0, 1.0, -0.30769232, 1.0, 1.0, -0.008860834, 0.013990382, 1.0, -0.016396027, 1.0, 1.0, 1.0, -0.0075803683, 0.0041343193, -0.007574973, 0.004567785, -0.006077188, 1.0, 0.014344007, 0.013289079, -0.0017932488, 0.015768314], "split_indices": [117, 1, 0, 1, 0, 1, 0, 26, 0, 31, 97, 0, 0, 53, 113, 1, 5, 83, 0, 0, 13, 0, 106, 71, 15, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1974.0, 98.0, 1864.0, 110.0, 1741.0, 123.0, 1651.0, 90.0, 201.0, 1450.0, 98.0, 103.0, 796.0, 654.0, 369.0, 427.0, 500.0, 154.0, 140.0, 229.0, 124.0, 303.0, 323.0, 177.0, 107.0, 122.0, 138.0, 165.0, 121.0, 202.0, 88.0, 89.0, 103.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00130204, 0.0055870875, -0.0016834874, -0.005765349, 0.0028529626, -0.006273193, 0.038283926, -0.061713886, 0.0020736603, 0.010041603, -0.016944595, -0.013117455, 0.00034940607, -0.011695043, 0.06874989, -0.0072989664, 0.0053111706, -0.027199397, 0.034936816, -0.0004955255, 0.0148124695, -0.014832759, -0.007459983, -0.003472291, 0.011678702, 0.009455045, -0.03463725, -0.08620645, 0.02784492, 0.0011213953, -0.014338801, -0.0016629844, 0.007420424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, 15, -1, -1, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1], "loss_changes": [0.3391928, 0.0, 0.50120884, 0.0, 0.59043443, 0.67192096, 1.283368, 0.860584, 1.1585734, 0.0, 0.77740955, 0.0, 0.0, 0.75625473, 1.2636721, 0.0, 0.0, 1.8769344, 1.4881343, 0.0, 0.0, 0.0, 1.8713461, 0.0, 0.0, 0.0, 1.7174089, 1.6266305, 0.49689868, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 26, 26, 27, 27, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, 16, -1, -1, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0055870875, -0.5, -0.005765349, 1.0, -0.34615386, -0.03846154, 1.0, 1.0, 0.010041603, 1.0, -0.013117455, 0.00034940607, 1.0, 1.0, -0.0072989664, 0.0053111706, -0.1923077, 1.0, -0.0004955255, 0.0148124695, -0.014832759, 0.115384616, -0.003472291, 0.011678702, 0.009455045, 1.2692307, 1.0, 1.0, 0.0011213953, -0.014338801, -0.0016629844, 0.007420424], "split_indices": [1, 0, 1, 0, 58, 1, 1, 69, 7, 0, 108, 0, 0, 0, 71, 0, 0, 1, 109, 0, 0, 0, 1, 0, 0, 0, 1, 126, 50, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2082.0, 108.0, 1974.0, 148.0, 1826.0, 1452.0, 374.0, 190.0, 1262.0, 176.0, 198.0, 92.0, 98.0, 1046.0, 216.0, 110.0, 88.0, 785.0, 261.0, 112.0, 104.0, 110.0, 675.0, 141.0, 120.0, 142.0, 533.0, 292.0, 241.0, 108.0, 184.0, 123.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001447148, -0.009760547, 0.018671876, -0.036529973, 0.017087722, 0.07045721, -0.01997195, -0.09808309, 0.028940145, 0.07443728, -0.035253838, 0.030536212, 0.016526956, -0.06898496, 0.0061062877, -0.0032087625, -0.016445564, -0.008019957, 0.073715426, 0.015933039, -0.0018123565, -0.009769752, 0.014827875, -0.006812504, 0.011118109, -0.0015676788, -0.0125847, 0.0106958365, 0.0036241547, 0.009581949, -0.0070382007], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.37182587, 0.9774494, 1.3908228, 2.7443547, 2.038199, 1.1241455, 1.580761, 1.5374806, 1.6126313, 2.5459204, 1.1101869, 1.6629139, 0.0, 0.75174, 0.0, 0.0, 0.0, 0.0, 0.2915032, 0.0, 0.0, 0.0, 1.3595533, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 18, 18, 22, 22], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.03846154, 1.0, 1.0, -0.23076923, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.016526956, 1.0, 0.0061062877, -0.0032087625, -0.016445564, -0.008019957, 1.0, 0.015933039, -0.0018123565, -0.009769752, 1.0, -0.006812504, 0.011118109, -0.0015676788, -0.0125847, 0.0106958365, 0.0036241547, 0.009581949, -0.0070382007], "split_indices": [50, 97, 1, 122, 137, 1, 23, 15, 81, 69, 69, 69, 0, 97, 0, 0, 0, 0, 13, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1360.0, 695.0, 681.0, 679.0, 297.0, 398.0, 351.0, 330.0, 324.0, 355.0, 209.0, 88.0, 248.0, 150.0, 176.0, 175.0, 96.0, 234.0, 169.0, 155.0, 158.0, 197.0, 94.0, 115.0, 128.0, 120.0, 124.0, 110.0, 101.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012279346, 0.020242782, -0.007764846, 0.046631165, -0.0054943147, -0.014269232, 0.0030293488, 0.09989939, -0.0015999333, 0.031498324, -0.017272566, 0.025234673, -0.0029317853, 0.08555615, -0.007678403, -0.034071755, 0.0056897416, -0.0020703992, 0.024807168, 0.008401706, -0.08002107, -0.09644468, -0.008697588, -0.00051158044, -0.016539693, -0.0019165162, -0.019743493, 0.00839238, -0.050822776, -0.010269927, -0.016833384, 0.0037867376, -0.004797189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, 13, 15, -1, -1, 17, 19, 21, -1, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1], "loss_changes": [0.2916518, 0.962257, 2.3200989, 1.1977015, 0.0, 0.0, 0.8525128, 3.8215733, 0.0, 1.3003348, 1.072802, 0.0, 0.0, 4.4553833, 2.3615239, 1.1110278, 0.0, 0.0, 0.0, 0.0, 1.2726246, 1.5843084, 1.946945, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6345346, 0.46279207, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 20, 20, 21, 21, 22, 22, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, 14, 16, -1, -1, 18, 20, 22, -1, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1], "split_conditions": [0.0, 1.0, 1.0, 1.0, -0.0054943147, -0.014269232, 1.0, 1.0, -0.0015999333, 1.0, 1.0, 0.025234673, -0.0029317853, 1.0, -0.30769232, 0.1923077, 0.0056897416, -0.0020703992, 0.024807168, 0.008401706, 1.0, -0.15384616, 1.0, -0.00051158044, -0.016539693, -0.0019165162, -0.019743493, 0.00839238, 1.0, 1.2307693, -0.016833384, 0.0037867376, -0.004797189], "split_indices": [0, 105, 104, 97, 0, 0, 16, 115, 0, 69, 42, 0, 0, 0, 1, 1, 0, 0, 0, 0, 39, 1, 124, 0, 0, 0, 0, 0, 2, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 485.0, 1593.0, 359.0, 126.0, 118.0, 1475.0, 194.0, 165.0, 614.0, 861.0, 89.0, 105.0, 258.0, 356.0, 702.0, 159.0, 156.0, 102.0, 157.0, 199.0, 203.0, 499.0, 106.0, 93.0, 115.0, 88.0, 156.0, 343.0, 255.0, 88.0, 112.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0023861257, -0.003062561, 0.033024117, 0.005266654, -0.05089971, -0.0022379365, 0.0579044, -0.0047541, 0.013180147, 0.0018059183, -0.014556149, -0.0022326799, 0.012330296, -0.03961414, 0.01066685, -0.08346405, 0.004996493, -0.0701075, 0.03374524, -0.014170106, -0.0026035863, -0.01786886, 0.0014947678, 0.061600864, -0.008647821, 0.10153229, 0.016395463, -0.01302058, 0.04420346, -0.004832921, 0.020857623, -0.011766144, 0.012946958, 1.0456924e-05, 0.009231131], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, -1, -1, -1, -1, 15, 17, 19, -1, 21, 23, -1, -1, -1, -1, 25, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.34606013, 0.7012649, 0.43145636, 1.9006939, 1.703749, 0.0, 1.1333536, 0.7466917, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6733426, 1.7951685, 0.9565109, 0.0, 1.9763732, 0.8844831, 0.0, 0.0, 0.0, 0.0, 0.815912, 1.9080752, 3.8500245, 3.2135737, 0.0, 0.43915078, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 23, 23, 24, 24, 25, 25, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, -1, -1, -1, -1, 16, 18, 20, -1, 22, 24, -1, -1, -1, -1, 26, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.5769231, 1.0, 1.0, 1.0, -0.0022379365, 1.0, 1.0, 0.013180147, 0.0018059183, -0.014556149, -0.0022326799, 0.012330296, 1.0, 1.0, 1.0, 0.004996493, 1.0, 1.0, -0.014170106, -0.0026035863, -0.01786886, 0.0014947678, 1.0, 1.0, -0.3846154, 1.0, -0.01302058, -0.23076923, -0.004832921, 0.020857623, -0.011766144, 0.012946958, 1.0456924e-05, 0.009231131], "split_indices": [1, 1, 69, 44, 2, 0, 39, 127, 0, 0, 0, 0, 0, 83, 17, 109, 0, 23, 122, 0, 0, 0, 0, 69, 81, 1, 50, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1760.0, 313.0, 1499.0, 261.0, 97.0, 216.0, 1389.0, 110.0, 151.0, 110.0, 97.0, 119.0, 426.0, 963.0, 286.0, 140.0, 214.0, 749.0, 142.0, 144.0, 94.0, 120.0, 452.0, 297.0, 240.0, 212.0, 90.0, 207.0, 100.0, 140.0, 97.0, 115.0, 108.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032298095, -0.005391236, 0.0013898317, 0.004491882, -0.0053802067, 0.02106338, -0.008805204, 0.00028559097, 0.018294742, 0.0145616755, -0.06494953, 0.010536919, -0.033114582, -0.007040193, 0.039550975, -0.01835651, -0.005337577, -0.091058575, 0.018708553, -0.02620458, 0.09886894, -0.008742933, 0.009447849, -0.0153500885, -0.002762508, 0.015072735, -0.040565178, 0.0026226118, -0.008199087, 0.01784061, 0.002775953, 0.0029113942, -0.010745716], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, -1, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.48489416, 0.0, 0.32495272, 0.3959737, 0.0, 2.6908736, 1.307982, 2.488456, 0.0, 1.4947194, 2.0717747, 0.0, 1.6155279, 0.0, 2.1218643, 0.0, 1.597845, 1.0060768, 2.2223704, 0.75462806, 1.6175694, 0.0, 0.0, 0.0, 0.0, 0.0, 0.91355133, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 26, 26], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, -1, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.53846157, -0.005391236, 1.0, 1.0, -0.0053802067, 0.7307692, 1.0, -0.34615386, 0.018294742, -0.34615386, 1.0, 0.010536919, 0.03846154, -0.007040193, 1.0, -0.01835651, 2.3076923, 1.0, 0.1923077, 0.115384616, -0.03846154, -0.008742933, 0.009447849, -0.0153500885, -0.002762508, 0.015072735, 1.0, 0.0026226118, -0.008199087, 0.01784061, 0.002775953, 0.0029113942, -0.010745716], "split_indices": [1, 0, 43, 124, 0, 1, 1, 1, 0, 1, 59, 0, 1, 0, 13, 0, 1, 111, 1, 1, 1, 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 173.0, 1898.0, 1797.0, 101.0, 800.0, 997.0, 709.0, 91.0, 704.0, 293.0, 171.0, 538.0, 160.0, 544.0, 98.0, 195.0, 254.0, 284.0, 258.0, 286.0, 107.0, 88.0, 128.0, 126.0, 88.0, 196.0, 133.0, 125.0, 135.0, 151.0, 96.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0017719539, 0.007311202, -0.037131093, 0.011471703, 0.0012610459, -0.012300066, 0.0072088935, -0.006539132, 0.008514267, -0.00395269, 0.05655102, -0.042619042, 0.019702015, -0.0029494513, 0.013839923, -0.0053141103, -0.011722889, 0.056458484, -0.030060576, 0.0044060163, -0.005103105, 0.09580665, -0.00037774854, -0.084288895, 0.007030935, 0.002496827, 0.013659238, 0.003518998, -0.003756223, -0.0053451317, -0.011662341], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, 9, 11, 13, 15, 17, -1, -1, 19, -1, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.44779566, 1.1820259, 2.4290771, 0.0, 0.832491, 0.0, 0.0, 0.0, 0.9300486, 1.1277527, 2.253656, 1.3025911, 1.3992594, 0.0, 0.0, 0.7042591, 0.0, 0.98401713, 1.7689401, 0.0, 0.0, 0.75118995, 0.23806217, 0.2103914, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, 10, 12, 14, 16, 18, -1, -1, 20, -1, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, -0.5769231, 0.03846154, 0.011471703, -0.46153846, -0.012300066, 0.0072088935, -0.006539132, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0029494513, 0.013839923, 1.0, -0.011722889, 1.0, 1.2692307, 0.0044060163, -0.005103105, 1.0, 0.5, 1.0, 0.007030935, 0.002496827, 0.013659238, 0.003518998, -0.003756223, -0.0053451317, -0.011662341], "split_indices": [0, 1, 1, 0, 1, 0, 0, 0, 58, 69, 71, 71, 50, 0, 0, 39, 0, 23, 1, 0, 0, 81, 1, 39, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1819.0, 259.0, 97.0, 1722.0, 145.0, 114.0, 169.0, 1553.0, 1233.0, 320.0, 468.0, 765.0, 156.0, 164.0, 312.0, 156.0, 440.0, 325.0, 150.0, 162.0, 260.0, 180.0, 211.0, 114.0, 95.0, 165.0, 92.0, 88.0, 108.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0033626317, -0.0015591258, 0.030779354, 0.00536039, -0.049553405, 0.007624881, -0.00097297813, 0.024059758, -0.016986972, -0.012094153, 0.0017412415, 0.037418053, -0.0051673125, 0.015741415, -0.08911066, 0.019279918, 0.01418867, -0.00975146, 0.010157526, 0.0024644719, -0.018717567, 0.045081664, -0.037127078, -0.066775315, 0.0062408345, 0.07214979, -0.0028075408, -0.0080154035, 0.0010734329, -0.0031976965, -0.010868298, 0.02636673, 0.016651893, 0.0073206364, -0.0026389456], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, 15, -1, 17, 19, 21, -1, 23, -1, -1, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.27662393, 0.5771851, 0.57468194, 0.63476205, 1.0469439, 0.0, 0.0, 0.83664453, 1.6334608, 0.0, 0.0, 1.3320909, 0.0, 1.0415603, 2.4095716, 0.8717841, 0.0, 1.5101427, 0.0, 0.0, 0.0, 0.8138727, 0.3871543, 0.2989546, 0.0, 1.2961518, 0.0, 0.0, 0.0, 0.0, 0.0, 0.49915838, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 21, 21, 22, 22, 23, 23, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, 16, -1, 18, 20, 22, -1, 24, -1, -1, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.4615384, -0.03846154, 1.0, 0.007624881, -0.00097297813, 3.0, 1.0, -0.012094153, 0.0017412415, -0.15384616, -0.0051673125, 1.0, 1.0, -0.30769232, 0.01418867, 1.0, 0.010157526, 0.0024644719, -0.018717567, 1.0, 1.0, 1.0, 0.0062408345, 1.0, -0.0028075408, -0.0080154035, 0.0010734329, -0.0031976965, -0.010868298, 1.0, 0.016651893, 0.0073206364, -0.0026389456], "split_indices": [1, 119, 1, 1, 39, 0, 0, 0, 50, 0, 0, 1, 0, 0, 2, 1, 0, 124, 0, 0, 0, 42, 126, 12, 0, 113, 0, 0, 0, 0, 0, 126, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1738.0, 312.0, 1519.0, 219.0, 147.0, 165.0, 827.0, 692.0, 106.0, 113.0, 703.0, 124.0, 476.0, 216.0, 599.0, 104.0, 367.0, 109.0, 100.0, 116.0, 411.0, 188.0, 205.0, 162.0, 300.0, 111.0, 99.0, 89.0, 112.0, 93.0, 202.0, 98.0, 107.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027463306, -0.009660117, 0.029613232, -0.04710118, 0.0018690756, -0.0028067806, 0.06336547, -0.008117934, -0.0162507, 0.021464271, -0.030166877, 0.0024133741, 0.011771772, -0.014910656, 0.05855648, -0.014930953, 0.08417227, 0.022845244, -0.10653584, 0.00028101943, 0.012193116, -0.061863314, 0.04357942, 0.020620827, 0.007732126, 0.010164385, -0.020978563, -0.017318398, -0.002359589, -0.014033531, 0.00087087986, -0.0038732034, 0.017120844, -0.0027260627, 0.004350253, 0.00051226973, -0.004519626], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, -1, 19, 21, 23, 25, 27, -1, -1, 29, 31, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4613253, 0.7333994, 0.70671165, 1.7995567, 0.8154482, 0.0, 0.4883039, 2.8107002, 0.0, 1.8395114, 1.9959012, 0.0, 0.0, 0.0, 0.7171791, 1.4004753, 2.7612202, 1.0048975, 1.1166139, 0.0, 0.0, 1.5672358, 2.3847108, 0.0, 0.22781035, 0.0, 0.11820514, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, -1, 20, 22, 24, 26, 28, -1, -1, 30, 32, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.3846154, 1.0, 2.0, 0.46153846, -0.0028067806, 0.46153846, 1.0, -0.0162507, 1.0, 1.0, 0.0024133741, 0.011771772, -0.014910656, 1.0, 1.0, 1.0, 1.0, 1.1538461, 0.00028101943, 0.012193116, 1.0, 1.0, 0.020620827, -0.115384616, 0.010164385, 1.3076923, -0.017318398, -0.002359589, -0.014033531, 0.00087087986, -0.0038732034, 0.017120844, -0.0027260627, 0.004350253, 0.00051226973, -0.004519626], "split_indices": [74, 1, 97, 0, 1, 0, 1, 109, 0, 105, 127, 0, 0, 0, 69, 97, 69, 97, 1, 0, 0, 122, 109, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1699.0, 363.0, 400.0, 1299.0, 134.0, 229.0, 299.0, 101.0, 806.0, 493.0, 133.0, 96.0, 96.0, 203.0, 510.0, 296.0, 291.0, 202.0, 108.0, 95.0, 283.0, 227.0, 114.0, 182.0, 104.0, 187.0, 112.0, 90.0, 134.0, 149.0, 138.0, 89.0, 92.0, 90.0, 90.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.000677917, -0.0037183343, 0.005897504, -0.0063081146, 0.0048921737, 0.01787523, -0.02207429, -0.013084552, 0.10716977, 0.006397585, -0.044924334, 0.03798716, -0.044087037, 0.0016955031, 0.020126468, 0.038060654, -0.012817043, 0.030057577, -0.0779439, -0.001645905, 0.007986889, -0.010553206, 0.0019179385, 0.105876915, -0.006705453, -0.0070732213, 0.011714782, -0.0015074601, -0.120908625, 0.0049387976, -0.004317857, 0.0042090896, 0.016377497, -0.019595345, 0.00044239615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.37398455, 0.26747194, 0.0, 0.7129915, 0.0, 2.0402305, 0.7364608, 0.86767596, 1.6128616, 2.1474612, 1.5548466, 0.47202262, 0.96393126, 0.0, 0.0, 2.9084358, 0.0, 1.6853392, 1.177706, 0.0, 0.0, 0.0, 0.4174429, 0.91588473, 0.0, 0.0, 0.0, 0.0, 2.4360416, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 22, 22, 23, 23, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.005897504, 1.0, 0.0048921737, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0016955031, 0.020126468, 1.0, -0.012817043, 1.0, 1.0, -0.001645905, 0.007986889, -0.010553206, 1.0, 1.0, -0.006705453, -0.0070732213, 0.011714782, -0.0015074601, 1.0, 0.0049387976, -0.004317857, 0.0042090896, 0.016377497, -0.019595345, 0.00044239615], "split_indices": [66, 114, 0, 2, 0, 93, 13, 53, 13, 64, 5, 111, 69, 0, 0, 108, 0, 93, 93, 0, 0, 0, 106, 122, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1962.0, 100.0, 1870.0, 92.0, 738.0, 1132.0, 548.0, 190.0, 504.0, 628.0, 207.0, 341.0, 97.0, 93.0, 408.0, 96.0, 192.0, 436.0, 90.0, 117.0, 146.0, 195.0, 248.0, 160.0, 89.0, 103.0, 177.0, 259.0, 95.0, 100.0, 118.0, 130.0, 162.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021971974, -0.0072155492, 0.02625636, 0.0006633073, -0.06905265, 0.006292303, -0.0015757512, -0.004640826, 0.0067034187, -0.012876718, -0.0017224916, 0.020847782, -0.028851554, -0.006872683, 0.0843576, 0.0073220283, -0.07796905, -0.063768, 0.05963161, -0.0022968545, 0.017076425, 0.01578006, -0.03599755, -0.043162942, -0.014996988, -0.012317128, 0.0010359136, -0.0032637406, 0.015782619, -0.13599883, 0.008110919, 0.0032496245, -0.010512523, -0.021799825, -0.005399938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.29429007, 0.85358447, 0.4760174, 0.5470704, 0.6127836, 0.0, 0.0, 0.8880037, 0.0, 0.0, 0.0, 1.2341257, 1.3112459, 1.8464861, 1.9752972, 2.7704346, 0.78439915, 1.1580931, 2.0385716, 0.0, 0.0, 0.0, 3.8645725, 0.98917186, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1968551, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.6923077, 1.0, 1.0, 1.0, 0.006292303, -0.0015757512, 1.0, 0.0067034187, -0.012876718, -0.0017224916, 1.0, 1.0, 1.0, 1.0, -0.42307693, 1.0, 1.0, 1.0, -0.0022968545, 0.017076425, 0.01578006, 1.0, -0.1923077, -0.014996988, -0.012317128, 0.0010359136, -0.0032637406, 0.015782619, 0.0, 0.008110919, 0.0032496245, -0.010512523, -0.021799825, -0.005399938], "split_indices": [1, 1, 115, 44, 97, 0, 0, 69, 0, 0, 0, 105, 97, 97, 15, 1, 61, 106, 124, 0, 0, 0, 59, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1752.0, 309.0, 1554.0, 198.0, 165.0, 144.0, 1439.0, 115.0, 92.0, 106.0, 701.0, 738.0, 488.0, 213.0, 425.0, 313.0, 263.0, 225.0, 95.0, 118.0, 95.0, 330.0, 211.0, 102.0, 146.0, 117.0, 116.0, 109.0, 178.0, 152.0, 95.0, 116.0, 89.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017567616, 0.0055003897, -0.038212284, 0.03725819, -0.025095979, 0.0064801457, -0.014002821, 0.018555995, 0.01659013, -0.05986195, -0.0005632571, 0.041580833, -0.008488392, -0.08771185, -0.00083286606, -0.033733733, 0.007261909, 0.07835224, -0.045314714, -0.014655376, -0.0032273983, 0.0030798104, -0.008076008, -0.0013163412, 0.11717708, -0.0017829482, -0.0071893805, -0.0037174716, 0.0045850277, 0.06423229, 0.020338802, -0.00017498524, 0.013021446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.5450004, 1.6693347, 3.5870461, 2.0281801, 0.74629074, 0.0, 0.0, 1.7529216, 0.0, 0.5195409, 1.245304, 1.9235537, 0.0, 0.76658607, 0.0, 0.6111162, 0.0, 1.5029528, 0.13076505, 0.0, 0.0, 0.34089762, 0.0, 0.0, 1.3556318, 0.0, 0.0, 0.0, 0.0, 0.8010712, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.03846154, 0.84615386, -0.115384616, 0.0064801457, -0.014002821, 1.0, 0.01659013, 1.0, 1.0, 0.1923077, -0.008488392, 1.0, -0.00083286606, 1.0, 0.007261909, 1.0, 1.0, -0.014655376, -0.0032273983, 1.0, -0.008076008, -0.0013163412, 1.0, -0.0017829482, -0.0071893805, -0.0037174716, 0.0045850277, 1.0, 0.020338802, -0.00017498524, 0.013021446], "split_indices": [64, 108, 1, 1, 1, 0, 0, 119, 0, 111, 71, 1, 0, 50, 0, 50, 0, 2, 81, 0, 0, 80, 0, 0, 109, 0, 0, 0, 0, 71, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1718.0, 342.0, 843.0, 875.0, 170.0, 172.0, 736.0, 107.0, 362.0, 513.0, 602.0, 134.0, 235.0, 127.0, 353.0, 160.0, 423.0, 179.0, 114.0, 121.0, 198.0, 155.0, 126.0, 297.0, 88.0, 91.0, 102.0, 96.0, 184.0, 113.0, 92.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002630098, 0.0009401052, -0.007081061, -0.013468569, 0.017779352, -0.0028928379, -0.007664463, 0.04894327, -0.004793744, 0.009674064, -0.014535129, 0.08206174, -0.002222036, 0.01101419, -0.03414486, -0.029985782, 0.0068149976, 0.01740025, 0.0022707556, 0.011556313, -0.010812365, -0.07592533, 0.0002697972, -0.0030962036, 0.006315981, -0.0008388849, -0.015190384, 0.02936891, -0.004816612, -0.0032844134, 0.010552628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, 19, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.50387603, 0.47725567, 0.0, 0.7082208, 0.6380439, 1.0532452, 0.0, 0.89795256, 1.7744552, 0.0, 1.0386391, 1.4188373, 0.0, 0.0, 1.4166048, 0.95210063, 0.0, 0.0, 0.0, 0.5682708, 0.0, 1.3957189, 0.5820996, 0.0, 0.0, 0.0, 0.0, 1.2223995, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 14, 14, 15, 15, 19, 19, 21, 21, 22, 22, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, 20, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [3.6538463, 0.03846154, -0.007081061, 3.0, 1.0, -0.5769231, -0.007664463, 1.0, 0.0, 0.009674064, 1.0, 1.0, -0.002222036, 0.01101419, 1.1923077, 1.0, 0.0068149976, 0.01740025, 0.0022707556, 1.0, -0.010812365, -0.30769232, 1.0, -0.0030962036, 0.006315981, -0.0008388849, -0.015190384, 1.0, -0.004816612, -0.0032844134, 0.010552628], "split_indices": [1, 1, 0, 0, 124, 1, 0, 23, 0, 0, 0, 115, 0, 0, 1, 53, 0, 0, 0, 13, 0, 1, 109, 0, 0, 0, 0, 124, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1967.0, 103.0, 1060.0, 907.0, 908.0, 152.0, 381.0, 526.0, 95.0, 813.0, 260.0, 121.0, 107.0, 419.0, 685.0, 128.0, 102.0, 158.0, 259.0, 160.0, 272.0, 413.0, 142.0, 117.0, 144.0, 128.0, 258.0, 155.0, 142.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-1.7881394E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}