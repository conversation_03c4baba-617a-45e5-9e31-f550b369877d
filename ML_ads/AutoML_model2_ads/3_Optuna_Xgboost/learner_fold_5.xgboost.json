{"learner": {"attributes": {"best_iteration": "13", "best_score": "1.190508"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "64"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.0036250432, 0.1589701, -0.27053827, 0.11059196, 0.05122866, -0.24428815, -0.042969428, -0.095097266, 0.1874758, -0.14794022, -0.29666075, -0.1375847, 0.0011121276, 0.30957726, 0.08449537, -0.025363553, -0.004488726, -0.3499123, -0.021647742, -0.0059429724, -0.020152966, 0.41851577, 0.004172837, -0.006618598, 0.17181602, -0.039885424, -0.02853782, 0.05926559, 0.02308764, 0.0013709068, 0.028990824], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [89.87891, 21.998428, 3.2754364, 17.90165, 0.0, 3.3959541, 0.0, 1.3899899, 10.361023, 2.5814533, 1.8616676, 1.0994749, 0.0, 11.000507, 5.881445, 0.0, 0.0, 0.8275032, 0.0, 0.0, 0.0, 8.757046, 0.0, 0.0, 5.283949, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 21, 21, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.05122866, 1.0, -0.042969428, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0011121276, 1.0, 1.0, -0.025363553, -0.004488726, 1.0, -0.021647742, -0.0059429724, -0.020152966, 1.0, 0.004172837, -0.006618598, 1.0, -0.039885424, -0.02853782, 0.05926559, 0.02308764, 0.0013709068, 0.028990824], "split_indices": [137, 125, 40, 17, 0, 97, 0, 0, 53, 13, 115, 53, 0, 106, 81, 0, 0, 23, 0, 0, 0, 126, 0, 0, 15, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1287.0, 784.0, 1132.0, 155.0, 673.0, 111.0, 308.0, 824.0, 237.0, 436.0, 220.0, 88.0, 377.0, 447.0, 117.0, 120.0, 262.0, 174.0, 99.0, 121.0, 268.0, 109.0, 164.0, 283.0, 149.0, 113.0, 139.0, 129.0, 121.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.006255586, -0.2204025, 0.15234026, -0.25168377, -0.0024824073, 0.10923707, 0.046342056, -0.28589034, -0.0066081355, -0.08632392, 0.18318664, -0.23888347, -0.040582906, -7.892792e-05, -0.014875434, 0.10726687, 0.3807154, -0.19088915, -0.033120584, 0.1596178, -0.004798069, 0.025467053, 0.056765832, -0.02815783, -0.0075669284, 0.04814467, 0.03720193, -0.01101789, 0.14064834, 0.0033011425, 0.024589328], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1], "loss_changes": [68.01055, 4.924942, 16.747284, 4.406082, 0.0, 15.864429, 0.0, 3.3038368, 0.0, 1.6073289, 11.937084, 1.865427, 0.0, 0.0, 0.0, 4.6732283, 5.2074623, 2.8944244, 0.0, 10.181134, 0.0, 0.0, 0.0, 0.0, 0.0, 4.1300344, 0.0, 0.0, 2.0164258, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, -0.0024824073, 1.0, 0.046342056, 1.0, -0.0066081355, 1.0, 1.0, 0.6923077, -0.040582906, -7.892792e-05, -0.014875434, 0.46153846, 1.0, 1.0, -0.033120584, -0.115384616, -0.004798069, 0.025467053, 0.056765832, -0.02815783, -0.0075669284, 1.0, 0.03720193, -0.01101789, 1.0, 0.0033011425, 0.024589328], "split_indices": [2, 71, 125, 1, 0, 17, 0, 116, 0, 13, 113, 1, 0, 0, 0, 1, 61, 12, 0, 1, 0, 0, 0, 0, 0, 5, 0, 0, 126, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 805.0, 1249.0, 694.0, 111.0, 1097.0, 152.0, 586.0, 108.0, 301.0, 796.0, 421.0, 165.0, 127.0, 174.0, 575.0, 221.0, 277.0, 144.0, 430.0, 145.0, 132.0, 89.0, 155.0, 122.0, 282.0, 148.0, 104.0, 178.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00026632525, 0.13518678, -0.21857429, -0.022183672, 0.19872035, -0.26267174, -0.10192943, -0.019301154, 0.03620839, 0.12598293, 0.40184188, -0.30838037, -0.015220915, -0.021939259, 0.0018827039, -0.0063224486, 0.012535512, 0.17842853, -0.007920566, 0.024169756, 0.06479584, -0.26167932, -0.36585858, 0.23521945, 0.006127694, -0.033346098, -0.019364279, -0.035322938, -0.03782132, 0.00959841, 0.2821469, 0.02132783, 0.037357587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, -1, -1, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [61.23708, 12.827829, 4.068695, 3.6807723, 13.503925, 2.898182, 3.0780232, 0.0, 2.4376326, 7.2423124, 9.49881, 1.0898209, 0.0, 0.0, 0.0, 0.0, 0.0, 3.5660858, 0.0, 0.0, 0.0, 1.0939646, 0.028392792, 2.358759, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.700077, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 17, 17, 21, 21, 22, 22, 23, 23, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, -1, -1, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.019301154, 1.0, 1.0, 1.0, 1.0, -0.015220915, -0.021939259, 0.0018827039, -0.0063224486, 0.012535512, 1.0, -0.007920566, 0.024169756, 0.06479584, 1.0, 1.0, 1.0, 0.006127694, -0.033346098, -0.019364279, -0.035322938, -0.03782132, 0.00959841, 1.0, 0.02132783, 0.037357587], "split_indices": [137, 17, 93, 71, 113, 50, 13, 0, 109, 7, 61, 23, 0, 0, 0, 0, 0, 124, 0, 0, 0, 115, 12, 81, 0, 0, 0, 0, 0, 0, 115, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1283.0, 791.0, 369.0, 914.0, 574.0, 217.0, 94.0, 275.0, 673.0, 241.0, 406.0, 168.0, 110.0, 107.0, 130.0, 145.0, 536.0, 137.0, 146.0, 95.0, 224.0, 182.0, 361.0, 175.0, 109.0, 115.0, 90.0, 92.0, 91.0, 270.0, 154.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018106459, 0.11650364, -0.19397716, 0.08086878, 0.037589766, -0.23183931, -0.094238445, 0.025601318, 0.27745017, -0.29110983, -0.15401661, -0.0011321163, -0.019788502, -0.09750445, 0.07402031, 0.0116306115, 0.038058233, -0.021896053, -0.03544502, -0.022701485, -0.00680983, -0.00069099676, -0.01726643, 0.16994064, -0.03849521, 0.2548765, -0.004821039, -0.017293869, 0.0047823605, 0.014227005, 0.04314908], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [46.836254, 11.785421, 2.9644108, 12.179165, 0.0, 2.6245632, 1.8563232, 5.215575, 4.0883064, 1.4760971, 1.5428824, 0.0, 0.0, 1.6818392, 6.777706, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.2812786, 3.353846, 4.8526497, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.0, 0.037589766, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0011321163, -0.019788502, 1.0, 1.0, 0.0116306115, 0.038058233, -0.021896053, -0.03544502, -0.022701485, -0.00680983, -0.00069099676, -0.01726643, 0.115384616, 1.0, 1.0, -0.004821039, -0.017293869, 0.0047823605, 0.014227005, 0.04314908], "split_indices": [137, 125, 93, 0, 0, 39, 23, 17, 15, 122, 115, 0, 0, 13, 122, 0, 0, 0, 0, 0, 0, 0, 0, 1, 39, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1275.0, 785.0, 1121.0, 154.0, 569.0, 216.0, 875.0, 246.0, 323.0, 246.0, 120.0, 96.0, 247.0, 628.0, 96.0, 150.0, 151.0, 172.0, 133.0, 113.0, 112.0, 135.0, 339.0, 289.0, 244.0, 95.0, 113.0, 176.0, 149.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0034260307, 0.10811213, -0.1673706, 0.07525889, 0.034566633, -0.20651503, -0.06255863, 0.02890009, 0.24233976, -0.18191725, -0.032009885, 0.006322421, -0.015040698, -0.028216625, 0.1344739, 0.007901199, 0.0344014, -0.0056712828, -0.22964913, -0.012583989, 0.011600229, 0.2513267, -0.010381416, -0.030916963, -0.16416165, 0.06085356, -0.012826062, 0.012520738, 0.040134232, -0.02239582, -0.010107956, -0.012590596, 0.021531347, 0.011988161, -0.013749299], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, -1, -1, 23, -1, 25, 27, -1, -1, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [37.02955, 10.020882, 3.2289066, 8.737112, 0.0, 1.6009102, 2.364661, 5.3245163, 4.0685215, 2.8148088, 0.0, 0.0, 0.0, 2.2272809, 8.631836, 0.0, 0.0, 0.0, 1.7757893, 0.0, 2.8036652, 3.9353304, 0.0, 0.0, 0.70537996, 3.414598, 0.0, 0.0, 0.0, 0.0, 0.0, 3.3754034, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 18, 18, 20, 20, 21, 21, 24, 24, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, -1, -1, 24, -1, 26, 28, -1, -1, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.0, 0.034566633, 1.0, 1.0, 1.0, 1.0, 0.0, -0.032009885, 0.006322421, -0.015040698, 1.0, 1.0, 0.007901199, 0.0344014, -0.0056712828, 1.0, -0.012583989, 0.1923077, 1.0, -0.010381416, -0.030916963, 1.3076923, -0.1923077, -0.012826062, 0.012520738, 0.040134232, -0.02239582, -0.010107956, 1.0, 0.021531347, 0.011988161, -0.013749299], "split_indices": [137, 125, 93, 0, 0, 0, 80, 50, 15, 1, 0, 0, 0, 17, 97, 0, 0, 0, 111, 0, 1, 109, 0, 0, 1, 1, 0, 0, 0, 0, 0, 122, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1284.0, 787.0, 1128.0, 156.0, 573.0, 214.0, 883.0, 245.0, 471.0, 102.0, 88.0, 126.0, 573.0, 310.0, 94.0, 151.0, 130.0, 341.0, 166.0, 407.0, 208.0, 102.0, 154.0, 187.0, 301.0, 106.0, 113.0, 95.0, 96.0, 91.0, 204.0, 97.0, 99.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002380572, -0.12935139, 0.1271422, -0.09353693, -0.26819605, 0.07759918, 0.30523518, -0.12123237, 0.007749452, -0.02362857, -0.03102756, 0.033749532, 0.03590342, 0.016784366, 0.040058896, -0.14740431, 0.0028072305, -0.034108024, 0.11797907, -0.2259732, -0.09808525, -0.1103033, 0.018177858, 0.022747394, -0.0018889466, -0.016680533, -0.030917797, 0.0065050893, -0.15405166, -0.023237577, -0.0010495591, -0.024935426, -0.010013761], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, -1, -1, -1, 19, -1, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [34.07713, 5.1168537, 9.20266, 3.8746953, 0.2833233, 10.070114, 2.9738827, 2.750946, 0.0, 0.0, 0.0, 4.0352216, 0.0, 0.0, 0.0, 2.3210907, 0.0, 6.4317694, 4.720717, 1.1372223, 3.3598945, 3.5211103, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4078522, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, -1, -1, -1, 20, -1, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.007749452, -0.02362857, -0.03102756, 1.0, 0.03590342, 0.016784366, 0.040058896, 1.0, 0.0028072305, 1.0, 1.0, 1.0, 0.0, 1.0, 0.018177858, 0.022747394, -0.0018889466, -0.016680533, -0.030917797, 0.0065050893, 1.0, -0.023237577, -0.0010495591, -0.024935426, -0.010013761], "split_indices": [71, 116, 0, 121, 109, 125, 13, 42, 0, 0, 0, 39, 0, 0, 0, 39, 0, 113, 13, 12, 0, 13, 0, 0, 0, 0, 0, 0, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1029.0, 1043.0, 818.0, 211.0, 816.0, 227.0, 704.0, 114.0, 120.0, 91.0, 706.0, 110.0, 93.0, 134.0, 599.0, 105.0, 391.0, 315.0, 231.0, 368.0, 289.0, 102.0, 175.0, 140.0, 135.0, 96.0, 94.0, 274.0, 130.0, 159.0, 99.0, 175.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.007841342, 0.09513701, -0.13434815, 0.07099885, 0.0406047, -0.11107267, -0.027400104, -0.08014954, 0.13231884, -0.13479511, 0.004038603, -0.14838822, 0.0097271, 0.24313144, 0.03414927, -0.091753624, -0.023799399, -0.0060404656, -0.020337794, 0.33102167, 0.0075105997, -0.021955187, 0.10170709, -0.14197563, 0.0024080963, 0.024349744, 0.04158936, -0.00598756, 0.20791169, -0.021915473, -0.004534979, 0.0062053404, 0.031260025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [25.582214, 9.583625, 2.5483818, 10.983074, 0.0, 2.4144773, 0.0, 4.1405754, 9.17051, 2.5807047, 0.0, 1.1950326, 0.0, 5.8480453, 7.661355, 2.3851523, 0.0, 0.0, 0.0, 1.9313698, 0.0, 0.0, 6.0577717, 2.1328425, 0.0, 0.0, 0.0, 0.0, 3.2524443, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 22, 22, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.0406047, 2.7307692, -0.027400104, 1.0, 1.0, 0.7692308, 0.004038603, -0.23076923, 0.0097271, 1.0, -1.0, 1.0, -0.023799399, -0.0060404656, -0.020337794, 1.0, 0.0075105997, -0.021955187, 1.0, 1.0, 0.0024080963, 0.024349744, 0.04158936, -0.00598756, 1.0, -0.021915473, -0.004534979, 0.0062053404, 0.031260025], "split_indices": [137, 102, 40, 17, 0, 1, 0, 39, 53, 1, 0, 1, 0, 97, 0, 93, 0, 0, 0, 113, 0, 0, 81, 12, 0, 0, 0, 0, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1277.0, 784.0, 1185.0, 92.0, 672.0, 112.0, 342.0, 843.0, 581.0, 91.0, 247.0, 95.0, 396.0, 447.0, 410.0, 171.0, 95.0, 152.0, 260.0, 136.0, 94.0, 353.0, 286.0, 124.0, 128.0, 132.0, 140.0, 213.0, 159.0, 127.0, 89.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006114956, -0.099778146, 0.09740982, -0.068614125, -0.22102103, 0.14154786, -0.06227176, -0.09900272, 0.012267116, -0.018596394, -0.026776377, 0.08121189, 0.32555753, 0.0055793575, -0.016933104, -0.06809231, -0.03102238, 0.20069611, 0.005898681, 0.019275976, 0.045190882, 0.006711525, -0.11932781, 0.0015077278, 0.03205749, 0.017392557, -0.14039336, 0.014240967, -0.009649549, -0.07957048, -0.022468474, -0.017123083, -0.010798253, 0.0059052873, -0.1496698, -0.015705016, -0.014228942], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [20.082441, 3.8804321, 7.3229046, 4.749132, 0.34411907, 9.037355, 2.8439975, 4.6028953, 0.0, 0.0, 0.0, 5.516227, 3.37261, 0.0, 0.0, 2.3570569, 0.0, 5.2736664, 9.2424555, 0.0, 0.0, 3.5012503, 1.5288782, 0.0, 0.0, 0.0, 0.20089221, 0.0, 0.0, 2.5751126, 0.0, 0.0, 0.0, 0.0, 0.009585619, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 26, 26, 29, 29, 34, 34], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, 0.46153846, 1.0, 1.0, 1.0, 1.0, 1.0, 0.012267116, -0.018596394, -0.026776377, 1.0, -0.42307693, 0.0055793575, -0.016933104, 1.0, -0.03102238, -0.3846154, 1.0, 0.019275976, 0.045190882, 0.115384616, 1.0, 0.0015077278, 0.03205749, 0.017392557, 1.0, 0.014240967, -0.009649549, 1.0, -0.022468474, -0.017123083, -0.010798253, 0.0059052873, 1.0, -0.015705016, -0.014228942], "split_indices": [71, 116, 1, 121, 109, 42, 124, 40, 0, 0, 0, 69, 1, 0, 0, 122, 0, 1, 121, 0, 0, 1, 127, 0, 0, 0, 61, 0, 0, 97, 0, 0, 0, 0, 108, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1027.0, 1039.0, 817.0, 210.0, 814.0, 225.0, 705.0, 112.0, 120.0, 90.0, 613.0, 201.0, 107.0, 118.0, 615.0, 90.0, 237.0, 376.0, 98.0, 103.0, 250.0, 365.0, 93.0, 144.0, 175.0, 201.0, 108.0, 142.0, 265.0, 100.0, 103.0, 98.0, 89.0, 176.0, 88.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.009216863, -0.09995147, 0.08125449, -0.15189104, -0.029351683, 0.035832673, 0.22990769, -0.113844186, -0.2422523, 0.010796893, -0.06957282, -0.10635857, 0.12583828, 0.010110485, 0.031314757, -0.15501909, 0.00014455155, -0.027455634, -0.020994825, -0.013630769, -0.0004398988, -0.020915862, -0.027808163, 0.039951857, 0.23925763, -0.0020118377, -0.21987517, 0.01195097, -0.013751926, 0.09986339, -0.0077938545, 0.04003815, 0.009971282, -0.024626078, -0.018528068, 0.0138056865, 0.0063701444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, 27, -1, 29, 31, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [16.951344, 3.7805967, 6.981668, 2.0421476, 2.4136345, 10.136023, 2.5946102, 1.9842644, 0.18366432, 0.0, 1.4700856, 4.5044518, 4.724473, 0.0, 0.0, 2.6947327, 0.0, 0.0, 0.0, 0.0, 0.0, 3.35669, 0.0, 1.9493864, 4.699155, 0.0, 0.18985939, 0.0, 0.0, 0.25274992, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 21, 21, 23, 23, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, 28, -1, 30, 32, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.1538461, 1.0, 0.010796893, 1.0, 0.1923077, -0.07692308, 0.010110485, 0.031314757, -0.26923078, 0.00014455155, -0.027455634, -0.020994825, -0.013630769, -0.0004398988, 1.0, -0.027808163, -0.30769232, 0.34615386, -0.0020118377, 1.0, 0.01195097, -0.013751926, 1.0, -0.0077938545, 0.04003815, 0.009971282, -0.024626078, -0.018528068, 0.0138056865, 0.0063701444], "split_indices": [71, 83, 42, 116, 53, 39, 93, 1, 12, 0, 39, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 121, 0, 1, 1, 0, 59, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1031.0, 1034.0, 594.0, 437.0, 792.0, 242.0, 418.0, 176.0, 99.0, 338.0, 307.0, 485.0, 95.0, 147.0, 308.0, 110.0, 88.0, 88.0, 167.0, 171.0, 205.0, 102.0, 276.0, 209.0, 100.0, 208.0, 93.0, 112.0, 183.0, 93.0, 97.0, 112.0, 118.0, 90.0, 89.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00057470606, -0.0819369, 0.08260891, -0.11971536, -0.0037939542, 0.029352738, 0.061829865, -0.1476372, 0.0035435238, 0.05534493, -0.013946555, 0.00977075, 0.2103577, -0.18198308, -0.00090794294, 0.010926839, -0.0024966565, -0.054464836, 0.07956704, 0.029868556, 0.0058237477, -0.01060846, -0.21631254, 0.026010077, -0.029239072, -0.072109275, 0.028074885, -0.19822377, -0.026427824, -0.0039387033, 0.01166482, 0.0042752814, -0.017884983, -0.016365232, -0.022453932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, 25, -1, -1, -1, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.997826, 3.0436358, 4.5448437, 3.0108023, 2.6958854, 0.0, 7.2992215, 2.8029852, 0.0, 1.0133778, 0.0, 3.1339004, 3.2919302, 1.2298212, 0.0, 0.0, 0.0, 6.9695315, 10.2223625, 0.0, 0.0, 0.0, 0.28198147, 1.6122725, 0.0, 2.3417451, 0.0, 0.21470356, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 22, 22, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, 26, -1, -1, -1, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, 1.0, 0.029352738, 1.0, 1.0, 0.0035435238, 1.0, -0.013946555, 1.0, 1.0, 0.0, -0.00090794294, 0.010926839, -0.0024966565, 0.5769231, 0.03846154, 0.029868556, 0.0058237477, -0.01060846, 1.0, 1.0, -0.029239072, 1.0, 0.028074885, 1.0, -0.026427824, -0.0039387033, 0.01166482, 0.0042752814, -0.017884983, -0.016365232, -0.022453932], "split_indices": [71, 2, 104, 1, 7, 0, 113, 62, 0, 12, 0, 39, 126, 1, 0, 0, 0, 1, 1, 0, 0, 0, 93, 42, 0, 13, 0, 122, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1031.0, 1037.0, 695.0, 336.0, 93.0, 944.0, 589.0, 106.0, 234.0, 102.0, 699.0, 245.0, 472.0, 117.0, 140.0, 94.0, 364.0, 335.0, 155.0, 90.0, 147.0, 325.0, 272.0, 92.0, 191.0, 144.0, 236.0, 89.0, 158.0, 114.0, 92.0, 99.0, 102.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004786659, 0.055494603, -0.10264785, 0.02983001, 0.024110755, -0.027449492, -0.12796974, 0.006650125, 0.027453309, -0.01440392, 0.010154336, -0.14799196, -0.001718006, -0.048726846, 0.067236386, -0.18708143, -0.09165712, -0.1065694, 0.0067850496, 0.024703471, 0.0034851732, -0.21911886, -0.01132873, 0.0002451849, -0.019546807, -0.0012737489, -0.019088212, 0.008913766, -0.007990188, -0.064390585, 0.018564155, -0.01816193, -0.024789006, -0.020935649, 0.006893571], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [12.164143, 6.078455, 1.4966726, 6.358524, 0.0, 2.9777694, 1.3043337, 3.4356062, 0.0, 0.0, 0.0, 1.0966454, 0.0, 1.7178584, 5.605094, 0.69506645, 1.9929874, 2.07274, 1.948918, 0.0, 4.4634056, 0.22117424, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.0832024, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0, 5.0, 0.024110755, 1.0, 1.0, 1.0, 0.027453309, -0.01440392, 0.010154336, 1.0, -0.001718006, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.024703471, 1.0, 1.0, -0.01132873, 0.0002451849, -0.019546807, -0.0012737489, -0.019088212, 0.008913766, -0.007990188, 1.0, 0.018564155, -0.01816193, -0.024789006, -0.020935649, 0.006893571], "split_indices": [137, 125, 0, 0, 0, 13, 42, 124, 0, 0, 0, 115, 0, 39, 89, 61, 23, 93, 13, 0, 61, 39, 0, 0, 0, 0, 0, 0, 0, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1276.0, 786.0, 1121.0, 155.0, 198.0, 588.0, 1024.0, 97.0, 104.0, 94.0, 498.0, 90.0, 535.0, 489.0, 294.0, 204.0, 262.0, 273.0, 128.0, 361.0, 205.0, 89.0, 107.0, 97.0, 124.0, 138.0, 140.0, 133.0, 263.0, 98.0, 89.0, 116.0, 126.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.004755096, 0.061939437, -0.089180104, 0.037619658, 0.023944221, -0.12180922, -0.037472155, -0.058562152, 0.07376142, -0.16155472, -0.07378339, 0.00495312, -0.015347664, 0.0020811544, -0.011843262, 0.114755206, -0.03343914, -0.009201132, -0.027172247, -0.012561847, -0.0015850038, 0.03845532, 0.03494431, -0.016618628, 0.01634934, -0.027493328, 0.023989847, -0.013636077, 0.03220816, 0.018858677, -0.007861129], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, -1, -1, -1, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1], "loss_changes": [11.044056, 5.516906, 1.3126297, 3.9072266, 0.0, 0.9105015, 3.0379272, 1.4589072, 3.590353, 1.9996338, 0.64864326, 0.0, 0.0, 0.0, 0.0, 10.582836, 5.9081445, 0.0, 0.0, 0.0, 0.0, 5.925068, 0.0, 0.0, 0.0, 2.1838486, 0.0, 0.0, 3.7605648, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 21, 21, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, -1, -1, -1, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.023944221, 1.0, 1.0, 1.0, 0.15384616, 1.0, 1.0, 0.00495312, -0.015347664, 0.0020811544, -0.011843262, -0.115384616, 0.8076923, -0.009201132, -0.027172247, -0.012561847, -0.0015850038, 1.0, 0.03494431, -0.016618628, 0.01634934, 1.0, 0.023989847, -0.013636077, 1.0, 0.018858677, -0.007861129], "split_indices": [137, 125, 115, 17, 0, 23, 23, 13, 1, 111, 13, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 61, 0, 0, 0, 5, 0, 0, 109, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1278.0, 778.0, 1124.0, 154.0, 477.0, 301.0, 307.0, 817.0, 261.0, 216.0, 172.0, 129.0, 132.0, 175.0, 591.0, 226.0, 160.0, 101.0, 114.0, 102.0, 446.0, 145.0, 135.0, 91.0, 336.0, 110.0, 119.0, 217.0, 90.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0034036713, -0.0633338, 0.0711197, -0.096470885, -0.0011902812, 0.10456952, -0.045514494, -0.11600107, 0.0011132864, 0.01028734, -0.05905329, 0.05588962, 0.2697046, -0.022759262, 0.0167777, -0.097351946, -0.020983558, -0.012294878, -0.0002430429, 0.021991672, 0.022806432, 0.0016497181, 0.053144705, -0.03719255, -0.13809144, 0.060410637, -0.047880165, -0.011161493, 0.0029203852, -0.0076925503, -0.021865144, 0.015198727, -0.002952089, -0.008007369, -0.0015686604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [9.314065, 2.137507, 3.9911246, 1.4227295, 2.1737387, 6.390812, 8.854543, 1.0027108, 0.0, 0.0, 0.8393631, 3.5835297, 11.995798, 0.0, 0.0, 1.1715126, 0.0, 0.0, 0.0, 1.377099, 0.0, 0.0, 0.0, 0.95368624, 1.4043446, 2.7259924, 0.18862891, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.46153846, 1.0, 1.0, 2.0, 1.0, 1.0, 0.0011132864, 0.01028734, 0.15384616, 1.0, -0.30769232, -0.022759262, 0.0167777, 0.23076923, -0.020983558, -0.012294878, -0.0002430429, 1.0, 0.022806432, 0.0016497181, 0.053144705, 1.0, 1.0, 1.0, 1.0, -0.011161493, 0.0029203852, -0.0076925503, -0.021865144, 0.015198727, -0.002952089, -0.008007369, -0.0015686604], "split_indices": [71, 74, 1, 42, 122, 0, 39, 7, 0, 0, 1, 125, 1, 0, 0, 1, 0, 0, 0, 97, 0, 0, 0, 69, 109, 53, 105, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1038.0, 1023.0, 677.0, 361.0, 795.0, 228.0, 573.0, 104.0, 129.0, 232.0, 614.0, 181.0, 123.0, 105.0, 478.0, 95.0, 109.0, 123.0, 513.0, 101.0, 92.0, 89.0, 193.0, 285.0, 331.0, 182.0, 91.0, 102.0, 162.0, 123.0, 164.0, 167.0, 91.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0039049766, 0.048443433, -0.06844175, 0.031554222, 0.025442213, -0.09435792, -0.00024756262, 0.01202146, 0.024080614, -0.12928264, -0.0011282563, -0.0076634646, 0.007974266, -0.045076143, 0.11497466, -0.0068966122, -0.16116093, -0.01824304, -0.018064162, 0.22069228, -0.01502804, -0.013587609, -0.021144325, -0.049465857, 0.01200711, 0.036022656, 3.0631138e-05, -0.09042296, 0.0068705417, -0.020001864, 0.0009067517, -0.010081628, 0.01114753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, 19, -1, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [6.6635337, 4.4528866, 1.3926578, 4.8352375, 0.0, 1.6566887, 1.3259177, 6.3604083, 0.0, 0.7729602, 0.0, 0.0, 0.0, 2.5317986, 10.824265, 0.0, 0.33437204, 2.5090816, 0.0, 8.487383, 0.0, 0.0, 0.0, 2.2941377, 0.0, 0.0, 0.0, 3.523289, 0.0, 0.0, 2.1594949, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, 20, -1, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 5.0, 0.025442213, 1.0, 1.0, 1.0, 0.024080614, 0.0, -0.0011282563, -0.0076634646, 0.007974266, 1.0, 1.0, -0.0068966122, 1.2692307, 1.0, -0.018064162, 0.03846154, -0.01502804, -0.013587609, -0.021144325, 1.0, 0.01200711, 0.036022656, 3.0631138e-05, 1.0, 0.0068705417, -0.020001864, -0.15384616, -0.010081628, 0.01114753], "split_indices": [137, 102, 93, 0, 0, 50, 13, 50, 0, 1, 0, 0, 0, 119, 106, 0, 1, 61, 0, 1, 0, 0, 0, 74, 0, 0, 0, 111, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1280.0, 788.0, 1183.0, 97.0, 571.0, 217.0, 1082.0, 101.0, 402.0, 169.0, 111.0, 106.0, 696.0, 386.0, 139.0, 263.0, 581.0, 115.0, 276.0, 110.0, 175.0, 88.0, 474.0, 107.0, 169.0, 107.0, 352.0, 122.0, 160.0, 192.0, 100.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004953621, -0.050929796, 0.060891088, -0.030291948, -0.13057443, 0.030399835, 0.17052406, -0.006514606, -0.012642242, -0.0097858915, -0.017360242, 0.0029638216, 0.020113999, 0.03961317, -4.4416316e-05, -0.04149278, 0.066726066, -0.031275984, 0.011603975, -0.07005138, 0.003103697, -0.0020409308, 0.017292233, -0.124473445, 0.01677482, -0.1356061, 0.0048407116, 0.0005112262, -0.025988325, -0.052499384, 0.010033685, -0.023301782, -0.005223567, -0.020407062, 0.012461752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, -1, -1, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [6.467646, 1.7012227, 3.4565032, 1.8788674, 0.29983568, 3.789703, 8.678639, 1.6882426, 0.0, 0.0, 0.0, 2.6985738, 0.0, 0.0, 0.0, 0.9238214, 1.9709857, 2.3958445, 0.0, 2.4849646, 0.0, 0.0, 0.0, 3.1935868, 2.0434086, 1.6729782, 0.0, 0.0, 0.0, 5.1812444, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, -1, -1, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.012642242, -0.0097858915, -0.017360242, 1.0, 0.020113999, 0.03961317, -4.4416316e-05, 1.0, 1.0, 1.0, 0.011603975, 1.0, 0.003103697, -0.0020409308, 0.017292233, -0.23076923, 1.0, 1.0, 0.0048407116, 0.0005112262, -0.025988325, 1.0, 0.010033685, -0.023301782, -0.005223567, -0.020407062, 0.012461752], "split_indices": [71, 116, 0, 7, 109, 125, 69, 2, 0, 0, 0, 116, 0, 0, 0, 93, 69, 93, 0, 50, 0, 0, 0, 1, 12, 12, 0, 0, 0, 109, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1035.0, 1034.0, 822.0, 213.0, 809.0, 225.0, 659.0, 163.0, 121.0, 92.0, 697.0, 112.0, 97.0, 128.0, 446.0, 213.0, 535.0, 162.0, 320.0, 126.0, 117.0, 96.0, 182.0, 353.0, 206.0, 114.0, 93.0, 89.0, 193.0, 160.0, 95.0, 111.0, 104.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0026111256, -0.04613937, 0.05098662, -0.023078673, -0.13533638, 0.023357576, 0.14308342, -0.0012985311, -0.017812738, -0.011715944, -0.015815424, -0.076594286, 0.08592907, 0.0021402566, 0.023613347, 0.018246217, -0.00866245, -0.15477504, 0.007239166, -0.0035836701, 0.20221199, 0.07573523, -0.024357243, -0.0320058, -0.0021958335, 0.051757693, -0.011367136, 0.031429794, 0.010389099, 0.018341968, -0.0098826885, -0.07755697, 0.004487526, 0.008987847, 0.0017173864, -0.017637303, 0.00095185376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, 21, -1, 23, -1, 25, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [4.8864584, 2.1227672, 2.646328, 2.7691255, 0.08792782, 5.0033097, 2.7173767, 1.1990582, 0.0, 0.0, 0.0, 3.587533, 5.121132, 0.0, 0.0, 1.4328003, 0.0, 4.434372, 0.0, 1.6936882, 2.358364, 4.6806087, 1.2375387, 0.0, 0.0, 0.24389666, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6348479, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, 22, -1, 24, -1, 26, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.017812738, -0.011715944, -0.015815424, 1.0, -0.07692308, 0.0021402566, 0.023613347, 0.1923077, -0.00866245, 1.0, 0.007239166, -0.30769232, 0.34615386, 1.0, 1.0, -0.0320058, -0.0021958335, 1.0, -0.011367136, 0.031429794, 0.010389099, 0.018341968, -0.0098826885, 1.0, 0.004487526, 0.008987847, 0.0017173864, -0.017637303, 0.00095185376], "split_indices": [71, 116, 42, 40, 109, 39, 13, 7, 0, 0, 0, 116, 1, 0, 0, 1, 0, 17, 0, 1, 1, 80, 81, 0, 0, 105, 0, 0, 0, 0, 0, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1032.0, 1040.0, 820.0, 212.0, 800.0, 240.0, 719.0, 101.0, 118.0, 94.0, 308.0, 492.0, 104.0, 136.0, 585.0, 134.0, 202.0, 106.0, 278.0, 214.0, 249.0, 336.0, 90.0, 112.0, 185.0, 93.0, 100.0, 114.0, 154.0, 95.0, 190.0, 146.0, 88.0, 97.0, 89.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.002317412, 0.023599371, -0.08380392, 0.055710856, -0.074234135, -0.11812748, 0.0037091242, -0.029124096, 0.082905024, -0.11901742, 0.0030382525, -0.014819455, -0.008291191, -0.11806186, 0.013689304, 0.13362, -0.009817822, -0.018270364, -0.0023488086, 0.002644349, -0.025964795, 0.22909522, 0.025749728, -0.100304335, 0.009997246, 0.34790537, 0.008223264, -0.021540755, 0.013161595, -0.01654694, -0.003069615, 0.044833735, 0.024747338, 0.01076346, -0.013472301], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, 19, -1, 21, 23, -1, -1, -1, -1, 25, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.764634, 5.174181, 1.6888683, 2.8607001, 1.9068265, 0.33564758, 0.0, 4.4443235, 4.415588, 1.7339115, 0.0, 0.0, 0.0, 4.010151, 0.0, 6.2514544, 3.2982671, 0.0, 0.0, 0.0, 0.0, 5.6185017, 1.426843, 0.82555544, 0.0, 1.7954102, 0.0, 2.8802104, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 21, 21, 22, 22, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, 20, -1, 22, 24, -1, -1, -1, -1, 26, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.3461539, 1.0, 1.0, 1.0, 0.0037091242, -0.07692308, 0.1923077, 1.0, 0.0030382525, -0.014819455, -0.008291191, 1.0, 0.013689304, 1.0, 0.84615386, -0.018270364, -0.0023488086, 0.002644349, -0.025964795, 1.0, 1.0, 1.0, 0.009997246, 1.0, 0.008223264, 1.0, 0.013161595, -0.01654694, -0.003069615, 0.044833735, 0.024747338, 0.01076346, -0.013472301], "split_indices": [80, 23, 1, 5, 39, 97, 0, 1, 1, 116, 0, 0, 0, 69, 0, 121, 1, 0, 0, 0, 0, 122, 111, 71, 0, 39, 0, 39, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1647.0, 407.0, 1240.0, 407.0, 317.0, 90.0, 301.0, 939.0, 285.0, 122.0, 171.0, 146.0, 196.0, 105.0, 607.0, 332.0, 171.0, 114.0, 97.0, 99.0, 322.0, 285.0, 182.0, 150.0, 178.0, 144.0, 197.0, 88.0, 94.0, 88.0, 89.0, 89.0, 92.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0023243062, -0.008205419, 0.01534064, 0.030661969, -0.060815714, -0.034721434, 0.12932612, -0.116317816, -0.008075556, 0.02464424, -0.08739802, 0.2749761, -0.012439866, -0.06828879, -0.026088519, 0.06184533, -0.011590538, 0.10602491, -0.009905436, -0.14197505, 0.0016835108, 0.035676386, 0.020235417, -0.010719029, 0.012968573, -0.0063614417, -0.01684495, 0.016706916, -0.0023975493, 0.03007347, -0.0061959964, -0.025091484, -0.0026286733, 0.0067726555, -0.008886857], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.296248, 3.9608257, 0.0, 7.1864114, 2.4090772, 2.0952117, 9.167809, 2.7843137, 3.1816928, 3.171003, 2.019501, 1.300766, 3.0299537, 1.8670086, 0.0, 2.3117814, 0.0, 6.214577, 0.0, 2.9365125, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1369786, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.01534064, 1.0, 1.0, 1.0, 0.03846154, 1.0, 1.0, -0.03846154, 1.0, 1.0, 0.96153843, 1.0, -0.026088519, 1.0, -0.011590538, 1.0, -0.009905436, 1.0, 0.0016835108, 0.035676386, 0.020235417, -0.010719029, 0.012968573, 1.0, -0.01684495, 0.016706916, -0.0023975493, 0.03007347, -0.0061959964, -0.025091484, -0.0026286733, 0.0067726555, -0.008886857], "split_indices": [0, 106, 0, 50, 39, 69, 1, 50, 15, 1, 116, 39, 1, 124, 0, 3, 0, 93, 0, 111, 0, 0, 0, 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1937.0, 135.0, 1114.0, 823.0, 670.0, 444.0, 401.0, 422.0, 315.0, 355.0, 219.0, 225.0, 301.0, 100.0, 256.0, 166.0, 190.0, 125.0, 233.0, 122.0, 103.0, 116.0, 135.0, 90.0, 186.0, 115.0, 115.0, 141.0, 88.0, 102.0, 120.0, 113.0, 98.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0052297837, -0.016544037, 0.015574872, -0.03197543, 0.014264296, -0.057768326, 0.01585411, -0.046554185, -0.018183213, 0.018620087, -0.024347719, -0.018799512, -0.103843704, -0.062214717, 0.011473386, -0.013160127, -0.002788932, -0.050059665, -0.017906612, 0.009574252, -0.10913272, -0.03356814, 0.045944802, 0.0014332238, -0.010949837, -0.19331948, 0.0017495286, -0.012940821, 0.009596458, -0.0055116857, 0.015043231, -0.023470087, -0.015283766, 0.0049714525, -0.0035740193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [3.7720182, 4.7532835, 0.0, 2.1761808, 0.0, 1.5943935, 4.2322197, 1.671144, 0.0, 0.0, 2.6333008, 1.2786632, 1.3876967, 2.9125383, 0.0, 0.0, 0.9299911, 0.76547456, 0.0, 0.0, 3.2301025, 1.5720217, 2.5343237, 0.0, 0.0, 0.30488396, 0.0, 0.0, 0.47653058, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 20, 20, 21, 21, 22, 22, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.015574872, 1.0, 0.014264296, 1.0, 1.0, 1.0, -0.018183213, 0.018620087, 1.0, -0.46153846, 1.0, -0.30769232, 0.011473386, -0.013160127, 1.0, 0.30769232, -0.017906612, 0.009574252, 0.9230769, 1.0, 1.0, 0.0014332238, -0.010949837, 1.0, 0.0017495286, -0.012940821, 0.1923077, -0.0055116857, 0.015043231, -0.023470087, -0.015283766, 0.0049714525, -0.0035740193], "split_indices": [0, 125, 0, 50, 0, 119, 81, 23, 0, 0, 58, 1, 115, 1, 0, 0, 71, 1, 0, 0, 1, 115, 122, 0, 0, 111, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1935.0, 136.0, 1764.0, 171.0, 1146.0, 618.0, 1051.0, 95.0, 118.0, 500.0, 708.0, 343.0, 393.0, 107.0, 88.0, 620.0, 200.0, 143.0, 90.0, 303.0, 380.0, 240.0, 96.0, 104.0, 182.0, 121.0, 118.0, 262.0, 122.0, 118.0, 90.0, 92.0, 139.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010029582, -0.020664698, 0.08679867, -0.0701174, 0.01445824, -0.016465731, 0.18683606, 0.0070819757, -0.11384744, -0.010080349, 0.021009775, 0.0050154785, -0.008752764, 0.025607664, 0.011157454, -0.14088094, 0.0019818181, -0.07019757, 0.06160943, -0.20019919, -0.063429125, -0.11908097, 0.007362464, 0.12334858, -0.0056911698, -0.013043295, -0.028740695, 0.004935681, -0.019522397, -0.07514714, -0.02363888, -0.002765524, 0.029261902, -0.01384078, 0.0023815061], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, -1, -1, 19, -1, 21, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [3.566603, 2.9319282, 3.9048548, 4.320395, 4.738309, 0.8805578, 1.0005393, 0.0, 1.9331961, 3.7796862, 0.0, 0.0, 0.0, 0.0, 0.0, 2.044466, 0.0, 3.3535578, 2.926958, 1.5332069, 2.8688688, 1.8347459, 0.0, 6.7224083, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6214477, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, -1, -1, 20, -1, 22, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.26923078, 1.0, 1.0, 1.0, 0.0070819757, 1.0, 1.0, 0.021009775, 0.0050154785, -0.008752764, 0.025607664, 0.011157454, 1.0, 0.0019818181, 0.5, 0.46153846, 1.0, 1.0, 1.0, 0.007362464, 1.0, -0.0056911698, -0.013043295, -0.028740695, 0.004935681, -0.019522397, 1.0, -0.02363888, -0.002765524, 0.029261902, -0.01384078, 0.0023815061], "split_indices": [42, 39, 93, 1, 88, 69, 53, 0, 62, 12, 0, 0, 0, 0, 0, 116, 0, 1, 1, 71, 122, 137, 0, 115, 0, 0, 0, 0, 0, 97, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1688.0, 378.0, 701.0, 987.0, 186.0, 192.0, 166.0, 535.0, 877.0, 110.0, 96.0, 90.0, 100.0, 92.0, 445.0, 90.0, 477.0, 400.0, 252.0, 193.0, 356.0, 121.0, 263.0, 137.0, 140.0, 112.0, 104.0, 89.0, 259.0, 97.0, 139.0, 124.0, 158.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00069064397, 0.008574615, -0.01869413, -0.029801672, 0.047185384, -0.043511394, 0.008307927, 0.0829252, -0.039362244, -0.1089467, 0.004089578, 0.042542323, 0.02824235, -0.012080364, 0.0069226264, -0.1594413, -0.0053602313, 0.039908074, -0.012923479, -0.05821731, 0.11207237, -0.019009626, -0.012941194, -0.009467924, 0.011458929, -0.016329883, 0.0054238313, 0.021510912, 0.019339275, -0.012653272, 0.0064884014, 0.010617981, -0.006559264], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [3.568667, 2.9190238, 0.0, 1.5289954, 3.0375195, 2.7441254, 0.0, 5.5991387, 2.5381134, 1.0367932, 2.435494, 4.049365, 0.0, 0.0, 0.0, 0.17858553, 0.0, 1.4823592, 0.0, 2.7888138, 3.2677813, 0.0, 0.0, 2.1063669, 0.0, 0.0, 0.0, 0.0, 1.327596, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 17, 17, 19, 19, 20, 20, 23, 23, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01869413, 3.1923077, 0.15384616, 1.0, 0.008307927, -0.07692308, 1.0, 0.34615386, 1.0, 1.0, 0.02824235, -0.012080364, 0.0069226264, 1.0, -0.0053602313, 1.0, -0.012923479, 1.0, 1.0, -0.019009626, -0.012941194, 1.0, 0.011458929, -0.016329883, 0.0054238313, 0.021510912, 1.0, -0.012653272, 0.0064884014, 0.010617981, -0.006559264], "split_indices": [117, 71, 0, 1, 1, 17, 0, 1, 53, 1, 7, 109, 0, 0, 0, 59, 0, 74, 0, 121, 105, 0, 0, 124, 0, 0, 0, 0, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1970.0, 98.0, 988.0, 982.0, 881.0, 107.0, 695.0, 287.0, 371.0, 510.0, 578.0, 117.0, 164.0, 123.0, 194.0, 177.0, 402.0, 108.0, 236.0, 342.0, 96.0, 98.0, 242.0, 160.0, 122.0, 114.0, 162.0, 180.0, 94.0, 148.0, 89.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0051038004, 0.002701879, -0.014852406, 0.015407123, -0.09560846, -0.010351847, 0.06365206, -0.002943937, -0.014854371, -0.04525874, 0.030640341, 0.1146563, -0.059968352, 0.0016121647, -0.11018499, -0.0137574235, 0.0155407805, 0.19995236, -0.0014035989, -0.01177502, -0.00028357096, 0.008389793, -0.042368144, -0.00529223, -0.019307952, -0.050648127, 0.010893522, 0.028838847, 0.0075149983, -0.009739944, 0.00047386132, -0.008825226, -0.014792158, -0.006398642, 0.0062884293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [2.3207085, 2.4556456, 0.0, 2.1636102, 0.7881017, 1.6240829, 3.8209302, 0.0, 0.0, 1.8654524, 2.8915656, 4.7091103, 0.58431715, 1.2883477, 1.2199175, 1.7425938, 0.0, 2.8475533, 0.0, 0.0, 0.0, 0.0, 0.6014244, 0.0, 0.0, 1.204204, 0.0, 0.0, 0.0, 0.0, 0.0, 0.81880563, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 22, 22, 25, 25, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.014852406, 1.0, 0.07692308, 1.0, 1.0, -0.002943937, -0.014854371, 1.0, 1.0, 1.0, 0.42307693, 1.0, -0.115384616, 1.0, 0.0155407805, 0.03846154, -0.0014035989, -0.01177502, -0.00028357096, 0.008389793, 1.0, -0.00529223, -0.019307952, 1.0, 0.010893522, 0.028838847, 0.0075149983, -0.009739944, 0.00047386132, 0.23076923, -0.014792158, -0.006398642, 0.0062884293], "split_indices": [43, 40, 0, 50, 1, 106, 106, 0, 0, 105, 121, 97, 1, 126, 1, 71, 0, 1, 0, 0, 0, 0, 115, 0, 0, 127, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1966.0, 107.0, 1741.0, 225.0, 1135.0, 606.0, 100.0, 125.0, 613.0, 522.0, 429.0, 177.0, 356.0, 257.0, 385.0, 137.0, 258.0, 171.0, 88.0, 89.0, 124.0, 232.0, 152.0, 105.0, 296.0, 89.0, 151.0, 107.0, 107.0, 125.0, 207.0, 89.0, 117.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029632759, 0.004969014, -0.015689684, 0.016402837, -0.08930609, 0.02603854, -0.012848626, -0.0014035909, -0.016600994, 0.03471082, -0.012148551, 0.076434605, 0.0028714838, 0.14590931, -0.015715864, 0.025568549, -0.07931577, 0.21493392, -0.0008518589, 0.010550672, -0.07352973, 0.0064622792, 0.015416014, -0.016164495, 0.00012981934, 0.0119051, 0.034709685, -0.013749226, -0.0016398112, -0.031185545, 0.06772257, 0.025555573, -0.014841929, -0.0035752219, 0.018473614, 0.009559331, -0.0018847419], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, 33, 35, -1, -1, -1, -1, -1], "loss_changes": [2.5165756, 2.1127326, 0.0, 2.440397, 1.2239844, 2.0968874, 0.0, 0.0, 0.0, 2.0564523, 0.0, 4.289426, 1.6378293, 4.071863, 2.0184035, 1.6903508, 1.2610079, 3.3454494, 0.0, 0.0, 0.71258473, 1.3814838, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4678826, 2.7606142, 0.7774716, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 29, 29, 30, 30, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, 34, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.015689684, 1.0, 1.0, 1.0, -0.012848626, -0.0014035909, -0.016600994, 1.0, -0.012148551, 1.0, 1.0, 1.0, 1.0, 1.0, -0.23076923, 1.0, -0.0008518589, 0.010550672, 0.07692308, 1.0, 0.015416014, -0.016164495, 0.00012981934, 0.0119051, 0.034709685, -0.013749226, -0.0016398112, 1.0, 1.0, 1.0, -0.014841929, -0.0035752219, 0.018473614, 0.009559331, -0.0018847419], "split_indices": [117, 40, 0, 43, 111, 52, 0, 0, 0, 53, 0, 97, 7, 122, 81, 61, 1, 121, 0, 0, 1, 15, 0, 0, 0, 0, 0, 0, 0, 71, 111, 97, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 1748.0, 212.0, 1639.0, 109.0, 107.0, 105.0, 1548.0, 91.0, 670.0, 878.0, 382.0, 288.0, 688.0, 190.0, 264.0, 118.0, 93.0, 195.0, 599.0, 89.0, 94.0, 96.0, 153.0, 111.0, 92.0, 103.0, 371.0, 228.0, 250.0, 121.0, 121.0, 107.0, 97.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010356078, 0.0089154495, -0.08354461, -0.0020822142, 0.011545931, -0.0033646382, -0.012338269, -0.01085669, 0.0053975508, 0.04137924, -0.020745702, -0.0028234292, 0.023594348, 0.012542905, -0.020946275, -0.047127612, 0.0151133565, -0.056166325, 0.06996419, 0.023949292, -0.13370535, 0.00093163707, -0.10013329, -0.0047670077, 0.11980923, 0.01368506, -0.009607189, -0.024243498, -0.0012758859, -0.0054297275, -0.014425578, 0.026682375, 0.0265573, -0.0032378105, 0.008842746], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, 9, 11, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [1.7012196, 2.1665359, 0.44329, 1.3349016, 0.0, 0.0, 0.0, 0.0, 1.4731023, 5.667571, 5.6978908, 3.662844, 0.0, 3.0418823, 0.0, 2.566084, 0.0, 1.0105559, 2.4626641, 3.103076, 2.4722872, 0.0, 0.42470312, 0.0, 4.004483, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6564051, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 24, 24, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, 10, 12, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.07692308, -0.53846157, 0.011545931, -0.0033646382, -0.012338269, -0.01085669, -0.03846154, -0.15384616, 1.0, 1.0, 0.023594348, 1.0, -0.020946275, 1.0, 0.0151133565, 1.0, 1.0, -0.34615386, 1.0, 0.00093163707, 1.0384616, -0.0047670077, 1.0, 0.01368506, -0.009607189, -0.024243498, -0.0012758859, -0.0054297275, -0.014425578, 1.0, 0.0265573, -0.0032378105, 0.008842746], "split_indices": [40, 125, 1, 1, 0, 0, 0, 0, 1, 1, 64, 61, 0, 39, 0, 108, 0, 124, 126, 1, 111, 0, 1, 0, 93, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1849.0, 223.0, 1676.0, 173.0, 99.0, 124.0, 110.0, 1566.0, 659.0, 907.0, 537.0, 122.0, 771.0, 136.0, 417.0, 120.0, 351.0, 420.0, 229.0, 188.0, 141.0, 210.0, 125.0, 295.0, 118.0, 111.0, 99.0, 89.0, 103.0, 107.0, 180.0, 115.0, 92.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00320335, -0.010170229, 0.010978886, -0.01887394, 0.011769646, 0.013077806, -0.06009939, 0.07925918, -0.047619604, -0.020862553, -0.1149594, 0.0045828293, 0.19906078, -0.018020138, -0.01888605, 0.05658089, -0.07634444, -0.076335095, -0.017881406, 0.012515615, -0.008887931, 0.034564037, 0.006305907, -0.08024888, 0.0075856345, -0.0029891543, 0.0143053355, -0.0010162512, -0.01743917, -0.0015770163, -0.015372358, -0.019402962, -0.002335849], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.6129787, 2.1479251, 0.0, 2.380226, 0.0, 4.089344, 1.6983483, 4.3568707, 2.219927, 1.976486, 0.8114257, 3.3807123, 3.7278585, 2.5645568, 0.0, 1.4356774, 1.7390399, 0.96084034, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7088797, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.010978886, 1.0, 0.011769646, -0.03846154, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.01888605, 0.42307693, 0.96153843, 1.0, -0.017881406, 0.012515615, -0.008887931, 0.034564037, 0.006305907, 1.0, 0.0075856345, -0.0029891543, 0.0143053355, -0.0010162512, -0.01743917, -0.0015770163, -0.015372358, -0.019402962, -0.002335849], "split_indices": [102, 0, 0, 106, 0, 1, 15, 50, 15, 137, 71, 69, 39, 93, 0, 1, 1, 7, 0, 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1930.0, 119.0, 1807.0, 123.0, 1018.0, 789.0, 487.0, 531.0, 460.0, 329.0, 300.0, 187.0, 439.0, 92.0, 192.0, 268.0, 205.0, 124.0, 131.0, 169.0, 90.0, 97.0, 264.0, 175.0, 96.0, 96.0, 160.0, 108.0, 115.0, 90.0, 88.0, 176.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008912024, 0.012639181, -0.054784518, 0.0039392337, 0.013960913, 0.0022798069, -0.07844966, -0.0092355525, 0.012755235, -0.014924767, -0.050503056, 0.004109993, -0.013108621, 0.00124105, -0.009878558, -0.07914333, 0.03625732, 0.011982063, -0.032828405, -0.062244225, 0.08624318, -0.008447475, 0.006854626, -0.015219297, -0.015358107, 0.030288033, 0.028022101, 0.0074195266, -0.011795095, -0.014204542, 0.072454736, 0.01962595, 0.021247663, 0.01031664, -0.008225262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1], "loss_changes": [1.50798, 1.825956, 0.7619412, 2.519408, 0.0, 0.0, 0.6291827, 2.2733772, 0.0, 0.0, 0.6925788, 3.3722284, 0.0, 0.0, 0.0, 7.968771, 4.475629, 1.4021926, 0.0, 1.3143027, 6.544997, 0.0, 0.0, 1.8555127, 0.0, 3.4008315, 0.0, 0.0, 0.0, 0.0, 2.7813416, 2.3234985, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 23, 23, 25, 25, 30, 30, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.013960913, 0.0022798069, 0.0, 1.3461539, 0.012755235, -0.014924767, 1.0, 1.0, -0.013108621, 0.00124105, -0.009878558, 1.0, 1.0, 1.0, -0.032828405, 1.0, 1.0, -0.008447475, 0.006854626, 1.0, -0.015358107, -1.0, 0.028022101, 0.0074195266, -0.011795095, -0.014204542, 1.0, 1.0, 0.021247663, 0.01031664, -0.008225262], "split_indices": [80, 102, 26, 88, 0, 0, 0, 1, 0, 0, 39, 5, 0, 0, 0, 0, 17, 13, 0, 15, 61, 0, 0, 121, 0, 0, 0, 0, 0, 0, 62, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1653.0, 415.0, 1547.0, 106.0, 97.0, 318.0, 1398.0, 149.0, 90.0, 228.0, 1260.0, 138.0, 99.0, 129.0, 351.0, 909.0, 257.0, 94.0, 306.0, 603.0, 95.0, 162.0, 202.0, 104.0, 468.0, 135.0, 108.0, 94.0, 92.0, 376.0, 273.0, 103.0, 150.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002496739, 0.0054716994, -0.011369471, -0.0033879878, 0.095427744, -0.012351162, 0.012002229, 0.013999875, 0.005085671, 0.0052425, -0.08693059, 0.017035702, -0.03375418, -0.17663395, 0.0030073766, 0.030758763, -0.008566226, -0.009344407, -0.0048993365, -0.023648402, -0.01154389, 0.014763035, 0.0153849665, 0.0049695987, -0.005408434, -0.007207499, 0.035540234, 0.012703474, 0.014806315, 0.007585005, -0.0021105874], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, 19, -1, 21, -1, -1, 23, -1, -1, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1], "loss_changes": [1.3383651, 1.5644767, 0.0, 1.9766859, 0.3496368, 2.1860008, 0.0, 0.0, 0.0, 0.6199394, 3.3376276, 1.4586568, 0.53909284, 0.65925455, 0.0, 1.7976316, 0.0, 0.0, 0.56659234, 0.0, 0.0, 1.4578346, 0.0, 0.0, 0.0, 0.0, 1.6754172, 1.1571403, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 21, 21, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, 20, -1, 22, -1, -1, 24, -1, -1, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.011369471, 5.0, 1.0, 1.0, 0.012002229, 0.013999875, 0.005085671, 1.0, 1.0, 1.3461539, 0.0, 1.0, 0.0030073766, 0.84615386, -0.008566226, -0.009344407, 1.0, -0.023648402, -0.01154389, 1.0, 0.0153849665, 0.0049695987, -0.005408434, -0.007207499, 1.0, 1.0, 0.014806315, 0.007585005, -0.0021105874], "split_indices": [117, 125, 0, 0, 39, 0, 0, 0, 0, 80, 109, 1, 0, 93, 0, 1, 0, 0, 106, 0, 0, 89, 0, 0, 0, 0, 58, 5, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1963.0, 99.0, 1787.0, 176.0, 1666.0, 121.0, 88.0, 88.0, 1348.0, 318.0, 1035.0, 313.0, 180.0, 138.0, 913.0, 122.0, 102.0, 211.0, 91.0, 89.0, 808.0, 105.0, 100.0, 111.0, 156.0, 652.0, 542.0, 110.0, 189.0, 353.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.006133606, -0.0003742194, 0.009729034, 0.008805644, -0.018619081, -0.0033263562, 0.013811128, -0.017442573, 0.06509808, 0.010020474, -0.067745075, 0.027438823, -0.026989577, -0.014964129, 0.015340229, -0.017186264, -0.1310013, -0.009493254, 0.0034482598, -0.05038434, 0.09297429, 0.006764074, -0.06441949, -0.024805026, -0.003620128, 0.02245873, -0.12198011, 0.021475194, -0.0031394332, -0.008180778, -0.004703119, 0.01165319, -0.0054347827, -0.023082895, -0.06266035, -0.01186812, 2.8437953e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, -1, 17, 19, -1, 21, 23, -1, -1, 25, 27, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [1.228584, 3.297256, 0.0, 2.889612, 0.0, 1.6265656, 0.0, 1.9285184, 5.550636, 3.2348514, 1.5766921, 0.0, 0.83532053, 2.9400418, 0.0, 1.097823, 2.4300768, 0.0, 0.0, 3.019633, 2.877611, 0.0, 0.053213656, 0.0, 0.0, 2.0737002, 1.8854098, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.66645545, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 20, 20, 22, 22, 25, 25, 26, 26, 34, 34], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, -1, 18, 20, -1, 22, 24, -1, -1, 26, 28, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [5.0, 1.0, 0.009729034, 1.0, -0.018619081, 1.2692307, 0.013811128, 0.115384616, 1.0, 1.0, 1.0, 0.027438823, 2.7307692, -0.115384616, 0.015340229, 1.0, 1.0, -0.009493254, 0.0034482598, 1.0, 1.0, 0.006764074, 1.0, -0.024805026, -0.003620128, 1.0, 1.0, 0.021475194, -0.0031394332, -0.008180778, -0.004703119, 0.01165319, -0.0054347827, -0.023082895, 1.0, -0.01186812, 2.8437953e-05], "split_indices": [0, 52, 0, 125, 0, 1, 0, 1, 137, 64, 127, 0, 1, 1, 0, 69, 69, 0, 0, 93, 106, 0, 106, 0, 0, 122, 69, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1933.0, 138.0, 1842.0, 91.0, 1684.0, 158.0, 1396.0, 288.0, 903.0, 493.0, 88.0, 200.0, 769.0, 134.0, 274.0, 219.0, 95.0, 105.0, 579.0, 190.0, 98.0, 176.0, 98.0, 121.0, 287.0, 292.0, 96.0, 94.0, 88.0, 88.0, 129.0, 158.0, 103.0, 189.0, 100.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014481617, -0.01230782, 0.04578853, 0.033806026, -0.027014988, -0.02882526, 0.117372334, 0.13363771, -0.0473072, 0.006229694, -0.057314575, 0.002094104, -0.008474248, 0.008580597, 0.014677234, 0.024358964, 0.0016189009, -0.012597549, 0.0032778499, 0.080904886, -0.07432744, 0.0066741235, -0.09589887, -0.002575858, 0.14479469, -0.10756751, -0.0011467458, -0.17093514, -0.04836784, 0.0005524842, 0.026435649, -0.008581285, -0.013147369, -0.02310792, -0.010635938, 0.00440117, -0.107999094, -0.0014850425, -0.021490833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1, 23, 25, -1, 27, -1, 29, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1, 37, -1, -1], "loss_changes": [1.0592918, 1.1387045, 2.06168, 3.2876544, 1.2822931, 0.52594817, 0.18282533, 2.3502946, 1.4112461, 3.6514807, 3.1878793, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1466334, 0.61012495, 0.0, 1.8118072, 0.0, 3.2803164, 0.09933305, 0.0, 0.76511717, 1.7132084, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8821468, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 19, 19, 20, 20, 22, 22, 24, 24, 25, 25, 27, 27, 28, 28, 36, 36], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1, 24, 26, -1, 28, -1, 30, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1, 38, -1, -1], "split_conditions": [1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.002094104, -0.008474248, 0.008580597, 0.014677234, 0.024358964, 0.0016189009, -0.012597549, 0.0032778499, 1.0, 1.0, 0.0066741235, 1.0, -0.002575858, 1.0, 1.0, -0.0011467458, 1.0, 1.0, 0.0005524842, 0.026435649, -0.008581285, -0.013147369, -0.02310792, -0.010635938, 0.00440117, 1.0, -0.0014850425, -0.021490833], "split_indices": [42, 0, 93, 122, 13, 69, 69, 109, 13, 108, 89, 0, 0, 0, 0, 0, 0, 0, 0, 39, 80, 0, 69, 0, 122, 97, 0, 97, 124, 0, 0, 0, 0, 0, 0, 0, 71, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1679.0, 386.0, 406.0, 1273.0, 189.0, 197.0, 182.0, 224.0, 607.0, 666.0, 100.0, 89.0, 95.0, 102.0, 94.0, 88.0, 113.0, 111.0, 315.0, 292.0, 158.0, 508.0, 118.0, 197.0, 191.0, 101.0, 197.0, 311.0, 91.0, 106.0, 100.0, 91.0, 102.0, 95.0, 122.0, 189.0, 101.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027112672, -0.007981041, 0.010656717, -0.045891225, 0.014925598, 0.010083701, -0.15604989, -0.0071087247, 0.134557, -0.04415564, 0.09854895, -0.02434302, -0.0065827924, 0.010930588, -0.025428437, 0.022790423, 0.002529826, 0.012340617, -0.124682926, 0.023385882, -0.005367463, 0.0005055398, -0.013303955, -0.0024302609, -0.02353061, -0.05755422, 0.06985961, -0.013771293, -0.023985941, -0.005175604, 0.13011464, 0.004775846, -0.0077230236, 0.002609244, 0.022168972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, 21, -1, -1, -1, 23, -1, -1, 25, -1, -1, -1, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [1.1891768, 1.7107381, 0.0, 4.5752635, 3.2370043, 2.3607621, 1.9709048, 2.211591, 1.9480083, 4.1154566, 3.8517056, 0.0, 0.0, 0.0, 2.500543, 0.0, 0.0, 0.0, 2.2875037, 0.0, 0.0, 2.9072633, 0.0, 0.0, 0.0, 1.0574799, 2.4108977, 0.0, 1.0581346, 0.0, 2.0956845, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 18, 18, 21, 21, 25, 25, 26, 26, 28, 28, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, 22, -1, -1, -1, 24, -1, -1, 26, -1, -1, -1, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, -0.1923077, 0.010656717, 1.0, 1.0, -0.3846154, 1.0, -0.03846154, 0.5, 1.0, 1.0, -0.02434302, -0.0065827924, 0.010930588, 1.0, 0.022790423, 0.002529826, 0.012340617, 1.0, 0.023385882, -0.005367463, 1.0, -0.013303955, -0.0024302609, -0.02353061, 0.26923078, 1.0, -0.013771293, 1.0, -0.005175604, 1.0, 0.004775846, -0.0077230236, 0.002609244, 0.022168972], "split_indices": [114, 1, 0, 126, 62, 1, 69, 1, 1, 15, 122, 0, 0, 0, 64, 0, 0, 0, 108, 0, 0, 39, 0, 0, 0, 1, 126, 0, 126, 0, 127, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1970.0, 95.0, 742.0, 1228.0, 492.0, 250.0, 1037.0, 191.0, 305.0, 187.0, 127.0, 123.0, 141.0, 896.0, 103.0, 88.0, 99.0, 206.0, 99.0, 88.0, 722.0, 174.0, 108.0, 98.0, 393.0, 329.0, 116.0, 277.0, 109.0, 220.0, 118.0, 159.0, 103.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0012481639, 0.007112779, -0.011256018, 0.018267145, -0.037647314, 0.030557908, -0.009974076, -0.07296881, 0.004924978, 0.015614959, 0.02002578, -0.011252932, -0.0025244989, 0.035885617, -0.056036178, 0.101753466, -0.021506583, -0.014866239, 0.003280935, -0.0008345088, 0.0319078, 0.03809989, -0.0890606, 0.009041711, -0.0084481845, -0.0039023955, 0.014072438, -0.0032278772, -0.013011231], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [1.3755978, 0.9785701, 0.0, 2.2756884, 1.2001101, 3.603395, 0.0, 0.5248575, 0.0, 1.8968549, 0.0, 0.0, 0.0, 3.8483467, 2.370075, 10.567777, 2.1905012, 0.0, 0.0, 2.4578116, 0.0, 2.2873764, 0.5944021, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.011256018, 1.3461539, 1.1538461, 0.88461536, -0.009974076, 1.0, 0.004924978, 0.23076923, 0.02002578, -0.011252932, -0.0025244989, 1.0, 1.0, -0.1923077, 1.0, -0.014866239, 0.003280935, 1.0, 0.0319078, 1.0, -0.26923078, 0.009041711, -0.0084481845, -0.0039023955, 0.014072438, -0.0032278772, -0.013011231], "split_indices": [117, 80, 0, 1, 1, 1, 0, 97, 0, 1, 0, 0, 0, 69, 69, 1, 121, 0, 0, 59, 0, 109, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 1569.0, 391.0, 1421.0, 148.0, 278.0, 113.0, 1306.0, 115.0, 152.0, 126.0, 1018.0, 288.0, 474.0, 544.0, 141.0, 147.0, 322.0, 152.0, 289.0, 255.0, 154.0, 168.0, 165.0, 124.0, 107.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0006118159, -0.010120653, 0.048327416, 0.042197928, -0.026944669, -0.010267622, 0.09604038, 0.121438466, -0.024906661, 0.014633052, -0.06532411, 0.020218356, -0.0017738965, 0.023068829, 0.0009839172, -0.009480779, 0.006353963, -0.015960094, 0.014544514, -0.021467954, -0.10291509, 0.02745819, -0.020410603, 0.05915332, -0.014306071, -0.15400942, 0.00283973, -0.063335955, 0.106850296, -0.0019057054, 0.013736372, -0.021830756, -0.00985639, -0.0021071273, -0.010133144, 0.018329512, -0.00013749195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, 21, -1, 23, 25, 27, -1, 29, -1, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0569874, 1.4831517, 2.7306309, 2.1801357, 2.0345554, 0.0, 3.4781482, 2.292133, 1.3725142, 2.4491959, 1.093019, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.0518117, 0.0, 2.999707, 2.3952274, 2.9049606, 0.0, 1.1255035, 0.0, 0.9162154, 0.0, 0.3019026, 1.7787511, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23, 25, 25, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, 22, -1, 24, 26, 28, -1, 30, -1, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.0, -0.5, 1.0, 1.0, -0.010267622, 1.0, 1.0, 1.0, 1.0, 1.0, 0.020218356, -0.0017738965, 0.023068829, 0.0009839172, -0.009480779, 0.006353963, 2.0, 0.014544514, 0.26923078, 0.46153846, 1.0, -0.020410603, 1.0, -0.014306071, 1.0, 0.00283973, 0.6923077, 1.0, -0.0019057054, 0.013736372, -0.021830756, -0.00985639, -0.0021071273, -0.010133144, 0.018329512, -0.00013749195], "split_indices": [42, 0, 1, 122, 13, 0, 126, 109, 126, 62, 53, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 127, 0, 113, 0, 122, 0, 1, 97, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1685.0, 379.0, 410.0, 1275.0, 91.0, 288.0, 188.0, 222.0, 612.0, 663.0, 149.0, 139.0, 95.0, 93.0, 124.0, 98.0, 496.0, 116.0, 306.0, 357.0, 403.0, 93.0, 184.0, 122.0, 257.0, 100.0, 188.0, 215.0, 92.0, 92.0, 119.0, 138.0, 89.0, 99.0, 126.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.008450603, 0.013553276, -0.008420817, 0.024493251, -0.030718096, 0.043982837, -0.009476272, -0.054345164, 0.004902323, 0.069786035, -0.007960087, -0.06345151, 0.05180388, -0.00987755, -0.0010212995, 0.12732305, 0.01824245, 0.0032914386, -0.1178758, 0.021855786, -0.007135428, 0.024098983, 0.039398517, 0.064025186, -0.0056573222, -0.019538801, -0.0032780834, 0.019761859, -0.010122071, 0.015146631, -7.039322e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, -1, 17, 19, -1, -1, 21, 23, -1, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.969255, 0.94104886, 0.0, 1.0314771, 0.7253608, 3.1569657, 1.8787233, 0.5823595, 0.0, 2.4288783, 0.0, 1.5838833, 5.4628735, 0.0, 0.0, 10.652544, 1.4797151, 0.0, 1.2730076, 0.0, 0.0, 6.0669737, 0.0, 1.5168769, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, -1, 18, 20, -1, -1, 22, 24, -1, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008420817, 0.1923077, 1.3076923, 1.0, 0.84615386, 1.0, 0.004902323, 1.0, -0.007960087, 1.0, 1.0, -0.00987755, -0.0010212995, -0.1923077, 1.0, 0.0032914386, 1.0, 0.021855786, -0.007135428, 1.0, 0.039398517, 1.0, -0.0056573222, -0.019538801, -0.0032780834, 0.019761859, -0.010122071, 0.015146631, -7.039322e-05], "split_indices": [43, 80, 0, 1, 1, 119, 1, 106, 0, 69, 0, 127, 108, 0, 0, 1, 113, 0, 69, 0, 0, 59, 0, 106, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1943.0, 107.0, 1558.0, 385.0, 990.0, 568.0, 297.0, 88.0, 819.0, 171.0, 302.0, 266.0, 148.0, 149.0, 387.0, 432.0, 109.0, 193.0, 113.0, 153.0, 279.0, 108.0, 268.0, 164.0, 101.0, 92.0, 117.0, 162.0, 114.0, 154.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003983305, -0.007001853, 0.009399317, 0.00078735425, -0.016745955, -0.008620616, 0.010538186, 0.018964076, -0.0311123, -0.008038518, 0.017615778, -0.06495764, 0.041820403, 0.06485484, -0.076593, -0.041728582, -0.01903945, 0.01824463, -0.022288444, 0.017834922, -0.002895842, -0.016408615, -0.04360376, 0.008269056, -0.07842776, -0.008694121, 0.003748484, 0.0045078825, -0.013675776, -0.017834207, -0.03992257, 0.007620799, -0.10874069, -0.018118074, -0.0037063118], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1], "loss_changes": [1.296498, 2.4296863, 0.0, 1.8243772, 0.0, 1.0553449, 0.0, 3.2429037, 2.312921, 3.2581525, 0.0, 1.8648186, 2.6775634, 3.3645406, 0.9698069, 2.4656835, 0.0, 0.0, 0.7883598, 0.0, 0.0, 0.0, 2.015718, 0.0, 1.6042898, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4055579, 0.0, 0.98134947, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 22, 22, 24, 24, 30, 30, 32, 32], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1], "split_conditions": [5.0, 1.0, 0.009399317, 1.0, -0.016745955, -0.03846154, 0.010538186, 1.0, 1.2692307, 1.0, 0.017615778, 1.0, 1.0, 1.0, 1.0, 0.0, -0.01903945, 0.01824463, 2.7307692, 0.017834922, -0.002895842, -0.016408615, 1.0, 0.008269056, 0.15384616, -0.008694121, 0.003748484, 0.0045078825, -0.013675776, -0.017834207, 0.5, 0.007620799, 0.84615386, -0.018118074, -0.0037063118], "split_indices": [0, 52, 0, 125, 0, 1, 0, 64, 1, 93, 0, 64, 137, 122, 89, 0, 0, 0, 1, 0, 0, 0, 39, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1944.0, 136.0, 1854.0, 90.0, 1701.0, 153.0, 764.0, 937.0, 652.0, 112.0, 640.0, 297.0, 316.0, 336.0, 540.0, 100.0, 93.0, 204.0, 143.0, 173.0, 92.0, 244.0, 123.0, 417.0, 98.0, 106.0, 125.0, 119.0, 116.0, 301.0, 112.0, 189.0, 94.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.006261998, -0.011422392, 0.009920354, -0.009121264, -0.0049600396, 0.0029506022, -0.06641084, 0.008457797, -0.008009787, -0.0011006135, -0.010738236, 0.03365352, -0.012234339, 0.094942346, -0.011375, -0.11598787, 0.021026358, 0.015036717, 0.0028517153, 0.04290216, -0.013589322, -0.02155657, -0.0021243894, 0.017580848, -0.004914774, 0.017870195, -0.0048741847, -0.06973479, 0.07713586, -0.017722286, -0.0019705463, 0.017591117, -1.0195772e-05, 0.0074077905, -0.007321597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [1.1200534, 1.0116712, 0.0, 0.0, 0.8822994, 0.7354411, 0.4698928, 0.7862009, 0.0, 0.0, 0.0, 1.876627, 2.8573575, 1.0603013, 2.6493301, 1.896313, 2.5175457, 0.0, 0.0, 3.39755, 0.0, 0.0, 0.0, 0.0, 2.856047, 0.0, 0.0, 2.185154, 1.808119, 0.0, 0.99693114, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 24, 24, 27, 27, 28, 28, 30, 30], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 0.009920354, -0.009121264, 1.0, 1.0, 1.0, 1.0, -0.008009787, -0.0011006135, -0.010738236, 1.0, -0.1923077, 1.0, 1.0, 1.0, -0.03846154, 0.015036717, 0.0028517153, 1.0, -0.013589322, -0.02155657, -0.0021243894, 0.017580848, 1.0, 0.017870195, -0.0048741847, 1.0, 1.0, -0.017722286, 1.0, 0.017591117, -1.0195772e-05, 0.0074077905, -0.007321597], "split_indices": [114, 1, 0, 0, 40, 43, 59, 126, 0, 0, 0, 69, 1, 15, 113, 69, 1, 0, 0, 109, 0, 0, 0, 0, 122, 0, 0, 69, 97, 0, 71, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1962.0, 96.0, 147.0, 1815.0, 1608.0, 207.0, 1508.0, 100.0, 88.0, 119.0, 680.0, 828.0, 288.0, 392.0, 201.0, 627.0, 157.0, 131.0, 273.0, 119.0, 98.0, 103.0, 90.0, 537.0, 110.0, 163.0, 300.0, 237.0, 116.0, 184.0, 104.0, 133.0, 89.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017605206, 0.006308762, -0.05884675, -0.0025549417, 0.015576312, -0.01807826, 0.009934029, -0.06619416, 0.0118314605, 0.0031571728, -0.1361796, 0.0013918603, 0.01096053, -0.009935438, -0.017423227, -0.0445132, 0.035948884, 0.0053649037, -0.07084378, 0.0054066726, 0.014740812, -0.110209, 0.0013718525, 0.064039305, -0.05117586, -0.051613957, -0.024471123, -0.006320792, 0.13409679, 0.011123828, -0.016525246, 0.0029810453, -0.013975382, 0.00096452393, 0.02585483], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, 13, 15, -1, -1, -1, 17, 19, -1, 21, 23, -1, 25, -1, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9484681, 2.3897939, 4.9186115, 1.5591638, 0.0, 0.0, 0.0, 2.1484473, 1.4177794, 0.0, 0.25643682, 1.9908594, 0.0, 0.0, 0.0, 1.3931365, 2.4374151, 0.0, 1.4147463, 1.8644816, 0.0, 2.2855372, 0.0, 2.4604354, 5.298909, 1.449701, 0.0, 0.0, 2.7568953, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 10, 10, 11, 11, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, 14, 16, -1, -1, -1, 18, 20, -1, 22, 24, -1, 26, -1, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, -1.0, 0.015576312, -0.01807826, 0.009934029, 1.0, 1.0, 0.0031571728, -0.115384616, 1.0, 0.01096053, -0.009935438, -0.017423227, 1.0, 1.0, 0.0053649037, 1.0, 1.0, 0.014740812, 1.0, 0.0013718525, 1.0, 1.0, 1.0, -0.024471123, -0.006320792, 1.0, 0.011123828, -0.016525246, 0.0029810453, -0.013975382, 0.00096452393, 0.02585483], "split_indices": [0, 102, 122, 0, 0, 0, 0, 122, 88, 0, 1, 17, 0, 0, 0, 5, 61, 0, 80, 13, 0, 50, 0, 39, 39, 59, 0, 0, 93, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1804.0, 255.0, 1703.0, 101.0, 144.0, 111.0, 314.0, 1389.0, 131.0, 183.0, 1255.0, 134.0, 93.0, 90.0, 539.0, 716.0, 114.0, 425.0, 562.0, 154.0, 290.0, 135.0, 276.0, 286.0, 202.0, 88.0, 98.0, 178.0, 118.0, 168.0, 105.0, 97.0, 89.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004969547, 0.008847511, -0.009622789, -0.07086849, -9.263787e-06, -0.12590002, 0.0039194524, 0.02730292, -0.03218239, 0.0033121356, -0.028138762, 0.013508244, 0.0037484518, -0.014493826, -0.001280287, 0.013773965, -0.0262204, 0.10712954, -0.093641534, -0.053152043, 0.010817631, 0.0040782583, 0.018470442, -0.012258887, -0.019550823, -0.004861655, -0.14275752, -0.007018374, 0.004320104, 0.0062905937, -0.051989384, -0.009846656, -0.018607503, -0.0005713084, -0.009091335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, 25, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.89833903, 0.0, 1.1587329, 1.617202, 1.4947, 4.4012027, 0.0, 2.3355937, 2.721311, 0.0, 0.0, 0.0, 3.0317502, 0.0, 6.1378875, 0.0, 2.233246, 1.4514129, 2.7440495, 2.2241201, 0.0, 0.0, 0.0, 0.5911015, 0.0, 1.0667068, 0.34534264, 0.0, 0.0, 0.0, 0.35484737, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 25, 25, 26, 26, 30, 30], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, 26, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008847511, -0.42307693, 1.0, 1.0, 1.0, 0.0039194524, -0.26923078, -0.1923077, 0.0033121356, -0.028138762, 0.013508244, 1.0, -0.014493826, 0.46153846, 0.013773965, 1.0, 1.0, 1.0, 1.0, 0.010817631, 0.0040782583, 0.018470442, 1.2307693, -0.019550823, 1.0, -0.07692308, -0.007018374, 0.004320104, 0.0062905937, 1.0, -0.009846656, -0.018607503, -0.0005713084, -0.009091335], "split_indices": [1, 0, 1, 61, 12, 109, 0, 1, 1, 0, 0, 0, 89, 0, 1, 0, 62, 71, 115, 15, 0, 0, 0, 1, 0, 126, 1, 0, 0, 0, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 98.0, 1968.0, 267.0, 1701.0, 178.0, 89.0, 920.0, 781.0, 88.0, 90.0, 165.0, 755.0, 168.0, 613.0, 138.0, 617.0, 282.0, 331.0, 514.0, 103.0, 152.0, 130.0, 184.0, 147.0, 334.0, 180.0, 90.0, 94.0, 137.0, 197.0, 89.0, 91.0, 90.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.006889216, -0.012654868, 0.007615336, -0.023606697, 0.046615984, -0.058731068, 0.011474574, 0.018517975, -0.016306676, 0.00891667, -0.077116154, 0.044964746, -0.04883074, 0.003802408, -0.007791891, -0.020588534, -0.047870267, 0.023404205, 0.0020127501, -0.01017104, 0.00015646145, -0.099784866, 0.002313835, -0.051382266, 0.00911157, -0.02245099, -0.036123108, -0.00775972, 0.008438465, -0.016124688, 0.0019826267, 0.004291177, -0.011852121], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9882319, 1.2528096, 0.0, 2.0072663, 2.6243596, 2.2133586, 1.6460028, 0.0, 0.69292, 0.0, 2.7265606, 4.255533, 0.77548295, 0.0, 0.0, 0.0, 1.5371202, 0.0, 2.031518, 0.0, 0.0, 2.3026617, 1.9675092, 2.0888214, 0.0, 0.0, 1.2503664, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 11, 11, 12, 12, 16, 16, 18, 18, 21, 21, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.2692307, 0.007615336, 1.0, 1.0, 1.0, 0.115384616, 0.018517975, 1.0, 0.00891667, 1.0, 1.0, 0.65384614, 0.003802408, -0.007791891, -0.020588534, 1.0, 0.023404205, 1.0, -0.01017104, 0.00015646145, 0.03846154, 1.0, -0.42307693, 0.00911157, -0.02245099, 0.46153846, -0.00775972, 0.008438465, -0.016124688, 0.0019826267, 0.004291177, -0.011852121], "split_indices": [0, 1, 0, 124, 137, 104, 1, 0, 106, 0, 89, 81, 1, 0, 0, 0, 15, 0, 12, 0, 0, 1, 115, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1930.0, 134.0, 1629.0, 301.0, 814.0, 815.0, 94.0, 207.0, 90.0, 724.0, 524.0, 291.0, 110.0, 97.0, 134.0, 590.0, 97.0, 427.0, 142.0, 149.0, 290.0, 300.0, 267.0, 160.0, 98.0, 192.0, 152.0, 148.0, 105.0, 162.0, 98.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0053413967, 0.023916064, -0.008816803, 0.016598359, -0.008990003, -0.052036397, 0.014919487, 0.026479857, -0.0620107, 0.0038195613, -0.07936219, -0.00867232, 0.043490265, -0.005897357, 0.062460255, 0.011484195, -0.16143058, -0.012311071, -0.0030655484, 0.015116218, 0.013941146, -0.0016961517, 0.013585001, -0.018299092, -0.013915946, 0.03569787, -0.0044677, -0.0016668711, 0.008247478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, -1, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1], "loss_changes": [0.5399059, 4.151297, 1.195142, 0.0, 1.3559393, 1.0183182, 2.1838162, 1.3282483, 5.081392, 0.0, 0.67547774, 0.0, 1.5976216, 0.0, 1.7719386, 0.0, 0.088831425, 0.0, 0.0, 0.55748135, 0.0, 0.0, 0.0, 0.0, 0.0, 0.8254974, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 19, 19, 25, 25], "right_children": [2, 4, 6, -1, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.016598359, 1.0, 0.0, 1.0, 1.0, 1.0, 0.0038195613, 1.0, -0.00867232, 1.0, -0.005897357, 1.0, 0.011484195, 1.0, -0.012311071, -0.0030655484, 1.0, 0.013941146, -0.0016961517, 0.013585001, -0.018299092, -0.013915946, 1.0, -0.0044677, -0.0016668711, 0.008247478], "split_indices": [53, 81, 81, 0, 121, 0, 5, 69, 69, 0, 137, 0, 0, 0, 2, 0, 61, 0, 0, 105, 0, 0, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 888.0, 1165.0, 167.0, 721.0, 413.0, 752.0, 432.0, 289.0, 96.0, 317.0, 165.0, 587.0, 128.0, 304.0, 104.0, 185.0, 167.0, 150.0, 453.0, 134.0, 146.0, 158.0, 94.0, 91.0, 337.0, 116.0, 159.0, 178.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0032158075, 0.008586435, -0.0106300665, -0.0019373774, 0.05620816, -0.029763447, 0.017488366, -0.007664827, 0.10148883, -0.0039751143, -0.014475402, -0.033303007, 0.06060198, 0.020581316, -0.0010113915, 0.0103822965, -0.03748344, 0.02574741, -0.013100462, 0.017694365, 0.013367886, 0.0051432946, -0.07537222, -0.052010257, 0.01738179, -0.011031076, 0.085336074, -0.014800849, -1.9619321e-05, -0.0068698335, -0.0035872303, 0.002293635, 0.017373567], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, -1, -1, -1, 21, 23, -1, -1, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.2204568, 0.9912988, 0.0, 0.87567824, 2.153667, 1.974966, 2.089069, 0.0, 3.10865, 1.9650002, 0.0, 2.5269632, 2.8355708, 0.0, 0.0, 0.0, 1.3981075, 3.1432176, 0.0, 0.0, 3.266641, 0.0, 1.5890067, 0.04820639, 0.0, 0.0, 1.2797369, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 16, 16, 17, 17, 20, 20, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, -1, -1, -1, 22, 24, -1, -1, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0106300665, 1.0, -0.5, 1.0, -0.07692308, -0.007664827, 1.0, -0.26923078, -0.014475402, -0.30769232, 0.23076923, 0.020581316, -0.0010113915, 0.0103822965, 1.0, 1.0, -0.013100462, 0.017694365, 1.0, 0.0051432946, 1.0, -0.46153846, 0.01738179, -0.011031076, 1.0, -0.014800849, -1.9619321e-05, -0.0068698335, -0.0035872303, 0.002293635, 0.017373567], "split_indices": [117, 42, 0, 39, 1, 0, 1, 0, 126, 1, 0, 1, 1, 0, 0, 0, 81, 12, 0, 0, 69, 0, 116, 1, 0, 0, 53, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1978.0, 97.0, 1620.0, 358.0, 666.0, 954.0, 91.0, 267.0, 544.0, 122.0, 438.0, 516.0, 138.0, 129.0, 129.0, 415.0, 273.0, 165.0, 149.0, 367.0, 124.0, 291.0, 179.0, 94.0, 135.0, 232.0, 148.0, 143.0, 88.0, 91.0, 136.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0060623633, -0.010394964, 0.008107594, -0.0028246373, -0.009971498, -0.011252624, 0.015889904, -0.038837258, 0.017943028, -0.15487455, -0.009746215, 0.061723594, -0.032219864, -0.013168654, -0.017806252, -0.043816175, 0.0862358, 0.028822169, 0.018903777, 0.012330886, -0.015516283, -0.0060580685, -0.020590901, 0.016617, 0.0014458116, 0.013724575, -0.01412206, -0.008274207, 0.009105373, -0.06771877, 0.04875143, 0.0040725204, -0.007881372, -0.01377825, 0.0017914647, 0.009609884, -0.0012606903], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.78112084, 1.3327538, 0.0, 2.4765794, 0.0, 1.3908421, 0.0, 2.9975734, 1.8425779, 0.095705986, 2.321773, 1.8765899, 2.141586, 0.0, 0.0, 3.2070467, 1.0671731, 1.6575959, 0.0, 2.1480267, 0.0, 1.4363267, 0.0, 0.0, 0.0, 0.0, 0.9047811, 0.0, 0.0, 1.1999599, 0.65366066, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 26, 26, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [3.6923077, 1.3461539, 0.008107594, 1.1923077, -0.009971498, 1.0, 0.015889904, 1.0, 1.0, -0.34615386, 1.0, 1.0, 1.0, -0.013168654, -0.017806252, 1.0, 1.0, 0.0, 0.018903777, -0.34615386, -0.015516283, 1.0, -0.020590901, 0.016617, 0.0014458116, 0.013724575, 1.0, -0.008274207, 0.009105373, 1.0, 1.0, 0.0040725204, -0.007881372, -0.01377825, 0.0017914647, 0.009609884, -0.0012606903], "split_indices": [1, 1, 0, 1, 0, 124, 0, 89, 15, 1, 0, 42, 106, 0, 0, 61, 111, 0, 0, 1, 0, 15, 0, 0, 0, 0, 50, 0, 0, 105, 69, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1971.0, 98.0, 1817.0, 154.0, 1727.0, 90.0, 888.0, 839.0, 178.0, 710.0, 448.0, 391.0, 89.0, 89.0, 524.0, 186.0, 356.0, 92.0, 287.0, 104.0, 425.0, 99.0, 88.0, 98.0, 101.0, 255.0, 130.0, 157.0, 200.0, 225.0, 138.0, 117.0, 110.0, 90.0, 127.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0028890222, -0.002282957, 0.007734239, 0.004751933, -0.014772341, 0.0107008815, -0.0005069911, -0.010324885, 0.049332447, 0.018366786, -0.04260302, 0.017116802, -0.008773729, -0.0068726847, 0.015067048, -0.056602288, 0.0050414293, 0.0043369695, -0.0069607757, -0.05530987, 0.014781378, -0.016133357, -0.031397242, -0.014828798, -0.019866481, -0.07703972, 0.014245226, -0.079055406, 0.03956406, 0.00047122547, -0.014568645, -0.0044917776, 0.0065611573, -0.004385907, -0.011385625, 0.013138803, -0.0066459454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, -1, 7, 9, 11, 13, 15, -1, 17, 19, -1, 21, -1, -1, -1, 23, -1, -1, 25, 27, -1, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.79440236, 1.9736714, 0.0, 0.9894809, 0.0, 0.0, 0.85630697, 1.3539783, 2.0388675, 2.5845995, 0.89589584, 0.0, 0.6185585, 4.8701744, 0.0, 1.5785735, 0.0, 0.0, 0.0, 2.8725646, 0.0, 0.0, 1.0041196, 1.3484792, 0.0, 1.3524938, 0.732396, 0.2168001, 2.0347195, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 19, 19, 22, 22, 23, 23, 25, 25, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, -1, 8, 10, 12, 14, 16, -1, 18, 20, -1, 22, -1, -1, -1, 24, -1, -1, 26, 28, -1, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.007734239, -0.5769231, -0.014772341, 0.0107008815, 1.2692307, -0.03846154, 1.0, -0.15384616, 1.0, 0.017116802, 1.0, 1.0, 0.015067048, 0.07692308, 0.0050414293, 0.0043369695, -0.0069607757, 1.0, 0.014781378, -0.016133357, 1.0, 1.0, -0.019866481, 0.46153846, 0.46153846, 1.0, 1.0, 0.00047122547, -0.014568645, -0.0044917776, 0.0065611573, -0.004385907, -0.011385625, 0.013138803, -0.0066459454], "split_indices": [0, 52, 0, 1, 0, 0, 1, 1, 137, 1, 44, 0, 106, 61, 0, 1, 0, 0, 0, 113, 0, 0, 137, 13, 0, 1, 1, 93, 109, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1929.0, 134.0, 1840.0, 89.0, 90.0, 1750.0, 1462.0, 288.0, 774.0, 688.0, 93.0, 195.0, 650.0, 124.0, 598.0, 90.0, 105.0, 90.0, 495.0, 155.0, 116.0, 482.0, 386.0, 109.0, 241.0, 241.0, 177.0, 209.0, 110.0, 131.0, 112.0, 129.0, 88.0, 89.0, 112.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.004939401, -0.0107176015, 0.007963242, -0.0031637375, -0.016714197, -0.016186006, 0.0447278, 0.048502, -0.027150078, -0.011250211, 0.018053535, -0.0057247444, 0.019510921, 0.0020953547, -0.069374256, 0.005878291, -0.00950694, 0.08953977, -0.034509275, -0.04824563, -0.015868079, 0.0013209049, 0.019640272, 0.017826851, -0.087663166, -0.11039622, 0.018952701, 0.0114866765, -0.008697623, -0.016266098, 0.0037333171, -0.006182257, -0.016325574, 0.007389467, -0.004910039], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, 21, 23, 25, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0086205, 2.2828665, 0.0, 1.1493988, 0.0, 1.0276943, 2.9952817, 3.2557626, 1.5299973, 1.6377636, 0.0, 0.0, 0.0, 2.3430371, 0.9566698, 0.0, 0.0, 1.7618955, 1.4354446, 1.7123308, 0.0, 0.0, 0.0, 2.6442213, 2.3998604, 0.5468936, 0.73657745, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, 22, 24, 26, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.007963242, 1.0, -0.016714197, 1.0, 1.0, 0.9230769, 0.1923077, 1.0, 0.018053535, -0.0057247444, 0.019510921, 1.0, 1.0, 0.005878291, -0.00950694, 1.0, 1.0, 1.0, -0.015868079, 0.0013209049, 0.019640272, 1.0, 1.0, 1.0, 1.0, 0.0114866765, -0.008697623, -0.016266098, 0.0037333171, -0.006182257, -0.016325574, 0.007389467, -0.004910039], "split_indices": [0, 52, 0, 58, 0, 26, 64, 1, 1, 106, 0, 0, 0, 81, 0, 0, 0, 124, 126, 115, 0, 0, 0, 39, 106, 13, 127, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1932.0, 132.0, 1843.0, 89.0, 1449.0, 394.0, 210.0, 1239.0, 279.0, 115.0, 122.0, 88.0, 732.0, 507.0, 152.0, 127.0, 216.0, 516.0, 410.0, 97.0, 126.0, 90.0, 260.0, 256.0, 213.0, 197.0, 135.0, 125.0, 160.0, 96.0, 111.0, 102.0, 109.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0041256947, 0.017469654, -0.017423447, 0.008999875, 0.012804212, -0.046135113, 0.026748333, -0.10040997, 0.028409552, -0.0098453555, -0.09279335, 0.008621712, -0.0031214897, -0.0143393, -0.0056943954, -0.0365857, 0.05750356, 0.024828898, -0.008036269, -0.015258615, -0.004526572, -0.082679614, 0.0077112587, 0.025466014, 0.017533291, 0.0013712954, 0.00363195, -0.0017907642, -0.016926948, 0.0423701, -0.0047566174, -0.015189408, 0.014099342, -0.0064605693, 0.0062788553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [0.5955179, 1.1978141, 1.0044504, 2.5228484, 0.0, 0.8127434, 1.0754651, 0.33442533, 1.9079909, 0.6601868, 0.59677947, 0.0, 0.0, 0.0, 0.0, 1.6351293, 2.6311488, 0.023118772, 0.0, 0.0, 0.0, 1.245108, 0.0, 0.6765291, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5261364, 0.0, 1.0828, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23, 29, 29, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -1.0, 0.012804212, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.008621712, -0.0031214897, -0.0143393, -0.0056943954, 1.0, 1.0, 1.0, -0.008036269, -0.015258615, -0.004526572, 1.0, 0.0077112587, 1.0, 0.017533291, 0.0013712954, 0.00363195, -0.0017907642, -0.016926948, 1.0, -0.0047566174, 1.0, 0.014099342, -0.0064605693, 0.0062788553], "split_indices": [137, 102, 115, 0, 0, 17, 69, 124, 17, 61, 109, 0, 0, 0, 0, 39, 42, 122, 0, 0, 0, 61, 0, 74, 0, 0, 0, 0, 0, 122, 0, 113, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1279.0, 792.0, 1188.0, 91.0, 480.0, 312.0, 179.0, 1009.0, 270.0, 210.0, 154.0, 158.0, 90.0, 89.0, 312.0, 697.0, 181.0, 89.0, 93.0, 117.0, 222.0, 90.0, 548.0, 149.0, 92.0, 89.0, 127.0, 95.0, 445.0, 103.0, 281.0, 164.0, 172.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002106731, 0.007902658, -0.045522887, 0.013302873, -0.009180235, -0.00068285427, -0.008253484, 0.0038176922, 0.010404061, -0.009747738, 0.011282983, 0.0051937504, 0.010203217, 0.01915671, -0.016170925, 0.027556231, -0.007475792, 0.013884379, 0.015719466, 0.02329089, -0.0066706515, 0.0036425348, -0.005609959], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, -1, 11, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1], "loss_changes": [0.57254374, 0.995554, 0.32223433, 1.509604, 0.0, 0.0, 0.0, 1.200841, 0.0, 0.0, 0.81728464, 3.2300167, 0.0, 1.0089236, 0.0, 2.0807943, 0.0, 0.80508, 0.0, 0.99165606, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, -1, 12, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.009180235, -0.00068285427, -0.008253484, -0.53846157, 0.010404061, -0.009747738, 3.4615386, 1.3461539, 0.010203217, 1.0, -0.016170925, 0.88461536, -0.007475792, 0.5769231, 0.015719466, 1.0, -0.0066706515, 0.0036425348, -0.005609959], "split_indices": [40, 117, 111, 125, 0, 0, 0, 1, 0, 0, 1, 1, 0, 31, 0, 1, 0, 1, 0, 116, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1849.0, 225.0, 1754.0, 95.0, 110.0, 115.0, 1588.0, 166.0, 109.0, 1479.0, 1386.0, 93.0, 1279.0, 107.0, 1174.0, 105.0, 1062.0, 112.0, 951.0, 111.0, 816.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [0.004141932, 0.008889324, -0.00016234865, -0.059756607, 0.005943892, -0.012191644, 0.00044751863, 0.014853527, -0.062018957, -0.0068203397, 0.036173426, -0.0013967137, -0.010046039, -0.10050406, 0.026187392, 0.09732187, -0.012800657, -0.01982535, 0.0018495215, 0.10149077, -0.034336813, 0.17356639, 0.021934016, -0.08949149, 0.043962624, 0.0018411642, 0.017836994, 0.004081434, -0.012218243, 0.023001174, 0.011712099, 0.01251598, -0.008363779, -0.015664076, -0.002375585, 0.009719832, -0.0039386153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, -1, 3, 5, 7, -1, -1, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, -1, -1, 25, 27, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7547579, 0.0, 0.71651316, 0.730653, 1.081466, 0.0, 0.0, 0.7296319, 0.38236606, 2.4212613, 2.3837726, 0.0, 0.0, 2.3729517, 2.6388953, 2.0347598, 1.9241246, 0.0, 0.0, 1.6478593, 2.1191459, 0.56074905, 1.9397966, 0.8298502, 1.1270311, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24], "right_children": [2, -1, 4, 6, 8, -1, -1, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, -1, -1, 26, 28, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008889324, -0.46153846, 1.0, 1.0, -0.012191644, 0.00044751863, 1.0, 1.0, -0.1923077, -0.03846154, -0.0013967137, -0.010046039, 1.0, 0.46153846, 1.0, 0.46153846, -0.01982535, 0.0018495215, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0018411642, 0.017836994, 0.004081434, -0.012218243, 0.023001174, 0.011712099, 0.01251598, -0.008363779, -0.015664076, -0.002375585, 0.009719832, -0.0039386153], "split_indices": [1, 0, 1, 124, 40, 0, 0, 13, 69, 1, 1, 0, 0, 97, 1, 39, 1, 0, 0, 59, 115, 59, 69, 59, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 100.0, 1969.0, 183.0, 1786.0, 93.0, 90.0, 1579.0, 207.0, 783.0, 796.0, 92.0, 115.0, 204.0, 579.0, 354.0, 442.0, 112.0, 92.0, 258.0, 321.0, 176.0, 178.0, 188.0, 254.0, 124.0, 134.0, 173.0, 148.0, 88.0, 88.0, 90.0, 88.0, 93.0, 95.0, 155.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.006257847, 0.008394523, -0.010841338, -0.0083374, -0.0061314227, -0.026414683, 0.013503807, -0.010857701, -0.01188065, -0.01161502, 0.014254564, 0.011350273, -0.027125787, -0.036275253, 0.06737684, -0.01541113, -0.0008375525, -0.08008386, 0.04140388, 0.019773882, -0.004850045, 0.009355789, -0.0383181, 0.00053448125, -0.14613289, 0.010050234, -0.0021071616, -0.010327695, -0.0009479412, -0.02241437, -0.0046771723, 0.010919593, -0.0061626025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, 13, -1, -1, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.8550039, 0.0, 0.67231315, 0.0, 0.7359965, 1.3065395, 3.0436559, 1.5739763, 0.0, 1.5310947, 0.0, 0.0, 2.296698, 2.038406, 2.8248215, 0.0, 2.0166557, 2.1610708, 0.7975168, 0.0, 0.0, 0.0, 0.99042886, 0.0, 1.6742678, 0.0, 0.0, 0.0, 1.7309796, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 22, 22, 24, 24, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, 14, -1, -1, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008394523, 1.0, -0.0083374, 1.0, 1.0, 1.0, -0.3846154, -0.01188065, 1.0, 0.014254564, 0.011350273, -0.1923077, 0.53846157, 1.0, -0.01541113, 0.1923077, -0.23076923, 1.3461539, 0.019773882, -0.004850045, 0.009355789, 0.8076923, 0.00053448125, 1.0, 0.010050234, -0.0021071616, -0.010327695, 1.0, -0.02241437, -0.0046771723, 0.010919593, -0.0061626025], "split_indices": [1, 0, 104, 0, 13, 7, 42, 1, 0, 7, 0, 0, 1, 1, 69, 0, 1, 1, 1, 0, 0, 0, 1, 0, 106, 0, 0, 0, 137, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 100.0, 1968.0, 120.0, 1848.0, 909.0, 939.0, 778.0, 131.0, 786.0, 153.0, 90.0, 688.0, 599.0, 187.0, 118.0, 570.0, 383.0, 216.0, 88.0, 99.0, 162.0, 408.0, 167.0, 216.0, 111.0, 105.0, 149.0, 259.0, 121.0, 95.0, 92.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.001445796, -0.04681538, 0.0057138125, -0.01828647, 0.0035123394, -0.0023995375, 0.011842179, -0.010136882, 0.006899287, 0.039584797, -0.026360007, 0.08196155, -0.008136549, -0.017086644, -0.010399565, 0.017368658, 0.0012579783, -0.025151255, 0.0056543224, -0.010401047, -0.009509876, 0.0820717, -0.05491151, -0.0025789763, 0.015156636, -0.016993826, -0.019039761, 0.0104791885, -0.007583474], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, -1, -1, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, -1, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, 27, -1, -1], "loss_changes": [0.67207026, 3.1436558, 1.6341033, 0.0, 0.0, 0.9208301, 0.0, 1.2131879, 0.0, 1.8964279, 0.81641555, 1.7437474, 0.0, 0.60151553, 0.0, 0.0, 0.0, 0.94197863, 0.0, 3.1034677, 0.0, 1.8364702, 2.1002407, 0.0, 0.0, 0.0, 2.72881, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 19, 19, 21, 21, 22, 22, 26, 26], "right_children": [2, 4, 6, -1, -1, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, -1, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, 28, -1, -1], "split_conditions": [-0.46153846, 1.0, 1.0, -0.01828647, 0.0035123394, 1.0, 0.011842179, 0.0, 0.006899287, 1.0, 1.0, 1.0, -0.008136549, 5.0, -0.010399565, 0.017368658, 0.0012579783, 1.0, 0.0056543224, -0.03846154, -0.009509876, 1.0, 0.26923078, -0.0025789763, 0.015156636, -0.016993826, 0.6923077, 0.0104791885, -0.007583474], "split_indices": [1, 89, 90, 0, 0, 119, 0, 0, 0, 80, 40, 122, 0, 0, 0, 0, 0, 62, 0, 1, 0, 13, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 282.0, 1787.0, 106.0, 176.0, 1667.0, 120.0, 1504.0, 163.0, 370.0, 1134.0, 274.0, 96.0, 1013.0, 121.0, 118.0, 156.0, 913.0, 100.0, 754.0, 159.0, 245.0, 509.0, 96.0, 149.0, 121.0, 388.0, 122.0, 266.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.007146135, 0.017578349, -0.044681337, 0.044825513, -0.009132076, -0.013429957, 0.0045985063, 0.010351231, 0.027075514, 0.011751351, -0.015989056, 0.10811381, -0.027278133, -0.038462862, 0.044101946, 0.016645802, 0.0046233553, -0.10448231, 0.029237332, -0.014867629, 0.005214203, 0.019269053, -0.016787557, -0.0020066935, -0.02349424, -0.03456225, 0.014789266, -0.009095952, 0.004923833, -0.01070065, 0.00416162], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.1100063, 1.2437817, 2.7951255, 6.5892916, 2.7170298, 0.0, 0.0, 2.7001982, 0.0, 1.2313404, 0.0, 0.73651195, 2.3125122, 2.965806, 4.1708913, 0.0, 0.0, 2.4668748, 2.3164692, 0.0, 0.0, 0.0, 1.6014073, 0.0, 0.0, 1.0982195, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.84615386, 1.0, -0.013429957, 0.0045985063, -0.34615386, 0.027075514, -0.115384616, -0.015989056, 1.0, 1.0, 1.0, 0.34615386, 0.016645802, 0.0046233553, 1.0, 1.0, -0.014867629, 0.005214203, 0.019269053, 1.0, -0.0020066935, -0.02349424, 0.07692308, 0.014789266, -0.009095952, 0.004923833, -0.01070065, 0.00416162], "split_indices": [64, 108, 108, 1, 62, 0, 0, 1, 0, 1, 0, 39, 59, 109, 1, 0, 0, 39, 71, 0, 0, 0, 23, 0, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1709.0, 344.0, 846.0, 863.0, 173.0, 171.0, 734.0, 112.0, 758.0, 105.0, 204.0, 530.0, 297.0, 461.0, 105.0, 99.0, 224.0, 306.0, 134.0, 163.0, 134.0, 327.0, 136.0, 88.0, 199.0, 107.0, 154.0, 173.0, 102.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018647698, 0.0044139195, -0.050886855, -0.008621778, 0.010332727, -0.013179979, 0.002089072, 0.0029631378, 0.010656021, 0.011367346, -0.0066277273, -0.01522081, 0.010708458, 0.027453166, -0.0348216, 0.019760467, -0.04734497, -0.08227875, 0.009491382, 0.020196693, -0.017149301, 0.0039634006, -0.11701173, -0.02926668, 0.018346226, -0.007202176, 0.012356247, -0.170951, -0.0010504517, -0.0133013055, 0.0049661216, -0.026637865, -0.0046233647, -0.0072820275, 0.0053423243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, -1, -1, 11, 13, -1, 15, 17, -1, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.6343645, 0.9800603, 1.3590102, 0.0, 1.2162038, 0.0, 0.0, 1.6914603, 0.0, 0.0, 1.4324859, 1.1400726, 0.0, 5.4598904, 1.9641721, 0.0, 2.49878, 1.9097118, 3.2567604, 1.8397211, 0.0, 0.0, 2.0164661, 1.4028544, 0.0, 0.0, 0.0, 2.773046, 0.0, 0.0, 1.1194835, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 22, 22, 23, 23, 27, 27, 30, 30], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, -1, -1, 12, 14, -1, 16, 18, -1, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.1923077, -0.008621778, 1.0, -0.013179979, 0.002089072, -0.5, 0.010656021, 0.011367346, 5.0, 1.0, 0.010708458, 1.0, 1.0, 0.019760467, 0.53846157, 0.0, 1.0, 0.0, -0.017149301, 0.0039634006, 1.0, -1.0, 0.018346226, -0.007202176, 0.012356247, 1.0, -0.0010504517, -0.0133013055, 1.0, -0.026637865, -0.0046233647, -0.0072820275, 0.0053423243], "split_indices": [119, 104, 1, 0, 90, 0, 0, 1, 0, 0, 0, 81, 0, 53, 53, 0, 1, 0, 61, 1, 0, 0, 80, 0, 0, 0, 0, 15, 0, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1827.0, 234.0, 112.0, 1715.0, 110.0, 124.0, 1593.0, 122.0, 127.0, 1466.0, 1363.0, 103.0, 429.0, 934.0, 131.0, 298.0, 451.0, 483.0, 193.0, 105.0, 100.0, 351.0, 395.0, 88.0, 102.0, 91.0, 233.0, 118.0, 98.0, 297.0, 132.0, 101.0, 114.0, 183.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0055923667, 0.009601766, -0.0066760704, 0.0005064968, 0.037242413, -0.020917442, 0.022104854, -0.010301479, 0.078166276, 0.010713307, -0.039464053, 0.043511704, -0.010966155, 0.0066264863, 0.029052645, -0.086416885, 0.009107831, 0.10715914, 0.0060251104, 0.009780566, -0.008987701, -0.002662068, -0.11797598, -0.03823627, 0.012243152, 0.016690504, 0.001911252, -0.010213594, 0.04667876, -0.020034185, -0.0034070543, 0.00860644, -0.012421872, 0.010790465, -0.0016259728], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, -1, 15, 17, -1, 19, -1, 21, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6022315, 0.49450195, 0.0, 0.6848284, 2.7953146, 1.7645528, 2.0788584, 0.0, 5.727459, 0.0, 1.4801015, 1.5126765, 0.0, 2.4813488, 0.0, 0.6227474, 1.7115018, 1.2361989, 1.7544593, 0.0, 0.0, 0.0, 1.4927628, 2.4047275, 0.0, 0.0, 0.0, 0.0, 1.1175053, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 22, 22, 23, 23, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, -1, 16, 18, -1, 20, -1, 22, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0066760704, 1.0, 1.0, 1.0, 1.0, -0.010301479, 1.0, 0.010713307, 1.0, 1.0, -0.010966155, 1.0, 0.029052645, -0.26923078, 0.1923077, 1.0, -1.0, 0.009780566, -0.008987701, -0.002662068, 1.0, -0.34615386, 0.012243152, 0.016690504, 0.001911252, -0.010213594, 1.0, -0.020034185, -0.0034070543, 0.00860644, -0.012421872, 0.010790465, -0.0016259728], "split_indices": [43, 0, 0, 59, 5, 81, 64, 0, 61, 0, 58, 122, 0, 109, 0, 1, 1, 137, 0, 0, 0, 0, 12, 1, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1967.0, 109.0, 1480.0, 487.0, 743.0, 737.0, 110.0, 377.0, 94.0, 649.0, 634.0, 103.0, 282.0, 95.0, 330.0, 319.0, 235.0, 399.0, 145.0, 137.0, 114.0, 216.0, 225.0, 94.0, 140.0, 95.0, 109.0, 290.0, 109.0, 107.0, 92.0, 133.0, 147.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0028831444, -0.001919513, 0.051818345, -0.007455855, 0.007670545, 0.0016925838, 0.008864929, -0.0076643615, -0.0026669009, 0.0236099, -0.020686818, 0.06954059, -0.013856588, -0.005744687, -0.014886684, -0.0025571452, 0.013259831, 0.044974692, -0.00882078, -0.017651133, 0.009087114, 0.0003132643, 0.008601206, -0.0032596046, -0.010425958, -0.022060223, 0.0073808557, 0.010625642, -0.010409536, -0.0019243903, 0.009320618], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, 9, 11, 13, 15, 17, 19, -1, -1, -1, 21, -1, 23, -1, -1, -1, 25, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [0.48648947, 0.82053035, 0.23774761, 0.58348423, 0.0, 0.0, 0.0, 0.0, 0.7798641, 1.1529775, 1.8712308, 1.8052624, 1.6140714, 1.006557, 0.0, 0.0, 0.0, 0.35371977, 0.0, 0.9709671, 0.0, 0.0, 0.0, 0.9678845, 0.0, 1.4399064, 0.0, 0.9471912, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 23, 23, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, 10, 12, 14, 16, 18, 20, -1, -1, -1, 22, -1, 24, -1, -1, -1, 26, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.0, 5.0, -0.3846154, -0.53846157, 0.007670545, 0.0016925838, 0.008864929, -0.0076643615, -0.03846154, 1.0, 2.0, 1.0, 1.0, 1.0, -0.014886684, -0.0025571452, 0.013259831, 1.0, -0.00882078, 1.0, 0.009087114, 0.0003132643, 0.008601206, 1.0, -0.010425958, 1.0, 0.0073808557, 1.0, -0.010409536, -0.0019243903, 0.009320618], "split_indices": [125, 0, 1, 1, 0, 0, 0, 0, 1, 59, 0, 13, 93, 88, 0, 0, 0, 83, 0, 64, 0, 0, 0, 105, 0, 127, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1885.0, 185.0, 1761.0, 124.0, 95.0, 90.0, 114.0, 1647.0, 670.0, 977.0, 301.0, 369.0, 875.0, 102.0, 120.0, 181.0, 206.0, 163.0, 779.0, 96.0, 102.0, 104.0, 668.0, 111.0, 537.0, 131.0, 384.0, 153.0, 282.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.00022415382, 0.005023523, -0.010292121, -4.7176643e-05, 0.008624292, 0.0082445275, -0.012573199, -0.02966376, 0.028875582, 0.0074547795, -0.118129626, 0.0066835545, 0.11705189, -0.0548534, 0.011326115, -0.009292008, -0.014278509, 0.029610904, -0.057983313, 0.022365317, 0.0034792616, 0.00065189967, -0.011622582, -0.028136866, 0.14201282, -0.011137849, 0.0011116285, -0.092078306, 0.020334862, 0.037052743, -0.006697058, -0.0050069685, -0.012904586, 0.009419849, -0.004671056], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1085607, 0.8059696, 0.0, 1.9196248, 0.0, 1.3514479, 0.0, 1.9997883, 2.1896715, 2.8282263, 0.11187863, 1.32548, 1.973012, 1.016975, 0.0, 0.0, 0.0, 4.284034, 0.86336315, 0.0, 0.0, 0.0, 0.0, 1.3513179, 10.69729, 0.0, 0.0, 0.29195523, 1.2281502, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 23, 23, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.1923077, -0.010292121, 1.3461539, 0.008624292, 1.0, -0.012573199, 1.0, 1.0, 0.26923078, 0.115384616, 0.23076923, 1.0, -0.23076923, 0.011326115, -0.009292008, -0.014278509, -0.15384616, 0.65384614, 0.022365317, 0.0034792616, 0.00065189967, -0.011622582, 1.0, 1.0, -0.011137849, 0.0011116285, 1.0, 1.0, 0.037052743, -0.006697058, -0.0050069685, -0.012904586, 0.009419849, -0.004671056], "split_indices": [117, 1, 0, 1, 0, 17, 0, 61, 61, 1, 1, 1, 39, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 39, 69, 0, 0, 124, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1957.0, 100.0, 1842.0, 115.0, 1728.0, 114.0, 609.0, 1119.0, 429.0, 180.0, 894.0, 225.0, 270.0, 159.0, 89.0, 91.0, 660.0, 234.0, 98.0, 127.0, 135.0, 135.0, 436.0, 224.0, 132.0, 102.0, 188.0, 248.0, 107.0, 117.0, 88.0, 100.0, 118.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0050023287, 0.0011766999, 0.006238674, 0.0064437757, -0.010937201, 0.012913293, -0.008584039, 0.004476804, 0.010923493, 0.023698498, -0.06446574, 0.009151635, 0.04772568, -0.013941786, 0.0012239636, -0.0357474, 0.05440054, 0.10216861, 0.0095166275, 0.002748664, -0.06372084, 0.010950029, 0.013395331, 0.0011458489, 0.020384368, -0.0031143609, 0.0073357024, -0.0016449037, -0.01310086, -0.0074063973, 0.007293943], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.45311272, 1.1266891, 0.0, 1.1027217, 0.0, 1.4025756, 0.0, 2.1030803, 0.0, 0.43375432, 1.9892343, 1.5704519, 0.97353935, 0.0, 0.0, 0.6863229, 1.3307937, 1.78003, 0.7138357, 0.0, 0.8556385, 1.3122219, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.006238674, 3.0, -0.010937201, 1.0, -0.008584039, 1.0, 0.010923493, 1.0, 1.0, 1.0, 1.0, -0.013941786, 0.0012239636, 1.0, 1.0, 1.0, 1.0, 0.002748664, 1.0, 1.0, 0.013395331, 0.0011458489, 0.020384368, -0.0031143609, 0.0073357024, -0.0016449037, -0.01310086, -0.0074063973, 0.007293943], "split_indices": [0, 52, 0, 0, 0, 125, 0, 113, 0, 15, 109, 39, 106, 0, 0, 26, 50, 97, 93, 0, 71, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1935.0, 129.0, 1847.0, 88.0, 1726.0, 121.0, 1587.0, 139.0, 1241.0, 346.0, 773.0, 468.0, 175.0, 171.0, 388.0, 385.0, 193.0, 275.0, 119.0, 269.0, 249.0, 136.0, 102.0, 91.0, 168.0, 107.0, 158.0, 111.0, 105.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0025525496, 0.009446114, -0.045889426, 0.0018663981, 0.013792603, -0.012937027, 0.007191997, -0.042424455, 0.011814418, 0.0016230926, -0.009147546, 0.008327345, 0.0036085923, -0.005118939, 0.010324074, -0.023594055, 0.048615456, 0.015054854, -0.06446737, 0.011912532, -0.0018625716, -0.008744992, 0.010058559, 0.0015957969, -0.11927336, -0.04590134, 0.006182082, -0.008458603, -0.017513817, 0.0029528819, -0.011493914], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, 13, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [0.69158536, 1.7655739, 2.537386, 0.75431794, 0.0, 0.0, 0.0, 0.90341145, 0.81975955, 0.0, 0.0, 0.0, 1.0904068, 1.1446398, 0.0, 1.3553904, 1.3986442, 0.8977079, 1.8380489, 0.0, 0.0, 0.9045789, 0.0, 0.0, 0.48057365, 1.1769027, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, 14, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, -1.0, 0.013792603, -0.012937027, 0.007191997, 1.0, 0.0, 0.0016230926, -0.009147546, 0.008327345, 1.0, 1.0, 0.010324074, 1.0, 1.0, 1.0, 1.0, 0.011912532, -0.0018625716, 1.0, 0.010058559, 0.0015957969, 1.0, 1.0, 0.006182082, -0.008458603, -0.017513817, 0.0029528819, -0.011493914], "split_indices": [0, 102, 106, 0, 0, 0, 0, 127, 0, 0, 0, 0, 125, 105, 0, 13, 109, 116, 17, 0, 0, 109, 0, 0, 71, 108, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1813.0, 258.0, 1712.0, 101.0, 151.0, 107.0, 314.0, 1398.0, 143.0, 171.0, 144.0, 1254.0, 1153.0, 101.0, 858.0, 295.0, 441.0, 417.0, 144.0, 151.0, 345.0, 96.0, 169.0, 248.0, 226.0, 119.0, 153.0, 95.0, 108.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047442713, -0.00810015, 0.006227158, -0.0071457117, -0.002860881, -0.013212638, 0.03190026, -0.021381844, 0.009355887, -0.024352022, 0.017371276, -0.009890133, -0.010555679, 0.0074602137, -0.067437336, 0.0118648335, -0.11448978, -0.015379793, 0.0023156581, 0.047051758, -0.012766009, -0.01863243, -0.0031723888, 0.12782645, -0.0068842354, 0.027467217, -0.059523553, 0.023351552, 0.0062787007, 0.008508596, -0.002197107, -0.013368315, 0.00015797526], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, -1, 13, -1, 15, -1, -1, 17, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46688616, 0.65625346, 0.0, 0.0, 0.65706575, 1.2272393, 3.3424797, 1.2642794, 0.0, 1.2790413, 0.0, 2.6168966, 0.0, 0.0, 1.6351628, 0.82508296, 1.1771979, 0.0, 0.0, 3.669634, 1.053476, 0.0, 0.0, 1.5878837, 0.0, 0.8574204, 1.1736319, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, -1, 14, -1, 16, -1, -1, 18, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 0.006227158, -0.0071457117, 1.0, 5.0, 1.0, 2.0, 0.009355887, -0.15384616, 0.017371276, 1.0, -0.010555679, 0.0074602137, 0.7692308, 1.0, 1.0, -0.015379793, 0.0023156581, 1.0, 1.0, -0.01863243, -0.0031723888, 0.1923077, -0.0068842354, 1.0, 1.0, 0.023351552, 0.0062787007, 0.008508596, -0.002197107, -0.013368315, 0.00015797526], "split_indices": [114, 1, 0, 0, 61, 0, 0, 0, 0, 1, 0, 113, 0, 0, 1, 122, 137, 0, 0, 105, 12, 0, 0, 1, 0, 69, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1977.0, 99.0, 151.0, 1826.0, 1407.0, 419.0, 1307.0, 100.0, 300.0, 119.0, 1150.0, 157.0, 91.0, 209.0, 952.0, 198.0, 107.0, 102.0, 392.0, 560.0, 106.0, 92.0, 231.0, 161.0, 301.0, 259.0, 88.0, 143.0, 139.0, 162.0, 117.0, 142.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023402683, -0.022163345, 0.010235828, 0.010035955, -0.08098681, -0.0039156512, 0.08902715, -0.020853266, 0.060211357, -0.01682013, 0.0014626108, -0.047867008, 0.025316788, 0.016379997, 0.0002492273, 0.061824296, -0.014486963, 0.012343193, -0.0010486898, -0.1358983, 0.07527787, 0.01930497, -0.021889476, -0.004453784, 0.017987462, -0.0067548864, -0.02195043, 0.01033361, 0.004658194, 0.03067698, -0.082839385, 0.009761925, -0.0033822116, -0.016697867, -0.0015658379], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, 23, -1, -1, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.513801, 1.5152599, 1.4060326, 0.8012876, 2.3598902, 1.3734571, 1.2423276, 3.281079, 0.88050854, 0.0, 0.0, 4.6289377, 5.083385, 0.0, 0.0, 2.4107685, 0.0, 0.0, 0.0, 1.4228902, 0.14331758, 0.0, 1.6051645, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1614656, 1.3113939, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 19, 19, 20, 20, 22, 22, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, 24, -1, -1, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [-0.1923077, 1.0, 1.0, -0.3846154, 1.0, 1.0, 1.0, 1.0, 1.0, -0.01682013, 0.0014626108, 0.3846154, 0.115384616, 0.016379997, 0.0002492273, 1.0, -0.014486963, 0.012343193, -0.0010486898, 1.0, 1.0, 0.01930497, 1.0, -0.004453784, 0.017987462, -0.0067548864, -0.02195043, 0.01033361, 0.004658194, 1.0, 1.0, 0.009761925, -0.0033822116, -0.016697867, -0.0015658379], "split_indices": [1, 126, 62, 1, 97, 124, 74, 97, 122, 0, 0, 1, 1, 0, 0, 69, 0, 0, 0, 39, 126, 0, 17, 0, 0, 0, 0, 0, 0, 12, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 800.0, 1261.0, 517.0, 283.0, 1069.0, 192.0, 320.0, 197.0, 148.0, 135.0, 427.0, 642.0, 103.0, 89.0, 192.0, 128.0, 104.0, 93.0, 249.0, 178.0, 141.0, 501.0, 101.0, 91.0, 137.0, 112.0, 90.0, 88.0, 269.0, 232.0, 132.0, 137.0, 103.0, 129.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002619931, -0.013667643, 0.019050105, 0.0025223282, -0.010047769, -0.044883944, 0.040251035, -0.02591167, 0.044488665, -0.020645667, 0.0050453977, 0.10427146, 0.003816644, -0.055352483, 0.0052666254, 0.08714817, -0.004266909, 0.21433786, -0.012809092, 0.07113708, -0.055765796, -0.010525199, -0.014764359, 0.00032108903, 0.016494367, 0.03582607, 0.0084807305, 0.015673503, -0.0067959563, 0.0025799552, -0.012846537, 0.005551498, -0.0076812813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, 17, 19, 21, -1, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.55260986, 1.4574543, 1.3934144, 1.0429186, 0.0, 3.9434266, 1.8007245, 1.2052805, 1.3124919, 0.0, 0.0, 7.1610823, 1.973469, 0.7675986, 0.0, 1.5475959, 0.0, 3.5420551, 0.0, 2.750376, 1.5476692, 0.0, 0.9113914, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, 18, 20, 22, -1, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.0, -0.42307693, 1.0, -0.010047769, 1.0, 1.0, 1.2692307, 0.5, -0.020645667, 0.0050453977, 0.23076923, 1.0, 1.0, 0.0052666254, -0.15384616, -0.004266909, 1.0, -0.012809092, 1.0, 1.0, -0.010525199, 1.0, 0.00032108903, 0.016494367, 0.03582607, 0.0084807305, 0.015673503, -0.0067959563, 0.0025799552, -0.012846537, 0.005551498, -0.0076812813], "split_indices": [71, 0, 1, 83, 0, 89, 69, 1, 1, 0, 0, 1, 105, 17, 0, 1, 0, 109, 0, 113, 17, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1037.0, 1028.0, 874.0, 163.0, 256.0, 772.0, 521.0, 353.0, 95.0, 161.0, 280.0, 492.0, 379.0, 142.0, 237.0, 116.0, 190.0, 90.0, 231.0, 261.0, 170.0, 209.0, 114.0, 123.0, 90.0, 100.0, 143.0, 88.0, 123.0, 138.0, 98.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-9.556356e-05, -0.016111521, 0.011814778, 0.005861094, -0.021361541, -0.0057071624, 0.020140221, -0.027352555, 0.076080516, 0.058044527, -0.018855508, 0.05266072, -0.08775474, 0.02728282, -0.00337602, 0.012333372, -0.0003808359, -0.0063100373, -0.007577475, -0.0024350472, 0.016490048, -0.016375381, -0.0015628216, 0.0062679932, -0.028031394, 0.027755411, -0.06617479, -0.0063484656, 0.009107876, -0.0154246045, 0.006411391, 0.0013463356, -4.872376e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, 23, -1, -1, -1, -1, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [0.39314717, 3.8145766, 3.9265332, 1.8448045, 0.0, 0.90696394, 0.0, 2.5953088, 5.489171, 0.74709046, 0.6405283, 1.9966991, 1.6773539, 0.0, 0.0, 0.0, 0.0, 1.1014385, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1894953, 1.3115213, 2.1223955, 0.0, 0.0, 0.0, 0.00885414, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 17, 17, 24, 24, 25, 25, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, 24, -1, -1, -1, -1, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.021361541, 1.0, 0.020140221, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.02728282, -0.00337602, 0.012333372, -0.0003808359, 0.0, -0.007577475, -0.0024350472, 0.016490048, -0.016375381, -0.0015628216, 0.0062679932, 1.0, 1.0, 1.0, -0.0063484656, 0.009107876, -0.0154246045, 1.0, 0.0013463356, -4.872376e-05], "split_indices": [16, 125, 125, 121, 0, 26, 0, 108, 69, 97, 64, 81, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 108, 39, 59, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 879.0, 1182.0, 791.0, 88.0, 1082.0, 100.0, 537.0, 254.0, 185.0, 897.0, 231.0, 306.0, 91.0, 163.0, 90.0, 95.0, 735.0, 162.0, 137.0, 94.0, 149.0, 157.0, 176.0, 559.0, 227.0, 332.0, 93.0, 134.0, 150.0, 182.0, 90.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0069701956, -0.011456275, 0.008582079, -0.01800826, 0.05391715, -0.04478488, 0.009374847, 0.002763352, 0.008109678, -0.0149147315, -0.022651633, -0.07036744, 0.029366897, 0.010415556, -0.052773613, -7.667187e-05, -0.014075904, -0.029066209, 0.06598857, -0.017760767, -0.123679005, -0.012922634, 0.0058660232, 0.03109527, 0.02012491, -0.011291341, 0.048886217, -0.01757885, -0.007702858, -0.0012621034, 0.007557855, 0.015784496, -0.0025298444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, -1, -1, 21, 23, 25, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.85751134, 0.84165996, 0.0, 1.3095436, 0.1278736, 2.0858207, 1.407689, 0.0, 0.0, 0.0, 2.845664, 0.8671768, 1.5107825, 0.0, 1.4945247, 0.0, 0.0, 2.3899806, 2.0483432, 2.5556798, 0.48375416, 0.0, 0.0, 0.6709025, 0.0, 0.0, 1.9156876, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 10, 10, 11, 11, 12, 12, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, -1, -1, 22, 24, 26, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.008582079, 1.0, -0.3846154, -0.42307693, -1.0, 0.002763352, 0.008109678, -0.0149147315, -0.23076923, -0.15384616, 1.0, 0.010415556, 1.0, -7.667187e-05, -0.014075904, 0.07692308, 2.0, 1.0, 0.1923077, -0.012922634, 0.0058660232, 1.0, 0.02012491, -0.011291341, 1.0, -0.01757885, -0.007702858, -0.0012621034, 0.007557855, 0.015784496, -0.0025298444], "split_indices": [114, 125, 0, 122, 1, 1, 0, 0, 0, 0, 1, 1, 81, 0, 105, 0, 0, 1, 0, 59, 1, 0, 0, 39, 0, 0, 53, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1965.0, 95.0, 1786.0, 179.0, 903.0, 883.0, 91.0, 88.0, 158.0, 745.0, 177.0, 706.0, 143.0, 602.0, 89.0, 88.0, 272.0, 434.0, 403.0, 199.0, 127.0, 145.0, 345.0, 89.0, 166.0, 237.0, 94.0, 105.0, 174.0, 171.0, 96.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00027497043, 0.009355128, -0.0045072143, -0.06142121, 0.0014618909, -0.01105022, -0.0009643435, -0.005199835, 0.009546, -0.012823727, 0.0066512376, 0.038420927, -0.029529035, -0.008334635, 0.010046764, -0.020704797, -0.010416736, 0.0049139797, -0.0052768677, -0.038098406, 0.02495342, -0.071600325, 0.02202669, 0.013450018, -0.008776858, -0.01420061, -0.039755236, -0.0039346456, 0.008293491, 0.0053488575, -0.008680884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, -1, 3, 5, 7, -1, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [0.9238, 0.0, 0.66925967, 0.47522354, 1.1164964, 0.0, 0.0, 0.9102989, 0.0, 1.2883669, 0.0, 1.0733811, 0.7475405, 0.5388562, 0.0, 0.8060737, 0.0, 0.0, 0.0, 1.480515, 3.4575326, 1.0582604, 0.9831281, 0.0, 0.0, 0.0, 1.4259238, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 26, 26], "right_children": [2, -1, 4, 6, 8, -1, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009355128, -0.46153846, 1.0, 1.0, -0.01105022, -0.0009643435, 1.0, 0.009546, 0.0, 0.0066512376, 1.0, 1.0, 1.0, 0.010046764, 1.0, -0.010416736, 0.0049139797, -0.0052768677, 1.0, 1.0, 1.0, 1.0, 0.013450018, -0.008776858, -0.01420061, 1.0, -0.0039346456, 0.008293491, 0.0053488575, -0.008680884], "split_indices": [1, 0, 1, 124, 90, 0, 0, 119, 0, 0, 0, 39, 40, 108, 0, 121, 0, 0, 0, 109, 109, 59, 12, 0, 0, 0, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 101.0, 1970.0, 187.0, 1783.0, 96.0, 91.0, 1665.0, 118.0, 1505.0, 160.0, 370.0, 1135.0, 211.0, 159.0, 1015.0, 120.0, 92.0, 119.0, 735.0, 280.0, 472.0, 263.0, 142.0, 138.0, 147.0, 325.0, 131.0, 132.0, 109.0, 216.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0012266259, 0.010909663, -0.0042135813, -0.0889525, 0.0046034586, -0.016688982, -0.0006685297, -0.019621715, 0.032587208, -0.03139096, 0.0068752686, 0.015220808, 0.010532946, -0.016374888, -0.010788586, -0.019947005, 0.012114798, -0.03465283, 0.0070670033, -0.06409373, 0.033575997, -0.05630175, 0.005965678, 0.00848072, -0.015804315, 0.012696191, -0.005980988, -0.08650222, 0.0018506721, -0.0057836557, 0.008225099, -0.016906885, -0.0020448891], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, 5, 7, -1, -1, 9, 11, 13, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.2100544, 0.0, 1.4666486, 1.186161, 1.2053261, 0.0, 0.0, 0.9912152, 1.0421984, 0.9660167, 0.0, 2.4810014, 0.0, 1.1184741, 0.0, 1.1814327, 0.0, 0.5109007, 0.0, 3.8330288, 1.9709291, 0.85625625, 0.98316914, 0.0, 0.0, 0.0, 0.0, 1.4725258, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 27, 27], "right_children": [2, -1, 4, 6, 8, -1, -1, 10, 12, 14, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.010909663, -0.46153846, 1.0, 1.0, -0.016688982, -0.0006685297, 3.1923077, 1.0, 2.0, 0.0068752686, 1.0, 0.010532946, 1.0, -0.010788586, 1.0, 0.012114798, 1.0, 0.0070670033, 1.0, 1.0, 1.1538461, 1.0, 0.00848072, -0.015804315, 0.012696191, -0.005980988, 1.0, 0.0018506721, -0.0057836557, 0.008225099, -0.016906885, -0.0020448891], "split_indices": [1, 0, 1, 124, 71, 0, 0, 1, 42, 0, 0, 116, 0, 73, 0, 111, 0, 15, 0, 69, 13, 1, 83, 0, 0, 0, 0, 108, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 99.0, 1963.0, 185.0, 1778.0, 95.0, 90.0, 953.0, 825.0, 841.0, 112.0, 666.0, 159.0, 703.0, 138.0, 500.0, 166.0, 581.0, 122.0, 274.0, 226.0, 379.0, 202.0, 106.0, 168.0, 113.0, 113.0, 270.0, 109.0, 110.0, 92.0, 120.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0003759088, 0.020604448, -0.017522153, 0.045248736, -0.043359928, -0.072735004, 0.0014978865, 0.0004006039, 0.14769801, -0.010155601, 0.0046428293, -0.0016313037, -0.014812231, 0.07101261, -0.031731676, 0.056775015, -0.06786931, 0.0055545527, 0.02623984, 0.0016846051, 0.016411135, 0.006308558, -0.052731477, 0.017188508, -0.0061876876, 0.0044262256, -0.020265376, -0.0054098694, -0.10859404, -0.005507443, 0.006161315, -0.022585314, -0.0020399136], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.74039567, 1.5133023, 1.1394137, 3.184098, 1.3951615, 1.182473, 1.8641247, 1.8550619, 2.2302532, 0.0, 0.0, 0.0, 0.0, 1.3161803, 1.0871644, 3.6057193, 3.2947633, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1816468, 0.0, 0.0, 0.0, 0.0, 0.8055379, 2.1200387, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 13, 13, 14, 14, 15, 15, 16, 16, 22, 22, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.1923077, 1.0, 1.0, 1.0, 0.1923077, 1.0, -0.34615386, -0.010155601, 0.0046428293, -0.0016313037, -0.014812231, 0.03846154, 1.0, 1.0, 1.0, 0.0055545527, 0.02623984, 0.0016846051, 0.016411135, 0.006308558, 1.0, 0.017188508, -0.0061876876, 0.0044262256, -0.020265376, 1.0, 1.0, -0.005507443, 0.006161315, -0.022585314, -0.0020399136], "split_indices": [126, 23, 1, 61, 121, 93, 1, 15, 1, 0, 0, 0, 0, 1, 26, 69, 108, 0, 0, 0, 0, 0, 127, 0, 0, 0, 0, 106, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 960.0, 1085.0, 693.0, 267.0, 278.0, 807.0, 482.0, 211.0, 162.0, 105.0, 159.0, 119.0, 261.0, 546.0, 264.0, 218.0, 117.0, 94.0, 165.0, 96.0, 99.0, 447.0, 134.0, 130.0, 119.0, 99.0, 242.0, 205.0, 139.0, 103.0, 88.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018674041, -0.029444331, 0.005901945, -0.07949506, 0.010871219, -0.007941574, 0.04837401, 0.000709695, -0.015357936, 0.00862148, -0.05036625, -0.010229784, 0.09254492, 0.014032978, -0.0060870573, -0.019817095, 0.0024211311, 0.0016541623, 0.015968893, 0.0073959096, -0.022635655, 0.0077170543, -0.009724473, 0.038515948, -0.07333839, 0.014831466, -0.013378953, -0.017420609, -0.03780544, 0.00570533, -0.010201284, 0.0016291653, -0.009528363], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, 13, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1, 23, -1, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.44350743, 3.1462498, 0.94956005, 2.1426468, 0.0, 0.85586697, 2.6421592, 0.0, 0.0, 1.6970196, 2.6683607, 0.0, 1.5666709, 0.0, 1.0438254, 0.0, 1.8773911, 0.0, 0.0, 0.0, 2.0246618, 0.0, 0.0, 1.6866064, 1.2795324, 0.0, 1.2547797, 0.0, 0.82088256, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 20, 20, 23, 23, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, 14, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1, 24, -1, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 0.07692308, 1.0, 1.0, 0.010871219, 1.0, 1.0, 0.000709695, -0.015357936, -0.42307693, 1.0, -0.010229784, 1.0, 0.014032978, 1.0, -0.019817095, 1.0, 0.0016541623, 0.015968893, 0.0073959096, 1.0, 0.0077170543, -0.009724473, 1.0, 1.0, 0.014831466, 0.7307692, -0.017420609, 1.0, 0.00570533, -0.010201284, 0.0016291653, -0.009528363], "split_indices": [5, 1, 0, 69, 0, 15, 89, 0, 0, 1, 89, 0, 71, 0, 89, 0, 97, 0, 0, 0, 97, 0, 0, 69, 137, 0, 1, 0, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 455.0, 1615.0, 334.0, 121.0, 1218.0, 397.0, 154.0, 180.0, 876.0, 342.0, 90.0, 307.0, 88.0, 788.0, 90.0, 252.0, 144.0, 163.0, 135.0, 653.0, 144.0, 108.0, 296.0, 357.0, 95.0, 201.0, 93.0, 264.0, 112.0, 89.0, 136.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-4.8831037E-9", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}