{"learner": {"attributes": {"best_iteration": "33", "best_score": "0.634515"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "84"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.001560326, 0.14367408, -0.23029998, 0.100632094, 0.045041632, -0.26433238, -0.002084595, -0.09786503, 0.17077412, -0.23070349, -0.046067074, 0.00033781628, -0.018382626, 0.1097593, 0.36744618, -0.18419524, -0.0361049, 0.2278665, 0.02126024, 0.0524216, 0.021225985, -0.23657933, -0.13421406, 0.038130973, 0.0076663303, -0.007387854, 0.09458673, -0.018339107, -0.027558401, -0.01977022, -0.0043919785, 0.020621575, -0.00096705434], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [68.24056, 16.952385, 5.609913, 15.677286, 0.0, 4.469986, 0.0, 2.5586796, 9.983923, 3.5039177, 0.0, 0.0, 0.0, 6.637258, 4.792721, 1.1153603, 0.0, 6.3107014, 2.5323584, 0.0, 0.0, 0.4315138, 1.2497096, 0.0, 0.0, 0.0, 2.3858187, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.045041632, 1.0, -0.002084595, 1.0, 2.0, 1.0, -0.046067074, 0.00033781628, -0.018382626, 1.0, 1.0, 1.0, -0.0361049, 1.0, 1.0, 0.0524216, 0.021225985, 1.0, 1.0, 0.038130973, 0.0076663303, -0.007387854, 1.0, -0.018339107, -0.027558401, -0.01977022, -0.0043919785, 0.020621575, -0.00096705434], "split_indices": [137, 125, 71, 17, 0, 40, 0, 53, 0, 116, 0, 0, 0, 53, 69, 111, 0, 126, 111, 0, 0, 97, 81, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1284.0, 787.0, 1126.0, 158.0, 677.0, 110.0, 294.0, 832.0, 578.0, 99.0, 135.0, 159.0, 635.0, 197.0, 426.0, 152.0, 272.0, 363.0, 98.0, 99.0, 208.0, 218.0, 135.0, 137.0, 158.0, 205.0, 88.0, 120.0, 128.0, 90.0, 99.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.006054414, -0.21016367, 0.14371039, -0.24255915, -0.0013497287, 0.10452554, 0.04259438, -0.27901253, -0.0051676026, -0.08620015, 0.17168906, -0.21720356, -0.37000164, -0.01696665, -0.0011025129, 0.23693727, -0.008119368, -0.013307884, -0.27452382, -0.03338507, -0.041980144, 0.15655166, 0.051263012, -0.012108601, 0.015434948, -0.018205442, -0.0344673, 0.28068075, 0.0030393384, 0.01778253, 0.044113524, -0.013610098, 0.011219255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [61.134666, 5.0905075, 13.879391, 4.7734146, 0.0, 14.116411, 0.0, 3.2393951, 0.0, 1.8008053, 9.561726, 1.6539612, 0.41946793, 0.0, 0.0, 13.252716, 3.9827223, 0.0, 1.3232765, 0.0, 0.0, 8.822625, 0.0, 0.0, 0.0, 0.0, 0.0, 4.224924, 3.1438358, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 21, 21, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, -0.0013497287, 1.0, 0.04259438, 1.0, -0.0051676026, 1.0, 0.15384616, 1.0, 1.0, -0.01696665, -0.0011025129, 2.0, 0.7307692, -0.013307884, 1.0, -0.03338507, -0.041980144, 1.0, 0.051263012, -0.012108601, 0.015434948, -0.018205442, -0.0344673, 1.0, 1.0, 0.01778253, 0.044113524, -0.013610098, 0.011219255], "split_indices": [2, 71, 125, 1, 0, 17, 0, 23, 0, 105, 1, 122, 116, 0, 0, 0, 1, 0, 69, 0, 0, 122, 0, 0, 0, 0, 0, 12, 71, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 799.0, 1255.0, 686.0, 113.0, 1102.0, 153.0, 576.0, 110.0, 287.0, 815.0, 343.0, 233.0, 136.0, 151.0, 598.0, 217.0, 139.0, 204.0, 135.0, 98.0, 463.0, 135.0, 128.0, 89.0, 88.0, 116.0, 256.0, 207.0, 156.0, 100.0, 91.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00029963918, -0.1486571, 0.15069976, -0.21593219, -0.011557127, 0.0840017, 0.33521482, -0.18736526, -0.038906507, 0.054388296, -0.016736938, 0.046775322, 0.028192192, 0.012397445, 0.04949992, -0.15060091, -0.029383862, -0.0014008497, 0.016134927, -0.0092393, 0.08554165, -0.19544898, -0.0073484094, -0.018119846, 0.14982525, -0.15025489, -0.028583711, 0.008738928, -0.0108263595, 0.06965971, 0.027714698, -0.0063175405, -0.024711857, 0.012959309, 0.00023188074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, 21, -1, -1, -1, -1, 23, 25, -1, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [46.464054, 9.6108, 12.700615, 3.4571648, 3.5243616, 5.5848317, 9.248301, 2.348652, 0.0, 1.7631054, 0.0, 3.4420385, 0.0, 0.0, 0.0, 1.5425072, 0.0, 0.0, 0.0, 0.0, 3.3252037, 1.1519718, 0.0, 1.8165988, 3.1436982, 1.5857491, 0.0, 0.0, 0.0, 0.7627974, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 20, 20, 21, 21, 23, 23, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, 22, -1, -1, -1, -1, 24, 26, -1, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.038906507, 1.0, -0.016736938, 1.0, 0.028192192, 0.012397445, 0.04949992, 1.0, -0.029383862, -0.0014008497, 0.016134927, -0.0092393, 1.0, 1.0, -0.0073484094, 1.0, 1.0, 1.0, -0.028583711, 0.008738928, -0.0108263595, 1.0, 0.027714698, -0.0063175405, -0.024711857, 0.012959309, 0.00023188074], "split_indices": [71, 2, 113, 40, 7, 125, 109, 116, 0, 124, 0, 5, 0, 0, 0, 50, 0, 0, 0, 0, 17, 23, 0, 53, 15, 13, 0, 0, 0, 115, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1042.0, 1032.0, 699.0, 343.0, 758.0, 274.0, 600.0, 99.0, 241.0, 102.0, 638.0, 120.0, 118.0, 156.0, 446.0, 154.0, 147.0, 94.0, 139.0, 499.0, 282.0, 164.0, 191.0, 308.0, 188.0, 94.0, 88.0, 103.0, 189.0, 119.0, 99.0, 89.0, 100.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002589833, 0.11159078, -0.17444992, -0.033704035, 0.167437, -0.21869719, -0.060093943, -0.0919662, 0.012287549, 0.10216282, 0.38048464, -0.2512495, -0.0041878927, -0.018302046, 0.0073369103, 0.0014504482, -0.023075836, 0.15878768, -0.006803346, 0.01757261, 0.05473886, -0.016230445, -0.29642347, 0.00531537, 0.046142127, -0.025397215, -0.32710838, -0.0133685125, 0.06792304, -0.03107564, -0.034494687, -0.0053567193, 0.016421532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, -1, -1, -1, -1, -1, 23, -1, -1, -1, -1, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [39.75285, 10.345561, 3.9720516, 3.229422, 12.807894, 3.2578068, 3.5929465, 3.8125427, 0.0, 6.7943244, 7.3817997, 1.9206028, 0.0, 0.0, 0.0, 0.0, 0.0, 24.569866, 0.0, 0.0, 0.0, 0.0, 0.41292763, 3.0545764, 0.0, 0.0, 0.053668976, 0.0, 2.8310547, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 17, 17, 22, 22, 23, 23, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, -1, -1, -1, -1, -1, 24, -1, -1, -1, -1, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 2.7307692, 1.0, 1.0, 0.012287549, 0.23076923, 1.0, 0.0, -0.0041878927, -0.018302046, 0.0073369103, 0.0014504482, -0.023075836, -0.15384616, -0.006803346, 0.01757261, 0.05473886, -0.016230445, 1.0, 1.0, 0.046142127, -0.025397215, 1.0, -0.0133685125, 1.0, -0.03107564, -0.034494687, -0.0053567193, 0.016421532], "split_indices": [137, 17, 93, 39, 61, 1, 13, 126, 0, 1, 50, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 122, 5, 0, 0, 111, 0, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1275.0, 785.0, 354.0, 921.0, 566.0, 219.0, 258.0, 96.0, 705.0, 216.0, 478.0, 88.0, 114.0, 105.0, 146.0, 112.0, 529.0, 176.0, 97.0, 119.0, 161.0, 317.0, 351.0, 178.0, 133.0, 184.0, 109.0, 242.0, 96.0, 88.0, 107.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020200077, 0.09273943, -0.1559891, 0.048937, 0.2516616, -0.1961767, -0.05398825, 0.009255504, 0.032576267, 0.008907023, 0.034496687, -0.2250124, -0.0043667792, -0.016159238, 0.0066938265, -0.13598768, 0.062564045, -0.19741859, -0.034499902, -0.003487032, -0.020411639, 0.003316282, 0.17188403, -0.007924112, -0.2518248, 0.056103617, -0.015353749, 0.030058084, 0.004984393, -0.019644398, -0.03129195, 0.01732743, -0.0065661967], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, -1, -1, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [30.21594, 8.924231, 3.2342434, 11.03978, 4.202263, 2.4890995, 2.9017193, 6.8058357, 0.0, 0.0, 0.0, 1.5759811, 0.0, 0.0, 0.0, 1.625802, 4.1646876, 2.4882488, 0.0, 0.0, 0.0, 3.4527152, 3.5495944, 0.0, 0.8966198, 4.4514155, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, -1, -1, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.0, 1.0, 1.0, 1.0, 2.7307692, 1.0, 1.0, 0.032576267, 0.008907023, 0.034496687, 1.0, -0.0043667792, -0.016159238, 0.0066938265, -0.115384616, 1.0, 0.0, -0.034499902, -0.003487032, -0.020411639, 0.1923077, 1.0, -0.007924112, 1.0, 1.0, -0.015353749, 0.030058084, 0.004984393, -0.019644398, -0.03129195, 0.01732743, -0.0065661967], "split_indices": [137, 0, 93, 125, 15, 1, 13, 17, 0, 0, 0, 0, 0, 0, 0, 1, 50, 1, 0, 0, 0, 1, 126, 0, 23, 69, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1282.0, 789.0, 1005.0, 277.0, 566.0, 223.0, 879.0, 126.0, 101.0, 176.0, 476.0, 90.0, 118.0, 105.0, 236.0, 643.0, 387.0, 89.0, 95.0, 141.0, 417.0, 226.0, 122.0, 265.0, 312.0, 105.0, 110.0, 116.0, 139.0, 126.0, 159.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0031354208, -0.112665944, 0.12005998, -0.15939766, -0.018389756, 0.0283293, 0.2186197, -0.20484647, -0.11208293, 0.03925048, -0.015195995, -0.11261561, 0.21180071, 0.11010582, 0.36570483, -0.28249845, -0.0119383335, -0.019345108, -0.077840485, -0.0010821606, 0.011755473, -0.18096262, 0.0019433445, 0.029870642, 0.007955286, -0.00047720104, 0.02645318, 0.043756723, 0.028124798, -0.025510905, -0.031171372, -0.011717795, -0.003135072, -0.031049345, -0.00718209], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, 23, 25, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [28.054928, 4.586322, 9.32122, 1.4966755, 2.656161, 13.808899, 7.9325066, 2.355914, 0.9501085, 0.94492686, 0.0, 2.7255986, 2.666397, 5.073676, 1.280613, 0.14883327, 0.0, 0.0, 0.43890905, 0.0, 0.0, 2.8133044, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 21, 21], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, 24, 26, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.015195995, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0119383335, -0.019345108, 1.0, -0.0010821606, 0.011755473, 1.0, 0.0019433445, 0.029870642, 0.007955286, -0.00047720104, 0.02645318, 0.043756723, 0.028124798, -0.025510905, -0.031171372, -0.011717795, -0.003135072, -0.031049345, -0.00718209], "split_indices": [71, 2, 39, 111, 7, 42, 50, 17, 124, 124, 0, 116, 12, 106, 126, 109, 0, 0, 115, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1041.0, 1031.0, 696.0, 345.0, 534.0, 497.0, 355.0, 341.0, 241.0, 104.0, 302.0, 232.0, 286.0, 211.0, 186.0, 169.0, 101.0, 240.0, 147.0, 94.0, 199.0, 103.0, 140.0, 92.0, 164.0, 122.0, 114.0, 97.0, 96.0, 90.0, 130.0, 110.0, 91.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.004841078, -0.117020026, 0.07186952, -0.14102066, 0.0033662168, -0.015059873, 0.15250169, -0.11932881, -0.028078768, 0.08973998, -0.1729286, 0.07327363, 0.28289783, -0.0027264943, -0.1496903, 0.19144364, -0.00942952, -0.008913143, -0.031290796, -0.02194479, 0.021610128, 0.03597006, 0.019512327, -0.11715659, -0.027643615, -0.0025147775, 0.03406511, -0.0158931, 0.009080538, -0.16412261, -0.0005876719, -0.02112096, -0.009438053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, 19, 21, -1, 23, 25, -1, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [17.735554, 3.0269833, 8.57939, 2.188962, 0.0, 9.7447815, 6.5602064, 1.7469969, 0.0, 6.625837, 2.7565188, 5.3719306, 1.6179161, 0.0, 1.9380503, 7.368289, 0.0, 0.0, 0.0, 3.660517, 0.0, 0.0, 0.0, 1.9546623, 0.0, 0.0, 0.0, 0.0, 0.0, 0.8636761, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 19, 19, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, 20, 22, -1, 24, 26, -1, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 2.7307692, 1.0, 1.0, 0.0033662168, -0.03846154, 1.0, 0.0, -0.028078768, 1.0, 1.0, 1.0, 1.0, -0.0027264943, 0.96153843, -0.5, -0.00942952, -0.008913143, -0.031290796, -0.1923077, 0.021610128, 0.03597006, 0.019512327, 0.34615386, -0.027643615, -0.0025147775, 0.03406511, -0.0158931, 0.009080538, 1.0, -0.0005876719, -0.02112096, -0.009438053], "split_indices": [127, 1, 39, 40, 0, 1, 50, 0, 0, 122, 124, 97, 121, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 837.0, 1224.0, 722.0, 115.0, 589.0, 635.0, 625.0, 97.0, 354.0, 235.0, 395.0, 240.0, 155.0, 470.0, 228.0, 126.0, 147.0, 88.0, 237.0, 158.0, 128.0, 112.0, 374.0, 96.0, 93.0, 135.0, 107.0, 130.0, 263.0, 111.0, 157.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009956755, -0.10354102, 0.102346905, -0.16585805, -0.059570607, 0.068289205, 0.031861328, -0.21208644, -0.00011078346, 0.016881466, -0.11259726, 0.12320745, -0.047499944, -0.26653156, -0.01092978, 0.01509555, -0.01140003, -0.16073051, -0.0049247667, 0.07337846, 0.035683956, -0.018450363, 0.0048728813, -0.021184068, -0.032700703, -0.025125613, -0.008926287, 0.005395608, 0.020414544, -0.014098577, 0.06988081, 0.018628776, -0.0041508568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, 21, 23, -1, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [21.89402, 2.8414888, 7.579134, 3.2673225, 2.464831, 5.653095, 0.0, 1.874773, 0.0, 4.3694134, 1.0946693, 7.0199146, 3.770537, 0.72432995, 0.0, 0.0, 0.0, 1.319809, 0.0, 4.4182854, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0866947, 0.0, 0.0, 2.9433954, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 19, 19, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, 22, 24, -1, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.3846154, 1.0, 0.115384616, 0.031861328, 1.0, -0.00011078346, 0.115384616, 1.0, -0.07692308, 1.0, 1.0, -0.01092978, 0.01509555, -0.01140003, 1.0, -0.0049247667, 1.0, 0.035683956, -0.018450363, 0.0048728813, -0.021184068, -0.032700703, -0.025125613, -0.008926287, 1.0, 0.020414544, -0.014098577, 1.0, 0.018628776, -0.0041508568], "split_indices": [71, 17, 125, 1, 122, 1, 0, 83, 0, 1, 2, 1, 93, 109, 0, 0, 0, 12, 0, 61, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 108, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1037.0, 1029.0, 429.0, 608.0, 889.0, 140.0, 335.0, 94.0, 249.0, 359.0, 603.0, 286.0, 219.0, 116.0, 123.0, 126.0, 204.0, 155.0, 497.0, 106.0, 118.0, 168.0, 115.0, 104.0, 90.0, 114.0, 327.0, 170.0, 100.0, 227.0, 111.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0027581113, -0.084007576, 0.089104645, -0.15497442, -0.033754107, 0.046474576, 0.20988983, -0.24547625, -0.08578911, 0.03914055, -0.09736715, 0.0018743472, 0.17162146, 0.004000859, 0.033593073, -0.027435979, -0.021627871, 0.00089472247, -0.017016368, -0.006084992, 0.08473207, -0.18220498, 0.000490308, -0.08223721, 0.10022017, 0.0075322664, 0.02688832, 0.0034151822, 0.012985756, -0.00906901, -0.027371982, 0.010912505, -0.021745455, 0.024001421, -0.0023365838, 0.010692431, -0.008092484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [15.470808, 3.6733184, 5.329296, 2.6736164, 2.796142, 4.269908, 5.781234, 0.15601444, 1.9343879, 1.2809999, 2.793797, 4.6654196, 1.8826032, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.4405141, 1.4739928, 0.0, 3.8290195, 4.4919133, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5871446, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 20, 20, 21, 21, 23, 23, 24, 24, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.34615386, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.115384616, 0.004000859, 0.033593073, -0.027435979, -0.021627871, 0.00089472247, -0.017016368, -0.006084992, 1.0, 1.0, 0.000490308, 0.07692308, 1.0, 0.0075322664, 0.02688832, 0.0034151822, 0.012985756, -0.00906901, -0.027371982, 1.0, -0.021745455, 0.024001421, -0.0023365838, 0.010692431, -0.008092484], "split_indices": [71, 17, 113, 1, 137, 0, 109, 59, 12, 5, 115, 39, 1, 0, 0, 0, 0, 0, 0, 0, 74, 126, 0, 1, 53, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1030.0, 1035.0, 427.0, 603.0, 765.0, 270.0, 185.0, 242.0, 281.0, 322.0, 564.0, 201.0, 115.0, 155.0, 93.0, 92.0, 114.0, 128.0, 88.0, 193.0, 176.0, 146.0, 304.0, 260.0, 101.0, 100.0, 91.0, 102.0, 88.0, 88.0, 180.0, 124.0, 122.0, 138.0, 88.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0060208356, -0.08671611, 0.075458646, -0.06656, -0.021942273, 0.027809696, 0.20873503, -0.13048464, -0.023706807, -0.013771394, 0.1454128, 0.011593256, 0.031875085, -0.021346848, -0.073093936, 0.04341086, -0.072754346, -0.08302871, 0.01398684, 0.02477747, 0.005657036, -0.01632317, 0.0013735996, 0.016632067, -0.009077505, -0.014337149, 0.001513605, -0.028189994, -0.026873251, -0.011205576, 0.0051290616], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [13.597118, 2.7791672, 6.5346437, 2.4709158, 0.0, 3.70667, 2.7668371, 1.7240248, 1.7776566, 5.958781, 1.8006268, 0.0, 0.0, 0.0, 1.6749048, 3.7603512, 1.9364504, 3.93093, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9863795, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 17, 17, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.021942273, 1.0, 1.0, 0.34615386, 1.0, 1.0, 1.0, 0.011593256, 0.031875085, -0.021346848, 1.0, 0.115384616, 1.0, 1.0, 0.01398684, 0.02477747, 0.005657036, -0.01632317, 0.0013735996, 0.016632067, -0.009077505, -0.014337149, 0.001513605, 1.0, -0.026873251, -0.011205576, 0.0051290616], "split_indices": [71, 40, 113, 17, 0, 0, 61, 1, 122, 116, 13, 0, 0, 0, 111, 1, 12, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1039.0, 1029.0, 902.0, 137.0, 758.0, 271.0, 362.0, 540.0, 560.0, 198.0, 147.0, 124.0, 148.0, 214.0, 228.0, 312.0, 386.0, 174.0, 92.0, 106.0, 105.0, 109.0, 119.0, 109.0, 173.0, 139.0, 298.0, 88.0, 145.0, 153.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.002206687, -0.02318629, 0.20449759, -0.04103495, 0.022626659, 0.034088034, 0.0068114805, -0.08879581, 0.047097053, -0.051086757, -0.1771173, 0.13743529, -0.06426535, -0.008466845, -0.0952366, -0.13275708, -0.025024233, 0.22161433, -0.003092277, -0.013887184, 0.00076766073, 0.008100356, -0.04320966, -0.1524435, -0.029180005, -0.010004781, -0.017589016, 0.041293573, 0.006702664, -0.010464306, 0.0043101697, -0.021560635, -0.008744093, -0.0013117429, -0.004615519], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [8.942014, 8.334893, 3.534048, 7.3535776, 0.0, 0.0, 0.0, 3.7734776, 6.177014, 1.4940398, 1.0996618, 4.8043833, 1.476018, 1.2558151, 1.4737675, 0.29768825, 0.0, 6.6841574, 0.0, 0.0, 0.0, 0.0, 1.5429986, 0.8581009, 0.04935223, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, 1.0, 0.022626659, 0.034088034, 0.0068114805, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, -0.025024233, 1.0, -0.003092277, -0.013887184, 0.00076766073, 0.008100356, 1.0, 1.0, 1.0, -0.010004781, -0.017589016, 0.041293573, 0.006702664, -0.010464306, 0.0043101697, -0.021560635, -0.008744093, -0.0013117429, -0.004615519], "split_indices": [125, 0, 15, 50, 0, 0, 0, 23, 97, 69, 115, 106, 39, 0, 115, 12, 0, 126, 0, 0, 0, 0, 124, 124, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1872.0, 190.0, 1747.0, 125.0, 95.0, 95.0, 1133.0, 614.0, 794.0, 339.0, 339.0, 275.0, 404.0, 390.0, 211.0, 128.0, 226.0, 113.0, 135.0, 140.0, 113.0, 291.0, 209.0, 181.0, 120.0, 91.0, 101.0, 125.0, 170.0, 121.0, 106.0, 103.0, 93.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0072777257, -0.07213111, 0.05833715, -0.054520052, -0.019041436, 0.030394372, 0.023437662, -0.032957066, -0.14259934, -0.029508125, 0.09510038, 0.0056479424, -0.04815259, -0.013029324, -0.015504526, 0.034464985, -0.026759366, -0.0076230676, 0.21573459, -0.012286777, -0.018401744, 0.097642325, -0.013508233, 0.007988571, -0.014527737, 0.035356995, 0.010237465, -0.009573082, 0.022537166, -0.0014152534, 0.016846891, -0.007425748, 0.07220145, -0.0009616918, 0.014510894], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, -1, 21, -1, 23, 25, -1, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [8.748994, 2.1539183, 5.0272493, 1.7093267, 0.0, 3.4186773, 0.0, 0.9825819, 0.027108908, 6.9758315, 5.2541924, 0.0, 1.3737144, 0.0, 0.0, 3.8668697, 0.0, 2.758525, 3.046876, 0.0, 1.3992697, 2.0824468, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3892915, 0.0, 0.0, 0.0, 1.1393483, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 17, 17, 18, 18, 20, 20, 21, 21, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, -1, 22, -1, 24, 26, -1, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.019041436, 1.0, 0.023437662, -0.42307693, 1.0, 0.5769231, -0.07692308, 0.0056479424, 1.0, -0.013029324, -0.015504526, 1.0, -0.026759366, -0.30769232, 0.3846154, -0.012286777, 1.0, -0.3846154, -0.013508233, 0.007988571, -0.014527737, 0.035356995, 0.010237465, -0.009573082, 0.07692308, -0.0014152534, 0.016846891, -0.007425748, 1.0, -0.0009616918, 0.014510894], "split_indices": [71, 40, 125, 116, 0, 39, 0, 1, 106, 1, 1, 0, 59, 0, 0, 23, 0, 1, 1, 0, 39, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 69, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1034.0, 1022.0, 900.0, 134.0, 882.0, 140.0, 723.0, 177.0, 458.0, 424.0, 105.0, 618.0, 89.0, 88.0, 361.0, 97.0, 229.0, 195.0, 176.0, 442.0, 263.0, 98.0, 140.0, 89.0, 88.0, 107.0, 153.0, 289.0, 102.0, 161.0, 98.0, 191.0, 90.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025650584, -0.062960945, 0.058360614, -0.045624755, -0.017853554, 0.07871369, -0.015858468, -0.063234985, 0.011686963, 0.10875925, -0.0076653413, -0.028253527, -0.14611635, 0.1619206, 0.008862607, 0.034523014, -0.075095974, -0.017928744, -0.009601417, 0.11087208, 0.03006394, -0.010964335, 0.013000205, -0.004957044, 0.017841628, -0.12317854, -0.0010063408, 0.0074622757, 0.026598677, -0.01666835, -0.008489414, 0.012065037, -0.014189931], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [7.58378, 2.073749, 4.530308, 2.5754075, 0.0, 4.3786693, 0.0, 2.35424, 0.0, 4.174163, 0.0, 1.6790867, 0.40052795, 3.6327524, 3.919119, 2.9525182, 1.022507, 0.0, 0.0, 6.015142, 0.0, 0.0, 0.0, 0.0, 0.0, 0.31312442, 0.0, 3.8038394, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 3.5, -0.017853554, 1.0, -0.015858468, 1.0, 0.011686963, 0.07692308, -0.0076653413, 1.0, 0.88461536, -0.1923077, 1.0, 1.0, 1.0, -0.017928744, -0.009601417, 1.0, 0.03006394, -0.010964335, 0.013000205, -0.004957044, 0.017841628, -0.15384616, -0.0010063408, 1.0, 0.026598677, -0.01666835, -0.008489414, 0.012065037, -0.014189931], "split_indices": [71, 40, 41, 1, 0, 7, 0, 23, 0, 1, 0, 122, 1, 1, 39, 74, 12, 0, 0, 61, 0, 0, 0, 0, 0, 1, 0, 124, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1035.0, 1026.0, 900.0, 135.0, 938.0, 88.0, 812.0, 88.0, 786.0, 152.0, 571.0, 241.0, 513.0, 273.0, 244.0, 327.0, 145.0, 96.0, 375.0, 138.0, 138.0, 135.0, 154.0, 90.0, 188.0, 139.0, 225.0, 150.0, 88.0, 100.0, 128.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003685029, 0.044528514, -0.06320659, 0.026337646, 0.026467511, -0.08973405, 0.005667425, 0.09708548, -0.03567865, -0.062189132, -0.02229151, -0.00833626, 0.010148073, 0.18652228, -0.050549407, -0.1333231, 0.01573799, 0.01019889, -0.13067241, 0.27435425, -0.0031726812, 0.003435262, -0.0133048585, -0.023786817, 0.0012580385, -0.07622834, 0.022594677, -0.0059084236, 0.008854712, -0.016955195, -0.006658524, 0.037125967, 0.015454386, 4.3533124e-05, -0.017906988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.6499553, 5.1419797, 1.4324093, 5.2035975, 0.0, 2.0763478, 1.859597, 7.3150134, 3.172988, 2.325007, 0.0, 0.0, 0.0, 6.613392, 1.4639088, 3.325261, 8.003503, 1.2376322, 0.6004939, 2.8561249, 0.0, 0.0, 0.0, 0.0, 0.0, 2.2706532, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.026467511, 1.0, 1.0, 1.0, 1.0, 1.0, -0.02229151, -0.00833626, 0.010148073, 1.0, -0.23076923, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0031726812, 0.003435262, -0.0133048585, -0.023786817, 0.0012580385, 1.0, 0.022594677, -0.0059084236, 0.008854712, -0.016955195, -0.006658524, 0.037125967, 0.015454386, 4.3533124e-05, -0.017906988], "split_indices": [137, 102, 93, 53, 0, 0, 13, 97, 5, 106, 0, 0, 0, 122, 1, 97, 0, 12, 74, 126, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1284.0, 784.0, 1186.0, 98.0, 566.0, 218.0, 554.0, 632.0, 469.0, 97.0, 113.0, 105.0, 345.0, 209.0, 218.0, 414.0, 228.0, 241.0, 246.0, 99.0, 103.0, 106.0, 127.0, 91.0, 288.0, 126.0, 121.0, 107.0, 150.0, 91.0, 136.0, 110.0, 165.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022137014, 0.008690736, -0.02082447, -0.0062915715, 0.15546797, 0.03657281, -0.057318095, 0.02893467, 0.0015572215, -8.075684e-05, 0.13049759, -0.029581878, -0.022762818, 0.0718513, -0.052866723, 0.039193522, -0.0047021746, -0.09413108, 0.040747844, 0.018978199, -0.006791839, -0.015278816, 0.014580234, -0.0016358009, -0.13463788, -0.015119013, 0.112331286, 0.011735764, -0.0117562115, -0.0072749555, -0.023246132, -0.0034142025, 0.023867851], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [4.648324, 4.321157, 0.0, 3.8998141, 3.408689, 3.335956, 3.845138, 0.0, 0.0, 2.6465113, 12.623583, 3.1778095, 0.0, 4.862526, 2.709238, 0.0, 0.0, 1.149873, 4.60276, 0.0, 0.0, 0.0, 3.2594998, 0.0, 1.4529905, 0.0, 4.515586, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.02082447, 0.115384616, 1.0, -0.15384616, 1.0, 0.02893467, 0.0015572215, 1.0, 1.0, 1.0, -0.022762818, 1.0, 1.0, 0.039193522, -0.0047021746, 1.0, 1.0, 0.018978199, -0.006791839, -0.015278816, -0.34615386, -0.0016358009, 1.0, -0.015119013, 1.0, 0.011735764, -0.0117562115, -0.0072749555, -0.023246132, -0.0034142025, 0.023867851], "split_indices": [117, 125, 0, 1, 15, 1, 64, 0, 0, 53, 69, 39, 0, 97, 5, 0, 0, 124, 126, 0, 0, 0, 1, 0, 115, 0, 122, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1965.0, 104.0, 1783.0, 182.0, 969.0, 814.0, 93.0, 89.0, 697.0, 272.0, 700.0, 114.0, 295.0, 402.0, 110.0, 162.0, 365.0, 335.0, 160.0, 135.0, 162.0, 240.0, 125.0, 240.0, 91.0, 244.0, 135.0, 105.0, 147.0, 93.0, 113.0, 131.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00062368787, -0.013082886, 0.019728767, 0.04084593, -0.060867917, -0.016024686, 0.17494307, -0.0952934, 0.027894635, 0.013339789, -0.0132116815, -0.0014701688, 0.03005973, -0.1412665, -0.036589283, 0.022718856, -0.06764316, 0.067099944, -0.049571015, -0.19441989, -0.005740955, 0.03268178, -0.014887548, 0.0015896497, -0.016620122, 0.019951496, -0.0050587887, -0.012408567, 0.00018923181, -0.02714917, -0.015284781, -0.008397459, 0.017948534, 0.009249494, -0.010261255], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, 27, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.5852623, 4.99163, 0.0, 6.9398317, 3.1381974, 2.1783414, 6.457841, 1.9971194, 5.46451, 1.7248683, 0.0, 0.0, 0.0, 1.8497705, 2.5279102, 0.0, 1.5973006, 2.627598, 0.90117174, 0.81382465, 0.0, 3.44224, 0.0, 0.0, 0.0, 0.0, 1.693979, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, 28, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, -0.03846154, 0.019728767, 1.0, 1.2692307, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0132116815, -0.0014701688, 0.03005973, 1.0, 0.46153846, 0.022718856, 1.0, 0.0, 1.0, 0.30769232, -0.005740955, 1.0, -0.014887548, 0.0015896497, -0.016620122, 0.019951496, 1.0, -0.012408567, 0.00018923181, -0.02714917, -0.015284781, -0.008397459, 0.017948534, 0.009249494, -0.010261255], "split_indices": [0, 1, 0, 50, 1, 0, 109, 93, 137, 69, 0, 0, 0, 115, 1, 0, 106, 0, 13, 1, 0, 59, 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1937.0, 135.0, 910.0, 1027.0, 639.0, 271.0, 740.0, 287.0, 510.0, 129.0, 108.0, 163.0, 415.0, 325.0, 93.0, 194.0, 275.0, 235.0, 254.0, 161.0, 201.0, 124.0, 105.0, 89.0, 97.0, 178.0, 96.0, 139.0, 89.0, 165.0, 112.0, 89.0, 89.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0037944952, -0.042456057, 0.05067986, -0.05431674, 0.007082555, -0.0039040158, 0.10834509, -0.09779665, -0.025766207, 0.04661819, -0.022352098, 0.05865895, 0.023854451, -0.14982854, -0.044343103, 0.005462688, -0.015207596, -0.04947366, 0.15726942, -0.0416927, 0.019848226, -0.018675786, -0.011591385, -0.0003843916, -0.008528731, 0.06157266, -0.06062837, -0.008366877, -0.0012141353, 0.00024323173, 0.029441083, 0.009699016, -0.014864306, 0.016188486, -0.007217689, 0.0035364802, -0.013528976], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, -1, 21, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.454046, 1.3892796, 3.210544, 1.1619265, 0.0, 5.81406, 3.2086768, 1.0318573, 2.2286506, 4.5295224, 0.0, 5.037309, 0.0, 0.23545933, 0.30345163, 1.6798905, 0.0, 0.2910605, 4.2044463, 3.099926, 0.0, 0.0, 0.0, 0.0, 0.0, 3.2870946, 1.490733, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, -1, 22, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.6153846, 1.0, 1.0, 0.007082555, 0.5769231, 1.0, 0.34615386, 1.0, 1.0, -0.022352098, 0.03846154, 0.023854451, 1.0, 1.0384616, 0.1923077, -0.015207596, 1.0, -0.42307693, 1.0, 0.019848226, -0.018675786, -0.011591385, -0.0003843916, -0.008528731, 1.0, 1.0, -0.008366877, -0.0012141353, 0.00024323173, 0.029441083, 0.009699016, -0.014864306, 0.016188486, -0.007217689, 0.0035364802, -0.013528976], "split_indices": [71, 1, 39, 17, 0, 1, 113, 1, 7, 42, 0, 1, 0, 69, 1, 1, 0, 17, 1, 53, 0, 0, 0, 0, 0, 80, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1034.0, 1020.0, 936.0, 98.0, 524.0, 496.0, 371.0, 565.0, 426.0, 98.0, 359.0, 137.0, 188.0, 183.0, 453.0, 112.0, 228.0, 198.0, 209.0, 150.0, 90.0, 98.0, 92.0, 91.0, 245.0, 208.0, 119.0, 109.0, 93.0, 105.0, 91.0, 118.0, 140.0, 105.0, 91.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0031333144, 0.012473262, -0.017725583, -0.027426444, 0.05294413, -0.039086312, 0.008015684, 0.090151064, -0.04121492, -0.08917847, -0.006001316, 0.04847734, 0.030250585, -0.018251376, 0.033728097, -0.13700901, -0.0039708014, 0.025428852, -0.0131431, -0.005343375, 0.14067024, -0.011607376, 0.018188382, -0.016409444, -0.011137976, 0.08494321, -0.01890572, 0.07291483, -0.009741186, -0.0033416075, 0.027229652, 0.018872738, -0.0010192248, 0.0068865656, -0.007861416, 0.016018808, -0.0033752453], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, 25, -1, 27, 29, -1, -1, -1, -1, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.4909573, 3.1811085, 0.0, 1.2443717, 3.4262955, 1.4832823, 0.0, 6.2035794, 2.9332538, 0.8423662, 2.124887, 2.907667, 0.0, 0.0, 4.0171146, 0.12564564, 0.0, 1.1372125, 0.0, 2.6658926, 4.949496, 0.0, 0.0, 0.0, 0.0, 1.8167346, 1.2944511, 1.8618407, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 17, 17, 19, 19, 20, 20, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, 26, -1, 28, 30, -1, -1, -1, -1, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.017725583, 3.6153846, 0.15384616, 1.0, 0.008015684, -0.07692308, 1.0, 0.34615386, 1.0, 1.0, 0.030250585, -0.018251376, 1.0, 1.0, -0.0039708014, 1.0, -0.0131431, 1.0, 1.0, -0.011607376, 0.018188382, -0.016409444, -0.011137976, 1.0, 0.0, 1.0, -0.009741186, -0.0033416075, 0.027229652, 0.018872738, -0.0010192248, 0.0068865656, -0.007861416, 0.016018808, -0.0033752453], "split_indices": [117, 71, 0, 1, 1, 17, 0, 1, 69, 1, 7, 61, 0, 0, 39, 81, 0, 3, 0, 124, 124, 0, 0, 0, 0, 93, 1, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1970.0, 102.0, 992.0, 978.0, 895.0, 97.0, 701.0, 277.0, 356.0, 539.0, 586.0, 115.0, 96.0, 181.0, 181.0, 175.0, 431.0, 108.0, 370.0, 216.0, 90.0, 91.0, 88.0, 93.0, 184.0, 247.0, 200.0, 170.0, 93.0, 123.0, 88.0, 96.0, 100.0, 147.0, 110.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.003812148, -0.009398342, 0.13459602, -0.02239543, 0.017010473, 0.024656951, 0.002024006, 0.016639996, -0.054311447, 0.07711685, -0.061191067, -0.08848386, 0.028040437, -0.021271639, 0.21875302, 0.0037201897, -0.10542657, -0.15522511, -0.028787503, 0.020487793, -0.054422628, -0.014728874, 0.011684309, 0.017504191, 0.026343549, -0.019733654, -0.0039965487, -0.2180465, -0.0032464028, 0.01598359, -0.095443904, 0.0025434955, -0.014970727, -0.029229382, -0.013875322, -0.0018669636, -0.017947242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, 19, 21, 23, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [3.5781064, 4.388405, 2.4329185, 2.1852303, 0.0, 0.0, 0.0, 3.7138052, 2.715667, 6.1873045, 1.5015999, 2.7172298, 4.1268654, 4.560063, 0.35546684, 0.0, 1.4319327, 2.4832706, 4.5262647, 0.0, 1.4685764, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2539959, 0.0, 0.0, 1.7160261, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 20, 20, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, 20, 22, 24, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, -0.03846154, 0.017010473, 0.024656951, 0.002024006, 1.0, 1.2692307, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0037201897, 1.0, 1.0, 0.0, 0.020487793, 1.0, -0.014728874, 0.011684309, 0.017504191, 0.026343549, -0.019733654, -0.0039965487, 1.0, -0.0032464028, 0.01598359, 1.0, 0.0025434955, -0.014970727, -0.029229382, -0.013875322, -0.0018669636, -0.017947242], "split_indices": [125, 0, 15, 1, 0, 0, 0, 106, 1, 50, 15, 124, 137, 97, 61, 0, 81, 83, 0, 0, 106, 0, 0, 0, 0, 0, 0, 13, 0, 0, 50, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1881.0, 190.0, 1754.0, 127.0, 96.0, 94.0, 789.0, 965.0, 444.0, 345.0, 682.0, 283.0, 262.0, 182.0, 107.0, 238.0, 322.0, 360.0, 90.0, 193.0, 137.0, 125.0, 92.0, 90.0, 99.0, 139.0, 213.0, 109.0, 94.0, 266.0, 105.0, 88.0, 110.0, 103.0, 139.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025836224, -0.012082798, 0.12068503, -0.024245696, 0.01532006, 0.023009952, 0.0006407648, 0.013767879, -0.05478953, 0.07420473, -0.06421514, -0.037762497, -0.02119759, 0.01987908, 0.02637901, -0.0144541785, -0.006201429, -0.05335763, 0.0100315735, -0.10585337, 0.01827406, 0.0090644, -0.012241597, -0.03437079, -0.018105341, -0.002838433, -0.01764192, 0.020030601, -0.106475696, 0.010313681, -0.014596979, -0.06478211, -0.018902054, -0.011542139, 0.0051210555, 0.001321454, -0.01352063], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, -1, 19, -1, -1, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, 29, 31, -1, 33, 35, -1, -1, -1, -1, -1], "loss_changes": [2.9545605, 3.7834315, 2.3006618, 2.035374, 0.0, 0.0, 0.0, 3.6808903, 2.6014764, 4.5317125, 1.5890757, 1.8884865, 0.0, 7.003127, 0.0, 0.0, 2.2284598, 1.9105372, 0.0, 1.0550661, 0.0, 0.0, 0.0, 2.6909087, 0.0, 0.0, 0.0, 1.1252073, 1.0152683, 0.0, 1.8312618, 1.0765992, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 16, 16, 17, 17, 19, 19, 23, 23, 27, 27, 28, 28, 30, 30, 31, 31], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, -1, 20, -1, -1, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, 30, 32, -1, 34, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, -0.03846154, 0.01532006, 0.023009952, 0.0006407648, 1.0, 2.0, -0.1923077, 1.0, 1.0, -0.02119759, 1.0, 0.02637901, -0.0144541785, 1.0, 1.0, 0.0100315735, 1.0, 0.01827406, 0.0090644, -0.012241597, 1.0, -0.018105341, -0.002838433, -0.01764192, 1.0, 1.0, 0.010313681, 0.30769232, 1.0, -0.018902054, -0.011542139, 0.0051210555, 0.001321454, -0.01352063], "split_indices": [125, 0, 15, 1, 0, 0, 0, 106, 0, 1, 115, 88, 0, 61, 0, 0, 97, 64, 0, 109, 0, 0, 0, 23, 0, 0, 0, 69, 115, 0, 1, 61, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1882.0, 184.0, 1753.0, 129.0, 94.0, 90.0, 781.0, 972.0, 440.0, 341.0, 877.0, 95.0, 342.0, 98.0, 143.0, 198.0, 788.0, 89.0, 193.0, 149.0, 108.0, 90.0, 686.0, 102.0, 92.0, 101.0, 391.0, 295.0, 115.0, 276.0, 196.0, 99.0, 109.0, 167.0, 93.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.004664395, -0.03129608, 0.04090418, -0.014384527, -0.012548699, 0.055456605, -0.009006764, -0.02956751, 0.01096098, 0.03634056, 0.019891461, -0.010911747, -0.014034639, 0.0056008413, 0.1379434, 0.05420519, -0.05057531, 0.05117114, -0.07716134, 0.0033362475, 0.024034552, -0.0069431695, 0.014133187, -0.08291277, 0.0049290364, 0.111138746, -0.0027643412, 0.00082757755, -0.016183566, -0.041799348, -0.016876725, 0.028570822, -0.0027971293, -0.0109599605, 0.002926051], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [2.695017, 1.6534448, 1.9631366, 1.6566913, 0.0, 2.5421576, 0.0, 1.6202645, 0.0, 2.554812, 0.0, 1.7330383, 0.0, 2.3685002, 2.0347683, 2.7361064, 1.3466607, 1.91416, 1.6132559, 0.0, 0.0, 0.0, 0.0, 1.1118774, 0.0, 5.5854053, 0.0, 0.0, 0.0, 1.0262078, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.0, 1.0, 1.0, -0.012548699, 1.0, -0.009006764, 1.0, 0.01096098, 2.0, 0.019891461, 1.0, -0.014034639, 1.0, -0.1923077, 1.0, 1.0, 1.0, 1.0, 0.0033362475, 0.024034552, -0.0069431695, 0.014133187, 1.0, 0.0049290364, 1.0, -0.0027643412, 0.00082757755, -0.016183566, 1.0, -0.016876725, 0.028570822, -0.0027971293, -0.0109599605, 0.002926051], "split_indices": [71, 0, 90, 121, 0, 125, 0, 40, 0, 0, 0, 122, 0, 97, 1, 17, 50, 105, 109, 0, 0, 0, 0, 127, 0, 126, 0, 0, 0, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1038.0, 1030.0, 880.0, 158.0, 927.0, 103.0, 784.0, 96.0, 818.0, 109.0, 671.0, 113.0, 628.0, 190.0, 254.0, 417.0, 405.0, 223.0, 94.0, 96.0, 105.0, 149.0, 315.0, 102.0, 230.0, 175.0, 111.0, 112.0, 213.0, 102.0, 102.0, 128.0, 109.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0041924906, -0.0036364733, 0.0129391765, -0.021809384, 0.06321543, -0.007490046, -0.11956076, -0.0029418264, 0.122661464, -0.025830178, 0.049443115, -0.020909982, -0.0030021688, 0.025628794, 0.005182332, -0.058755733, 0.013996795, -0.0048551783, 0.12697756, -0.09492456, 0.005921027, -0.031958487, 0.011626351, 0.008935127, 0.016629487, -0.06296813, -0.018287543, -0.010596386, 0.0036631841, -0.012433625, -0.00043017307], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [2.031915, 2.3702571, 0.0, 2.147193, 2.296297, 1.3970884, 1.571379, 0.0, 2.404325, 1.3270612, 2.4769418, 0.0, 0.0, 0.0, 0.0, 2.3637478, 2.1524608, 0.0, 0.26924348, 1.1916914, 0.0, 1.6040331, 0.0, 0.0, 0.0, 1.1196764, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0129391765, 1.0, 1.0, 1.0, 1.0, -0.0029418264, 1.0, 1.0, 1.0, -0.020909982, -0.0030021688, 0.025628794, 0.005182332, 1.0, 1.0, -0.0048551783, 1.0, 1.0, 0.005921027, 1.0, 0.011626351, 0.008935127, 0.016629487, 1.0, -0.018287543, -0.010596386, 0.0036631841, -0.012433625, -0.00043017307], "split_indices": [102, 113, 0, 64, 71, 62, 13, 0, 69, 93, 15, 0, 0, 0, 0, 74, 50, 0, 39, 50, 0, 122, 0, 0, 0, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1951.0, 122.0, 1534.0, 417.0, 1338.0, 196.0, 163.0, 254.0, 1012.0, 326.0, 98.0, 98.0, 88.0, 166.0, 554.0, 458.0, 144.0, 182.0, 424.0, 130.0, 316.0, 142.0, 93.0, 89.0, 311.0, 113.0, 152.0, 164.0, 152.0, 159.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0013093392, 0.009456469, -0.015679335, 0.01889387, -0.014195225, 0.030982703, -0.07728862, 0.022417122, 0.014039178, -0.0009954026, -0.013605333, -0.004607567, 0.07035143, 0.012425931, -0.011897537, 0.14423873, -0.023770558, 0.030498575, -0.0102453185, 0.077431165, 0.026733784, -0.0063825445, 0.0015299348, 0.058549657, -0.00781526, 0.018868312, -0.0046839598, 0.12536493, 0.006650329, 0.0039450713, 0.025131678, -0.010786067, 0.011771223], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [2.6547387, 2.8006532, 0.0, 2.1452441, 0.0, 1.5359923, 0.8151207, 1.969023, 0.0, 0.0, 0.0, 1.8935378, 3.8110225, 1.7564391, 0.0, 2.5247517, 0.37715083, 2.2279294, 0.0, 2.751248, 0.0, 0.0, 0.0, 2.0147145, 0.0, 0.0, 0.0, 2.7485473, 4.158724, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.015679335, 1.0, -0.014195225, 5.0, 1.0, 1.0, 0.014039178, -0.0009954026, -0.013605333, 1.0, 1.0, 2.0, -0.011897537, 1.0, 0.53846157, 0.6923077, -0.0102453185, 1.0, 0.026733784, -0.0063825445, 0.0015299348, 1.0, -0.00781526, 0.018868312, -0.0046839598, 1.0, 1.0, 0.0039450713, 0.025131678, -0.010786067, 0.011771223], "split_indices": [117, 43, 0, 40, 0, 0, 108, 50, 0, 0, 0, 119, 97, 0, 0, 105, 1, 1, 0, 126, 0, 0, 0, 15, 0, 0, 0, 71, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 1845.0, 115.0, 1639.0, 206.0, 1520.0, 119.0, 96.0, 110.0, 972.0, 548.0, 846.0, 126.0, 307.0, 241.0, 731.0, 115.0, 199.0, 108.0, 119.0, 122.0, 581.0, 150.0, 105.0, 94.0, 254.0, 327.0, 151.0, 103.0, 161.0, 166.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0010714767, 0.008348138, -0.012275829, 0.023335222, -0.053848255, 0.009997182, 0.014064369, -0.014198403, -0.013283573, -0.0013652105, 0.011607384, -0.00751098, 0.009423724, 0.0306537, -0.053610068, 0.001239302, 0.014702439, 0.013870857, -0.1089596, -0.033874467, 0.011732975, 0.0108357575, -0.0055869315, -0.017874882, 0.002829249, 0.05140619, -0.11371892, 0.014648037, -0.0029444266, -0.029223964, 0.005244265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [1.8670108, 1.8242029, 0.0, 2.4674764, 1.1900996, 1.7066834, 0.0, 1.6710565, 0.0, 2.1395411, 0.0, 0.0, 0.0, 2.714418, 1.8152286, 2.5803444, 0.0, 1.443105, 2.5575178, 3.3092654, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8063961, 7.445484, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.012275829, 1.0, 1.0, 1.0, 0.014064369, 1.0, -0.013283573, 0.1923077, 0.011607384, -0.00751098, 0.009423724, 1.0, 1.0, 1.0, 0.014702439, 1.0, 1.1538461, -0.26923078, 0.011732975, 0.0108357575, -0.0055869315, -0.017874882, 0.002829249, 1.0, 1.0, 0.014648037, -0.0029444266, -0.029223964, 0.005244265], "split_indices": [43, 80, 0, 125, 116, 88, 0, 111, 0, 1, 0, 0, 0, 42, 127, 62, 0, 122, 1, 1, 0, 0, 0, 0, 0, 53, 124, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1957.0, 115.0, 1577.0, 380.0, 1416.0, 161.0, 253.0, 127.0, 1279.0, 137.0, 162.0, 91.0, 793.0, 486.0, 633.0, 160.0, 219.0, 267.0, 486.0, 147.0, 93.0, 126.0, 177.0, 90.0, 235.0, 251.0, 108.0, 127.0, 121.0, 130.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.004605338, -0.012716579, 0.011319198, -0.02072896, 0.011890497, -0.014485249, -0.013451061, -0.033865787, 0.0481067, -0.094951645, 0.001197935, 0.11855272, -0.012529887, -0.03833307, -0.1442431, 0.031608168, -0.04187781, 0.22995633, -0.009743384, 0.002915519, -0.008265374, -0.0049751783, -0.020621467, -0.020398675, -0.015234192, 0.0051556635, 0.038716, -0.0005956081, -0.009912792, 0.052117333, -0.043706577, 0.015097059, -0.0016319513, -0.012077603, 0.002730739], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, -1, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [1.9577832, 2.021672, 0.0, 1.283728, 0.0, 2.0779815, 0.0, 2.8016024, 4.9473724, 1.3312173, 11.271563, 6.9297647, 0.0, 0.66402996, 1.4932213, 0.0, 1.7344238, 5.3285646, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9541568, 0.0, 0.0, 0.0, 1.1112555, 0.0, 1.4883457, 1.4722393, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 17, 17, 23, 23, 27, 27, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, -1, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.011319198, 1.0, 0.011890497, 1.0, -0.013451061, -0.15384616, 1.0, 1.0, -0.03846154, 1.0, -0.012529887, -0.42307693, 1.0, 0.031608168, 1.0, 1.0, -0.009743384, 0.002915519, -0.008265374, -0.0049751783, -0.020621467, 1.0, -0.015234192, 0.0051556635, 0.038716, 1.0, -0.009912792, 1.0, 0.88461536, 0.015097059, -0.0016319513, -0.012077603, 0.002730739], "split_indices": [0, 102, 0, 117, 0, 61, 0, 1, 126, 71, 1, 23, 0, 1, 93, 0, 0, 113, 0, 0, 0, 0, 0, 15, 0, 0, 0, 97, 0, 122, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1917.0, 132.0, 1807.0, 110.0, 1713.0, 94.0, 1308.0, 405.0, 477.0, 831.0, 288.0, 117.0, 222.0, 255.0, 100.0, 731.0, 190.0, 98.0, 88.0, 134.0, 101.0, 154.0, 612.0, 119.0, 89.0, 101.0, 489.0, 123.0, 220.0, 269.0, 90.0, 130.0, 129.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0010527348, 0.007873917, -0.013179145, 0.017343536, -0.073077515, 0.02845824, -0.07455749, -0.001268769, -0.012681889, 0.016415704, 0.014599666, -0.021028198, 0.00528573, 0.027688708, -0.012385871, 0.012661753, 0.013206266, 0.00075702876, 0.014834997, -0.049058307, 0.04854661, -0.10415574, 0.0025441297, 0.021151187, 0.0020956965, -0.01482905, -0.0060371235, 0.006435808, -0.006711919, -0.008439506, 0.0043818364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, -1, -1, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8739269, 1.5078613, 0.0, 1.7987778, 0.66855884, 2.2236881, 3.285729, 0.0, 0.0, 2.253372, 0.0, 0.0, 0.0, 2.068749, 0.0, 1.862476, 0.0, 2.5234933, 0.0, 1.4756013, 4.0953074, 0.4850366, 1.1540521, 0.0, 1.5192308, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, -1, -1, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.013179145, 1.3461539, 1.0, 0.88461536, 3.1923077, -0.001268769, -0.012681889, 0.61538464, 0.014599666, -0.021028198, 0.00528573, 1.0, -0.012385871, 5.0, 0.013206266, 1.0, 0.014834997, 1.0, 1.0, 1.0, 1.0, 0.021151187, 1.0, -0.01482905, -0.0060371235, 0.006435808, -0.006711919, -0.008439506, 0.0043818364], "split_indices": [117, 40, 0, 1, 108, 1, 1, 0, 0, 1, 0, 0, 0, 125, 0, 0, 0, 124, 0, 115, 81, 97, 97, 0, 127, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1967.0, 101.0, 1761.0, 206.0, 1571.0, 190.0, 97.0, 109.0, 1425.0, 146.0, 92.0, 98.0, 1319.0, 106.0, 1153.0, 166.0, 1060.0, 93.0, 519.0, 541.0, 251.0, 268.0, 120.0, 421.0, 125.0, 126.0, 142.0, 126.0, 137.0, 284.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0023519523, -0.005477224, 0.011590446, -0.014187117, 0.013717666, -0.0023076648, -0.016716903, -0.013387381, 0.017930826, -0.07988659, 0.0022686082, 0.0013347372, -0.14625655, 0.010451878, -0.009220174, -0.016828122, -0.012398156, -0.019931423, 0.011477414, 0.029660605, -0.06689426, 0.054719772, -0.00863742, 0.003788262, -0.09509408, 0.0135486545, -0.0012817949, -0.0033059232, -0.014731999], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, -1, 15, -1, 17, -1, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1], "loss_changes": [1.833165, 2.3967826, 0.0, 3.3039262, 0.0, 3.39467, 0.0, 1.6553667, 0.0, 1.8749443, 1.5118778, 0.0, 0.086835146, 0.0, 1.5366507, 0.0, 0.0, 2.4803665, 0.0, 1.5062068, 1.6162152, 2.3237472, 0.0, 0.0, 1.3963645, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14, 17, 17, 19, 19, 20, 20, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, -1, 16, -1, 18, -1, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.011590446, 3.0, 0.013717666, 1.0, -0.016716903, -1.0, 0.017930826, 1.0, 0.0, 0.0013347372, 1.0, 0.010451878, 1.0, -0.016828122, -0.012398156, 1.0, 0.011477414, 1.0, 1.0, 1.0, -0.00863742, 0.003788262, -0.03846154, 0.0135486545, -0.0012817949, -0.0033059232, -0.014731999], "split_indices": [0, 84, 0, 0, 0, 102, 0, 0, 0, 122, 0, 0, 124, 0, 125, 0, 0, 13, 0, 58, 89, 108, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1929.0, 133.0, 1818.0, 111.0, 1687.0, 131.0, 1590.0, 97.0, 303.0, 1287.0, 126.0, 177.0, 130.0, 1157.0, 89.0, 88.0, 1065.0, 92.0, 518.0, 547.0, 426.0, 92.0, 116.0, 431.0, 194.0, 232.0, 197.0, 234.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.001369917, 0.0044498225, -0.011371374, 0.014403228, -0.081198, 0.025179515, -0.05930657, -0.00012741872, -0.015439922, 0.013274204, 0.01964969, 0.0011132559, -0.014424789, -0.012483477, 0.023036374, 0.013004451, 0.014264772, -0.009384584, 0.10726418, -0.085880466, 0.024883797, 0.033255808, -0.0027307316, -0.00091157324, -0.01646521, 0.027621666, -0.022618974, 0.0068466137, -0.0053962353], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, -1, -1, -1, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1], "loss_changes": [1.3540446, 1.6785476, 0.0, 1.4011769, 1.199356, 3.1389234, 1.3462186, 0.0, 0.0, 1.9401215, 0.0, 0.0, 0.0, 0.0, 1.6127079, 2.6168773, 0.0, 2.626633, 7.215718, 1.8745332, 8.261794, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6615608, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, -1, -1, -1, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1], "split_conditions": [1.0, 1.0, -0.011371374, 1.0, 1.0, 1.0, 1.0, -0.00012741872, -0.015439922, -0.53846157, 0.01964969, 0.0011132559, -0.014424789, -0.012483477, 5.0, 1.0, 0.014264772, -0.1923077, -0.15384616, 1.0, -0.03846154, 0.033255808, -0.0027307316, -0.00091157324, -0.01646521, 0.027621666, 0.0, 0.0068466137, -0.0053962353], "split_indices": [117, 40, 0, 119, 108, 125, 15, 0, 0, 1, 0, 0, 0, 0, 0, 61, 0, 1, 1, 108, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1969.0, 102.0, 1764.0, 205.0, 1539.0, 225.0, 98.0, 107.0, 1439.0, 100.0, 123.0, 102.0, 95.0, 1344.0, 1240.0, 104.0, 1002.0, 238.0, 310.0, 692.0, 89.0, 149.0, 157.0, 153.0, 110.0, 582.0, 149.0, 433.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.006105378, 0.0006469243, -0.013605411, -0.01101867, 0.05497431, 0.017347809, -0.055646192, -0.0011601266, 0.0116032595, -0.039798148, 0.064564526, -0.034524847, -0.018190486, 0.033959575, -0.09333658, 0.016840437, 0.02039327, -0.10531347, 0.013699896, 0.010607657, -0.0041292906, -0.014710821, -0.0046188976, -0.011084749, 0.05786804, -0.0017463962, -0.018694352, 0.011048249, -0.05251976, -0.0018168625, 0.013145193, -0.0007590612, -0.009211076], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, -1, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [1.8119413, 1.2440735, 0.0, 2.045736, 1.4105519, 2.6658657, 1.6747205, 0.0, 0.0, 1.7651465, 3.5983086, 1.8366051, 0.0, 1.0202729, 0.65661716, 2.111208, 0.0, 1.563312, 2.0508516, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7064978, 0.0, 0.0, 0.0, 0.33796984, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, -1, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.013605411, 1.0, 1.0, 1.0, 3.0, -0.0011601266, 0.0116032595, 1.0, 1.0, 1.0, -0.018190486, 1.0, 1.0, 1.0, 0.02039327, 1.0, 1.0, 0.010607657, -0.0041292906, -0.014710821, -0.0046188976, -0.011084749, 1.0, -0.0017463962, -0.018694352, 0.011048249, 1.0, -0.0018168625, 0.013145193, -0.0007590612, -0.009211076], "split_indices": [117, 42, 0, 109, 93, 39, 0, 0, 0, 122, 121, 2, 0, 93, 137, 59, 0, 59, 121, 0, 0, 0, 0, 0, 81, 0, 0, 0, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1963.0, 102.0, 1616.0, 347.0, 988.0, 628.0, 166.0, 181.0, 447.0, 541.0, 538.0, 90.0, 188.0, 259.0, 403.0, 138.0, 218.0, 320.0, 96.0, 92.0, 121.0, 138.0, 98.0, 305.0, 105.0, 113.0, 130.0, 190.0, 150.0, 155.0, 89.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00027494718, -0.0054194494, 0.0119675575, -0.009472056, 0.002631478, -0.009626763, 0.06732776, -0.047511082, 0.017045153, 0.016189767, -0.005599137, -0.013804032, -0.019319406, -0.06742342, 0.075819865, 0.015498376, -0.050953507, -0.015210159, -0.022069454, 0.020156115, 0.024889931, -0.099387735, 0.0017638308, 0.009161837, -0.008197801, -0.0070096874, 0.07416294, -0.01984292, -0.00027424208, 0.023337832, -0.0030919195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, 13, -1, -1, 15, -1, 17, 19, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1], "loss_changes": [1.4040239, 1.4170641, 0.0, 0.0, 1.4338573, 1.5358801, 3.358736, 3.0838208, 4.4284377, 0.0, 0.0, 3.1978924, 0.0, 2.9290183, 5.0676155, 0.0, 1.3886763, 1.9472303, 0.0, 1.9399624, 0.0, 2.3451135, 0.0, 0.0, 0.0, 0.0, 4.1659436, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 16, 16, 17, 17, 19, 19, 21, 21, 26, 26], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, 14, -1, -1, 16, -1, 18, 20, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 0.0119675575, -0.009472056, 1.0, 1.0, 1.0, 1.0, -0.07692308, 0.016189767, -0.005599137, -0.26923078, -0.019319406, 1.0, 1.0, 0.015498376, 1.0, -0.3846154, -0.022069454, 1.0, 0.024889931, 0.3846154, 0.0017638308, 0.009161837, -0.008197801, -0.0070096874, 0.8076923, -0.01984292, -0.00027424208, 0.023337832, -0.0030919195], "split_indices": [114, 1, 0, 0, 42, 39, 126, 0, 1, 0, 0, 1, 0, 7, 0, 0, 116, 1, 0, 124, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1971.0, 94.0, 163.0, 1808.0, 1520.0, 288.0, 628.0, 892.0, 163.0, 125.0, 510.0, 118.0, 366.0, 526.0, 92.0, 418.0, 273.0, 93.0, 398.0, 128.0, 245.0, 173.0, 105.0, 168.0, 149.0, 249.0, 121.0, 124.0, 99.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033363262, -0.014202894, 0.06070009, -0.045236617, 0.017543426, 0.019402651, -0.0073098377, 0.012666263, -0.06502978, 0.00011850185, 0.0139358, -0.0076634507, 0.004929468, -0.09993337, 0.006606646, -0.11369163, 0.035317518, -0.07783084, -0.020347672, -0.01790417, -0.0048341523, 0.072421744, -0.00863625, -0.026627608, -0.13665108, -0.008449088, 0.12013788, 0.009268309, -0.08564152, -0.020371389, -0.0050608194, 0.18012519, 0.004690658, -0.0147652645, -0.0020904595, 0.010012991, 0.026012046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, 27, 29, -1, 31, -1, 33, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [1.434159, 1.735934, 2.7111893, 3.031564, 1.8487928, 0.0, 0.77696985, 0.0, 3.656009, 3.052575, 0.0, 0.0, 0.0, 1.4440866, 0.0, 0.76871324, 2.627639, 1.5661283, 0.0, 0.0, 0.0, 3.3393197, 0.0, 1.9573956, 1.3964067, 0.0, 1.5023875, 0.0, 0.74667966, 0.0, 0.0, 1.2030568, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 23, 23, 24, 24, 26, 26, 28, 28, 31, 31], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, 28, 30, -1, 32, -1, 34, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.019402651, 2.7307692, 0.012666263, 1.0, 1.0, 0.0139358, -0.0076634507, 0.004929468, 1.0, 0.006606646, 1.0, 1.0, 1.0, -0.020347672, -0.01790417, -0.0048341523, 1.0, -0.00863625, -0.23076923, 1.0, -0.008449088, 1.0, 0.009268309, 1.0, -0.020371389, -0.0050608194, 1.0, 0.004690658, -0.0147652645, -0.0020904595, 0.010012991, 0.026012046], "split_indices": [1, 124, 137, 104, 105, 0, 1, 0, 62, 5, 0, 0, 0, 64, 0, 111, 23, 39, 0, 0, 0, 89, 0, 1, 111, 0, 97, 0, 109, 0, 0, 13, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1762.0, 299.0, 891.0, 871.0, 101.0, 198.0, 92.0, 799.0, 762.0, 109.0, 89.0, 109.0, 631.0, 168.0, 180.0, 582.0, 520.0, 111.0, 90.0, 90.0, 446.0, 136.0, 278.0, 242.0, 104.0, 342.0, 92.0, 186.0, 136.0, 106.0, 188.0, 154.0, 95.0, 91.0, 94.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00037685904, -0.009897298, 0.05582238, -0.016249808, 0.0108329905, 0.012559897, -0.00278069, 0.009975223, -0.046413645, -0.119480215, 0.055470124, -0.12877496, -0.008626146, -0.023535265, 0.006133166, 0.14257036, -0.0066478704, -0.021340815, -0.003916331, 0.10234286, -0.101291, 0.02800878, -0.0049954006, -0.017234523, 0.05108639, 0.025465904, -0.0013549862, -0.017010925, -0.0049470006, -0.007684113, 0.019512331], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1043254, 1.325584, 1.7447748, 1.3250047, 0.0, 0.0, 0.0, 5.2770476, 2.4244254, 4.881609, 3.587157, 1.8581085, 5.4910827, 0.0, 0.0, 7.307227, 3.7022023, 0.0, 0.0, 4.289519, 1.0377722, 0.0, 0.0, 0.0, 5.288345, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 0.0108329905, 0.012559897, -0.00278069, 1.0, -0.1923077, 1.0, -0.26923078, 1.0, 0.1923077, -0.023535265, 0.006133166, 1.0, -0.07692308, -0.021340815, -0.003916331, 1.0, 1.0, 0.02800878, -0.0049954006, -0.017234523, 1.0, 0.025465904, -0.0013549862, -0.017010925, -0.0049470006, -0.007684113, 0.019512331], "split_indices": [1, 114, 115, 126, 0, 0, 0, 89, 1, 97, 1, 69, 1, 0, 0, 97, 1, 0, 0, 69, 69, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1765.0, 299.0, 1675.0, 90.0, 163.0, 136.0, 896.0, 779.0, 233.0, 663.0, 245.0, 534.0, 142.0, 91.0, 276.0, 387.0, 126.0, 119.0, 243.0, 291.0, 161.0, 115.0, 100.0, 287.0, 105.0, 138.0, 125.0, 166.0, 152.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0041011483, 0.009836556, -0.009330013, 0.0041308515, 0.0118009625, 0.013269964, -0.012916075, 0.003402206, 0.018600874, 0.014192093, -0.05038854, 0.0032647091, 0.009886167, -0.011251914, -0.0011324374, -0.029079584, 0.03209443, 0.023632307, -0.074187145, -0.0479126, 0.1297254, -0.009884666, 0.010220374, 0.0009437315, -0.01714525, -0.1480476, 0.024461657, 0.027049873, 0.0014058131, -0.004975315, -0.026451413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.1452036, 1.1949056, 0.0, 2.2402086, 0.0, 2.9335203, 0.0, 0.94488466, 0.0, 1.2545938, 0.66016537, 1.1199049, 0.0, 0.0, 0.0, 1.3457811, 4.9600897, 2.5116937, 2.4807982, 10.223051, 4.6568985, 0.0, 0.0, 0.0, 0.0, 2.976481, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 3.5769231, -0.009330013, 1.3461539, 0.0118009625, 1.1153846, -0.012916075, 1.0, 0.018600874, 1.0, 1.0, -0.1923077, 0.009886167, -0.011251914, -0.0011324374, 1.0, 1.0, 1.0, 1.0, 1.0, 0.115384616, -0.009884666, 0.010220374, 0.0009437315, -0.01714525, 1.0, 0.024461657, 0.027049873, 0.0014058131, -0.004975315, -0.026451413], "split_indices": [43, 1, 0, 1, 0, 1, 0, 80, 0, 125, 122, 1, 0, 0, 0, 108, 124, 5, 39, 62, 1, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1936.0, 114.0, 1839.0, 97.0, 1721.0, 118.0, 1628.0, 93.0, 1356.0, 272.0, 1201.0, 155.0, 105.0, 167.0, 566.0, 635.0, 261.0, 305.0, 349.0, 286.0, 102.0, 159.0, 164.0, 141.0, 260.0, 89.0, 129.0, 157.0, 141.0, 119.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0020203986, 0.007370882, -0.008774879, 0.017345637, -0.034046594, 0.026010389, -0.006918152, -0.072361566, 0.0027624748, 0.010941004, 0.02027089, -0.010431368, -0.0037864563, 0.021221722, -0.011103018, 0.062319838, -0.017651333, 0.005860492, 0.13894323, 0.04232891, -0.09615489, -0.009050188, 0.071046814, 0.024785027, -0.00380307, 0.015134754, 0.000333391, -0.0018529398, -0.017153036, 0.018197475, -0.0053747064, -0.012057287, 0.015686383], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9990417, 0.81097245, 0.0, 1.1860826, 0.9002787, 3.8290157, 0.0, 0.2590288, 0.0, 1.661485, 0.0, 0.0, 0.0, 1.9522786, 0.0, 2.5697067, 2.9570403, 2.1482759, 4.856974, 1.6298723, 1.5914869, 0.0, 2.823998, 0.0, 0.0, 0.0, 4.8638563, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008774879, 1.3461539, 1.0, 0.88461536, -0.006918152, 1.0, 0.0027624748, 0.5769231, 0.02027089, -0.010431368, -0.0037864563, 1.0, -0.011103018, -0.1923077, 1.0, 1.0, 0.23076923, -0.3846154, -0.23076923, -0.009050188, 1.0, 0.024785027, -0.00380307, 0.015134754, 1.0, -0.0018529398, -0.017153036, 0.018197475, -0.0053747064, -0.012057287, 0.015686383], "split_indices": [43, 80, 0, 1, 111, 1, 0, 12, 0, 1, 0, 0, 0, 69, 0, 1, 97, 13, 1, 1, 1, 0, 59, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1963.0, 117.0, 1582.0, 381.0, 1438.0, 144.0, 235.0, 146.0, 1325.0, 113.0, 122.0, 113.0, 1222.0, 103.0, 594.0, 628.0, 342.0, 252.0, 356.0, 272.0, 138.0, 204.0, 156.0, 96.0, 99.0, 257.0, 134.0, 138.0, 108.0, 96.0, 145.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.010709318, -0.015924824, 0.010074376, -0.010719267, -0.007618538, -0.018262245, 0.046754554, -0.009389024, -0.009244454, 0.011442356, -0.0030231178, 0.011439713, -0.053307362, -0.07259565, 0.040234186, -0.111107, 0.012279244, 0.0026114925, -0.014681414, 0.010471101, 0.015400936, 0.0033930205, -0.20486844, 0.03791738, -0.009299892, -0.024224732, -0.017434804, -0.02186069, 0.102970004, -0.013535199, 0.008565734, 0.01918314, 0.0015834041], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, -1, -1, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [1.1962826, 1.4904181, 0.0, 0.0, 1.0428736, 0.99196106, 1.5368168, 1.2312719, 0.0, 0.0, 0.0, 2.2092352, 4.4072924, 1.7069933, 2.3026838, 4.4332414, 0.0, 0.0, 0.0, 1.5306882, 0.0, 0.0, 0.22587967, 1.656595, 0.0, 0.0, 0.0, 2.7089245, 1.579576, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 19, 19, 22, 22, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, -1, -1, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 0.010074376, -0.010719267, 1.2692307, 1.0, 1.0, 1.0, -0.009244454, 0.011442356, -0.0030231178, 1.0, 1.0, 1.0, 1.0, 1.0, 0.012279244, 0.0026114925, -0.014681414, 1.0, 0.015400936, 0.0033930205, 1.0, 1.0, -0.009299892, -0.024224732, -0.017434804, -0.115384616, 0.07692308, -0.013535199, 0.008565734, 0.01918314, 0.0015834041], "split_indices": [114, 1, 0, 0, 1, 40, 115, 105, 0, 0, 0, 5, 42, 124, 61, 109, 0, 0, 0, 23, 0, 0, 13, 12, 0, 0, 0, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1966.0, 92.0, 164.0, 1802.0, 1507.0, 295.0, 1346.0, 161.0, 157.0, 138.0, 913.0, 433.0, 233.0, 680.0, 326.0, 107.0, 100.0, 133.0, 539.0, 141.0, 128.0, 198.0, 426.0, 113.0, 89.0, 109.0, 222.0, 204.0, 108.0, 114.0, 101.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0047066393, 0.0022617164, -0.008576188, -0.021178955, 0.02625276, -0.0065371436, -0.011372413, 0.051593367, -0.030566303, -0.037160344, 0.02785506, 0.02105839, 0.0207725, -0.010497593, 0.0065693744, 0.0042025317, -0.09216419, 0.012364595, -0.007436312, -0.007758108, 0.05674705, 0.011531605, -0.0063899285, -0.014090814, -0.004834383, 0.0036280004, -0.0062602162, 0.017513892, 0.017018221, -0.00419383, 0.010207027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [1.1629684, 1.0662463, 0.0, 1.2994723, 1.3491204, 0.8720491, 0.0, 3.0893235, 2.070013, 0.99650156, 1.3184313, 1.9080085, 0.0, 0.0, 0.0, 1.8917584, 0.40156317, 0.0, 0.68731964, 0.0, 1.8720204, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4942833, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 18, 18, 20, 20, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, -0.008576188, 2.0, 0.115384616, 1.0, -0.011372413, -0.07692308, 1.0, 1.0, 1.0, 1.0, 0.0207725, -0.010497593, 0.0065693744, 1.0, 1.0, 0.012364595, 1.0, -0.007758108, 1.0, 0.011531605, -0.0063899285, -0.014090814, -0.004834383, 0.0036280004, -0.0062602162, 0.017513892, 1.0, -0.00419383, 0.010207027], "split_indices": [41, 71, 0, 0, 1, 115, 0, 1, 53, 39, 122, 5, 0, 0, 0, 80, 122, 0, 15, 0, 17, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1896.0, 163.0, 959.0, 937.0, 828.0, 131.0, 648.0, 289.0, 438.0, 390.0, 542.0, 106.0, 163.0, 126.0, 250.0, 188.0, 105.0, 285.0, 144.0, 398.0, 95.0, 155.0, 89.0, 99.0, 159.0, 126.0, 100.0, 298.0, 176.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.002404275, -0.0022433274, 0.008504712, 0.0061457506, -0.012305928, -0.002065619, 0.014663338, -0.06801837, 0.0067626997, -0.016379606, 0.002053079, 0.03714114, -0.015769422, 0.019892814, 0.005913867, -0.06605457, 0.041523676, 0.049196444, -0.06400412, -0.16454336, 0.019039731, -0.062863916, 0.14745395, 0.018837146, -0.03973978, -0.015897516, 0.00084313855, -0.026511818, -0.009406177, 0.0065632337, -0.0039305934, -0.014569323, 0.0005305316, 0.027438471, -0.0008768464, -0.009445529, 0.0023614995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, -1, 13, 15, -1, 17, 19, 21, 23, 25, 27, 29, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.79353243, 1.9824737, 0.0, 2.1099272, 0.0, 1.0061315, 0.0, 1.7301311, 1.0431639, 0.0, 0.0, 3.278856, 2.520868, 0.0, 1.6462696, 3.9054694, 4.522643, 4.158908, 1.43089, 1.5311522, 0.67961913, 1.163161, 4.025375, 0.0, 0.71063054, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, -1, 14, 16, -1, 18, 20, 22, 24, 26, 28, 30, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5, 1.3461539, 0.008504712, 1.1538461, -0.012305928, 1.0, 0.014663338, 1.0, 1.0, -0.016379606, 0.002053079, 1.0, 1.0, 0.019892814, 1.0, 1.0, 1.0, -0.30769232, -0.30769232, -0.30769232, -0.03846154, -0.115384616, 1.0, 0.018837146, 1.0, -0.015897516, 0.00084313855, -0.026511818, -0.009406177, 0.0065632337, -0.0039305934, -0.014569323, 0.0005305316, 0.027438471, -0.0008768464, -0.009445529, 0.0023614995], "split_indices": [1, 1, 0, 1, 0, 26, 0, 116, 53, 0, 0, 81, 97, 0, 97, 106, 2, 1, 1, 1, 1, 1, 69, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1956.0, 110.0, 1829.0, 127.0, 1728.0, 101.0, 204.0, 1524.0, 98.0, 106.0, 649.0, 875.0, 105.0, 544.0, 466.0, 409.0, 336.0, 208.0, 216.0, 250.0, 206.0, 203.0, 131.0, 205.0, 90.0, 118.0, 89.0, 127.0, 139.0, 111.0, 93.0, 113.0, 112.0, 91.0, 110.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.000149415, 0.0046004816, -0.008546812, -0.0014887773, 0.06344127, -0.008568425, 0.009294457, 0.016003786, -0.0031077948, -0.0140154185, 0.008498814, -0.005155021, -0.0127710765, -0.009584996, 0.0016353322, 0.008136482, -0.0060783313, -0.03362372, 0.018986257, -0.06312356, 0.011884384, -0.030520622, 0.07134019, -0.0029149135, -0.018360691, -0.013125918, 0.007330805, -0.0048823864, 0.015017799], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, -1, 15, -1, 17, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [0.78656816, 0.70297855, 0.0, 1.1886902, 1.6799636, 0.8428815, 0.0, 0.0, 0.0, 1.5745437, 0.0, 0.89298373, 0.0, 0.0, 0.8296439, 0.0, 0.84920913, 2.6356921, 1.6691706, 2.0098355, 0.0, 3.462111, 2.9651968, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, -1, 16, -1, 18, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008546812, 5.0, 1.0, 1.0, 0.009294457, 0.016003786, -0.0031077948, 3.0, 0.008498814, -0.53846157, -0.0127710765, -0.009584996, -0.42307693, 0.008136482, 1.0, 1.0, 1.0, 1.0, 0.011884384, 1.0, 1.0, -0.0029149135, -0.018360691, -0.013125918, 0.007330805, -0.0048823864, 0.015017799], "split_indices": [117, 125, 0, 0, 15, 84, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 111, 64, 122, 113, 0, 97, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1962.0, 102.0, 1778.0, 184.0, 1654.0, 124.0, 91.0, 93.0, 1563.0, 91.0, 1450.0, 113.0, 101.0, 1349.0, 119.0, 1230.0, 586.0, 644.0, 491.0, 95.0, 331.0, 313.0, 383.0, 108.0, 168.0, 163.0, 124.0, 189.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006300936, -0.005122346, 0.009404529, -0.013322918, 0.07499091, -0.019867308, 0.0077098734, 0.014740676, -0.00023623432, -0.0044636815, -0.08528397, 0.0048412276, -0.056976553, 0.0036742191, -0.020731015, 0.04509544, -0.015605353, -0.012261553, 0.0016953627, 0.0137073565, -0.02494293, -0.06517083, 0.012409913, -0.007468608, 0.0038272291, -0.01073624, -7.11478e-05, 0.009993001, -0.0081830425, 0.026349034, -0.048662327, -0.010818361, 0.013443512, -0.008025883, -0.0017065796], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, -1], "loss_changes": [0.8731523, 1.2876699, 0.0, 1.0521398, 1.019492, 1.67069, 0.0, 0.0, 0.0, 0.65573823, 4.705363, 0.93828964, 0.9802458, 0.0, 0.0, 2.4737277, 1.0497744, 0.0, 0.0, 0.0, 0.68550646, 0.7424618, 0.87051004, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5465531, 3.0681741, 0.17970085, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 20, 20, 21, 21, 22, 22, 28, 28, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.009404529, 5.0, 1.0, 1.0, 0.0077098734, 0.014740676, -0.00023623432, 1.0, 1.0, 0.0, 1.0, 0.0036742191, -0.020731015, 1.0, 1.0, -0.012261553, 0.0016953627, 0.0137073565, 1.0, 1.0, 1.0, -0.007468608, 0.0038272291, -0.01073624, -7.11478e-05, 0.009993001, 1.0, 1.0, 1.0, -0.010818361, 0.013443512, -0.008025883, -0.0017065796], "split_indices": [114, 125, 0, 0, 15, 0, 0, 0, 0, 42, 59, 0, 50, 0, 0, 122, 69, 0, 0, 0, 126, 137, 5, 0, 0, 0, 0, 0, 13, 126, 122, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1960.0, 93.0, 1778.0, 182.0, 1658.0, 120.0, 94.0, 88.0, 1342.0, 316.0, 1140.0, 202.0, 158.0, 158.0, 384.0, 756.0, 107.0, 95.0, 166.0, 218.0, 273.0, 483.0, 122.0, 96.0, 165.0, 108.0, 92.0, 391.0, 211.0, 180.0, 94.0, 117.0, 90.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0058894926, -0.0018737687, 0.052364513, -0.028911749, 0.025966706, 0.015546629, -0.0015656296, 0.011112732, -0.045397144, 0.1032492, 0.0041284547, -0.006424509, 0.004998381, -0.077396385, 0.007631422, 0.025178289, -0.0032048782, 0.05607856, -0.05079469, -0.054350786, -0.018945292, -0.0017218542, 0.12813336, -0.011911291, 0.0016705322, 0.0029789694, -0.08849799, 0.0116793765, 0.013960177, -0.0151561815, -0.054520734, -0.011772992, 0.0029758152], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, 17, -1, -1, 19, -1, -1, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [0.7486555, 1.3383898, 1.6514077, 2.0823565, 1.4784379, 0.0, 0.6300628, 0.0, 3.1429996, 3.8785882, 1.9487789, 0.0, 0.0, 1.6501591, 0.0, 0.0, 0.0, 1.8537747, 1.531012, 1.522776, 0.0, 0.0, 0.023017168, 0.0, 0.0, 0.0, 0.8078108, 0.0, 0.0, 0.0, 1.3051641, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 10, 10, 13, 13, 17, 17, 18, 18, 19, 19, 22, 22, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, 18, -1, -1, 20, -1, -1, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.015546629, 2.7307692, 0.011112732, 1.0, 0.03846154, 1.0, -0.006424509, 0.004998381, 1.0, 0.007631422, 0.025178289, -0.0032048782, 1.0, 1.0, -0.30769232, -0.018945292, -0.0017218542, -0.03846154, -0.011911291, 0.0016705322, 0.0029789694, 1.0, 0.0116793765, 0.013960177, -0.0151561815, 0.115384616, -0.011772992, 0.0029758152], "split_indices": [1, 124, 137, 104, 81, 0, 1, 0, 62, 1, 15, 0, 0, 64, 0, 0, 0, 71, 50, 1, 0, 0, 1, 0, 0, 0, 53, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1778.0, 297.0, 902.0, 876.0, 102.0, 195.0, 95.0, 807.0, 193.0, 683.0, 88.0, 107.0, 639.0, 168.0, 92.0, 101.0, 351.0, 332.0, 530.0, 109.0, 174.0, 177.0, 165.0, 167.0, 153.0, 377.0, 89.0, 88.0, 132.0, 245.0, 140.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0010829149, -0.0066289906, 0.047276195, -0.013562184, 0.008637723, 0.015308538, -0.005093993, 0.015145028, -0.049037766, -0.005735396, 0.003757696, -0.004281893, 0.013200546, -0.017130716, -0.032484367, -0.049388636, 0.11467401, -0.0016729132, -0.090683766, -0.017886167, -0.017456947, 0.025481287, -0.006916401, 0.037616502, -0.054130774, -0.0013859958, -0.015790457, 0.010310574, -0.052602604, -0.0023670262, 0.009452566, 0.0037683335, -0.014394896, -0.018296497, 0.0043311478, 0.009831981, -0.006616032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, 15, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [0.737057, 1.1432835, 1.6402097, 1.6803684, 0.0, 0.0, 0.44153658, 2.070458, 1.493692, 0.0, 0.0, 4.1959877, 0.0, 0.0, 1.165585, 2.235967, 5.539012, 0.87594146, 1.1619352, 1.9027846, 0.0, 0.0, 0.0, 0.8475305, 1.5008774, 0.0, 0.0, 0.0, 2.6125512, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6232227, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8, 11, 11, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24, 28, 28, 34, 34], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, 16, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.2692307, 5.0, 1.0, -0.03846154, 0.008637723, 0.015308538, 2.7307692, -0.15384616, 0.03846154, -0.005735396, 0.003757696, 1.0, 0.013200546, -0.017130716, 1.0, 1.0, 1.0, 0.5, 1.0, -0.5, -0.017456947, 0.025481287, -0.006916401, 1.0, 1.0, -0.0013859958, -0.015790457, 0.010310574, 1.0, -0.0023670262, 0.009452566, 0.0037683335, -0.014394896, -0.018296497, 1.0, 0.009831981, -0.006616032], "split_indices": [1, 0, 137, 1, 0, 0, 1, 1, 1, 0, 0, 61, 0, 0, 50, 42, 105, 1, 2, 1, 0, 0, 0, 12, 59, 0, 0, 0, 81, 0, 0, 0, 0, 0, 93, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1773.0, 296.0, 1650.0, 123.0, 98.0, 198.0, 912.0, 738.0, 89.0, 109.0, 782.0, 130.0, 88.0, 650.0, 567.0, 215.0, 425.0, 225.0, 453.0, 114.0, 122.0, 93.0, 243.0, 182.0, 105.0, 120.0, 101.0, 352.0, 117.0, 126.0, 90.0, 92.0, 107.0, 245.0, 105.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0028326933, 0.007793231, -0.008276948, 0.012973677, -0.008606424, -0.03027883, 0.022169095, 0.005100803, -0.09212755, -0.009616711, 0.032100584, -0.004287122, -0.014138383, 0.0137060145, 0.116336025, 0.044164523, -0.03359901, 0.0010190093, 0.022944232, 0.009664829, 0.017669093, -0.093321905, 0.006547072, 0.032400414, -0.008168714, -0.015046402, -0.0026509576, -0.012764098, 0.0678094, 0.006994457, -0.01043662, 0.012774171, -0.0012287012], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, 11, -1, 13, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.8760172, 0.94813573, 0.0, 0.7349955, 0.0, 1.6289062, 1.7910883, 0.0, 0.44641745, 0.0, 2.1785607, 0.0, 0.0, 1.6627301, 3.0254538, 3.2096276, 2.6743624, 0.0, 0.0, 1.1568557, 0.0, 1.0766184, 0.0, 0.7132567, 0.0, 0.0, 0.0, 1.4849526, 1.2000909, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 8, 8, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, 12, -1, 14, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008276948, -1.0, -0.008606424, 1.0, 1.0, 0.005100803, -0.15384616, -0.009616711, 1.0, -0.004287122, -0.014138383, 1.0, 1.0, 1.0, 1.0, 0.0010190093, 0.022944232, 2.0, 0.017669093, 1.0, 0.006547072, 1.0, -0.008168714, -0.015046402, -0.0026509576, 0.3846154, 1.0, 0.006994457, -0.01043662, 0.012774171, -0.0012287012], "split_indices": [43, 117, 0, 0, 0, 122, 104, 0, 1, 0, 42, 0, 0, 109, 93, 105, 61, 0, 0, 0, 0, 122, 0, 106, 0, 0, 0, 1, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1950.0, 113.0, 1848.0, 102.0, 324.0, 1524.0, 140.0, 184.0, 118.0, 1406.0, 92.0, 92.0, 1154.0, 252.0, 702.0, 452.0, 130.0, 122.0, 557.0, 145.0, 282.0, 170.0, 446.0, 111.0, 152.0, 130.0, 196.0, 250.0, 103.0, 93.0, 143.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.005327173, -0.011007784, 0.008751976, -0.0007449526, -0.09907601, -0.009657727, 0.015910367, -0.016183887, -0.004908787, -0.025011541, 0.053179175, 0.0026203757, -0.058344558, 0.018168533, -0.0078539625, -0.00787745, 0.018124169, -0.10064039, 0.0040580668, 0.092344865, -0.01981494, -0.024917958, -0.046143856, 0.0005053593, 0.020265799, -0.015255384, 0.017267663, 0.011729744, -0.11361963, 0.0070015476, -0.0036494513, -0.018298827, -0.0025463616], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [1.0886098, 1.757948, 0.0, 2.4818175, 0.636891, 1.591897, 0.0, 0.0, 0.0, 1.2213194, 5.484244, 0.9148986, 2.5146592, 0.0, 0.0, 0.0, 1.7148634, 3.40794, 0.0, 1.9836503, 1.9836892, 0.0, 3.3967254, 0.0, 0.0, 0.0, 0.89328897, 0.0, 1.3331265, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 16, 16, 17, 17, 19, 19, 20, 20, 22, 22, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.008751976, 1.0, -0.1923077, 1.0, 0.015910367, -0.016183887, -0.004908787, 1.0, 1.0, 1.0, 1.0, 0.018168533, -0.0078539625, -0.00787745, 1.0, 1.0, 0.0040580668, 1.0, -0.15384616, -0.024917958, 1.0, 0.0005053593, 0.020265799, -0.015255384, 1.0, 0.011729744, 1.0, 0.0070015476, -0.0036494513, -0.018298827, -0.0025463616], "split_indices": [102, 119, 0, 125, 1, 105, 0, 0, 0, 127, 109, 89, 109, 0, 0, 0, 97, 59, 0, 13, 1, 0, 115, 0, 0, 0, 23, 0, 81, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1945.0, 119.0, 1742.0, 203.0, 1650.0, 92.0, 90.0, 113.0, 1326.0, 324.0, 725.0, 601.0, 164.0, 160.0, 116.0, 609.0, 421.0, 180.0, 206.0, 403.0, 113.0, 308.0, 115.0, 91.0, 88.0, 315.0, 90.0, 218.0, 159.0, 156.0, 122.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010581114, -0.017376507, 0.01601815, 0.015342345, -0.050908644, 0.0015701463, 0.014216031, 0.07508812, -0.051487897, -0.096891105, -0.0018364388, -0.029690927, 0.033247117, 0.012051, 0.00067543035, 0.002114579, -0.009442859, -0.014752105, -0.0049877563, 0.0041801324, -0.0055043916, -0.107228756, 0.056821175, -0.006658257, 0.09537136, 0.00015173409, -0.02115543, -0.0066281557, 0.014309004, -0.0013629016, 0.02091806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.577099, 1.161864, 1.8443719, 2.1401541, 1.1801287, 0.89915246, 0.0, 0.8783891, 0.78909224, 0.6426785, 0.5874294, 3.0655384, 2.7970314, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.734145, 2.2939057, 0.0, 3.4486601, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 21, 21, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.014216031, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.012051, 0.00067543035, 0.002114579, -0.009442859, -0.014752105, -0.0049877563, 0.0041801324, -0.0055043916, 1.0, 1.0, -0.006658257, 1.0, 0.00015173409, -0.02115543, -0.0066281557, 0.014309004, -0.0013629016, 0.02091806], "split_indices": [39, 126, 88, 97, 97, 122, 0, 58, 69, 13, 115, 97, 81, 0, 0, 0, 0, 0, 0, 0, 0, 53, 2, 0, 126, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1059.0, 1012.0, 536.0, 523.0, 908.0, 104.0, 283.0, 253.0, 270.0, 253.0, 457.0, 451.0, 170.0, 113.0, 94.0, 159.0, 130.0, 140.0, 139.0, 114.0, 241.0, 216.0, 173.0, 278.0, 118.0, 123.0, 89.0, 127.0, 142.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044670603, 0.003981072, -0.06933035, -0.0039332737, 0.014734617, -1.6813143e-06, -0.014792691, -0.009233779, 0.00943345, -0.07028662, 0.0017714511, -0.018391272, 0.0008260254, -0.0061547426, 0.011428897, -0.015135118, 0.009447578, -0.0015547506, -0.017477663, 0.009770411, -0.008224655, -0.0022109416, 0.009963058, 0.0016676525, -0.007944721], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, -1, -1, 15, -1, 17, -1, 19, -1, 21, -1, 23, -1, -1, -1], "loss_changes": [1.1364975, 2.0820665, 1.3020253, 0.90579087, 0.0, 0.0, 0.0, 1.1086363, 0.0, 2.249094, 1.2467866, 0.0, 0.0, 1.1802318, 0.0, 2.59942, 0.0, 1.009802, 0.0, 1.0432707, 0.0, 1.2472719, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 19, 19, 21, 21], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, -1, -1, 16, -1, 18, -1, 20, -1, 22, -1, 24, -1, -1, -1], "split_conditions": [1.3461539, 1.1538461, 1.0, 1.0, 0.014734617, -1.6813143e-06, -0.014792691, -0.5, 0.00943345, 1.0, 1.0, -0.018391272, 0.0008260254, 5.0, 0.011428897, 3.0, 0.009447578, 0.6923077, -0.017477663, 0.42307693, -0.008224655, 1.0, 0.009963058, 0.0016676525, -0.007944721], "split_indices": [1, 1, 12, 84, 0, 0, 0, 1, 0, 89, 90, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 116, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1835.0, 239.0, 1739.0, 96.0, 127.0, 112.0, 1650.0, 89.0, 252.0, 1398.0, 103.0, 149.0, 1306.0, 92.0, 1199.0, 107.0, 1105.0, 94.0, 969.0, 136.0, 855.0, 114.0, 687.0, 168.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017338528, -0.0075181127, 0.009390249, 0.01417632, -0.0360919, -0.0035240687, 0.019475607, -0.013727594, -0.015531212, -0.010671324, 0.013912986, -0.042118873, 0.04855416, -0.0070078997, 0.010960919, 0.0009792484, -0.08539436, 0.012537529, -0.0024223723, 0.025005084, -0.014825605, -0.0069109895, 0.012304464, -0.01552857, -0.0020495232, 0.011505289, -0.013459772, -0.007963913, 0.028001979, -0.0052200165, 0.009909026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, 13, 15, 17, 19, -1, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, -1, 27, -1, 29, -1, -1], "loss_changes": [1.1445404, 1.2094096, 0.0, 3.5447326, 2.2450051, 1.8173088, 0.0, 1.2536954, 0.0, 0.0, 1.729771, 0.90830016, 1.2411754, 3.2059388, 0.0, 2.0875323, 1.10222, 0.0, 0.0, 2.002005, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1112844, 0.0, 1.419657, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, 14, 16, 18, 20, -1, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, -1, 28, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 0.009390249, 4.0, 1.0, -0.3846154, 0.019475607, 1.0, -0.015531212, -0.010671324, 1.0, 0.1923077, 1.0, 1.0, 0.010960919, -0.1923077, 0.7692308, 0.012537529, -0.0024223723, 0.0, -0.014825605, -0.0069109895, 0.012304464, -0.01552857, -0.0020495232, 0.011505289, 1.0, -0.007963913, 1.0, -0.0052200165, 0.009909026], "split_indices": [102, 109, 0, 0, 119, 1, 0, 113, 0, 0, 105, 1, 39, 113, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 0, 69, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1951.0, 118.0, 1109.0, 842.0, 1010.0, 99.0, 709.0, 133.0, 146.0, 864.0, 487.0, 222.0, 709.0, 155.0, 244.0, 243.0, 108.0, 114.0, 578.0, 131.0, 155.0, 89.0, 117.0, 126.0, 173.0, 405.0, 156.0, 249.0, 117.0, 132.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0058698608, 0.012661759, -0.047619965, 0.0049640415, 0.014344744, 0.005204173, -0.019239172, 0.012397982, -0.0065210545, 0.022082528, -0.049500868, 0.013052762, 0.012957971, -0.011668356, 0.0012795777, -0.008905658, 0.021355772, -0.010568876, 0.049930956, -0.088330485, 0.038032122, 0.011859238, 0.01636484, 0.0024068223, -0.019448485, 0.014820012, -0.034146905, 0.010243829, -0.029391725, -0.013033392, 0.004717482, -0.010807804, 0.0024047622], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, -1, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [0.75129896, 1.8473881, 3.361771, 0.9040603, 0.0, 0.0, 0.0, 0.93935734, 0.0, 1.3152632, 0.88727456, 1.0597686, 0.0, 0.0, 0.0, 0.0, 1.0545641, 2.0634933, 2.6409454, 2.505639, 2.6718116, 1.7075686, 0.0, 0.0, 0.0, 0.0, 1.5878854, 0.0, 1.320353, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, -1, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.1538461, 1.0, 1.0, 0.014344744, 0.005204173, -0.019239172, 1.0, -0.0065210545, 1.0, -0.23076923, -0.53846157, 0.012957971, -0.011668356, 0.0012795777, -0.008905658, 1.0, -0.15384616, 2.0, -0.34615386, 0.1923077, 0.0, 0.01636484, 0.0024068223, -0.019448485, 0.014820012, 1.0, 0.010243829, 1.0, -0.013033392, 0.004717482, -0.010807804, 0.0024047622], "split_indices": [1, 1, 17, 40, 0, 0, 0, 119, 0, 125, 1, 1, 0, 0, 0, 0, 13, 1, 0, 1, 1, 0, 0, 0, 0, 0, 122, 0, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1835.0, 233.0, 1733.0, 102.0, 138.0, 95.0, 1567.0, 166.0, 1355.0, 212.0, 1250.0, 105.0, 102.0, 110.0, 94.0, 1156.0, 546.0, 610.0, 210.0, 336.0, 457.0, 153.0, 102.0, 108.0, 133.0, 203.0, 143.0, 314.0, 93.0, 110.0, 127.0, 187.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004593988, 0.008528008, 0.00023701995, -0.0073387967, 0.006196753, -0.001228796, 0.012025589, 0.0064550727, -0.06142575, -0.01379263, 0.04010619, -0.013989498, 0.000858108, 0.007830464, -0.031191362, -0.04732374, 0.07968445, -0.015791029, -0.015016315, 0.0022623145, -0.011806551, 0.1645662, -0.00919695, 0.0045177187, -0.00960249, 0.0246513, 0.006440899, -0.006650865, 0.004751772, -0.049369905, 0.08380613, -0.012471871, 0.0021598137, 0.016118051, 0.0030105975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.7273499, 0.0, 0.86133534, 0.0, 1.5380648, 0.78864014, 0.0, 1.0302131, 1.0602231, 1.5126425, 1.9654648, 0.0, 0.0, 0.0, 1.4547707, 0.87582594, 2.9498649, 1.1455033, 0.0, 0.0, 0.0, 1.6415119, 0.62082916, 2.3969648, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7860177, 0.9431881, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 23, 23, 29, 29, 30, 30], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008528008, -0.5, -0.0073387967, 1.0, 1.0, 0.012025589, 1.0, 1.0, 1.0, 1.0, -0.013989498, 0.000858108, 0.007830464, 1.0, 1.0, 1.0, 1.0, -0.015016315, 0.0022623145, -0.011806551, 1.0, -0.23076923, 1.0, -0.00960249, 0.0246513, 0.006440899, -0.006650865, 0.004751772, 1.0, 0.42307693, -0.012471871, 0.0021598137, 0.016118051, 0.0030105975], "split_indices": [1, 0, 1, 0, 90, 40, 0, 15, 137, 26, 17, 0, 0, 0, 62, 93, 106, 113, 0, 0, 0, 109, 1, 50, 0, 0, 0, 0, 0, 106, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 106.0, 1963.0, 147.0, 1816.0, 1705.0, 111.0, 1512.0, 193.0, 944.0, 568.0, 91.0, 102.0, 150.0, 794.0, 177.0, 391.0, 703.0, 91.0, 89.0, 88.0, 200.0, 191.0, 561.0, 142.0, 110.0, 90.0, 95.0, 96.0, 334.0, 227.0, 162.0, 172.0, 93.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0064996383, 0.010937355, -0.023616845, -0.009238248, 0.021139177, -0.03984083, 0.03833612, 0.01427877, -0.012068848, -0.05348292, 0.006566764, -0.005627477, 0.014315018, 0.08681588, -0.017022414, 0.010590943, -0.07456756, -6.070523e-05, 0.017273154, -0.0722704, 0.031053014, 0.011630301, -0.00951211, -0.12887801, -0.0066236034, -0.012373898, 0.0007850771, -0.039636247, 0.01893006, -0.21521465, -0.0005243912, 0.0044229347, -0.009156483, -0.011984966, 0.0025123174, -0.020506112, -0.022548225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, 29, 31, -1, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.61276436, 4.1130424, 1.0413084, 2.421783, 0.0, 1.1817111, 2.1320589, 1.7323897, 0.0, 0.98215866, 0.0, 0.0, 0.0, 1.7275304, 1.4156857, 2.011507, 2.0184662, 0.0, 0.0, 1.0226839, 3.1881256, 0.0, 0.0, 3.2449422, 1.0496416, 0.0, 0.0, 1.0233314, 0.0, 0.018659592, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 24, 24, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, 30, 32, -1, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1538461, 1.0, 1.0, 0.021139177, 3.0, 1.0, -0.34615386, -0.012068848, -0.30769232, 0.006566764, -0.005627477, 0.014315018, -0.5, 1.0, 1.0, 1.0, -6.070523e-05, 0.017273154, 0.115384616, 1.0, 0.011630301, -0.00951211, 1.0, 1.0769231, -0.012373898, 0.0007850771, 1.0, 0.01893006, 0.34615386, -0.0005243912, 0.0044229347, -0.009156483, -0.011984966, 0.0025123174, -0.020506112, -0.022548225], "split_indices": [108, 1, 58, 50, 0, 0, 39, 1, 0, 1, 0, 0, 0, 1, 115, 53, 53, 0, 0, 1, 71, 0, 0, 23, 1, 0, 0, 97, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1017.0, 1036.0, 924.0, 93.0, 821.0, 215.0, 763.0, 161.0, 727.0, 94.0, 113.0, 102.0, 230.0, 533.0, 180.0, 547.0, 114.0, 116.0, 248.0, 285.0, 90.0, 90.0, 304.0, 243.0, 151.0, 97.0, 197.0, 88.0, 179.0, 125.0, 152.0, 91.0, 88.0, 109.0, 90.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0014023047, 0.007908968, -0.048914045, -0.0014522701, 0.009506899, 0.0007787547, -0.012602824, -0.05949469, 0.014085237, 0.0039968234, -0.021695366, 0.032488454, -0.056780867, -0.012612181, 0.00995385, 0.11490576, 0.0032854658, 0.0018335835, -0.09476124, 0.019177591, 0.00081038236, -0.011084444, 0.03605926, -0.0043166997, -0.014635545, 0.015392219, 0.010056508, 0.07514065, -0.047207434, -0.0029830707, 0.018679202, -0.01574251, 0.0073763155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, -1, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1], "loss_changes": [0.6747539, 1.4890649, 1.03191, 1.4862233, 0.0, 0.0, 0.0, 3.479064, 1.6954135, 3.0830743, 0.0, 2.4838498, 0.7645938, 0.0, 0.0, 2.216668, 2.8502383, 0.0, 0.4738289, 0.0, 0.0, 0.0, 1.8143377, 0.0, 0.0, 0.0, 1.8075826, 2.6604853, 3.4399393, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 22, 22, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, -1, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1], "split_conditions": [1.3461539, 0.88461536, 1.0, 1.0, 0.009506899, 0.0007787547, -0.012602824, 1.0, 1.0, -0.42307693, -0.021695366, -0.34615386, 1.0, -0.012612181, 0.00995385, 1.0, -0.1923077, 0.0018335835, -0.23076923, 0.019177591, 0.00081038236, -0.011084444, -0.03846154, -0.0043166997, -0.014635545, 0.015392219, 1.0, 1.0, 1.0, -0.0029830707, 0.018679202, -0.01574251, 0.0073763155], "split_indices": [1, 1, 17, 89, 0, 0, 0, 106, 7, 1, 0, 1, 17, 0, 0, 97, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 126, 71, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1825.0, 236.0, 1648.0, 177.0, 136.0, 100.0, 348.0, 1300.0, 248.0, 100.0, 1032.0, 268.0, 105.0, 143.0, 270.0, 762.0, 90.0, 178.0, 157.0, 113.0, 170.0, 592.0, 89.0, 89.0, 107.0, 485.0, 227.0, 258.0, 117.0, 110.0, 135.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0019433878, 0.01880617, -0.018082274, -0.003678607, 0.10601986, 0.0013713547, -0.020150222, 0.023933815, -0.011663853, 0.0013038268, 0.021009099, -0.029732356, 0.013299318, 0.08527759, -0.007123069, 0.060442768, -0.06348107, 0.018139658, -0.0035994013, -0.008439261, 0.013060788, -0.006417103, 0.017615562, -0.11082699, 0.007893243, -0.016383843, 0.03997736, -0.15538412, -0.0035617047, -0.007792177, 0.00458378, 0.010433274, -0.0012569672, -0.0073366896, -0.021180732], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, -1, -1, -1, 25, -1, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.70104104, 2.2100253, 3.386207, 2.7947114, 2.2353168, 3.5125895, 0.0, 1.3717057, 0.0, 0.0, 0.0, 2.1120467, 0.0, 2.8208747, 0.74548775, 2.7252703, 3.405063, 0.0, 0.0, 0.0, 0.30037597, 0.0, 0.0, 1.270081, 0.0, 0.69304746, 0.66957384, 1.1013851, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 20, 20, 23, 23, 25, 25, 26, 26, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, -1, -1, -1, 26, -1, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 4.0, 1.0, 1.0, 1.0, -0.020150222, 1.0, -0.011663853, 0.0013038268, 0.021009099, -0.3846154, 0.013299318, 1.0, 1.0, 1.0, 1.0, 0.018139658, -0.0035994013, -0.008439261, 1.0, -0.006417103, 0.017615562, 1.0, 0.007893243, 0.0, 0.07692308, 1.0, -0.0035617047, -0.007792177, 0.00458378, 0.010433274, -0.0012569672, -0.0073366896, -0.021180732], "split_indices": [109, 105, 0, 113, 39, 0, 0, 122, 0, 0, 0, 1, 0, 12, 26, 39, 42, 0, 0, 0, 69, 0, 0, 122, 0, 1, 1, 39, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1127.0, 949.0, 896.0, 231.0, 858.0, 91.0, 720.0, 176.0, 122.0, 109.0, 694.0, 164.0, 242.0, 478.0, 189.0, 505.0, 135.0, 107.0, 99.0, 379.0, 91.0, 98.0, 379.0, 126.0, 181.0, 198.0, 238.0, 141.0, 91.0, 90.0, 89.0, 109.0, 97.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0005372439, 0.0042988593, -0.006341019, 0.00044184463, 0.0072946274, 0.006918163, -0.00946722, 0.002102953, 0.009280682, -0.017281132, 0.02238132, -0.040960588, 0.06941196, 0.00902263, 0.0033066873, 0.01596025, -0.09981393, 0.016885309, -0.0030029137, 0.02901026, -0.013687051, 0.055408712, -0.0058515696, -0.011028835, -0.02165029, 0.060108505, -0.003899371, -0.0030703973, 0.012387538, -0.005298018, 0.0031844487, 0.11826671, -0.007365533, 0.01642029, 0.0035071135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.49792933, 0.51763314, 0.0, 1.140195, 0.0, 0.7167198, 0.0, 0.64504033, 0.0, 1.722337, 1.0378823, 2.207638, 1.779937, 0.0, 2.255512, 0.98421705, 3.356717, 0.0, 0.0, 1.1187314, 0.0, 1.2911911, 0.0, 0.33094126, 0.0, 2.823946, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9668863, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 19, 19, 21, 21, 23, 23, 25, 25, 31, 31], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.006341019, 1.3461539, 0.0072946274, 1.1538461, -0.00946722, 1.0, 0.009280682, 1.0, 1.0, 1.0, 1.0, 0.00902263, 3.0, 1.0, -0.115384616, 0.016885309, -0.0030029137, 1.0, -0.013687051, 1.0, -0.0058515696, 1.0, -0.02165029, 1.0, -0.003899371, -0.0030703973, 0.012387538, -0.005298018, 0.0031844487, 1.0, -0.007365533, 0.01642029, 0.0035071135], "split_indices": [43, 1, 0, 1, 0, 1, 0, 124, 0, 62, 81, 39, 13, 0, 0, 111, 1, 0, 0, 80, 0, 53, 0, 106, 0, 97, 0, 0, 0, 0, 0, 59, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1955.0, 115.0, 1851.0, 104.0, 1733.0, 118.0, 1641.0, 92.0, 839.0, 802.0, 659.0, 180.0, 176.0, 626.0, 335.0, 324.0, 90.0, 90.0, 529.0, 97.0, 219.0, 116.0, 184.0, 140.0, 363.0, 166.0, 97.0, 122.0, 93.0, 91.0, 253.0, 110.0, 163.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00059495214, 0.0041787094, -0.0061640805, 0.00026911506, 0.0075557805, 0.007720813, -0.011176552, -0.045241605, 0.01870534, 0.0058890716, -0.09730778, -0.011902202, 0.031646874, -0.015180615, -0.0037011672, 0.013239806, 0.021571754, 0.029141536, -0.0049189236, -0.007036806, 0.039573174, 0.023052553, 0.10994206, -0.0014774351, 0.006779519, 0.02074149, 0.0011409695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, -1, 13, -1, 15, -1, -1, -1, 17, 19, -1, -1, 21, 23, 25, -1, -1, -1, -1], "loss_changes": [0.4587887, 0.5427779, 0.0, 1.53946, 0.0, 1.0058751, 0.0, 1.6102664, 2.5524013, 0.0, 0.65063536, 0.0, 1.32874, 0.0, 0.0, 0.0, 0.63741785, 1.1159016, 0.0, 0.0, 1.1311487, 1.3336707, 1.776782, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 12, 12, 16, 16, 17, 17, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, -1, 14, -1, 16, -1, -1, -1, 18, 20, -1, -1, 22, 24, 26, -1, -1, -1, -1], "split_conditions": [1.0, 3.5, -0.0061640805, 1.3461539, 0.0075557805, -1.0, -0.011176552, -0.30769232, 1.0, 0.0058890716, 1.0, -0.011902202, 0.0, -0.015180615, -0.0037011672, 0.013239806, 1.0, -0.53846157, -0.0049189236, -0.007036806, 1.0, 1.0, -0.07692308, -0.0014774351, 0.006779519, 0.02074149, 0.0011409695], "split_indices": [43, 1, 0, 1, 0, 0, 0, 1, 104, 0, 39, 0, 0, 0, 0, 0, 40, 1, 0, 0, 42, 122, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1945.0, 112.0, 1844.0, 101.0, 1729.0, 115.0, 297.0, 1432.0, 99.0, 198.0, 123.0, 1309.0, 104.0, 94.0, 119.0, 1190.0, 1075.0, 115.0, 102.0, 973.0, 788.0, 185.0, 427.0, 361.0, 93.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034462018, 0.00393453, -0.053769387, -0.0039724074, 0.008176549, -0.016689422, 0.009975715, -0.015714863, 0.045586, 0.023566438, -0.049211405, -0.0028618877, 0.011838194, -0.0068882233, 0.04444195, -0.09177206, -0.021689495, 0.009841967, 0.012349487, -0.0020380912, -0.019572757, -0.00965259, 0.020035828, 0.008599478, -0.06160475, 0.011684982, -0.0074031646, -0.012038834, -0.00041274205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, -1, -1, 25, -1, 27, -1, -1, -1, -1], "loss_changes": [0.766615, 1.1077285, 4.585063, 0.95088565, 0.0, 0.0, 0.0, 1.7381558, 1.6907681, 1.1733873, 0.83517253, 0.0, 0.0, 0.0, 1.3566735, 2.0780203, 1.3520744, 1.8770995, 0.0, 0.0, 0.0, 0.0, 2.5317593, 0.0, 0.6014125, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 17, 17, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, -1, -1, 26, -1, 28, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.008176549, -0.016689422, 0.009975715, 1.0, 1.0, -1.0, 1.0, -0.0028618877, 0.011838194, -0.0068882233, 1.0, 1.0, 1.0, 1.0, 0.012349487, -0.0020380912, -0.019572757, -0.00965259, 1.0, 0.008599478, 1.0, 0.011684982, -0.0074031646, -0.012038834, -0.00041274205], "split_indices": [0, 125, 106, 58, 0, 0, 0, 108, 13, 0, 59, 0, 0, 0, 109, 39, 81, 13, 0, 0, 0, 0, 93, 0, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1800.0, 264.0, 1634.0, 166.0, 152.0, 112.0, 1321.0, 313.0, 608.0, 713.0, 155.0, 158.0, 112.0, 496.0, 280.0, 433.0, 345.0, 151.0, 166.0, 114.0, 155.0, 278.0, 167.0, 178.0, 137.0, 141.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016700258, -0.006637931, 0.008282754, 0.0019334245, -0.08106447, -0.006153629, 0.011508307, -0.00042475315, -0.018057728, -0.01119665, 0.006121933, -0.027052483, 0.045903496, -0.07655549, 0.010604901, -0.010436603, 0.017824315, 0.0045343023, -0.16318156, 0.010802242, -0.021548614, 0.0076048947, -0.007736148, -0.021134712, -0.009141482, -0.056511465, 0.0048790835, 0.0020463432, -0.09637348, -0.004713438, -0.017247025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1], "loss_changes": [0.8693556, 1.2478037, 0.0, 1.6049935, 1.544142, 0.5561922, 0.0, 0.0, 0.0, 1.3788791, 0.0, 2.2220716, 6.582471, 3.6176121, 2.120578, 0.0, 0.0, 1.5578951, 0.86071396, 0.0, 1.2517674, 0.0, 0.0, 0.0, 0.0, 1.0432475, 0.0, 0.0, 0.83931327, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 20, 20, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 0.008282754, 1.0, 1.0, 5.0, 0.011508307, -0.00042475315, -0.018057728, 1.0, 0.006121933, 1.0, 1.0, 1.0, 0.0, -0.010436603, 0.017824315, 1.0, 1.0, 0.010802242, 1.0, 0.0076048947, -0.007736148, -0.021134712, -0.009141482, 1.0, 0.0048790835, 0.0020463432, 1.0, -0.004713438, -0.017247025], "split_indices": [102, 119, 0, 90, 15, 0, 0, 0, 0, 61, 0, 111, 17, 127, 0, 0, 0, 80, 105, 0, 50, 0, 0, 0, 0, 115, 0, 0, 69, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1956.0, 115.0, 1754.0, 202.0, 1637.0, 117.0, 114.0, 88.0, 1523.0, 114.0, 1192.0, 331.0, 515.0, 677.0, 155.0, 176.0, 266.0, 249.0, 168.0, 509.0, 142.0, 124.0, 149.0, 100.0, 340.0, 169.0, 116.0, 224.0, 136.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.004637954, 0.0020728034, -0.04971729, -0.004703392, 0.011417611, -0.01409809, 0.00619109, -0.06415946, 0.009309095, 0.00277464, -0.14100732, 0.00023637373, 0.011095566, -0.02130555, -0.006976864, -0.03443014, 0.03340264, 0.015757937, -0.07866888, 0.0030174006, 0.015424237, 0.00615479, -0.0014768689, -0.016306283, 0.0005213578, -0.048014507, 0.06821236, 0.009343801, -0.01695186, -0.00015900962, 0.012517119], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.62802434, 1.372659, 2.7404628, 1.4196492, 0.0, 0.0, 0.0, 2.2954001, 1.2717285, 0.0, 0.90847206, 1.4555947, 0.0, 0.0, 0.0, 1.3743393, 2.3756182, 0.40536594, 2.3290484, 1.7200711, 0.0, 0.0, 0.0, 0.0, 0.0, 4.9842477, 0.9025221, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, -0.115384616, -1.0, 0.011417611, -0.01409809, 0.00619109, 1.0, 1.0, 0.00277464, 1.0, 1.0, 0.011095566, -0.02130555, -0.006976864, 1.0, 1.0, 1.0, 1.0, 1.0, 0.015424237, 0.00615479, -0.0014768689, -0.016306283, 0.0005213578, 1.0, 1.0, 0.009343801, -0.01695186, -0.00015900962, 0.012517119], "split_indices": [0, 102, 1, 0, 0, 0, 0, 127, 125, 0, 124, 111, 0, 0, 0, 71, 105, 108, 61, 12, 0, 0, 0, 0, 0, 39, 81, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1807.0, 269.0, 1704.0, 103.0, 148.0, 121.0, 325.0, 1379.0, 148.0, 177.0, 1266.0, 113.0, 88.0, 89.0, 619.0, 647.0, 290.0, 329.0, 517.0, 130.0, 116.0, 174.0, 164.0, 165.0, 290.0, 227.0, 134.0, 156.0, 102.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.003481417, -0.054131076, 0.010506041, 9.799068e-05, -0.015297902, 0.039929084, -0.004110037, 0.013748178, -0.07524149, -0.03937818, 0.09599984, 0.014318177, -0.010619119, -0.0011699147, -0.013610832, -0.011199697, 0.004762054, 0.019235259, 0.0020450514, -0.004895625, 0.0890157, 0.08238344, -0.039171893, 0.022056801, -0.0030455245, 0.018735532, 2.3151913e-05, -0.015414402, -0.0010433729, 0.013543542, -0.06721492, 0.0026681335, -0.011913404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [1.4601362, 2.390753, 0.69453007, 2.980922, 0.0, 2.3834949, 2.0297863, 0.0, 0.7193773, 1.402539, 2.2857263, 1.3117944, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.1748936, 2.9390202, 1.7678459, 2.2883, 0.0, 0.0, 0.0, 0.0, 0.0, 3.5401568, 0.0, 1.2870032, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 19, 19, 20, 20, 21, 21, 22, 22, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [-0.3846154, 1.0, -0.03846154, 1.0, -0.015297902, 1.0, 1.0, 0.013748178, 1.0, -0.23076923, 1.0, 1.0, -0.010619119, -0.0011699147, -0.013610832, -0.011199697, 0.004762054, 0.019235259, 0.0020450514, 0.0, 1.0, 1.0, 0.26923078, 0.022056801, -0.0030455245, 0.018735532, 2.3151913e-05, -0.015414402, 1.0, 0.013543542, 1.0384616, 0.0026681335, -0.011913404], "split_indices": [1, 121, 1, 13, 0, 13, 64, 0, 113, 1, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 124, 122, 1, 0, 0, 0, 0, 0, 124, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 446.0, 1615.0, 288.0, 158.0, 536.0, 1079.0, 102.0, 186.0, 222.0, 314.0, 914.0, 165.0, 91.0, 95.0, 121.0, 101.0, 138.0, 176.0, 727.0, 187.0, 205.0, 522.0, 89.0, 98.0, 90.0, 115.0, 130.0, 392.0, 128.0, 264.0, 94.0, 170.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026653453, -0.020846017, 0.016288586, -0.013704859, -0.0031933817, -0.0010169988, 0.01656111, 0.06852192, -0.04442043, 0.010220326, -0.025204856, 0.16435024, -0.0042144316, 0.007493736, -0.07942884, -0.089465596, 0.025576795, 0.015981052, 0.016874027, -0.007969402, 0.006500058, -0.015923219, -0.015195066, -0.010837274, -0.02681663, -0.030902205, 0.010218052, -0.0029982158, -0.0002604192, -0.012814747, 0.009180913, -0.0129887825, 0.0060035773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, -1, 7, 9, -1, 11, 13, -1, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7115891, 2.1620502, 2.6125388, 0.0, 2.705298, 2.2619889, 0.0, 3.5420573, 1.0559282, 0.0, 2.3952377, 0.003566265, 0.0, 1.1732508, 1.5981231, 4.5525026, 1.7738659, 0.0, 0.0, 0.0, 0.0, 0.03464141, 0.0, 2.7093306, 0.0, 2.1243665, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 21, 21, 23, 23, 25, 25], "right_children": [2, 4, 6, -1, 8, 10, -1, 12, 14, -1, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, -0.013704859, -0.07692308, -0.42307693, 0.01656111, 1.0, 1.0, 0.010220326, 0.03846154, 1.0, -0.0042144316, 0.3846154, 1.0, 1.0, 1.0, 0.015981052, 0.016874027, -0.007969402, 0.006500058, 1.0, -0.015195066, 1.0, -0.02681663, 1.0, 0.010218052, -0.0029982158, -0.0002604192, -0.012814747, 0.009180913, -0.0129887825, 0.0060035773], "split_indices": [39, 1, 88, 0, 1, 1, 0, 122, 126, 0, 1, 124, 0, 1, 2, 121, 93, 0, 0, 0, 0, 81, 0, 106, 0, 115, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1054.0, 1011.0, 139.0, 915.0, 906.0, 105.0, 334.0, 581.0, 172.0, 734.0, 179.0, 155.0, 234.0, 347.0, 324.0, 410.0, 88.0, 91.0, 93.0, 141.0, 185.0, 162.0, 225.0, 99.0, 236.0, 174.0, 90.0, 95.0, 105.0, 120.0, 113.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.006875429, 0.003837382, 0.0055584586, -0.02035911, 0.03226555, 0.00074489746, -0.01528454, -0.0646089, 0.05796133, -0.029708875, 0.046341438, -0.0015641408, -0.010813552, 0.038816355, 0.016264392, -0.007423497, -0.014468113, -0.030863436, 0.017020252, 0.0082737105, 0.066062324, 0.012850947, -0.04533157, 0.0073103034, -0.010253731, 0.0060977163, -0.005750017, -0.0009779932, 0.11595856, 0.005801105, -0.012385678, 0.0211212, 0.001660816, -0.0069775353, 0.009603017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [0.30498803, 1.3344522, 0.0, 2.9301996, 2.220424, 1.2552824, 0.0, 0.39856958, 1.4129224, 1.3887122, 3.4616902, 0.0, 0.0, 0.4959694, 0.0, 2.3394423, 0.0, 1.6617246, 0.0, 0.97408944, 1.1920372, 0.0, 1.4253979, 0.0, 0.0, 0.0, 0.0, 0.0, 1.798058, 1.4661263, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 22, 22, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0055584586, 3.0, -1.0, 1.0, -0.01528454, 1.0, 3.0, 1.0, 1.0, -0.0015641408, -0.010813552, 1.0, 0.016264392, 1.0, -0.014468113, 1.0, 0.017020252, 1.0, 1.0, 0.012850947, 1.0, 0.0073103034, -0.010253731, 0.0060977163, -0.005750017, -0.0009779932, 1.0, 1.0, -0.012385678, 0.0211212, 0.001660816, -0.0069775353, 0.009603017], "split_indices": [84, 122, 0, 0, 0, 12, 0, 127, 0, 80, 50, 0, 0, 39, 0, 127, 0, 71, 0, 23, 69, 0, 16, 0, 0, 0, 0, 0, 115, 105, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1940.0, 121.0, 1048.0, 892.0, 904.0, 144.0, 187.0, 705.0, 542.0, 362.0, 88.0, 99.0, 596.0, 109.0, 454.0, 88.0, 223.0, 139.0, 281.0, 315.0, 99.0, 355.0, 91.0, 132.0, 156.0, 125.0, 125.0, 190.0, 215.0, 140.0, 97.0, 93.0, 117.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0094814, 0.0032803277, 0.046582717, -0.019451438, 0.026824882, 0.009855702, -0.0014181325, -0.0770718, 0.030278992, -0.034296926, 0.05103739, -0.039380196, -0.013383624, -0.019611252, 0.016256375, 0.0020228396, -0.012716976, 0.10822173, -0.005963069, 0.0031165776, -0.012490941, 0.006263549, -0.010185801, 0.023069976, 0.03086718, -0.053183496, 0.0057234173, 0.021140277, -0.01496684, -0.0040518506, -0.0065567004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, -1, 21, -1, -1, -1, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [0.47393984, 0.9446445, 0.93165994, 2.573206, 1.283084, 0.0, 0.0, 0.89004946, 3.1810653, 1.2457249, 2.0241709, 1.5084358, 0.0, 2.367585, 0.0, 0.0, 0.0, 2.9370115, 0.92808646, 0.0, 0.0, 0.0, 0.0, 0.0, 6.1926885, 0.02791661, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 18, 18, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, -1, 22, -1, -1, -1, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, -0.34615386, 0.009855702, -0.0014181325, 1.0, 1.0, 1.0, 0.115384616, 1.0, -0.013383624, -0.26923078, 0.016256375, 0.0020228396, -0.012716976, 1.0, 1.0, 0.0031165776, -0.012490941, 0.006263549, -0.010185801, 0.023069976, 1.0, 1.0, 0.0057234173, 0.021140277, -0.01496684, -0.0040518506, -0.0065567004], "split_indices": [1, 124, 115, 15, 1, 0, 0, 111, 62, 0, 1, 39, 0, 1, 0, 0, 0, 69, 13, 0, 0, 0, 0, 0, 113, 97, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1765.0, 295.0, 898.0, 867.0, 159.0, 136.0, 416.0, 482.0, 246.0, 621.0, 250.0, 166.0, 350.0, 132.0, 155.0, 91.0, 310.0, 311.0, 137.0, 113.0, 175.0, 175.0, 120.0, 190.0, 178.0, 133.0, 95.0, 95.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00066568836, -0.01585047, 0.017849004, 0.00015698942, -0.018977424, 0.0010160932, 0.016852003, 0.022120316, -0.054277286, 0.022468464, -0.009206962, -0.007797616, 0.045619674, -0.0017582113, -0.007794457, 0.012753561, -0.0028319806, 0.11069497, -0.023793962, -0.07250142, 0.07576307, 0.0250656, 0.03038944, -0.010030965, 0.005502294, -0.15401939, 0.009932563, -0.0022541482, 0.016039615, -0.0022960773, 0.008798345, -0.009390872, -0.02124756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, 17, -1, -1, -1, 19, 21, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.58775467, 2.9399862, 2.574275, 1.1561043, 0.0, 1.823178, 0.0, 1.6206679, 0.24143577, 1.9724187, 0.0, 0.0, 2.5205488, 0.0, 0.0, 0.0, 3.2744527, 3.2370172, 1.6282972, 4.440216, 2.3378687, 0.0, 0.56229573, 0.0, 0.0, 0.75547504, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, 18, -1, -1, -1, 20, 22, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 0.5, -0.018977424, 1.0, 0.016852003, -0.5, 1.0, -0.42307693, -0.009206962, -0.007797616, 1.0, -0.0017582113, -0.007794457, 0.012753561, 1.0, -0.23076923, -0.15384616, 0.65384614, 1.0, 0.0250656, 0.115384616, -0.010030965, 0.005502294, -0.115384616, 0.009932563, -0.0022541482, 0.016039615, -0.0022960773, 0.008798345, -0.009390872, -0.02124756], "split_indices": [39, 88, 88, 1, 0, 7, 0, 1, 108, 1, 0, 0, 59, 0, 0, 0, 115, 1, 1, 1, 81, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1056.0, 1015.0, 967.0, 89.0, 913.0, 102.0, 689.0, 278.0, 742.0, 171.0, 131.0, 558.0, 109.0, 169.0, 144.0, 598.0, 288.0, 270.0, 317.0, 281.0, 105.0, 183.0, 137.0, 133.0, 215.0, 102.0, 130.0, 151.0, 95.0, 88.0, 106.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002719212, 0.0035193574, -0.0075446493, -4.4787517e-05, 0.006132731, -0.006062709, 0.00866634, 0.0025167828, -0.0116154775, -0.0034602962, 0.009962829, -0.07189558, 0.013282106, -0.01412645, 0.0010224952, 0.031011757, -0.03210787, 0.019176356, 0.013271813, -0.0108023435, 0.02206302, -0.0010546799, 0.07464232, -0.006603569, 0.011470294, 0.002125697, -0.011474918, 0.019604633, -0.004332569], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, 17, 19, 21, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5876862, 0.40444875, 0.0, 0.9648137, 0.0, 1.6330997, 0.0, 0.93103117, 0.0, 1.7312603, 0.0, 1.6918944, 0.9769646, 0.0, 0.0, 1.0508608, 1.4023335, 0.8775091, 0.0, 0.0, 1.62413, 1.453536, 2.9932547, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, 18, 20, 22, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0075446493, 5.0, 0.006132731, 3.0, 0.00866634, 1.0, -0.0116154775, -1.0, 0.009962829, 0.0, 0.6923077, -0.01412645, 0.0010224952, 1.0, 1.0, 1.0, 0.013271813, -0.0108023435, 1.0, 1.0, 1.0, -0.006603569, 0.011470294, 0.002125697, -0.011474918, 0.019604633, -0.004332569], "split_indices": [117, 84, 0, 0, 0, 0, 0, 102, 0, 0, 0, 1, 1, 0, 0, 73, 111, 61, 0, 0, 12, 0, 105, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1963.0, 99.0, 1849.0, 114.0, 1729.0, 120.0, 1604.0, 125.0, 1511.0, 93.0, 297.0, 1214.0, 161.0, 136.0, 873.0, 341.0, 782.0, 91.0, 142.0, 199.0, 573.0, 209.0, 102.0, 97.0, 479.0, 94.0, 103.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002380128, 0.010972073, -0.023657277, 0.045906253, -0.0071860184, 0.040489424, -0.068956755, 0.0022192039, 0.019901892, 0.021898286, -0.05710685, 0.020334965, -0.005760766, -0.1069957, 0.0037701433, 0.010349113, -0.03302193, -0.021681627, 0.08822387, 0.002903366, -0.12528224, -0.15541652, -0.0025992633, 0.0052462122, -0.008728129, 0.03391814, -0.008160954, -0.0014442594, 0.021281911, -0.02360977, -0.001796618, -0.009808018, -0.021470746, 0.0071358024, -0.00018938947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, -1, -1, 21, -1, -1, 23, 25, 27, -1, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5368779, 0.87728965, 1.9236479, 3.1639159, 1.3212407, 4.3774557, 1.5741802, 1.3133693, 0.0, 1.6620164, 1.9673425, 0.0, 0.0, 1.1217582, 0.0, 0.0, 1.2662586, 1.1561965, 2.9165206, 0.0, 2.223855, 0.6085143, 0.0, 0.0, 0.0, 0.24134398, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, -1, -1, 22, -1, -1, 24, 26, 28, -1, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.019901892, 1.0, 1.0, 0.020334965, -0.005760766, 1.0, 0.0037701433, 0.010349113, 1.0, 1.0, 1.0, 0.002903366, 1.0, -0.07692308, -0.0025992633, 0.0052462122, -0.008728129, 1.0, -0.008160954, -0.0014442594, 0.021281911, -0.02360977, -0.001796618, -0.009808018, -0.021470746, 0.0071358024, -0.00018938947], "split_indices": [121, 108, 69, 71, 15, 109, 116, 0, 0, 93, 93, 0, 0, 61, 0, 0, 111, 53, 53, 0, 50, 1, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1383.0, 662.0, 473.0, 910.0, 274.0, 388.0, 368.0, 105.0, 575.0, 335.0, 103.0, 171.0, 286.0, 102.0, 95.0, 273.0, 347.0, 228.0, 148.0, 187.0, 179.0, 107.0, 106.0, 167.0, 180.0, 167.0, 125.0, 103.0, 92.0, 95.0, 91.0, 88.0, 88.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0023999587, -0.0015086852, 0.004937745, 0.005583442, -0.059890267, -0.0104368245, 0.012950276, -0.011629898, -0.0002933866, 0.004720781, 0.011516197, -0.04190542, 0.017298643, 0.01204777, -0.017412042, 0.034131803, -0.0465011, -0.013879674, 0.012196549, 0.10313156, 0.016197523, -0.011292282, 0.0028660279, 0.000563211, 0.021377696, -0.054894604, 0.04700412, -0.017090682, 0.005011642, -0.006044821, 0.1105714, -0.008645772, 0.006878382, 0.02052789, -0.00020054173], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, -1, 11, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, 27, 29, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.38009003, 0.791249, 0.0, 1.3802327, 0.665057, 0.0, 1.3433173, 0.0, 0.0, 0.8667847, 0.0, 2.2398946, 1.2500792, 3.6974483, 0.0, 1.1397012, 1.2131407, 0.0, 0.0, 2.049694, 1.600968, 0.0, 0.0, 0.0, 0.0, 2.6923463, 1.7198106, 0.0, 0.0, 1.6727784, 2.4735541, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 19, 19, 20, 20, 25, 25, 26, 26, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, -1, 12, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, 28, 30, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 0.004937745, 1.0, 3.5, -0.0104368245, 0.88461536, -0.011629898, -0.0002933866, 1.0, 0.011516197, 1.0, 1.0, -0.42307693, -0.017412042, -0.42307693, 1.0, -0.013879674, 0.012196549, 1.0, -0.1923077, -0.011292282, 0.0028660279, 0.000563211, 0.021377696, 1.0, 1.0, -0.017090682, 0.005011642, 1.0, 1.0, -0.008645772, 0.006878382, 0.02052789, -0.00020054173], "split_indices": [31, 1, 0, 26, 1, 0, 1, 0, 0, 89, 0, 106, 7, 1, 0, 1, 97, 0, 0, 39, 1, 0, 0, 0, 0, 106, 71, 0, 0, 124, 126, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1911.0, 159.0, 1704.0, 207.0, 107.0, 1597.0, 104.0, 103.0, 1478.0, 119.0, 314.0, 1164.0, 223.0, 91.0, 921.0, 243.0, 94.0, 129.0, 190.0, 731.0, 129.0, 114.0, 101.0, 89.0, 221.0, 510.0, 105.0, 116.0, 278.0, 232.0, 134.0, 144.0, 126.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0041296594, -0.0017626896, 0.04351422, 0.0035300096, -0.009069054, 0.011725823, -0.0035335135, -0.01953273, 0.023334164, -0.008237837, -0.004170461, -0.037151895, 0.04196526, -0.02962534, 0.058487684, -0.019907976, 0.0129358815, 0.021220421, 0.017364036, 0.006679149, -0.016274182, 0.013228108, -0.0015305674, 0.014689522, -0.0022817478, -0.011574143, 0.074091375, -0.030435527, 0.009285858, 3.083658e-05, 0.018235987, 0.034203652, -0.09986281, -0.005198275, 0.014241551, -0.019412106, 0.0006045322], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, -1, -1, -1, 27, -1, 29, 31, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [0.4796838, 0.84626204, 1.564146, 0.77508456, 0.0, 0.0, 0.0, 0.7569138, 1.0288795, 0.0, 1.0048219, 5.7969856, 1.9066409, 2.165062, 0.99107444, 0.0, 0.0, 1.7810395, 0.0, 2.90493, 0.0, 0.0, 0.0, 0.0, 1.3607081, 0.0, 1.813361, 1.7591875, 0.0, 0.0, 0.0, 1.8932575, 1.886734, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 24, 24, 26, 26, 27, 27, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, -1, -1, -1, 28, -1, 30, 32, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.3076923, 1.0, 1.0, 1.0, -0.009069054, 0.011725823, -0.0035335135, -1.0, 1.0, -0.008237837, 0.34615386, -0.34615386, 1.0, 2.0, 0.61538464, -0.019907976, 0.0129358815, -1.0, 0.017364036, 1.0, -0.016274182, 0.013228108, -0.0015305674, 0.014689522, 1.0, -0.011574143, 1.0, 1.0, 0.009285858, 3.083658e-05, 0.018235987, 1.0, 1.0, -0.005198275, 0.014241551, -0.019412106, 0.0006045322], "split_indices": [1, 1, 115, 71, 0, 0, 0, 0, 89, 0, 1, 1, 125, 0, 1, 0, 0, 0, 0, 17, 0, 0, 0, 0, 42, 0, 12, 121, 0, 0, 0, 12, 126, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1798.0, 269.0, 1697.0, 101.0, 139.0, 130.0, 784.0, 913.0, 154.0, 630.0, 215.0, 698.0, 448.0, 182.0, 109.0, 106.0, 603.0, 95.0, 352.0, 96.0, 91.0, 91.0, 95.0, 508.0, 125.0, 227.0, 392.0, 116.0, 135.0, 92.0, 203.0, 189.0, 113.0, 90.0, 100.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025706736, 0.0014383494, -0.008088558, -0.0032247961, 0.048174027, -0.007689268, 0.00823024, 0.007898551, 0.0017704854, -0.02217857, 0.014044681, -0.05069988, 0.02188046, -0.014711781, 0.09187468, -0.024050128, -0.019644067, 0.1371913, -0.081464134, -0.04409425, 0.004735866, 0.016263044, 0.0014686546, 0.048604764, -0.085233204, 0.018094322, 0.009250843, -0.0035430335, -0.014036757, 0.001998833, -0.070261315, 0.010464255, -0.0012305846, -0.016955212, 0.005091474, -0.0037957882, -0.0126058115], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [0.65116626, 0.42998642, 0.0, 0.6850095, 0.16804504, 0.5369211, 0.0, 0.0, 0.0, 1.2855237, 1.526395, 2.4119358, 4.7905345, 0.9082437, 1.0049165, 2.3337564, 0.0, 0.37144184, 0.57484794, 0.5667765, 0.0, 0.0, 0.0, 0.81919134, 3.271757, 0.0, 0.0, 0.0, 0.0, 0.0, 0.43258238, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008088558, 1.0, 1.0, 1.0, 0.00823024, 0.007898551, 0.0017704854, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.019644067, 1.0, 1.0, 0.0, 0.004735866, 0.016263044, 0.0014686546, 1.0, 1.0, 0.018094322, 0.009250843, -0.0035430335, -0.014036757, 0.001998833, 1.0, 0.010464255, -0.0012305846, -0.016955212, 0.005091474, -0.0037957882, -0.0126058115], "split_indices": [117, 125, 0, 114, 58, 137, 0, 0, 0, 105, 93, 42, 109, 50, 12, 93, 0, 53, 12, 0, 0, 0, 0, 124, 12, 0, 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1973.0, 101.0, 1794.0, 179.0, 1705.0, 89.0, 89.0, 90.0, 1023.0, 682.0, 621.0, 402.0, 498.0, 184.0, 525.0, 96.0, 190.0, 212.0, 338.0, 160.0, 96.0, 88.0, 240.0, 285.0, 96.0, 94.0, 119.0, 93.0, 98.0, 240.0, 125.0, 115.0, 176.0, 109.0, 152.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-2.1772768e-05, -0.006233502, 0.028791865, -0.0634063, 0.0029841517, 0.01212827, -0.023554005, -0.011364934, 8.467822e-05, 0.022444312, -0.034998324, 0.005213003, -0.0073121735, 0.06461291, -0.009506214, 0.012645406, -0.1008789, 0.01859041, 0.029049132, -0.07841754, 0.044511363, 0.007805263, -0.024124047, -0.02078077, -0.0021573345, 0.084113106, -0.00979351, 0.0020652174, -0.020433795, -0.0043949923, 0.011186259, -0.00790924, 0.0036466033, 0.015709842, 0.00084976666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, 13, 15, -1, -1, 17, 19, 21, 23, -1, 25, 27, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.37138867, 0.8995877, 1.7816772, 0.7651005, 1.0865431, 0.0, 0.88159925, 0.0, 0.0, 1.3095846, 1.5631208, 0.0, 0.0, 1.8073865, 2.0585, 0.6950418, 1.7723293, 0.0, 2.265491, 3.0314014, 1.8469727, 0.0, 0.6161494, 0.0, 0.0, 1.2472517, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 22, 22, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, 14, 16, -1, -1, 18, 20, 22, 24, -1, 26, 28, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, 1.0, 0.46153846, 0.01212827, 1.0, -0.011364934, 8.467822e-05, 1.0, 1.0, 0.005213003, -0.0073121735, 1.0, 1.0, 0.96153843, 0.8076923, 0.01859041, 1.0, 1.0, 1.0, 0.007805263, 1.0, -0.02078077, -0.0021573345, 1.0, -0.00979351, 0.0020652174, -0.020433795, -0.0043949923, 0.011186259, -0.00790924, 0.0036466033, 0.015709842, 0.00084976666], "split_indices": [74, 1, 122, 39, 1, 0, 111, 0, 0, 53, 2, 0, 0, 50, 122, 1, 1, 0, 122, 39, 2, 0, 53, 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1707.0, 368.0, 237.0, 1470.0, 133.0, 235.0, 133.0, 104.0, 972.0, 498.0, 93.0, 142.0, 419.0, 553.0, 289.0, 209.0, 95.0, 324.0, 243.0, 310.0, 104.0, 185.0, 89.0, 120.0, 226.0, 98.0, 136.0, 107.0, 134.0, 176.0, 97.0, 88.0, 115.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0057800952, 0.005953373, -0.009369864, -0.008328982, -0.0033928365, -0.009774703, 0.009199924, -3.773083e-05, -0.052377783, 0.021779973, -0.04179389, -0.12557235, 0.006306729, 0.06434938, -0.012141207, -0.070858225, 0.0021991585, -0.0047176024, -0.019775905, 0.019595366, 0.019344747, 0.016246155, -0.014546617, 0.0070382375, -0.019042028, 0.070705, -0.006034531, -0.011830314, 0.007641004, 0.005623985, -0.0038341861, 0.0027897868, 0.011591698, -0.005118058, 0.0032512124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, 27, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.48603857, 0.0, 0.8681793, 0.0, 1.1067607, 0.7068616, 0.0, 1.2635896, 2.6786354, 1.3154885, 0.8824483, 1.0978785, 0.0, 2.3341732, 1.9188654, 3.0455017, 0.0, 0.0, 0.0, 1.2257216, 0.0, 0.7060814, 0.0, 0.44208938, 0.0, 0.35417688, 0.0, 0.49729276, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 21, 21, 23, 23, 25, 25, 27, 27], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, 28, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.005953373, -0.5, -0.008328982, 1.0, 1.0, 0.009199924, 0.46153846, 0.53846157, 1.0, 1.0, 1.0, 0.006306729, 2.0, 2.0, 1.0, 0.0021991585, -0.0047176024, -0.019775905, 1.0, 0.019344747, 1.0, -0.014546617, 1.0, -0.019042028, -0.03846154, -0.006034531, 1.0, 0.007641004, 0.005623985, -0.0038341861, 0.0027897868, 0.011591698, -0.005118058, 0.0032512124], "split_indices": [1, 0, 1, 0, 90, 74, 0, 1, 1, 53, 61, 13, 0, 0, 0, 127, 0, 0, 0, 113, 0, 121, 0, 12, 0, 1, 0, 13, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 108.0, 1965.0, 147.0, 1818.0, 1704.0, 114.0, 1387.0, 317.0, 911.0, 476.0, 194.0, 123.0, 404.0, 507.0, 327.0, 149.0, 93.0, 101.0, 300.0, 104.0, 418.0, 89.0, 198.0, 129.0, 183.0, 117.0, 285.0, 133.0, 95.0, 103.0, 94.0, 89.0, 151.0, 134.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0051077176, 0.009306638, 0.00034347587, 0.005443913, -0.0059371362, -0.025765937, 0.02220624, -0.053827524, 0.0058063595, 0.013475284, 0.0040427744, -0.00018993282, -0.12552132, 0.01820672, -0.0075293817, -0.004907283, 0.004520131, -0.0064313076, -0.01931054, 0.05608598, -0.040817074, 0.033981845, 0.014283426, 0.0008002315, -0.0833636, -0.0035860727, 0.010804432, -0.0076454617, -0.009035017, -0.008207722, 0.0068364115], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, -1, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1], "loss_changes": [0.8645128, 0.0, 0.5960468, 0.9432388, 0.0, 1.4820055, 2.3978884, 1.8150674, 0.0, 0.0, 1.1349561, 0.5990909, 0.8356128, 1.9160616, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0009326, 0.6958272, 1.1574676, 0.0, 0.0, 0.0086398125, 1.5586969, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 19, 19, 20, 20, 21, 21, 24, 24, 25, 25], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, -1, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009306638, 1.0, -0.1923077, -0.0059371362, 1.0, -0.03846154, 1.0, 0.0058063595, 0.013475284, 1.0, 1.0, 1.0, 1.0, -0.0075293817, -0.004907283, 0.004520131, -0.0064313076, -0.01931054, 1.0, 1.0, 1.2692307, 0.014283426, 0.0008002315, 0.7307692, 1.0, 0.010804432, -0.0076454617, -0.009035017, -0.008207722, 0.0068364115], "split_indices": [1, 0, 41, 1, 0, 61, 1, 108, 0, 0, 64, 115, 109, 109, 0, 0, 0, 0, 0, 0, 97, 1, 0, 0, 1, 124, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 106.0, 1957.0, 1803.0, 154.0, 630.0, 1173.0, 472.0, 158.0, 163.0, 1010.0, 270.0, 202.0, 857.0, 153.0, 130.0, 140.0, 106.0, 96.0, 522.0, 335.0, 416.0, 106.0, 156.0, 179.0, 276.0, 140.0, 90.0, 89.0, 132.0, 144.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033042356, 0.0066665164, -0.007207341, 0.0002471213, -0.035718367, -0.00567485, 0.009403315, -0.012913286, -0.0034422511, 0.002270764, -0.011235065, 0.0066152522, -0.005853813, -0.00774242, 0.012157072, 0.004966989, -0.009081298, 0.04314204, -0.032030243, -0.005422354, 0.015666133, 0.018702954, -0.12106702, 0.009124711, -0.065945856, -0.0707446, 0.0139967445, -0.017341409, -0.005708722, -0.0154217845, 0.0017837696, -0.012812947, -7.4764095e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.56340086, 0.0, 0.41529226, 0.86031216, 1.2210977, 1.2349603, 0.0, 0.0, 1.1541499, 1.6198406, 0.0, 0.0, 0.0, 1.3207781, 0.0, 1.5324228, 0.0, 2.9439402, 2.4889336, 2.1881897, 0.0, 3.8072312, 0.66983056, 0.0, 1.7010207, 0.81138647, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0066665164, 1.0, 3.5, 1.0, 1.3461539, 0.009403315, -0.012913286, 1.0, 0.88461536, -0.011235065, 0.0066152522, -0.005853813, 0.46153846, 0.012157072, 1.0, -0.009081298, 1.0, 1.0, 1.0, 0.015666133, 1.0, 1.0, 0.009124711, 1.0, 1.0, 0.0139967445, -0.017341409, -0.005708722, -0.0154217845, 0.0017837696, -0.012812947, -7.4764095e-05], "split_indices": [1, 0, 116, 1, 69, 1, 0, 0, 126, 1, 0, 0, 0, 1, 0, 69, 0, 105, 121, 126, 0, 50, 59, 0, 83, 111, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 109.0, 1954.0, 1549.0, 405.0, 1457.0, 92.0, 104.0, 301.0, 1356.0, 101.0, 133.0, 168.0, 1251.0, 105.0, 1085.0, 166.0, 534.0, 551.0, 374.0, 160.0, 351.0, 200.0, 144.0, 230.0, 202.0, 149.0, 110.0, 90.0, 112.0, 118.0, 111.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0046303147, -0.0035309813, 0.029336069, -0.009902807, 0.006054878, -0.024132557, 0.091082275, -0.018620735, 0.008107296, 0.0061436775, -0.014943053, -0.004282452, 0.019962797, -0.029802106, 0.0067303204, -0.09148051, -0.017219966, -0.021198774, 0.0014987012, 0.024556493, -0.04132977, -0.027364489, 0.010356671, 0.015995048, -0.08056045, 0.00522177, -0.009834323, 0.012040409, -0.0050130663, -0.12906514, -0.00148238, -0.020029968, -0.0049018073], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, 15, -1, 17, 19, -1, -1, 21, 23, 25, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.41818273, 0.6365475, 1.7002649, 1.1246442, 0.0, 2.9591794, 3.473867, 1.2432071, 0.0, 0.0, 0.0, 0.0, 0.0, 0.8885726, 0.0, 2.4890409, 0.95786846, 0.0, 0.0, 1.4275964, 1.3560818, 1.1862153, 0.0, 1.6915102, 1.1414955, 0.0, 0.0, 0.0, 0.0, 1.1746352, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 13, 13, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 23, 23, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, 16, -1, 18, 20, -1, -1, 22, 24, 26, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.07692308, 1.0, 0.006054878, 1.0, 1.0, 1.0, 0.008107296, 0.0061436775, -0.014943053, -0.004282452, 0.019962797, -0.46153846, 0.0067303204, 1.0, -0.07692308, -0.021198774, 0.0014987012, 1.0, 1.0, -0.30769232, 0.010356671, 1.0, 1.0, 0.00522177, -0.009834323, 0.012040409, -0.0050130663, 0.9230769, -0.00148238, -0.020029968, -0.0049018073], "split_indices": [0, 31, 1, 73, 0, 126, 39, 40, 0, 0, 0, 0, 0, 1, 0, 39, 1, 0, 0, 111, 126, 1, 0, 109, 122, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1559.0, 515.0, 1418.0, 141.0, 276.0, 239.0, 1294.0, 124.0, 164.0, 112.0, 107.0, 132.0, 1145.0, 149.0, 194.0, 951.0, 91.0, 103.0, 348.0, 603.0, 210.0, 138.0, 245.0, 358.0, 99.0, 111.0, 95.0, 150.0, 206.0, 152.0, 109.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [9.75062e-05, -0.050346456, 0.004938631, -0.0057670115, -0.0042941393, -0.0018566296, 0.04002528, -0.020377124, 0.024780866, 0.0041487254, 0.012890125, -0.0063507045, -0.010177128, 0.096955895, -0.017956676, 0.0070079505, -0.0054902295, 0.009066693, -0.010656382, 0.0016753002, 0.017782712, -0.009526094, 0.040354714, 0.009070901, -0.017884722, -0.0043712384, 0.0102931, 0.07753719, -0.07315962, -0.0044279615, 0.019935403, -0.003459078, -0.010771549], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, -1, -1, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, -1, -1, 23, -1, -1, -1, -1, 25, -1, 27, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [0.50477266, 0.009815693, 0.44966573, 0.0, 0.0, 0.7794765, 0.97570014, 1.0640348, 1.9988102, 0.84873503, 0.0, 1.2282956, 0.0, 1.5631511, 1.8346419, 0.0, 0.0, 1.5160593, 0.0, 0.0, 0.0, 0.0, 1.220461, 0.0, 2.732158, 0.0, 0.0, 2.8194745, 0.43715143, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 22, 22, 24, 24, 27, 27, 28, 28], "right_children": [2, 4, 6, -1, -1, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, -1, -1, 24, -1, -1, -1, -1, 26, -1, 28, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [-0.53846157, 1.0, 1.0, -0.0057670115, -0.0042941393, 1.0, 1.0, 1.0, 1.0, 1.0, 0.012890125, 1.0, -0.010177128, 1.0, 1.0, 0.0070079505, -0.0054902295, 0.115384616, -0.010656382, 0.0016753002, 0.017782712, -0.009526094, -0.1923077, 0.009070901, 1.0, -0.0043712384, 0.0102931, 0.5769231, 1.0, -0.0044279615, 0.019935403, -0.003459078, -0.010771549], "split_indices": [1, 108, 42, 0, 0, 15, 0, 113, 53, 69, 0, 64, 0, 113, 111, 0, 0, 1, 0, 0, 0, 0, 1, 0, 124, 0, 0, 1, 12, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 181.0, 1886.0, 91.0, 90.0, 1580.0, 306.0, 932.0, 648.0, 218.0, 88.0, 795.0, 137.0, 241.0, 407.0, 103.0, 115.0, 689.0, 106.0, 121.0, 120.0, 175.0, 232.0, 171.0, 518.0, 99.0, 133.0, 190.0, 328.0, 95.0, 95.0, 155.0, 173.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003253765, 0.006829109, -0.0057589235, -0.008391968, 0.02050003, 0.008695074, -0.010273172, 0.007195597, 0.0779011, 0.029568288, -0.01139808, 0.030845545, -0.009686416, 0.019640295, -0.0064839716, 0.01750327, 0.0040482096, -0.0068553546, 0.07105985, 0.0962354, -0.0587696, -0.013700234, 0.037186768, 0.02411975, 0.016367137, 0.017545355, -0.00022104172, -0.00066395587, -0.010648183, -0.005682236, 0.018797372, 0.010734714, -0.009077019], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, -1, -1, -1, 19, 21, 23, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.45073164, 0.40722466, 0.0, 1.4927006, 0.7873628, 2.0075414, 0.0, 2.0598633, 3.28152, 2.4872158, 0.0, 1.0339911, 0.0, 0.0, 0.0, 0.0, 3.3008685, 2.0176463, 1.4345734, 1.8014991, 0.8431742, 0.0, 3.7281172, 2.094076, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, -1, -1, -1, 20, 22, 24, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0057589235, 1.0, 1.0, 1.0, -0.010273172, 1.0, -0.03846154, -0.3846154, -0.01139808, 1.0, -0.009686416, 0.019640295, -0.0064839716, 0.01750327, 1.0, 1.0, 0.42307693, 1.0, 0.6923077, -0.013700234, 1.0, -0.23076923, 0.016367137, 0.017545355, -0.00022104172, -0.00066395587, -0.010648183, -0.005682236, 0.018797372, 0.010734714, -0.009077019], "split_indices": [43, 13, 0, 64, 64, 7, 0, 58, 1, 1, 0, 97, 0, 0, 0, 0, 124, 89, 1, 97, 1, 0, 109, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1957.0, 115.0, 926.0, 1031.0, 784.0, 142.0, 837.0, 194.0, 670.0, 114.0, 682.0, 155.0, 106.0, 88.0, 100.0, 570.0, 352.0, 330.0, 231.0, 339.0, 89.0, 263.0, 219.0, 111.0, 128.0, 103.0, 162.0, 177.0, 162.0, 101.0, 127.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009203519, 0.0047661364, -0.0449787, 0.011402148, -0.0008398945, -0.008962884, 0.0025879077, -0.0056185466, 0.007345459, -0.03555249, 0.012110443, -0.011881599, -0.013805771, 0.071992114, -0.010644588, -0.036511935, 0.0082574515, 0.02132727, -0.01042154, 0.023907145, -0.06483876, -0.10719349, 0.037465636, -0.008503492, 0.006758332, 0.0741881, -0.023391027, -0.016128091, 0.0015832953, 0.0007734689, -0.020141391, -0.0013025928, 0.009289662, 0.012528143, 0.00046141754, 0.0018053887, -0.0077838297], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, -1, 11, 13, 15, -1, 17, 19, 21, -1, -1, 23, 25, 27, 29, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5216187, 1.1294297, 0.75298667, 0.0, 0.622718, 0.0, 0.0, 0.87459123, 0.0, 1.4873765, 1.4103009, 1.1585898, 0.0, 3.3183835, 1.404377, 2.065396, 0.0, 0.0, 1.0476371, 1.0892146, 2.2718048, 2.1873744, 0.54016817, 0.0, 0.0, 0.78915703, 0.5325489, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 25, 25, 26, 26], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, -1, 12, 14, 16, -1, 18, 20, 22, -1, -1, 24, 26, 28, 30, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, 1.0, 0.011402148, 1.0, -0.008962884, 0.0025879077, -0.115384616, 0.007345459, 3.0, 0.1923077, 1.0, -0.013805771, 1.0, 1.0, 1.0, 0.0082574515, 0.02132727, 1.0, 1.0, 1.0, 1.0, -0.30769232, -0.008503492, 0.006758332, 1.0, 1.0, -0.016128091, 0.0015832953, 0.0007734689, -0.020141391, -0.0013025928, 0.009289662, 0.012528143, 0.00046141754, 0.0018053887, -0.0077838297], "split_indices": [119, 1, 58, 0, 90, 0, 0, 1, 0, 0, 1, 61, 0, 69, 127, 111, 0, 0, 111, 12, 39, 108, 1, 0, 0, 80, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2082.0, 1844.0, 238.0, 90.0, 1754.0, 146.0, 92.0, 1648.0, 106.0, 613.0, 1035.0, 498.0, 115.0, 285.0, 750.0, 395.0, 103.0, 105.0, 180.0, 458.0, 292.0, 202.0, 193.0, 92.0, 88.0, 222.0, 236.0, 133.0, 159.0, 91.0, 111.0, 101.0, 92.0, 128.0, 94.0, 134.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005714923, -0.0043474543, 0.0060315873, -0.02172829, 0.01572373, -0.0016122988, -0.015168625, 0.00043100092, 0.01264206, -0.03876698, 0.05365657, -0.048254516, 0.01566042, -0.06380129, 0.0056229257, -0.01730798, 0.01683312, 0.0006300788, -0.010765921, 0.0018942169, 0.006861586, -0.016932635, -0.01882919, 0.007376242, -0.0080091385, 0.02771804, -0.0053321174, 0.01004452, -0.07727365, -0.004104314, 0.010893933, -0.0011061344, -0.012929759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.47246152, 0.6750324, 0.0, 2.7109592, 1.5201855, 1.8440405, 0.0, 0.5850056, 0.0, 1.2770748, 2.937758, 0.60927844, 0.43812603, 2.0169148, 0.0, 1.2750494, 0.0, 0.0, 0.0, 0.68014103, 0.0, 0.0, 2.0773365, 0.0, 0.0, 1.8150833, 0.0, 0.0, 0.68892455, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 19, 19, 22, 22, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0060315873, 3.0, 3.0, 1.0, -0.015168625, -1.0, 0.01264206, 1.0, 1.0, -0.115384616, 1.0, 1.0, 0.0056229257, 1.0, 0.01683312, 0.0006300788, -0.010765921, 0.6923077, 0.006861586, -0.016932635, 1.0, 0.007376242, -0.0080091385, 1.0, -0.0053321174, 0.01004452, -0.34615386, -0.004104314, 0.010893933, -0.0011061344, -0.012929759], "split_indices": [84, 122, 0, 0, 0, 12, 0, 0, 0, 116, 50, 1, 121, 17, 0, 71, 0, 0, 0, 1, 0, 0, 124, 0, 0, 124, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1935.0, 120.0, 1037.0, 898.0, 898.0, 139.0, 789.0, 109.0, 537.0, 361.0, 188.0, 601.0, 425.0, 112.0, 223.0, 138.0, 98.0, 90.0, 477.0, 124.0, 127.0, 298.0, 91.0, 132.0, 325.0, 152.0, 98.0, 200.0, 176.0, 149.0, 88.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00078489794, -0.002086395, 0.0056409193, 0.014186979, -0.01697212, 0.044345133, -0.034736242, -0.027383894, 0.008454265, 0.08627422, -0.027089596, -0.016348748, 0.024166128, -0.0108748, -0.012619204, 0.13255316, -0.0060976925, -0.006518566, 0.0010307428, 0.009860137, -0.009291426, -0.055999223, 0.027004872, 0.024013422, 0.005645923, -0.0087796245, -0.01470386, 0.072821766, -0.007966803, -0.0074360617, 0.0036163968, -0.0028736538, 0.1277043, 0.019792903, 0.007036482], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, -1, 19, 21, -1, 23, -1, -1, -1, -1, -1, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.3318851, 0.47866815, 0.0, 1.3928099, 1.0907711, 1.7491931, 2.7301514, 1.5268335, 0.0, 2.5077832, 0.3077309, 0.0, 2.152582, 1.3708575, 0.0, 2.2921543, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5733758, 2.1309159, 0.0, 0.0, 0.7103344, 0.0, 1.7000022, 0.0, 0.0, 0.0, 0.0, 0.7972758, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 21, 21, 22, 22, 25, 25, 27, 27, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, -1, 20, 22, -1, 24, -1, -1, -1, -1, -1, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 0.0056409193, 1.0, 1.0, 1.0, -0.3846154, 1.0, 0.008454265, 1.0, 1.0, -0.016348748, 1.0, 1.0, -0.012619204, 1.0, -0.0060976925, -0.006518566, 0.0010307428, 0.009860137, -0.009291426, 0.5769231, 1.0, 0.024013422, 0.005645923, -0.15384616, -0.01470386, 1.0, -0.007966803, -0.0074360617, 0.0036163968, -0.0028736538, 0.3846154, 0.019792903, 0.007036482], "split_indices": [66, 126, 0, 105, 88, 80, 1, 61, 0, 23, 12, 0, 39, 81, 0, 69, 0, 0, 0, 0, 0, 1, 109, 0, 0, 1, 0, 5, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1976.0, 102.0, 944.0, 1032.0, 584.0, 360.0, 936.0, 96.0, 368.0, 216.0, 113.0, 247.0, 802.0, 134.0, 280.0, 88.0, 107.0, 109.0, 151.0, 96.0, 366.0, 436.0, 116.0, 164.0, 241.0, 125.0, 305.0, 131.0, 98.0, 143.0, 107.0, 198.0, 89.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0044533405, 0.0019734818, 0.005287193, -0.0052061835, 0.005630449, -0.0060177003, 0.048174072, 0.016844781, -0.038222663, 0.015924979, 0.0028772287, 0.03197425, -0.005901713, -0.011700227, -0.016311554, -0.06636273, 0.013493286, 0.019644242, 0.01085964, -0.009032171, 0.0018972852, 0.0027292033, -0.016738364, 0.010314288, -0.002356429, 0.013190942, -0.039326064, 0.017181607, -0.0053097024, -0.08776466, 0.005857085, 0.05389766, -0.0033617297, -0.011577207, -0.0057321787, 0.0008780967, 0.010893008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, -1, 23, -1, -1, 25, -1, -1, -1, 27, -1, 29, 31, -1, 33, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [0.24890752, 0.38967764, 0.0, 0.0, 0.91528916, 1.0676138, 1.9974573, 0.9732922, 1.0391409, 0.0, 2.5784748, 0.667939, 0.0, 0.0, 0.6347383, 1.7503021, 0.0, 1.1187491, 0.0, 0.0, 2.025905, 0.0, 0.0, 0.0, 0.47784126, 0.0, 1.3609506, 0.64906716, 0.0, 0.1637038, 0.0, 0.5015416, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 14, 14, 15, 15, 17, 17, 20, 20, 24, 24, 26, 26, 27, 27, 29, 29, 31, 31], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, -1, 24, -1, -1, 26, -1, -1, -1, 28, -1, 30, 32, -1, 34, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.005287193, -0.0052061835, 1.0, 1.0, 1.0, 1.3461539, 0.0, 0.015924979, 1.0, 0.88461536, -0.005901713, -0.011700227, -0.3846154, -0.30769232, 0.013493286, 1.0, 0.01085964, -0.009032171, -0.07692308, 0.0027292033, -0.016738364, 0.010314288, 0.1923077, 0.013190942, 1.0, 1.0, -0.0053097024, 1.0, 0.005857085, -0.07692308, -0.0033617297, -0.011577207, -0.0057321787, 0.0008780967, 0.010893008], "split_indices": [66, 104, 0, 0, 113, 109, 69, 1, 0, 0, 111, 1, 0, 0, 1, 1, 0, 53, 0, 0, 1, 0, 0, 0, 1, 0, 61, 7, 0, 53, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1972.0, 101.0, 125.0, 1847.0, 1450.0, 397.0, 848.0, 602.0, 115.0, 282.0, 707.0, 141.0, 131.0, 471.0, 185.0, 97.0, 609.0, 98.0, 93.0, 378.0, 96.0, 89.0, 127.0, 482.0, 91.0, 287.0, 348.0, 134.0, 192.0, 95.0, 202.0, 146.0, 100.0, 92.0, 111.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.005814653, 0.0075764637, -0.018342234, -0.056944553, 0.02951534, -0.0050779926, -0.011020936, 0.005722597, -0.015758378, 0.0041001784, 0.011512429, -0.065037586, 0.02324937, 0.047266357, -0.07705222, -0.00088405516, -0.013189971, 0.011402885, -0.009144157, 0.015079965, 0.01026347, -0.011710059, -0.002810418, -0.054052573, 0.046507884, 0.008679306, -0.007259561, -0.012416111, -0.0012073984, -0.002622547, 0.014651628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, 13, -1, 15, 17, 19, 21, -1, -1, -1, 23, -1, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.34742746, 1.4169341, 1.3038456, 2.9184685, 1.6252964, 1.5880948, 0.0, 0.0, 0.0, 2.0177503, 0.0, 1.1272357, 1.8673241, 1.4404671, 0.39205742, 0.0, 0.0, 0.0, 1.1696467, 0.0, 1.7565045, 0.0, 0.0, 0.7622516, 1.5202551, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, 14, -1, 16, 18, 20, 22, -1, -1, -1, 24, -1, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, -0.115384616, -0.011020936, 0.005722597, -0.015758378, 1.0, 0.011512429, 1.0, 0.1923077, -0.34615386, -0.115384616, -0.00088405516, -0.013189971, 0.011402885, 1.0, 0.015079965, 1.0, -0.011710059, -0.002810418, 1.0, 1.2692307, 0.008679306, -0.007259561, -0.012416111, -0.0012073984, -0.002622547, 0.014651628], "split_indices": [126, 89, 64, 93, 0, 1, 0, 0, 0, 105, 0, 93, 1, 1, 1, 0, 0, 0, 122, 0, 109, 0, 0, 59, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1001.0, 1070.0, 254.0, 747.0, 935.0, 135.0, 119.0, 135.0, 576.0, 171.0, 300.0, 635.0, 376.0, 200.0, 163.0, 137.0, 167.0, 468.0, 99.0, 277.0, 110.0, 90.0, 259.0, 209.0, 144.0, 133.0, 97.0, 162.0, 121.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026683898, 0.024551112, -0.011157302, -0.0014934086, 0.011441647, -0.02181101, 0.0335184, -0.07276349, 0.06440454, -0.04561121, 0.0037250181, -0.004668892, 0.009979753, -0.017534947, 0.002761631, 0.00045348943, 0.012994601, -0.071966164, 0.0069794296, -0.022871785, 0.011388014, -0.12958173, -0.014088691, -0.010202977, 0.013415686, 0.008548779, -0.010220316, -0.0034911726, -0.019709831, 0.009270917, -0.009987717, -0.032809123, 0.0075451294, 0.0041481783, -0.009941479], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, 21, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.48015088, 1.1562073, 0.7539236, 1.7987796, 0.0, 0.77732855, 1.6214018, 1.8947515, 0.7808647, 0.9175482, 1.8076708, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4705772, 3.0638359, 1.2388407, 0.0, 1.4125857, 2.0156457, 0.0, 0.0, 0.9850335, 0.0, 0.0, 0.0, 0.0, 0.0, 1.088603, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 25, 25, 31, 31], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, 22, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [0.0, 1.0, 1.0, 1.0, 0.011441647, 1.0, -0.26923078, 0.03846154, 1.0, 1.0, 1.0, -0.004668892, 0.009979753, -0.017534947, 0.002761631, 0.00045348943, 0.012994601, 0.07692308, 1.0, 1.0, 0.011388014, -0.34615386, 0.6923077, -0.010202977, 0.013415686, 1.0, -0.010220316, -0.0034911726, -0.019709831, 0.009270917, -0.009987717, 1.0, 0.0075451294, 0.0041481783, -0.009941479], "split_indices": [0, 58, 42, 13, 0, 97, 1, 1, 111, 105, 62, 0, 0, 0, 0, 0, 0, 1, 126, 105, 0, 1, 1, 0, 0, 23, 0, 0, 0, 0, 0, 122, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 494.0, 1584.0, 383.0, 111.0, 1279.0, 305.0, 184.0, 199.0, 662.0, 617.0, 138.0, 167.0, 91.0, 93.0, 104.0, 95.0, 441.0, 221.0, 497.0, 120.0, 221.0, 220.0, 119.0, 102.0, 356.0, 141.0, 92.0, 129.0, 98.0, 122.0, 220.0, 136.0, 104.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.003723019, -0.0072927126, 0.006588598, -0.014540898, 0.033208963, 0.0065731737, -0.03986656, 0.009573131, 0.001789088, -0.010816241, 0.008037711, -0.077688314, 0.0019810328, -0.0036416508, 0.0033924666, -0.060051255, 0.021448875, -0.14009479, -0.0046521467, -0.0070747286, 0.049456477, 0.0017177047, -0.015583831, -0.028857462, 0.09171372, -0.021792244, -0.0033730292, 0.0078958, -0.0088262325, 0.012631188, -0.0012027822, -0.01078137, 0.0015916486, 0.0050365776, 0.013217242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, -1, -1, 21, 23, 25, 27, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5093895, 0.572449, 0.0, 0.8844398, 0.5814752, 1.1576334, 1.1902277, 0.0, 0.24186853, 1.1596587, 0.0, 1.8003817, 1.2326528, 0.0, 0.0, 2.1378698, 1.5588324, 1.7632346, 1.2723, 0.0, 1.0206871, 0.0, 0.0, 0.9085422, 0.3078102, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, -1, -1, 22, 24, 26, 28, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.2692307, 0.006588598, -0.03846154, 1.3846154, -0.1923077, 1.0, 0.009573131, 1.0, 1.0, 0.008037711, 1.0, 0.34615386, -0.0036416508, 0.0033924666, -0.3846154, -0.3846154, 1.0, 1.0, -0.0070747286, 0.7307692, 0.0017177047, -0.015583831, 1.0, 1.0, -0.021792244, -0.0033730292, 0.0078958, -0.0088262325, 0.012631188, -0.0012027822, -0.01078137, 0.0015916486, 0.0050365776, 0.013217242], "split_indices": [66, 1, 0, 1, 1, 1, 137, 0, 23, 13, 0, 12, 1, 0, 0, 1, 1, 15, 111, 0, 1, 0, 0, 89, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1950.0, 100.0, 1654.0, 296.0, 902.0, 752.0, 99.0, 197.0, 730.0, 172.0, 395.0, 357.0, 90.0, 107.0, 289.0, 441.0, 213.0, 182.0, 141.0, 216.0, 160.0, 129.0, 257.0, 184.0, 123.0, 90.0, 91.0, 91.0, 96.0, 120.0, 93.0, 164.0, 91.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001414065, -0.00767604, 0.021072118, -0.0014007692, -0.00818383, 0.10607427, -0.014933155, 0.02977612, -0.019590456, 0.00092587475, 0.02134132, 0.020096978, -0.011941436, 0.057249643, -0.0068081915, 0.022423724, -0.04989, -0.02764011, 0.007868342, 0.09147763, -0.005075864, -0.038485166, 0.012913614, -0.018151965, -0.015832825, -0.0056482432, 3.0085948e-05, 0.0024920823, 0.01639071, -0.012558023, 0.009116768, -0.075747594, 0.044324633, -0.001798006, -0.012462779, 0.006962991, 0.0018731765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, 17, -1, 19, -1, 21, 23, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [0.3684666, 0.65619713, 1.9954631, 0.73722726, 0.0, 2.0160618, 1.6762761, 1.2877939, 1.0451417, 0.0, 0.0, 0.9592841, 0.0, 1.382643, 0.0, 2.2359087, 1.6416507, 0.15231197, 0.0, 1.3690705, 0.0, 2.4729755, 0.0, 1.3278021, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.54214764, 0.11463103, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 23, 23, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, 18, -1, 20, -1, 22, 24, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [0.42307693, 1.0, 1.0, -0.34615386, -0.00818383, 0.7307692, 1.0, 1.0, 1.0, 0.00092587475, 0.02134132, 1.0, -0.011941436, 1.0, -0.0068081915, 0.03846154, 1.0, 1.0, 0.007868342, -0.5, -0.005075864, 1.0, 0.012913614, 1.0, -0.015832825, -0.0056482432, 3.0085948e-05, 0.0024920823, 0.01639071, -0.012558023, 0.009116768, 1.0, 1.0, -0.001798006, -0.012462779, 0.006962991, 0.0018731765], "split_indices": [1, 88, 124, 1, 0, 1, 2, 7, 69, 0, 0, 13, 0, 64, 0, 1, 0, 53, 0, 1, 0, 108, 0, 124, 0, 0, 0, 0, 0, 0, 0, 121, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1410.0, 652.0, 1300.0, 110.0, 194.0, 458.0, 479.0, 821.0, 102.0, 92.0, 343.0, 115.0, 374.0, 105.0, 344.0, 477.0, 189.0, 154.0, 284.0, 90.0, 219.0, 125.0, 369.0, 108.0, 93.0, 96.0, 148.0, 136.0, 131.0, 88.0, 192.0, 177.0, 88.0, 104.0, 89.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00026557234, -0.0030349018, 0.005406864, -0.0068687657, 0.0056341635, -0.02589362, 0.015115507, -0.036898974, 0.00637796, 0.03797422, -0.048123557, -0.0062688165, -0.05655713, -0.007986064, 0.13490038, 0.004654946, -0.012266926, 0.007053427, -0.05010621, -0.030013002, -0.09808788, -0.023459803, 0.0017998122, 0.0063503087, 0.020629762, -0.014813937, 0.008522214, -0.008159117, 0.0023166945, -0.017330565, -0.0042301337, -0.005237995, 0.0005677763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, 19, 21, 23, -1, -1, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.31026763, 0.44663256, 0.0, 0.77083033, 0.0, 0.975043, 1.2359567, 0.52987623, 0.0, 2.7975855, 1.6020452, 1.1581953, 0.59088457, 0.17128293, 1.0297081, 0.0, 0.0, 0.0, 2.9054, 0.8969363, 0.87699246, 0.22499107, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, 20, 22, 24, -1, -1, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.005406864, 1.0, 0.0056341635, 1.0, 1.0, 1.0, 0.00637796, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.004654946, -0.012266926, 0.007053427, 1.0, 1.0, 1.0, 1.0, 0.0017998122, 0.0063503087, 0.020629762, -0.014813937, 0.008522214, -0.008159117, 0.0023166945, -0.017330565, -0.0042301337, -0.005237995, 0.0005677763], "split_indices": [66, 84, 0, 122, 0, 74, 50, 69, 0, 71, 17, 109, 121, 80, 39, 0, 0, 0, 42, 93, 61, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1962.0, 100.0, 1843.0, 119.0, 988.0, 855.0, 880.0, 108.0, 628.0, 227.0, 344.0, 536.0, 426.0, 202.0, 100.0, 127.0, 125.0, 219.0, 327.0, 209.0, 267.0, 159.0, 101.0, 101.0, 127.0, 92.0, 166.0, 161.0, 89.0, 120.0, 134.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0037658766, 0.006749458, 0.00027612515, 0.004167032, -0.006194452, 0.011689523, -0.025902485, 0.004941808, 0.00743536, 0.00077018276, -0.008035003, 0.054591533, -0.009547409, -0.005324361, 0.0077554523, 0.1024929, -0.0053707184, -0.023692835, 0.011349108, 0.02132432, 0.0019662816, 0.009612913, -0.036494944, 0.008237871, -0.07606629, 0.051463775, -0.0075613856, -0.15974368, -0.0036871044, 0.012006521, -0.005871427, -0.025290905, -0.0058022295, 0.010703532, -0.010289442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, 19, -1, 21, -1, -1, -1, -1, 23, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.45836073, 0.0, 0.47305292, 0.41597754, 0.0, 0.62199646, 0.5344323, 0.9553442, 0.0, 1.0244112, 0.0, 1.5562971, 1.7891637, 0.0, 0.0, 1.9080784, 0.0, 1.414324, 0.0, 0.0, 0.0, 0.0, 1.474525, 1.4172056, 2.676973, 1.9500604, 0.0, 1.9427652, 2.6033204, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 22, 22, 23, 23, 24, 24, 25, 25, 27, 27, 28, 28], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, 20, -1, 22, -1, -1, -1, -1, 24, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.006749458, 1.0, 1.0, -0.006194452, 1.0, 1.0, 0.0, 0.00743536, 0.65384614, -0.008035003, 1.0, 1.0, -0.005324361, 0.0077554523, 1.0, -0.0053707184, -0.46153846, 0.011349108, 0.02132432, 0.0019662816, 0.009612913, 1.0, 1.0, 0.03846154, 1.0, -0.0075613856, 1.0, 1.0, 0.012006521, -0.005871427, -0.025290905, -0.0058022295, 0.010703532, -0.010289442], "split_indices": [1, 0, 43, 80, 0, 88, 116, 0, 0, 1, 0, 105, 125, 0, 0, 108, 0, 1, 0, 0, 0, 0, 39, 0, 1, 106, 0, 97, 124, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 107.0, 1954.0, 1839.0, 115.0, 1471.0, 368.0, 1328.0, 143.0, 247.0, 121.0, 300.0, 1028.0, 145.0, 102.0, 208.0, 92.0, 922.0, 106.0, 89.0, 119.0, 89.0, 833.0, 391.0, 442.0, 258.0, 133.0, 205.0, 237.0, 159.0, 99.0, 107.0, 98.0, 112.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0037241601, -0.0036011527, 0.026045503, -0.014944736, 0.034063328, -0.030339392, 0.09230981, -0.03022303, 0.056499466, -0.034183364, 0.10423791, 0.00321025, -0.012121468, -0.0022778176, 0.02093821, -0.07055867, 0.010442541, 0.01720077, -0.0061230045, -0.005632958, -0.0012037114, 0.014663206, 0.006232006, 0.00048265234, -0.13849898, 0.011108336, -0.032395817, 0.0074009905, -0.0041807513, -0.021732481, -0.008749402, -0.07548725, 0.0016316219, -0.0003811534, -0.015038431], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, -1, -1, -1, 23, 25, -1, -1, -1, -1, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.33683237, 0.662665, 1.9017801, 1.3011222, 1.719317, 1.560467, 3.1528268, 1.6107469, 2.8557332, 0.08926271, 0.3145411, 0.0, 0.0, 0.0, 0.0, 2.3794994, 2.1082199, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7493847, 1.0131674, 0.0, 0.7199816, 0.0, 0.0, 0.0, 0.0, 0.9770303, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 15, 15, 16, 16, 23, 23, 24, 24, 26, 26, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, -1, -1, -1, 24, 26, -1, -1, -1, -1, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.07692308, 1.0, 1.0, 1.0, 1.0, 1.0, -0.115384616, 0.42307693, 0.15384616, 0.00321025, -0.012121468, -0.0022778176, 0.02093821, 1.0, 0.0, 0.01720077, -0.0061230045, -0.005632958, -0.0012037114, 0.014663206, 0.006232006, -0.15384616, 1.0, 0.011108336, 0.30769232, 0.0074009905, -0.0041807513, -0.021732481, -0.008749402, 1.0, 0.0016316219, -0.0003811534, -0.015038431], "split_indices": [0, 116, 1, 64, 71, 126, 71, 111, 1, 1, 1, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 1, 109, 0, 1, 0, 0, 0, 0, 106, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1551.0, 509.0, 1192.0, 359.0, 275.0, 234.0, 982.0, 210.0, 182.0, 177.0, 163.0, 112.0, 118.0, 116.0, 493.0, 489.0, 106.0, 104.0, 91.0, 91.0, 88.0, 89.0, 241.0, 252.0, 146.0, 343.0, 88.0, 153.0, 99.0, 153.0, 182.0, 161.0, 93.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "-2.1938986E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}