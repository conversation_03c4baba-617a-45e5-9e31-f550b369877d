{"learner": {"attributes": {"best_iteration": "13", "best_score": "0.680782"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "64"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.0025553266, 0.14158118, -0.23342724, 0.09290083, 0.047136083, -0.2668796, -0.002912891, -0.11303237, 0.1708284, -0.23611382, -0.045084637, -0.19215654, 0.008208057, 0.10758722, 0.36502355, -0.1890493, -0.036584288, -0.011825674, -0.025189219, 0.16288668, -0.006428946, 0.05214258, 0.021481544, -0.251907, -0.13387726, 0.24592939, 0.068256594, -0.020125756, -0.029135078, -0.02049754, -0.005422097, 0.042221684, 0.009501662, 0.017129606, -0.004378628], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [68.91682, 20.468586, 5.4400673, 17.829187, 0.0, 3.8713608, 0.0, 4.7086353, 9.898592, 3.5779, 0.0, 0.9579344, 0.0, 5.77885, 4.6515903, 1.4912338, 0.0, 0.0, 0.0, 3.6148357, 0.0, 0.0, 0.0, 0.40155792, 1.2969208, 6.5179853, 2.4821405, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 23, 23, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.047136083, 1.0, -0.002912891, 1.0, 2.0, 1.0, -0.045084637, 1.0, 0.008208057, 1.0, 1.0, 1.0, -0.036584288, -0.011825674, -0.025189219, 1.0, -0.006428946, 0.05214258, 0.021481544, 1.0, 1.0, 1.0, 1.0, -0.020125756, -0.029135078, -0.02049754, -0.005422097, 0.042221684, 0.009501662, 0.017129606, -0.004378628], "split_indices": [137, 125, 71, 17, 0, 40, 0, 39, 0, 116, 0, 126, 0, 7, 69, 111, 0, 0, 0, 122, 0, 0, 0, 97, 13, 126, 15, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1275.0, 796.0, 1111.0, 164.0, 684.0, 112.0, 305.0, 806.0, 586.0, 98.0, 217.0, 88.0, 608.0, 198.0, 430.0, 156.0, 97.0, 120.0, 460.0, 148.0, 97.0, 101.0, 201.0, 229.0, 245.0, 215.0, 88.0, 113.0, 121.0, 108.0, 113.0, 132.0, 112.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012852976, -0.21051316, 0.1369032, -0.24196763, -0.0022323988, 0.087963186, 0.048107218, -0.27843943, -0.00375879, -0.09917109, 0.15639816, -0.2175318, -0.3628295, -0.017601172, -0.0028436973, 0.090321004, 0.335177, -0.013306011, -0.27521977, -0.032843895, -0.041077785, -0.0082452865, 0.15073146, 0.017261144, 0.056794133, -0.034385346, -0.02159452, 0.2580277, -0.003330194, 0.041088358, 0.012520117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [59.38706, 4.8361397, 20.835575, 5.217865, 0.0, 13.8694725, 0.0, 3.0531578, 0.0, 1.5762231, 9.3678665, 1.6811829, 0.41059113, 0.0, 0.0, 6.043225, 8.097641, 0.0, 0.8339863, 0.0, 0.0, 0.0, 8.471073, 0.0, 0.0, 0.0, 0.0, 5.5021973, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 22, 22, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, -0.0022323988, 1.0, 0.048107218, 1.0, -0.00375879, 1.0, 1.0, 1.0, 1.0, -0.017601172, -0.0028436973, 1.0, 1.0, -0.013306011, 1.0, -0.032843895, -0.041077785, -0.0082452865, 0.23076923, 0.017261144, 0.056794133, -0.034385346, -0.02159452, 1.0, -0.003330194, 0.041088358, 0.012520117], "split_indices": [2, 71, 125, 1, 0, 17, 0, 23, 0, 105, 113, 122, 116, 0, 0, 5, 61, 0, 12, 0, 0, 0, 1, 0, 0, 0, 0, 69, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 817.0, 1237.0, 700.0, 117.0, 1083.0, 154.0, 594.0, 106.0, 290.0, 793.0, 345.0, 249.0, 139.0, 151.0, 579.0, 214.0, 140.0, 205.0, 145.0, 104.0, 150.0, 429.0, 126.0, 88.0, 95.0, 110.0, 271.0, 158.0, 126.0, 145.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00092192274, 0.11859254, -0.18838716, 0.07525563, 0.04091587, -0.21404661, -0.0036661641, -0.09282872, 0.13956617, -0.18725602, -0.03790202, -0.00056986674, -0.01663936, 0.25649026, 0.044020966, -0.1447114, -0.03012435, 0.34791484, 0.003860918, -0.014722903, 0.13803613, -0.18717937, -0.007819529, 0.05240035, 0.015290754, 0.0021440582, 0.026878968, -0.013367943, -0.028487489], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [46.20067, 16.105474, 3.095089, 12.031078, 0.0, 3.0054188, 0.0, 1.9741907, 8.993085, 2.8369884, 0.0, 0.0, 0.0, 7.210926, 7.9653172, 1.2033663, 0.0, 8.756336, 0.0, 0.0, 4.5278482, 1.3589411, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.04091587, 1.0, -0.0036661641, 1.0, 1.0, 1.0, -0.03790202, -0.00056986674, -0.01663936, 1.0, 1.0, 1.0, -0.03012435, 1.0, 0.003860918, -0.014722903, 1.0, 1.0, -0.007819529, 0.05240035, 0.015290754, 0.0021440582, 0.026878968, -0.013367943, -0.028487489], "split_indices": [137, 125, 71, 17, 0, 40, 0, 53, 53, 116, 0, 0, 0, 106, 5, 50, 0, 126, 0, 0, 15, 23, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1279.0, 795.0, 1113.0, 166.0, 680.0, 115.0, 308.0, 805.0, 585.0, 95.0, 141.0, 167.0, 362.0, 443.0, 426.0, 159.0, 255.0, 107.0, 146.0, 297.0, 260.0, 166.0, 134.0, 121.0, 157.0, 140.0, 168.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.011016295, 0.09514746, -0.18168461, 0.060192354, 0.03292407, -0.20643176, -0.0021995427, -0.089281805, 0.116664514, -0.25009394, -0.09772186, -0.15336505, 0.0009804856, 0.059534494, 0.29450002, -0.015260582, -0.2959016, -0.021685751, 0.0034224042, -0.008590092, -0.021520711, 0.13614798, -0.029939096, 0.042521756, 0.015113234, -0.3243624, -0.02493704, 0.022363035, 0.027896767, -0.016876346, 0.0057229674, -0.028139595, -0.036335047, 0.008438872, -0.004104097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, -1, 25, -1, -1, -1, -1, 27, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [37.324707, 10.392097, 3.1219616, 9.327447, 0.0, 3.2466125, 0.0, 1.9239883, 8.148115, 2.1792622, 3.081015, 0.7676692, 0.0, 4.1609144, 3.6544304, 0.0, 0.43967056, 0.0, 0.0, 0.0, 0.0, 5.313988, 3.3883224, 0.0, 0.0, 0.34508705, 0.0, 0.71574736, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 21, 21, 22, 22, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, -1, 26, -1, -1, -1, -1, 28, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 0.03292407, 1.0, -0.0021995427, 1.0, 2.0, 0.0, 1.0, -0.07692308, 0.0009804856, 1.0, 1.0, -0.015260582, 1.0, -0.021685751, 0.0034224042, -0.008590092, -0.021520711, 1.0, 1.0, 0.042521756, 0.015113234, 0.6923077, -0.02493704, 1.0, 0.027896767, -0.016876346, 0.0057229674, -0.028139595, -0.036335047, 0.008438872, -0.004104097], "split_indices": [137, 125, 1, 17, 0, 93, 0, 124, 0, 1, 13, 1, 0, 106, 121, 0, 115, 0, 0, 0, 0, 50, 39, 0, 0, 1, 0, 109, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1270.0, 790.0, 1105.0, 165.0, 684.0, 106.0, 303.0, 802.0, 488.0, 196.0, 184.0, 119.0, 607.0, 195.0, 156.0, 332.0, 103.0, 93.0, 88.0, 96.0, 327.0, 280.0, 102.0, 93.0, 206.0, 126.0, 182.0, 145.0, 108.0, 172.0, 98.0, 108.0, 92.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.005279013, 0.08776791, -0.153711, 0.05305425, 0.031766795, -0.17807709, 0.0003643707, 0.022582846, 0.033754893, -0.21658882, -0.08419273, -0.044458214, 0.14209895, -0.18800822, -0.03419038, -0.016703168, 0.00064123294, -0.003331712, -0.1463804, 0.23544866, -0.00837184, -0.005571994, -0.2483587, -0.01749494, 0.10454225, -0.0240901, -0.0049782386, 0.03997486, -0.0034961645, -0.019662455, -0.03040121, 0.0029997842, 0.021470962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [28.602873, 10.159395, 3.0596256, 9.587858, 0.0, 2.498413, 0.0, 8.004475, 0.0, 1.7549725, 1.5086317, 2.682689, 7.5677147, 3.18548, 0.0, 0.0, 0.0, 8.441964, 1.6800117, 11.284814, 0.0, 0.0, 0.78889275, 0.0, 2.2994604, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 22, 22, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 5.0, 0.031766795, 1.0, 0.0003643707, 1.0, 0.033754893, 1.0, 1.0, 0.1923077, 1.0, 0.0, -0.03419038, -0.016703168, 0.00064123294, 1.0, 1.0, 0.03846154, -0.00837184, -0.005571994, 1.0, -0.01749494, 1.0, -0.0240901, -0.0049782386, 0.03997486, -0.0034961645, -0.019662455, -0.03040121, 0.0029997842, 0.021470962], "split_indices": [137, 125, 1, 0, 0, 93, 0, 50, 0, 0, 13, 1, 106, 1, 0, 0, 0, 5, 122, 1, 0, 0, 23, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1273.0, 798.0, 1106.0, 167.0, 691.0, 107.0, 999.0, 107.0, 490.0, 201.0, 640.0, 359.0, 399.0, 91.0, 105.0, 96.0, 456.0, 184.0, 254.0, 105.0, 125.0, 274.0, 176.0, 280.0, 93.0, 91.0, 158.0, 96.0, 142.0, 132.0, 167.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.006365967, -0.11209993, 0.12483187, -0.16452488, -0.0019133319, 0.03177755, 0.22574992, -0.18785174, -0.006188672, 0.06769153, -0.016248694, -0.10758502, 0.21342535, 0.14306259, 0.3387559, -0.25695828, -0.12475444, 0.0009795532, 0.01580347, -0.17733963, 0.0016070865, 0.031162748, 0.014014015, 0.0028850879, 0.029752925, 0.04273795, 0.022939062, -0.021487555, -0.028366948, -0.001816729, -0.17362852, -0.03255589, -0.0055439635, -0.023996424, -0.011287244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, 21, 23, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [29.078796, 5.984481, 9.728931, 1.6807461, 3.7330194, 13.644734, 4.6440487, 2.4941692, 0.0, 1.2187077, 0.0, 2.6307993, 1.684041, 5.0632253, 2.0353909, 0.30687332, 1.5575957, 0.0, 0.0, 3.5232449, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.82620955, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, 22, 24, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.006188672, 1.0, -0.016248694, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0009795532, 0.01580347, 1.0, 0.0016070865, 0.031162748, 0.014014015, 0.0028850879, 0.029752925, 0.04273795, 0.022939062, -0.021487555, -0.028366948, -0.001816729, 1.0, -0.03255589, -0.0055439635, -0.023996424, -0.011287244], "split_indices": [71, 2, 39, 113, 7, 42, 50, 115, 0, 124, 0, 116, 105, 106, 121, 53, 106, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1036.0, 1036.0, 702.0, 334.0, 539.0, 497.0, 572.0, 130.0, 233.0, 101.0, 305.0, 234.0, 287.0, 210.0, 273.0, 299.0, 142.0, 91.0, 195.0, 110.0, 100.0, 134.0, 165.0, 122.0, 116.0, 94.0, 106.0, 167.0, 94.0, 205.0, 88.0, 107.0, 98.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002721811, 0.08339764, -0.12814547, -0.044923835, 0.13706714, -0.1519694, 0.002303628, 0.0050059, -0.020833028, 0.08107933, 0.32423413, -0.18949732, -0.06209003, 0.011398103, -0.007395211, 0.14707397, -0.011843935, 0.010046096, 0.05215613, -0.124482624, -0.23570418, 0.0012988517, -0.015764458, 0.032842807, 0.045712996, -0.005095493, -0.0198753, -0.21697709, -0.02733597, 0.10251214, -0.014262067, -0.02571627, -0.017969145, 0.019123577, -0.0001826824], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [21.759682, 8.780861, 2.8309708, 3.0677245, 9.420685, 2.2902565, 0.0, 2.4780848, 0.0, 9.111677, 9.140404, 1.4389744, 1.4348199, 0.0, 0.0, 18.417389, 0.0, 0.0, 0.0, 1.0867245, 0.19744873, 0.0, 0.0, 4.6452813, 0.0, 0.0, 0.0, 0.2801895, 0.0, 2.517995, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 19, 19, 20, 20, 23, 23, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 1.0, 1.0, 0.002303628, -0.15384616, -0.020833028, 0.23076923, 1.0, 1.0, 1.0, 0.011398103, -0.007395211, -0.07692308, -0.011843935, 0.010046096, 0.05215613, 0.46153846, 0.6923077, 0.0012988517, -0.015764458, 1.0, 0.045712996, -0.005095493, -0.0198753, -0.03846154, -0.02733597, 1.0, -0.014262067, -0.02571627, -0.017969145, 0.019123577, -0.0001826824], "split_indices": [137, 17, 1, 115, 61, 93, 0, 1, 0, 1, 50, 122, 23, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 7, 0, 0, 0, 1, 0, 106, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1275.0, 786.0, 376.0, 899.0, 679.0, 107.0, 288.0, 88.0, 692.0, 207.0, 479.0, 200.0, 121.0, 167.0, 520.0, 172.0, 97.0, 110.0, 199.0, 280.0, 112.0, 88.0, 380.0, 140.0, 100.0, 99.0, 187.0, 93.0, 272.0, 108.0, 90.0, 97.0, 147.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037783165, -0.094554156, 0.08788056, -0.07166519, -0.02476559, 0.13178091, -0.05019309, -0.13924652, -0.023885572, 0.06098666, 0.24753903, -0.015693618, 0.0030810502, -0.22293587, -0.06405352, 0.06623857, -0.087059416, -0.011425769, 0.1233285, 0.010768784, 0.034423616, -0.02413918, -0.020468727, -0.015002176, 0.0009749375, 0.0213847, -0.009856694, -0.014748961, -0.001324823, 0.044815864, 0.026904792, -0.006996095, 0.020741638], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [17.18997, 3.6375046, 6.2312026, 2.9157958, 0.0, 6.392105, 2.1443505, 2.3535256, 3.0118551, 5.287726, 4.0028667, 0.0, 0.0, 0.059612274, 1.2499074, 5.303217, 1.3871913, 0.0, 4.08437, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.329763, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.34615386, 1.0, -0.02476559, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.015693618, 0.0030810502, 0.46153846, 0.46153846, 0.15384616, 1.0, -0.011425769, 1.0, 0.010768784, 0.034423616, -0.02413918, -0.020468727, -0.015002176, 0.0009749375, 0.0213847, -0.009856694, -0.014748961, -0.001324823, 1.0, 0.026904792, -0.006996095, 0.020741638], "split_indices": [71, 40, 1, 17, 0, 50, 93, 111, 122, 5, 109, 0, 0, 1, 1, 1, 12, 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1038.0, 1028.0, 903.0, 135.0, 780.0, 248.0, 374.0, 529.0, 484.0, 296.0, 107.0, 141.0, 177.0, 197.0, 218.0, 311.0, 127.0, 357.0, 121.0, 175.0, 88.0, 89.0, 91.0, 106.0, 115.0, 103.0, 171.0, 140.0, 232.0, 125.0, 136.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031954306, -0.09398864, 0.08786197, -0.13725314, -0.035117313, 0.1355799, -0.023120679, -0.18178383, 0.00091534853, 0.013269565, -0.082222715, 0.23420501, 0.03219163, -0.0142684, 0.011470924, -0.2525964, -0.11831693, -0.026283007, -0.022766592, 0.30740333, 0.0099632675, -0.019096185, 0.12577213, -0.030399298, -0.018462026, -0.0061595594, -0.021018082, 0.011055118, -0.010610297, 0.011328748, 0.043324392, 0.02241499, 0.0022516095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, -1, -1, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [17.072168, 2.6336365, 5.460034, 3.8856754, 3.4623444, 7.3518057, 5.1086154, 2.0538712, 0.0, 0.0, 2.7825286, 3.634821, 7.3507504, 0.0, 0.0, 0.7546482, 1.2557642, 2.6977592, 0.0, 5.838209, 0.0, 0.0, 2.519208, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 22, 22], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, -1, -1, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.15384616, 1.3076923, 1.0, 1.0, 1.0, 1.0, 0.00091534853, 0.013269565, 0.5769231, 1.0, 1.0, -0.0142684, 0.011470924, 1.0, 1.0, 1.0, -0.022766592, 1.0, 0.0099632675, -0.019096185, 1.0, -0.030399298, -0.018462026, -0.0061595594, -0.021018082, 0.011055118, -0.010610297, 0.011328748, 0.043324392, 0.02241499, 0.0022516095], "split_indices": [71, 83, 1, 1, 53, 53, 39, 17, 0, 0, 1, 97, 5, 0, 0, 106, 58, 106, 0, 109, 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1034.0, 1031.0, 596.0, 438.0, 721.0, 310.0, 457.0, 139.0, 96.0, 342.0, 369.0, 352.0, 166.0, 144.0, 216.0, 241.0, 247.0, 95.0, 239.0, 130.0, 104.0, 248.0, 123.0, 93.0, 149.0, 92.0, 91.0, 156.0, 94.0, 145.0, 127.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0038003386, -0.0764207, 0.08402138, -0.13602698, -0.03138709, 0.04125657, 0.23435181, -0.17257479, -0.0013125966, 0.027364133, -0.14226563, 0.16080143, -0.023201644, 0.03705064, 0.013429882, -0.22151949, -0.0071626315, 0.015127534, -0.054353155, -0.010583565, -0.016990215, 0.065498896, 0.03641134, -0.14992698, 0.04558141, -0.027331153, -0.017835945, -0.013686635, 0.001876826, -0.006980851, 0.02125722, -0.015499999, -0.014485392, -0.075391226, 0.018581724, 0.008214578, -0.022950355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, 27, -1, -1, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [13.308439, 2.7755508, 6.6474333, 1.9988337, 3.8368936, 6.203047, 3.1195908, 1.694725, 0.0, 3.89839, 0.20538568, 5.464072, 4.5587587, 0.0, 0.0, 0.51636314, 0.0, 0.0, 1.3997679, 0.0, 0.0, 3.820821, 0.0, 0.004734516, 5.751033, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.418668, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 18, 18, 21, 21, 23, 23, 24, 24, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, 28, -1, -1, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.0, 1.3846154, 1.0, -0.30769232, 1.0, 1.0, -0.0013125966, 1.0, -0.115384616, 1.0, 1.0, 0.03705064, 0.013429882, 1.0, -0.0071626315, 0.015127534, 1.0, -0.010583565, -0.016990215, -0.5, 0.03641134, -0.115384616, 1.0, -0.027331153, -0.017835945, -0.013686635, 0.001876826, -0.006980851, 0.02125722, -0.015499999, -0.014485392, 0.5, 0.018581724, 0.008214578, -0.022950355], "split_indices": [71, 17, 0, 1, 80, 1, 69, 83, 0, 122, 1, 50, 16, 0, 0, 122, 0, 0, 16, 0, 0, 1, 0, 1, 39, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1034.0, 1034.0, 445.0, 589.0, 805.0, 229.0, 343.0, 102.0, 385.0, 204.0, 282.0, 523.0, 97.0, 132.0, 231.0, 112.0, 153.0, 232.0, 88.0, 116.0, 192.0, 90.0, 184.0, 339.0, 105.0, 126.0, 109.0, 123.0, 100.0, 92.0, 92.0, 92.0, 182.0, 157.0, 90.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042424602, 0.054466497, -0.09857759, 0.03667025, 0.027228514, -0.122137934, 0.00037825163, -0.14029923, 0.06868483, -0.00041731104, -0.14810361, -0.0069791377, -0.021080704, 0.13303247, 0.005490829, -0.1871268, -0.0070278957, 0.21033716, 0.003210963, 0.046346355, -0.016293397, -0.22532853, -0.013992207, 0.28531882, 0.005591949, -0.0054019424, 0.0058006044, -0.005140659, 0.10913078, -0.025218684, -0.020159936, 0.013135457, 0.040506877, -0.002034386, 0.023651714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, 15, -1, -1, 17, 19, 21, -1, 23, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [11.42001, 4.9268456, 1.9076056, 6.657084, 0.0, 1.969533, 0.0, 0.89484286, 4.0460525, 0.0, 1.6004801, 0.0, 0.0, 4.9476547, 3.4543033, 0.6329584, 0.0, 3.577754, 0.5770136, 2.4794946, 0.0, 0.12364006, 0.0, 3.8349361, 0.0, 0.0, 0.0, 0.0, 4.057353, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, 16, -1, -1, 18, 20, 22, -1, 24, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -1.0, 0.027228514, -1.0, 0.00037825163, 1.0, 1.0, -0.00041731104, 1.0, -0.0069791377, -0.021080704, 1.0, 1.0, 1.0, -0.0070278957, 1.0, 1.0, 1.0, -0.016293397, 1.0, -0.013992207, 1.0, 0.005591949, -0.0054019424, 0.0058006044, -0.005140659, 1.0, -0.025218684, -0.020159936, 0.013135457, 0.040506877, -0.002034386, 0.023651714], "split_indices": [137, 102, 113, 0, 0, 0, 0, 15, 53, 0, 83, 0, 0, 97, 23, 13, 0, 59, 15, 81, 0, 23, 0, 124, 0, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1271.0, 791.0, 1175.0, 96.0, 643.0, 148.0, 180.0, 995.0, 116.0, 527.0, 90.0, 90.0, 493.0, 502.0, 351.0, 176.0, 309.0, 184.0, 404.0, 98.0, 194.0, 157.0, 208.0, 101.0, 90.0, 94.0, 158.0, 246.0, 91.0, 103.0, 91.0, 117.0, 122.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005681588, -0.013540225, 0.19556057, 0.025127154, -0.069737494, 0.031660635, 0.007322705, -0.06830364, 0.060221806, -0.106928155, 0.032491, 0.0014830843, -0.12621756, 0.100618295, -0.043731812, -0.14239398, -0.0025293792, -0.008886376, 0.016251398, -0.005061633, -0.020353695, 0.04109481, 0.030417949, -0.013406112, 0.009429946, -0.10735001, -0.018614829, -0.030755712, 0.02618719, -0.017841278, -0.004012843, -0.013779812, 0.0066610523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [7.504027, 4.0569935, 2.7987018, 3.6264868, 2.8932805, 0.0, 0.0, 1.4540229, 3.3762867, 1.6155381, 3.2031188, 0.0, 1.040488, 7.0155516, 2.8053606, 0.5964632, 0.0, 0.0, 0.0, 0.0, 0.0, 7.1066017, 0.0, 0.0, 0.0, 1.031821, 0.0, 3.522743, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 21, 21, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.031660635, 0.007322705, 1.0, 0.15384616, 1.0, 1.0, 0.0014830843, -0.115384616, -0.07692308, 0.8076923, 1.0, -0.0025293792, -0.008886376, 0.016251398, -0.005061633, -0.020353695, 1.0, 0.030417949, -0.013406112, 0.009429946, 1.0, -0.018614829, 1.0, 0.02618719, -0.017841278, -0.004012843, -0.013779812, 0.0066610523], "split_indices": [125, 137, 15, 17, 93, 0, 0, 13, 1, 50, 13, 0, 1, 1, 1, 23, 0, 0, 0, 0, 0, 61, 0, 0, 0, 115, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1867.0, 189.0, 1106.0, 761.0, 95.0, 94.0, 302.0, 804.0, 558.0, 203.0, 124.0, 178.0, 579.0, 225.0, 389.0, 169.0, 105.0, 98.0, 90.0, 88.0, 448.0, 131.0, 136.0, 89.0, 216.0, 173.0, 338.0, 110.0, 105.0, 111.0, 161.0, 177.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0027169804, -0.05509925, 0.060141806, -0.034312032, -0.019244885, -0.011222335, 0.13635871, -0.052011937, 0.012148599, 0.046809137, -0.02349073, -0.0024155544, 0.17450072, -0.007455194, -0.1519489, -0.04825177, 0.16058671, 0.035026137, 0.12046427, -0.08367167, 0.07329459, -0.018247455, -0.012900052, 0.0009244345, -0.013512093, 0.0014688177, 0.029911662, -0.005236137, 0.22723615, -0.12830693, 0.0013038019, -0.00070702285, 0.01968745, 0.033504974, 0.012275697, -0.015042107, -0.0101418095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, 17, 19, 21, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [6.842699, 2.932203, 5.6240854, 2.459788, 0.0, 6.931731, 3.0611687, 3.566745, 0.0, 4.5858994, 0.0, 0.0, 3.8369818, 3.409573, 0.17302608, 1.1537621, 3.900782, 0.0, 5.701953, 1.230249, 2.671568, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.151475, 0.115950584, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, 18, 20, 22, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, -0.019244885, 0.5, 0.0, 1.0, 0.012148599, 1.0, -0.02349073, -0.0024155544, -0.42307693, 1.0, 1.0, 1.0, -0.42307693, 0.035026137, -0.07692308, 1.0, 1.0, -0.018247455, -0.012900052, 0.0009244345, -0.013512093, 0.0014688177, 0.029911662, -0.005236137, 0.34615386, 1.0, 0.0013038019, -0.00070702285, 0.01968745, 0.033504974, 0.012275697, -0.015042107, -0.0101418095], "split_indices": [71, 40, 39, 1, 0, 1, 0, 23, 0, 42, 0, 0, 1, 83, 26, 59, 1, 0, 1, 127, 12, 0, 0, 0, 0, 0, 0, 0, 1, 13, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1027.0, 1034.0, 892.0, 135.0, 534.0, 500.0, 801.0, 91.0, 424.0, 110.0, 96.0, 404.0, 554.0, 247.0, 231.0, 193.0, 95.0, 309.0, 285.0, 269.0, 106.0, 141.0, 139.0, 92.0, 94.0, 99.0, 118.0, 191.0, 195.0, 90.0, 163.0, 106.0, 94.0, 97.0, 107.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034304513, 0.04007277, -0.072948165, 0.021814821, 0.025640175, -0.10298832, 0.005210595, 0.00034686792, 0.02231956, -0.07632612, -0.023276247, -0.008790611, 0.010441907, -0.026678039, 0.02246062, 0.0018265179, -0.109553665, 0.023008421, -0.10612178, -0.0049114036, -0.15578997, -0.08378742, 0.10230594, -0.056885827, -0.022921164, -0.010230557, -0.02071769, -0.01730357, 0.0052817035, 0.011439809, 0.024489585, -0.015076595, 0.0063346596, -0.014972882, 0.014382835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, -1, -1, 19, 21, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [6.2541375, 5.024047, 1.8689294, 5.0711517, 0.0, 1.9895358, 2.0415905, 6.4242244, 0.0, 1.499229, 0.0, 0.0, 0.0, 3.7341256, 0.0, 0.0, 0.98645926, 4.9287515, 2.2060013, 0.0, 0.5496788, 3.0235445, 4.3275013, 2.9347339, 0.0, 0.0, 0.0, 0.0, 0.0, 4.352724, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, -1, -1, 20, 22, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 5.0, 0.025640175, 1.0, 1.0, 1.0, 0.02231956, 0.0, -0.023276247, -0.008790611, 0.010441907, 1.0, 0.02246062, 0.0018265179, 1.0, 1.0, 0.46153846, -0.0049114036, 1.0, 0.115384616, 1.0, -0.1923077, -0.022921164, -0.010230557, -0.02071769, -0.01730357, 0.0052817035, 1.0, 0.024489585, -0.015076595, 0.0063346596, -0.014972882, 0.014382835], "split_indices": [137, 102, 93, 0, 0, 0, 13, 125, 0, 1, 0, 0, 0, 115, 0, 0, 53, 108, 1, 0, 23, 1, 109, 1, 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1272.0, 796.0, 1173.0, 99.0, 575.0, 221.0, 1060.0, 113.0, 477.0, 98.0, 114.0, 107.0, 946.0, 114.0, 124.0, 353.0, 582.0, 364.0, 153.0, 200.0, 248.0, 334.0, 260.0, 104.0, 98.0, 102.0, 150.0, 98.0, 204.0, 130.0, 146.0, 114.0, 92.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018054046, -0.053426996, 0.050468985, -0.08065286, 0.0036987055, 0.023202108, 0.02100711, -0.099812925, -0.0013775702, 0.066135496, -0.008265003, -0.020323575, 0.16429262, -0.12014945, -0.0012991561, -0.0009394651, 0.012825377, -0.051010285, 0.008014117, 0.029209638, 0.0065981993, -0.005296655, -0.1537409, 0.025401264, -0.092898555, -0.021721242, -0.12511647, -0.00569967, 0.011150255, -0.016990453, -0.021387706, -0.0074686753, -0.017654475, -0.011397231, 0.0074449563], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, 21, -1, -1, -1, 23, -1, -1, -1, -1, 25, 27, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [5.5831704, 1.6190643, 4.4737024, 0.9033656, 1.8114892, 5.391852, 0.0, 0.96757317, 0.0, 0.914902, 0.0, 2.0686479, 2.6008434, 1.0020056, 0.0, 0.0, 0.0, 1.6451844, 0.0, 0.0, 0.0, 0.0, 0.53778267, 1.2912122, 3.0488381, 0.0, 0.52907586, 0.0, 0.0, 1.8090769, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 17, 17, 22, 22, 23, 23, 24, 24, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, 22, -1, -1, -1, 24, -1, -1, -1, -1, 26, 28, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.3846154, 1.0, 2.0, 0.02100711, 1.0, -0.0013775702, 1.0, -0.008265003, 1.0, 1.0, 0.0, -0.0012991561, -0.0009394651, 0.012825377, -0.30769232, 0.008014117, 0.029209638, 0.0065981993, -0.005296655, 0.34615386, -0.46153846, 1.0, -0.021721242, 0.84615386, -0.00569967, 0.011150255, 1.0, -0.021387706, -0.0074686753, -0.017654475, -0.011397231, 0.0074449563], "split_indices": [71, 2, 125, 1, 93, 0, 0, 62, 0, 74, 0, 116, 69, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 15, 0, 1, 0, 0, 59, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1041.0, 1028.0, 705.0, 336.0, 878.0, 150.0, 548.0, 157.0, 195.0, 141.0, 671.0, 207.0, 444.0, 104.0, 88.0, 107.0, 514.0, 157.0, 90.0, 117.0, 148.0, 296.0, 182.0, 332.0, 92.0, 204.0, 93.0, 89.0, 204.0, 128.0, 103.0, 101.0, 99.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015499176, 0.0124006355, -0.12142135, 0.024464587, -0.017023707, -0.0045258184, -0.017582359, 0.00870304, 0.16286512, -0.0077627157, 0.02160284, 0.013697127, 0.01881835, -0.01312262, 0.0015933563, -0.025162408, 0.099021256, 0.0040922174, -0.15680824, 0.04106097, -0.043463897, -0.015418397, 0.016982527, -0.021655925, -0.009320228, -0.017018735, 0.007464725, 0.04705891, -0.092521906, 0.015431221, -0.00086669944, -0.020503296, -0.004869228], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, -1, 15, 17, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [3.4649494, 4.089384, 0.894984, 3.7978282, 0.0, 0.0, 0.0, 5.3357205, 0.11669445, 1.6726336, 0.0, 0.0, 0.0, 0.0, 3.508696, 4.0669203, 12.8750515, 2.7937913, 0.72969866, 0.0, 2.9785235, 3.7237105, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5520887, 1.7062347, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, -1, 16, 18, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.03846154, 1.0, -0.017023707, -0.0045258184, -0.017582359, 5.0, -0.42307693, -0.53846157, 0.02160284, 0.013697127, 0.01881835, -0.01312262, 1.0, 1.0, -0.1923077, 1.0, 1.0, 0.04106097, 1.0, 1.0, 0.016982527, -0.021655925, -0.009320228, -0.017018735, 0.007464725, 1.0, 1.0, 0.015431221, -0.00086669944, -0.020503296, -0.004869228], "split_indices": [40, 43, 1, 125, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 61, 0, 1, 73, 93, 0, 12, 12, 0, 0, 0, 0, 0, 17, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1856.0, 216.0, 1741.0, 115.0, 90.0, 126.0, 1563.0, 178.0, 1448.0, 115.0, 88.0, 90.0, 102.0, 1346.0, 1056.0, 290.0, 864.0, 192.0, 91.0, 199.0, 773.0, 91.0, 99.0, 93.0, 96.0, 103.0, 427.0, 346.0, 146.0, 281.0, 97.0, 249.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0012029951, -0.043241333, 0.04590774, -0.02657087, -0.0135937575, -0.010326303, 0.10719138, -0.044601075, 0.011124414, 0.043397333, -0.019768186, 0.03638645, 0.19337474, 0.02113445, -0.08437039, -0.05171978, 0.15175241, 0.019428192, -0.008332514, 0.004849154, 0.039652612, 0.10473548, -0.0122628035, -0.05677067, -0.017530156, 0.0010304458, -0.014546099, 0.0009909072, 0.02878647, 0.019496227, 0.0014508647, -0.098204724, -0.011825239, -0.0033510905, -0.015528748, -0.006409948, 0.0041042995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [4.0810356, 1.5916476, 3.5289369, 2.169259, 0.0, 5.3749356, 2.9900804, 2.0182061, 0.0, 4.2771654, 0.0, 5.0846167, 6.5047407, 3.497439, 1.2071531, 1.2849448, 3.745483, 0.0, 0.0, 0.0, 0.0, 1.497921, 0.0, 0.6871779, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.70903647, 0.48916563, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 21, 21, 23, 23, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 2.0, 1.0, 1.0, -0.0135937575, 0.46153846, -0.07692308, 1.0, 0.011124414, 1.0, -0.019768186, -0.42307693, 1.0, 1.0, 1.0, 1.0, -0.42307693, 0.019428192, -0.008332514, 0.004849154, 0.039652612, 1.0, -0.0122628035, 1.0, -0.017530156, 0.0010304458, -0.014546099, 0.0009909072, 0.02878647, 0.019496227, 0.0014508647, 1.0, 1.0, -0.0033510905, -0.015528748, -0.006409948, 0.0041042995], "split_indices": [71, 0, 39, 121, 0, 1, 1, 122, 0, 42, 0, 1, 122, 23, 127, 122, 1, 0, 0, 0, 0, 81, 0, 39, 0, 0, 0, 0, 0, 0, 0, 23, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1030.0, 1024.0, 873.0, 157.0, 534.0, 490.0, 772.0, 101.0, 415.0, 119.0, 269.0, 221.0, 291.0, 481.0, 221.0, 194.0, 116.0, 153.0, 129.0, 92.0, 184.0, 107.0, 369.0, 112.0, 133.0, 88.0, 95.0, 99.0, 92.0, 92.0, 192.0, 177.0, 90.0, 102.0, 89.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0022435684, 0.011129122, -0.016936956, -0.02920767, 0.051877517, -0.008349112, -0.10351025, -0.006941964, 0.11649079, 0.009522261, -0.014228626, -0.0060568983, -0.016526846, -0.09740812, 0.11164204, 0.029907782, 0.22337314, 0.0348041, -0.009197686, -0.18814625, 0.0047610826, 0.017707065, 0.006179165, -0.009985223, 0.012039834, 0.011508289, 0.036938246, 0.01555198, 0.01034188, -0.014533455, -0.022954654, -0.023146778, 0.056454554, 0.0075747306, -0.013232588, -0.008422208, 0.02110443], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [3.159546, 3.2380092, 0.0, 1.5343459, 3.7245092, 1.8502847, 0.5754781, 5.5033817, 4.3217087, 1.7500694, 0.0, 0.0, 0.0, 3.8291957, 0.7240834, 3.0294507, 3.3045778, 1.6123233, 0.0, 0.3172617, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.70109034, 0.0, 0.0, 2.8396547, 4.153709, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 28, 28, 31, 31, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.016936956, 1.0, 1.0, 1.0, 1.0, 1.0, -0.07692308, 1.0, -0.014228626, -0.0060568983, -0.016526846, 1.0, 1.0, 1.0, 1.0, -0.30769232, -0.009197686, 1.0, 0.0047610826, 0.017707065, 0.006179165, -0.009985223, 0.012039834, 0.011508289, 0.036938246, 0.01555198, 1.0, -0.014533455, -0.022954654, 0.5769231, 1.0, 0.0075747306, -0.013232588, -0.008422208, 0.02110443], "split_indices": [117, 71, 0, 116, 39, 40, 109, 42, 1, 7, 0, 0, 0, 116, 105, 109, 106, 1, 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 81, 0, 0, 1, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1970.0, 102.0, 990.0, 980.0, 773.0, 217.0, 513.0, 467.0, 682.0, 91.0, 128.0, 89.0, 291.0, 222.0, 258.0, 209.0, 546.0, 136.0, 179.0, 112.0, 96.0, 126.0, 106.0, 152.0, 120.0, 89.0, 92.0, 454.0, 88.0, 91.0, 263.0, 191.0, 138.0, 125.0, 100.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.000792555, -0.0095142005, 0.013975359, -0.0233996, 0.12289028, -0.04320537, 0.041503128, 0.023817008, -0.00015594657, -0.017263664, -0.16238524, 0.021156523, -0.048304938, 0.028184, -0.054571453, -0.025635405, -0.00795151, -0.016366497, 0.0051727514, 0.07355391, -0.0096583245, -0.00176242, -0.12102775, 0.0031879155, 0.019239424, 0.008043579, -0.07385807, -0.022219269, -0.0044488474, 0.00680288, -0.009996801, -0.004467217, -0.010337186], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, -1, 17, 19, 21, -1, -1, -1, -1, 23, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [2.9661639, 3.544607, 0.0, 2.2431078, 2.6254177, 4.1336417, 6.231364, 0.0, 0.0, 1.861716, 1.8611417, 0.0, 3.0811126, 2.802036, 2.1162238, 0.0, 0.0, 0.0, 0.0, 3.035521, 0.0, 1.9911805, 2.067405, 1.5250286, 0.0, 0.0, 0.15418774, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 12, 12, 13, 13, 14, 14, 19, 19, 21, 21, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, -1, 18, 20, 22, -1, -1, -1, -1, 24, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.013975359, 1.0, 1.0, 1.0, -0.23076923, 0.023817008, -0.00015594657, 1.0, -0.07692308, 0.021156523, 0.53846157, 1.0, 1.0, -0.025635405, -0.00795151, -0.016366497, 0.0051727514, 1.0, -0.0096583245, 1.0, 1.0, 1.0, 0.019239424, 0.008043579, 1.0, -0.022219269, -0.0044488474, 0.00680288, -0.009996801, -0.004467217, -0.010337186], "split_indices": [0, 125, 0, 61, 15, 0, 1, 0, 0, 106, 1, 0, 1, 121, 12, 0, 0, 0, 0, 50, 0, 15, 39, 23, 0, 0, 83, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1928.0, 143.0, 1745.0, 183.0, 1337.0, 408.0, 95.0, 88.0, 1098.0, 239.0, 141.0, 267.0, 495.0, 603.0, 112.0, 127.0, 124.0, 143.0, 363.0, 132.0, 336.0, 267.0, 228.0, 135.0, 157.0, 179.0, 115.0, 152.0, 140.0, 88.0, 90.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017254066, -0.011558627, 0.013150777, -0.021360032, 0.014120807, -0.04282851, 0.04285583, -0.02281516, -0.019336917, 0.015951367, -0.026563792, -0.044908572, 0.029946158, -0.013067006, 0.010031571, -0.09834691, 0.01020373, 0.0844299, -0.007855956, -0.057783447, -0.01766955, -0.009106242, 0.06574898, -0.0035210145, 0.018792806, -0.015547786, -0.013961501, 0.016743796, -0.003294912, 0.008210502, -0.0103236055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, 13, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [2.7066894, 2.8808594, 0.0, 2.492539, 0.0, 4.0823746, 3.66855, 1.3941503, 0.0, 0.0, 3.7513423, 2.4827278, 2.0868645, 0.0, 0.0, 1.3602223, 2.3343146, 2.909894, 0.0, 0.97465086, 0.0, 0.0, 2.6897848, 0.0, 0.0, 1.5927191, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 19, 19, 22, 22, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, 14, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.013150777, 1.0, 0.014120807, 2.0, -0.23076923, 1.0, -0.019336917, 0.015951367, 1.0, 1.0, 0.5, -0.013067006, 0.010031571, 1.0, 1.0, -0.15384616, -0.007855956, 1.0, -0.01766955, -0.009106242, 1.0, -0.0035210145, 0.018792806, 1.0, -0.013961501, 0.016743796, -0.003294912, 0.008210502, -0.0103236055], "split_indices": [0, 102, 0, 61, 0, 0, 1, 83, 0, 0, 53, 39, 1, 0, 0, 121, 59, 1, 0, 115, 0, 0, 12, 0, 0, 80, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1924.0, 142.0, 1808.0, 116.0, 1355.0, 453.0, 1196.0, 159.0, 169.0, 284.0, 843.0, 353.0, 156.0, 128.0, 428.0, 415.0, 235.0, 118.0, 282.0, 146.0, 147.0, 268.0, 109.0, 126.0, 186.0, 96.0, 132.0, 136.0, 88.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0004949872, 0.008524812, -0.015753193, -0.02589932, 0.043018986, -0.010457068, -0.012396914, 0.09796846, -0.0049178805, 0.011025169, -0.025088435, 0.0020066751, 0.21958338, -0.010952353, 0.032750983, -0.060743794, 0.046646737, -0.017779328, 0.015334911, 0.007149274, 0.0340991, 0.013497767, -0.03885272, -0.040640093, -0.013589779, 0.015363636, -0.0052175727, 0.0011471923, -0.00973252, -0.08635793, 0.002447317, -0.0021812764, -0.0133342985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [2.6241434, 2.3368664, 0.0, 1.4917026, 2.589326, 1.5029801, 0.0, 5.345036, 2.0686972, 0.0, 1.9413272, 6.9661093, 3.6318226, 0.0, 2.825447, 0.76601255, 2.6643915, 0.0, 0.0, 0.0, 0.0, 0.0, 0.66797197, 1.1907351, 0.0, 0.0, 0.0, 0.0, 0.0, 0.71267414, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.015753193, 1.0, 1.0, -0.42307693, -0.012396914, 1.0, -0.3846154, 0.011025169, 0.88461536, -0.07692308, 1.0, -0.010952353, -0.03846154, 1.0, 1.0, -0.017779328, 0.015334911, 0.007149274, 0.0340991, 0.013497767, 1.0, 1.0, -0.013589779, 0.015363636, -0.0052175727, 0.0011471923, -0.00973252, -0.03846154, 0.002447317, -0.0021812764, -0.0133342985], "split_indices": [117, 71, 0, 64, 121, 1, 0, 109, 1, 0, 1, 1, 113, 0, 1, 50, 12, 0, 0, 0, 0, 0, 126, 124, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1968.0, 100.0, 985.0, 983.0, 851.0, 134.0, 458.0, 525.0, 92.0, 759.0, 256.0, 202.0, 139.0, 386.0, 507.0, 252.0, 117.0, 139.0, 91.0, 111.0, 159.0, 227.0, 400.0, 107.0, 121.0, 131.0, 122.0, 105.0, 235.0, 165.0, 99.0, 136.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0021204823, 0.009436311, -0.012477351, 0.043399703, -0.01651771, 0.10634418, -0.029289976, -0.081011645, 0.00439093, 0.029424656, 0.058070906, 0.0065755486, -0.07397554, -0.018840585, 0.0031227374, 0.026197568, -0.011240671, 0.017553119, 0.004077051, -0.006083463, -0.020825094, -0.03756226, 0.085602224, 0.009333114, -0.013253632, 0.0010681212, -0.0022475624, -0.10042962, 0.008437871, 0.14925493, -0.002723665, -0.020641185, -0.0017157827, 0.030847913, 0.0036519512], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, -1, 21, -1, -1, 23, 25, -1, 27, 29, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.9244376, 1.7277136, 0.0, 3.8845263, 1.498162, 4.12715, 1.6733813, 3.2786398, 2.1369023, 0.0, 2.2958517, 0.0, 2.4431508, 0.0, 0.0, 2.677855, 0.0, 0.0, 3.0239391, 0.048916087, 0.0, 2.6141424, 2.6287951, 0.0, 0.0, 0.0, 0.0, 1.9856987, 0.0, 4.200348, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 12, 12, 15, 15, 18, 18, 19, 19, 21, 21, 22, 22, 27, 27, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, -1, 22, -1, -1, 24, 26, -1, 28, 30, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.012477351, 1.0, 1.0, -0.42307693, 1.0, 1.0, 1.0, 0.029424656, 1.0, 0.0065755486, 1.0, -0.018840585, 0.0031227374, 1.0, -0.011240671, 0.017553119, 1.0, 1.1153846, -0.020825094, 0.65384614, 0.1923077, 0.009333114, -0.013253632, 0.0010681212, -0.0022475624, 1.0, 0.008437871, 1.0, -0.002723665, -0.020641185, -0.0017157827, 0.030847913, 0.0036519512], "split_indices": [43, 53, 0, 97, 5, 1, 69, 97, 64, 0, 69, 0, 15, 0, 0, 127, 0, 0, 121, 1, 0, 1, 1, 0, 0, 0, 0, 12, 0, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1960.0, 113.0, 849.0, 1111.0, 455.0, 394.0, 272.0, 839.0, 93.0, 362.0, 126.0, 268.0, 139.0, 133.0, 707.0, 132.0, 114.0, 248.0, 178.0, 90.0, 341.0, 366.0, 150.0, 98.0, 88.0, 90.0, 225.0, 116.0, 234.0, 132.0, 99.0, 126.0, 97.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [2.4785573e-05, 0.008447398, -0.01634239, -0.021335462, 0.038352072, -0.03647726, 0.010513458, -0.0053846925, 0.08562501, -0.061092757, 0.040258348, 0.03603595, -0.019519141, 0.020354751, 0.1678404, -0.041783284, -0.018584494, -0.0019380151, 0.009204971, -0.021926409, 0.014709673, 0.013339156, -0.0062738517, 0.0066879014, 0.030551499, -0.102654904, 0.008813821, -0.09629981, 0.011378585, -0.0063693523, -0.017668149, -0.05533352, 0.0121353045, -0.0007765548, -0.018383926, 0.00095753226, -0.010633335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1, -1, -1, 29, 31, 33, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [2.837306, 1.7456675, 0.0, 1.8805143, 2.022079, 1.6565522, 0.0, 3.9938545, 2.522123, 1.599508, 0.6579058, 2.6843731, 0.0, 2.4608607, 2.8911614, 1.7709589, 0.0, 0.0, 0.0, 2.7655866, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7527697, 2.2667954, 1.3717922, 0.0, 0.0, 0.0, 0.66206837, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 25, 25, 26, 26, 27, 27, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1, -1, -1, 30, 32, 34, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01634239, 3.1538463, 1.0, 1.0, 0.010513458, 0.5769231, -0.07692308, 1.0, 1.0, 1.0, -0.019519141, -0.42307693, 1.0, 1.0, -0.018584494, -0.0019380151, 0.009204971, -0.15384616, 0.014709673, 0.013339156, -0.0062738517, 0.0066879014, 0.030551499, 1.0, 1.0, 1.0, 0.011378585, -0.0063693523, -0.017668149, 1.0, 0.0121353045, -0.0007765548, -0.018383926, 0.00095753226, -0.010633335], "split_indices": [117, 71, 0, 1, 39, 62, 0, 1, 1, 7, 74, 61, 0, 1, 106, 17, 0, 0, 0, 1, 0, 0, 0, 0, 0, 50, 127, 121, 0, 0, 0, 53, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 982.0, 978.0, 877.0, 105.0, 508.0, 470.0, 664.0, 213.0, 417.0, 91.0, 262.0, 208.0, 575.0, 89.0, 99.0, 114.0, 274.0, 143.0, 111.0, 151.0, 120.0, 88.0, 261.0, 314.0, 177.0, 97.0, 171.0, 90.0, 200.0, 114.0, 88.0, 89.0, 88.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.004046907, -0.0036072661, 0.013079486, -0.018806538, 0.0550725, -0.0020380598, -0.13281532, -0.07071609, 0.17595229, 0.03170887, -0.06053274, -0.0050130063, -0.021467369, -0.019887736, 0.004388963, 0.005690831, 0.030341348, -0.012393682, 0.07856784, -0.0029575885, -0.1296229, 0.07812003, -0.09968914, -0.012899349, 0.16469873, -0.01167975, 0.0067255036, -0.0222175, -0.0036244448, 0.02192603, -0.002001655, -0.016885392, -0.002738048, 0.08185604, 0.031162728, 0.0037702469, 0.01289208], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, 21, 23, 25, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1], "loss_changes": [2.0101528, 1.7427526, 0.0, 2.9670415, 6.1125298, 2.6708436, 1.3469269, 2.8935394, 3.1105628, 1.7731432, 1.9690489, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.4924347, 7.4370174, 2.1581101, 1.9445326, 3.005673, 1.125272, 0.0, 3.5785532, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.39067805, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 33, 33], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, 22, 24, 26, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.013079486, 1.0, 1.0, 1.0, 0.23076923, 1.0, 1.0, 1.0, 0.15384616, -0.0050130063, -0.021467369, -0.019887736, 0.004388963, 0.005690831, 0.030341348, 1.0, -0.1923077, -0.3846154, 0.7692308, 0.03846154, 1.0, -0.012899349, 1.0, -0.01167975, 0.0067255036, -0.0222175, -0.0036244448, 0.02192603, -0.002001655, -0.016885392, -0.002738048, 0.53846157, 0.031162728, 0.0037702469, 0.01289208], "split_indices": [102, 113, 0, 64, 109, 109, 1, 111, 61, 39, 1, 0, 0, 0, 0, 0, 0, 115, 1, 1, 1, 1, 15, 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1954.0, 118.0, 1552.0, 402.0, 1353.0, 199.0, 197.0, 205.0, 858.0, 495.0, 99.0, 100.0, 93.0, 104.0, 106.0, 99.0, 442.0, 416.0, 270.0, 225.0, 217.0, 225.0, 122.0, 294.0, 103.0, 167.0, 113.0, 112.0, 89.0, 128.0, 115.0, 110.0, 188.0, 106.0, 97.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006122319, -0.007802336, 0.009818702, -0.016480861, 0.012765809, -0.03676343, 0.04598022, -0.0205716, -0.015855854, -0.01783148, 0.024956996, -0.06991275, 0.005357672, -0.009199401, 0.005329514, -0.019475594, -0.023304611, -0.014051489, 0.0153165925, -0.010905939, 0.0062450143, -0.031102775, 0.010317607, 0.00518915, -0.05798058, -0.09590857, -0.0006717768, -0.01418668, -0.0029022894, 0.00053297513, -0.00062902615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, -1, 15, 17, -1, -1, -1, 19, 21, -1, -1, -1, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1], "loss_changes": [1.455562, 2.2453892, 0.0, 2.2740335, 0.0, 2.672175, 5.7162204, 1.5301385, 0.0, 1.7671018, 0.0, 2.3973076, 2.2491665, 0.0, 0.0, 0.0, 2.2061641, 1.3852241, 0.0, 0.0, 0.0, 1.3495758, 0.0, 0.0, 0.99333835, 0.84533525, 0.0061369506, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 12, 12, 16, 16, 17, 17, 21, 21, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, -1, 16, 18, -1, -1, -1, 20, 22, -1, -1, -1, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1], "split_conditions": [5.0, 1.0, 0.009818702, 1.0, 0.012765809, 2.0, 1.0, -0.15384616, -0.015855854, 1.0, 0.024956996, -1.0, 1.0, -0.009199401, 0.005329514, -0.019475594, 1.0, 2.2692308, 0.0153165925, -0.010905939, 0.0062450143, 0.07692308, 0.010317607, 0.00518915, 1.0, 0.88461536, 0.53846157, -0.01418668, -0.0029022894, 0.00053297513, -0.00062902615], "split_indices": [0, 102, 0, 61, 0, 0, 0, 1, 0, 93, 0, 0, 73, 0, 0, 0, 69, 1, 0, 0, 0, 1, 0, 0, 93, 1, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1910.0, 139.0, 1795.0, 115.0, 1355.0, 440.0, 1196.0, 159.0, 335.0, 105.0, 412.0, 784.0, 164.0, 171.0, 112.0, 300.0, 693.0, 91.0, 150.0, 150.0, 605.0, 88.0, 148.0, 457.0, 275.0, 182.0, 163.0, 112.0, 88.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0027243772, 0.009869908, -0.013643659, 0.01777793, -0.012110667, 0.032785848, -0.0443324, 0.045144774, -0.0090621635, -0.011346218, 0.0048119817, 0.029511394, 0.020942776, -0.0057638623, 0.008716446, -0.015810415, 0.08636967, 0.08558087, -0.0613939, 0.14519502, -0.006220204, -0.005399046, 0.021509303, -0.11148784, 0.009231314, 0.23654953, 0.0024822012, -0.018593393, -0.0781672, 0.011331553, 0.033415083, -0.002102745, -0.015346348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [2.0563753, 2.0373504, 0.0, 1.7291324, 0.0, 2.278625, 1.2264404, 3.4877486, 0.0, 0.0, 1.0851651, 3.1953807, 0.0, 0.0, 0.0, 3.1890202, 4.8068805, 3.868304, 3.6651013, 4.3326664, 0.0, 0.0, 0.0, 0.89053154, 0.0, 2.6942253, 0.0, 0.0, 1.0669974, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 23, 23, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.013643659, 1.0, -0.012110667, 1.3461539, 1.0, 0.84615386, -0.0090621635, -0.011346218, 1.0, 1.0, 0.020942776, -0.0057638623, 0.008716446, -0.34615386, 1.0, -0.5, 1.0, 1.0, -0.006220204, -0.005399046, 0.021509303, 1.0, 0.009231314, 1.0, 0.0024822012, -0.018593393, 1.0, 0.011331553, 0.033415083, -0.002102745, -0.015346348], "split_indices": [117, 43, 0, 80, 0, 1, 122, 1, 0, 0, 13, 124, 0, 0, 0, 1, 106, 1, 62, 53, 0, 0, 0, 71, 0, 109, 0, 0, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1967.0, 101.0, 1855.0, 112.0, 1494.0, 361.0, 1358.0, 136.0, 150.0, 211.0, 1240.0, 118.0, 120.0, 91.0, 690.0, 550.0, 214.0, 476.0, 394.0, 156.0, 103.0, 111.0, 359.0, 117.0, 224.0, 170.0, 111.0, 248.0, 99.0, 125.0, 141.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0013795709, 0.008821344, -0.012818699, 0.0154417185, -0.011237736, 0.0064020418, 0.010191267, -0.0022955798, 0.011512234, -0.009509607, 0.010011529, 0.002131639, -0.01681363, -0.006922013, 0.013030698, -0.0548105, 0.034980413, -0.10981317, -0.007789283, 0.064444765, -0.0062478594, -0.004740776, -0.016485125, 0.009914453, -0.0067022243, -0.0003641538, 0.013359494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.9881902, 1.5646424, 0.0, 1.4453065, 0.0, 1.582948, 0.0, 1.1451316, 0.0, 2.6738956, 0.0, 1.565454, 0.0, 2.5283716, 0.0, 1.5207394, 1.9296932, 0.9307964, 2.0078802, 2.4294212, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.012818699, 1.0, -0.011237736, 5.0, 0.010191267, 3.1538463, 0.011512234, 1.3461539, 0.010011529, 1.0769231, -0.01681363, 1.0, 0.013030698, 1.0, 1.0, 1.0, -0.34615386, -0.1923077, -0.0062478594, -0.004740776, -0.016485125, 0.009914453, -0.0067022243, -0.0003641538, 0.013359494], "split_indices": [43, 117, 0, 125, 0, 0, 0, 1, 0, 1, 0, 1, 0, 124, 0, 15, 80, 93, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1950.0, 112.0, 1849.0, 101.0, 1674.0, 175.0, 1550.0, 124.0, 1448.0, 102.0, 1349.0, 99.0, 1260.0, 89.0, 588.0, 672.0, 271.0, 317.0, 516.0, 156.0, 127.0, 144.0, 113.0, 204.0, 260.0, 256.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [0.0011359482, 0.00745824, -0.012218001, 0.015298596, -0.05931733, 0.004951348, 0.107435584, 0.0018259416, -0.012385023, -0.013070494, 0.014281722, 0.02388169, -0.0026931644, 0.02610184, -0.06425502, 0.0081162015, 0.09272746, -0.020016849, 0.0071658413, -0.008209985, 0.017191047, -0.005053773, 0.020771665, -0.08255888, 0.03375303, -0.00068179664, -0.14772142, 0.024758847, -0.018017652, -0.02159098, -0.008029068, -0.006558762, 0.0044100396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, -1, -1, 15, 17, 19, 21, -1, -1, 23, -1, -1, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1], "loss_changes": [1.614633, 1.031382, 0.0, 1.6807809, 1.0362947, 2.006172, 3.1422956, 0.0, 0.0, 0.0, 1.3766888, 0.0, 0.0, 1.5446141, 3.583659, 2.714248, 4.5138626, 0.0, 0.0, 2.8796713, 0.0, 0.0, 0.0, 1.6435115, 6.531541, 0.0, 0.82304, 0.0, 1.4036031, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 23, 23, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, -1, -1, 16, 18, 20, 22, -1, -1, 24, -1, -1, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.012218001, 1.0, 1.0, -0.53846157, 1.0, 0.0018259416, -0.012385023, -0.013070494, 1.3461539, 0.02388169, -0.0026931644, 1.0, 3.1923077, 5.0, 1.0, -0.020016849, 0.0071658413, -0.1923077, 0.017191047, -0.005053773, 0.020771665, 1.0, -0.03846154, -0.00068179664, -0.34615386, 0.024758847, 1.0, -0.02159098, -0.008029068, -0.006558762, 0.0044100396], "split_indices": [117, 40, 0, 125, 108, 1, 15, 0, 0, 0, 1, 0, 0, 61, 1, 0, 17, 0, 0, 1, 0, 0, 0, 71, 1, 0, 1, 0, 93, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1970.0, 101.0, 1763.0, 207.0, 1585.0, 178.0, 94.0, 113.0, 102.0, 1483.0, 90.0, 88.0, 1289.0, 194.0, 1015.0, 274.0, 97.0, 97.0, 923.0, 92.0, 122.0, 152.0, 333.0, 590.0, 154.0, 179.0, 115.0, 475.0, 89.0, 90.0, 269.0, 206.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003907797, 0.00089752866, -0.008849887, 0.010999122, -0.041357666, 0.019385664, -0.0069376575, -0.08232351, 0.0017315972, 0.0063381814, 0.015189911, -0.011695916, -0.005593442, 0.00844214, -0.0056479713, 0.0065563447, -0.012443667, 0.083928615, -0.019301746, -0.004408914, 0.019688547, 0.004903324, -0.019884488, 0.072674066, -0.062267665, 0.022330822, -0.0051762834, -0.011641898, 0.003915129], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, -1, -1, -1, 15, 17, -1, 19, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.8393972, 0.8340546, 0.0, 1.0630147, 0.906163, 2.4689643, 0.0, 0.20290828, 0.0, 1.2166929, 0.0, 0.0, 0.0, 0.0, 1.6338516, 2.044715, 0.0, 3.7018843, 3.328925, 0.0, 0.0, 3.072754, 0.0, 6.298135, 1.861778, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 14, 14, 15, 15, 17, 17, 18, 18, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, -1, -1, -1, 16, 18, -1, 20, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008849887, 1.0, 1.0, 1.0, -0.0069376575, 1.0, 0.0017315972, 1.0, 0.015189911, -0.011695916, -0.005593442, 0.00844214, 1.0, 0.0, -0.012443667, 1.0, 1.0, -0.004408914, 0.019688547, 1.0, -0.019884488, 1.0, 1.0, 0.022330822, -0.0051762834, -0.011641898, 0.003915129], "split_indices": [43, 80, 0, 90, 111, 125, 0, 106, 0, 89, 0, 0, 0, 0, 119, 0, 0, 13, 64, 0, 0, 13, 0, 124, 109, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1954.0, 111.0, 1577.0, 377.0, 1428.0, 149.0, 222.0, 155.0, 1300.0, 128.0, 96.0, 126.0, 173.0, 1127.0, 1022.0, 105.0, 256.0, 766.0, 120.0, 136.0, 675.0, 91.0, 336.0, 339.0, 152.0, 184.0, 221.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014058056, 0.009717149, -0.05864268, 0.0018017663, 0.011490666, 0.020806646, -0.016987177, -0.0074984594, 0.011923685, -0.012571412, 0.014019397, -0.02279654, 0.055265572, -0.014234843, -0.010588388, 0.017847788, -0.007936576, 0.021558525, -0.049177706, 0.0023241036, -0.0045137173, 0.01899155, -0.027591435, -0.10967711, 0.007067826, 0.012329902, -0.07458304, -0.004322376, -0.016488448, -0.009179904, 0.009850111, -0.017486934, -0.00692577, 0.0058232434, -0.006018555], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, -1, 13, 15, -1, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [1.3146683, 1.4395913, 2.9692583, 1.7562141, 0.0, 3.4285736, 0.0, 1.4306521, 0.0, 0.0, 0.0, 1.7484896, 2.2738872, 0.0, 1.3484534, 0.0, 0.22384642, 4.906921, 1.680994, 0.0, 0.0, 0.0, 3.2545784, 0.8731537, 2.314169, 0.0, 2.3747835, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7252951, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 11, 11, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18, 22, 22, 23, 23, 24, 24, 26, 26, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, -1, 14, 16, -1, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 5.0, 1.0, 1.0, 0.011490666, -0.3846154, -0.016987177, 1.0, 0.011923685, -0.012571412, 0.014019397, -0.5, 1.0, -0.014234843, 1.0, 0.017847788, 1.0, -0.23076923, 0.03846154, 0.0023241036, -0.0045137173, 0.01899155, 0.0, 1.0, 1.0, 0.012329902, 0.30769232, -0.004322376, -0.016488448, -0.009179904, 0.009850111, -0.017486934, 1.0, 0.0058232434, -0.006018555], "split_indices": [64, 0, 126, 125, 0, 1, 0, 74, 0, 0, 0, 1, 122, 0, 106, 0, 124, 1, 1, 0, 0, 0, 0, 71, 39, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1729.0, 336.0, 1608.0, 121.0, 196.0, 140.0, 1490.0, 118.0, 88.0, 108.0, 1198.0, 292.0, 111.0, 1087.0, 99.0, 193.0, 593.0, 494.0, 105.0, 88.0, 134.0, 459.0, 238.0, 256.0, 109.0, 350.0, 108.0, 130.0, 123.0, 133.0, 141.0, 209.0, 94.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00075033825, -0.0049925437, 0.009202453, 0.0006778179, -0.01162885, 0.010557318, -0.05822636, 0.020146979, -0.00788835, -0.015280599, 0.007713528, -0.017200261, 0.04505969, 0.031501364, -0.016161412, 0.16103294, 0.00039684222, -0.036071457, 0.01671229, -0.00070110307, 0.02650602, 0.0617312, -0.105249554, -0.0123270415, 0.009665614, 0.111524895, -0.008714175, -0.0058157737, -0.015806278, 0.0011089601, 0.020217752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.080329, 1.2236807, 0.0, 1.0736862, 0.0, 1.355177, 3.392651, 1.3277107, 0.0, 0.0, 0.0, 4.015952, 4.4338193, 3.913169, 0.0, 4.160514, 4.004488, 3.2985067, 0.0, 0.0, 0.0, 2.8984575, 0.56456447, 0.0, 0.0, 2.6676824, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 25, 25], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.009202453, 1.0, -0.01162885, 1.0, 1.0, -0.115384616, -0.00788835, -0.015280599, 0.007713528, 1.0, 0.1923077, 1.0, -0.016161412, 1.0, 1.0, 1.0, 0.01671229, -0.00070110307, 0.02650602, 1.0, 1.0, -0.0123270415, 0.009665614, 1.0, -0.008714175, -0.0058157737, -0.015806278, 0.0011089601, 0.020217752], "split_indices": [102, 117, 0, 64, 0, 41, 105, 1, 0, 0, 0, 7, 1, 61, 0, 127, 109, 97, 0, 0, 0, 62, 59, 0, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1939.0, 122.0, 1845.0, 94.0, 1580.0, 265.0, 1427.0, 153.0, 156.0, 109.0, 571.0, 856.0, 427.0, 144.0, 238.0, 618.0, 285.0, 142.0, 91.0, 147.0, 391.0, 227.0, 172.0, 113.0, 293.0, 98.0, 120.0, 107.0, 139.0, 154.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0012866182, 0.01127344, -0.0040904954, -0.009948174, 0.0034926878, -0.0146861775, 0.103736155, 0.04113091, -0.03354984, 0.021434946, -0.0010083307, 0.0136181265, -0.03308647, -0.019451777, -0.014556932, -0.0101423785, 0.0046753134, -0.005905227, -0.010652269, -0.022576412, 0.010274001, -0.007634078, -0.013253246, -0.036953088, 0.044729903, 0.004174232, -0.009378868, -0.0064288746, 0.018329568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, -1, -1, -1, 19, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [1.2368883, 0.0, 1.4243144, 0.0, 3.3238988, 1.6257006, 3.525186, 2.7512121, 1.8224635, 0.0, 0.0, 0.0, 1.1948695, 1.208998, 0.0, 0.0, 0.0, 1.606574, 0.0, 1.2634666, 0.0, 1.0393713, 0.0, 1.9411521, 3.6708205, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 12, 12, 13, 13, 17, 17, 19, 19, 21, 21, 23, 23, 24, 24], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, -1, -1, -1, 20, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.01127344, -0.5, -0.009948174, 1.0, 0.0, 1.0, 1.0, 1.0, 0.021434946, -0.0010083307, 0.0136181265, 1.0, 1.3461539, -0.014556932, -0.0101423785, 0.0046753134, 0.88461536, -0.010652269, 0.5769231, 0.010274001, 0.03846154, -0.013253246, 1.0, 1.0, 0.004174232, -0.009378868, -0.0064288746, 0.018329568], "split_indices": [1, 0, 1, 0, 42, 0, 126, 122, 40, 0, 0, 0, 126, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 53, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 95.0, 1969.0, 145.0, 1824.0, 1544.0, 280.0, 390.0, 1154.0, 142.0, 138.0, 171.0, 219.0, 1025.0, 129.0, 118.0, 101.0, 887.0, 138.0, 769.0, 118.0, 677.0, 92.0, 434.0, 243.0, 182.0, 252.0, 136.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.00061114324, 0.0053266543, -0.011639823, 0.010166093, 0.00028379032, -0.004830364, 0.007829711, 0.0052330713, -0.01469436, -0.019538322, 0.027512843, -0.057756547, 0.015534754, -0.018246515, 0.06927978, 0.005022187, -0.014797501, -0.04478492, 0.090344645, -0.06571877, 0.007882359, 0.16912504, -0.014540913, -0.009425073, 0.009638844, 0.0020110866, -0.010408627, 0.015815742, 0.002476742, 0.0012262731, -0.015867274, 0.0005531809, 0.03568267, -0.011515123, 0.0115493145], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, -1, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.4094168, 0.947311, 0.0, 0.0, 0.73929566, 2.4870272, 0.0, 0.8962873, 0.0, 1.0307915, 1.6341002, 2.084279, 1.809516, 1.8801198, 3.7409878, 1.9682323, 0.0, 0.85434675, 0.7960075, 1.9861413, 0.0, 6.2641706, 3.1791127, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, -1, -1, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.011639823, 0.010166093, 3.1923077, 1.3461539, 0.007829711, 1.0, -0.01469436, 1.0, -0.1923077, 1.0, 1.0, 1.0, 0.15384616, 1.0, -0.014797501, 1.0, 0.15384616, 1.0, 0.007882359, 1.0, 1.0, -0.009425073, 0.009638844, 0.0020110866, -0.010408627, 0.015815742, 0.002476742, 0.0012262731, -0.015867274, 0.0005531809, 0.03568267, -0.011515123, 0.0115493145], "split_indices": [117, 1, 0, 0, 1, 1, 0, 71, 0, 115, 1, 39, 12, 61, 1, 13, 0, 39, 1, 124, 0, 59, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1950.0, 100.0, 97.0, 1853.0, 1739.0, 114.0, 1624.0, 115.0, 769.0, 855.0, 368.0, 401.0, 408.0, 447.0, 217.0, 151.0, 222.0, 179.0, 274.0, 134.0, 204.0, 243.0, 104.0, 113.0, 106.0, 116.0, 88.0, 91.0, 149.0, 125.0, 109.0, 95.0, 137.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.006150379, 0.0006214029, 0.009645696, -0.0061915875, 0.009272296, -0.039741997, 0.019359833, -0.068938255, 0.018145135, 0.089584745, -0.041975845, -0.016671052, -0.04630898, -0.0045300126, 0.12614565, -0.014057926, -0.0069329627, -0.07814545, 0.019786287, 0.070227325, 0.18032557, -0.010615465, 0.037910994, -0.016706457, -0.03791208, 0.007087961, -0.0030208218, -0.0022587855, 0.016011154, 0.016975125, 0.019268202, -0.0012452456, 0.0101677645, 0.0064003877, -0.011468879], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, 27, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0385498, 1.2298748, 0.0, 1.5645007, 0.0, 5.095375, 4.462355, 1.5421224, 0.0, 2.3819215, 1.9108067, 0.0, 1.1910002, 0.0, 1.1512661, 0.0, 1.8153936, 1.3666105, 0.470007, 1.5600702, 0.02521658, 0.0, 0.90243423, 0.0, 2.0579152, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, 28, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.009645696, 1.0, 0.009272296, 0.6923077, 0.115384616, 1.0, 0.018145135, -1.0, 1.0, -0.016671052, 0.115384616, -0.0045300126, 1.0, -0.014057926, 1.0, 1.0, 1.0, 1.0, -0.26923078, -0.010615465, 1.0, -0.016706457, -0.30769232, 0.007087961, -0.0030208218, -0.0022587855, 0.016011154, 0.016975125, 0.019268202, -0.0012452456, 0.0101677645, 0.0064003877, -0.011468879], "split_indices": [102, 0, 0, 124, 0, 1, 1, 89, 0, 0, 137, 0, 1, 0, 50, 0, 81, 15, 13, 59, 1, 0, 39, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1960.0, 120.0, 1825.0, 135.0, 789.0, 1036.0, 697.0, 92.0, 483.0, 553.0, 131.0, 566.0, 103.0, 380.0, 145.0, 408.0, 382.0, 184.0, 187.0, 193.0, 127.0, 281.0, 119.0, 263.0, 91.0, 93.0, 92.0, 95.0, 104.0, 89.0, 157.0, 124.0, 113.0, 150.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0067488537, 0.00058281346, -0.07301998, 0.0051952503, -0.008113649, 0.0009205222, -0.015444691, 0.014829191, -0.07272485, 0.005951187, 0.009594185, -0.017018605, 0.002781403, 0.02593788, -0.049252693, 0.07529819, -0.015108191, -0.013090238, 0.01246373, -0.0093278345, 0.027017644, 0.042680286, -0.09542439, 0.007163852, -0.006840845, -0.087598406, 0.010199029, 0.015171744, -0.042653125, -0.01356219, -0.005384074, -0.007855891, -0.009663787, 0.00022440361, -0.008658479], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, 21, -1, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9999369, 0.698442, 1.3725466, 1.3166884, 0.0, 0.0, 0.0, 1.1241047, 1.8911378, 1.5524035, 0.0, 0.0, 0.0, 2.0929067, 1.884634, 7.734643, 2.6177223, 0.0, 1.0193316, 2.849129, 0.0, 3.0518808, 0.39448714, 0.0, 0.0, 0.0156883, 0.0, 0.0, 0.36292318, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 22, 22, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, 22, -1, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.3461539, -0.008113649, 0.0009205222, -0.015444691, 0.88461536, 3.1923077, 0.1923077, 0.009594185, -0.017018605, 0.002781403, 1.0, 1.0, -0.1923077, 1.0, -0.013090238, 1.0, 1.0, 0.027017644, 1.0, 1.0, 0.007163852, -0.006840845, 1.0, 0.010199029, 0.015171744, 1.0, -0.01356219, -0.005384074, -0.007855891, -0.009663787, 0.00022440361, -0.008658479], "split_indices": [40, 14, 13, 1, 0, 0, 0, 1, 1, 1, 0, 0, 0, 69, 69, 1, 121, 0, 59, 97, 0, 53, 53, 0, 0, 124, 0, 0, 15, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1853.0, 205.0, 1754.0, 99.0, 102.0, 103.0, 1561.0, 193.0, 1407.0, 154.0, 98.0, 95.0, 1033.0, 374.0, 469.0, 564.0, 161.0, 213.0, 327.0, 142.0, 328.0, 236.0, 123.0, 90.0, 192.0, 135.0, 144.0, 184.0, 120.0, 116.0, 96.0, 96.0, 91.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026951476, -0.01244591, 0.0531646, -0.0033410992, -0.009834545, 0.013483576, -0.0037518514, -0.032439265, 0.026238332, -0.07305377, 0.01966333, 0.011839014, 0.0026851485, 0.001697137, -0.12193341, -0.031407274, 0.014046498, 0.04557141, -0.04543468, -0.058369838, -0.021921327, 0.007345829, -0.009514911, -0.0042182985, 0.10526223, 0.0101304045, -0.13142674, 0.0010505427, -0.012724514, 0.0038625577, 0.017257882, -0.018405108, -0.007992205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, -1, 17, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1214861, 1.3710192, 2.2662957, 1.3642209, 0.0, 0.0, 0.0, 1.6907811, 1.7059879, 1.9757783, 2.159295, 0.0, 1.2918634, 0.0, 1.7993851, 1.6443436, 0.0, 1.7338221, 3.7224183, 0.8349099, 0.0, 0.0, 0.0, 0.0, 0.8836925, 0.0, 0.50413346, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, -1, 18, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, -0.009834545, 0.013483576, -0.0037518514, 1.0, 1.0, -0.34615386, 1.0, 0.011839014, 1.0, 0.001697137, 1.0, 0.0, 0.014046498, 1.0, 1.0, 1.0, -0.021921327, 0.007345829, -0.009514911, -0.0042182985, 0.03846154, 0.0101304045, 1.0, 0.0010505427, -0.012724514, 0.0038625577, 0.017257882, -0.018405108, -0.007992205], "split_indices": [1, 40, 115, 124, 0, 0, 0, 115, 81, 1, 0, 0, 15, 0, 39, 0, 0, 93, 5, 23, 0, 0, 0, 0, 1, 0, 53, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1753.0, 306.0, 1585.0, 168.0, 161.0, 145.0, 799.0, 786.0, 449.0, 350.0, 160.0, 626.0, 158.0, 291.0, 246.0, 104.0, 331.0, 295.0, 176.0, 115.0, 93.0, 153.0, 134.0, 197.0, 109.0, 186.0, 88.0, 88.0, 99.0, 98.0, 92.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0022889695, 0.007847428, -0.009288742, 0.010521891, 0.002700499, 0.009069305, -0.0076494194, 0.034832433, -0.020291662, 0.015359081, 0.020882254, -0.06284106, 0.04413265, 0.014887097, -0.0031328658, -0.014014067, -0.03950532, -0.0067163813, 0.11871877, 0.08346901, -0.034728643, -0.07944169, 0.0031959743, 0.0052343, 0.019014485, 0.023060018, -0.0078055384, -0.10012713, 0.012931028, -0.013793997, -0.00014439684, -0.020350475, -0.0031465855, -0.0077819154, 0.009574059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, -1, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, 19, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0929843, 0.978272, 0.0, 0.0, 0.9351121, 1.2980344, 0.0, 3.0967877, 2.198455, 2.029432, 0.0, 0.87125576, 2.6480744, 0.0, 1.9755751, 0.0, 1.0588547, 0.0, 0.9055226, 4.5866985, 1.6488248, 1.0859298, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5828619, 2.2995849, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 16, 16, 18, 18, 19, 19, 20, 20, 21, 21, 27, 27, 28, 28], "right_children": [2, 4, -1, -1, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, 20, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.009288742, 0.010521891, 1.0, 1.0, -0.0076494194, 1.0, 1.0, 1.0, 0.020882254, 0.0, 1.0, 0.014887097, -0.30769232, -0.014014067, 1.0, -0.0067163813, 1.0, 1.0, 1.0, 1.0, 0.0031959743, 0.0052343, 0.019014485, 0.023060018, -0.0078055384, 1.0, 1.0, -0.013793997, -0.00014439684, -0.020350475, -0.0031465855, -0.0077819154, 0.009574059], "split_indices": [43, 1, 0, 0, 90, 97, 0, 119, 115, 26, 0, 0, 39, 0, 1, 0, 59, 0, 74, 59, 59, 50, 0, 0, 0, 0, 0, 71, 106, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1952.0, 114.0, 98.0, 1854.0, 1716.0, 138.0, 914.0, 802.0, 822.0, 92.0, 483.0, 319.0, 100.0, 722.0, 112.0, 371.0, 128.0, 191.0, 193.0, 529.0, 238.0, 133.0, 99.0, 92.0, 101.0, 92.0, 223.0, 306.0, 136.0, 102.0, 89.0, 134.0, 146.0, 160.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0048642266, 0.009163206, 0.0005390731, -0.00909993, 0.0077737207, -0.0069640446, 0.08928044, -0.007833013, 0.0008344037, 0.019312, -0.0018348865, 0.026756933, -0.047589943, 0.001186149, 0.0141592985, 0.003908551, -0.09520563, 0.10336989, -0.037370164, -0.0070767696, 0.00805329, -0.020941205, -0.002492472, 0.020778384, 0.0011524262, -0.015391925, -0.008097364, 0.037610814, -0.057799473, -0.0036416054, 0.010176746, -0.013734184, 0.00039144044], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, -1, 5, 7, 9, -1, 11, -1, -1, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1], "loss_changes": [0.7745863, 0.0, 1.3019788, 0.0, 2.1886356, 0.8587488, 3.1181552, 0.0, 1.7460966, 0.0, 0.0, 2.6604214, 1.1892861, 2.919413, 0.0, 1.3332304, 2.0226855, 1.9467618, 1.8355038, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.976871, 1.0638468, 1.0112273, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 8, 8, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 26, 26, 27, 27, 28, 28], "right_children": [2, -1, 4, -1, 6, 8, 10, -1, 12, -1, -1, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009163206, -0.5, -0.00909993, 1.0, -0.3846154, 1.0, -0.007833013, 1.0, 0.019312, -0.0018348865, 1.0, 0.23076923, 0.0, 0.0141592985, 1.0, 0.65384614, 1.0, -0.115384616, -0.0070767696, 0.00805329, -0.020941205, -0.002492472, 0.020778384, 0.0011524262, -0.015391925, 0.6923077, 1.0, 1.0, -0.0036416054, 0.010176746, -0.013734184, 0.00039144044], "split_indices": [1, 0, 1, 0, 42, 1, 126, 0, 109, 0, 0, 105, 1, 0, 0, 97, 1, 106, 1, 0, 0, 0, 0, 0, 0, 0, 1, 115, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 98.0, 1966.0, 144.0, 1822.0, 1543.0, 279.0, 152.0, 1391.0, 142.0, 137.0, 906.0, 485.0, 741.0, 165.0, 233.0, 252.0, 203.0, 538.0, 118.0, 115.0, 96.0, 156.0, 95.0, 108.0, 108.0, 430.0, 224.0, 206.0, 104.0, 120.0, 90.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0032286043, -0.001190923, 0.007441985, -0.043652587, 0.0072329636, 0.0049152668, -0.10965848, 0.009102537, -0.0011062929, -0.009277764, -0.012708974, -0.012747559, 0.053702764, -0.0040126652, -0.011077244, 0.012256331, -0.0038319926, -0.033011254, 0.034814913, -0.06770984, 0.006287181, 0.12064363, -0.0320479, -0.031989638, -0.016030757, 0.020140216, 0.0026425312, -0.01596414, 0.003728034, -0.009887656, 0.003118417, -0.0069117653, 0.0050913733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, 11, -1, -1, 13, 15, 17, -1, -1, -1, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.64593905, 0.69141924, 0.0, 1.9602222, 1.1271105, 0.0, 0.055024862, 0.0, 0.9360146, 0.0, 0.0, 1.0360463, 1.6285408, 1.2509248, 0.0, 0.0, 0.0, 2.1159763, 2.7259064, 1.5446532, 0.0, 1.5826576, 2.3618376, 0.79136723, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.76301265, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 8, 8, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 30, 30], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, 12, -1, -1, 14, 16, 18, -1, -1, -1, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, -1.0, 0.007441985, 1.0, 0.0, 0.0049152668, 1.0, 0.009102537, 1.0, -0.009277764, -0.012708974, 1.0, 1.0, 1.0, -0.011077244, 0.012256331, -0.0038319926, 1.0, 1.0, 1.0, 0.006287181, 1.0, 1.0, 1.0, -0.016030757, 0.020140216, 0.0026425312, -0.01596414, 0.003728034, -0.009887656, 1.0, -0.0069117653, 0.0050913733], "split_indices": [102, 0, 0, 122, 0, 0, 13, 0, 42, 0, 0, 73, 12, 15, 0, 0, 0, 61, 53, 0, 0, 126, 59, 59, 0, 0, 0, 0, 0, 0, 81, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1933.0, 120.0, 320.0, 1613.0, 133.0, 187.0, 146.0, 1467.0, 95.0, 92.0, 1210.0, 257.0, 1111.0, 99.0, 147.0, 110.0, 636.0, 475.0, 467.0, 169.0, 208.0, 267.0, 337.0, 130.0, 112.0, 96.0, 94.0, 173.0, 116.0, 221.0, 88.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.001415071, -0.0054645706, 0.0082984995, -0.008294656, 0.0011621766, 0.030662067, -0.019206142, 0.05380941, -0.006849391, 0.00049819367, -0.012169135, 0.019429512, 0.0040409206, -0.03950483, 0.044492245, -0.014133199, 0.058780108, -0.012916765, 0.002005725, 0.014232382, 0.015319564, 0.014257069, -0.0022966785, 0.009750391, -0.06620725, -0.014622869, 0.0753995, -0.0014792193, -0.01110045, 0.017099282, 0.0018798175], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, -1, 13, -1, -1, 15, 17, 19, -1, 21, -1, 23, -1, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [0.7091892, 1.0166383, 0.0, 0.0, 1.0959744, 1.7099223, 2.178936, 4.2230234, 0.0, 1.5927052, 0.0, 0.0, 3.549087, 1.7642062, 1.2300777, 0.0, 2.2192774, 0.0, 2.110606, 0.0, 3.2223287, 0.0, 0.0, 0.0, 0.43531436, 0.0, 1.3093911, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 16, 16, 18, 18, 20, 20, 24, 24, 26, 26], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, -1, 14, -1, -1, 16, 18, 20, -1, 22, -1, 24, -1, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 0.0082984995, -0.008294656, -0.03846154, 1.0, 1.0, 1.0, -0.006849391, 1.0, -0.012169135, 0.019429512, 0.0, 0.34615386, 0.1923077, -0.014133199, -0.30769232, -0.012916765, 1.0, 0.014232382, 0.53846157, 0.014257069, -0.0022966785, 0.009750391, 1.1923077, -0.014622869, 1.0, -0.0014792193, -0.01110045, 0.017099282, 0.0018798175], "split_indices": [66, 1, 0, 0, 1, 62, 64, 69, 0, 39, 0, 0, 0, 1, 1, 0, 1, 0, 108, 0, 1, 0, 0, 0, 1, 0, 108, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1980.0, 95.0, 156.0, 1824.0, 745.0, 1079.0, 604.0, 141.0, 905.0, 174.0, 158.0, 446.0, 474.0, 431.0, 122.0, 324.0, 150.0, 324.0, 99.0, 332.0, 160.0, 164.0, 135.0, 189.0, 90.0, 242.0, 88.0, 101.0, 90.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0011642703, 0.010063043, -0.04424834, -0.006927786, 0.07145658, 0.005405045, -0.01431288, 0.0056366767, -0.07281885, 0.12093515, -0.002179147, -0.011732952, 0.010252508, -0.015871096, 0.0043882346, 0.020763135, 0.0069705555, -0.064085975, 0.033822287, -0.022622706, -0.020573527, 0.024081782, -0.016554927, 0.04948729, -0.019366499, 0.03263131, -0.009259748, 0.014203513, -0.009118541, 0.011602699, -0.004815823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, 17, -1, -1, -1, -1, -1, 19, 21, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [0.8361169, 1.80461, 3.295022, 1.1217856, 1.7301679, 0.0, 0.0, 1.9151582, 2.1751456, 1.088145, 0.0, 2.3014812, 0.0, 0.0, 0.0, 0.0, 0.0, 3.1677642, 5.3807755, 0.0, 4.2929335, 0.0, 1.5522027, 3.280777, 0.0, 1.6978498, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 17, 17, 18, 18, 20, 20, 22, 22, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, 18, -1, -1, -1, -1, -1, 20, 22, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.03846154, 1.0, 1.0, 0.005405045, -0.01431288, 1.0, 1.0, -0.07692308, -0.002179147, 0.0, 0.010252508, -0.015871096, 0.0043882346, 0.020763135, 0.0069705555, 1.0, 0.1923077, -0.022622706, 1.0, 0.024081782, 1.0, 1.0, -0.019366499, 1.0, -0.009259748, 0.014203513, -0.009118541, 0.011602699, -0.004815823], "split_indices": [64, 116, 1, 58, 80, 0, 0, 42, 12, 1, 0, 1, 0, 0, 0, 0, 0, 89, 1, 0, 105, 0, 127, 7, 0, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1730.0, 339.0, 1355.0, 375.0, 170.0, 169.0, 1138.0, 217.0, 245.0, 130.0, 965.0, 173.0, 125.0, 92.0, 91.0, 154.0, 449.0, 516.0, 95.0, 354.0, 101.0, 415.0, 252.0, 102.0, 252.0, 163.0, 152.0, 100.0, 124.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.00641131, 0.0030767522, -0.07078692, -0.0070856716, 0.016054774, -0.021020202, 0.009016379, -0.05217897, 0.0035361566, 0.0022072543, -0.011693321, 0.0233263, -0.043970026, -0.003574261, 0.11442139, 0.0058206315, -0.08796768, -0.05362905, 0.047980797, 0.0022782604, 0.017786361, -0.003496384, -0.017547397, -0.08682474, 0.0029360151, 0.09697739, -0.003924797, -0.0033238367, -0.018087426, 0.0018267324, 0.017113395], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, 13, 15, 17, 19, -1, 21, 23, 25, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [1.2600802, 2.8773162, 5.9463243, 0.8089861, 0.0, 0.0, 0.0, 1.5482087, 1.2851907, 0.0, 0.0, 2.3647418, 1.8071988, 1.9225302, 1.2790284, 0.0, 1.3033249, 1.0413461, 1.5685263, 0.0, 0.0, 0.0, 0.0, 1.360738, 0.0, 1.3716633, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 23, 23, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, 14, 16, 18, 20, -1, 22, 24, 26, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, -1.0, 0.016054774, -0.021020202, 0.009016379, 1.0, 0.6923077, 0.0022072543, -0.011693321, 1.0, 1.0, 1.0, 1.0, 0.0058206315, 1.0, 0.115384616, 1.0, 0.0022782604, 0.017786361, -0.003496384, -0.017547397, -0.1923077, 0.0029360151, 1.0, -0.003924797, -0.0033238367, -0.018087426, 0.0018267324, 0.017113395], "split_indices": [0, 102, 122, 0, 0, 0, 0, 127, 1, 0, 0, 83, 108, 124, 5, 0, 17, 1, 97, 0, 0, 0, 0, 1, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1798.0, 265.0, 1689.0, 109.0, 142.0, 123.0, 322.0, 1367.0, 150.0, 172.0, 965.0, 402.0, 745.0, 220.0, 121.0, 281.0, 378.0, 367.0, 90.0, 130.0, 175.0, 106.0, 270.0, 108.0, 235.0, 132.0, 172.0, 98.0, 114.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0015939244, -0.0071450826, 0.052979294, -0.032276787, 0.018915271, 0.012881312, -0.0031399166, -0.07881671, 0.0087993285, 0.050601676, -0.01904407, -0.13802956, -0.012760149, -0.041564405, 0.014611922, 0.08749719, -0.005294376, 0.01791757, -0.012346971, -0.005366367, -0.024095593, 0.011188367, -0.016681435, -0.0044561424, -0.010724011, -0.0011700419, 0.14665173, -0.010057419, 0.08784714, 0.0059350845, -0.005357742, 0.005720768, 0.026464174, 0.017869374, 0.0003694476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.92685395, 1.155316, 1.9196227, 1.7166885, 1.0416207, 0.0, 0.0, 1.6466975, 3.2989051, 1.8032107, 1.5207388, 1.9277306, 3.821179, 0.8505516, 0.0, 2.0420609, 0.0, 2.4112487, 0.0, 0.0, 0.0, 0.0, 0.0, 0.6989446, 0.0, 0.0, 2.3006625, 0.0, 1.3990316, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 23, 23, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.012881312, -0.0031399166, 1.0, 1.0, 0.7692308, 1.0, 1.0, -0.07692308, 1.0, 0.014611922, -0.1923077, -0.005294376, -0.42307693, -0.012346971, -0.005366367, -0.024095593, 0.011188367, -0.016681435, 1.0, -0.010724011, -0.0011700419, 1.0, -0.010057419, -0.1923077, 0.0059350845, -0.005357742, 0.005720768, 0.026464174, 0.017869374, 0.0003694476], "split_indices": [1, 124, 115, 106, 15, 0, 0, 97, 0, 1, 106, 126, 1, 12, 0, 1, 0, 1, 0, 0, 0, 0, 0, 69, 0, 0, 71, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1764.0, 300.0, 898.0, 866.0, 158.0, 142.0, 421.0, 477.0, 472.0, 394.0, 222.0, 199.0, 349.0, 128.0, 348.0, 124.0, 291.0, 103.0, 122.0, 100.0, 110.0, 89.0, 223.0, 126.0, 130.0, 218.0, 108.0, 183.0, 97.0, 126.0, 124.0, 94.0, 88.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.003504844, 0.007146167, -0.006536515, 0.001303194, 0.06410716, -0.0052487, 0.008678737, 0.015703222, -0.003191538, -0.0152942715, 0.03738139, -0.0065493765, -0.007553114, 0.011836243, -0.0052607846, 0.006581124, -0.017017974, 0.00512672, -0.006345138, -0.04192765, 0.011607216, 0.013542511, -0.1118384, -0.044420708, 0.056983456, -0.004988035, 0.006193454, -0.00016626787, -0.021327, 0.004506926, -0.011511029, 0.010521744, -0.0038936369], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, 17, -1, 19, -1, -1, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.51936084, 0.65465975, 0.0, 0.99918884, 1.6328909, 0.7095996, 0.0, 0.0, 0.0, 0.7063919, 1.091214, 0.8870489, 0.0, 0.0, 0.6809053, 0.0, 0.7294444, 0.0, 0.0, 2.121245, 1.2101521, 0.93609416, 2.704422, 1.3474402, 1.2167947, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 14, 14, 16, 16, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, 18, -1, 20, -1, -1, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.006536515, 5.0, 1.0, 1.0, 0.008678737, 0.015703222, -0.003191538, 1.0, 1.0, 1.0, -0.007553114, 0.011836243, 1.0, 0.006581124, 1.0, 0.00512672, -0.006345138, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.004988035, 0.006193454, -0.00016626787, -0.021327, 0.004506926, -0.011511029, 0.010521744, -0.0038936369], "split_indices": [117, 125, 0, 0, 15, 74, 0, 0, 0, 62, 122, 81, 0, 0, 69, 0, 53, 0, 0, 97, 17, 13, 2, 26, 58, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1967.0, 104.0, 1784.0, 183.0, 1657.0, 127.0, 93.0, 90.0, 1341.0, 316.0, 1171.0, 170.0, 109.0, 207.0, 148.0, 1023.0, 105.0, 102.0, 547.0, 476.0, 305.0, 242.0, 213.0, 263.0, 132.0, 173.0, 116.0, 126.0, 94.0, 119.0, 175.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.003755169, -0.002846431, 0.04175201, -0.0720944, 0.007103761, 0.013726174, -0.0064710197, -0.016804596, -3.196003e-05, 0.0004617381, 0.011576424, -0.0076289163, 0.0054379627, 0.03306442, -0.032774, -0.06615239, 0.073016234, -0.01539782, -0.00039607318, 0.0042597465, -0.018918255, 0.01315657, 0.020325635, -0.04370322, 0.04367892, 0.09809328, -0.007816265, -0.012639815, 0.0033962936, -0.0061915345, 0.013398983, 0.015702141, 0.0042848117], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, -1, 13, -1, -1, -1, 15, 17, 19, 21, -1, 23, -1, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5202422, 1.2175174, 1.4139712, 1.5288918, 1.1150655, 0.0, 0.8666899, 0.0, 0.0, 1.5776842, 0.0, 0.0, 0.0, 2.9134603, 2.8294497, 2.8230777, 4.0851707, 0.0, 1.0860858, 0.0, 0.0, 2.7845314, 0.0, 1.8432854, 2.6892407, 0.60552156, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 21, 21, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, -1, 14, -1, -1, -1, 16, 18, 20, 22, -1, 24, -1, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.013726174, 2.7307692, -0.016804596, -3.196003e-05, 1.0, 0.011576424, -0.0076289163, 0.0054379627, 1.0, 1.0, 1.0, 1.0, -0.01539782, 1.0, 0.0042597465, -0.018918255, 1.0, 0.020325635, -0.07692308, 1.0, 1.0, -0.007816265, -0.012639815, 0.0033962936, -0.0061915345, 0.013398983, 0.015702141, 0.0042848117], "split_indices": [1, 26, 137, 116, 44, 0, 1, 0, 0, 126, 0, 0, 0, 89, 59, 69, 61, 0, 97, 0, 0, 108, 0, 1, 2, 13, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1767.0, 307.0, 222.0, 1545.0, 103.0, 204.0, 95.0, 127.0, 1456.0, 89.0, 95.0, 109.0, 735.0, 721.0, 211.0, 524.0, 152.0, 569.0, 112.0, 99.0, 359.0, 165.0, 287.0, 282.0, 186.0, 173.0, 139.0, 148.0, 130.0, 152.0, 90.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040689656, -0.00012624305, -0.007932517, 0.0074575595, -0.0039612055, -0.0065638707, 0.0009530507, 0.027713506, -0.02011839, 0.013643562, 0.006615626, -0.08298268, 0.0034222784, 0.010384487, -0.017265234, -0.018737642, -0.0018296976, 0.009109139, -0.021489158, 0.004201493, -0.009968939, 0.009311106, -0.009015566, -0.0504356, 0.06660282, -0.005037844, 0.059974447, 0.004412139, -0.01789098, -0.002354874, 0.014946944, 0.014328025, -0.004438664], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, -1, 25, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6139017, 0.56321704, 0.0, 0.0, 0.5667954, 0.0, 0.9766425, 1.7501736, 1.4339917, 0.0, 1.4837059, 1.7827339, 1.5396948, 0.0, 0.90769076, 0.0, 0.0, 0.0, 1.1611056, 1.3876368, 0.0, 1.1461233, 0.0, 2.6361456, 1.4194057, 0.0, 1.7822466, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 14, 14, 18, 18, 19, 19, 21, 21, 23, 23, 24, 24, 26, 26], "right_children": [2, 4, -1, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, -1, 26, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5769231, -0.007932517, 0.0074575595, -0.5, -0.0065638707, 1.0, -0.34615386, 1.0, 0.013643562, 1.0, -0.115384616, 0.0, 0.010384487, 1.0, -0.018737642, -0.0018296976, 0.009109139, 1.0, 1.0, -0.009968939, 1.0, -0.009015566, 1.0, 1.0, -0.005037844, 0.46153846, 0.004412139, -0.01789098, -0.002354874, 0.014946944, 0.014328025, -0.004438664], "split_indices": [117, 1, 0, 0, 1, 0, 59, 1, 111, 0, 89, 1, 0, 0, 113, 0, 0, 0, 74, 12, 0, 69, 0, 122, 71, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1966.0, 103.0, 96.0, 1870.0, 138.0, 1732.0, 763.0, 969.0, 124.0, 639.0, 264.0, 705.0, 126.0, 513.0, 101.0, 163.0, 156.0, 549.0, 407.0, 106.0, 379.0, 170.0, 217.0, 190.0, 174.0, 205.0, 125.0, 92.0, 91.0, 99.0, 114.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021966894, 0.009860505, -0.06434867, 0.0038174945, 0.009031742, -0.020969136, -0.0078932205, -0.0045552244, 0.010784855, 0.010851226, -0.011329978, -0.019575914, 0.05660043, -0.010980346, -0.0098174345, 0.014806377, 0.01016518, -0.028176537, 0.033262823, 0.0062160683, -0.004025467, 0.0014170224, -0.016051395, -0.010222747, 0.015035321, 0.02595452, -0.008224901, -0.0050028046, 0.015843859, -0.006834937, 0.0054635652], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, 19, 21, 23, -1, -1, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [1.5497148, 0.8421016, 2.757009, 1.403218, 0.0, 0.0, 2.9693158, 1.3696326, 0.0, 0.0, 0.0, 0.80869055, 1.2486537, 0.8209183, 0.0, 0.0, 0.5112131, 3.042993, 4.791113, 0.0, 0.0, 1.3036264, 0.0, 0.0, 0.0, 2.0137644, 0.0, 1.5036007, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 18, 18, 21, 21, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, 20, 22, 24, -1, -1, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.0, 5.0, -0.3846154, 1.0, 0.009031742, -0.020969136, 0.23076923, 1.0, 0.010784855, 0.010851226, -0.011329978, 1.0, 1.0, 1.0, -0.0098174345, 0.014806377, 1.0, 1.0, 1.0, 0.0062160683, -0.004025467, 0.7307692, -0.016051395, -0.010222747, 0.015035321, 0.26923078, -0.008224901, 1.0, 0.015843859, -0.006834937, 0.0054635652], "split_indices": [64, 0, 1, 125, 0, 0, 1, 74, 0, 0, 0, 88, 122, 61, 0, 0, 13, 0, 109, 0, 0, 1, 0, 0, 0, 1, 0, 126, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1732.0, 336.0, 1611.0, 121.0, 94.0, 242.0, 1491.0, 120.0, 115.0, 127.0, 1197.0, 294.0, 1079.0, 118.0, 99.0, 195.0, 777.0, 302.0, 96.0, 99.0, 635.0, 142.0, 140.0, 162.0, 491.0, 144.0, 398.0, 93.0, 193.0, 205.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00047432003, 0.0068778777, -0.050476916, -0.006647008, 0.011728034, -0.018583765, 0.004577958, 0.009167867, 0.0020591004, -0.005716859, 0.012865874, 0.012809247, -0.03832281, -0.017750982, 0.017176349, 0.0053179897, -0.06691741, 0.040064324, -0.079102874, -0.015069497, -0.028391963, -0.0044416264, 0.106739596, -0.020944955, -0.018747779, -0.008614025, 0.0060369275, 0.021276897, -0.0008208111, 0.0033403968, -0.009929902], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, -1, 7, -1, -1, -1, 9, 11, -1, 13, 15, 17, -1, -1, 19, 21, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6750508, 0.6538666, 3.00978, 0.0, 1.3327161, 0.0, 0.0, 0.0, 1.5140594, 0.8752849, 0.0, 4.4884944, 1.373654, 2.7489855, 0.0, 0.0, 1.2910267, 2.2474742, 2.9580235, 0.0, 1.4044719, 0.0, 2.7178876, 0.0, 1.0796285, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 16, 16, 17, 17, 18, 18, 20, 20, 22, 22, 24, 24], "right_children": [2, 4, 6, -1, 8, -1, -1, -1, 10, 12, -1, 14, 16, 18, -1, -1, 20, 22, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.30769232, -0.006647008, -0.46153846, -0.018583765, 0.004577958, 0.009167867, 1.0, 1.0, 0.012865874, 1.0, 1.0, 1.0, 0.017176349, 0.0053179897, -0.15384616, 0.42307693, 1.0, -0.015069497, 1.0, -0.0044416264, 1.0, -0.020944955, 0.15384616, -0.008614025, 0.0060369275, 0.021276897, -0.0008208111, 0.0033403968, -0.009929902], "split_indices": [119, 104, 1, 0, 1, 0, 0, 0, 90, 109, 0, 105, 81, 127, 0, 0, 1, 1, 111, 0, 122, 0, 12, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1838.0, 231.0, 114.0, 1724.0, 96.0, 135.0, 186.0, 1538.0, 1449.0, 89.0, 924.0, 525.0, 775.0, 149.0, 125.0, 400.0, 399.0, 376.0, 126.0, 274.0, 176.0, 223.0, 119.0, 257.0, 166.0, 108.0, 116.0, 107.0, 156.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013335925, 0.007651531, -0.005277832, -0.03844895, 0.012109833, -0.010350509, -0.083537124, 0.023771208, -0.013698019, -0.010707402, 0.026850829, -0.01548145, -0.0046000504, 0.015343636, 0.0028778948, 0.09323266, -0.010929964, 0.018951397, -0.008080172, 0.01788359, 0.0011784912, 0.054427598, -0.040507145, 0.01309255, 0.032230355, -0.099917345, 0.004907227, -0.0035258674, 0.06745274, -0.0046009794, -0.016034117, -0.00073632796, 0.014676901], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, -1, 3, 5, 7, 9, 11, 13, -1, -1, 15, -1, -1, -1, 17, 19, -1, 21, -1, -1, -1, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.63038313, 0.0, 1.1270052, 0.8513616, 2.2288783, 1.4896731, 0.6902816, 3.2211607, 0.0, 0.0, 2.7023375, 0.0, 0.0, 0.0, 1.3773049, 1.4014095, 0.0, 1.811943, 0.0, 0.0, 0.0, 0.91354656, 1.70834, 0.0, 0.99126107, 0.62865806, 0.0, 0.0, 1.6259512, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 10, 10, 14, 14, 15, 15, 17, 17, 21, 21, 22, 22, 24, 24, 25, 25, 28, 28], "right_children": [2, -1, 4, 6, 8, 10, 12, 14, -1, -1, 16, -1, -1, -1, 18, 20, -1, 22, -1, -1, -1, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.007651531, -0.1923077, 1.0, 1.0, -0.5, 1.0, -0.03846154, -0.013698019, -0.010707402, 1.0, -0.01548145, -0.0046000504, 0.015343636, 1.0, 1.0, -0.010929964, 1.0, -0.008080172, 0.01788359, 0.0011784912, 1.0, 1.0, 0.01309255, 1.0, 1.0, 0.004907227, -0.0035258674, 1.0, -0.0046009794, -0.016034117, -0.00073632796, 0.014676901], "split_indices": [1, 0, 1, 126, 41, 1, 81, 1, 0, 0, 16, 0, 0, 0, 64, 39, 0, 109, 0, 0, 0, 26, 93, 0, 59, 59, 0, 0, 81, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 99.0, 1954.0, 672.0, 1282.0, 414.0, 258.0, 1189.0, 93.0, 115.0, 299.0, 89.0, 169.0, 165.0, 1024.0, 201.0, 98.0, 859.0, 165.0, 98.0, 103.0, 538.0, 321.0, 121.0, 417.0, 193.0, 128.0, 143.0, 274.0, 102.0, 91.0, 141.0, 133.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0017283182, 0.0070482804, -0.03716317, -0.000626836, 0.015572135, -0.01104588, 0.0018458319, -0.028716305, 0.029279193, -0.08368086, 0.018330596, 0.05982918, -0.006256922, -0.04321256, -0.13488108, -0.025731016, 0.014311311, 0.007298154, 0.013906591, -0.0074574873, 0.040080458, 0.00019192293, -0.009745753, -0.024864879, -0.0027229877, 0.010709549, -0.11575089, 0.012216616, -0.051098414, -0.0027851115, 0.014575182, -0.007212294, -0.016702494, -0.0036309704, -0.0066391253], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42642337, 2.0687842, 1.0110497, 1.4482367, 0.0, 0.0, 0.0, 2.2988763, 0.9064995, 0.84951377, 2.633599, 1.8689111, 1.2219505, 0.5606318, 2.2167482, 4.2327867, 0.0, 1.8111326, 0.0, 0.0, 1.6510376, 0.0, 0.0, 0.0, 0.0, 0.0, 0.4720025, 0.0, 0.04048258, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.1923077, 1.0, 1.0, 0.015572135, -0.01104588, 0.0018458319, 1.0, 1.0, 1.0, 1.0, 1.0, -0.34615386, 1.0, 1.0, -0.34615386, 0.014311311, 0.07692308, 0.013906591, -0.0074574873, 1.0, 0.00019192293, -0.009745753, -0.024864879, -0.0027229877, 0.010709549, 1.0, 0.012216616, 0.6923077, -0.0027851115, 0.014575182, -0.007212294, -0.016702494, -0.0036309704, -0.0066391253], "split_indices": [1, 1, 23, 124, 0, 0, 0, 15, 15, 126, 62, 71, 1, 122, 106, 1, 0, 1, 0, 0, 97, 0, 0, 0, 0, 0, 39, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1813.0, 248.0, 1724.0, 89.0, 107.0, 141.0, 889.0, 835.0, 410.0, 479.0, 449.0, 386.0, 229.0, 181.0, 354.0, 125.0, 270.0, 179.0, 156.0, 230.0, 125.0, 104.0, 88.0, 93.0, 143.0, 211.0, 91.0, 179.0, 140.0, 90.0, 114.0, 97.0, 91.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0015120553, 0.009670257, -0.054000508, -0.0001511715, 0.016755437, -0.018715417, 0.009848189, -0.007597819, 0.009001052, -0.019412344, 0.041960794, -0.012962164, -0.009177945, 0.011818918, -0.011570359, -0.021953499, 0.0074526803, -0.007932167, 0.0056180917, -0.03407947, 0.009076849, -0.017864311, -0.014520918, 0.01810212, -0.08898895, 0.0055170045, -0.0030296596, -0.0038624255, -0.017309798], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, -1, 15, -1, 17, 19, -1, -1, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1], "loss_changes": [0.9401843, 2.8066726, 5.4007554, 1.1440692, 0.0, 0.0, 0.0, 0.9215951, 0.0, 1.4335936, 1.2364202, 0.0, 1.2436825, 0.0, 0.8170625, 1.3791649, 0.0, 0.0, 0.0, 1.6416087, 0.0, 2.0336895, 0.0, 0.94725317, 1.1310446, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 12, 12, 14, 14, 15, 15, 19, 19, 21, 21, 23, 23, 24, 24], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, -1, 16, -1, 18, 20, -1, -1, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.016755437, -0.018715417, 0.009848189, 1.0, 0.009001052, -0.5, 0.1923077, -0.012962164, 1.0, 0.011818918, 1.0384616, 1.0, 0.0074526803, -0.007932167, 0.0056180917, 1.0, 0.009076849, 1.0, -0.014520918, 1.0, 1.0, 0.0055170045, -0.0030296596, -0.0038624255, -0.017309798], "split_indices": [0, 102, 122, 125, 0, 0, 0, 74, 0, 1, 1, 0, 42, 0, 1, 88, 0, 0, 0, 0, 0, 109, 0, 97, 106, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1810.0, 266.0, 1704.0, 106.0, 142.0, 124.0, 1574.0, 130.0, 1271.0, 303.0, 108.0, 1163.0, 125.0, 178.0, 1009.0, 154.0, 89.0, 89.0, 911.0, 98.0, 795.0, 116.0, 528.0, 267.0, 299.0, 229.0, 167.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.000934762, -0.0065793023, 0.0068669026, 0.05244725, -0.0024281375, 0.12442226, -0.0073662787, -0.063115664, 0.011225376, 0.030775523, -0.0043490147, -0.01781606, 0.0019253765, 0.021754345, -0.012417938, 0.013072806, 0.0041915867, 0.020734368, -0.008379976, 0.05519305, -0.0367321, 0.031075953, 0.014778544, -0.075488344, 0.0021402247, -0.018803146, 0.012810106, -0.0055607166, -0.009820967, -0.009942167, 0.005120766], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, -1, -1, -1, -1, 15, -1, -1, 17, 19, -1, 21, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [0.8193858, 0.0, 0.80539894, 2.9227207, 1.3083563, 6.3106966, 0.0, 2.748094, 1.8376913, 0.0, 0.0, 0.0, 0.0, 2.2889988, 0.0, 0.0, 1.4992901, 1.7168496, 0.0, 1.210318, 0.73224765, 2.080996, 0.0, 0.08808625, 0.0, 1.6029438, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 13, 13, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23, 25, 25], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, -1, -1, -1, -1, 16, -1, -1, 18, 20, -1, 22, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [-0.53846157, -0.0065793023, -0.34615386, 1.0, -0.1923077, 1.0, -0.0073662787, 1.0, 1.0, 0.030775523, -0.0043490147, -0.01781606, 0.0019253765, -0.03846154, -0.012417938, 0.013072806, 1.0, 1.0, -0.008379976, 1.0, 1.0, 1.2692307, 0.014778544, 0.84615386, 0.0021402247, 1.0, 0.012810106, -0.0055607166, -0.009820967, -0.009942167, 0.005120766], "split_indices": [1, 0, 1, 126, 1, 108, 0, 13, 41, 0, 0, 0, 0, 1, 0, 0, 64, 109, 0, 0, 93, 1, 0, 1, 0, 124, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 169.0, 1901.0, 322.0, 1579.0, 205.0, 117.0, 290.0, 1289.0, 98.0, 107.0, 121.0, 169.0, 1196.0, 93.0, 166.0, 1030.0, 867.0, 163.0, 542.0, 325.0, 430.0, 112.0, 195.0, 130.0, 284.0, 146.0, 104.0, 91.0, 132.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0077232304, 0.00060694094, -0.049939267, 0.05692964, -0.0071514156, 0.019289153, -0.01655017, 0.00023822363, 0.012706198, -0.047751885, 0.01337145, -0.014460574, 0.014737511, -0.011691529, -0.02104154, 0.06966082, -0.007153106, -0.01375738, 0.032040287, 0.02773839, -0.031892672, -0.069167316, 0.035856742, 0.0887065, -0.004452396, -0.009294072, 0.0026501084, -0.015955742, 0.0029382985, 0.017603269, -0.0077629513, 0.008297984, 0.00944982, 0.009999622, -0.056443643, -0.011976606, 0.004245314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, 17, -1, 19, 21, -1, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, 35, -1, -1], "loss_changes": [0.7233789, 0.7507174, 2.7120697, 0.7957113, 1.2581893, 4.4504385, 0.0, 0.0, 0.0, 2.973911, 1.1587806, 0.0, 0.0, 2.2846005, 0.0, 5.6534624, 1.9604082, 0.0, 1.3362908, 0.0, 0.6416687, 2.6812997, 2.6536639, 0.005870104, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.7363569, 0.0, 0.0, 0.0, 1.4278238, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 13, 13, 15, 15, 16, 16, 18, 18, 20, 20, 21, 21, 22, 22, 23, 23, 30, 30, 34, 34], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, 18, -1, 20, 22, -1, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, -0.46153846, 0.23076923, -0.53846157, -0.115384616, -0.3846154, -0.01655017, 0.00023822363, 0.012706198, 3.0, 1.0, -0.014460574, 0.014737511, 1.0, -0.02104154, 0.23076923, 0.42307693, -0.01375738, 1.0, 0.02773839, 1.0, 1.0, 1.0, 1.0, -0.004452396, -0.009294072, 0.0026501084, -0.015955742, 0.0029382985, 0.017603269, 1.0, 0.008297984, 0.00944982, 0.009999622, 1.5769231, -0.011976606, 0.004245314], "split_indices": [64, 1, 1, 1, 1, 1, 0, 0, 0, 0, 69, 0, 0, 2, 0, 1, 1, 0, 124, 0, 137, 124, 124, 39, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1718.0, 339.0, 208.0, 1510.0, 212.0, 127.0, 117.0, 91.0, 507.0, 1003.0, 93.0, 119.0, 415.0, 92.0, 268.0, 735.0, 107.0, 308.0, 88.0, 180.0, 301.0, 434.0, 177.0, 131.0, 88.0, 92.0, 157.0, 144.0, 103.0, 331.0, 89.0, 88.0, 103.0, 228.0, 139.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012873153, 0.002062276, -0.005911967, 0.005261479, -0.005778541, -0.0043211295, 0.009286705, -0.0014069777, 0.061217, -0.022271482, 0.056624155, 0.01345278, -0.0035367056, 0.008549003, -0.060880303, -0.0405984, 0.14541416, 0.0404192, -0.03351964, -0.0058114445, -0.11117044, -0.011810943, 0.0037783512, 0.023206186, 0.006223234, 0.014592121, -0.015262407, -0.01451641, 0.0063127787, -0.0072667687, 0.007078111, -0.024347888, 0.00066667306, -0.0083072735, 0.005510677], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, -1, -1, 7, 9, 11, 13, 15, -1, -1, 17, 19, 21, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.39982745, 0.3735478, 0.0, 0.3613509, 0.0, 0.0, 0.94960773, 1.7169013, 2.0675516, 1.2411102, 3.2371464, 0.0, 0.0, 0.7776269, 1.2822412, 1.0875081, 1.412672, 1.9385928, 2.6975381, 1.1316726, 3.7729871, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0306996, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 26, 26], "right_children": [2, 4, -1, 6, -1, -1, 8, 10, 12, 14, 16, -1, -1, 18, 20, 22, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.005911967, 1.0, -0.005778541, -0.0043211295, 1.0, 1.0, 1.0, 1.0, 1.0, 0.01345278, -0.0035367056, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.011810943, 0.0037783512, 0.023206186, 0.006223234, 0.014592121, 1.0, -0.01451641, 0.0063127787, -0.0072667687, 0.007078111, -0.024347888, 0.00066667306, -0.0083072735, 0.005510677], "split_indices": [43, 117, 0, 104, 0, 0, 62, 113, 124, 109, 109, 0, 0, 12, 105, 59, 39, 126, 39, 111, 93, 0, 0, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1951.0, 113.0, 1852.0, 99.0, 142.0, 1710.0, 1418.0, 292.0, 1043.0, 375.0, 166.0, 126.0, 580.0, 463.0, 179.0, 196.0, 330.0, 250.0, 221.0, 242.0, 90.0, 89.0, 96.0, 100.0, 114.0, 216.0, 116.0, 134.0, 118.0, 103.0, 114.0, 128.0, 110.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.005744107, 0.002917989, 0.006264134, 0.018952169, -0.011263145, -0.07471916, 0.05378403, 0.0043599596, -0.011083296, -0.016624214, 0.0065800943, 0.11084374, -0.015435938, 0.012871285, -0.018397428, 0.043639477, 0.022782892, -0.011987874, 0.0069734664, -0.06729329, 0.03867081, -0.0033229466, 0.0092216395, -0.017922765, -0.030463263, -0.002478293, 0.06590199, -0.010630816, 0.0048375493, 0.010618583, 3.3356955e-05], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, -1, 19, 21, -1, -1, -1, 23, 25, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [0.33301312, 0.4486264, 0.0, 3.0213034, 1.6287024, 3.2280662, 2.6660283, 2.5611024, 0.0, 0.0, 0.0, 2.9089036, 2.7131143, 0.0, 2.1346567, 0.8775035, 0.0, 0.0, 0.0, 1.6984876, 0.6099558, 0.0, 0.0, 0.0, 1.8536506, 0.0, 0.65241265, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 15, 15, 19, 19, 20, 20, 24, 24, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, -1, 20, 22, -1, -1, -1, 24, 26, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.006264134, 1.0, 1.0, 1.0, 1.0, 1.0, -0.011083296, -0.016624214, 0.0065800943, 1.0, 1.0, 0.012871285, 1.0, 1.0, 0.022782892, -0.011987874, 0.0069734664, 1.0, 1.0, -0.0033229466, 0.0092216395, -0.017922765, 1.0, -0.002478293, 1.0, -0.010630816, 0.0048375493, 0.010618583, 3.3356955e-05], "split_indices": [66, 126, 0, 89, 64, 97, 97, 89, 0, 0, 0, 50, 17, 0, 12, 106, 0, 0, 0, 59, 69, 0, 0, 0, 81, 0, 50, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1973.0, 98.0, 926.0, 1047.0, 251.0, 675.0, 905.0, 142.0, 152.0, 99.0, 370.0, 305.0, 140.0, 765.0, 235.0, 135.0, 137.0, 168.0, 412.0, 353.0, 91.0, 144.0, 102.0, 310.0, 106.0, 247.0, 158.0, 152.0, 153.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00011034581, 0.008166569, -0.0039303117, -0.0076512457, 0.0015557755, -0.0102729695, 0.066592984, 0.0028279698, -0.07438818, 0.015229518, -0.000120937024, -0.012224741, 0.015282287, -0.011914027, -0.0004435856, 0.027698534, -0.009593779, 0.014274415, 0.0129634505, -0.02230361, 0.06855777, -0.06725513, 0.028481305, 0.016875213, -0.0048565115, -0.013981595, -0.0017084498, 0.010884811, -0.0031525888, 0.005797567, -0.0051101022], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, -1, -1, 17, -1, 19, -1, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6841196, 0.0, 0.7876239, 0.0, 1.4147586, 1.3069928, 1.64446, 2.012585, 0.8264551, 0.0, 0.0, 0.0, 1.6225994, 0.0, 0.0, 1.4463989, 0.0, 1.8545297, 0.0, 1.2738355, 2.7657418, 1.0775644, 1.2635179, 0.0, 0.6305247, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 12, 12, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, -1, -1, 18, -1, 20, -1, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008166569, -0.5, -0.0076512457, 1.0, 1.0, -0.03846154, -0.3846154, 1.0, 0.015229518, -0.000120937024, -0.012224741, 1.4615384, -0.011914027, -0.0004435856, 1.2692307, -0.009593779, 1.0, 0.0129634505, 1.0, 0.115384616, 1.0, 1.0, 0.016875213, 1.0, -0.013981595, -0.0017084498, 0.010884811, -0.0031525888, 0.005797567, -0.0051101022], "split_indices": [1, 0, 1, 0, 42, 113, 1, 1, 137, 0, 0, 0, 1, 0, 0, 1, 0, 124, 0, 97, 1, 122, 39, 0, 12, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 98.0, 1978.0, 139.0, 1839.0, 1556.0, 283.0, 1292.0, 264.0, 125.0, 158.0, 117.0, 1175.0, 161.0, 103.0, 1057.0, 118.0, 934.0, 123.0, 558.0, 376.0, 296.0, 262.0, 159.0, 217.0, 121.0, 175.0, 112.0, 150.0, 92.0, 125.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0018923501, -0.0040197154, 0.036461245, -0.028699769, 0.021808244, -0.0019080415, 0.009018784, -0.061603986, 0.007105511, 0.086323075, 0.004126396, -0.114927344, -0.015291258, -0.027008351, 0.011573998, 0.022082519, -0.0035699424, 0.042404108, -0.06436222, -0.004519328, -0.020354768, -0.0069838758, 0.0030833443, 0.00414713, -0.010251158, 0.012646529, -0.00446453, 0.0007629028, -0.013174606, -0.014648885, 0.06926392, 0.0051908167, 0.0086431], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, 19, 21, 23, -1, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [0.42121404, 1.1218863, 0.89820355, 1.0603304, 0.9810374, 0.0, 0.0, 1.1582187, 1.597261, 3.036273, 1.7695717, 1.3472075, 0.6315129, 1.6959027, 0.0, 0.0, 0.0, 1.7059484, 1.1739538, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.911003, 0.0, 0.0, 0.0, 0.054524004, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 17, 17, 18, 18, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, 20, 22, 24, -1, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, -0.0019080415, 0.009018784, 1.0, 1.0, 0.03846154, 1.0, 1.0, 1.0, 1.0, 0.011573998, 0.022082519, -0.0035699424, -0.30769232, 1.0, -0.004519328, -0.020354768, -0.0069838758, 0.0030833443, 0.00414713, -0.010251158, 0.012646529, -0.07692308, 0.0007629028, -0.013174606, -0.014648885, 0.26923078, 0.0051908167, 0.0086431], "split_indices": [1, 124, 39, 97, 81, 0, 0, 122, 62, 1, 97, 126, 15, 39, 0, 0, 0, 1, 71, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1760.0, 301.0, 900.0, 860.0, 148.0, 153.0, 469.0, 431.0, 185.0, 675.0, 218.0, 251.0, 328.0, 103.0, 88.0, 97.0, 433.0, 242.0, 122.0, 96.0, 115.0, 136.0, 172.0, 156.0, 155.0, 278.0, 117.0, 125.0, 95.0, 183.0, 91.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010506384, 0.003245499, -0.008205764, -0.0030454632, 0.040856957, -0.04397422, 0.008635044, 0.010996032, -0.0034950438, -0.013764705, 0.01974017, 0.051630687, -0.031131173, 0.011311757, -0.010509065, 0.15123689, 0.0033581678, -0.009877392, -0.01738696, -0.00047355713, 0.027822336, -0.009301557, 0.054687675, 0.0098276725, -0.010662929, -0.007654652, 0.02020738, 0.038474098, -0.009056619, 0.0052468753, 0.009996924, -0.00932784, 0.009182972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, -1, -1, 19, 21, 23, -1, -1, -1, -1, 25, 27, -1, -1, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [0.71865577, 0.4639968, 0.0, 0.8031555, 1.4720315, 2.2261791, 2.234675, 0.0, 0.0, 0.0, 2.587716, 3.019576, 2.0599027, 0.0, 0.0, 4.0603104, 2.0925035, 1.1267433, 0.0, 0.0, 0.0, 0.0, 5.3384194, 1.4120792, 0.0, 0.0, 0.0, 0.7805451, 0.0, 2.1155887, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 22, 22, 23, 23, 27, 27, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, -1, -1, 20, 22, 24, -1, -1, -1, -1, 26, 28, -1, -1, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.2692307, -0.008205764, 1.0, 1.0, -0.3846154, 1.0, 0.010996032, -0.0034950438, -0.013764705, 0.115384616, -0.34615386, 1.0, 0.011311757, -0.010509065, 1.0, -0.115384616, 0.5769231, -0.01738696, -0.00047355713, 0.027822336, -0.009301557, 1.0, 2.0, -0.010662929, -0.007654652, 0.02020738, 1.0, -0.009056619, 1.0, 0.009996924, -0.00932784, 0.009182972], "split_indices": [43, 1, 0, 89, 115, 1, 126, 0, 0, 0, 1, 1, 113, 0, 0, 93, 1, 1, 0, 0, 0, 0, 71, 0, 0, 0, 0, 124, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1961.0, 104.0, 1680.0, 281.0, 373.0, 1307.0, 147.0, 134.0, 151.0, 222.0, 628.0, 679.0, 127.0, 95.0, 205.0, 423.0, 591.0, 88.0, 92.0, 113.0, 147.0, 276.0, 491.0, 100.0, 146.0, 130.0, 382.0, 109.0, 248.0, 134.0, 116.0, 132.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0013695864, -0.0015361172, 0.006084574, -0.00633591, 0.002735654, -0.007759266, 0.042605396, 0.015491495, -0.041461132, 0.017228384, -0.0063738585, 0.08315412, -0.014266478, -0.022867922, -0.0100645125, -0.01020156, 0.008655462, 0.002977519, 0.015976274, -0.044597574, 0.010782252, 0.002850659, -0.006357678, 0.001858347, -0.08162753, -0.003519892, 0.0054120882, -0.003964748, -0.014026632], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, -1, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, -1, -1, -1, -1, -1, 23, -1, 25, -1, -1, 27, -1, -1, -1, -1], "loss_changes": [0.3561824, 0.51894414, 0.0, 0.0, 0.7690741, 1.1401294, 2.4326456, 1.7336258, 0.65364945, 0.0, 2.4708202, 1.0754819, 2.2144496, 0.47323245, 0.0, 0.0, 0.0, 0.0, 0.0, 1.1206646, 0.0, 0.5403745, 0.0, 0.0, 0.74342036, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 19, 19, 21, 21, 24, 24], "right_children": [2, 4, -1, -1, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, -1, -1, -1, -1, -1, 24, -1, 26, -1, -1, 28, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.006084574, -0.00633591, 1.0, 1.0, 1.0, 1.0, 1.0, 0.017228384, 1.0, 1.0, 1.0, 1.0, -0.0100645125, -0.01020156, 0.008655462, 0.002977519, 0.015976274, 1.0, 0.010782252, 1.0, -0.006357678, 0.001858347, 1.0, -0.003519892, 0.0054120882, -0.003964748, -0.014026632], "split_indices": [66, 104, 0, 0, 113, 109, 69, 126, 42, 0, 109, 97, 105, 108, 0, 0, 0, 0, 0, 16, 0, 12, 0, 0, 83, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1965.0, 96.0, 127.0, 1838.0, 1455.0, 383.0, 861.0, 594.0, 105.0, 278.0, 263.0, 598.0, 452.0, 142.0, 137.0, 141.0, 155.0, 108.0, 479.0, 119.0, 277.0, 175.0, 177.0, 302.0, 159.0, 118.0, 176.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.002568997, -0.03210698, 0.00515465, -0.12742029, 0.0114834355, 0.01611899, -0.046485085, -0.02129301, -0.006528314, 0.1007442, -0.003261416, -0.0004385782, -0.008629325, 0.0032443255, 0.018829359, -0.010674892, 0.015869785, 0.09891131, -0.0076260096, 0.0011987961, 0.021122796, 0.040611777, -0.047437456, 0.016486559, -0.045013886, 0.015210574, -0.11434951, 0.006707067, -0.01630604, 0.008966408, -0.0045947633, -0.006208539, -0.017300142], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, -1, 9, 11, -1, -1, 13, 15, -1, -1, -1, -1, -1, 17, 19, 21, -1, -1, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.46997046, 5.9803333, 0.92459744, 1.376153, 0.0, 2.209176, 0.47930586, 0.0, 0.0, 1.5009055, 2.1699052, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8047923, 1.9916389, 1.3846201, 0.0, 0.0, 3.4684167, 1.6558042, 0.0, 2.5536206, 0.92890245, 0.5854888, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 9, 9, 10, 10, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, 12, -1, -1, 14, 16, -1, -1, -1, -1, -1, 18, 20, 22, -1, -1, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.0114834355, -0.34615386, 1.0, -0.02129301, -0.006528314, 1.0, -0.1923077, -0.0004385782, -0.008629325, 0.0032443255, 0.018829359, -0.010674892, 0.07692308, 1.0, 1.0, 0.0011987961, 0.021122796, 1.0, 1.0, 0.016486559, 1.0769231, 1.0, 1.0, 0.006707067, -0.01630604, 0.008966408, -0.0045947633, -0.006208539, -0.017300142], "split_indices": [89, 97, 7, 124, 0, 1, 39, 0, 0, 39, 1, 0, 0, 0, 0, 0, 1, 124, 97, 0, 0, 124, 122, 0, 1, 109, 115, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 427.0, 1633.0, 259.0, 168.0, 1347.0, 286.0, 109.0, 150.0, 251.0, 1096.0, 139.0, 147.0, 141.0, 110.0, 171.0, 925.0, 204.0, 721.0, 115.0, 89.0, 326.0, 395.0, 133.0, 193.0, 204.0, 191.0, 99.0, 94.0, 92.0, 112.0, 101.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0030961558, 0.010238057, -0.05013286, -0.0007703509, 0.011215862, 0.0012361993, -0.014548587, 0.009041443, -0.006473076, -0.014296609, 0.0076938705, -0.054560132, 0.001912343, -0.11253344, 0.014322119, 0.013587911, -0.010937038, -0.0062577287, -0.016480235, 0.0057474296, -0.002791189, -0.006959725, 0.032707084, 0.006975712, 0.012814115, -0.041564655, 0.039474256, -0.014446487, 0.003334062, 0.01288716, -0.002494441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, -1, -1, -1, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, -1, 23, 25, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.7873037, 2.0487416, 1.4599731, 0.85696256, 0.0, 0.0, 0.0, 0.0, 1.0121434, 0.92542875, 0.0, 1.6252865, 1.3135809, 0.5770645, 0.3389831, 1.4552448, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8270028, 0.92441, 0.0, 1.811326, 2.0213597, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 22, 22, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, -1, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, -1, 24, 26, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.3461539, 0.88461536, 1.0, -0.5769231, 0.011215862, 0.0012361993, -0.014548587, 0.009041443, 1.0, 1.0, 0.0076938705, 1.0, 1.0, -0.15384616, 1.0, -1.0, -0.010937038, -0.0062577287, -0.016480235, 0.0057474296, -0.002791189, -0.006959725, 1.0, 1.0, 0.012814115, 1.0, 1.0, -0.014446487, 0.003334062, 0.01288716, -0.002494441], "split_indices": [1, 1, 17, 1, 0, 0, 0, 0, 44, 17, 0, 137, 116, 1, 59, 0, 0, 0, 0, 0, 0, 0, 42, 15, 0, 53, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1826.0, 245.0, 1648.0, 178.0, 148.0, 97.0, 97.0, 1551.0, 1418.0, 133.0, 407.0, 1011.0, 221.0, 186.0, 915.0, 96.0, 113.0, 108.0, 92.0, 94.0, 171.0, 744.0, 586.0, 158.0, 235.0, 351.0, 99.0, 136.0, 147.0, 204.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.001137884, 0.004105366, -0.039666228, -0.0044662035, 0.07217124, 0.002032556, -0.013087827, -0.04790038, 0.008022854, 0.0010968862, 0.014456111, 0.012070114, -0.019753551, 0.042334523, -0.024061467, -0.0095051145, 0.010019827, 0.08805254, -0.01180088, 0.0056968858, -0.013182923, 0.016892651, 0.018726083, -0.010259187, 0.009388548, -0.03424054, 0.056153554, 0.0061427206, -0.0034146912, 0.0029869736, -0.013658173, -0.0018121178, 0.0144992], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, 15, -1, 17, 19, -1, -1, 21, 23, 25, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.41655242, 1.0589281, 1.3515779, 0.87443274, 0.89937735, 0.0, 0.0, 3.2305303, 1.378285, 0.0, 0.0, 2.4261827, 0.0, 1.4973528, 2.074924, 0.0, 0.0, 2.3155649, 2.657917, 1.0216606, 0.0, 0.43414778, 0.0, 0.0, 0.0, 1.8567979, 1.4780536, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, 16, -1, 18, 20, -1, -1, 22, 24, 26, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 0.84615386, 1.0, 1.0, 1.0, 0.002032556, -0.013087827, 1.0, 1.0, 0.0010968862, 0.014456111, -0.3846154, -0.019753551, 1.0, 2.0, -0.0095051145, 0.010019827, 1.0, 1.0, 1.0, -0.013182923, 1.0, 0.018726083, -0.010259187, 0.009388548, 1.0, 1.0, 0.0061427206, -0.0034146912, 0.0029869736, -0.013658173, -0.0018121178, 0.0144992], "split_indices": [1, 1, 17, 89, 122, 0, 0, 106, 126, 0, 0, 1, 0, 121, 0, 0, 0, 71, 15, 12, 0, 13, 0, 0, 0, 93, 124, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1815.0, 247.0, 1612.0, 203.0, 149.0, 98.0, 360.0, 1252.0, 110.0, 93.0, 257.0, 103.0, 605.0, 647.0, 116.0, 141.0, 328.0, 277.0, 507.0, 140.0, 191.0, 137.0, 149.0, 128.0, 283.0, 224.0, 102.0, 89.0, 174.0, 109.0, 122.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006686814, -0.016246201, 0.016029406, -0.012438673, -0.0004293277, 0.0033548828, 0.01150491, 0.05739975, -0.031560652, 0.0141414255, -0.013658848, -0.0064998255, 0.12879859, -0.10445339, 0.006550773, -0.071440786, 0.014133601, 0.0058603473, 0.02213285, -0.01724039, -0.002025382, 0.0114298295, -0.027210105, 0.0009137476, -0.01207116, -0.029281804, 0.012606858, 0.027339384, -0.013001494, 0.025612175, -0.020301223, -0.0016128846, 0.0075481646, 0.014199636, -0.029894114, -0.011841531, 0.0044430266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, -1, 7, 9, -1, 11, 13, -1, 15, -1, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [0.5319348, 1.8096507, 1.2387117, 0.0, 1.661673, 2.0552921, 0.0, 2.8227217, 1.6668284, 0.0, 1.2509975, 0.0, 1.3250093, 1.1786084, 1.4332348, 1.00445, 2.5562038, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6823857, 0.0, 0.0, 3.6144307, 0.0, 0.41016138, 0.0, 1.8604962, 0.0, 0.0, 0.0, 0.0, 1.2829605, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 22, 22, 25, 25, 27, 27, 29, 29, 34, 34], "right_children": [2, 4, 6, -1, 8, 10, -1, 12, 14, -1, 16, -1, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, -0.5, 1.0, -0.012438673, -0.07692308, -0.5, 0.01150491, 1.0, 0.34615386, 0.0141414255, 1.0, -0.0064998255, 1.0, 1.0, 0.5769231, 1.0, 0.53846157, 0.0058603473, 0.02213285, -0.01724039, -0.002025382, 0.0114298295, 1.0, 0.0009137476, -0.01207116, 1.0, 0.012606858, 1.3076923, -0.013001494, -0.30769232, -0.020301223, -0.0016128846, 0.0075481646, 0.014199636, 1.0, -0.011841531, 0.0044430266], "split_indices": [39, 1, 88, 0, 1, 1, 0, 13, 1, 0, 81, 0, 121, 71, 1, 122, 1, 0, 0, 0, 0, 0, 71, 0, 0, 61, 0, 1, 0, 1, 0, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1058.0, 987.0, 135.0, 923.0, 875.0, 112.0, 323.0, 600.0, 96.0, 779.0, 119.0, 204.0, 206.0, 394.0, 253.0, 526.0, 116.0, 88.0, 114.0, 92.0, 94.0, 300.0, 96.0, 157.0, 379.0, 147.0, 196.0, 104.0, 288.0, 91.0, 103.0, 93.0, 93.0, 195.0, 89.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010012537, -0.005251275, 0.0034897677, -0.0012450841, 0.028507922, -0.06299712, 0.007570336, 0.0085846605, -0.0031542704, -0.010668415, -0.0015669465, 0.02931385, -0.011851907, -0.005263061, 0.055268377, -0.010517328, 0.0046165665, 0.013301914, 0.026956946, -0.012438466, 0.010416608, -0.008658163, 0.095873386, -0.006295246, 0.010759528, -0.007144159, 0.025053432, 0.056277886, -0.008567764, -0.002193484, 0.017557207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, -1, 3, 5, 7, 9, 11, -1, -1, -1, -1, 13, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, 25, -1, 27, -1, -1, 29, -1, -1, -1], "loss_changes": [0.4788723, 0.0, 0.22554268, 0.8715367, 1.043297, 0.41352063, 0.59165335, 0.0, 0.0, 0.0, 0.0, 1.4058343, 1.1372771, 0.0, 1.1050197, 0.0, 1.0679294, 0.0, 2.8794804, 0.6292693, 0.0, 0.0, 5.925854, 0.0, 1.6153955, 0.0, 0.0, 2.332581, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 11, 11, 12, 12, 14, 14, 16, 16, 18, 18, 19, 19, 22, 22, 24, 24, 27, 27], "right_children": [2, -1, 4, 6, 8, 10, 12, -1, -1, -1, -1, 14, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, 26, -1, 28, -1, -1, 30, -1, -1, -1], "split_conditions": [-0.53846157, -0.005251275, 1.2692307, 1.0, 1.0, 1.0, 1.0, 0.0085846605, -0.0031542704, -0.010668415, -0.0015669465, 1.0, -1.0, -0.005263061, -0.34615386, -0.010517328, 1.0, 0.013301914, -0.115384616, 1.0, 0.010416608, -0.008658163, 1.0, -0.006295246, 0.15384616, -0.007144159, 0.025053432, 1.0, -0.008567764, -0.002193484, 0.017557207], "split_indices": [1, 0, 1, 26, 115, 124, 126, 0, 0, 0, 0, 89, 0, 0, 1, 0, 73, 0, 1, 81, 0, 0, 71, 0, 1, 0, 0, 122, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 166.0, 1904.0, 1601.0, 303.0, 200.0, 1401.0, 155.0, 148.0, 104.0, 96.0, 661.0, 740.0, 159.0, 502.0, 111.0, 629.0, 134.0, 368.0, 537.0, 92.0, 139.0, 229.0, 169.0, 368.0, 110.0, 119.0, 250.0, 118.0, 151.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.14592025E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}