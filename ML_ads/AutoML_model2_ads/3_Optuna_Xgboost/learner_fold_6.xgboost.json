{"learner": {"attributes": {"best_iteration": "15", "best_score": "0.978876"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "66"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.004007997, -0.2411179, 0.16114496, -0.2743544, -0.005028489, 0.110521205, 0.052269656, -0.24726352, -0.04479691, -0.13464488, 0.19392517, -0.20505145, -0.036035012, -0.0045218896, -0.022343662, 0.29257312, 0.10696216, -0.010290201, -0.26589045, 0.42553896, 0.007785124, 0.19658701, -0.013129053, -0.21458176, -0.03713903, 0.05746272, 0.023527397, 0.007605237, 0.031787973, -0.029409682, -0.013419287], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [79.77148, 5.1311836, 23.098507, 3.2406273, 0.0, 22.635742, 0.0, 2.8450737, 0.0, 2.2312202, 7.0860214, 2.6971645, 0.0, 0.0, 0.0, 11.04911, 9.374126, 0.0, 1.4723511, 6.7795296, 0.0, 4.663769, 0.0, 1.1697578, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.005028489, 1.0, 0.052269656, 1.0, -0.04479691, 1.0, 1.0, 1.0, -0.036035012, -0.0045218896, -0.022343662, 1.0, 1.0, -0.010290201, 1.0, 1.0, 0.007785124, 1.0, -0.013129053, 1.0, -0.03713903, 0.05746272, 0.023527397, 0.007605237, 0.031787973, -0.029409682, -0.013419287], "split_indices": [2, 71, 125, 40, 0, 17, 0, 116, 0, 53, 53, 53, 0, 0, 0, 97, 7, 0, 23, 59, 0, 15, 0, 12, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 809.0, 1262.0, 689.0, 120.0, 1107.0, 155.0, 596.0, 93.0, 281.0, 826.0, 434.0, 162.0, 140.0, 141.0, 387.0, 439.0, 162.0, 272.0, 239.0, 148.0, 319.0, 120.0, 183.0, 89.0, 134.0, 105.0, 160.0, 159.0, 92.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.003915416, -0.21109885, 0.14052479, -0.24470967, -0.00094338454, 0.09606095, 0.045636155, -0.28157756, -0.004367526, -0.12887545, 0.17314282, -0.22254308, -0.36651748, -0.00615955, -0.02319168, 0.2406568, -0.02423056, -0.26861197, -0.009406196, -0.032807793, -0.041827764, 0.15893877, 0.047696237, -0.013039864, 0.01217505, -0.01435374, -0.034098215, 0.06366452, 0.043022815, -0.013342731, 0.16896017, 0.0043938593, 0.032620378], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, 25, -1, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [60.332073, 5.408947, 17.638403, 5.0696106, 0.0, 19.089708, 0.0, 2.8983116, 0.0, 1.9480639, 10.926876, 2.0183735, 0.47154045, 0.0, 0.0, 11.798668, 3.2391922, 2.271967, 0.0, 0.0, 0.0, 11.7344885, 0.0, 0.0, 0.0, 0.0, 0.0, 6.972978, 0.0, 0.0, 4.305285, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 21, 21, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, 26, -1, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, -0.00094338454, 1.0, 0.045636155, 1.0, -0.004367526, 1.0, 0.1923077, 1.0, 1.0, -0.00615955, -0.02319168, -0.115384616, 0.7692308, 1.0, -0.009406196, -0.032807793, -0.041827764, 1.0, 0.047696237, -0.013039864, 0.01217505, -0.01435374, -0.034098215, 1.0, 0.043022815, -0.013342731, 1.0, 0.0043938593, 0.032620378], "split_indices": [2, 71, 125, 1, 0, 17, 0, 23, 0, 59, 1, 93, 116, 0, 0, 1, 1, 122, 0, 0, 0, 61, 0, 0, 0, 0, 0, 5, 0, 0, 126, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 798.0, 1256.0, 684.0, 114.0, 1101.0, 155.0, 578.0, 106.0, 281.0, 820.0, 341.0, 237.0, 170.0, 111.0, 611.0, 209.0, 251.0, 90.0, 136.0, 101.0, 454.0, 157.0, 121.0, 88.0, 92.0, 159.0, 336.0, 118.0, 117.0, 219.0, 122.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.002697469, -0.15632866, 0.15452921, -0.21721403, -0.030314393, 0.10704105, 0.046438094, -0.17667006, -0.31723619, 0.053006414, -0.02364238, -0.06537001, 0.18402965, -0.007474276, -0.23150437, -0.037726503, -0.024731243, 0.013511184, -0.0031222394, -0.016850105, 0.0018054442, 0.06359779, 0.25750834, -0.014758082, -0.28864378, 0.021550473, -0.009474586, 0.15226421, 0.041874766, -0.03209774, -0.025831018, 0.005033875, 0.026402457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, 21, -1, 23, -1, -1, -1, -1, -1, -1, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [50.07717, 7.7721653, 15.611858, 2.769764, 5.6671576, 12.211791, 0.0, 2.7163067, 0.82689285, 1.6251762, 0.0, 2.4434373, 5.6280727, 0.0, 1.5153275, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.7968936, 6.7029495, 0.0, 0.18438816, 0.0, 0.0, 2.7225018, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 21, 21, 22, 22, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, 22, -1, 24, -1, -1, -1, -1, -1, -1, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.046438094, 1.0, 1.0, 1.0, -0.02364238, 1.0, 1.0, -0.007474276, 1.0, -0.037726503, -0.024731243, 0.013511184, -0.0031222394, -0.016850105, 0.0018054442, 1.0, 1.0, -0.014758082, 1.0, 0.021550473, -0.009474586, 1.0, 0.041874766, -0.03209774, -0.025831018, 0.005033875, 0.026402457], "split_indices": [71, 2, 125, 116, 7, 17, 0, 53, 111, 93, 0, 93, 93, 0, 106, 0, 0, 0, 0, 0, 0, 124, 113, 0, 12, 0, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1013.0, 1061.0, 683.0, 330.0, 920.0, 141.0, 486.0, 197.0, 235.0, 95.0, 284.0, 636.0, 170.0, 316.0, 106.0, 91.0, 119.0, 116.0, 127.0, 157.0, 241.0, 395.0, 128.0, 188.0, 123.0, 118.0, 239.0, 156.0, 91.0, 97.0, 125.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.000907003, -0.16835302, 0.113, -0.19906819, 0.0033808597, -0.06600122, 0.18030123, -0.24327718, -0.09645467, 0.0027592003, -0.016836884, 0.14583923, 0.0483264, -0.15871355, -0.29498485, 0.0038422241, -0.019680312, 0.20516494, -0.020837758, -0.0240105, -0.0077322074, -0.024478806, -0.035424493, 0.12516299, 0.04356968, -0.0136436, 0.013942341, 0.25934994, -0.05327707, 0.01638233, 0.038292106, -0.007348474, -0.0033069365], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, -1, -1, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1], "loss_changes": [39.290947, 5.1786633, 14.769629, 3.28438, 0.0, 3.2096064, 9.30267, 2.2125301, 2.950563, 0.0, 0.0, 7.9105835, 0.0, 1.2719164, 0.934042, 0.0, 0.0, 10.881367, 3.8904412, 0.0, 0.0, 0.0, 0.0, 10.487617, 0.0, 0.0, 0.0, 2.9510822, 0.07676965, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 23, 23, 27, 27, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, -1, -1, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1], "split_conditions": [1.0, 2.7307692, 1.0, 1.0, 0.0033808597, -0.03846154, 1.0, 0.0, 1.0, 0.0027592003, -0.016836884, 0.1923077, 0.0483264, -0.30769232, 1.0, 0.0038422241, -0.019680312, -0.115384616, 0.8076923, -0.0240105, -0.0077322074, -0.024478806, -0.035424493, 1.0, 0.04356968, -0.0136436, 0.013942341, 1.0, 1.0, 0.01638233, 0.038292106, -0.007348474, -0.0033069365], "split_indices": [127, 1, 17, 93, 0, 1, 125, 1, 80, 0, 0, 1, 0, 1, 23, 0, 0, 1, 1, 0, 0, 0, 0, 122, 0, 0, 0, 61, 111, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 834.0, 1226.0, 724.0, 110.0, 335.0, 891.0, 506.0, 218.0, 175.0, 160.0, 800.0, 91.0, 192.0, 314.0, 93.0, 125.0, 590.0, 210.0, 96.0, 96.0, 170.0, 144.0, 438.0, 152.0, 122.0, 88.0, 250.0, 188.0, 141.0, 109.0, 94.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0013857291, -0.15550609, 0.10319054, -0.19384122, -0.053106416, 0.06590518, 0.03680368, -0.22450398, -0.0031067599, 0.010589164, -0.01615142, -0.12062793, 0.13043927, -0.14735799, -0.28243113, -0.020514261, -0.0043536816, 0.17990252, -0.029739656, -0.0049439943, -0.025487581, -0.024344353, -0.30101585, 0.113182366, 0.041318305, -0.018582609, 0.011846359, -0.024718568, -0.03480519, 0.23915674, -0.03787286, 0.014688477, 0.03558768, -0.011904385, 0.0073846327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [33.078716, 3.1992855, 12.402857, 2.959715, 3.826532, 13.253553, 0.0, 2.2299538, 0.0, 0.0, 0.0, 1.8438373, 6.4809914, 2.2529774, 0.20650291, 0.0, 0.0, 9.727818, 4.464576, 0.0, 0.0, 0.0, 0.48866653, 9.2481365, 0.0, 0.0, 0.0, 0.0, 0.0, 2.8540459, 2.0041072, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 22, 22, 23, 23, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, 1.0, 1.0, 0.03680368, 1.0, -0.0031067599, 0.010589164, -0.01615142, 1.0, 0.26923078, 0.46153846, 1.0, -0.020514261, -0.0043536816, 2.0, 1.0, -0.0049439943, -0.025487581, -0.024344353, 1.0, 1.0, 0.041318305, -0.018582609, 0.011846359, -0.024718568, -0.03480519, 1.0, -0.23076923, 0.014688477, 0.03558768, -0.011904385, 0.0073846327], "split_indices": [2, 93, 125, 1, 80, 17, 0, 122, 0, 0, 0, 105, 1, 1, 59, 0, 0, 0, 69, 0, 0, 0, 17, 122, 0, 0, 0, 0, 0, 50, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 815.0, 1256.0, 593.0, 222.0, 1101.0, 155.0, 499.0, 94.0, 90.0, 132.0, 283.0, 818.0, 214.0, 285.0, 135.0, 148.0, 625.0, 193.0, 112.0, 102.0, 92.0, 193.0, 486.0, 139.0, 94.0, 99.0, 90.0, 103.0, 265.0, 221.0, 148.0, 117.0, 128.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0077480236, -0.13083902, 0.11045194, -0.19678925, -0.07996641, 0.025900211, 0.19843143, -0.24366692, -0.010366739, 0.0088094985, -0.14965965, -0.11332443, 0.2046505, 0.13353647, 0.28293908, -0.019024557, -0.031391386, 0.0108700935, -0.015627421, -0.023028014, -0.1027965, -0.17927146, 0.0012862644, 0.003627967, 0.030844072, 0.0052651297, 0.024074141, 0.03418247, 0.021319102, -0.01859137, -0.00025137744, -0.004990963, -0.029743848], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [30.146257, 3.405386, 7.862829, 1.9294777, 3.5451977, 13.4137945, 2.8407745, 1.1032867, 0.0, 4.155593, 1.2127781, 2.5214648, 4.124158, 2.5406876, 0.9241066, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6920488, 3.041973, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.010366739, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.019024557, -0.031391386, 0.0108700935, -0.015627421, -0.023028014, 1.0, 1.0, 0.0012862644, 0.003627967, 0.030844072, 0.0052651297, 0.024074141, 0.03418247, 0.021319102, -0.01859137, -0.00025137744, -0.004990963, -0.029743848], "split_indices": [71, 17, 39, 83, 106, 42, 50, 109, 0, 80, 39, 116, 93, 106, 126, 0, 0, 0, 0, 0, 12, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1015.0, 1057.0, 442.0, 573.0, 539.0, 518.0, 294.0, 148.0, 252.0, 321.0, 303.0, 236.0, 293.0, 225.0, 167.0, 127.0, 157.0, 95.0, 118.0, 203.0, 199.0, 104.0, 90.0, 146.0, 167.0, 126.0, 122.0, 103.0, 111.0, 92.0, 95.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0043402207, -0.12208816, 0.089069776, -0.14845137, 0.0049752006, 0.14714257, -0.0754097, -0.048312318, -0.18481363, 0.21048905, -0.045139175, -0.023061683, 0.045497496, -0.01579721, 0.0060205157, -0.15053472, -0.025915343, 0.005336988, 0.25867227, 0.008449789, -0.018184738, -0.0018729231, 0.010901847, -0.20384362, 0.00093919755, 0.19908199, 0.04076479, -0.17388423, -0.026477227, 0.29267976, 0.0007320705, -0.017298786, -0.017473236, 0.038247228, 0.0144285755], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, 19, -1, 21, -1, -1, 23, -1, -1, 25, -1, -1, -1, -1, 27, -1, 29, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [22.077887, 3.746523, 11.786903, 2.6107988, 0.0, 11.108498, 6.042543, 2.272901, 1.3403969, 5.193367, 4.0052743, 0.0, 0.73843396, 0.0, 0.0, 3.0691872, 0.0, 0.0, 4.6606865, 0.0, 0.0, 0.0, 0.0, 0.49285316, 0.0, 6.7306585, 0.0, 0.00013685226, 0.0, 3.3578148, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 18, 18, 23, 23, 25, 25, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, 20, -1, 22, -1, -1, 24, -1, -1, 26, -1, -1, -1, -1, 28, -1, 30, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.7307692, 0.23076923, 0.0, 0.0049752006, 1.0, 1.0, 1.0, 0.7307692, 1.0, 1.0, -0.023061683, 0.6923077, -0.01579721, 0.0060205157, 0.26923078, -0.025915343, 0.005336988, -0.15384616, 0.008449789, -0.018184738, -0.0018729231, 0.010901847, 1.0, 0.00093919755, 1.0, 0.04076479, 1.0, -0.026477227, 1.0, 0.0007320705, -0.017298786, -0.017473236, 0.038247228, 0.0144285755], "split_indices": [127, 1, 1, 0, 0, 7, 39, 13, 1, 17, 106, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 58, 0, 106, 0, 13, 0, 97, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 827.0, 1234.0, 717.0, 110.0, 912.0, 322.0, 191.0, 526.0, 686.0, 226.0, 141.0, 181.0, 95.0, 96.0, 360.0, 166.0, 161.0, 525.0, 116.0, 110.0, 90.0, 91.0, 270.0, 90.0, 375.0, 150.0, 181.0, 89.0, 252.0, 123.0, 88.0, 93.0, 157.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010780548, -0.098895684, 0.0930238, -0.07672889, -0.025636857, 0.056518838, 0.03272211, -0.046617143, -0.19273312, 0.10791306, -0.06296335, -0.071212634, 0.010393705, -0.020556835, -0.01808436, 0.06888012, 0.026312998, -0.024068136, 0.0073478194, -0.11347538, 0.0036297848, -0.010705976, 0.14332189, 0.00037193068, -0.17642096, -0.012257228, 0.00941136, 0.006637177, 0.0243897, -0.1398497, -0.024654724, -0.019407207, -0.009116019], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, 21, -1, -1, -1, 23, -1, 25, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [19.017157, 3.536048, 9.00248, 3.1018624, 0.0, 5.5941734, 0.0, 2.6105828, 0.027925968, 3.8593102, 6.643985, 2.7534752, 0.0, 0.0, 0.0, 3.0155857, 0.0, 0.0, 0.0, 3.2089462, 0.0, 2.8845417, 2.0354261, 0.0, 0.72578335, 0.0, 0.0, 0.0, 0.0, 0.49105072, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 15, 15, 19, 19, 21, 21, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, 22, -1, -1, -1, 24, -1, 26, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.025636857, 0.15384616, 0.03272211, 1.0, 0.3846154, -0.07692308, 1.0, 1.0, 0.010393705, -0.020556835, -0.01808436, 1.0, 0.026312998, -0.024068136, 0.0073478194, 1.0, 0.0036297848, 1.0, 1.0, 0.00037193068, 1.0, -0.012257228, 0.00941136, 0.006637177, 0.0243897, 1.0, -0.024654724, -0.019407207, -0.009116019], "split_indices": [71, 40, 125, 116, 0, 1, 0, 121, 1, 1, 93, 50, 0, 0, 0, 109, 0, 0, 0, 122, 0, 111, 61, 0, 127, 0, 0, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1013.0, 1053.0, 888.0, 125.0, 911.0, 142.0, 705.0, 183.0, 637.0, 274.0, 606.0, 99.0, 88.0, 95.0, 509.0, 128.0, 119.0, 155.0, 435.0, 171.0, 246.0, 263.0, 152.0, 283.0, 119.0, 127.0, 149.0, 114.0, 186.0, 97.0, 88.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [1.9592382e-05, -0.09032156, 0.08837048, -0.06386085, -0.18393369, 0.05500694, 0.030740935, -0.08735072, 0.008109914, -0.014992783, -0.023046799, 0.021247199, 0.032408204, -0.028259864, -0.13231444, -0.059185337, 0.10726246, -0.01582356, 0.047501374, -0.16964659, -0.0047611236, -0.1552308, 0.017226845, 0.17993598, -0.0031034113, 0.010052547, -0.0012150699, -0.022457352, -0.012950765, -0.080737375, -0.030652154, 0.006942206, 0.028958648, -0.003449517, -0.013571418], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [16.482258, 2.5290604, 7.6294594, 2.710453, 0.35604763, 8.230018, 0.0, 1.8200068, 0.0, 0.0, 0.0, 5.569333, 0.0, 2.9147487, 1.2300763, 9.247717, 3.9096446, 0.0, 0.59148073, 0.59527016, 0.0, 3.313428, 0.0, 3.090065, 0.0, 0.0, 0.0, 0.0, 0.0, 0.50082254, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 4.0, 0.030740935, 1.0, 0.008109914, -0.014992783, -0.023046799, 1.0, 0.032408204, 1.0, 1.0, 1.0, 1.0, -0.01582356, 0.15384616, 1.0, -0.0047611236, 0.23076923, 0.017226845, -0.07692308, -0.0031034113, 0.010052547, -0.0012150699, -0.022457352, -0.012950765, 1.0, -0.030652154, 0.006942206, 0.028958648, -0.003449517, -0.013571418], "split_indices": [71, 116, 125, 121, 109, 0, 0, 106, 0, 0, 0, 39, 0, 17, 93, 113, 97, 0, 1, 111, 0, 1, 0, 1, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1021.0, 1044.0, 796.0, 225.0, 906.0, 138.0, 685.0, 111.0, 130.0, 95.0, 805.0, 101.0, 296.0, 389.0, 416.0, 389.0, 109.0, 187.0, 270.0, 119.0, 294.0, 122.0, 255.0, 134.0, 99.0, 88.0, 114.0, 156.0, 197.0, 97.0, 127.0, 128.0, 107.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0038818715, -0.08693544, 0.092443936, -0.06787616, -0.022231681, 0.14200404, -0.030386088, -0.08563037, 0.008523004, 0.097998194, 0.28139684, -0.014550944, 0.009021929, -0.045886554, -0.17869714, 0.18344569, 0.015220918, 0.0124231195, 0.044032834, -0.07541599, 0.009567205, -0.021188414, -0.014605865, 0.009901477, 0.031123301, 0.016330209, -0.009833824, -0.051842768, -0.016845495, 0.002666749, -0.09319798, -0.0008818786, -0.017417476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [16.632858, 2.6344566, 6.373581, 2.4328609, 0.0, 4.5760355, 4.1792336, 2.9664598, 0.0, 4.0104537, 4.4711637, 0.0, 0.0, 2.3492422, 0.25996017, 3.0101862, 4.8430004, 0.0, 0.0, 1.0198503, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2045665, 0.0, 0.0, 1.6603589, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 0.15384616, 2.7307692, -0.022231681, 2.0, 1.0, 1.0, 0.008523004, 1.0, -0.34615386, -0.014550944, 0.009021929, 1.0, 1.0, 1.0, 1.0, 0.0124231195, 0.044032834, 1.0, 0.009567205, -0.021188414, -0.014605865, 0.009901477, 0.031123301, 0.016330209, -0.009833824, 1.0, -0.016845495, 0.002666749, 1.0, -0.0008818786, -0.017417476], "split_indices": [71, 40, 1, 1, 0, 0, 39, 23, 0, 121, 1, 0, 0, 73, 80, 16, 69, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 111, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1021.0, 1047.0, 895.0, 126.0, 746.0, 301.0, 802.0, 93.0, 567.0, 179.0, 154.0, 147.0, 562.0, 240.0, 279.0, 288.0, 90.0, 89.0, 465.0, 97.0, 119.0, 121.0, 168.0, 111.0, 125.0, 163.0, 371.0, 94.0, 128.0, 243.0, 119.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0019014778, -0.07575718, 0.0764597, -0.058545597, -0.01987367, 0.047613524, 0.026283462, -0.028477615, -0.11787436, 0.0049975147, 0.19719148, -0.08515049, 0.022345146, -0.018189887, -0.005637711, 0.067799896, -0.06126354, 0.039557125, 0.004405616, 0.0016110762, -0.017281964, 0.017760282, -0.048359033, 0.13347954, -0.0056261616, 0.00573223, -0.12290729, 0.00415486, -0.011937854, 0.029637158, -0.00018461548, -0.021922452, 0.00016240617], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [11.939168, 2.1378388, 5.655765, 1.5805314, 0.0, 5.8070917, 0.0, 1.6935998, 1.1733241, 2.9503984, 6.136547, 2.4679425, 3.4029841, 0.0, 0.0, 2.9659872, 2.5219767, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3600469, 5.24635, 0.0, 0.0, 2.722756, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.01987367, 2.0, 0.026283462, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.018189887, -0.005637711, 1.0, 1.0, 0.039557125, 0.004405616, 0.0016110762, -0.017281964, 0.017760282, 1.0, 1.0, -0.0056261616, 0.00573223, 1.0, 0.00415486, -0.011937854, 0.029637158, -0.00018461548, -0.021922452, 0.00016240617], "split_indices": [71, 40, 125, 23, 0, 0, 0, 115, 111, 121, 69, 39, 106, 0, 0, 97, 69, 0, 0, 0, 0, 0, 97, 126, 0, 0, 61, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1010.0, 1052.0, 886.0, 124.0, 911.0, 141.0, 588.0, 298.0, 709.0, 202.0, 278.0, 310.0, 146.0, 152.0, 364.0, 345.0, 88.0, 114.0, 129.0, 149.0, 97.0, 213.0, 238.0, 126.0, 118.0, 227.0, 94.0, 119.0, 108.0, 130.0, 128.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0038395296, -0.064552546, 0.06962122, 0.0009859569, -0.09573851, 0.042574447, 0.024503885, 0.05277207, -0.013217831, -0.078388125, -0.02071958, 0.0837022, -0.053088997, 0.01269678, -0.0020166074, -0.09878632, 0.0035161753, 0.18337302, 0.017254975, -0.017555153, 0.007965663, -0.1337127, -0.04397876, -0.0017610111, 0.037508007, 0.07739308, -0.015881944, -0.0067156195, -0.16949934, 0.00081192405, -0.0106028095, 0.018468663, -0.0028399846, -0.014621019, -0.019828362], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, -1, -1, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [9.249833, 2.060233, 4.9722137, 2.2412193, 1.3208027, 3.5724554, 0.0, 1.2663379, 0.0, 1.3688817, 0.0, 4.205509, 4.437989, 0.0, 0.0, 0.95902824, 0.0, 9.78659, 4.0343256, 0.0, 0.0, 0.72884035, 0.63036627, 0.0, 0.0, 3.2236557, 0.0, 0.0, 0.13340092, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, -1, -1, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.15384616, 0.024503885, 1.0, -0.013217831, 2.7307692, -0.02071958, 1.0, 1.0, 0.01269678, -0.0020166074, 1.0, 0.0035161753, -0.34615386, 1.0, -0.017555153, 0.007965663, -0.03846154, 1.0, -0.0017610111, 0.037508007, -0.30769232, -0.015881944, -0.0067156195, 1.0, 0.00081192405, -0.0106028095, 0.018468663, -0.0028399846, -0.014621019, -0.019828362], "split_indices": [71, 3, 125, 7, 40, 1, 0, 93, 0, 1, 0, 69, 39, 0, 0, 115, 0, 1, 7, 0, 0, 1, 69, 0, 0, 1, 0, 0, 50, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1008.0, 1048.0, 325.0, 683.0, 908.0, 140.0, 234.0, 91.0, 591.0, 92.0, 635.0, 273.0, 116.0, 118.0, 501.0, 90.0, 254.0, 381.0, 142.0, 131.0, 306.0, 195.0, 124.0, 130.0, 284.0, 97.0, 107.0, 199.0, 106.0, 89.0, 141.0, 143.0, 110.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.001251449, 0.046184428, -0.07804091, 0.027621295, 0.023690825, -0.11092289, 0.009440409, -0.038561586, 0.09791857, -0.13442823, 0.0013319616, -0.010549473, 0.012984867, -0.12072383, 0.1492378, 0.2602087, -0.017509649, -0.11300803, -0.022877336, -0.07711101, -0.02482707, 0.008894046, 0.02136463, 0.01442035, 0.034830283, 0.05303511, -0.012688139, -0.002098872, -0.16189326, -0.024943357, -0.017331006, 0.00947654, 0.00087235373, -0.013091005, -0.01933644, -0.014861527, 0.0124550145], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [7.5073485, 4.5105095, 2.2638516, 5.4015255, 0.0, 1.6704488, 2.975415, 9.227151, 10.5466, 0.9720478, 0.0, 0.0, 0.0, 2.314073, 0.7068248, 2.3913336, 2.5384338, 1.7633667, 0.0, 1.5557278, 0.0, 0.0, 0.0, 0.0, 0.0, 0.36982667, 0.0, 0.0, 0.24961805, 3.7161179, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 25, 25, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, 1.0, 0.023690825, 2.7307692, 1.0, 1.0, 1.0, 1.0, 0.0013319616, -0.010549473, 0.012984867, 1.0, -0.15384616, 1.0, 1.0, 0.0, -0.022877336, 0.34615386, -0.02482707, 0.008894046, 0.02136463, 0.01442035, 0.034830283, -0.07692308, -0.012688139, -0.002098872, 1.0, 1.0, -0.017331006, 0.00947654, 0.00087235373, -0.013091005, -0.01933644, -0.014861527, 0.0124550145], "split_indices": [137, 0, 93, 109, 0, 1, 13, 105, 105, 0, 0, 0, 0, 113, 1, 69, 53, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 23, 124, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1274.0, 787.0, 1161.0, 113.0, 572.0, 215.0, 598.0, 563.0, 481.0, 91.0, 110.0, 105.0, 416.0, 182.0, 234.0, 329.0, 392.0, 89.0, 310.0, 106.0, 94.0, 88.0, 101.0, 133.0, 200.0, 129.0, 136.0, 256.0, 201.0, 109.0, 103.0, 97.0, 129.0, 127.0, 110.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024940388, 0.037002355, -0.06691437, 0.052433707, -0.014343745, 0.030798435, -0.10370641, 0.033674486, 0.025302732, -0.0083366595, 0.014603046, -0.020705776, -0.072630376, -0.0296132, 0.1470036, -0.020580728, -0.11309001, -0.00050590077, -0.012736978, 0.24436945, -0.009206429, 0.0042615803, -0.008927264, -0.020043252, -0.0052668112, 0.030626776, -0.014254877, 0.04209323, 0.15414776, 0.06922734, -0.012073944, 0.004136707, 0.026943466, 0.015489712, 0.011840861, -0.006579902, 0.011251679], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [5.2617593, 3.5696383, 2.8257124, 4.4440775, 0.0, 2.828426, 1.8339086, 7.746125, 0.0, 0.0, 0.0, 0.0, 0.924494, 1.9718826, 9.008218, 0.83348954, 1.3035171, 2.3614416, 0.0, 4.3806934, 0.0, 0.0, 0.0, 0.0, 0.0, 2.559156, 0.0, 0.0, 2.3663878, 1.7157841, 0.0, 0.0, 0.0, 0.0, 1.6336417, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 19, 19, 25, 25, 28, 28, 29, 29, 34, 34], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, 0.0, 5.0, -0.014343745, 1.0, -0.15384616, 1.0, 0.025302732, -0.0083366595, 0.014603046, -0.020705776, 0.6923077, 1.0, 1.0, 1.0, 1.0, 1.0, -0.012736978, -0.30769232, -0.009206429, 0.0042615803, -0.008927264, -0.020043252, -0.0052668112, 0.61538464, -0.014254877, 0.04209323, 1.0, 1.0, -0.012073944, 0.004136707, 0.026943466, 0.015489712, 1.0, -0.006579902, 0.011251679], "split_indices": [137, 40, 0, 0, 0, 13, 1, 50, 0, 0, 0, 0, 1, 7, 97, 13, 111, 119, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 105, 15, 0, 0, 0, 0, 126, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1282.0, 786.0, 1181.0, 101.0, 215.0, 571.0, 1080.0, 101.0, 108.0, 107.0, 132.0, 439.0, 693.0, 387.0, 192.0, 247.0, 534.0, 159.0, 275.0, 112.0, 100.0, 92.0, 101.0, 146.0, 438.0, 96.0, 93.0, 182.0, 349.0, 89.0, 92.0, 90.0, 140.0, 209.0, 118.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [9.430883e-05, -0.050602335, 0.04900959, -0.035151105, -0.012972005, 0.025347667, 0.020081248, -0.08629633, 0.0071949335, 0.0042578196, 0.019261908, -0.036622535, -0.015843404, -0.04200714, 0.009417719, -0.05332212, 0.06550996, 0.005582038, -0.014301913, -0.011651854, 0.004862983, -0.12377125, 0.011702616, 0.022739047, 0.021326398, -0.025917495, -0.0022819366, -0.008418521, 0.010050216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, 21, 23, -1, -1, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1], "loss_changes": [5.1307898, 1.2420251, 3.7823207, 1.8409282, 0.0, 3.2137601, 0.0, 1.3795912, 1.9900641, 2.853258, 0.0, 2.2425196, 0.0, 2.005786, 0.0, 5.0043707, 2.4772725, 0.0, 0.0, 0.0, 0.0, 4.0324306, 0.0, 2.527688, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 21, 21, 23, 23], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, 22, 24, -1, -1, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.012972005, 4.0, 0.020081248, 1.0, 1.0, 1.0, 0.019261908, 1.0, -0.015843404, 1.0, 0.009417719, 1.0, 0.53846157, 0.005582038, -0.014301913, -0.011651854, 0.004862983, 1.0, 0.011702616, 1.0, 0.021326398, -0.025917495, -0.0022819366, -0.008418521, 0.010050216], "split_indices": [71, 7, 125, 17, 0, 0, 0, 109, 127, 39, 0, 12, 0, 115, 0, 113, 1, 0, 0, 0, 0, 13, 0, 124, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1016.0, 1053.0, 850.0, 166.0, 911.0, 142.0, 385.0, 465.0, 809.0, 102.0, 228.0, 157.0, 297.0, 168.0, 417.0, 392.0, 122.0, 106.0, 163.0, 134.0, 295.0, 122.0, 304.0, 88.0, 126.0, 169.0, 128.0, 176.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0019769042, 0.04627262, -0.039744925, -0.0075499197, 0.17038316, -0.10970396, 0.03384659, -0.10379897, 0.03794595, 0.23813513, 0.0034208389, -0.02041207, -0.08260079, -0.008013525, 0.016204322, -0.0014572466, -0.019543698, -0.0067451126, 0.0898185, 0.015005876, 0.032364614, -0.00052287325, -0.12747082, -0.08283801, 0.009175244, -0.005980407, 0.16116631, -0.0069248127, -0.023948615, -0.015964031, 0.0029411484, 0.036346268, 0.0019399535], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, -1, 25, -1, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [3.8292599, 6.7133455, 5.493334, 3.0696328, 2.8047352, 1.3997684, 2.7904897, 1.8397205, 2.6023946, 1.5288935, 0.0, 0.0, 1.4754658, 2.926255, 0.0, 0.0, 0.0, 0.0, 3.4054031, 0.0, 0.0, 0.0, 1.7543731, 1.9311091, 0.0, 0.0, 6.1946435, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 18, 18, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, -1, 26, -1, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.03846154, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0034208389, -0.02041207, 0.5, 1.0, 0.016204322, -0.0014572466, -0.019543698, -0.0067451126, -0.5, 0.015005876, 0.032364614, -0.00052287325, 1.0, 1.2692307, 0.009175244, -0.005980407, -0.34615386, -0.0069248127, -0.023948615, -0.015964031, 0.0029411484, 0.036346268, 0.0019399535], "split_indices": [1, 50, 39, 5, 59, 16, 0, 115, 127, 39, 0, 0, 1, 93, 0, 0, 0, 0, 1, 0, 0, 0, 71, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1005.0, 1067.0, 701.0, 304.0, 547.0, 520.0, 225.0, 476.0, 203.0, 101.0, 122.0, 425.0, 392.0, 128.0, 114.0, 111.0, 157.0, 319.0, 100.0, 103.0, 156.0, 269.0, 224.0, 168.0, 103.0, 216.0, 177.0, 92.0, 133.0, 91.0, 89.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.007031496, 0.019754075, -0.019052753, -0.006886736, -0.022954777, -0.020000799, 0.015555533, 0.024516338, -0.06840566, -0.037891403, 0.124368735, -0.041571617, -0.027615303, -0.020785963, 0.00085981, 0.19870149, -0.00084553455, -0.104311556, 0.018107822, -0.0112360185, 0.033868562, 0.035474874, -0.0011039394, -0.16883625, -0.0030737647, 0.011023168, -0.029086767, 0.10117585, -0.005695015, -0.013247243, -0.020481316, 0.005396115, -0.009955169, 0.016578864, 0.004339708], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, 11, 13, 15, 17, -1, -1, 19, 21, -1, 23, 25, -1, 27, -1, -1, 29, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [5.0512285, 0.0, 4.968118, 3.9069245, 0.0, 3.6567733, 0.0, 5.5087013, 4.532232, 3.583043, 3.3568826, 2.6958847, 0.0, 0.0, 1.655602, 7.135028, 0.0, 1.6663136, 1.6043193, 0.0, 2.0966773, 0.0, 0.0, 0.24464321, 0.0, 0.0, 1.4278797, 0.7354491, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 17, 17, 18, 18, 20, 20, 23, 23, 26, 26, 27, 27], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, 12, 14, 16, 18, -1, -1, 20, 22, -1, 24, 26, -1, 28, -1, -1, 30, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.019754075, 1.0, 5.0, -0.022954777, 1.0, 0.015555533, 1.0, 2.0, 1.0, 1.0, 1.0, -0.027615303, -0.020785963, 1.0, 1.0, -0.00084553455, 1.0, -0.03846154, -0.0112360185, 1.0, 0.035474874, -0.0011039394, 0.23076923, -0.0030737647, 0.011023168, 1.0, 0.03846154, -0.005695015, -0.013247243, -0.020481316, 0.005396115, -0.009955169, 0.016578864, 0.004339708], "split_indices": [1, 0, 43, 0, 0, 97, 0, 50, 0, 89, 122, 111, 0, 0, 5, 111, 0, 127, 1, 0, 108, 0, 0, 1, 0, 0, 17, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 114.0, 1940.0, 1834.0, 106.0, 1697.0, 137.0, 884.0, 813.0, 544.0, 340.0, 720.0, 93.0, 101.0, 443.0, 218.0, 122.0, 351.0, 369.0, 100.0, 343.0, 125.0, 93.0, 187.0, 164.0, 125.0, 244.0, 197.0, 146.0, 93.0, 94.0, 112.0, 132.0, 93.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0033658398, -0.03982844, 0.045165434, -0.026971987, -0.013262296, 0.059709612, -0.011045723, -0.009170117, -0.094829634, 0.04175376, 0.017374977, 0.055623237, -0.03988298, -0.0060635386, -0.013290956, 0.01295225, 0.13276653, 0.014823935, -0.00047785472, 0.005546042, -0.07372318, 0.065524995, -0.045971557, 0.023003109, 0.0051563974, -0.00033738668, -0.11793352, -0.0038953305, 0.13294697, -0.018923953, 0.017010968, -0.024976483, 0.00045645545, 0.003760627, 0.022735294, 0.0069648298, -0.002677581], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [3.7410028, 1.215675, 2.383364, 1.0811534, 0.0, 1.9719229, 0.0, 1.4109026, 0.24219263, 2.1809256, 0.0, 1.2754729, 1.5519177, 0.0, 0.0, 1.9578012, 1.5796254, 0.0, 0.0, 0.0, 1.1041092, 2.3527408, 2.6889677, 0.0, 0.0, 0.0, 3.5204997, 0.0, 1.8271477, 0.0, 0.4770977, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 20, 20, 21, 21, 22, 22, 26, 26, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.013262296, 1.0, -0.011045723, 1.0, 1.0, 2.0, 0.017374977, 1.0, 0.0, -0.0060635386, -0.013290956, 1.0, 1.0, 0.014823935, -0.00047785472, 0.005546042, 1.0, 1.0, 1.0, 0.023003109, 0.0051563974, -0.00033738668, 0.15384616, -0.0038953305, 1.0, -0.018923953, 1.0, -0.024976483, 0.00045645545, 0.003760627, 0.022735294, 0.0069648298, -0.002677581], "split_indices": [71, 40, 44, 116, 0, 125, 0, 53, 106, 0, 0, 97, 0, 0, 0, 121, 124, 0, 0, 0, 39, 69, 17, 0, 0, 0, 1, 0, 50, 0, 109, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1019.0, 1053.0, 895.0, 124.0, 963.0, 90.0, 709.0, 186.0, 832.0, 131.0, 228.0, 481.0, 98.0, 88.0, 632.0, 200.0, 90.0, 138.0, 126.0, 355.0, 334.0, 298.0, 91.0, 109.0, 137.0, 218.0, 131.0, 203.0, 91.0, 207.0, 105.0, 113.0, 101.0, 102.0, 94.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030042534, 0.006553121, -0.020179767, -0.032244816, 0.04373608, -0.07447367, 0.0005912226, 0.07674424, -0.03770672, -0.14983559, -0.017561346, -0.05135934, 0.06839112, 0.033805054, 0.024072004, 0.003968216, -0.0137642, -0.017768087, -0.012318792, 0.005673247, -0.009002829, -0.01144684, 0.0013410465, -0.0022724734, 0.019448077, -0.00067848095, 0.017143672, -0.06541527, 0.011310131, 0.0061939275, -0.020764336], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1], "loss_changes": [3.9347832, 2.8506217, 0.0, 1.3408701, 2.712472, 1.8142557, 1.9161001, 5.0554276, 2.250559, 0.13504505, 1.2975069, 1.2589693, 2.7113495, 2.70049, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.3514106, 0.0, 5.252885, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 25, 25, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.020179767, 1.0, 0.15384616, 0.34615386, 1.0, -0.115384616, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.024072004, 0.003968216, -0.0137642, -0.017768087, -0.012318792, 0.005673247, -0.009002829, -0.01144684, 0.0013410465, -0.0022724734, 0.019448077, 1.0, 0.017143672, 1.0, 0.011310131, 0.0061939275, -0.020764336], "split_indices": [117, 71, 0, 17, 1, 1, 2, 1, 59, 69, 12, 109, 97, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 0, 124, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1976.0, 95.0, 967.0, 1009.0, 423.0, 544.0, 718.0, 291.0, 182.0, 241.0, 308.0, 236.0, 569.0, 149.0, 164.0, 127.0, 89.0, 93.0, 119.0, 122.0, 156.0, 152.0, 137.0, 99.0, 455.0, 114.0, 290.0, 165.0, 153.0, 137.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0004396127, -0.036029667, 0.03525277, -0.0218557, -0.013535991, 0.05381187, -0.012452419, -0.050374977, 0.018055782, 0.032146826, 0.022570148, -0.014292051, -0.0059637516, 0.06769379, -0.008710948, -0.040158037, 0.075447276, 0.007953035, -0.040922888, -0.008670483, 0.019921858, -0.013919967, 0.007896895, 0.037652746, 0.02288058, -0.0071131014, 0.0008245622, -0.007186501, 0.10188122, -0.0050847162, 0.024142442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, 17, 19, -1, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [2.623016, 1.4205741, 3.1343396, 1.0050719, 0.0, 3.5266235, 0.0, 2.1166804, 1.9210316, 2.6330304, 0.0, 0.0, 1.0401021, 5.0768113, 0.0, 3.716538, 3.0487547, 0.0, 0.36686647, 0.0, 0.0, 0.0, 0.0, 2.9684148, 0.0, 0.0, 0.0, 0.0, 5.669049, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 15, 15, 16, 16, 18, 18, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, 18, 20, -1, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.013535991, 1.0, -0.012452419, 1.0, 0.5769231, 1.0, 0.022570148, -0.014292051, 0.0, -0.15384616, -0.008710948, 1.0, 1.0, 0.007953035, 1.1538461, -0.008670483, 0.019921858, -0.013919967, 0.007896895, 1.0, 0.02288058, -0.0071131014, 0.0008245622, -0.007186501, -0.07692308, -0.0050847162, 0.024142442], "split_indices": [71, 40, 90, 83, 0, 125, 0, 124, 1, 93, 0, 0, 0, 1, 0, 59, 42, 0, 1, 0, 0, 0, 0, 39, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1009.0, 1057.0, 883.0, 126.0, 947.0, 110.0, 515.0, 368.0, 841.0, 106.0, 167.0, 348.0, 250.0, 118.0, 315.0, 526.0, 101.0, 247.0, 115.0, 135.0, 172.0, 143.0, 422.0, 104.0, 153.0, 94.0, 156.0, 266.0, 127.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00054930925, 0.016082969, -0.008888602, -0.017567815, 0.01750515, -0.03188775, 0.053437103, -0.047742732, 0.042392526, 0.012604526, -0.002855577, -0.09352156, -0.00058326457, 0.017069891, -0.0048223846, 0.0004934485, -0.13570242, 0.05949132, -0.099529654, 0.006972298, -0.0075226324, -0.20356904, 0.0037734509, -0.011756523, 0.026753506, -0.020360908, -0.0010086381, -0.012005859, -0.26011255, 0.008388257, -0.012929772, -0.02014556, -0.031079669], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, 13, -1, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, -1, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [3.128289, 0.0, 3.117877, 1.8963057, 0.0, 1.8278095, 1.8633996, 2.761239, 3.1740787, 0.0, 0.0, 2.5736961, 3.744823, 0.0, 0.0, 1.0536509, 5.27322, 5.810487, 2.21559, 0.0, 0.0, 1.5204754, 0.0, 3.2825277, 0.0, 0.0, 0.0, 0.0, 0.5708103, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 23, 23, 28, 28], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, 14, -1, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, -1, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.016082969, 6.0, 1.0, 0.01750515, 1.2692307, 1.0, 1.0, 1.0, 0.012604526, -0.002855577, -0.26923078, 0.115384616, 0.017069891, -0.0048223846, 1.0, 1.0, -0.115384616, 0.65384614, 0.006972298, -0.0075226324, 1.0, 0.0037734509, 1.0, 0.026753506, -0.020360908, -0.0010086381, -0.012005859, 1.0, 0.008388257, -0.012929772, -0.02014556, -0.031079669], "split_indices": [1, 0, 0, 42, 0, 1, 97, 124, 23, 0, 0, 1, 1, 0, 0, 126, 83, 1, 1, 0, 0, 105, 0, 59, 0, 0, 0, 0, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 115.0, 1953.0, 1865.0, 88.0, 1552.0, 313.0, 1279.0, 273.0, 166.0, 147.0, 649.0, 630.0, 113.0, 160.0, 201.0, 448.0, 392.0, 238.0, 105.0, 96.0, 322.0, 126.0, 292.0, 100.0, 110.0, 128.0, 130.0, 192.0, 161.0, 131.0, 89.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008453028, -0.009681465, 0.014959994, 0.03190907, -0.042122085, 0.018984582, -0.0018228207, -0.11697633, -0.017291496, 0.050850686, -0.08310676, 0.00016825534, -0.017794473, -0.09656171, 0.02162817, 0.10804387, -0.006001006, -0.017206533, 0.0039306255, -0.005313107, -0.016353417, -0.06359988, 0.08395119, 0.02752654, 0.02354362, -2.0418853e-05, -0.0137561485, -0.0033295348, 0.022191105, -0.00944794, 0.015482768, 0.0039863647, -0.004652275], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, 19, 21, 23, -1, -1, -1, -1, -1, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [2.75576, 2.6417782, 0.0, 4.571, 2.0445418, 0.0, 3.027028, 1.9822378, 2.5483508, 2.720066, 3.027333, 0.0, 0.0, 0.7911539, 2.9426649, 3.9988627, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0971897, 3.853197, 0.0, 2.912974, 0.0, 0.0, 0.36566785, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 21, 21, 22, 22, 24, 24, 27, 27], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, 20, 22, 24, -1, -1, -1, -1, -1, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.014959994, 1.0, 1.0, 0.018984582, 0.23076923, 1.0, 1.0, 1.0, 1.2692307, 0.00016825534, -0.017794473, 0.5769231, 1.0, -0.3846154, -0.006001006, -0.017206533, 0.0039306255, -0.005313107, -0.016353417, 1.0, 1.0, 0.02752654, -0.115384616, -2.0418853e-05, -0.0137561485, 1.0, 0.022191105, -0.00944794, 0.015482768, 0.0039863647, -0.004652275], "split_indices": [102, 53, 0, 81, 5, 0, 1, 127, 81, 97, 1, 0, 0, 1, 17, 1, 0, 0, 0, 0, 0, 126, 111, 0, 1, 0, 0, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1958.0, 115.0, 858.0, 1100.0, 151.0, 707.0, 274.0, 826.0, 429.0, 278.0, 93.0, 181.0, 272.0, 554.0, 283.0, 146.0, 161.0, 117.0, 165.0, 107.0, 234.0, 320.0, 95.0, 188.0, 126.0, 108.0, 196.0, 124.0, 99.0, 89.0, 98.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016319685, 0.014837394, -0.010170768, -0.013676404, 0.00022645775, -0.036254458, 0.03974394, 0.008251087, -0.048874155, 0.007839341, 0.019060409, -0.094276235, -0.004216365, 0.084304206, -0.060897686, -0.1642428, -0.015075479, 0.0803229, -0.089951955, 0.009583217, 0.017358122, 0.008978985, -0.1264521, -0.010584815, -0.023872575, 0.008119724, -0.012273532, 0.018648384, -0.0039502284, -0.012804115, -0.006018475, 0.01009038, -0.008789377, -0.012911374, 0.014707173, -0.00753783, -0.018670768], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, -1, 3, -1, 5, 7, 9, -1, 11, 13, -1, 15, 17, 19, 21, 23, 25, 27, 29, 31, -1, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.6398733, 0.0, 2.566627, 0.0, 2.5978246, 1.4043591, 4.163359, 0.0, 1.71734, 3.752761, 0.0, 2.3273897, 3.0949066, 2.2547524, 1.722353, 0.96991634, 2.0418472, 2.7349625, 0.24036694, 1.6379046, 0.0, 3.470668, 0.59703064, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, -1, 4, -1, 6, 8, 10, -1, 12, 14, -1, 16, 18, 20, 22, 24, 26, 28, 30, 32, -1, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.014837394, -0.5, -0.013676404, 1.0, -0.34615386, 1.0, 0.008251087, 0.42307693, 1.0, 0.019060409, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 0.017358122, 1.0, 1.0, -0.010584815, -0.023872575, 0.008119724, -0.012273532, 0.018648384, -0.0039502284, -0.012804115, -0.006018475, 0.01009038, -0.008789377, -0.012911374, 0.014707173, -0.00753783, -0.018670768], "split_indices": [1, 0, 1, 0, 71, 1, 42, 0, 1, 121, 0, 124, 12, 12, 109, 74, 1, 23, 53, 113, 0, 39, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 111.0, 1950.0, 148.0, 1802.0, 937.0, 865.0, 90.0, 847.0, 714.0, 151.0, 420.0, 427.0, 338.0, 376.0, 223.0, 197.0, 215.0, 212.0, 184.0, 154.0, 182.0, 194.0, 125.0, 98.0, 104.0, 93.0, 114.0, 101.0, 93.0, 119.0, 95.0, 89.0, 91.0, 91.0, 105.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0006584116, 0.007768714, -0.012260778, 0.018023172, -0.08142476, 0.0074010924, 0.011590686, -0.0025086065, -0.012669689, -0.019851921, 0.036687385, 0.040870775, -0.058044575, 0.07756744, -0.013224305, -0.04328546, 0.01635171, -0.10240014, 0.018438784, -0.030121807, 0.20301987, -0.01058602, 0.03704, 0.013081478, -0.020661663, -0.01867869, -0.0016411085, 0.013885829, -0.009080775, 0.0044990117, -0.013753997, 0.031322077, 0.010362296, -0.007484031, 0.014992828], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, 27, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.8160245, 1.7917614, 0.0, 1.8268024, 0.51521516, 1.2650515, 0.0, 0.0, 0.0, 1.9040312, 1.5588602, 3.271901, 1.7098012, 5.6741495, 1.6017601, 5.3459673, 0.0, 2.3147712, 2.4337518, 1.8234552, 2.125002, 0.0, 2.8164845, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 22, 22], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, 28, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.012260778, 1.0, 1.0, 1.0, 0.011590686, -0.0025086065, -0.012669689, -0.26923078, 0.1923077, 1.0, 0.65384614, -0.1923077, 1.0, 1.0, 0.01635171, 1.0, 1.0, 1.0, 1.0, -0.01058602, 1.2692307, 0.013081478, -0.020661663, -0.01867869, -0.0016411085, 0.013885829, -0.009080775, 0.0044990117, -0.013753997, 0.031322077, 0.010362296, -0.007484031, 0.014992828], "split_indices": [43, 40, 0, 125, 108, 111, 0, 0, 0, 1, 1, 61, 1, 1, 69, 108, 0, 124, 23, 12, 69, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1959.0, 113.0, 1757.0, 202.0, 1585.0, 172.0, 90.0, 112.0, 821.0, 764.0, 317.0, 504.0, 420.0, 344.0, 188.0, 129.0, 319.0, 185.0, 226.0, 194.0, 121.0, 223.0, 91.0, 97.0, 161.0, 158.0, 88.0, 97.0, 133.0, 93.0, 92.0, 102.0, 112.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0042428747, 0.017787328, -0.101322465, 0.007474144, 0.018930657, -0.021123437, 0.0037401263, -0.003594526, 0.014809056, -0.02014369, 0.01326474, -0.013808072, -0.0034042376, 0.03339305, -0.05589979, 0.0040608402, 0.020390363, -0.11722071, 0.023294922, -0.053258017, 0.016546795, -0.059761878, -0.023580592, -0.0072929063, 0.009873455, -0.015208646, -0.01684074, -0.013041052, 0.0025337588, 0.008809395, -0.006228326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, -1, 13, 15, 17, 19, -1, 21, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1], "loss_changes": [2.9297109, 3.212339, 3.552642, 2.6661763, 0.0, 0.0, 0.0, 3.5804474, 0.0, 2.7954686, 0.0, 0.0, 2.3953006, 3.6460586, 2.4815655, 5.7545395, 0.0, 1.9623644, 1.6187799, 2.0110443, 0.0, 1.1663597, 0.0, 0.0, 0.0, 1.6777108, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 12, 12, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, -1, 14, 16, 18, 20, -1, 22, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.1538461, 3.5, 5.0, 0.018930657, -0.021123437, 0.0037401263, 1.0, 0.014809056, 1.0, 0.01326474, -0.013808072, -0.03846154, -0.15384616, 1.0, 1.0, 0.020390363, 0.5, 0.34615386, 1.0, 0.016546795, 1.0, -0.023580592, -0.0072929063, 0.009873455, 1.0, -0.01684074, -0.013041052, 0.0025337588, 0.008809395, -0.006228326], "split_indices": [1, 1, 1, 0, 0, 0, 0, 125, 0, 26, 0, 0, 1, 1, 137, 61, 0, 1, 1, 113, 0, 115, 0, 0, 0, 122, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1816.0, 233.0, 1713.0, 103.0, 130.0, 103.0, 1588.0, 125.0, 1416.0, 172.0, 176.0, 1240.0, 729.0, 511.0, 622.0, 107.0, 288.0, 223.0, 459.0, 163.0, 194.0, 94.0, 98.0, 125.0, 345.0, 114.0, 106.0, 88.0, 108.0, 237.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.002971726, 0.0041949432, -0.014859552, 0.011173108, -0.011054334, -0.04118692, 0.025219178, -0.123112746, 0.0078117545, 0.114723176, -0.00090588327, -0.022770284, -0.0002059356, 0.21704096, -0.0014926055, -0.0385958, 0.04488147, 0.037548702, 0.0049792286, 0.032640982, -0.11755997, 0.08913402, -0.011498584, 0.09587462, -0.011089934, -0.2106978, 0.010152557, -0.006015692, 0.13548253, 0.023116207, -0.00759404, -0.009322829, -0.032048237, 0.031057114, 0.038606085, 0.013554069, -0.003965303], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, -1, -1, 17, -1, 19, 21, -1, -1, 23, 25, 27, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1, -1, 35, -1, -1], "loss_changes": [2.1582427, 1.5781058, 0.0, 1.366471, 0.0, 3.8412285, 3.4256063, 2.9500096, 0.0, 4.3908563, 1.9569681, 0.0, 0.0, 4.902478, 0.0, 3.4988458, 3.6221628, 0.0, 0.0, 2.968041, 6.01952, 2.7746851, 0.0, 5.276483, 0.0, 2.6695414, 0.0, 0.0, 5.1903586, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4944458, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 23, 23, 25, 25, 28, 28, 34, 34], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, -1, -1, 18, -1, 20, 22, -1, -1, 24, 26, 28, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.0, -0.014859552, 1.0, -0.011054334, 1.0, -0.34615386, 1.0, 0.0078117545, 1.0, 1.0, -0.022770284, -0.0002059356, 1.0, -0.0014926055, 1.0, 1.0, 0.037548702, 0.0049792286, 1.0, 0.65384614, -0.15384616, -0.011498584, 0.46153846, -0.011089934, 1.0, 0.010152557, -0.006015692, 0.1923077, 0.023116207, -0.00759404, -0.009322829, -0.032048237, 0.031057114, 1.0, 0.013554069, -0.003965303], "split_indices": [117, 43, 0, 89, 0, 97, 1, 50, 0, 97, 115, 0, 0, 53, 0, 39, 23, 0, 0, 106, 1, 1, 0, 1, 0, 93, 0, 0, 1, 0, 0, 0, 0, 0, 122, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1971.0, 97.0, 1858.0, 113.0, 393.0, 1465.0, 233.0, 160.0, 331.0, 1134.0, 125.0, 108.0, 185.0, 146.0, 622.0, 512.0, 95.0, 90.0, 327.0, 295.0, 401.0, 111.0, 227.0, 100.0, 207.0, 88.0, 95.0, 306.0, 127.0, 100.0, 100.0, 107.0, 109.0, 197.0, 88.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.007106103, 0.012089885, -0.014458184, -0.012440861, -0.0056256, 0.039166432, -0.03639396, 0.0044910866, 0.020205988, -0.056435212, 0.0068239993, 0.031026231, -0.012662373, -0.10994413, -0.022895196, 0.12271917, -0.052996565, -0.019409824, -0.029593572, 0.02079306, -0.09978654, 0.023525272, 0.0040140576, -0.015202192, 0.0016001724, -0.0069606104, 0.001087361, 0.08791914, -0.0077314265, -0.020431109, 0.00068495737, 0.021317264, -0.003614143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [1.9405538, 0.0, 1.893735, 0.0, 2.48761, 4.1515636, 2.243785, 2.1083648, 0.0, 1.6116307, 0.0, 3.882966, 0.0, 2.339593, 1.8543057, 2.2395787, 1.7969689, 0.0, 0.28659755, 2.3181174, 2.229219, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.2476556, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 27, 27], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.012089885, -0.5, -0.012440861, -0.03846154, 1.0, 1.0, 3.0, 0.020205988, 0.42307693, 0.0068239993, 1.0, -0.012662373, 1.0, 1.3461539, 1.0, 1.0, -0.019409824, 0.115384616, 1.0, 3.1538463, 0.023525272, 0.0040140576, -0.015202192, 0.0016001724, -0.0069606104, 0.001087361, 1.0, -0.0077314265, -0.020431109, 0.00068495737, 0.021317264, -0.003614143], "split_indices": [1, 0, 1, 0, 1, 42, 62, 0, 0, 1, 0, 122, 0, 93, 1, 69, 39, 0, 1, 59, 1, 0, 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 112.0, 1950.0, 145.0, 1805.0, 735.0, 1070.0, 606.0, 129.0, 898.0, 172.0, 504.0, 102.0, 346.0, 552.0, 241.0, 263.0, 169.0, 177.0, 352.0, 200.0, 102.0, 139.0, 108.0, 155.0, 89.0, 88.0, 209.0, 143.0, 101.0, 99.0, 104.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002461312, 0.0046296897, -0.014676689, -0.0023785976, 0.0096438285, -0.013124913, 0.017457737, -0.0005843683, -0.017686566, -0.011056463, 0.017581223, -0.021880621, 0.013005847, -0.0086239595, -0.10951789, -0.08049796, 0.012915323, -0.019440213, -0.003250947, 0.0060690804, -0.16425402, -0.032932784, 0.04711002, -0.016738415, -0.01610883, 0.0054024635, -0.010559585, -0.00011708003, 0.009777736], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [2.1191947, 1.2701133, 0.0, 3.487579, 0.0, 3.550325, 0.0, 2.9666698, 0.0, 2.3156145, 0.0, 1.6357825, 0.0, 1.8933442, 1.2093077, 3.3347673, 1.4752643, 0.0, 0.0, 0.0, 0.0017528534, 2.5400743, 1.3185351, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 20, 20, 21, 21, 22, 22], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, -0.014676689, 1.0, 0.0096438285, 3.0, 0.017457737, 1.0, -0.017686566, 1.0, 0.017581223, 1.0, 0.013005847, -1.0, 1.0, 1.0, 1.0, -0.019440213, -0.003250947, 0.0060690804, -0.15384616, 1.0, 1.0, -0.016738415, -0.01610883, 0.0054024635, -0.010559585, -0.00011708003, 0.009777736], "split_indices": [117, 0, 0, 84, 0, 0, 0, 102, 0, 125, 0, 0, 0, 0, 15, 122, 17, 0, 0, 0, 1, 53, 93, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1974.0, 97.0, 1834.0, 140.0, 1729.0, 105.0, 1606.0, 123.0, 1516.0, 90.0, 1408.0, 108.0, 1223.0, 185.0, 282.0, 941.0, 88.0, 97.0, 105.0, 177.0, 402.0, 539.0, 89.0, 88.0, 183.0, 219.0, 276.0, 263.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00245753, 0.009408941, -0.014169275, 0.017461672, -0.06347649, 0.024133358, -0.007968739, 0.0026927467, -0.014182662, 0.015624989, 0.010227462, 0.006899894, 0.012672983, 0.029755669, -0.017452385, 0.10952332, -0.038439468, 0.0099226115, -0.041289933, 0.0032911561, 0.017336644, -0.016453998, 0.06614625, -0.095596924, 0.0034334648, 0.014196898, -0.0013357181, -0.015748149, -0.0056215827, 0.005982792, -0.005677141], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, 21, -1, 23, -1, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [2.0692284, 1.1562458, 0.0, 1.1498141, 1.3883002, 1.1036584, 0.0, 0.0, 0.0, 1.4511919, 0.0, 0.77254725, 0.0, 3.8948727, 1.8690531, 1.6140733, 5.0906887, 0.0, 1.3552669, 0.0, 0.0, 0.0, 1.2719439, 0.6141441, 1.0389376, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 22, 22, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, 22, -1, 24, -1, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.014169275, 1.0, 1.0, 1.0, -0.007968739, 0.0026927467, -0.014182662, 5.0, 0.010227462, 1.0, 0.012672983, 1.0, 1.0, 1.0, 1.0, 0.0099226115, 1.0, 0.0032911561, 0.017336644, -0.016453998, 1.0, 1.0, 1.0, 0.014196898, -0.0013357181, -0.015748149, -0.0056215827, 0.005982792, -0.005677141], "split_indices": [117, 40, 0, 43, 108, 125, 0, 0, 0, 0, 0, 97, 0, 53, 89, 13, 122, 0, 111, 0, 0, 0, 13, 17, 106, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1970.0, 95.0, 1774.0, 196.0, 1660.0, 114.0, 91.0, 105.0, 1497.0, 163.0, 1388.0, 109.0, 716.0, 672.0, 330.0, 386.0, 114.0, 558.0, 150.0, 180.0, 175.0, 211.0, 252.0, 306.0, 108.0, 103.0, 98.0, 154.0, 158.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.003429258, 0.008511015, -0.009856883, 0.017325718, -0.058388334, -0.0012014548, 0.09053709, -0.01249488, 0.0048259655, -0.017332895, 0.020641016, 0.17840563, -0.0041893325, -0.027761549, 0.012304377, 0.025090426, 0.009638692, -0.007294598, -0.08664542, -0.058924653, 0.059984975, -0.040081963, -0.015705843, -0.036398936, -0.014640314, 0.029234836, -0.019083112, 0.00013958788, -0.008067734, -0.08889373, 0.02399828, -0.07429031, 0.0070252153, 0.0017639444, -0.016474538, 0.0045630266, 0.0002826515, -0.017439736, 0.0028091867], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, 15, -1, 17, -1, -1, -1, 19, 21, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, 33, 35, 37, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.0703502, 1.1599356, 0.0, 2.357423, 1.6255662, 4.6451654, 4.0844, 0.0, 0.0, 1.88409, 0.0, 1.2546568, 0.0, 1.4437971, 0.0, 0.0, 0.0, 3.0880733, 1.0131094, 0.99116874, 7.0917964, 0.31318855, 0.0, 1.2682161, 0.0, 0.0, 1.4204016, 0.0, 0.0, 1.7292737, 0.08518551, 1.8243542, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 11, 11, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23, 26, 26, 29, 29, 30, 30, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, 16, -1, 18, -1, -1, -1, 20, 22, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, 34, 36, 38, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009856883, 1.0, 1.0, 5.0, 1.0, -0.01249488, 0.0048259655, 3.5, 0.020641016, -0.30769232, -0.0041893325, 0.5, 0.012304377, 0.025090426, 0.009638692, -0.15384616, 1.0, 1.0, -0.03846154, 1.0384616, -0.015705843, 1.0, -0.014640314, 0.029234836, 0.26923078, 0.00013958788, -0.008067734, 0.0, 1.0, 1.0, 0.0070252153, 0.0017639444, -0.016474538, 0.0045630266, 0.0002826515, -0.017439736, 0.0028091867], "split_indices": [117, 119, 0, 61, 58, 0, 23, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 1, 2, 0, 1, 1, 0, 69, 0, 0, 1, 0, 0, 0, 124, 39, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1967.0, 98.0, 1738.0, 229.0, 1387.0, 351.0, 141.0, 88.0, 1287.0, 100.0, 211.0, 140.0, 1198.0, 89.0, 112.0, 99.0, 889.0, 309.0, 503.0, 386.0, 186.0, 123.0, 400.0, 103.0, 98.0, 288.0, 92.0, 94.0, 214.0, 186.0, 178.0, 110.0, 89.0, 125.0, 92.0, 94.0, 90.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [0.0033475684, 0.013632595, -0.037942674, -0.019546969, 0.03771902, 0.008556834, -0.075074166, -0.085887276, 0.025860706, 0.016322719, 0.011538422, -0.002950556, -0.09840353, -0.002520488, -0.016780848, 0.04661709, -0.003889918, 0.046821743, -0.016786367, -0.004389564, -0.014937847, -0.0061236243, 0.09900301, -0.023800768, 0.11471995, 0.02046411, -0.0010737493, 0.01120183, -0.086204134, 0.003522227, 0.18489107, -0.00161868, -0.017162526, 0.00641096, 0.03043305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, 17, -1, 19, -1, -1, 21, -1, 23, -1, -1, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1], "loss_changes": [0.8752473, 1.318642, 1.8849069, 2.0905776, 3.1412995, 0.0, 0.33593488, 1.4018705, 0.5538026, 0.0, 5.006952, 0.0, 0.58071375, 0.0, 0.0, 1.762799, 0.0, 3.169589, 0.0, 0.0, 0.0, 0.0, 2.4344835, 2.7460837, 1.8799343, 0.0, 0.0, 0.0, 1.3277723, 0.0, 2.5822654, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 10, 10, 12, 12, 15, 15, 17, 17, 22, 22, 23, 23, 24, 24, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, 18, -1, 20, -1, -1, 22, -1, 24, -1, -1, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, -0.1923077, 1.0, 1.0, -0.03846154, 0.008556834, -0.1923077, 1.0, -0.30769232, 0.016322719, 1.0, -0.002950556, 1.0, -0.002520488, -0.016780848, 1.0, -0.003889918, 1.0, -0.016786367, -0.004389564, -0.014937847, -0.0061236243, 1.0, 1.0, 1.0, 0.02046411, -0.0010737493, 0.01120183, 1.0, 0.003522227, 0.53846157, -0.00161868, -0.017162526, 0.00641096, 0.03043305], "split_indices": [80, 1, 26, 13, 1, 0, 1, 106, 1, 0, 64, 0, 39, 0, 0, 89, 0, 39, 0, 0, 0, 0, 53, 69, 81, 0, 0, 0, 13, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1650.0, 411.0, 694.0, 956.0, 95.0, 316.0, 282.0, 412.0, 165.0, 791.0, 107.0, 209.0, 162.0, 120.0, 312.0, 100.0, 661.0, 130.0, 101.0, 108.0, 102.0, 210.0, 324.0, 337.0, 107.0, 103.0, 102.0, 222.0, 158.0, 179.0, 122.0, 100.0, 89.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015001568, 0.0028927587, -0.009058127, -0.01928039, 0.024160434, -0.0029944964, -0.0112668015, 0.040604036, -0.011368918, -0.016174903, 0.010526252, 0.025710417, 0.015541503, -0.059005912, 0.016620813, 0.05225254, -0.047155205, -0.0012247289, -0.010849716, 0.07830681, -0.048118934, 0.007531904, 0.017989883, -0.012192576, 0.001016886, 0.01310346, 0.0036303294, -0.009391542, -0.00049645215, -0.03225053, 0.00947586, 0.027429344, -0.016669424, -0.005901057, 0.010063074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1], "loss_changes": [0.8076967, 0.92758095, 0.0, 1.4646275, 2.2758112, 1.1700345, 0.0, 1.5338259, 0.0, 1.0268165, 0.0, 1.5356026, 0.0, 0.733583, 1.653324, 3.3223019, 0.90866464, 0.0, 0.0, 0.46952713, 0.39921647, 1.4956092, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.3749814, 0.0, 1.2971423, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 29, 29, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009058127, 2.0, 1.0, 1.0, -0.0112668015, 1.0, -0.011368918, 1.0, 0.010526252, 0.34615386, 0.015541503, 1.0, 1.0, -0.07692308, 1.0, -0.0012247289, -0.010849716, 0.46153846, 0.15384616, 1.0, 0.017989883, -0.012192576, 0.001016886, 0.01310346, 0.0036303294, -0.009391542, -0.00049645215, 1.0, 0.00947586, 1.0, -0.016669424, -0.005901057, 0.010063074], "split_indices": [117, 71, 0, 0, 90, 121, 0, 125, 0, 111, 0, 1, 0, 13, 106, 1, 93, 0, 0, 1, 1, 61, 0, 0, 0, 0, 0, 0, 0, 113, 0, 13, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1967.0, 97.0, 963.0, 1004.0, 820.0, 143.0, 897.0, 107.0, 731.0, 89.0, 794.0, 103.0, 317.0, 414.0, 582.0, 212.0, 163.0, 154.0, 212.0, 202.0, 431.0, 151.0, 92.0, 120.0, 94.0, 118.0, 98.0, 104.0, 296.0, 135.0, 205.0, 91.0, 94.0, 111.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0031583116, 0.008055801, -0.010107299, 0.0030505632, 0.008977844, 0.013328715, -0.08543789, -0.002160854, 0.07120379, -0.0018720443, -0.016428573, -0.012777936, 0.014278016, -0.013620493, 0.14766034, -0.00091775466, -0.014647448, 0.026087925, -0.0011486936, -0.04210731, 0.030055234, -0.09763713, 0.014122458, 0.023148537, -0.007455028, -0.016872188, -0.0023540298, 0.004924539, -0.003351951, -0.04175689, 0.010443436, -0.011353928, -0.00013622285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, -1, -1, 15, -1, -1, 17, 19, -1, -1, -1, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [1.0464666, 0.8009024, 0.0, 1.678023, 0.0, 1.4818484, 1.0100207, 2.006661, 5.534358, 0.0, 0.0, 1.9265829, 0.0, 0.0, 4.594713, 1.4237522, 0.0, 0.0, 0.0, 1.4956441, 4.8129807, 1.2693834, 0.39825153, 0.0, 2.0610137, 0.0, 0.0, 0.0, 0.0, 1.1917456, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 14, 14, 15, 15, 19, 19, 20, 20, 21, 21, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, -1, -1, 16, -1, -1, 18, 20, -1, -1, -1, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010107299, 1.0, 0.008977844, 1.0, 1.0, 3.5, 1.0, -0.0018720443, -0.016428573, 1.3461539, 0.014278016, -0.013620493, 1.0, -0.15384616, -0.014647448, 0.026087925, -0.0011486936, 1.0, -0.03846154, 1.0, -0.30769232, 0.023148537, 1.0, -0.016872188, -0.0023540298, 0.004924539, -0.003351951, 0.26923078, 0.010443436, -0.011353928, -0.00013622285], "split_indices": [117, 102, 0, 119, 0, 61, 97, 1, 89, 0, 0, 1, 0, 0, 23, 1, 0, 0, 0, 69, 1, 97, 1, 0, 62, 0, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1958.0, 92.0, 1845.0, 113.0, 1653.0, 192.0, 1304.0, 349.0, 104.0, 88.0, 1215.0, 89.0, 94.0, 255.0, 1116.0, 99.0, 149.0, 106.0, 479.0, 637.0, 241.0, 238.0, 100.0, 537.0, 123.0, 118.0, 137.0, 101.0, 411.0, 126.0, 148.0, 263.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0008795239, -0.0033005374, 0.009439082, -0.012644284, 0.05084854, -0.04333792, 0.018339936, 0.01713679, -0.0051929527, -0.022369672, -0.013629267, 0.100408114, -0.0034056683, -0.006228247, 0.0041516623, 0.034307696, -0.05089198, 0.027604643, -0.00732567, 0.04949798, -0.09898791, -0.008173285, 0.015337541, -0.09570029, -0.009971985, -0.0015482544, 0.015750889, -0.017611993, -0.0010737727, 0.0014744074, -0.021995023, -0.011477867, 0.0075922194, -0.009275856, 0.011695331], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, -1, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, -1, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8130368, 1.0073568, 0.0, 1.6148293, 1.9789474, 1.6625803, 1.5080061, 0.0, 0.5333256, 1.1251324, 0.0, 5.398889, 3.377842, 0.0, 0.0, 3.219287, 0.84893584, 0.0, 0.0, 2.370827, 1.620045, 0.0, 0.0, 3.032719, 2.1785529, 3.156101, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 23, 23, 24, 24, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, -1, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, -1, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [6.0, 1.2692307, 0.009439082, 1.0, 1.0, 1.0, 1.0, 0.01713679, 2.7307692, -0.34615386, -0.013629267, 0.0, 1.0, -0.006228247, 0.0041516623, -0.5, 1.0, 0.027604643, -0.00732567, 1.0, 1.0, -0.008173285, 0.015337541, 1.0, 1.0, -0.07692308, 0.015750889, -0.017611993, -0.0010737727, 0.0014744074, -0.021995023, -0.011477867, 0.0075922194, -0.009275856, 0.011695331], "split_indices": [0, 1, 0, 124, 137, 50, 81, 0, 1, 1, 0, 1, 97, 0, 0, 1, 97, 0, 0, 61, 53, 0, 0, 13, 13, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1991.0, 89.0, 1698.0, 293.0, 853.0, 845.0, 93.0, 200.0, 696.0, 157.0, 177.0, 668.0, 90.0, 110.0, 233.0, 463.0, 88.0, 89.0, 430.0, 238.0, 118.0, 115.0, 221.0, 242.0, 292.0, 138.0, 127.0, 111.0, 117.0, 104.0, 109.0, 133.0, 165.0, 127.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.006190399, 0.009769022, 0.00092422054, -0.011369458, 0.01008355, 0.01971699, -0.051721092, 0.00961643, 0.016249767, 0.0020075515, -0.016165968, -0.0063474188, 0.09340952, -0.009076427, 0.007654992, 0.020134417, -0.002937851, 0.019139651, -0.009241039, 0.0015262845, 0.010197583, 0.03700366, -0.08700733, -0.0033010624, 0.007014873, 0.0098488415, -0.024184264], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, 17, -1, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1], "loss_changes": [0.99165606, 0.0, 2.0429716, 0.0, 1.0728949, 2.2483342, 1.9180524, 1.9476339, 0.0, 0.0, 0.0, 1.4456345, 3.0879695, 0.0, 1.205528, 0.0, 0.0, 1.3729411, 0.0, 2.4373696, 0.0, 1.2856282, 6.3761272, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, 18, -1, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009769022, -0.5, -0.011369458, 1.3461539, 1.1538461, 1.0, 1.0, 0.016249767, 0.0020075515, -0.016165968, 1.0, 1.0, -0.009076427, 1.0, 0.020134417, -0.002937851, 1.0, -0.009241039, 1.0, 0.010197583, 1.0, 1.0, -0.0033010624, 0.007014873, 0.0098488415, -0.024184264], "split_indices": [1, 0, 1, 0, 1, 1, 17, 42, 0, 0, 0, 26, 126, 0, 40, 0, 0, 23, 0, 105, 0, 5, 69, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 112.0, 1946.0, 144.0, 1802.0, 1559.0, 243.0, 1456.0, 103.0, 147.0, 96.0, 1223.0, 233.0, 174.0, 1049.0, 124.0, 109.0, 941.0, 108.0, 776.0, 165.0, 554.0, 222.0, 178.0, 376.0, 101.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037295646, -0.008733092, 0.0067018815, -0.00042038283, -0.012433771, -0.009397035, 0.015164408, -0.017280554, 0.009129407, 0.008755165, -0.0462208, 0.06121338, -0.03188306, -0.013753182, -0.020401817, -0.00822909, 0.02734421, 0.010242228, -0.08111523, -0.07065785, 0.018481284, -0.008970565, 0.004379809, -0.018220894, -0.007219306, -0.000966265, -0.013615267, 0.012836556, -0.066917896, -0.011125079, 0.011151225, -0.0048164185, -0.008526826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.7288682, 1.8479786, 0.0, 2.448863, 0.0, 1.3446975, 0.0, 1.1837173, 0.0, 1.763006, 1.7540227, 5.320306, 3.0812593, 0.0, 1.1333842, 1.1530069, 0.0, 0.0, 2.54741, 1.0107017, 3.0685773, 0.0, 0.0, 0.0, 2.433309, 0.0, 0.0, 0.0, 0.063320875, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 0.0067018815, 1.0, -0.012433771, 1.0, 0.015164408, 1.0, 0.009129407, -0.03846154, -1.0, -0.1923077, 0.0, -0.013753182, 0.03846154, 1.0, 0.02734421, 0.010242228, 0.65384614, -0.26923078, 1.0, -0.008970565, 0.004379809, -0.018220894, 1.0, -0.000966265, -0.013615267, 0.012836556, 1.0, -0.011125079, 0.011151225, -0.0048164185, -0.008526826], "split_indices": [0, 0, 0, 102, 0, 125, 0, 106, 0, 1, 0, 1, 0, 0, 1, 69, 0, 0, 1, 1, 124, 0, 0, 0, 109, 0, 0, 0, 53, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 1923.0, 136.0, 1794.0, 129.0, 1694.0, 100.0, 1571.0, 123.0, 827.0, 744.0, 361.0, 466.0, 164.0, 580.0, 272.0, 89.0, 125.0, 341.0, 253.0, 327.0, 106.0, 166.0, 144.0, 197.0, 131.0, 122.0, 143.0, 184.0, 105.0, 92.0, 91.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020313424, -0.0067621074, 0.0074685863, -0.014192591, 0.009877398, -0.005054988, -0.013795227, -0.011426988, 0.010843738, -0.01951614, 0.009944632, -0.044861685, 0.0055594067, -0.011904777, -0.08771586, 0.10977127, -0.029735155, -0.080931485, 0.009913817, -0.016510202, -0.0019778006, -0.001444592, 0.023398848, -0.098044604, 0.047112964, 0.0070488043, -0.01715963, 0.00075850554, -0.017271386, -0.0014464047, 0.016223524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.74981534, 1.5260222, 0.0, 2.0559158, 0.0, 1.2243322, 0.0, 1.4376842, 0.0, 0.9495169, 0.0, 1.0493693, 2.762262, 3.2192702, 1.6981552, 2.9316845, 2.9449434, 3.5556617, 0.0, 0.0, 0.0, 0.0, 0.0, 2.3425255, 1.871466, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 23, 23, 24, 24], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.0074685863, 3.0, 0.009877398, 1.0, -0.013795227, 1.0, 0.010843738, 1.0, 0.009944632, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.009913817, -0.016510202, -0.0019778006, -0.001444592, 0.023398848, -0.07692308, 0.88461536, 0.0070488043, -0.01715963, 0.00075850554, -0.017271386, -0.0014464047, 0.016223524], "split_indices": [84, 0, 0, 0, 0, 102, 0, 125, 0, 111, 0, 97, 0, 50, 53, 39, 12, 2, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1946.0, 120.0, 1818.0, 128.0, 1693.0, 125.0, 1603.0, 90.0, 1494.0, 109.0, 743.0, 751.0, 420.0, 323.0, 190.0, 561.0, 259.0, 161.0, 151.0, 172.0, 95.0, 95.0, 297.0, 264.0, 97.0, 162.0, 123.0, 174.0, 172.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002013902, 0.033591438, -0.010637097, -0.026581027, 0.07997895, 0.015049287, -0.03199767, -0.01840303, 0.011941737, 0.01678898, -0.0030269893, -0.022591146, 0.015016881, 0.013139631, -0.108688645, -0.059423324, 0.011246015, 0.015934294, -0.020344567, -0.020924931, -0.03678235, 0.00315335, -0.11506359, 0.019173691, -0.016622582, 0.0021311166, -0.010147744, -0.0008716005, -0.023034109, 0.07244112, -0.008969745, -0.0023046217, 0.01829589], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, -1, -1, -1, 15, -1, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.7278738, 1.3593394, 0.8652619, 4.873317, 2.66532, 3.6415458, 2.9804583, 0.0, 0.0, 0.0, 0.0, 2.7855706, 0.0, 2.6533618, 2.3066704, 2.2267795, 0.0, 0.0, 2.5423536, 0.0, 0.69905615, 0.0, 3.3468385, 2.012352, 0.0, 0.0, 0.0, 0.0, 0.0, 2.4588614, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 11, 11, 13, 13, 14, 14, 15, 15, 18, 18, 20, 20, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, -1, -1, -1, 16, -1, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [0.0, 1.0, 1.0, 1.0, 1.0, 0.42307693, 0.6923077, -0.01840303, 0.011941737, 0.01678898, -0.0030269893, 1.0, 0.015016881, 1.0, 1.0, -0.34615386, 0.011246015, 0.015934294, 3.0, -0.020924931, 1.0, 0.00315335, 1.0, 1.0, -0.016622582, 0.0021311166, -0.010147744, -0.0008716005, -0.023034109, 1.0, -0.008969745, -0.0023046217, 0.01829589], "split_indices": [0, 124, 124, 115, 122, 1, 1, 0, 0, 0, 0, 62, 0, 81, 23, 1, 0, 0, 0, 0, 53, 0, 39, 111, 0, 0, 0, 0, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 487.0, 1577.0, 212.0, 275.0, 716.0, 861.0, 102.0, 110.0, 153.0, 122.0, 560.0, 156.0, 542.0, 319.0, 440.0, 120.0, 101.0, 441.0, 133.0, 186.0, 167.0, 273.0, 347.0, 94.0, 98.0, 88.0, 142.0, 131.0, 233.0, 114.0, 125.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0030556235, 0.0071062176, -0.0066485526, -0.032843936, 0.01777873, -0.09960754, 0.0063903304, 0.0081250565, 0.013337734, 0.0027048683, -0.018433621, 0.016280586, -0.008944198, -0.003909068, 0.09734982, -0.017795352, 0.012433481, 0.017418353, -0.0033907716, 0.05968297, -0.051067535, -0.0039070924, 0.016492438, -0.031967673, -0.016878078, -0.010516054, -0.0054000597, -0.008399724, 0.007395649], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, 13, -1, 15, 17, 19, -1, -1, -1, 21, 23, -1, -1, 25, -1, -1, 27, -1, -1], "loss_changes": [0.578295, 0.8271549, 0.0, 2.6418104, 1.7085211, 2.5970023, 0.0, 1.1243392, 0.0, 0.0, 0.0, 2.134335, 0.0, 1.8591871, 2.6221013, 2.4283562, 0.0, 0.0, 0.0, 2.9412189, 1.4816338, 0.0, 0.0, 1.1025652, 0.0, 0.0, 2.5946755, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 19, 19, 20, 20, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, 14, -1, 16, 18, 20, -1, -1, -1, 22, 24, -1, -1, 26, -1, -1, 28, -1, -1], "split_conditions": [1.0, 1.0, -0.0066485526, 1.0, 1.0, 1.0, 0.0063903304, 1.0, 0.013337734, 0.0027048683, -0.018433621, 1.0, -0.008944198, 4.0, 1.0, 0.0, 0.012433481, 0.017418353, -0.0033907716, 1.0, 1.0, -0.0039070924, 0.016492438, 1.0, -0.016878078, -0.010516054, 1.0, -0.008399724, 0.007395649], "split_indices": [43, 89, 0, 126, 125, 15, 0, 119, 0, 0, 0, 61, 0, 0, 126, 0, 0, 0, 0, 111, 64, 0, 0, 59, 0, 0, 81, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1940.0, 113.0, 409.0, 1531.0, 242.0, 167.0, 1413.0, 118.0, 97.0, 145.0, 1304.0, 109.0, 1044.0, 260.0, 942.0, 102.0, 164.0, 96.0, 283.0, 659.0, 146.0, 137.0, 567.0, 92.0, 151.0, 416.0, 209.0, 207.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00038599267, 0.008784208, -0.004698144, -0.014161961, 0.006397476, 0.014349837, -0.04550734, 0.0038515627, 0.019150818, 0.0029687672, -0.016517255, -0.07496476, 0.01520646, -0.00089860096, -0.014456868, 0.023820503, -0.007875769, 0.06491295, -0.003612885, 0.1311828, 0.0069920556, -0.039180506, 0.030873943, 0.025449043, -0.00037575938, -0.01066818, 0.011205427, 0.031053502, -0.011395908, 0.107880816, -0.007920968, 0.008979647, -0.0031027102, 0.0011741344, 0.022505077], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, 17, -1, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.9226255, 0.0, 2.9792078, 0.0, 0.7487571, 2.9255543, 2.1685734, 1.3290025, 0.0, 0.0, 0.0, 0.85877454, 1.0506155, 0.0, 0.0, 1.3403659, 0.0, 1.8270824, 0.87457615, 3.6938984, 3.0334783, 1.843452, 3.068745, 0.0, 0.0, 0.0, 0.0, 0.6600708, 0.0, 2.3993711, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 27, 27, 29, 29], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, 18, -1, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008784208, -0.5, -0.014161961, 1.3461539, 1.1923077, 1.0, 1.0, 0.019150818, 0.0029687672, -0.016517255, 1.0, 1.0, -0.00089860096, -0.014456868, 1.0, -0.007875769, 1.0, -0.07692308, 1.0, 1.0, -0.30769232, 1.0, 0.025449043, -0.00037575938, -0.01066818, 0.011205427, 1.0, -0.011395908, 1.0, -0.007920968, 0.008979647, -0.0031027102, 0.0011741344, 0.022505077], "split_indices": [1, 0, 1, 0, 1, 1, 17, 26, 0, 0, 0, 13, 40, 0, 0, 39, 0, 126, 1, 97, 13, 1, 109, 0, 0, 0, 0, 124, 0, 71, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 114.0, 1961.0, 147.0, 1814.0, 1573.0, 241.0, 1485.0, 88.0, 148.0, 93.0, 187.0, 1298.0, 96.0, 91.0, 1189.0, 109.0, 476.0, 713.0, 222.0, 254.0, 351.0, 362.0, 116.0, 106.0, 122.0, 132.0, 181.0, 170.0, 213.0, 149.0, 93.0, 88.0, 117.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.007930585, 0.0077940705, -0.012937915, -0.013397475, -0.0033137805, -0.009059161, 0.008221172, -0.019650599, 0.03432438, -0.0046576993, -0.12085266, 0.013408436, -0.041683204, -0.009168799, 0.0064068697, -0.007257752, -0.016912775, -0.012441238, 0.0038460654, 0.024505993, -0.06765088, 0.021158705, 0.0022638566, -0.014454588, 0.0014626752, -0.009781731, 0.026381781, 0.018049117, -0.0019098477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, -1, 17, -1, 19, -1, -1, -1, -1, 21, 23, -1, 25, -1, -1, -1, 27, -1, -1], "loss_changes": [0.88964057, 0.0, 2.2773294, 0.0, 0.8898826, 0.77976143, 0.0, 2.0696137, 2.524978, 1.1439881, 0.41016483, 0.0, 1.2531147, 0.0, 1.4127607, 0.0, 0.0, 0.0, 0.0, 3.5244358, 1.3096356, 0.0, 1.8272086, 0.0, 0.0, 0.0, 4.2754507, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 19, 19, 20, 20, 22, 22, 26, 26], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, -1, 18, -1, 20, -1, -1, -1, -1, 22, 24, -1, 26, -1, -1, -1, 28, -1, -1], "split_conditions": [-0.5769231, 0.0077940705, -0.5, -0.013397475, 1.0, 1.0, 0.008221172, 1.0, -0.03846154, 1.0, 1.0, 0.013408436, 0.34615386, -0.009168799, 1.0, -0.007257752, -0.016912775, -0.012441238, 0.0038460654, -0.34615386, 1.0, 0.021158705, -0.115384616, -0.014454588, 0.0014626752, -0.009781731, 0.1923077, 0.018049117, -0.0019098477], "split_indices": [1, 0, 1, 0, 90, 58, 0, 64, 1, 89, 50, 0, 1, 0, 7, 0, 0, 0, 0, 1, 97, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 114.0, 1955.0, 144.0, 1811.0, 1697.0, 114.0, 1364.0, 333.0, 1188.0, 176.0, 144.0, 189.0, 134.0, 1054.0, 88.0, 88.0, 93.0, 96.0, 847.0, 207.0, 90.0, 757.0, 107.0, 100.0, 147.0, 610.0, 139.0, 471.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027464805, -0.054091506, 0.004560091, 0.0008534644, -0.017029783, 0.00991209, -0.008772132, -0.033177555, 0.018840851, 0.00513816, -0.009435861, 0.0057605617, 0.08548266, -0.034575466, 0.047626585, 4.925802e-05, 0.019766955, 0.00887065, -0.072282106, -0.040308513, 0.09114087, -0.0016816183, -0.10529295, -0.0127705885, 0.0033643085, 0.04574384, 0.02311548, -0.16929133, 0.0019438552, -0.0039214767, 0.093902215, -0.024527187, -0.010158589, 0.0053957608, 0.013017841], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, -1, -1, 7, -1, 9, 11, -1, -1, 13, 15, 17, 19, -1, -1, -1, 21, 23, 25, -1, 27, -1, -1, 29, -1, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.7739472, 1.8703321, 0.8919652, 0.0, 0.0, 0.65674627, 0.0, 1.5158117, 1.2325752, 0.0, 0.0, 1.9960543, 2.2120664, 2.7984262, 2.2193313, 0.0, 0.0, 0.0, 0.84408, 1.2409302, 2.466211, 0.0, 2.306976, 0.0, 0.0, 1.1988006, 0.0, 0.98256016, 0.0, 0.0, 0.2709695, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 5, 5, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 19, 19, 20, 20, 22, 22, 25, 25, 27, 27, 30, 30], "right_children": [2, 4, 6, -1, -1, 8, -1, 10, 12, -1, -1, 14, 16, 18, 20, -1, -1, -1, 22, 24, 26, -1, 28, -1, -1, 30, -1, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [-0.5, 1.0, 1.0, 0.0008534644, -0.017029783, -1.0, -0.008772132, 1.0, 1.0, 0.00513816, -0.009435861, 1.0, 1.0, -0.26923078, -0.07692308, 4.925802e-05, 0.019766955, 0.00887065, 1.0, 1.0, 1.0, -0.0016816183, 1.0, -0.0127705885, 0.0033643085, 0.34615386, 0.02311548, 1.0, 0.0019438552, -0.0039214767, 1.2692307, -0.024527187, -0.010158589, 0.0053957608, 0.013017841], "split_indices": [1, 121, 43, 0, 0, 0, 0, 122, 42, 0, 0, 122, 105, 1, 1, 0, 0, 0, 39, 69, 62, 0, 0, 0, 0, 1, 0, 12, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 257.0, 1806.0, 167.0, 90.0, 1707.0, 99.0, 293.0, 1414.0, 123.0, 170.0, 1182.0, 232.0, 602.0, 580.0, 132.0, 100.0, 141.0, 461.0, 192.0, 388.0, 172.0, 289.0, 88.0, 104.0, 293.0, 95.0, 191.0, 98.0, 106.0, 187.0, 90.0, 101.0, 89.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030000063, -0.011982898, 0.029681565, -0.06585554, 0.0012882665, -0.038648024, 0.079207286, -0.017567292, -0.0067230924, 0.013293952, -0.013960195, -0.011154551, 0.0033473931, 0.014477067, -0.0013316707, -0.005978156, 0.00528323, 0.017445939, -0.0035065021, -0.025165994, 0.08353255, 0.013040818, -0.073548, 0.018389998, -0.0028642772, 0.008520375, -0.017734539, -0.11656902, -0.00013448544, -0.010626567, 0.02020736, -0.021095987, 0.0002755223, -0.003442994, 0.005979963], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, 19, 21, 23, 25, 27, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.6059388, 1.1575087, 1.5059123, 2.078006, 2.197237, 0.9831546, 1.5650762, 0.0, 0.65726286, 3.2410605, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0435803, 1.6045172, 2.43189, 1.0771073, 1.1896946, 0.0, 0.0, 0.0, 1.1420736, 2.7031488, 0.0, 0.0, 0.5148452, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 26, 26, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1, 20, 22, 24, 26, 28, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.34615386, 1.0, 1.0, 1.0, -0.017567292, 1.0, -0.46153846, -0.013960195, -0.011154551, 0.0033473931, 0.014477067, -0.0013316707, -0.005978156, 0.00528323, 0.017445939, 1.0, 1.0, 1.0, 1.0, 1.0, 0.018389998, -0.0028642772, 0.008520375, 1.0, 1.0, -0.00013448544, -0.010626567, 0.42307693, -0.021095987, 0.0002755223, -0.003442994, 0.005979963], "split_indices": [58, 89, 71, 1, 119, 106, 69, 0, 39, 1, 0, 0, 0, 0, 0, 0, 0, 0, 105, 127, 109, 97, 97, 0, 0, 0, 126, 12, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1619.0, 445.0, 320.0, 1299.0, 187.0, 258.0, 112.0, 208.0, 1197.0, 102.0, 93.0, 94.0, 151.0, 107.0, 110.0, 98.0, 113.0, 1084.0, 868.0, 216.0, 485.0, 383.0, 114.0, 102.0, 145.0, 340.0, 240.0, 143.0, 102.0, 238.0, 134.0, 106.0, 100.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024676695, -0.005923417, 0.006710566, -0.023034876, 0.011950518, -0.008143908, -0.00944088, 0.000444026, 0.011038226, 0.06949714, -0.035620775, -0.06028783, 0.027312048, 0.019497033, -0.0023854894, 0.01482135, -0.11853639, -0.013170089, 0.003004661, -0.03317487, 0.10671573, -0.041854702, 0.008970246, -0.0053730784, -0.022339037, 0.02849116, -0.01899094, 0.002560161, 0.022194324, -0.009728932, -0.00036090918, 0.010829206, -0.006872081], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.4979259, 0.60344034, 0.0, 1.071329, 1.0929627, 1.7791994, 0.0, 1.4098275, 0.0, 2.553473, 2.5763834, 1.7095308, 2.8769276, 0.0, 0.0, 1.6254389, 1.5832632, 0.0, 0.0, 3.2861671, 2.4207633, 0.46218815, 0.0, 0.0, 0.0, 1.8928548, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 19, 19, 20, 20, 21, 21, 25, 25], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.006710566, 2.0, 1.0, 1.0, -0.00944088, 1.0, 0.011038226, 1.0, 1.0, 1.0, 1.0, 0.019497033, -0.0023854894, 1.0, 1.0, -0.013170089, 0.003004661, 1.0, 1.0, 1.0, 0.008970246, -0.0053730784, -0.022339037, 1.0, -0.01899094, 0.002560161, 0.022194324, -0.009728932, -0.00036090918, 0.010829206, -0.006872081], "split_indices": [114, 39, 0, 0, 88, 81, 0, 81, 0, 122, 121, 69, 122, 0, 0, 71, 42, 0, 0, 0, 111, 122, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1973.0, 98.0, 1008.0, 965.0, 834.0, 174.0, 864.0, 101.0, 218.0, 616.0, 265.0, 599.0, 93.0, 125.0, 383.0, 233.0, 148.0, 117.0, 340.0, 259.0, 218.0, 165.0, 144.0, 89.0, 244.0, 96.0, 152.0, 107.0, 89.0, 129.0, 134.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00060609065, 0.0037510134, -0.009137148, -0.00243172, 0.007600182, 0.009355571, -0.096497804, -0.0010684445, 0.013086053, -0.0153669035, -0.0038760502, -0.03170684, 0.030656712, -0.014297898, -0.004299904, 0.015766934, 0.00013929215, -0.066960335, 0.05229029, 0.051562123, -0.09668612, 0.0030962084, -0.013074982, -0.01660911, 0.020609606, -0.0032258115, 0.12035799, -0.020589579, -0.0009509953, 0.00626852, -0.007750716, 0.031288046, -0.0018946854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, -1, -1, 13, 15, -1, 17, -1, 19, 21, 23, 25, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8202139, 0.8840344, 0.0, 2.0213144, 0.0, 2.0518434, 0.6700846, 1.4502358, 0.0, 0.0, 0.0, 2.3146675, 2.8411791, 0.0, 2.1594934, 0.0, 2.9426105, 1.8052158, 3.3910806, 2.2258642, 1.9516981, 0.0, 0.0, 1.0671802, 0.0, 0.0, 5.6856947, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, -1, -1, 14, 16, -1, 18, -1, 20, 22, 24, 26, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009137148, 1.3461539, 0.007600182, 0.88461536, 3.3461537, 1.0, 0.013086053, -0.0153669035, -0.0038760502, 1.0, 1.0, -0.014297898, 1.0, 0.015766934, 1.0, 1.0, 1.0, 1.0, -0.26923078, 0.0030962084, -0.013074982, -0.26923078, 0.020609606, -0.0032258115, -0.30769232, -0.020589579, -0.0009509953, 0.00626852, -0.007750716, 0.031288046, -0.0018946854], "split_indices": [14, 31, 0, 1, 0, 1, 1, 124, 0, 0, 0, 89, 81, 0, 15, 0, 97, 53, 62, 50, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1979.0, 95.0, 1823.0, 156.0, 1620.0, 203.0, 1492.0, 128.0, 102.0, 101.0, 759.0, 733.0, 150.0, 609.0, 142.0, 591.0, 289.0, 320.0, 386.0, 205.0, 114.0, 175.0, 221.0, 99.0, 174.0, 212.0, 91.0, 114.0, 96.0, 125.0, 89.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00040676713, -0.0052596447, 0.010036026, 0.0022864142, -0.071960755, -0.0032571647, 0.010273369, -0.019482214, 0.003156131, -0.014546738, 0.010456539, -0.0040358533, -0.010442144, 0.01927492, -0.03243749, -0.021438343, 0.05352256, 0.0070703407, -0.079167195, 0.004804303, -0.06629769, 0.09696049, -0.0008494232, 0.010411687, -0.03573183, -0.014857329, 0.0030725773, -0.012650283, -0.001272062, 0.00012515679, 0.018871447, -0.0122798635, 0.0050577847], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, 19, 21, 23, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1718357, 0.9855213, 0.0, 0.9794772, 2.5310545, 2.02919, 0.0, 0.0, 0.0, 1.4254956, 0.0, 0.8944486, 0.0, 1.0345954, 1.1243294, 1.0566254, 1.0856344, 1.370755, 2.1280012, 0.0, 0.66447616, 2.0812569, 0.0, 0.0, 1.7208683, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 21, 21, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, 20, 22, 24, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.010036026, 1.0, -0.1923077, 4.0, 0.010273369, -0.019482214, 0.003156131, 2.0, 0.010456539, 1.0, -0.010442144, 1.0, 1.0, 1.0, 0.5, -0.30769232, 0.15384616, 0.004804303, 1.0, -0.15384616, -0.0008494232, 0.010411687, 1.0, -0.014857329, 0.0030725773, -0.012650283, -0.001272062, 0.00012515679, 0.018871447, -0.0122798635, 0.0050577847], "split_indices": [102, 119, 0, 84, 1, 0, 0, 0, 0, 0, 0, 93, 0, 111, 106, 124, 1, 1, 1, 0, 109, 1, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1958.0, 111.0, 1759.0, 199.0, 1667.0, 92.0, 91.0, 108.0, 1509.0, 158.0, 1351.0, 158.0, 742.0, 609.0, 339.0, 403.0, 330.0, 279.0, 133.0, 206.0, 237.0, 166.0, 101.0, 229.0, 171.0, 108.0, 97.0, 109.0, 116.0, 121.0, 114.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0076719876, 0.0028381571, 0.009130151, -0.042703807, 0.011885121, 0.0036308353, -0.011252853, 0.009088854, 0.004059309, -0.01445757, 0.04799482, 0.03940823, -0.04915224, 0.16086213, -0.028686011, 0.014569427, -0.003178972, -0.10968522, -0.007996212, 0.013454004, 0.018659925, -0.011601591, 0.004511388, -0.012040955, 0.05513571, -0.003938382, -0.019513777, 0.005535049, -0.05107198, 0.016149852, -0.003986794, 0.0012777935, -0.013233555], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.83599067, 0.80549234, 0.0, 1.787509, 1.0083915, 0.0, 0.0, 0.0, 1.2073063, 1.9510858, 3.808095, 1.8513083, 1.5819733, 0.12058544, 1.6885738, 0.0, 1.9961891, 1.5439098, 1.0314516, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9704474, 0.0, 0.0, 0.0, 1.1674516, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 24, 24, 28, 28], "right_children": [2, 4, -1, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.009130151, 1.0, 0.0, 0.0036308353, -0.011252853, 0.009088854, 1.0, 1.0, 1.0, 1.0, 0.07692308, -0.115384616, 1.0, 0.014569427, 1.0, 1.0, 0.6923077, 0.013454004, 0.018659925, -0.011601591, 0.004511388, -0.012040955, -0.34615386, -0.003938382, -0.019513777, 0.005535049, 1.0, 0.016149852, -0.003986794, 0.0012777935, -0.013233555], "split_indices": [102, 0, 0, 127, 0, 0, 0, 0, 105, 126, 109, 108, 1, 1, 93, 0, 109, 93, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1955.0, 113.0, 324.0, 1631.0, 152.0, 172.0, 147.0, 1484.0, 1044.0, 440.0, 409.0, 635.0, 178.0, 262.0, 117.0, 292.0, 257.0, 378.0, 88.0, 90.0, 120.0, 142.0, 97.0, 195.0, 141.0, 116.0, 153.0, 225.0, 92.0, 103.0, 126.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0037281096, 0.012832306, -0.033335783, 0.059705306, -0.0005547939, 0.004530582, -0.05655379, 0.12133654, -0.0020800477, -0.012944812, 0.009899854, 0.0008894335, -0.09167231, 0.00055273407, 0.021064699, -0.0021647506, -0.010625119, -0.015361214, -0.0044149472, -0.019691136, 0.013866127, -0.08040366, 0.018375507, -0.025158174, -0.019473778, -0.017227225, 0.09112056, -0.010556581, 0.009801945, 0.03402871, -0.015971875, 0.026927326, -0.008511647, -0.0031926332, 0.009719695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, -1, -1, 17, -1, -1, 19, -1, -1, -1, 21, -1, 23, 25, 27, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [0.698157, 1.042267, 0.7449677, 1.8308568, 1.5936399, 0.0, 0.7240093, 2.1616805, 0.0, 1.1557196, 0.0, 0.0, 0.6034286, 0.0, 0.0, 2.5422156, 0.0, 0.0, 0.0, 2.1169877, 0.0, 2.2297044, 1.4581267, 2.3572521, 0.0, 2.7607372, 5.8084645, 0.0, 0.0, 1.1582214, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 12, 12, 15, 15, 19, 19, 21, 21, 22, 22, 23, 23, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, -1, -1, 18, -1, -1, 20, -1, -1, -1, 22, -1, 24, 26, 28, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 0.0, 1.0, 1.0, 1.0, 0.004530582, -0.1923077, 1.0, -0.0020800477, 1.3461539, 0.009899854, 0.0008894335, 1.0, 0.00055273407, 0.021064699, 0.84615386, -0.010625119, -0.015361214, -0.0044149472, 1.0, 0.013866127, 1.0, -0.115384616, 0.1923077, -0.019473778, -0.26923078, 1.0, -0.010556581, 0.009801945, 1.0, -0.015971875, 0.026927326, -0.008511647, -0.0031926332, 0.009719695], "split_indices": [80, 0, 26, 15, 125, 0, 1, 124, 0, 1, 0, 0, 97, 0, 0, 1, 0, 0, 0, 15, 0, 0, 1, 1, 0, 1, 69, 0, 0, 69, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1661.0, 408.0, 369.0, 1292.0, 93.0, 315.0, 209.0, 160.0, 1149.0, 143.0, 110.0, 205.0, 91.0, 118.0, 1030.0, 119.0, 89.0, 116.0, 916.0, 114.0, 353.0, 563.0, 238.0, 115.0, 378.0, 185.0, 144.0, 94.0, 278.0, 100.0, 92.0, 93.0, 136.0, 142.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0061696814, 0.007102355, -0.01066599, -0.014178442, -7.424882e-05, 0.004771598, -0.008050583, 0.029206343, -0.019008132, 0.00402708, 0.02429439, 0.006841791, -0.08187515, 0.028994264, -0.010422348, -0.021686245, 0.014041701, -0.0192564, -0.0014033582, 0.016801752, -0.005261972, 0.008388108, -0.051385496, -0.07809813, 0.049956765, -0.0067924787, -0.015840871, -0.019693874, 0.0021978149, 0.01334288, -0.0062518246, -0.0055410075, 0.0076143392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, -1, 15, 17, 19, -1, 21, -1, -1, -1, -1, 23, -1, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.71256447, 0.0, 2.6942182, 0.0, 0.699618, 0.98372036, 0.0, 4.4937644, 1.3943425, 2.0189254, 0.0, 2.3168688, 1.8773258, 2.8907855, 0.0, 1.5707705, 0.0, 0.0, 0.0, 0.0, 1.9586754, 0.0, 1.8660425, 2.4975572, 2.6006198, 1.1128714, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 20, 20, 22, 22, 23, 23, 24, 24, 25, 25], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, -1, 16, 18, 20, -1, 22, -1, -1, -1, -1, 24, -1, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.007102355, -0.5, -0.014178442, 1.0, 1.0, -0.008050583, 1.0384616, 0.96153843, 1.0, 0.02429439, 1.0, 1.0, -0.34615386, -0.010422348, 1.0, 0.014041701, -0.0192564, -0.0014033582, 0.016801752, 1.0, 0.008388108, 1.0, 1.0, 0.1923077, 0.0, -0.015840871, -0.019693874, 0.0021978149, 0.01334288, -0.0062518246, -0.0055410075, 0.0076143392], "split_indices": [1, 0, 1, 0, 43, 108, 0, 1, 1, 50, 0, 23, 97, 1, 0, 81, 0, 0, 0, 0, 115, 0, 0, 97, 1, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 113.0, 1940.0, 145.0, 1795.0, 1693.0, 102.0, 835.0, 858.0, 747.0, 88.0, 608.0, 250.0, 607.0, 140.0, 501.0, 107.0, 95.0, 155.0, 120.0, 487.0, 110.0, 391.0, 210.0, 277.0, 276.0, 115.0, 96.0, 114.0, 159.0, 118.0, 174.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004297868, -0.0032935857, 0.049034137, 0.0048790816, -0.0682186, 0.013617182, 0.005900965, -0.0025894127, 0.009481223, -0.00032991738, -0.014540056, -0.009195866, 0.012795064, -0.032995965, 0.019813487, -0.06668589, 0.0073353346, 0.0779407, -0.027422782, -0.013027814, -0.0010953347, -0.0047927485, 0.007845227, -0.003565571, 0.16327135, -0.014556858, 0.01728103, 0.027755562, 0.0041220156, -0.004763246, 0.008580084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, -1, -1, -1, -1, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1, -1], "loss_changes": [0.6999432, 0.934936, 1.1237986, 1.0511564, 0.98708934, 0.0, 2.3887472, 0.9843268, 0.0, 0.0, 0.0, 0.0, 0.0, 0.8329192, 2.2844334, 1.1837482, 1.0965042, 3.6155849, 2.4242399, 0.0, 0.0, 0.0, 0.0, 0.0, 2.9710374, 0.0, 1.4811375, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 24, 24, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, -1, -1, -1, -1, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.6923077, 1.0, 1.0, 1.0, 0.013617182, 3.3846154, 1.0, 0.009481223, -0.00032991738, -0.014540056, -0.009195866, 0.012795064, 1.0, 1.0, 1.0, 1.0, -0.3846154, 1.0, -0.013027814, -0.0010953347, -0.0047927485, 0.007845227, -0.003565571, 1.0, -0.014556858, 1.0, 0.027755562, 0.0041220156, -0.004763246, 0.008580084], "split_indices": [1, 1, 59, 44, 111, 0, 1, 71, 0, 0, 0, 0, 0, 124, 69, 83, 109, 1, 5, 0, 0, 0, 0, 0, 109, 0, 124, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1762.0, 299.0, 1565.0, 197.0, 99.0, 200.0, 1445.0, 120.0, 107.0, 90.0, 111.0, 89.0, 613.0, 832.0, 334.0, 279.0, 373.0, 459.0, 156.0, 178.0, 157.0, 122.0, 160.0, 213.0, 126.0, 333.0, 110.0, 103.0, 171.0, 162.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0036664526, 0.00011943977, 0.00760328, -0.0076143057, 0.0069645885, 0.028715845, -0.012259548, 0.018674036, 0.008413353, -0.028022865, 0.01126793, 0.046393607, -0.051473588, -0.015646754, -0.0086871, 0.019817743, -0.0027512899, 0.0118474625, -0.015681682, -0.080785766, 0.017819757, -0.06734341, 0.007716073, -0.0020282476, 0.004653323, -0.0024023724, -0.014105883, 0.0099541405, -0.010499619, -0.021348162, 0.008979443, 0.032831207, -0.010393173, -0.0035153963, 0.01008164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, -1, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, 19, -1, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, 33, -1, -1, -1], "loss_changes": [0.53287643, 1.0330935, 0.0, 0.0, 0.7593589, 2.7334635, 1.8985503, 0.0, 1.7172635, 2.1259406, 0.0, 3.4462438, 1.9544406, 0.0, 1.4218652, 0.0, 1.8014284, 0.20394471, 0.0, 0.68424404, 1.2589827, 4.4320226, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6355906, 0.0, 0.0, 1.275668, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11, 12, 12, 14, 14, 16, 16, 17, 17, 19, 19, 20, 20, 21, 21, 28, 28, 31, 31], "right_children": [2, 4, -1, -1, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, 20, -1, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, 34, -1, -1, -1], "split_conditions": [1.0, -0.53846157, 0.00760328, -0.0076143057, 1.0, 1.0, 4.0, 0.018674036, 1.0, -0.34615386, 0.01126793, -0.34615386, 1.0, -0.015646754, 1.0, 0.019817743, 0.1923077, 1.0, -0.015681682, 1.0, 0.0, 1.0, 0.007716073, -0.0020282476, 0.004653323, -0.0024023724, -0.014105883, 0.0099541405, 1.0, -0.021348162, 0.008979443, 1.0, -0.010393173, -0.0035153963, 0.01008164], "split_indices": [114, 1, 0, 0, 59, 81, 0, 0, 97, 1, 0, 1, 50, 0, 111, 0, 1, 17, 0, 12, 0, 121, 0, 0, 0, 0, 0, 0, 74, 0, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1979.0, 97.0, 163.0, 1816.0, 852.0, 964.0, 97.0, 755.0, 856.0, 108.0, 462.0, 293.0, 112.0, 744.0, 113.0, 349.0, 183.0, 110.0, 200.0, 544.0, 193.0, 156.0, 95.0, 88.0, 103.0, 97.0, 140.0, 404.0, 100.0, 93.0, 276.0, 128.0, 138.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0020973447, 0.0069736354, -0.0017351856, -0.008175201, 0.0046608653, 0.028549593, -0.011987497, 0.007906102, 0.013935395, 0.009952153, -0.01321902, -0.01187002, 0.011109441, -0.013688333, 0.09879063, -0.011454567, 0.019390136, -0.07720503, 0.02224874, 0.00069753884, 0.020080753, -0.027018353, 0.011186338, -0.017119525, 0.0003261378, 0.015279039, -0.030369574, -0.008007017, 0.005456129, -0.07959793, 0.0029985714, -0.0065542893, -0.009349675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.53660333, 0.0, 1.0026002, 0.0, 0.72144264, 1.704104, 2.8191726, 1.2815378, 0.0, 1.8985674, 0.0, 1.6914896, 0.0, 1.6297796, 1.7796738, 0.0, 1.7337837, 1.9512687, 3.1322098, 0.0, 0.0, 1.1642182, 0.0, 0.0, 0.0, 0.0, 0.96563745, 0.0, 0.0, 0.034966826, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 21, 21, 26, 26, 29, 29], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [-0.5769231, 0.0069736354, -0.5, -0.008175201, -0.03846154, 1.0, 1.0, 1.0, 0.013935395, 1.0, -0.01321902, 1.0, 0.011109441, 0.42307693, 1.0, -0.011454567, 1.0, 1.0, 1.0, 0.00069753884, 0.020080753, -0.23076923, 0.011186338, -0.017119525, 0.0003261378, 0.015279039, 1.0, -0.008007017, 0.005456129, 1.3076923, 0.0029985714, -0.0065542893, -0.009349675], "split_indices": [1, 0, 1, 0, 1, 64, 64, 116, 0, 0, 0, 127, 0, 1, 106, 0, 97, 12, 124, 0, 0, 1, 0, 0, 0, 0, 13, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 111.0, 1959.0, 145.0, 1814.0, 745.0, 1069.0, 628.0, 117.0, 904.0, 165.0, 527.0, 101.0, 714.0, 190.0, 123.0, 404.0, 258.0, 456.0, 100.0, 90.0, 269.0, 135.0, 119.0, 139.0, 131.0, 325.0, 163.0, 106.0, 179.0, 146.0, 89.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0045803376, -0.0026462097, 0.04657568, 0.004800438, -0.06398308, -0.0023608606, 0.010969742, -0.04520945, 0.0152739175, 0.0011194631, -0.015112094, 0.000228066, -0.012904406, -0.009630321, 0.02678358, -0.010523715, 0.06904258, 0.011059108, -0.012207061, 0.019651398, 0.03644714, 0.04095801, -0.011829938, 0.10434121, -0.009795538, -0.015668262, 0.013533515, 0.020466844, 0.058692306, -0.0051912856, 0.0032339897, -0.0011403066, 0.011376726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, 15, 17, 19, 21, -1, -1, 23, 25, -1, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.62426126, 0.8016037, 1.3379068, 0.81971174, 1.244657, 0.0, 0.0, 1.0789366, 1.6617742, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8493156, 1.4998711, 2.2852411, 2.018927, 0.0, 0.0, 3.9968085, 2.265952, 0.0, 1.3327291, 0.0, 0.46110964, 0.0, 0.0, 0.7721004, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 14, 14, 15, 15, 16, 16, 17, 17, 20, 20, 21, 21, 23, 23, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, 16, 18, 20, 22, -1, -1, 24, 26, -1, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.6923077, 1.0, -1.0, 1.0, -0.0023608606, 0.010969742, 1.0, 1.0, 0.0011194631, -0.015112094, 0.000228066, -0.012904406, -0.009630321, 1.0, 1.0, 1.0, 1.0, -0.012207061, 0.019651398, 1.0, 1.0, -0.011829938, -0.3846154, -0.009795538, 0.03846154, 0.013533515, 0.020466844, 1.0, -0.0051912856, 0.0032339897, -0.0011403066, 0.011376726], "split_indices": [1, 1, 39, 0, 111, 0, 0, 111, 104, 0, 0, 0, 0, 0, 124, 64, 81, 119, 0, 0, 97, 105, 0, 1, 0, 1, 0, 0, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1755.0, 302.0, 1565.0, 190.0, 143.0, 159.0, 271.0, 1294.0, 102.0, 88.0, 173.0, 98.0, 121.0, 1173.0, 623.0, 550.0, 522.0, 101.0, 112.0, 438.0, 424.0, 98.0, 291.0, 147.0, 265.0, 159.0, 91.0, 200.0, 151.0, 114.0, 88.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.008555486, -0.014593512, 0.0062813214, 0.004267925, -0.033199, 0.012583114, -0.008528201, -0.049384385, 0.0084284535, 0.031326335, -0.061019547, -0.090082094, -0.0033289941, -0.025161253, 0.08002254, 0.0029125283, -0.10030446, -0.022287963, -0.020374889, -0.008977859, 0.06543772, 0.0043251347, -0.0079891365, -0.0025075783, 0.014117069, -0.01794069, -0.002881928, -0.008758952, 0.002060239, -0.0026664843, 0.014953138], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, 13, 15, 17, 19, 21, 23, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.8894313, 0.66781276, 0.0, 1.469984, 1.8216524, 0.0, 1.7886755, 1.5782013, 0.0, 1.3368555, 1.306752, 3.4445558, 2.3482177, 0.84245163, 1.6773343, 0.0, 1.4532447, 0.78422606, 0.0, 0.0, 1.7039536, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 20, 20], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, 14, 16, 18, 20, 22, 24, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0062813214, 1.0, 1.0, 0.012583114, 1.0, 1.0, 0.0084284535, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0029125283, 1.0, 1.0, -0.020374889, -0.008977859, 1.0, 0.0043251347, -0.0079891365, -0.0025075783, 0.014117069, -0.01794069, -0.002881928, -0.008758952, 0.002060239, -0.0026664843, 0.014953138], "split_indices": [31, 108, 0, 104, 105, 0, 109, 109, 0, 81, 105, 113, 50, 15, 17, 0, 93, 17, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1903.0, 161.0, 945.0, 958.0, 90.0, 855.0, 842.0, 116.0, 486.0, 369.0, 447.0, 395.0, 225.0, 261.0, 112.0, 257.0, 280.0, 167.0, 175.0, 220.0, 100.0, 125.0, 96.0, 165.0, 122.0, 135.0, 111.0, 169.0, 105.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0034266259, -0.0048442986, 0.03399717, 0.0026439612, -0.05283725, -0.031515386, 0.08222886, 0.009990963, -0.0058162124, -0.017112, 0.0035405406, -0.011215916, 0.004658171, 0.013343397, 0.0008375311, 0.021384457, -0.039348647, 0.0047136573, 0.06387183, -0.00779273, 0.0006374178, 0.048003435, -0.05067574, 0.020446878, 0.0009917722, 0.0058009606, 0.01596719, 0.0053080153, -0.01556453, 0.00952864, -0.011794801, -0.005871273, 0.006654439, 0.007319532, -0.006388479], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, 27, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.52364546, 0.5857955, 1.3934639, 0.6299066, 2.296269, 1.1777344, 0.96054614, 0.7071853, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7238809, 0.41628653, 1.7599813, 2.5461338, 0.0, 0.0, 1.9416262, 1.8922627, 0.0, 2.2318614, 1.1717162, 0.0, 0.9864357, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, 28, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0058162124, -0.017112, 0.0035405406, -0.011215916, 0.004658171, 0.013343397, 0.0008375311, 1.0, 1.0, 1.0, 1.0, -0.00779273, 0.0006374178, 1.0, 1.0, 0.020446878, 1.0, 1.0, 0.01596719, 1.0, -0.01556453, 0.00952864, -0.011794801, -0.005871273, 0.006654439, 0.007319532, -0.006388479], "split_indices": [58, 64, 71, 73, 124, 106, 69, 7, 0, 0, 0, 0, 0, 0, 0, 0, 97, 106, 69, 0, 0, 15, 109, 0, 97, 12, 0, 12, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1630.0, 441.0, 1410.0, 220.0, 187.0, 254.0, 1258.0, 152.0, 94.0, 126.0, 92.0, 95.0, 150.0, 104.0, 1022.0, 236.0, 734.0, 288.0, 128.0, 108.0, 412.0, 322.0, 89.0, 199.0, 299.0, 113.0, 210.0, 112.0, 111.0, 88.0, 145.0, 154.0, 106.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018037135, 0.004553332, -0.049312912, -0.0015643182, 0.010614165, -0.010341894, 0.00043532075, -0.04970408, 0.0050997203, -0.015190068, 0.0029974605, -0.0053449715, 0.06570705, -0.06818397, 0.008527031, 0.013418092, 0.0005864319, 0.004438615, -0.014649364, -0.016676301, 0.026786415, 0.0054624574, 0.102481395, 0.033197638, -0.059957724, -0.0022939637, 0.019759236, 0.0020559765, 0.017448239, 0.009885384, -0.0184466, 0.0092404345, -0.0033615537], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, 21, 23, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.62698984, 1.1379335, 0.7113972, 0.5540308, 0.0, 0.0, 0.0, 1.7100074, 0.960299, 0.0, 0.0, 1.1279837, 0.91377854, 2.0627873, 3.392729, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5495512, 1.3590158, 2.517002, 2.314316, 4.4094586, 0.0, 0.0, 1.3890542, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, 22, 24, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 1.1538461, 3.1923077, 1.0, 0.010614165, -0.010341894, 0.00043532075, 1.0, 1.0, -0.015190068, 0.0029974605, -1.0, 1.0, -0.30769232, 1.0, 0.013418092, 0.0005864319, 0.004438615, -0.014649364, -0.016676301, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0022939637, 0.019759236, 1.0, 0.017448239, 0.009885384, -0.0184466, 0.0092404345, -0.0033615537], "split_indices": [1, 1, 1, 26, 0, 0, 0, 116, 23, 0, 0, 0, 53, 1, 104, 0, 0, 0, 0, 0, 42, 105, 93, 61, 69, 0, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1831.0, 245.0, 1727.0, 104.0, 122.0, 123.0, 210.0, 1517.0, 92.0, 118.0, 1294.0, 223.0, 234.0, 1060.0, 104.0, 119.0, 96.0, 138.0, 100.0, 960.0, 749.0, 211.0, 526.0, 223.0, 91.0, 120.0, 431.0, 95.0, 98.0, 125.0, 122.0, 309.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.004668102, 0.007777367, -0.0092716655, -0.009931745, -0.0020460759, -0.0076619904, 0.007907267, -0.017678153, 0.032108057, -0.045653563, 0.00017993372, 0.011772599, -0.03468288, -0.012276116, -0.009468988, 0.012300538, -0.020756217, 0.0053999233, -0.01363186, 0.0135575775, -0.085386045, -0.059982892, 0.0210012, 0.0011789644, -0.020364705, -0.120887265, 0.0016431278, -0.030627247, 0.011021518, -0.0072281444, -0.01631923, 0.0048591276, -0.010020181], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, 15, -1, 17, -1, 19, -1, 21, -1, -1, -1, 23, 25, 27, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.7822028, 0.0, 1.270037, 0.0, 0.82318926, 0.6732001, 0.0, 0.67444307, 1.9442911, 1.4675951, 2.1189098, 0.0, 1.7215348, 0.0, 3.9420705, 0.0, 1.1531553, 0.0, 0.0, 0.0, 2.7006428, 1.6893868, 1.5706391, 0.0, 0.0, 0.4153664, 0.0, 1.1905044, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 20, 20, 21, 21, 22, 22, 25, 25, 27, 27], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, 16, -1, 18, -1, 20, -1, 22, -1, -1, -1, 24, 26, 28, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.007777367, -0.5, -0.009931745, 1.0, 1.0, 0.007907267, 1.0, 1.0, -0.1923077, -0.30769232, 0.011772599, 1.0, -0.012276116, 0.1923077, 0.012300538, 1.0, 0.0053999233, -0.01363186, 0.0135575775, 1.0, 0.53846157, 1.0, 0.0011789644, -0.020364705, -0.07692308, 0.0016431278, 0.46153846, 0.011021518, -0.0072281444, -0.01631923, 0.0048591276, -0.010020181], "split_indices": [1, 0, 1, 0, 90, 58, 0, 69, 69, 1, 1, 0, 109, 0, 1, 0, 12, 0, 0, 0, 127, 1, 61, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 109.0, 1952.0, 145.0, 1807.0, 1690.0, 117.0, 1350.0, 340.0, 526.0, 824.0, 149.0, 191.0, 168.0, 358.0, 120.0, 704.0, 102.0, 89.0, 123.0, 235.0, 363.0, 341.0, 129.0, 106.0, 202.0, 161.0, 216.0, 125.0, 94.0, 108.0, 101.0, 115.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00049017294, 0.015420634, -0.015870623, -0.0028082898, 0.019195339, 0.004278248, -0.07772298, 0.0103356, -0.012553386, 0.052870367, -0.04334202, -0.016047394, -0.003108151, -0.033063225, 0.06184316, -0.017567646, 0.11980402, -0.014802197, 0.009391473, 0.027241124, -0.09925778, 0.0030058434, 0.016620422, -0.0074018734, 0.0030662382, 0.018854605, 0.0061783185, -0.00600217, 0.011416609, 0.007643835, -0.0049763196, -0.0037413705, -0.014294127, -0.0036066757, 0.0053505367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, 17, -1, -1, 19, 21, 23, 25, -1, 27, 29, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.50533724, 3.266273, 1.3085682, 1.4840444, 0.0, 1.832664, 0.9957832, 1.8575907, 0.0, 1.8481523, 2.208056, 0.0, 0.0, 1.800311, 2.333323, 0.52002394, 0.80168176, 0.0, 1.9345489, 0.8940623, 0.5808358, 0.4794749, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, 18, -1, -1, 20, 22, 24, 26, -1, 28, 30, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1538461, 1.1153846, 0.61538464, 0.019195339, 1.0, 1.0, 1.0, -0.012553386, 1.0, 1.0, -0.016047394, -0.003108151, 1.0, 1.0, 1.0, 1.0, -0.014802197, 1.0, -0.07692308, 1.0, 0.03846154, 0.016620422, -0.0074018734, 0.0030662382, 0.018854605, 0.0061783185, -0.00600217, 0.011416609, 0.007643835, -0.0049763196, -0.0037413705, -0.014294127, -0.0036066757, 0.0053505367], "split_indices": [108, 1, 1, 1, 0, 15, 97, 115, 0, 93, 13, 0, 0, 93, 71, 12, 39, 0, 0, 1, 13, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1015.0, 1050.0, 920.0, 95.0, 792.0, 258.0, 831.0, 89.0, 392.0, 400.0, 93.0, 165.0, 451.0, 380.0, 191.0, 201.0, 134.0, 266.0, 236.0, 215.0, 243.0, 137.0, 88.0, 103.0, 92.0, 109.0, 160.0, 106.0, 144.0, 92.0, 89.0, 126.0, 137.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00012838794, 0.008323453, -0.053544775, 0.0014610892, 0.012666418, -0.015620812, 0.0077350955, -0.0044261934, 0.0074538174, -0.013542331, 0.027077714, 0.015286913, -0.04660667, -0.020124083, 0.008407613, 0.06366135, -0.005384097, -0.0130542265, -0.10507912, 0.0004273389, -0.004304295, 0.002244963, 0.009932532, 0.00716382, -0.045312054, -0.067529514, 0.0064609847, -0.0030351793, -0.020331614, 0.001074873, -0.008843576, -0.01743012, 0.004875644], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.90654117, 1.4520293, 3.668627, 0.7270812, 0.0, 0.0, 0.0, 0.4491713, 0.0, 1.1562561, 0.94434035, 0.6479668, 1.1084696, 0.10735932, 0.0, 0.28513575, 1.3962055, 1.5188475, 1.512243, 0.0, 0.0, 0.0, 0.0, 0.0, 0.7228472, 2.6197863, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.012666418, -0.015620812, 0.0077350955, 1.0, 0.0074538174, 1.0, 1.0, 0.0, 1.0, 1.0, 0.008407613, 1.0, 1.0, 1.0, 1.0, 0.0004273389, -0.004304295, 0.002244963, 0.009932532, 0.00716382, 1.0, 1.0, 0.0064609847, -0.0030351793, -0.020331614, 0.001074873, -0.008843576, -0.01743012, 0.004875644], "split_indices": [0, 102, 122, 125, 0, 0, 0, 116, 0, 71, 71, 0, 106, 106, 0, 39, 53, 105, 108, 0, 0, 0, 0, 0, 16, 109, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1788.0, 273.0, 1690.0, 98.0, 153.0, 120.0, 1564.0, 126.0, 1213.0, 351.0, 648.0, 565.0, 192.0, 159.0, 194.0, 454.0, 359.0, 206.0, 93.0, 99.0, 90.0, 104.0, 155.0, 299.0, 211.0, 148.0, 117.0, 89.0, 130.0, 169.0, 110.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0007197491, 0.009396288, -0.0043888083, -0.01056537, 0.0035513423, -0.0034817567, 0.043692116, 0.017655343, -0.03899209, -0.0023701985, 0.014171992, -0.00074183615, 0.09854128, -0.07365385, 0.03645946, -0.017630635, 0.009043019, -0.0029631653, 0.022248877, 0.002174002, -0.110137835, 0.011329072, -0.0037866384, 0.01383596, -0.015198802, -0.015138588, -0.00636568, 0.006670034, -0.0112203, -0.0060634715, 0.030457499, 0.009115549, -0.00290265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, 19, 21, 23, -1, -1, -1, -1, 25, -1, -1, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [0.9812563, 0.0, 1.5703267, 0.0, 0.5112709, 1.1566523, 1.7837543, 1.4374788, 1.5037884, 0.0, 0.0, 1.2118114, 2.8437223, 1.3712575, 1.0336095, 2.8072393, 0.0, 0.0, 0.0, 0.0, 0.5464163, 0.0, 0.0, 0.7126261, 0.0, 0.0, 0.0, 0.0, 0.7517117, 0.0, 0.71489084, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 20, 20, 23, 23, 28, 28, 30, 30], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, 20, 22, 24, -1, -1, -1, -1, 26, -1, -1, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.009396288, -0.5, -0.01056537, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0023701985, 0.014171992, 1.3846154, 1.0, 0.0, 1.0, 1.0, 0.009043019, -0.0029631653, 0.022248877, 0.002174002, 1.0, 0.011329072, -0.0037866384, 1.0, -0.015198802, -0.015138588, -0.00636568, 0.006670034, 1.0, -0.0060634715, 0.115384616, 0.009115549, -0.00290265], "split_indices": [1, 0, 1, 0, 42, 109, 105, 105, 61, 0, 0, 1, 39, 0, 105, 113, 0, 0, 0, 0, 137, 0, 0, 122, 0, 0, 0, 0, 81, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 107.0, 1953.0, 142.0, 1811.0, 1541.0, 270.0, 966.0, 575.0, 160.0, 110.0, 787.0, 179.0, 394.0, 181.0, 664.0, 123.0, 88.0, 91.0, 109.0, 285.0, 89.0, 92.0, 538.0, 126.0, 151.0, 134.0, 173.0, 365.0, 167.0, 198.0, 98.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-6.684126e-05, 0.0078560505, -0.052468628, 0.0028662619, 0.009263253, -0.0132398, 0.0044356356, -0.004098369, 0.009369428, -0.010319271, 0.0019659097, 0.02128147, -0.028583521, 0.0052153766, 0.010975971, -0.08234822, 0.0036752913, -0.0725532, 0.05184426, -0.00016255605, -0.015870746, -0.007912467, 0.04507529, -0.013585349, -0.0020798235, 0.07693698, -0.005895839, 0.00928815, -0.0019603667, 0.052733835, 0.014598711, -0.0009826798, 0.010762576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, -1, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.85982484, 0.76100683, 2.1050515, 1.0747598, 0.0, 0.0, 0.0, 0.9482769, 0.0, 0.0, 0.87744796, 1.2949859, 0.99900615, 2.795848, 0.0, 1.3314066, 1.2340505, 0.94679356, 1.340124, 0.0, 0.0, 0.0, 0.74209356, 0.0, 0.0, 0.6567929, 0.0, 0.0, 0.0, 0.99931574, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 22, 22, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, -1, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [3.0, 1.0, -0.115384616, 1.0, 0.009263253, -0.0132398, 0.0044356356, -0.53846157, 0.009369428, -0.010319271, 1.0, 1.3076923, 1.0, 1.0, 0.010975971, 0.0, 1.0, 1.0, 0.8076923, -0.00016255605, -0.015870746, -0.007912467, 0.34615386, -0.013585349, -0.0020798235, 1.0, -0.005895839, 0.00928815, -0.0019603667, -0.1923077, 0.014598711, -0.0009826798, 0.010762576], "split_indices": [0, 102, 1, 125, 0, 0, 0, 1, 0, 0, 115, 1, 111, 108, 0, 1, 17, 13, 1, 0, 0, 0, 1, 0, 0, 7, 0, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1799.0, 272.0, 1699.0, 100.0, 149.0, 123.0, 1578.0, 121.0, 91.0, 1487.0, 911.0, 576.0, 771.0, 140.0, 216.0, 360.0, 289.0, 482.0, 105.0, 111.0, 120.0, 240.0, 130.0, 159.0, 393.0, 89.0, 138.0, 102.0, 291.0, 102.0, 136.0, 155.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.004344406, -0.01837212, 0.010323459, -0.009987249, -0.004743595, 0.026750714, -0.04431654, 0.048287988, -0.036318943, 0.006837342, 0.013987924, -0.013499509, 0.0028788624, 0.014170605, -0.005383361, -0.005544164, -0.013953279, 0.06386068, -0.05248104, 0.004965172, -0.04014458, -0.0010288233, 0.11304858, -0.01683657, 0.0050527523, 0.0018740864, -0.014395297, 0.021360414, 0.0004199754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, -1, 7, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, 19, -1, 21, 23, -1, 25, -1, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42427015, 1.1707089, 0.90476584, 0.0, 1.512065, 1.7458968, 1.5445739, 3.214981, 1.7978325, 2.2290888, 0.0, 0.0, 0.0, 0.0, 0.0, 0.832673, 0.0, 1.2254695, 3.8556874, 0.0, 1.6382316, 0.0, 2.210961, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 17, 17, 18, 18, 20, 20, 22, 22], "right_children": [2, 4, 6, -1, 8, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, 20, -1, 22, 24, -1, 26, -1, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, -0.009987249, -0.07692308, 1.2692307, 0.07692308, 1.0, 1.0, -0.15384616, 0.013987924, -0.013499509, 0.0028788624, 0.014170605, -0.005383361, 0.5, -0.013953279, 1.0, 1.0, 0.004965172, 1.0, -0.0010288233, -0.42307693, -0.01683657, 0.0050527523, 0.0018740864, -0.014395297, 0.021360414, 0.0004199754], "split_indices": [39, 1, 83, 0, 1, 1, 1, 122, 15, 1, 0, 0, 0, 0, 0, 1, 0, 109, 124, 0, 71, 0, 1, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1054.0, 1008.0, 151.0, 903.0, 775.0, 233.0, 337.0, 566.0, 659.0, 116.0, 104.0, 129.0, 176.0, 161.0, 436.0, 130.0, 336.0, 323.0, 168.0, 268.0, 134.0, 202.0, 152.0, 171.0, 171.0, 97.0, 105.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.0011240077, -0.0145015875, 0.01732545, -0.0087388, -0.0021354633, 0.0069020176, 0.010950125, 0.026652573, -0.06139332, 0.010240703, -0.008462362, 0.01644479, -0.0036567794, 0.0019432061, -0.016576627, -0.06800519, 0.04374547, -0.060461372, 0.07048254, -0.011294475, -0.020015672, -0.0034341402, 0.01880596, 0.002959427, -0.019001512, 0.0010566296, 0.014779377, 0.0031092472, -0.005788511, -0.062214043, 0.0086886175, -0.010194222, -0.002206319], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, -1, 7, 9, -1, 11, 13, -1, 15, -1, 17, -1, -1, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.5177064, 0.9382763, 0.964631, 0.0, 1.5182664, 1.3235728, 0.0, 2.501716, 2.454872, 0.0, 2.4153838, 0.0, 2.067824, 0.0, 0.0, 2.7204695, 2.818796, 3.2434397, 0.9866582, 0.5016081, 0.0, 1.656414, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.30147743, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 10, 10, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 29, 29], "right_children": [2, 4, 6, -1, 8, 10, -1, 12, 14, -1, 16, -1, 18, -1, -1, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, -0.0087388, 1.0, -0.46153846, 0.010950125, 1.0, 1.0, 0.010240703, 0.03846154, 0.01644479, 1.0, 0.0019432061, -0.016576627, -0.15384616, 1.0, 1.0, 1.0, 1.0, -0.020015672, 1.0, 0.01880596, 0.002959427, -0.019001512, 0.0010566296, 0.014779377, 0.0031092472, -0.005788511, 1.0, 0.0086886175, -0.010194222, -0.002206319], "split_indices": [39, 1, 88, 0, 115, 1, 0, 81, 12, 0, 1, 0, 12, 0, 0, 1, 0, 50, 127, 108, 0, 93, 0, 0, 0, 0, 0, 0, 0, 81, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1041.0, 1004.0, 151.0, 890.0, 902.0, 102.0, 599.0, 291.0, 125.0, 777.0, 108.0, 491.0, 164.0, 127.0, 363.0, 414.0, 278.0, 213.0, 254.0, 109.0, 312.0, 102.0, 164.0, 114.0, 120.0, 93.0, 133.0, 121.0, 189.0, 123.0, 95.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023048287, -0.022306161, 0.01071039, -0.009476286, -0.008819189, 0.09288008, -0.0038718337, 0.013136141, -0.00782502, -0.0019133121, 0.020608494, 0.029776396, -0.027286291, -0.016565837, 0.010655369, -0.022234347, 0.022216026, 0.009360357, -0.06489087, 0.0053267176, -0.067776725, -0.009417855, 0.035396338, -0.015599715, -0.018331815, -0.015829386, 0.00046369588, 0.010079019, -0.003954939, -0.010719524, 0.019126296, 0.007331983, -0.0033628445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, 21, -1, -1, 23, -1, 25, -1, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.538866, 0.68976915, 1.5025641, 1.0621643, 0.0, 2.3966036, 0.8390656, 1.4261887, 0.0, 0.0, 0.0, 4.372635, 2.8548956, 1.3947221, 0.0, 1.4262909, 0.0, 0.0, 2.0318325, 0.0, 1.4748033, 0.0, 0.9360892, 0.0, 1.0551842, 0.0, 0.0, 0.0, 0.0, 0.0, 0.63754964, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 20, 20, 22, 22, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, 22, -1, -1, 24, -1, 26, -1, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [-0.1923077, 3.0, -0.03846154, 1.0, -0.008819189, 1.0, 1.0, 1.0, -0.00782502, -0.0019133121, 0.020608494, 0.7307692, 0.0, 1.0, 0.010655369, 1.0, 0.022216026, 0.009360357, 1.0, 0.0053267176, 1.0, -0.009417855, 0.23076923, -0.015599715, 1.0, -0.015829386, 0.00046369588, 0.010079019, -0.003954939, -0.010719524, 1.0, 0.007331983, -0.0033628445], "split_indices": [1, 0, 1, 42, 0, 124, 124, 58, 0, 0, 0, 1, 0, 5, 0, 106, 0, 0, 137, 0, 69, 0, 1, 0, 81, 0, 0, 0, 0, 0, 50, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 816.0, 1254.0, 683.0, 133.0, 189.0, 1065.0, 514.0, 169.0, 95.0, 94.0, 437.0, 628.0, 390.0, 124.0, 344.0, 93.0, 149.0, 479.0, 165.0, 225.0, 153.0, 191.0, 162.0, 317.0, 100.0, 125.0, 102.0, 89.0, 94.0, 223.0, 110.0, 113.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0010825263, -0.01348285, 0.015327328, -0.026742725, 0.01903967, 0.029078765, -0.01039791, -0.009573227, -0.009376387, 0.0100967055, -0.0054484885, 0.017646816, 0.011775647, -0.028543396, 0.0067291628, -0.027467156, 0.045524266, -0.08270159, 0.029483227, -0.011401042, 0.007389515, -0.011286546, 0.08684123, -0.00089894095, -0.013535313, -0.0012888875, 0.008396167, -0.008356503, 0.004034092, 0.023587821, 0.018553834, -0.0037373845, 0.008660441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, -1, 15, -1, 17, -1, 19, 21, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.42886296, 0.44073194, 1.7144632, 0.869817, 1.7830079, 0.94989187, 0.0, 0.0, 0.85230786, 0.0, 0.0, 1.04386, 0.0, 1.4581747, 0.0, 2.7807956, 1.2041397, 0.93145394, 0.5170742, 0.0, 0.0, 0.80601597, 1.8541489, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.69532907, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 21, 21, 22, 22, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, -1, 16, -1, 18, -1, 20, 22, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 0.88461536, 1.0, -1.0, 1.0, 1.0, -0.01039791, -0.009573227, 1.0, 0.0100967055, -0.0054484885, 1.0, 0.011775647, 1.0, 0.0067291628, 1.0, -0.1923077, 1.0, 1.0, -0.011401042, 0.007389515, 1.0, 1.0, -0.00089894095, -0.013535313, -0.0012888875, 0.008396167, -0.008356503, 0.004034092, 1.0, 0.018553834, -0.0037373845, 0.008660441], "split_indices": [71, 1, 90, 0, 12, 125, 0, 0, 73, 0, 0, 93, 0, 124, 0, 59, 1, 126, 126, 0, 0, 13, 122, 0, 0, 0, 0, 0, 0, 105, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 1022.0, 1045.0, 726.0, 296.0, 937.0, 108.0, 146.0, 580.0, 140.0, 156.0, 830.0, 107.0, 464.0, 116.0, 317.0, 513.0, 240.0, 224.0, 171.0, 146.0, 216.0, 297.0, 100.0, 140.0, 126.0, 98.0, 90.0, 126.0, 181.0, 116.0, 92.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0025481852, 0.006097698, -0.006979598, 0.0024851412, 0.006513446, -0.022013148, 0.030933855, 0.002162893, -0.016826121, -0.07789074, 0.06026361, 0.040380687, -0.034230866, 0.0014961244, -0.016580382, 0.04136349, 0.015802877, 0.009784597, 0.00036436378, -0.10946464, 0.056881383, 0.0130319595, 0.08281827, -0.009221668, 0.009834601, -0.0016059101, -0.02284245, 0.017291266, -0.0078068003, 0.009017262, -0.053088594, 0.0036690792, 0.0133558465, -0.0048152865, -0.0057812], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, -1, 23, 25, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [0.53257513, 0.42164183, 0.0, 1.2984084, 0.0, 3.5392349, 2.7513306, 1.1947737, 0.0, 1.493812, 1.2546377, 0.96351093, 3.0160763, 0.0, 0.0, 0.66827726, 0.0, 0.0, 2.240597, 2.6778731, 3.116012, 1.7239972, 0.54065907, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.004242718, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, -1, 24, 26, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.006979598, 1.0, 0.006513446, 3.0, -1.0, 1.0, -0.016826121, 1.0, 3.0, 1.0, 1.0, 0.0014961244, -0.016580382, 1.0, 0.015802877, 0.009784597, 1.0, 1.0, 1.0, 1.0, 1.0, -0.009221668, 0.009834601, -0.0016059101, -0.02284245, 0.017291266, -0.0078068003, 0.009017262, 1.0, 0.0036690792, 0.0133558465, -0.0048152865, -0.0057812], "split_indices": [117, 84, 0, 122, 0, 0, 0, 93, 0, 127, 0, 108, 50, 0, 0, 93, 0, 0, 59, 121, 126, 115, 71, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1977.0, 97.0, 1863.0, 114.0, 1001.0, 862.0, 859.0, 142.0, 183.0, 679.0, 419.0, 440.0, 89.0, 94.0, 569.0, 110.0, 172.0, 247.0, 241.0, 199.0, 338.0, 231.0, 127.0, 120.0, 135.0, 106.0, 107.0, 92.0, 156.0, 182.0, 121.0, 110.0, 89.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.6929958E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}