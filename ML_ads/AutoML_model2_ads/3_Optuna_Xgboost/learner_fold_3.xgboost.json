{"learner": {"attributes": {"best_iteration": "40", "best_score": "1.048936"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "91"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.0026328564, 0.15402073, -0.24184325, 0.0983406, 0.05379386, -0.27249888, -0.005177845, -0.10215131, 0.17196065, -0.24564902, -0.04537353, 0.0004235489, -0.018919509, 0.28465354, 0.07492665, -0.19992349, -0.036967948, 0.35629818, 0.010319843, -0.008556007, 0.17732678, -0.012551442, -0.2526543, 0.049691, 0.010807532, 0.010528507, 0.030030707, -0.029284185, -0.0185393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, 17, 19, 21, -1, 23, -1, -1, 25, -1, 27, -1, -1, -1, -1, -1, -1], "loss_changes": [76.64919, 27.340668, 4.614628, 16.487173, 0.0, 3.3187256, 0.0, 2.7780929, 8.933928, 3.3687897, 0.0, 0.0, 0.0, 4.9141083, 7.2144656, 1.7028637, 0.0, 9.458729, 0.0, 0.0, 2.374401, 0.0, 0.68657875, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 13, 13, 14, 14, 15, 15, 17, 17, 20, 20, 22, 22], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, 18, 20, 22, -1, 24, -1, -1, 26, -1, 28, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.05379386, 1.0, -0.005177845, 1.0, 1.0, 1.0, -0.04537353, 0.0004235489, -0.018919509, 1.0, 1.0, 1.0, -0.036967948, 1.0, 0.010319843, -0.008556007, 1.0, -0.012551442, 1.0, 0.049691, 0.010807532, 0.010528507, 0.030030707, -0.029284185, -0.0185393], "split_indices": [137, 125, 71, 17, 0, 40, 0, 53, 53, 116, 0, 0, 0, 106, 81, 122, 0, 97, 0, 0, 109, 0, 74, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1279.0, 792.0, 1117.0, 162.0, 682.0, 110.0, 300.0, 817.0, 594.0, 88.0, 135.0, 165.0, 378.0, 439.0, 434.0, 160.0, 271.0, 107.0, 171.0, 268.0, 180.0, 254.0, 173.0, 98.0, 169.0, 99.0, 159.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018126705, -0.17025107, 0.16564454, -0.23884226, -0.026001709, 0.109016284, 0.05140656, -0.27684355, -0.0043455176, 0.053120427, -0.020539748, 0.039800547, 0.27935186, -0.20756912, -0.37668017, 0.017921975, -0.014163295, 0.11751633, -0.08820192, 0.011554765, 0.0423885, -0.26180592, -0.011416125, -0.042323597, -0.03309003, 0.011899233, 0.028428016, -0.023665344, 0.0031009123, -0.036669403, -0.018460827, -0.008613775, 0.01297235], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, -1, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [57.935585, 10.1317005, 20.32239, 5.152916, 4.684079, 10.445853, 0.0, 4.018265, 0.0, 5.6238437, 0.0, 6.2671223, 6.060833, 1.7376881, 0.50725174, 0.0, 0.0, 6.90434, 4.2119007, 0.0, 0.0, 1.7570724, 0.0, 0.0, 0.0, 2.7722728, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, -1, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 2.7307692, 1.0, 1.0, 0.05140656, 1.0, -0.0043455176, 0.1923077, -0.020539748, 0.15384616, 1.0, 1.0, 1.0, 0.017921975, -0.014163295, -0.1923077, 1.0, 0.011554765, 0.0423885, 1.0, -0.011416125, -0.042323597, -0.03309003, 1.0, 0.028428016, -0.023665344, 0.0031009123, -0.036669403, -0.018460827, -0.008613775, 0.01297235], "split_indices": [71, 2, 125, 1, 7, 113, 0, 23, 0, 1, 0, 1, 109, 83, 126, 0, 0, 1, 93, 0, 0, 109, 0, 0, 0, 109, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1024.0, 1030.0, 694.0, 330.0, 886.0, 144.0, 581.0, 113.0, 229.0, 101.0, 630.0, 256.0, 343.0, 238.0, 139.0, 90.0, 392.0, 238.0, 120.0, 136.0, 217.0, 126.0, 118.0, 120.0, 240.0, 152.0, 106.0, 132.0, 92.0, 125.0, 131.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002802762, 0.12081701, -0.20579116, 0.07372065, 0.044388656, -0.24458483, -0.098175995, -0.10982406, 0.14016117, -0.26536885, -0.014663244, -0.022521105, 0.0039021813, -0.0036486413, -0.01693831, 0.07597666, 0.30254245, -0.21902755, -0.035603654, -0.055763785, 0.14628375, 0.017838785, 0.048101466, -0.030109162, -0.012989834, 0.009896862, -0.017581483, -0.006219515, 0.20875724, 0.01021346, 0.035958923], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, -1, -1, -1, -1, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [52.043667, 19.61265, 3.2772064, 13.719157, 0.0, 1.1746826, 3.625218, 1.3060071, 8.60887, 1.9999847, 0.0, 0.0, 0.0, 0.0, 0.0, 5.483274, 5.185007, 2.3040028, 0.0, 3.8266122, 5.027421, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.7763844, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 15, 15, 16, 16, 17, 17, 19, 19, 20, 20, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, -1, -1, -1, -1, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.044388656, 1.0, 1.0, 1.0, 1.0, 1.0, -0.014663244, -0.022521105, 0.0039021813, -0.0036486413, -0.01693831, 1.0, 1.0, 1.0, -0.035603654, 1.0, 1.0, 0.017838785, 0.048101466, -0.030109162, -0.012989834, 0.009896862, -0.017581483, -0.006219515, 1.0, 0.01021346, 0.035958923], "split_indices": [137, 125, 93, 17, 0, 113, 13, 53, 113, 80, 0, 0, 0, 0, 0, 81, 61, 13, 0, 106, 5, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1289.0, 785.0, 1125.0, 164.0, 577.0, 208.0, 299.0, 826.0, 476.0, 101.0, 108.0, 100.0, 134.0, 165.0, 592.0, 234.0, 315.0, 161.0, 206.0, 386.0, 138.0, 96.0, 164.0, 151.0, 90.0, 116.0, 89.0, 297.0, 174.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.002142405, 0.10774741, -0.1791639, 0.066922545, 0.03974219, -0.20729284, -0.0014303842, -0.089478865, 0.124301724, -0.24032685, -0.12252301, -0.14027739, -0.0010458918, 0.18508287, -0.04425341, -0.015009779, -0.28727987, -0.0037471862, -0.020847892, -0.021122525, -0.0070871855, 0.09212265, 0.04683603, -0.013213682, 0.008357696, -0.024728462, -0.03346715, 0.0023484933, 0.034546833, -0.013656827, 0.08371404, -0.00606399, 0.025845831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, 23, -1, 25, -1, -1, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [40.07289, 15.030744, 3.6588573, 9.997242, 0.0, 1.887392, 0.0, 1.2002144, 8.349654, 2.054716, 1.381712, 0.8961992, 0.0, 15.773788, 2.4265807, 0.0, 0.60464287, 0.0, 0.0, 0.0, 0.0, 10.257496, 0.0, 0.0, 0.0, 0.0, 0.0, 3.7639115, 0.0, 0.0, 5.2972555, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 16, 16, 21, 21, 27, 27, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, 24, -1, 26, -1, -1, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 0.03974219, 1.0, -0.0014303842, 1.0, 0.15384616, 0.0, 1.0, 1.0, -0.0010458918, -0.115384616, 0.7307692, -0.015009779, 1.0, -0.0037471862, -0.020847892, -0.021122525, -0.0070871855, 1.0, 0.04683603, -0.013213682, 0.008357696, -0.024728462, -0.03346715, 1.0, 0.034546833, -0.013656827, 1.0, -0.00606399, 0.025845831], "split_indices": [137, 125, 1, 17, 0, 93, 0, 124, 1, 1, 12, 93, 0, 1, 1, 0, 23, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 5, 0, 0, 126, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1271.0, 789.0, 1114.0, 157.0, 674.0, 115.0, 299.0, 815.0, 485.0, 189.0, 182.0, 117.0, 599.0, 216.0, 166.0, 319.0, 95.0, 94.0, 90.0, 92.0, 451.0, 148.0, 128.0, 88.0, 173.0, 146.0, 333.0, 118.0, 123.0, 210.0, 115.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0032444545, 0.107432365, -0.16432224, 0.072740234, 0.034620844, -0.18867566, -0.0020531176, 0.13113041, -0.07232284, -0.027995298, -0.16257173, 0.09848793, 0.03836612, -0.1736508, 0.013224492, -0.13299398, -0.02991328, 0.21019284, -0.04849221, -0.0096412385, -0.02568883, -0.07981073, -0.20948221, 0.0026028592, 0.3230677, -0.12072576, 0.009886419, 0.002452886, -0.014560451, -0.018444745, -0.023508592, 0.012111942, 0.04641548, -0.024202174, -0.002108976], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, 15, 17, -1, 19, -1, 21, -1, 23, 25, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [36.156395, 10.578222, 2.7804337, 9.444338, 0.0, 1.6178474, 0.0, 6.5533667, 6.6330986, 0.0, 2.13268, 11.558558, 0.0, 1.375834, 0.0, 1.7654653, 0.0, 8.315004, 3.2357993, 0.0, 0.0, 1.7574135, 0.11409378, 0.0, 7.0660915, 2.4654298, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 10, 10, 11, 11, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, 16, 18, -1, 20, -1, 22, -1, 24, 26, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 0.1923077, 0.034620844, 1.0, -0.0020531176, 4.0, 1.2692307, -0.027995298, 0.96153843, 1.0, 0.03836612, 0.5769231, 0.013224492, 1.0, -0.02991328, 1.0, -0.1923077, -0.0096412385, -0.02568883, 1.0, 1.0, 0.0026028592, 1.0, 1.0, 0.009886419, 0.002452886, -0.014560451, -0.018444745, -0.023508592, 0.012111942, 0.04641548, -0.024202174, -0.002108976], "split_indices": [137, 125, 1, 1, 0, 26, 0, 0, 1, 0, 1, 122, 0, 1, 0, 80, 0, 108, 1, 0, 0, 17, 106, 0, 50, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1277.0, 794.0, 1115.0, 162.0, 679.0, 115.0, 795.0, 320.0, 151.0, 528.0, 704.0, 91.0, 214.0, 106.0, 434.0, 94.0, 400.0, 304.0, 111.0, 103.0, 256.0, 178.0, 152.0, 248.0, 204.0, 100.0, 99.0, 157.0, 90.0, 88.0, 102.0, 146.0, 92.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0028979774, -0.1255496, 0.12986632, -0.21212524, -0.059953034, 0.0895966, 0.037700098, -0.270621, -0.15310006, 0.03390448, -0.13276373, -0.0039638844, 0.19282061, -0.02065129, -0.034266632, -0.007714934, -0.021820066, 0.012003682, -0.011453631, -0.0050663273, -0.02046016, -0.11834517, 0.164598, 0.008259773, 0.25845894, -0.055871654, -0.023808606, 0.020741781, 0.012606013, 0.010982178, 0.044331856, 0.0011863988, -0.011932928], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [33.79178, 5.849436, 10.370028, 1.5330086, 4.0046253, 8.65329, 0.0, 1.0299644, 1.0927196, 3.2731016, 1.9463134, 9.061752, 3.0820427, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0945766, 0.31353426, 0.0, 7.336359, 0.79089546, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 21, 21, 22, 22, 24, 24, 25, 25], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.037700098, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.02065129, -0.034266632, -0.007714934, -0.021820066, 0.012003682, -0.011453631, -0.0050663273, -0.02046016, 1.0, 1.0, 0.008259773, 1.0, 1.0, -0.023808606, 0.020741781, 0.012606013, 0.010982178, 0.044331856, 0.0011863988, -0.011932928], "split_indices": [71, 17, 125, 111, 106, 39, 0, 109, 115, 80, 137, 42, 93, 0, 0, 0, 0, 0, 0, 0, 0, 106, 105, 0, 111, 53, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1030.0, 1042.0, 444.0, 586.0, 896.0, 146.0, 223.0, 221.0, 256.0, 330.0, 470.0, 426.0, 118.0, 105.0, 102.0, 119.0, 162.0, 94.0, 154.0, 176.0, 280.0, 190.0, 159.0, 267.0, 184.0, 96.0, 90.0, 100.0, 148.0, 119.0, 89.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.002130581, 0.073000826, -0.12325498, 0.049349353, 0.036603004, -0.14551164, 0.0005871992, 0.14565846, -0.036293097, -0.081657656, -0.17844532, 0.2184469, -0.0048604673, 0.0071681757, -0.014242507, -0.018392088, 0.0023320492, -0.2150572, -0.008181398, 0.27697325, 0.004798181, -0.06790624, 0.016577613, -0.02841107, -0.18908834, 0.3815441, 0.006466263, 0.029736001, -0.026916886, -0.013529613, -0.02401909, 0.048862323, 0.028364316, -0.0074720155, 0.016505424], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, 21, -1, -1, -1, 23, -1, 25, -1, 27, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [18.755608, 8.815685, 2.2675352, 9.708072, 0.0, 1.4152832, 0.0, 7.83362, 2.8736691, 2.4584079, 1.5708055, 4.020611, 0.0, 5.26307, 0.0, 0.0, 0.0, 0.5774231, 0.0, 6.66045, 0.0, 5.8955207, 0.0, 0.0, 0.64324665, 2.107111, 0.0, 2.8552344, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 19, 19, 21, 21, 24, 24, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, 22, -1, -1, -1, 24, -1, 26, -1, 28, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 2.7307692, 1.0, 0.036603004, 0.0, 0.0005871992, 0.23076923, 1.0, 1.0, 1.0, 1.0, -0.0048604673, 1.0, -0.014242507, -0.018392088, 0.0023320492, 0.34615386, -0.008181398, 1.0, 0.004798181, 0.1923077, 0.016577613, -0.02841107, 1.0, -0.30769232, 0.006466263, 1.0, -0.026916886, -0.013529613, -0.02401909, 0.048862323, 0.028364316, -0.0074720155, 0.016505424], "split_indices": [137, 102, 1, 53, 0, 1, 0, 1, 7, 109, 93, 106, 0, 0, 0, 0, 0, 1, 0, 97, 0, 1, 0, 0, 23, 1, 0, 126, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1272.0, 789.0, 1177.0, 95.0, 673.0, 116.0, 554.0, 623.0, 229.0, 444.0, 403.0, 151.0, 442.0, 181.0, 116.0, 113.0, 322.0, 122.0, 300.0, 103.0, 300.0, 142.0, 88.0, 234.0, 201.0, 99.0, 202.0, 98.0, 114.0, 120.0, 96.0, 105.0, 114.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.00049325026, -0.099153414, 0.097785614, -0.065250695, -0.22558875, 0.053703934, 0.21961133, -0.0401787, -0.025702475, -0.02702002, -0.018778242, 0.09166514, -0.049069084, 0.0038928643, 0.03615763, 0.027810426, -0.08969963, 0.020676741, 0.19049811, 0.0080799535, -0.017767688, 0.015136038, -0.0065030563, -0.11850898, 0.00076455873, -0.011653139, 0.11920387, 0.0021620868, 0.032300178, -0.019632863, -0.08067996, -0.0018796582, 0.024064431, 0.0017204443, -0.0147975525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, 33, -1, -1, -1, -1], "loss_changes": [20.032366, 4.419383, 5.558241, 3.9090314, 0.36767673, 2.9650548, 7.053918, 2.4207907, 0.0, 0.0, 0.0, 3.893876, 3.4239342, 0.0, 0.0, 3.4755616, 1.1666524, 4.3665476, 5.191429, 0.0, 0.0, 0.0, 0.0, 0.94497156, 0.0, 0.0, 3.1506612, 0.0, 0.0, 0.0, 1.4228326, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 23, 23, 26, 26, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, 34, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.34615386, 0.46153846, 1.0, 1.0, -0.025702475, -0.02702002, -0.018778242, -0.1923077, 1.0, 0.0038928643, 0.03615763, 0.115384616, 1.1923077, 1.0, 1.0, 0.0080799535, -0.017767688, 0.015136038, -0.0065030563, 1.0, 0.00076455873, -0.011653139, -0.46153846, 0.0021620868, 0.032300178, -0.019632863, -0.03846154, -0.0018796582, 0.024064431, 0.0017204443, -0.0147975525], "split_indices": [71, 116, 113, 40, 1, 1, 109, 122, 0, 0, 0, 1, 124, 0, 0, 1, 1, 109, 17, 0, 0, 0, 0, 5, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1031.0, 1035.0, 813.0, 218.0, 760.0, 275.0, 719.0, 94.0, 100.0, 118.0, 555.0, 205.0, 121.0, 154.0, 303.0, 416.0, 323.0, 232.0, 102.0, 103.0, 130.0, 173.0, 321.0, 95.0, 135.0, 188.0, 102.0, 130.0, 105.0, 216.0, 88.0, 100.0, 88.0, 128.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0015566568, -0.083787546, 0.086324215, -0.15485357, -0.030909605, 0.01727101, 0.16447084, -0.1959589, -0.0027311742, -0.0005061931, -0.0163579, 0.09333669, -0.024958232, 0.07643032, 0.2811561, -0.25665283, -0.007833081, 0.0968266, -0.09386623, -0.0939335, 0.23056707, 0.17893903, -0.014373039, 0.04271139, 0.009553584, -0.029851308, -0.02059131, 0.021432936, -0.005638777, 0.0019320641, -0.019903202, -0.006265746, -0.012352799, 0.004398929, 0.033566467, 0.026510436, 0.009368066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, 27, 29, 31, 33, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [14.939076, 3.8668008, 5.5905404, 2.3015242, 2.379825, 11.164107, 4.992692, 2.3702488, 0.0, 4.361757, 0.0, 10.999242, 0.0, 6.251444, 5.6623745, 0.46515083, 0.0, 4.230731, 2.9163296, 0.16753256, 4.8433905, 1.3884525, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, 28, 30, 32, 34, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.3846154, 1.0, 0.46153846, -0.07692308, 1.0, -0.0027311742, 0.1923077, -0.0163579, 1.0, -0.024958232, -0.30769232, 1.0, 1.0, -0.007833081, 1.0, 1.0, 1.0, -0.46153846, 1.0, -0.014373039, 0.04271139, 0.009553584, -0.029851308, -0.02059131, 0.021432936, -0.005638777, 0.0019320641, -0.019903202, -0.006265746, -0.012352799, 0.004398929, 0.033566467, 0.026510436, 0.009368066], "split_indices": [71, 17, 39, 1, 7, 1, 1, 83, 0, 1, 0, 109, 0, 1, 109, 106, 0, 80, 12, 121, 1, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1029.0, 1036.0, 439.0, 590.0, 550.0, 486.0, 332.0, 107.0, 480.0, 110.0, 428.0, 122.0, 277.0, 209.0, 219.0, 113.0, 235.0, 245.0, 181.0, 247.0, 189.0, 88.0, 117.0, 92.0, 120.0, 99.0, 133.0, 102.0, 118.0, 127.0, 88.0, 93.0, 89.0, 158.0, 94.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00288691, -0.07770723, 0.08301474, -0.03589619, -0.15732487, 0.029119065, 0.16543569, 0.01540929, -0.13408427, -0.21062331, -0.006507753, -0.014751446, 0.019363351, 0.24966063, -0.0033390392, 0.15606686, -0.0804936, -0.005409288, -0.018570991, -0.025192931, -0.017382968, -0.08322899, 0.071549006, 0.044187944, 0.1535512, 0.026266167, 0.004462682, -0.01276865, -0.00018387321, -0.19157259, 0.011355833, 0.0139696915, -0.0006620618, 0.022270508, 0.008582315, -0.01582484, -0.022415621], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, -1, 23, -1, 25, 27, -1, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [13.354801, 3.4320912, 4.606491, 3.4054096, 1.7454062, 4.5252686, 6.8659086, 5.989324, 0.9580679, 0.34195328, 0.0, 2.9252737, 0.0, 5.3205204, 0.0, 2.138207, 0.97995496, 0.0, 0.0, 0.0, 0.0, 5.8844986, 1.1666344, 0.0, 0.899261, 0.0, 0.0, 0.0, 0.0, 0.19327497, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 15, 15, 16, 16, 21, 21, 22, 22, 24, 24, 29, 29], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, -1, 24, -1, 26, 28, -1, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.2692307, 2.0, 1.0, 1.0, -0.1923077, 1.0, -0.006507753, 1.0, 0.019363351, -0.30769232, -0.0033390392, 0.23076923, 1.0, -0.005409288, -0.018570991, -0.025192931, -0.017382968, 1.0, 1.0, 0.044187944, 0.115384616, 0.026266167, 0.004462682, -0.01276865, -0.00018387321, -0.26923078, 0.011355833, 0.0139696915, -0.0006620618, 0.022270508, 0.008582315, -0.01582484, -0.022415621], "split_indices": [71, 23, 50, 80, 1, 0, 97, 122, 1, 26, 0, 39, 0, 1, 0, 1, 62, 0, 0, 0, 0, 116, 12, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1031.0, 1037.0, 676.0, 355.0, 627.0, 410.0, 444.0, 232.0, 225.0, 130.0, 495.0, 132.0, 288.0, 122.0, 180.0, 264.0, 91.0, 141.0, 106.0, 119.0, 276.0, 219.0, 96.0, 192.0, 92.0, 88.0, 165.0, 99.0, 178.0, 98.0, 117.0, 102.0, 95.0, 97.0, 88.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027314946, -0.07597666, 0.06980667, -0.04392232, -0.13761961, 0.039086536, 0.025559032, -0.003818826, -0.1220278, -0.09657585, -0.019261826, 0.10356329, -0.02881507, 0.10107776, -0.08096068, -0.016104601, -0.007344627, -0.015164173, -0.003870045, 0.06298096, 0.023432856, 0.009729381, -0.0943032, -9.787722e-05, 0.019385645, 8.374195e-05, -0.012813262, -0.021770552, 0.016729053, -0.0036515412, -0.015942225, -0.0074805818, 0.0032381215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [10.955551, 2.0272985, 5.9127603, 2.1143038, 0.79232883, 3.8921084, 0.0, 3.6089952, 0.4340837, 0.64057827, 0.0, 2.4198818, 3.575989, 1.7895797, 0.9916538, 0.0, 0.0, 0.0, 0.0, 3.0764573, 0.0, 0.0, 1.072479, 0.0, 0.0, 0.0, 0.0, 0.5514152, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 19, 19, 22, 22, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.025559032, 1.0, 1.0, 1.0, -0.019261826, 2.0, 1.0, 1.0, 1.0, -0.016104601, -0.007344627, -0.015164173, -0.003870045, 1.0, 0.023432856, 0.009729381, 1.0, -9.787722e-05, 0.019385645, 8.374195e-05, -0.012813262, 1.0, 0.016729053, -0.0036515412, -0.015942225, -0.0074805818, 0.0032381215], "split_indices": [71, 23, 125, 80, 115, 121, 0, 106, 12, 126, 0, 0, 69, 13, 126, 0, 0, 0, 0, 50, 0, 0, 15, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1026.0, 1036.0, 675.0, 351.0, 889.0, 147.0, 446.0, 229.0, 201.0, 150.0, 456.0, 433.0, 189.0, 257.0, 127.0, 102.0, 103.0, 98.0, 348.0, 108.0, 148.0, 285.0, 90.0, 99.0, 94.0, 163.0, 192.0, 156.0, 151.0, 134.0, 97.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0049433643, -0.068888, 0.059375577, -0.04183338, -0.17217594, -0.012002509, 0.14010257, -0.020698082, -0.020637048, -0.013199608, -0.022970617, 0.053496078, -0.024592606, 0.060044408, 0.24429308, -0.037145633, 0.009817282, -0.089035355, 0.15922637, 0.15249039, -0.013004111, 0.03750886, 0.008115023, 0.006813637, -0.098292, -0.0050038346, -0.012593572, -0.0035915484, 0.027127558, 0.020979946, 0.009822963, 0.07373763, -0.015343553, -0.016248893, -0.00043742373, -0.0012907196, 0.018204369], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, -1, 19, 21, 23, -1, 25, 27, 29, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [8.456022, 2.8810425, 5.906192, 2.8411498, 0.49467182, 8.334985, 4.012164, 1.4155176, 0.0, 0.0, 0.0, 6.404704, 0.0, 4.779759, 4.459715, 1.7095363, 0.0, 0.26045918, 5.3351817, 0.5690627, 0.0, 0.0, 0.0, 3.96807, 1.6037757, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.449266, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 17, 17, 18, 18, 19, 19, 23, 23, 24, 24, 31, 31], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, -1, 20, 22, 24, -1, 26, 28, 30, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.46153846, -0.07692308, 2.2692308, -0.020637048, -0.013199608, -0.022970617, 1.0, -0.024592606, -0.30769232, 1.0, 0.23076923, 0.009817282, 1.0, -0.46153846, 1.0, -0.013004111, 0.03750886, 0.008115023, 1.0, 1.0, -0.0050038346, -0.012593572, -0.0035915484, 0.027127558, 0.020979946, 0.009822963, 1.0, -0.015343553, -0.016248893, -0.00043742373, -0.0012907196, 0.018204369], "split_indices": [71, 116, 39, 40, 109, 1, 1, 1, 0, 0, 0, 109, 0, 1, 109, 1, 0, 121, 1, 121, 0, 0, 0, 7, 81, 0, 0, 0, 0, 0, 0, 127, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2056.0, 1031.0, 1025.0, 817.0, 214.0, 544.0, 481.0, 724.0, 93.0, 126.0, 88.0, 425.0, 119.0, 272.0, 209.0, 636.0, 88.0, 181.0, 244.0, 183.0, 89.0, 116.0, 93.0, 370.0, 266.0, 88.0, 93.0, 89.0, 155.0, 89.0, 94.0, 261.0, 109.0, 158.0, 108.0, 145.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.00095654716, 0.052895717, -0.08741973, 0.033233102, 0.030466262, -0.07036933, -0.020259349, 0.103593625, -0.029234404, -0.09908612, 0.008445754, 0.1699125, -0.0046209, -0.016806087, 0.002802469, -0.065476514, -0.024603873, -0.009473531, 0.011858287, 0.10524685, 0.037225336, -0.010789576, 0.05200169, 0.00083656976, -0.108812496, -0.0049580387, 0.028415835, -0.0064882278, 0.13235943, -0.003412577, -0.015973525, 0.004019111, 0.02193594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [9.596508, 6.287001, 1.5533314, 5.1776, 0.0, 1.5594254, 0.0, 5.503847, 2.7752814, 2.494204, 2.090988, 5.0244474, 0.0, 0.0, 2.761257, 1.3152105, 0.0, 0.0, 0.0, 8.06081, 0.0, 0.0, 3.2967784, 0.0, 0.9850421, 0.0, 0.0, 0.0, 1.6678767, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 14, 14, 15, 15, 19, 19, 22, 22, 24, 24, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 0.030466262, 1.0, -0.020259349, 0.15384616, -1.0, 1.0, 1.0, -0.115384616, -0.0046209, -0.016806087, 1.0, 1.0, -0.024603873, -0.009473531, 0.011858287, 1.0, 0.037225336, -0.010789576, 1.0, 0.00083656976, 0.07692308, -0.0049580387, 0.028415835, -0.0064882278, 1.0, -0.003412577, -0.015973525, 0.004019111, 0.02193594], "split_indices": [137, 102, 40, 53, 0, 93, 0, 1, 0, 0, 13, 1, 0, 0, 17, 53, 0, 0, 0, 61, 0, 0, 81, 0, 1, 0, 0, 0, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1270.0, 791.0, 1178.0, 92.0, 689.0, 102.0, 554.0, 624.0, 505.0, 184.0, 384.0, 170.0, 117.0, 507.0, 411.0, 94.0, 95.0, 89.0, 291.0, 93.0, 156.0, 351.0, 152.0, 259.0, 156.0, 135.0, 143.0, 208.0, 105.0, 154.0, 101.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00024317103, 0.044653613, -0.07101489, 0.02108143, 0.020090751, -0.0930408, 0.0024332162, -0.0014379134, 0.02405904, 0.0051166597, -0.12400059, 0.04106897, -0.061028913, -0.18075892, -0.063942336, 0.13428932, -0.08777215, -0.120928876, 0.015727313, -0.025741935, -0.010793149, 0.0015355883, -0.015950687, 0.05063421, 0.023532731, -0.017036527, -0.003756847, -0.20344779, 0.0023999286, -0.0019237355, 0.012516391, -0.029028473, -0.011906849], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, -1, 13, 15, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [6.5443964, 4.6924543, 1.6674838, 5.472121, 0.0, 2.8796864, 0.0, 2.54316, 0.0, 0.0, 1.8100758, 7.038221, 5.4658866, 1.5241537, 1.9551495, 2.8737965, 1.0200332, 3.922655, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9685966, 0.0, 0.0, 0.0, 1.5313921, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 23, 23, 27, 27], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, -1, 14, 16, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 5.0, 0.020090751, -1.0, 0.0024332162, -0.03846154, 0.02405904, 0.0051166597, 1.0, 1.0, 1.2692307, 1.0, 1.0, 1.0, -0.3846154, 1.0, 0.015727313, -0.025741935, -0.010793149, 0.0015355883, -0.015950687, -0.3846154, 0.023532731, -0.017036527, -0.003756847, 1.0, 0.0023999286, -0.0019237355, 0.012516391, -0.029028473, -0.011906849], "split_indices": [137, 125, 113, 0, 0, 0, 0, 1, 0, 0, 115, 122, 1, 23, 23, 50, 1, 124, 0, 0, 0, 0, 0, 1, 0, 0, 0, 122, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1274.0, 794.0, 1107.0, 167.0, 645.0, 149.0, 1004.0, 103.0, 114.0, 531.0, 586.0, 418.0, 273.0, 258.0, 340.0, 246.0, 328.0, 90.0, 133.0, 140.0, 141.0, 117.0, 186.0, 154.0, 93.0, 153.0, 209.0, 119.0, 96.0, 90.0, 103.0, 106.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011236042, 0.041560356, -0.07051212, -0.05196088, 0.0791121, -0.09165781, 0.002247094, -0.10281837, 0.009787728, 0.14642186, 0.020868544, 0.004072477, -0.120852455, -0.16624393, 0.0012916874, 0.21967927, 0.0012604984, -0.008685495, 0.06931225, -0.1579559, -0.004643353, -0.021750692, -0.011439836, 0.0071050846, 0.0337222, -0.002489123, 0.016022958, -0.13090836, -0.022490524, -0.0044239266, -0.01796597], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, -1, 19, 21, -1, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [6.1279163, 4.4987254, 1.5493584, 2.7966845, 3.583209, 2.4812422, 0.0, 2.011317, 0.0, 4.1565046, 2.5570774, 0.0, 1.4523892, 0.47042227, 0.0, 4.786831, 0.0, 0.0, 2.894879, 0.6355953, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0563083, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 12, 12, 13, 13, 15, 15, 18, 18, 19, 19, 27, 27], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, -1, 20, 22, -1, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 0.002247094, 1.0, 0.009787728, 1.0, 1.0, 0.004072477, 1.0, 1.0, 0.0012916874, 1.0, 0.0012604984, -0.008685495, 1.0, 1.0, -0.004643353, -0.021750692, -0.011439836, 0.0071050846, 0.0337222, -0.002489123, 0.016022958, 1.0, -0.022490524, -0.0044239266, -0.01796597], "split_indices": [137, 17, 113, 39, 53, 0, 0, 116, 0, 97, 5, 0, 83, 109, 0, 109, 0, 0, 13, 58, 0, 0, 0, 0, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1281.0, 788.0, 367.0, 914.0, 642.0, 146.0, 274.0, 93.0, 424.0, 490.0, 116.0, 526.0, 177.0, 97.0, 274.0, 150.0, 152.0, 338.0, 351.0, 175.0, 89.0, 88.0, 121.0, 153.0, 166.0, 172.0, 250.0, 101.0, 90.0, 160.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0035143548, -0.04357724, 0.050334007, -0.029410267, -0.0147430245, 0.025984809, 0.019926434, -0.044161037, 0.009848202, -0.0061034667, 0.12970144, -0.010696657, -0.12293314, -0.05792088, 0.054625213, -0.004906492, 0.02789325, -0.072087325, 0.042669076, -0.016055292, -0.0085002445, -0.11019004, 0.007838885, 0.013617858, -0.0041653044, -0.013139843, 0.0043898793, 0.011691972, -0.0034551562, -0.024206858, -0.0005043614], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, 29, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4.568371, 1.5198344, 3.767761, 1.7148372, 0.0, 2.9719825, 0.0, 2.1483884, 0.0, 2.1461198, 5.628952, 1.8739626, 0.34674644, 2.6219246, 2.4654703, 0.0, 0.0, 1.8298844, 1.754507, 0.0, 0.0, 3.6885035, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, 30, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 3.1538463, -0.0147430245, 2.0, 0.019926434, 1.0, 0.009848202, 1.0, -0.26923078, 1.0, 0.53846157, 1.0, 1.0, -0.004906492, 0.02789325, 1.0, 0.115384616, -0.016055292, -0.0085002445, 1.0, 0.007838885, 0.013617858, -0.0041653044, -0.013139843, 0.0043898793, 0.011691972, -0.0034551562, -0.024206858, -0.0005043614], "split_indices": [71, 40, 125, 1, 0, 0, 0, 23, 0, 39, 1, 124, 1, 113, 13, 0, 0, 62, 1, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1033.0, 1039.0, 909.0, 124.0, 893.0, 146.0, 815.0, 94.0, 682.0, 211.0, 572.0, 243.0, 368.0, 314.0, 96.0, 115.0, 266.0, 306.0, 122.0, 121.0, 266.0, 102.0, 170.0, 144.0, 176.0, 90.0, 156.0, 150.0, 118.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017778198, 0.007857007, -0.018427832, -0.03480654, 0.049869537, -0.0790166, -0.0004930373, 0.10046403, 0.003892399, -0.16319688, -0.01420834, 0.03072686, -0.013446583, -0.0116583835, 0.2290448, 0.107409775, -0.06400967, -0.021563713, -0.011298811, 0.0061188345, -0.008297014, 0.11638715, -0.034456857, 0.008146063, -0.016103683, 0.0098806685, 0.038852, -0.00015743799, 0.023245478, -0.016476467, -0.015726523, 0.02097557, 0.0029620389, 0.0025491223, -0.01220281, 0.001113352, -0.0053498982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, 17, 19, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.6116734, 3.4969795, 0.0, 1.4684579, 2.2866442, 2.3077087, 2.2795262, 6.7470565, 3.6199574, 0.4844637, 1.2390747, 2.4679763, 0.0, 3.477494, 4.5278034, 2.7800975, 1.3785803, 0.0, 0.0, 0.0, 0.0, 1.5473454, 1.317682, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.21057153, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 21, 21, 22, 22, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, 18, 20, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.018427832, 1.0, 1.0, 0.34615386, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.013446583, 1.0, 1.0, 1.0, 1.0, -0.021563713, -0.011298811, 0.0061188345, -0.008297014, 0.15384616, 0.3846154, 0.008146063, -0.016103683, 0.0098806685, 0.038852, -0.00015743799, 0.023245478, 1.0, -0.015726523, 0.02097557, 0.0029620389, 0.0025491223, -0.01220281, 0.001113352, -0.0053498982], "split_indices": [117, 71, 0, 17, 121, 1, 7, 109, 69, 59, 12, 122, 0, 113, 61, 15, 59, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2054.0, 1951.0, 103.0, 968.0, 983.0, 423.0, 545.0, 468.0, 515.0, 184.0, 239.0, 442.0, 103.0, 250.0, 218.0, 204.0, 311.0, 90.0, 94.0, 114.0, 125.0, 191.0, 251.0, 154.0, 96.0, 120.0, 98.0, 109.0, 95.0, 206.0, 105.0, 92.0, 99.0, 149.0, 102.0, 118.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.004962268, -0.009029593, 0.14118357, -0.019695684, 0.0133293485, 0.02924068, -0.001808342, 0.021596573, -0.053355522, -0.012975912, 0.020589026, 0.047379363, -0.08514482, -0.07244202, 0.015742403, -0.005150793, 0.017466007, -0.023385588, -0.053031128, -0.03386381, -0.022102538, 0.051746376, -0.10555053, 0.0106269745, -0.008941301, -0.006589205, 0.015930153, -0.020821614, -0.06477045, 0.007753121, -0.006844164, -0.016960653, -0.017647155, 0.0028775313, -0.0059605953], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, 17, 19, -1, -1, -1, -1, 21, 23, -1, 25, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [3.9492106, 2.8523796, 4.6483803, 2.4295292, 0.0, 0.0, 0.0, 5.001621, 3.0838065, 6.6979265, 0.0, 2.9074693, 3.4957824, 2.8087187, 0.0, 0.0, 0.0, 0.0, 3.3127172, 0.9613856, 0.0, 2.5431771, 1.6788702, 1.1426455, 0.0, 0.0, 0.0, 0.0, 1.4178429, 0.0, 0.0, 0.0, 0.38567063, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, 18, 20, -1, -1, -1, -1, 22, 24, -1, 26, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 5.0, 1.0, -0.03846154, 0.0133293485, 0.02924068, -0.001808342, -0.15384616, 0.0, 1.0, 0.020589026, 1.0, 0.15384616, 1.0, 0.015742403, -0.005150793, 0.017466007, -0.023385588, 1.0, 1.0, -0.022102538, 1.0, 1.0, 1.0, -0.008941301, -0.006589205, 0.015930153, -0.020821614, 1.0, 0.007753121, -0.006844164, -0.016960653, 1.1923077, 0.0028775313, -0.0059605953], "split_indices": [125, 0, 15, 1, 0, 0, 0, 1, 0, 61, 0, 39, 1, 0, 0, 0, 0, 0, 124, 93, 0, 111, 137, 115, 0, 0, 0, 0, 81, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 1879.0, 193.0, 1748.0, 131.0, 99.0, 94.0, 785.0, 963.0, 661.0, 124.0, 231.0, 732.0, 490.0, 171.0, 130.0, 101.0, 130.0, 602.0, 389.0, 101.0, 201.0, 401.0, 216.0, 173.0, 96.0, 105.0, 114.0, 287.0, 117.0, 99.0, 89.0, 198.0, 94.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [7.177204e-06, 0.015444423, -0.008822301, 0.0025903836, -0.113251336, 0.0111718355, -0.014506362, -0.0069919485, -0.014956753, 0.037243977, -0.031390723, 0.058015183, -0.0067705265, -0.048006926, 0.006775867, -0.00976351, 0.07885026, -0.07713027, -0.002886242, 0.03760794, 0.13833438, -0.0027445285, -0.0133913085, 0.0052350643, -0.0062997, 0.017797245, -0.012855424, 0.028345145, 0.03191641, -0.10359052, 0.009270565, 0.016967386, -0.0134968, -0.020386808, -0.00010338947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, -1, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [2.8240135, 0.0, 2.3347673, 2.237673, 0.30371332, 1.8520844, 0.0, 0.0, 0.0, 2.2562199, 1.0445064, 2.8019395, 0.0, 0.71353734, 0.0, 0.0, 1.8693862, 0.93101335, 0.7072305, 3.1874702, 6.2054667, 0.0, 0.0, 0.0, 0.0, 0.0, 3.1703491, 4.477682, 0.0, 1.830574, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 13, 13, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 26, 26, 27, 27, 29, 29], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, -1, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.015444423, 1.0, 1.0, 1.0, 1.0, -0.014506362, -0.0069919485, -0.014956753, 0.61538464, 2.7307692, -0.5, -0.0067705265, 1.0, 0.006775867, -0.00976351, 1.0, 1.0, 1.0, -0.34615386, -0.115384616, -0.0027445285, -0.0133913085, 0.0052350643, -0.0062997, 0.017797245, 0.03846154, 1.0, 0.03191641, 1.0, 0.009270565, 0.016967386, -0.0134968, -0.020386808, -0.00010338947], "split_indices": [1, 0, 40, 117, 106, 137, 0, 0, 0, 1, 1, 1, 0, 115, 0, 0, 124, 17, 69, 1, 1, 0, 0, 0, 0, 0, 1, 59, 0, 111, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 112.0, 1959.0, 1766.0, 193.0, 1669.0, 97.0, 88.0, 105.0, 1035.0, 634.0, 864.0, 171.0, 543.0, 91.0, 102.0, 762.0, 330.0, 213.0, 450.0, 312.0, 176.0, 154.0, 111.0, 102.0, 119.0, 331.0, 194.0, 118.0, 178.0, 153.0, 104.0, 90.0, 90.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.00067993, -0.01194166, 0.12530154, -0.02330407, 0.013817986, 0.026141075, -0.001370359, 0.008633134, -0.060386058, -0.032925054, 0.0659075, -0.10881698, -0.016638301, 0.0055138143, -0.06293178, 0.18173325, -0.045308262, -0.023201885, -0.05660503, -0.099227056, 0.07327358, -0.10939964, -0.004846942, 0.028544275, 0.00948148, -0.013738527, 0.006378294, 0.008596799, -0.1282898, -0.02001556, -0.0009512761, -0.0031462614, 0.015341955, -0.01792688, -0.0042568254, -0.0037277557, 0.0026870877, -0.015952924, -0.0095985355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, 17, 19, -1, 21, 23, 25, -1, 27, 29, 31, 33, 35, -1, -1, -1, -1, -1, 37, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [3.249659, 3.199972, 3.594778, 2.0654109, 0.0, 0.0, 0.0, 2.2302651, 1.7098265, 1.4348712, 5.075371, 2.4636889, 3.1485014, 0.0, 1.0931263, 1.7397523, 2.0190032, 0.0, 2.7492647, 2.001096, 1.7040199, 1.0506263, 0.18515314, 0.0, 0.0, 0.0, 0.0, 0.0, 0.18064141, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 28, 28], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, 18, 20, -1, 22, 24, 26, -1, 28, 30, 32, 34, 36, -1, -1, -1, -1, -1, 38, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 1.0, 1.0, 0.013817986, 0.026141075, -0.001370359, 1.0, 1.0, 0.0, 0.03846154, 1.0, 1.0, 0.0055138143, 1.0, 1.0, 0.88461536, -0.023201885, -0.23076923, 1.0, 1.0, 1.0, 1.0, 0.028544275, 0.00948148, -0.013738527, 0.006378294, 0.008596799, 0.34615386, -0.02001556, -0.0009512761, -0.0031462614, 0.015341955, -0.01792688, -0.0042568254, -0.0037277557, 0.0026870877, -0.015952924, -0.0095985355], "split_indices": [125, 0, 15, 106, 0, 0, 0, 50, 39, 0, 1, 53, 111, 0, 15, 39, 1, 0, 1, 124, 2, 137, 97, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1876.0, 190.0, 1744.0, 132.0, 96.0, 94.0, 937.0, 807.0, 543.0, 394.0, 383.0, 424.0, 138.0, 405.0, 193.0, 201.0, 114.0, 269.0, 221.0, 203.0, 225.0, 180.0, 88.0, 105.0, 109.0, 92.0, 90.0, 179.0, 104.0, 117.0, 88.0, 115.0, 110.0, 115.0, 89.0, 91.0, 91.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "39", "size_leaf_vector": "1"}}, {"base_weights": [-0.001534346, 0.0066335467, -0.015735874, -0.032707676, 0.046014834, -0.015223922, -0.012610475, 0.09872368, -0.0021731663, -0.06335817, 0.023472233, 0.008626514, 0.20335263, -0.011387033, 0.03812076, -0.020608246, -0.01428929, 0.009938884, -0.02118458, -0.011072151, 0.012797457, 0.027469045, 0.01150996, 0.013139458, -0.027002832, 0.0027145578, -0.008521639, 0.0035464913, -0.00782275, -0.010260495, 0.006034327], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, -1, 23, 25, -1, -1, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [2.6320617, 3.0443902, 0.0, 1.6051711, 2.4942153, 1.5422417, 0.0, 4.421155, 2.3088684, 1.2546377, 1.5560992, 3.5894766, 1.3661833, 0.0, 2.2900214, 0.74046886, 0.0, 0.0, 0.9338899, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4659884, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 15, 15, 18, 18, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, -1, 24, 26, -1, -1, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.015735874, 2.0, 1.0, 1.0, -0.012610475, 1.0, -0.3846154, 1.0, 1.0, -0.07692308, -0.3846154, -0.011387033, -0.03846154, 1.0, -0.01428929, 0.009938884, 1.0, -0.011072151, 0.012797457, 0.027469045, 0.01150996, 0.013139458, 1.0, 0.0027145578, -0.008521639, 0.0035464913, -0.00782275, -0.010260495, 0.006034327], "split_indices": [117, 71, 0, 0, 121, 17, 0, 109, 1, 109, 122, 1, 1, 0, 1, 12, 0, 0, 74, 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1965.0, 103.0, 983.0, 982.0, 828.0, 155.0, 469.0, 513.0, 369.0, 459.0, 252.0, 217.0, 136.0, 377.0, 240.0, 129.0, 170.0, 289.0, 126.0, 126.0, 120.0, 97.0, 155.0, 222.0, 138.0, 102.0, 145.0, 144.0, 119.0, 103.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.00558947, 0.014320395, -0.014037366, 0.040221434, -0.029561529, 0.0263906, 0.020359816, -0.017024366, -0.009653676, 0.008674816, 0.01833678, -0.044949967, 0.008788103, 0.03233658, -0.062589936, 0.029312814, -0.093233906, -0.03275257, 0.13267335, -0.019263845, 0.013120781, -0.0003573477, 0.005945861, -0.0033516665, -0.013177234, -0.079124756, 0.008144758, 0.035421774, -0.012182581, -0.020698244, 0.0041211857, 0.010749399, -0.0129257515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [2.641817, 2.223165, 0.0, 2.7793527, 2.0332925, 3.153624, 0.0, 0.0, 2.189502, 1.7182887, 0.0, 1.6745222, 0.0, 4.996089, 6.40159, 0.18241473, 0.6512983, 2.4572096, 9.6597, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.0773673, 0.0, 0.0, 2.5500255, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 25, 25, 28, 28], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.014037366, 1.0, 1.0, 1.0, 0.020359816, -0.017024366, 1.0, 0.34615386, 0.01833678, 1.0, 0.008788103, -0.1923077, 1.2692307, 1.0, 1.0, 1.0, 1.0, -0.019263845, 0.013120781, -0.0003573477, 0.005945861, -0.0033516665, -0.013177234, 1.0, 0.008144758, 0.035421774, 1.0, -0.020698244, 0.0041211857, 0.010749399, -0.0129257515], "split_indices": [43, 137, 0, 102, 5, 125, 0, 0, 93, 1, 0, 53, 0, 1, 1, 12, 122, 61, 69, 0, 0, 0, 0, 0, 0, 69, 0, 0, 121, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1956.0, 117.0, 1230.0, 726.0, 1134.0, 96.0, 90.0, 636.0, 1019.0, 115.0, 467.0, 169.0, 765.0, 254.0, 184.0, 283.0, 464.0, 301.0, 152.0, 102.0, 88.0, 96.0, 111.0, 172.0, 330.0, 134.0, 119.0, 182.0, 160.0, 170.0, 90.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00010148389, 0.007795605, -0.014921014, 0.019329818, -0.099031724, 0.029066302, -0.012335702, -0.0024438999, -0.017284742, 0.019014632, 0.015545365, -0.022806866, 0.065559775, -0.057537843, 0.011357574, 0.013843201, 0.19094805, -0.008108537, -0.022916736, -0.026003547, 0.010613344, 0.029749507, 0.009760153, -0.048805848, 0.06487377, -0.010728582, 0.019585188, -0.10938909, 0.0032561403, -0.0027078388, 0.01599607, 0.013533291, -0.005757994, -0.006022177, -0.016074158], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, 15, 17, -1, 19, 21, 23, -1, 25, -1, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [2.367721, 2.4150515, 0.0, 2.4576151, 1.0516666, 2.1037881, 0.0, 0.0, 0.0, 2.9860654, 0.0, 3.8272548, 4.7078567, 5.4633913, 0.0, 1.8902177, 2.1085072, 1.4850918, 0.0, 1.3302948, 0.0, 0.0, 0.0, 1.5823671, 1.5650778, 0.0, 2.0542886, 0.46457458, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 23, 23, 24, 24, 26, 26, 27, 27], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, 16, 18, -1, 20, 22, 24, -1, 26, -1, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.014921014, 1.0, 1.0, 5.0, -0.012335702, -0.0024438999, -0.017284742, 1.0, 0.015545365, 1.0, 1.0, 1.0, 0.011357574, 1.0, 1.0, 1.0, -0.022916736, 1.0, 0.010613344, 0.029749507, 0.009760153, 1.0, 1.0, -0.010728582, -0.26923078, 1.0, 0.0032561403, -0.0027078388, 0.01599607, 0.013533291, -0.005757994, -0.006022177, -0.016074158], "split_indices": [117, 40, 0, 43, 108, 0, 0, 0, 0, 109, 0, 105, 113, 113, 0, 42, 15, 74, 0, 2, 0, 0, 0, 13, 124, 0, 1, 108, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1960.0, 101.0, 1769.0, 191.0, 1656.0, 113.0, 95.0, 96.0, 1534.0, 122.0, 808.0, 726.0, 644.0, 164.0, 514.0, 212.0, 500.0, 144.0, 359.0, 155.0, 99.0, 113.0, 321.0, 179.0, 129.0, 230.0, 184.0, 137.0, 91.0, 88.0, 92.0, 138.0, 94.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0007207401, 0.012911566, -0.006408675, 0.0031066136, -0.09317439, -0.011730714, 0.013375027, -0.006434434, -0.012321832, -0.003721158, 0.09204099, -0.05892524, 0.024409894, -0.010205457, 0.1832071, 0.0039794883, -0.11044586, -0.056929663, 0.075695686, 0.03060448, 0.0074407966, -0.05797096, -0.02022769, 0.004021735, -0.015931217, 0.02559769, 0.013221995, -0.0018838824, -0.009669115, -0.0105002215, 0.082302034, 0.019160068, -0.0010364192], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, -1, 3, 5, 7, -1, 9, -1, -1, 11, 13, 15, 17, -1, 19, -1, 21, 23, 25, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [1.896669, 0.0, 1.6206548, 2.1872942, 0.16803598, 0.0, 2.1921678, 0.0, 0.0, 2.0793989, 5.1492267, 2.298927, 3.700177, 0.0, 2.6461964, 0.0, 1.4311898, 3.4115307, 6.126981, 0.0, 0.0, 0.28637308, 0.0, 0.0, 0.0, 0.0, 3.2994409, 0.0, 0.0, 0.0, 2.5827155, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 6, 6, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18, 21, 21, 26, 26, 30, 30], "right_children": [2, -1, 4, 6, 8, -1, 10, -1, -1, 12, 14, 16, 18, -1, 20, -1, 22, 24, 26, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.012911566, 1.0, -0.5, 1.0, -0.011730714, 1.0, -0.006434434, -0.012321832, 1.0, 1.0, -0.07692308, -0.07692308, -0.010205457, -0.03846154, 0.0039794883, 1.0, -0.30769232, 0.1923077, 0.03060448, 0.0074407966, 0.8076923, -0.02022769, 0.004021735, -0.015931217, 0.02559769, 1.0, -0.0018838824, -0.009669115, -0.0105002215, 1.0, 0.019160068, -0.0010364192], "split_indices": [1, 0, 40, 1, 2, 0, 42, 0, 0, 39, 109, 1, 1, 0, 1, 0, 127, 1, 1, 0, 0, 1, 0, 0, 0, 0, 69, 0, 0, 0, 12, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 109.0, 1963.0, 1769.0, 194.0, 139.0, 1630.0, 99.0, 95.0, 1339.0, 291.0, 452.0, 887.0, 93.0, 198.0, 155.0, 297.0, 343.0, 544.0, 93.0, 105.0, 189.0, 108.0, 176.0, 167.0, 140.0, 404.0, 94.0, 95.0, 149.0, 255.0, 117.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00035647606, -0.03021848, 0.02936014, -0.050165977, 0.03304357, 0.075506076, -0.010817921, -0.084843725, -0.015931675, 0.010623731, -0.0022773214, 0.023889588, 0.036037885, 0.06825654, -0.062109474, -0.12610006, -0.0001686388, 0.009598092, -0.0523507, 0.159889, -0.07384961, -0.0049687424, 0.019527316, -0.10413857, 0.004798844, -0.0058346693, -0.01756646, -0.009933182, -0.00030857502, 0.020730248, 0.011402157, -0.018437566, 0.0020599903, -0.0009865855, -0.019609308], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, -1, -1, 19, 21, 23, 25, -1, -1, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.818278, 1.289682, 1.9041142, 0.92242944, 1.0009329, 3.0824783, 2.226663, 1.3242755, 1.5936172, 0.0, 0.0, 0.0, 5.2397304, 3.2358625, 1.5408963, 0.86640596, 0.0, 0.0, 0.68278384, 0.3936262, 2.1295838, 0.0, 0.0, 2.0891805, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 20, 20, 23, 23], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, -1, -1, 20, 22, 24, 26, -1, -1, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.1538461, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.010623731, -0.0022773214, 0.023889588, 1.0, 1.0, 1.0, -0.1923077, -0.0001686388, 0.009598092, 1.0, 0.03846154, 1.0, -0.0049687424, 0.019527316, 1.0, 0.004798844, -0.0058346693, -0.01756646, -0.009933182, -0.00030857502, 0.020730248, 0.011402157, -0.018437566, 0.0020599903, -0.0009865855, -0.019609308], "split_indices": [71, 1, 121, 124, 23, 124, 69, 62, 3, 0, 0, 0, 15, 15, 116, 1, 0, 0, 109, 1, 109, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2049.0, 1022.0, 1027.0, 777.0, 245.0, 478.0, 549.0, 386.0, 391.0, 106.0, 139.0, 93.0, 385.0, 216.0, 333.0, 258.0, 128.0, 96.0, 295.0, 181.0, 204.0, 112.0, 104.0, 241.0, 92.0, 109.0, 149.0, 151.0, 144.0, 89.0, 92.0, 94.0, 110.0, 119.0, 122.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014585344, -0.009670191, 0.011162769, -0.0007979971, -0.01376427, -0.011247251, 0.01699066, -0.037724786, 0.018789444, -0.07873078, 0.034505952, -0.05952058, 0.04795318, -0.033585522, -0.15509798, -0.010050745, 0.1489241, -0.013239558, 0.0012017238, 0.1766528, -0.0108993985, 0.0052716453, -0.073256604, -0.020734264, -0.009088055, 0.024621267, 0.0050529963, 0.023119569, 0.012210987, -0.102723375, 0.076423384, 0.0034160544, -0.015477178, -0.002407054, -0.0188142, 0.014272066, 0.00018389215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, -1, 27, 29, -1, 31, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1, -1, -1], "loss_changes": [1.9203968, 2.189045, 0.0, 3.2160752, 0.0, 1.3512106, 0.0, 2.6745899, 1.8179152, 1.9858263, 5.051491, 1.1260774, 4.3930974, 1.239377, 0.71797276, 0.0, 1.6943533, 0.0, 0.0, 0.5414357, 3.1912935, 0.0, 2.1715198, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3033724, 1.008728, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 19, 19, 20, 20, 22, 22, 29, 29, 30, 30], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, -1, 28, 30, -1, 32, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1, -1, -1], "split_conditions": [5.0, 3.0, 0.011162769, 1.0, -0.01376427, 1.0, 0.01699066, 1.0, -0.1923077, 1.0, 1.0, 1.0, 0.1923077, 0.0, 1.0, -0.010050745, -0.30769232, -0.013239558, 0.0012017238, 1.0, 1.1538461, 0.0052716453, 1.0, -0.020734264, -0.009088055, 0.024621267, 0.0050529963, 0.023119569, 0.012210987, 0.5, 1.0, 0.0034160544, -0.015477178, -0.002407054, -0.0188142, 0.014272066, 0.00018389215], "split_indices": [0, 0, 0, 102, 0, 111, 0, 61, 1, 15, 17, 69, 1, 0, 58, 0, 1, 0, 0, 13, 1, 0, 69, 0, 0, 0, 0, 0, 0, 1, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1928.0, 140.0, 1803.0, 125.0, 1699.0, 104.0, 903.0, 796.0, 576.0, 327.0, 216.0, 580.0, 362.0, 214.0, 150.0, 177.0, 107.0, 109.0, 182.0, 398.0, 114.0, 248.0, 118.0, 96.0, 89.0, 88.0, 91.0, 91.0, 194.0, 204.0, 107.0, 141.0, 101.0, 93.0, 108.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00059854885, 0.025084224, -0.0385847, 0.014388636, 0.014489533, -0.059248794, 0.018782744, 0.029205052, -0.016694408, -0.03952817, -0.015449938, -0.0064948844, 0.008533864, -0.05112696, 0.054899488, -0.0047849687, -0.07384243, -0.016134359, 0.0070644612, 0.024854563, 0.027249753, -0.0060782586, 0.0060293316, -0.0032389106, -0.013114552, 0.005296361, 0.015608375, 0.024825472, -0.011365364, -0.042792752, 0.07603797, -0.012939769, 0.0074742497, 0.025331676, -0.02115038, -0.014436918, 0.008249158], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, 15, -1, -1, -1, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, -1, 27, -1, 29, -1, 31, 33, -1, -1, -1, 35, -1, -1], "loss_changes": [1.978341, 1.6261599, 0.9400592, 3.130006, 0.0, 1.0951068, 1.1702948, 2.22302, 0.0, 0.5758263, 0.0, 0.0, 0.0, 3.5029469, 5.334776, 0.8746149, 0.5772226, 0.0, 0.0, 1.8402568, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4495442, 0.0, 1.8561137, 0.0, 2.35138, 5.254978, 0.0, 0.0, 0.0, 2.515816, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 19, 19, 25, 25, 27, 27, 29, 29, 30, 30, 34, 34], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, 16, -1, -1, -1, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, -1, 28, -1, 30, -1, 32, 34, -1, -1, -1, 36, -1, -1], "split_conditions": [1.0, 1.2692307, 1.0, 0.61538464, 0.014489533, 1.0, 0.15384616, 1.0, -0.016694408, 1.0, -0.015449938, -0.0064948844, 0.008533864, -0.34615386, 1.0, 1.0, 0.6923077, -0.016134359, 0.0070644612, 4.0, 0.027249753, -0.0060782586, 0.0060293316, -0.0032389106, -0.013114552, 1.0, 0.015608375, 1.0, -0.011365364, -0.1923077, -0.30769232, -0.012939769, 0.0074742497, 0.025331676, 1.0, -0.014436918, 0.008249158], "split_indices": [137, 1, 93, 1, 0, 0, 1, 89, 0, 106, 0, 0, 0, 1, 125, 12, 1, 0, 0, 0, 0, 0, 0, 0, 0, 119, 0, 69, 0, 1, 1, 0, 0, 0, 59, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1269.0, 793.0, 1165.0, 104.0, 583.0, 210.0, 1077.0, 88.0, 483.0, 100.0, 93.0, 117.0, 261.0, 816.0, 240.0, 243.0, 137.0, 124.0, 717.0, 99.0, 129.0, 111.0, 141.0, 102.0, 624.0, 93.0, 536.0, 88.0, 231.0, 305.0, 133.0, 98.0, 108.0, 197.0, 90.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.00044253445, 0.0110690445, -0.0058010956, -0.011287258, 0.0028803744, -0.011180684, 0.0764231, -0.0475201, 0.014834566, -0.00368705, 0.01666384, 0.004695216, -0.09701254, -0.06239918, 0.06261238, 0.017546153, -0.06996539, -0.016737062, -0.049140025, -0.12009309, 0.0022667737, -0.017599601, 0.1655511, -0.015313788, 0.001556101, 0.0016420054, -0.011076652, -0.018391334, -0.0049624867, -0.0124055315, 0.0077100247, 0.0029859308, 0.0265845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, -1, -1, 15, 17, 19, 21, -1, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.4255675, 0.0, 1.8218946, 0.0, 1.8748028, 1.438867, 2.9742572, 1.6410074, 3.2730827, 0.0, 0.0, 3.939601, 1.0980387, 1.663759, 4.5247917, 0.0, 1.5293908, 0.0, 0.78380656, 0.9084542, 0.0, 3.1050527, 3.2661738, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 14, 14, 16, 16, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, -1, -1, 16, 18, 20, 22, -1, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.0110690445, -0.5, -0.011287258, 1.0, 1.0, 1.0, 1.0, -0.07692308, -0.00368705, 0.01666384, -0.07692308, 0.0, 1.0, 1.0, 0.017546153, 0.88461536, -0.016737062, 0.5, 1.0, 0.0022667737, 1.0, 1.0, -0.015313788, 0.001556101, 0.0016420054, -0.011076652, -0.018391334, -0.0049624867, -0.0124055315, 0.0077100247, 0.0029859308, 0.0265845], "split_indices": [1, 0, 1, 0, 42, 39, 71, 122, 1, 0, 0, 1, 1, 12, 93, 0, 1, 0, 1, 106, 0, 115, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 111.0, 1960.0, 147.0, 1813.0, 1522.0, 291.0, 635.0, 887.0, 129.0, 162.0, 309.0, 326.0, 339.0, 548.0, 94.0, 215.0, 132.0, 194.0, 202.0, 137.0, 308.0, 240.0, 109.0, 106.0, 94.0, 100.0, 106.0, 96.0, 145.0, 163.0, 102.0, 138.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003372195, 0.0016657497, -0.01013378, -0.0070471545, 0.085953645, -0.014982578, 0.009536939, 0.022020286, -0.005124606, -0.0037465554, -0.061752513, -0.02484578, 0.018591879, -0.15553923, 0.005287567, -0.0030137515, -0.016210882, 0.068268456, -0.0022156283, -0.021024935, -0.010082907, -0.049196362, 0.027471952, 0.015917992, -0.0021695984, -0.025196528, 0.008628525, 0.0020956467, -0.011529079, 0.009875963, -0.028132426, -0.0091986945, 0.02711713, -0.0114680845, 0.0053374306, 0.011799067, -0.0052817906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, 17, 19, -1, 21, -1, 23, 25, -1, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, 35, -1, -1, -1, -1], "loss_changes": [1.0191714, 1.4423468, 0.0, 1.4466391, 3.3890872, 0.86813927, 0.0, 0.0, 0.0, 0.62780315, 3.4401925, 2.0527601, 0.668769, 0.52680206, 0.0, 0.8320745, 0.0, 1.562151, 0.9274263, 0.0, 0.0, 1.0896275, 1.4111514, 0.0, 0.0, 1.2648466, 0.0, 0.0, 0.0, 0.0, 1.4108559, 0.0, 1.474588, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 21, 21, 22, 22, 25, 25, 30, 30, 32, 32], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, 18, 20, -1, 22, -1, 24, 26, -1, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, 36, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.01013378, 5.0, 1.0, 1.0, 0.009536939, 0.022020286, -0.005124606, 1.0, 1.0, 1.0, 0.0, 1.0, 0.005287567, 1.0, -0.016210882, 1.0, 1.0, -0.021024935, -0.010082907, 1.0, 1.0, 0.015917992, -0.0021695984, 1.0, 0.008628525, 0.0020956467, -0.011529079, 0.009875963, 0.0, -0.0091986945, 1.0, -0.0114680845, 0.0053374306, 0.011799067, -0.0052817906], "split_indices": [117, 125, 0, 0, 15, 0, 0, 0, 0, 111, 109, 126, 0, 122, 0, 17, 0, 108, 121, 0, 0, 97, 122, 0, 0, 97, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1964.0, 101.0, 1780.0, 184.0, 1652.0, 128.0, 93.0, 91.0, 1332.0, 320.0, 685.0, 647.0, 176.0, 144.0, 591.0, 94.0, 191.0, 456.0, 88.0, 88.0, 235.0, 356.0, 95.0, 96.0, 362.0, 94.0, 114.0, 121.0, 156.0, 200.0, 159.0, 203.0, 97.0, 103.0, 95.0, 108.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016294953, 0.009001706, -0.056335893, 0.017987158, -0.007972254, -0.01821639, 0.0065077073, 0.06447639, -0.021900052, 0.03441086, 0.02821098, -0.005584081, -0.013207025, 0.07750234, -0.061130334, -0.063752785, 0.03159724, 0.17612867, 0.0015441523, -0.01421908, 0.0016719796, 0.0015913151, -0.015599757, 0.021739185, -0.0136934025, 0.006042956, 0.028592473, 0.011308014, -0.0059800628, 0.0054649115, -0.069955185, -0.015817208, 0.002189947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [1.2009933, 1.378406, 5.133123, 2.9112918, 0.0, 0.0, 0.0, 4.743865, 1.5189157, 2.6225362, 0.0, 1.591813, 0.0, 3.288758, 1.2494929, 2.1090968, 3.7782269, 2.4263306, 1.6968536, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3880711, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6044207, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 24, 24, 30, 30], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, -0.007972254, -0.01821639, 0.0065077073, 1.0, 1.0, 0.1923077, 0.02821098, -0.115384616, -0.013207025, 1.0, 1.0, 1.0, 0.115384616, 1.0, -0.30769232, -0.01421908, 0.0016719796, 0.0015913151, -0.015599757, 0.021739185, 1.0769231, 0.006042956, 0.028592473, 0.011308014, -0.0059800628, 0.0054649115, 1.5769231, -0.015817208, 0.002189947], "split_indices": [64, 119, 108, 108, 0, 0, 0, 1, 62, 1, 0, 1, 0, 69, 97, 53, 1, 81, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1729.0, 336.0, 1570.0, 159.0, 165.0, 171.0, 725.0, 845.0, 637.0, 88.0, 736.0, 109.0, 439.0, 198.0, 287.0, 449.0, 191.0, 248.0, 97.0, 101.0, 154.0, 133.0, 88.0, 361.0, 93.0, 98.0, 88.0, 160.0, 163.0, 198.0, 101.0, 97.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00010790693, 0.0052693454, -0.008723258, -0.04337904, 0.01809483, -0.14446326, 0.010426518, 0.063019454, -0.02706377, -0.026892258, 0.0006258069, 0.027517047, 0.021524208, -0.07818624, 0.047251627, 0.091993935, -0.037789118, -0.12150382, -0.020429447, 0.018223407, -0.013365472, 0.019748712, -0.00051369675, 0.012685947, -0.11582164, -0.0047964775, -0.017628793, 0.002525808, -0.0059590214, 0.00549864, -0.010549194, -0.020867575, -0.0036814164], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, 15, -1, 17, 19, 21, 23, 25, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.9291038, 1.2141854, 0.0, 6.059348, 3.1242492, 4.5208406, 0.0, 4.1720953, 2.9177754, 0.0, 0.0, 2.6359224, 0.0, 1.1383569, 2.5610428, 3.2276947, 3.9957108, 1.0474799, 0.348886, 0.0, 1.3601557, 0.0, 0.0, 0.0, 1.5479312, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, 16, -1, 18, 20, 22, 24, 26, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.008723258, 1.0, 1.0, -0.1923077, 0.010426518, 1.0, 1.0, -0.026892258, 0.0006258069, 1.0, 0.021524208, 1.0, 1.0, 1.0, -0.26923078, 1.0, 0.1923077, 0.018223407, 0.15384616, 0.019748712, -0.00051369675, 0.012685947, 0.15384616, -0.0047964775, -0.017628793, 0.002525808, -0.0059590214, 0.00549864, -0.010549194, -0.020867575, -0.0036814164], "split_indices": [43, 89, 0, 97, 97, 1, 0, 42, 115, 0, 0, 13, 0, 111, 122, 124, 1, 124, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1946.0, 115.0, 406.0, 1540.0, 241.0, 165.0, 772.0, 768.0, 132.0, 109.0, 626.0, 146.0, 455.0, 313.0, 315.0, 311.0, 260.0, 195.0, 97.0, 216.0, 151.0, 164.0, 100.0, 211.0, 111.0, 149.0, 90.0, 105.0, 124.0, 92.0, 97.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00049781153, 0.006653163, -0.011790215, 0.012423395, -0.008956932, -0.04493604, 0.027191892, -0.018675458, 0.037200518, 0.011509835, 0.021224014, -0.0056802346, 0.017578209, 2.0023721e-05, 0.015139547, 0.043646656, -0.033953052, 0.01599497, 0.018656544, -0.005658103, -0.02012261, -0.03451503, 0.020017793, 0.0117342295, -0.043197192, -0.0116005605, 0.02146213, -0.10703829, 0.016370006, 0.00844983, -0.0053152083, -0.017476492, -0.001578553, -0.009318377, 0.0114620635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, -1, 23, -1, 25, -1, -1, 27, -1, 29, 31, 33, -1, -1, -1, -1, -1, -1], "loss_changes": [1.5042292, 1.0893533, 0.0, 1.5680054, 0.0, 4.414777, 4.2716513, 0.0, 3.126496, 2.1810508, 0.0, 0.0, 0.0, 1.8585923, 0.0, 2.1696177, 3.336752, 4.279418, 0.0, 2.7842457, 0.0, 1.6467419, 0.0, 0.0, 1.7569101, 0.0, 1.0065266, 1.3781934, 2.5725307, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 8, 8, 9, 9, 13, 13, 15, 15, 16, 16, 17, 17, 19, 19, 21, 21, 24, 24, 26, 26, 27, 27, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, -1, 24, -1, 26, -1, -1, 28, -1, 30, 32, 34, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.011790215, 1.0, -0.008956932, -0.3846154, 1.0, -0.018675458, 1.0, 5.0, 0.021224014, -0.0056802346, 0.017578209, -0.03846154, 0.015139547, -0.15384616, 1.0, 1.0, 0.018656544, 0.0, -0.02012261, 1.0, 0.020017793, 0.0117342295, 1.0, -0.0116005605, 1.0, 1.0, 1.0, 0.00844983, -0.0053152083, -0.017476492, -0.001578553, -0.009318377, 0.0114620635], "split_indices": [117, 43, 0, 89, 0, 1, 125, 0, 97, 0, 0, 0, 0, 1, 0, 1, 64, 61, 0, 0, 0, 106, 0, 0, 122, 0, 71, 109, 111, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1962.0, 102.0, 1851.0, 111.0, 379.0, 1472.0, 139.0, 240.0, 1357.0, 115.0, 143.0, 97.0, 1254.0, 103.0, 549.0, 705.0, 460.0, 89.0, 603.0, 102.0, 361.0, 99.0, 141.0, 462.0, 147.0, 214.0, 223.0, 239.0, 116.0, 98.0, 128.0, 95.0, 113.0, 126.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.003961248, 0.009655122, -0.009364079, 0.017301796, -0.057128377, 0.045029923, -0.0046008527, 0.000119138094, -0.011258936, 0.019015552, 0.01541245, -0.058057442, 0.023943104, 0.05505456, -0.043202333, -0.004068749, -0.020028569, -0.009276126, 0.056177985, 0.111622445, -0.0060343896, -0.010007349, 0.0032798182, -0.008357464, 0.013777696, -0.015308932, 0.17951258, 0.0250352, -0.0034938597, -0.060907725, 0.008980825, 0.022072373, 0.013644919, -0.014746398, 0.0067943037], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, 13, 15, 17, 19, 21, 23, -1, -1, 25, 27, -1, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1], "loss_changes": [1.1392537, 0.9891709, 0.0, 1.0555212, 0.6436602, 3.2967598, 1.4816128, 0.0, 0.0, 0.0, 1.4801421, 2.595406, 2.3813152, 2.4805818, 1.110815, 2.7630043, 0.0, 0.0, 4.3731384, 5.184749, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5050699, 0.32299232, 0.0, 0.0, 2.4424722, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 18, 18, 19, 19, 25, 25, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, 14, 16, 18, 20, 22, 24, -1, -1, 26, 28, -1, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.009364079, 1.0, 1.0, 1.0, 1.0, 0.000119138094, -0.011258936, 0.019015552, 0.15384616, 0.5769231, 1.0, 1.0, 1.0, 1.0, -0.020028569, -0.009276126, 1.0, -0.30769232, -0.0060343896, -0.010007349, 0.0032798182, -0.008357464, 0.013777696, 0.26923078, -0.03846154, 0.0250352, -0.0034938597, 1.0, 0.008980825, 0.022072373, 0.013644919, -0.014746398, 0.0067943037], "split_indices": [43, 40, 0, 53, 108, 81, 81, 0, 0, 0, 1, 1, 5, 97, 23, 124, 0, 0, 115, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 97, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1937.0, 113.0, 1738.0, 199.0, 767.0, 971.0, 97.0, 102.0, 130.0, 637.0, 338.0, 633.0, 380.0, 257.0, 245.0, 93.0, 137.0, 496.0, 255.0, 125.0, 147.0, 110.0, 157.0, 88.0, 314.0, 182.0, 131.0, 124.0, 219.0, 95.0, 93.0, 89.0, 131.0, 88.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.001696195, 0.008571331, -0.061873402, 0.0036089756, 0.010485179, -0.00026992048, -0.011820266, -0.029148342, 0.024250576, -0.116305195, 0.006928876, 0.015354186, 0.0014753069, 0.0020657415, -0.022636447, 0.08516277, -0.08758897, 0.025808306, -0.012725939, 0.022458995, -0.012995342, -0.017708147, -0.0009472618, -0.0068972637, 0.13860537, 0.008869033, -0.041112073, 0.017983092, 0.009737979, -0.020156758, 0.008150576, 0.012403471, -0.051329765, 0.0068125175, -0.013185128], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, 15, -1, 17, -1, -1, 19, 21, 23, -1, -1, -1, -1, -1, 25, 27, -1, 29, -1, -1, -1, 31, -1, 33, -1, -1], "loss_changes": [0.90906316, 0.89678895, 0.6766471, 1.2069517, 0.0, 0.0, 0.0, 2.16962, 3.2243845, 3.0449498, 3.6085157, 0.0, 2.9163585, 0.0, 0.0, 8.008142, 1.5449724, 2.8885596, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9852004, 0.2991197, 0.0, 3.533296, 0.0, 0.0, 0.0, 2.3573475, 0.0, 2.1738245, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 15, 15, 16, 16, 17, 17, 23, 23, 24, 24, 26, 26, 30, 30, 32, 32], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, 16, -1, 18, -1, -1, 20, 22, 24, -1, -1, -1, -1, -1, 26, 28, -1, 30, -1, -1, -1, 32, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.1923077, 0.010485179, -0.00026992048, -0.011820266, 1.0, -0.03846154, 1.0, 1.0, 0.015354186, 1.0, 0.0020657415, -0.022636447, 1.0, 1.0, 1.0, -0.012725939, 0.022458995, -0.012995342, -0.017708147, -0.0009472618, 0.0, 0.5769231, 0.008869033, 0.26923078, 0.017983092, 0.009737979, -0.020156758, 1.0, 0.012403471, 1.1923077, 0.0068125175, -0.013185128], "split_indices": [40, 114, 108, 1, 0, 0, 0, 89, 1, 93, 97, 0, 64, 0, 0, 59, 59, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 124, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2080.0, 1877.0, 203.0, 1785.0, 92.0, 99.0, 104.0, 690.0, 1095.0, 202.0, 488.0, 164.0, 931.0, 90.0, 112.0, 267.0, 221.0, 783.0, 148.0, 162.0, 105.0, 103.0, 118.0, 607.0, 176.0, 160.0, 447.0, 88.0, 88.0, 105.0, 342.0, 116.0, 226.0, 91.0, 135.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0027763743, -0.0042014685, 0.070983894, -0.037081476, 0.014159734, 0.016514612, -0.0028240783, -0.010084662, -0.10615309, 0.011865299, -0.0037154544, -0.062164515, 0.044888504, -0.019946897, -0.00023522465, 0.017868983, -0.013032486, -0.016818449, 0.0034814978, 0.018544126, -0.006184703, 0.038976215, -0.009972842, 0.018725945, 0.0035171718, -0.017855084, 0.0111242, 0.008633938, -0.011152992, -0.049279295, 0.013537849, 0.0017361193, -0.010973176], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, 17, 19, -1, -1, 21, -1, -1, -1, -1, -1, 23, -1, -1, 25, 27, -1, 29, -1, 31, -1, -1, -1], "loss_changes": [0.9794875, 1.1271387, 1.7845545, 1.2474928, 2.2376688, 0.0, 0.0, 1.3771003, 1.8210177, 0.0, 2.7956474, 2.539596, 3.5104623, 0.0, 0.0, 2.1694038, 0.0, 0.0, 0.0, 0.0, 0.0, 3.896164, 0.0, 0.0, 1.3767893, 1.2381963, 0.0, 2.855333, 0.0, 1.0756316, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 21, 21, 24, 24, 25, 25, 27, 27, 29, 29], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, 18, 20, -1, -1, 22, -1, -1, -1, -1, -1, 24, -1, -1, 26, 28, -1, 30, -1, 32, -1, -1, -1], "split_conditions": [1.0, -0.1923077, 1.0, -0.30769232, -0.03846154, 0.016514612, -0.0028240783, 1.0, 1.0, 0.011865299, 1.0, 1.0, 1.0, -0.019946897, -0.00023522465, 1.0, -0.013032486, -0.016818449, 0.0034814978, 0.018544126, -0.006184703, 0.1923077, -0.009972842, 0.018725945, 3.5769231, 1.3461539, 0.0111242, 0.9230769, -0.011152992, 1.0, 0.013537849, 0.0017361193, -0.010973176], "split_indices": [125, 1, 15, 1, 1, 0, 0, 93, 97, 0, 64, 59, 53, 0, 0, 58, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2058.0, 1867.0, 191.0, 669.0, 1198.0, 98.0, 93.0, 481.0, 188.0, 175.0, 1023.0, 247.0, 234.0, 99.0, 89.0, 874.0, 149.0, 118.0, 129.0, 101.0, 133.0, 741.0, 133.0, 143.0, 598.0, 499.0, 99.0, 389.0, 110.0, 267.0, 122.0, 127.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.005465407, 0.010338042, -7.789758e-06, 0.0056696404, -0.0094975745, 0.017771391, -0.043402288, -0.021023534, 0.042318627, -0.015676014, -0.00046369454, -0.059977885, 0.0086613465, 0.01858036, 0.014001721, -0.0037972403, 0.0054572416, -0.020429578, -0.020296019, 0.046656784, -0.013733255, 0.005341915, -0.07791185, 0.017519878, 0.014585936, -0.0053540496, -0.010202111, -0.042379558, 0.014027757, -0.022837792, 0.0220608, 0.013053565, -0.0064872922], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, -1, 3, 5, -1, 7, 9, 11, 13, -1, 15, 17, -1, -1, 19, -1, -1, 21, -1, 23, -1, -1, 25, -1, 27, -1, -1, 29, -1, -1, 31, -1, -1], "loss_changes": [1.103435, 0.0, 1.0513895, 1.0926954, 0.0, 1.405607, 1.7717429, 2.3983555, 3.6729975, 0.0, 0.544984, 2.3749766, 0.0, 0.0, 3.731081, 0.0, 0.0, 1.3966026, 0.0, 2.560041, 0.0, 0.0, 0.10870087, 0.0, 3.558563, 0.0, 0.0, 4.0991445, 0.0, 0.0, 2.3952515, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 14, 14, 17, 17, 19, 19, 22, 22, 24, 24, 27, 27, 30, 30], "right_children": [2, -1, 4, 6, -1, 8, 10, 12, 14, -1, 16, 18, -1, -1, 20, -1, -1, 22, -1, 24, -1, -1, 26, -1, 28, -1, -1, 30, -1, -1, 32, -1, -1], "split_conditions": [-0.5769231, 0.010338042, 1.0, 1.0, -0.0094975745, -0.1923077, 0.0, 1.0, -0.03846154, -0.015676014, 1.0, 1.0, 0.0086613465, 0.01858036, 1.0, -0.0037972403, 0.0054572416, 1.0, -0.020296019, 0.0, -0.013733255, 0.005341915, 1.0, 0.017519878, 1.0, -0.0053540496, -0.010202111, 0.23076923, 0.014027757, -0.022837792, 1.0, 0.013053565, -0.0064872922], "split_indices": [1, 0, 43, 80, 0, 1, 0, 61, 1, 0, 59, 113, 0, 0, 64, 0, 0, 115, 0, 0, 0, 0, 13, 0, 0, 0, 0, 1, 0, 0, 124, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2059.0, 109.0, 1950.0, 1840.0, 110.0, 1476.0, 364.0, 572.0, 904.0, 100.0, 264.0, 420.0, 152.0, 149.0, 755.0, 157.0, 107.0, 329.0, 91.0, 621.0, 134.0, 144.0, 185.0, 124.0, 497.0, 92.0, 93.0, 342.0, 155.0, 88.0, 254.0, 113.0, 141.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035375257, -0.009474685, 0.009360237, -0.015069385, 0.0064440663, -0.026568256, 0.026935484, -0.11110024, -0.009647578, -0.107844725, 0.14301415, -0.00546479, -0.019401459, -0.045630034, 0.02898157, -0.01457161, -0.0071619893, 0.028815513, -0.0003522405, 0.0016454154, -0.1007569, -0.010711866, 0.012748907, 0.008925022, -0.0081812395, -0.0037612363, -0.016345672, 0.033256374, -0.00945656, -0.00314216, 0.0141268615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, 5, -1, 7, 9, 11, 13, 15, 17, -1, -1, 19, 21, -1, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [1.1915345, 0.80515075, 0.0, 0.8742455, 0.0, 2.0325108, 6.0859466, 1.1093271, 1.6457264, 0.2469387, 4.4451094, 0.0, 0.0, 1.5975685, 2.2326684, 0.0, 0.0, 0.0, 0.0, 2.4127305, 1.120439, 1.500569, 0.0, 0.0, 0.0, 0.0, 0.0, 1.8652658, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 19, 19, 20, 20, 21, 21, 27, 27], "right_children": [2, 4, -1, 6, -1, 8, 10, 12, 14, 16, 18, -1, -1, 20, 22, -1, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.009360237, 1.0, 0.0064440663, -0.3846154, 1.0, 1.0, 1.0, -0.115384616, 1.0, -0.00546479, -0.019401459, 1.0, 1.0, -0.01457161, -0.0071619893, 0.028815513, -0.0003522405, 1.0, 1.0, 1.0, 0.012748907, 0.008925022, -0.0081812395, -0.0037612363, -0.016345672, 1.0, -0.00945656, -0.00314216, 0.0141268615], "split_indices": [102, 0, 0, 113, 0, 1, 109, 115, 39, 1, 39, 0, 0, 12, 50, 0, 0, 0, 0, 23, 59, 108, 0, 0, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1947.0, 119.0, 1810.0, 137.0, 1421.0, 389.0, 237.0, 1184.0, 180.0, 209.0, 141.0, 96.0, 613.0, 571.0, 88.0, 92.0, 105.0, 104.0, 330.0, 283.0, 407.0, 164.0, 161.0, 169.0, 141.0, 142.0, 267.0, 140.0, 167.0, 100.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0018624228, 0.009061473, -0.00318187, -0.008272166, 0.007817432, 0.000836339, -0.01231743, -0.010784956, 0.01911647, -0.049983136, 0.03140506, 0.03407376, -0.07945763, 0.011909325, 0.0055539324, -0.011921502, 0.014780416, -0.11335234, 0.0040170727, -0.022060707, 0.010005739, -0.14247088, 0.001282793, -0.05416297, 0.010704618, -0.09422245, -0.029655455, -0.10926382, 0.0032314733, -0.012993144, -0.0015893042, -0.021232868, -0.002448464], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, -1, 3, 5, -1, 7, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, -1, 21, -1, 23, -1, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.92403793, 0.0, 0.8087902, 1.9236269, 0.0, 3.7668018, 0.0, 2.6543038, 0.0, 2.0613089, 1.7522659, 3.7656565, 2.4977381, 0.0, 1.5579785, 0.0, 0.0, 1.7636089, 0.0, 1.9148157, 0.0, 2.8993735, 0.0, 1.7630486, 0.0, 0.83072734, 0.0, 1.9747312, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 17, 17, 19, 19, 21, 21, 23, 23, 25, 25, 27, 27], "right_children": [2, -1, 4, 6, -1, 8, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, -1, 22, -1, 24, -1, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.009061473, 3.5769231, 1.3461539, 0.007817432, 1.1538461, -0.01231743, 1.0, 0.01911647, -0.34615386, 1.0, -0.46153846, 1.0, 0.011909325, 1.0, -0.011921502, 0.014780416, 1.0, 0.0040170727, 1.0, 0.010005739, 1.0, 0.001282793, -0.07692308, 0.010704618, 0.26923078, -0.029655455, 1.0, 0.0032314733, -0.012993144, -0.0015893042, -0.021232868, -0.002448464], "split_indices": [1, 0, 1, 1, 0, 1, 0, 124, 0, 1, 81, 1, 62, 0, 23, 0, 0, 42, 0, 64, 0, 50, 0, 1, 0, 1, 0, 109, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 111.0, 1953.0, 1838.0, 115.0, 1703.0, 135.0, 1605.0, 98.0, 832.0, 773.0, 216.0, 616.0, 176.0, 597.0, 92.0, 124.0, 480.0, 136.0, 462.0, 135.0, 390.0, 90.0, 370.0, 92.0, 297.0, 93.0, 226.0, 144.0, 204.0, 93.0, 102.0, 124.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.004092575, 0.002169689, -0.07046551, 0.008655801, -0.05311041, -0.0046657077, -0.009400641, -0.008584787, 0.04098191, 0.0023561097, -0.01305643, -0.051023327, 0.035674803, 0.013206407, 0.014297353, -0.008468773, -0.01619397, -0.058458783, 0.10696943, -0.06322897, 0.079388276, 0.05954017, -0.011546376, -0.00011075882, -0.011337982, 0.15221341, 0.0023246908, 0.0023944185, -0.014798069, 0.0011643298, 0.015287432, 0.0007622036, 0.0122120045, 0.018852908, 0.011203433], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, 15, 17, 19, -1, 21, -1, 23, 25, 27, 29, 31, -1, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.85331917, 0.67264515, 0.09920317, 0.9357423, 1.1698864, 0.0, 0.0, 2.0567522, 1.6543949, 0.0, 0.0, 2.6384778, 3.5972137, 2.3219142, 0.0, 2.9397533, 0.0, 0.72760046, 1.1553206, 1.5736604, 1.2246641, 0.8025102, 0.0, 0.0, 0.0, 0.28890705, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 25, 25], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, 16, 18, 20, -1, 22, -1, 24, 26, 28, 30, 32, -1, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, -0.0046657077, -0.009400641, 1.0, 1.0, 0.0023561097, -0.01305643, 1.0, 1.0, 1.0, 0.014297353, 1.0, -0.01619397, 1.0, 1.0, 1.0, 1.0, 1.0, -0.011546376, -0.00011075882, -0.011337982, 1.0, 0.0023246908, 0.0023944185, -0.014798069, 0.0011643298, 0.015287432, 0.0007622036, 0.0122120045, 0.018852908, 0.011203433], "split_indices": [44, 40, 124, 50, 111, 0, 0, 106, 58, 0, 0, 0, 81, 93, 0, 105, 0, 15, 137, 13, 61, 12, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1876.0, 177.0, 1679.0, 197.0, 88.0, 89.0, 1095.0, 584.0, 99.0, 98.0, 559.0, 536.0, 459.0, 125.0, 404.0, 155.0, 231.0, 305.0, 213.0, 246.0, 247.0, 157.0, 113.0, 118.0, 198.0, 107.0, 105.0, 108.0, 128.0, 118.0, 135.0, 112.0, 104.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034015756, 0.007797801, -0.008044732, -0.017388072, 0.04102267, -0.047858454, 0.014636143, 0.024636408, -0.041480564, -0.015971366, -0.022929793, 0.090792075, -0.008124511, -0.010284423, 0.0025617997, -0.086987644, 0.03805182, 0.018495051, 0.00034993346, -0.008549248, 0.02046055, -0.01697404, -0.03630652, -0.016003355, 0.015638886, 0.015535449, -0.017370885, -0.007838467, 0.0019330116, 0.011984637, -0.009313096, 0.04512966, -0.012723515, 0.012349877, -0.0064086826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, -1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, 27, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1, -1], "loss_changes": [0.7840554, 0.0, 0.89994407, 1.6090778, 5.3195786, 2.3561974, 1.3936207, 0.0, 0.92230105, 0.0, 2.6992888, 1.5205789, 1.3689607, 0.0, 0.0, 1.4133782, 2.264443, 0.0, 0.0, 0.0, 2.306661, 0.0, 0.48928732, 2.5460966, 0.0, 0.0, 2.423902, 0.0, 0.0, 0.0, 0.0, 1.9258198, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 20, 20, 22, 22, 23, 23, 26, 26, 31, 31], "right_children": [2, -1, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, 28, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1, -1], "split_conditions": [-0.5769231, 0.007797801, 1.2692307, 1.0, 1.0, 1.0, 1.0, 0.024636408, 3.0384614, -0.015971366, 1.0, 0.03846154, -0.34615386, -0.010284423, 0.0025617997, 1.0, 1.0, 0.018495051, 0.00034993346, -0.008549248, 1.0, -0.01697404, 1.0, -0.30769232, 0.015638886, 0.015535449, 1.0, -0.007838467, 0.0019330116, 0.011984637, -0.009313096, 1.0, -0.012723515, 0.012349877, -0.0064086826], "split_indices": [1, 0, 1, 124, 108, 89, 81, 0, 1, 0, 15, 1, 1, 0, 0, 121, 62, 0, 0, 0, 89, 0, 97, 1, 0, 0, 113, 0, 0, 0, 0, 97, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 112.0, 1963.0, 1649.0, 314.0, 845.0, 804.0, 90.0, 224.0, 154.0, 691.0, 185.0, 619.0, 117.0, 107.0, 337.0, 354.0, 89.0, 96.0, 167.0, 452.0, 128.0, 209.0, 243.0, 111.0, 99.0, 353.0, 119.0, 90.0, 88.0, 155.0, 225.0, 128.0, 131.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0013547902, 0.0069581345, -0.010670183, 0.0010269702, 0.0658414, -0.004233785, 0.0072196885, 0.014785178, -0.0016168955, 0.0061600083, -0.014423369, -0.004384292, 0.01712407, -0.098004274, 0.008822287, -0.008246397, -0.011425093, -0.020830557, 0.035224304, -0.10747341, 0.049844876, -0.05225147, 0.07583129, -0.018708954, -0.0015118681, 0.013271371, -0.0040891236, -0.0118082445, 0.0012360382, 0.044086162, 0.021038728, 0.011220694, -0.0054957834], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, -1, 13, -1, 15, 17, -1, -1, 19, 21, 23, 25, 27, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [1.2527345, 0.6869673, 0.0, 0.6690665, 1.2106266, 2.4213367, 0.0, 0.0, 0.0, 2.696283, 0.0, 1.8001983, 0.0, 0.045445323, 0.99897397, 0.0, 0.0, 3.6802368, 2.3976858, 1.9852893, 2.4888542, 0.91024095, 1.9691591, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5166137, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, -1, 14, -1, 16, 18, -1, -1, 20, 22, 24, 26, 28, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.010670183, 3.1923077, 1.0, 1.3461539, 0.0072196885, 0.014785178, -0.0016168955, 1.1538461, -0.014423369, 1.0, 0.01712407, 0.115384616, 1.0, -0.008246397, -0.011425093, 1.0, -0.34615386, 1.0, 1.0, 1.0, 1.0, -0.018708954, -0.0015118681, 0.013271371, -0.0040891236, -0.0118082445, 0.0012360382, 1.0, 0.021038728, 0.011220694, -0.0054957834], "split_indices": [117, 125, 0, 1, 15, 1, 0, 0, 0, 1, 0, 26, 0, 1, 124, 0, 0, 15, 1, 97, 69, 50, 64, 0, 0, 0, 0, 0, 0, 113, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1967.0, 102.0, 1787.0, 180.0, 1664.0, 123.0, 90.0, 90.0, 1549.0, 115.0, 1456.0, 93.0, 180.0, 1276.0, 92.0, 88.0, 601.0, 675.0, 270.0, 331.0, 214.0, 461.0, 145.0, 125.0, 173.0, 158.0, 106.0, 108.0, 373.0, 88.0, 221.0, 152.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036457104, -0.02792875, 0.020101842, -0.012287709, -0.011390464, -0.012987662, 0.056875408, -0.033401474, 0.057704404, 0.023023464, -0.013770915, -0.0021382929, 0.1340896, 0.0016062167, -0.10891807, -0.0006288257, 0.011445264, -0.012342206, 0.09938435, 0.06692251, -0.014557229, 0.024333727, -0.00053754606, 0.006626621, -0.017183857, -0.017540768, -0.0026867033, 0.022832355, -0.0039105876, 0.012069146, 0.0014833794, -0.00561862, 0.0021157411], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, 15, 17, -1, 19, 21, 23, 25, -1, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.1896552, 1.371647, 1.2691423, 1.2753386, 0.0, 2.4657578, 2.2510083, 1.7527483, 0.7262945, 4.7638345, 0.0, 2.7735868, 3.2605543, 0.5503797, 1.145663, 0.0, 0.0, 0.0, 4.9999094, 0.52934253, 0.0, 0.0, 0.0, 0.0, 0.52488524, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 18, 18, 19, 19, 24, 24], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, 16, 18, -1, 20, 22, 24, 26, -1, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 2.0, 1.0, 1.0, -0.011390464, 0.46153846, -0.07692308, 1.0, 1.0, -0.42307693, -0.013770915, -0.30769232, 1.0, 1.0, 1.0, -0.0006288257, 0.011445264, -0.012342206, 1.0, 1.0, -0.014557229, 0.024333727, -0.00053754606, 0.006626621, 1.0, -0.017540768, -0.0026867033, 0.022832355, -0.0039105876, 0.012069146, 0.0014833794, -0.00561862, 0.0021157411], "split_indices": [71, 0, 39, 62, 0, 1, 1, 93, 74, 1, 0, 1, 109, 137, 12, 0, 0, 0, 126, 121, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 1020.0, 1043.0, 863.0, 157.0, 549.0, 494.0, 663.0, 200.0, 426.0, 123.0, 280.0, 214.0, 453.0, 210.0, 94.0, 106.0, 146.0, 280.0, 189.0, 91.0, 120.0, 94.0, 102.0, 351.0, 116.0, 94.0, 145.0, 135.0, 93.0, 96.0, 174.0, 177.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030605642, 0.009539401, -0.008603045, -0.034887597, 0.0056811343, -0.06830618, 0.0065368125, 0.096524134, -0.010161613, -0.13447051, 0.014615918, 0.00048575827, 0.018819066, 0.0057315324, -0.0096860165, -0.05549261, -0.027555723, 0.0058815456, -0.0051202932, -0.018916054, 0.095644936, -0.01292792, 0.0012145063, 0.07095363, -0.0530943, 0.0002093523, 0.01767228, 0.01447126, 0.00076322275, -0.021653011, -0.019491455, -0.01223518, 0.0050997897, 0.0124078905, -0.044810925, 0.0034233555, -0.009901288], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, -1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, 19, -1, 21, -1, -1, -1, 23, 25, -1, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, 33, -1, 35, -1, -1], "loss_changes": [1.1262885, 0.0, 0.7336358, 2.305078, 1.8220309, 2.8310266, 0.0, 1.5797173, 1.4853902, 3.1979632, 0.6661983, 0.0, 0.0, 2.0189116, 0.0, 0.91829896, 0.0, 0.0, 0.0, 2.1961854, 1.4866496, 0.0, 0.0, 0.92009205, 2.3097672, 0.0, 0.0, 0.0, 0.0, 1.1422457, 0.0, 0.0, 1.9893416, 0.0, 1.0111103, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 19, 19, 20, 20, 23, 23, 24, 24, 29, 29, 32, 32, 34, 34], "right_children": [2, -1, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, 20, -1, 22, -1, -1, -1, 24, 26, -1, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, 34, -1, 36, -1, -1], "split_conditions": [-0.5769231, 0.009539401, -0.1923077, 1.0, -0.03846154, 1.0, 0.0065368125, 1.0, 1.0, -0.34615386, 1.0, 0.00048575827, 0.018819066, 1.0, -0.0096860165, 1.0, -0.027555723, 0.0058815456, -0.0051202932, 0.0, 1.0, -0.01292792, 0.0012145063, 1.0, 1.0, 0.0002093523, 0.01767228, 0.01447126, 0.00076322275, 0.30769232, -0.019491455, -0.01223518, 1.0, 0.0124078905, 1.2692307, 0.0034233555, -0.009901288], "split_indices": [1, 0, 1, 61, 1, 69, 0, 124, 64, 1, 121, 0, 0, 0, 0, 39, 0, 0, 0, 0, 39, 0, 0, 97, 58, 0, 0, 0, 0, 1, 0, 0, 124, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 110.0, 1954.0, 688.0, 1266.0, 516.0, 172.0, 188.0, 1078.0, 287.0, 229.0, 94.0, 94.0, 911.0, 167.0, 184.0, 103.0, 137.0, 92.0, 715.0, 196.0, 88.0, 96.0, 197.0, 518.0, 91.0, 105.0, 91.0, 106.0, 424.0, 94.0, 89.0, 335.0, 99.0, 236.0, 96.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0041104145, -0.00039411132, 0.00779998, -0.007334183, 0.008674232, -0.013231687, 0.0069424785, -0.024975812, 0.02519819, -0.012990612, -0.011713227, -0.00827876, 0.10666963, -0.04801204, 0.013491636, 0.0047165635, 0.017784104, -0.0007345278, -0.12802012, 0.03749331, -0.009072804, 0.0088993255, -0.0076492727, -0.004305763, -0.021113556, 0.009982179, -0.001667308, -0.008962565, 0.05123638, -0.0018998208, 0.013005522], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, 11, 13, -1, -1, 15, 17, 19, -1, -1, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, 29, -1, -1], "loss_changes": [0.68930495, 1.1804388, 0.0, 0.81845677, 0.0, 0.7577752, 0.0, 1.4204042, 3.457519, 1.055434, 0.0, 0.0, 0.9486358, 1.853465, 0.35094678, 0.0, 0.0, 2.0936654, 1.2852278, 1.0600984, 1.6225976, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0573399, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 28, 28], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, 12, 14, -1, -1, 16, 18, 20, -1, -1, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 0.00779998, 5.0, 0.008674232, 1.0, 0.0069424785, 2.0, 1.0, 1.0, -0.011713227, -0.00827876, 1.0, 1.0, 1.0, 0.0047165635, 0.017784104, 1.0, 1.0, 1.0, 1.0, 0.0088993255, -0.0076492727, -0.004305763, -0.021113556, 0.009982179, -0.001667308, -0.008962565, 1.0, -0.0018998208, 0.013005522], "split_indices": [102, 125, 0, 0, 0, 61, 0, 0, 109, 111, 0, 0, 15, 15, 122, 0, 0, 12, 71, 39, 39, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1952.0, 119.0, 1808.0, 144.0, 1679.0, 129.0, 1286.0, 393.0, 1138.0, 148.0, 169.0, 224.0, 490.0, 648.0, 122.0, 102.0, 308.0, 182.0, 314.0, 334.0, 141.0, 167.0, 90.0, 92.0, 146.0, 168.0, 143.0, 191.0, 101.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0024671233, 0.010288248, -0.050464593, 0.012906003, 0.0034039326, -0.018909838, 0.012195387, 0.023861308, -0.018906372, 0.0058445763, 0.0155871725, -0.034998387, 0.010790568, -0.013378265, 0.024179472, -0.014707227, -0.08442802, 0.09158707, -0.02667486, -0.04121906, 0.0061240178, -0.0030479745, -0.013396232, 0.022079282, 0.025344508, 0.015576646, -0.015647849, 0.0029221757, -0.09059054, -0.0105089815, 0.017120657, 0.013224967, -0.05074274, -0.005712729, -0.013686947, -0.012754919, 0.002136124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, -1, 7, -1, -1, 9, 11, 13, -1, 15, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, -1, -1, 29, 31, -1, -1, 33, -1, -1, -1, 35, -1, -1, -1, -1], "loss_changes": [0.85860586, 1.4775155, 6.382108, 0.0, 0.77954876, 0.0, 0.0, 2.1191504, 1.6672204, 2.0070796, 0.0, 0.7271637, 0.0, 0.0, 2.3755822, 1.0349416, 0.5638528, 2.5505571, 2.166338, 1.3250295, 0.0, 0.0, 0.0, 0.0, 3.748008, 2.30583, 0.0, 0.0, 0.3468957, 0.0, 0.0, 0.0, 1.05223, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 7, 7, 8, 8, 9, 9, 11, 11, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 24, 24, 25, 25, 28, 28, 32, 32], "right_children": [2, 4, 6, -1, 8, -1, -1, 10, 12, 14, -1, 16, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, -1, -1, 30, 32, -1, -1, 34, -1, -1, -1, 36, -1, -1, -1, -1], "split_conditions": [3.0, -0.5769231, 1.0, 0.012906003, 1.0, -0.018909838, 0.012195387, 1.0, 1.0, -0.5, 0.0155871725, 0.5769231, 0.010790568, -0.013378265, 1.0, 0.115384616, 1.0, -0.26923078, 1.0, 0.0, 0.0061240178, -0.0030479745, -0.013396232, 0.022079282, 0.34615386, -0.03846154, -0.015647849, 0.0029221757, -0.23076923, -0.0105089815, 0.017120657, 0.013224967, 0.65384614, -0.005712729, -0.013686947, -0.012754919, 0.002136124], "split_indices": [0, 1, 122, 0, 122, 0, 0, 74, 61, 1, 0, 1, 0, 0, 126, 1, 12, 1, 61, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1807.0, 267.0, 99.0, 1708.0, 148.0, 119.0, 891.0, 817.0, 784.0, 107.0, 725.0, 92.0, 91.0, 693.0, 514.0, 211.0, 298.0, 395.0, 381.0, 133.0, 101.0, 110.0, 101.0, 197.0, 298.0, 97.0, 157.0, 224.0, 104.0, 93.0, 108.0, 190.0, 130.0, 94.0, 92.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028950844, -0.010908678, 0.041894257, -0.03837275, 0.01780376, 0.02381814, -0.03453612, -0.061329678, 0.047624603, 0.051350515, -0.019894814, -0.008375562, 0.0019240704, -0.09304001, 0.012860524, -0.0046730693, 0.013160084, -0.009192209, 0.12169541, -0.06835668, 0.008827205, -0.022154598, -0.052924387, 0.011059732, -0.009056966, 0.0043477393, -0.004163112, 0.01598062, 0.009311229, 0.0039253724, -0.014722644, -0.0043720775, -0.1041154, -0.012272126, 0.010687612, -0.01498385, -0.0059375553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, 23, -1, -1, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, 33, 35, -1, -1, -1, -1], "loss_changes": [0.7426127, 1.3839238, 4.710723, 1.7708888, 1.0850825, 0.0, 0.59819233, 1.6656382, 1.4975613, 1.933528, 2.1177552, 0.0, 0.0, 2.5569267, 2.1430943, 0.0, 0.0, 0.41688487, 0.22875786, 2.3679311, 0.0, 0.0, 0.93949664, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5542297, 0.3763981, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 22, 22, 31, 31, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, 24, -1, -1, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, 34, 36, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.02381814, 1.0, 1.0, -0.15384616, 1.0, 1.0, -0.008375562, 0.0019240704, 0.0, -0.115384616, -0.0046730693, 0.013160084, 1.0, 1.0, 1.0, 0.008827205, -0.022154598, 1.0, 0.011059732, -0.009056966, 0.0043477393, -0.004163112, 0.01598062, 0.009311229, 0.0039253724, -0.014722644, 1.0, 1.0, -0.012272126, 0.010687612, -0.01498385, -0.0059375553], "split_indices": [1, 124, 108, 62, 15, 0, 122, 58, 1, 93, 7, 0, 0, 0, 1, 0, 0, 81, 12, 93, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0, 17, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1755.0, 314.0, 897.0, 858.0, 88.0, 226.0, 708.0, 189.0, 454.0, 404.0, 118.0, 108.0, 496.0, 212.0, 89.0, 100.0, 244.0, 210.0, 279.0, 125.0, 118.0, 378.0, 109.0, 103.0, 93.0, 151.0, 90.0, 120.0, 118.0, 161.0, 194.0, 184.0, 94.0, 100.0, 91.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0046676737, 0.0007623279, 0.008577551, -0.0050064935, 0.007993297, 0.0024553223, -0.011285798, -0.0052810507, 0.01277846, -0.015356233, 0.012296404, -0.0331183, 0.01769812, -0.01983567, -0.013467749, -0.016586415, 0.011977255, -0.048420753, 0.0153971, 0.032292787, -0.012262277, -0.0028041091, -0.12302033, 0.08341664, -0.00705223, 0.010317094, -0.0071748504, 0.009827684, -0.004984179, -0.016565962, -0.007894373, 0.00048012318, 0.014513946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, -1, 5, -1, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, 21, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.65504736, 0.9011113, 0.0, 1.4799685, 0.0, 1.6677022, 0.0, 2.0931897, 0.0, 0.88184464, 0.0, 1.3179461, 1.8372769, 0.8701618, 0.0, 2.0369086, 0.0, 1.6232224, 2.261705, 1.9836743, 0.0, 1.4073653, 0.34016967, 1.0481101, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 19, 19, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, -1, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, 22, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 5.0, 0.008577551, 3.0, 0.007993297, 1.0, -0.011285798, 1.0, 0.01277846, 1.0, 0.012296404, 1.0, 1.2692307, 1.0, -0.013467749, 1.0, 0.011977255, 1.0, 0.3846154, 0.03846154, -0.012262277, 0.0, -0.115384616, 1.0, -0.00705223, 0.010317094, -0.0071748504, 0.009827684, -0.004984179, -0.016565962, -0.007894373, 0.00048012318, 0.014513946], "split_indices": [114, 0, 0, 0, 0, 102, 0, 125, 0, 50, 0, 42, 1, 12, 0, 106, 0, 93, 1, 1, 0, 0, 1, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2068.0, 1973.0, 95.0, 1839.0, 134.0, 1720.0, 119.0, 1620.0, 100.0, 1502.0, 118.0, 977.0, 525.0, 864.0, 113.0, 393.0, 132.0, 477.0, 387.0, 269.0, 124.0, 296.0, 181.0, 216.0, 171.0, 160.0, 109.0, 94.0, 202.0, 92.0, 89.0, 95.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00026844718, 0.0041169655, -0.007472047, 0.010145151, -0.039285954, 0.0016277533, 0.016336469, -0.011658096, 0.0034238533, -0.04913097, 0.01588627, -0.09997772, 0.006748999, 0.08843401, -0.010727497, -0.00012178957, -0.020869648, -0.0010810529, 0.018260356, 0.0085323155, -0.011232626, -0.028085317, 0.072198525, -0.09005472, 0.022801956, 0.001330625, 0.015901385, -0.0011254633, -0.015900476, 0.0187404, -0.06455666, -0.0134910215, 0.0005015153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, 19, -1, -1, -1, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1], "loss_changes": [0.59710544, 0.51490915, 0.0, 2.255094, 1.3639383, 1.184769, 0.0, 0.0, 0.0, 2.1287975, 2.4675226, 2.684261, 0.0, 3.2056146, 1.8295832, 0.0, 0.0, 0.0, 0.0, 1.8324065, 0.0, 1.5735736, 1.4673587, 1.2224851, 3.9399579, 0.0, 0.0, 0.0, 0.0, 0.0, 0.87613803, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 19, 19, 21, 21, 22, 22, 23, 23, 24, 24, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, 20, -1, -1, -1, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.3461539, -0.007472047, 1.1538461, 3.1923077, 1.0, 0.016336469, -0.011658096, 0.0034238533, 1.0, -0.34615386, 1.0, 0.006748999, 1.0, 1.0, -0.00012178957, -0.020869648, -0.0010810529, 0.018260356, 1.0, -0.011232626, 0.03846154, 1.0, 1.0, 0.23076923, 0.001330625, 0.015901385, -0.0011254633, -0.015900476, 0.0187404, 1.0, -0.0134910215, 0.0005015153], "split_indices": [117, 1, 0, 1, 1, 89, 0, 0, 0, 111, 1, 69, 0, 69, 113, 0, 0, 0, 0, 124, 0, 1, 2, 121, 1, 0, 0, 0, 0, 0, 97, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1968.0, 101.0, 1728.0, 240.0, 1637.0, 91.0, 117.0, 123.0, 359.0, 1278.0, 250.0, 109.0, 343.0, 935.0, 131.0, 119.0, 167.0, 176.0, 786.0, 149.0, 499.0, 287.0, 225.0, 274.0, 171.0, 116.0, 105.0, 120.0, 95.0, 179.0, 89.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0006521496, 0.006432633, -0.03768383, -0.0036594463, 0.069232345, -0.015333886, 0.010592113, -0.008628379, 0.005198469, 0.018081477, -0.0065264306, -0.04561828, 0.013624339, 0.01744147, -0.14323963, -0.0067294133, 0.01186668, 0.011465622, -0.0063202567, -0.022356428, -0.0074389908, -0.014802763, 0.01414668, 0.013204074, -0.012626965, 0.036896598, -0.05712641, 0.0082721235, -0.003817777, 0.00054079475, -0.013723242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, -1, 23, -1, 25, 27, 29, -1, -1, -1, -1], "loss_changes": [0.4549457, 1.130663, 4.467724, 0.42496803, 3.7068434, 0.0, 0.0, 1.1614306, 0.0, 0.0, 0.0, 3.262669, 1.8835856, 2.5244124, 1.1503077, 2.1769195, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0296001, 0.0, 1.1547762, 0.8531836, 1.3825889, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 22, 22, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, -1, 24, -1, 26, 28, 30, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 1.0, -0.015333886, 0.010592113, 1.0, 0.005198469, 0.018081477, -0.0065264306, 1.0, 1.0, 1.0, 1.0, -0.26923078, 0.01186668, 0.011465622, -0.0063202567, -0.022356428, -0.0074389908, -0.014802763, 0.07692308, 0.013204074, 1.0, 1.0, 1.0, 0.0082721235, -0.003817777, 0.00054079475, -0.013723242], "split_indices": [0, 0, 122, 41, 59, 0, 0, 16, 0, 0, 0, 108, 113, 106, 39, 1, 0, 0, 0, 0, 0, 0, 1, 0, 12, 109, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2053.0, 1784.0, 269.0, 1537.0, 247.0, 149.0, 120.0, 1411.0, 126.0, 135.0, 112.0, 530.0, 881.0, 322.0, 208.0, 738.0, 143.0, 146.0, 176.0, 96.0, 112.0, 95.0, 643.0, 119.0, 524.0, 248.0, 276.0, 154.0, 94.0, 155.0, 121.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.00024251608, -0.0068838005, 0.03698698, -0.034384836, 0.02167496, 0.020019175, -0.029177101, -0.08577836, 0.008056949, 0.06254568, -0.023192884, -0.0080718575, 0.0029302617, -0.11100814, 0.00032344987, -0.015348228, 0.056175027, 0.13617593, 0.0055040466, -0.0105827125, 0.03860621, -0.016790591, -0.0053380877, 0.011061168, 0.01625148, 0.022401886, 0.005522262, 0.008205253, -0.004705922, -0.004601748, 0.016922107, 0.016299332, -0.0077186823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, -1, -1, 23, 25, 27, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.50958556, 1.3736567, 3.3690684, 1.943479, 1.5733843, 0.0, 0.6691372, 0.9050472, 3.7932029, 1.8857951, 2.088649, 0.0, 0.0, 1.0295622, 0.0, 0.0, 1.8038208, 1.3937898, 1.0179805, 0.0, 2.5864286, 0.0, 0.0, 3.5396352, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 16, 16, 17, 17, 18, 18, 20, 20, 23, 23], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, -1, -1, 24, 26, 28, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 1.0, 1.0, 1.0, 1.0, 0.020019175, 3.0384614, 1.0, 1.0, 0.07692308, -0.34615386, -0.0080718575, 0.0029302617, 1.0, 0.00032344987, -0.015348228, 1.0, 1.0, 1.0, -0.0105827125, 1.0, -0.016790591, -0.0053380877, -0.34615386, 0.01625148, 0.022401886, 0.005522262, 0.008205253, -0.004705922, -0.004601748, 0.016922107, 0.016299332, -0.0077186823], "split_indices": [1, 124, 108, 15, 15, 0, 1, 0, 89, 1, 1, 0, 0, 122, 0, 0, 62, 50, 12, 0, 7, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1749.0, 312.0, 891.0, 858.0, 90.0, 222.0, 403.0, 488.0, 449.0, 409.0, 118.0, 104.0, 314.0, 89.0, 112.0, 376.0, 196.0, 253.0, 175.0, 234.0, 158.0, 156.0, 264.0, 112.0, 94.0, 102.0, 103.0, 150.0, 142.0, 92.0, 97.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.005514868, 0.0052873204, -0.07998016, -0.0028287405, 0.013865608, -0.022610167, 0.0108071845, -0.009721103, 0.008158932, -0.027349843, 0.007952319, 0.01814793, -0.04937316, -0.008090645, 0.022805935, -0.0052877697, 0.011220893, -0.014605868, -0.025371598, 0.07465839, -0.022171045, 0.030539101, -0.07748747, 0.034736123, 0.015644087, 0.011784952, -0.06781879, -0.0066330694, 0.0121925725, -0.014679732, -0.0029869992, -0.0010355845, 0.008112885, -0.012933015, -0.00093829754], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, 17, -1, 19, -1, -1, -1, 21, 23, 25, 27, 29, 31, -1, -1, 33, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1.66991, 1.9624443, 7.2268305, 0.9943642, 0.0, 0.0, 0.0, 0.49226516, 0.0, 0.7925915, 1.0413805, 1.7236314, 1.2368811, 0.0, 1.5765449, 0.0, 0.0, 0.0, 1.2442076, 1.0251911, 2.3137672, 1.8236365, 0.72937894, 0.44139948, 0.0, 0.0, 0.9812887, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 14, 14, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, 18, -1, 20, -1, -1, -1, 22, 24, 26, 28, 30, 32, -1, -1, 34, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.013865608, -0.022610167, 0.0108071845, 1.0, 0.008158932, -0.26923078, -0.34615386, 1.0, -0.07692308, -0.008090645, 1.0, -0.0052877697, 0.011220893, -0.014605868, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 0.015644087, 0.011784952, 1.0, -0.0066330694, 0.0121925725, -0.014679732, -0.0029869992, -0.0010355845, 0.008112885, -0.012933015, -0.00093829754], "split_indices": [0, 102, 122, 125, 0, 0, 0, 111, 0, 1, 1, 61, 1, 0, 108, 0, 0, 0, 12, 121, 1, 108, 53, 137, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1813.0, 263.0, 1709.0, 104.0, 148.0, 115.0, 1580.0, 129.0, 791.0, 789.0, 258.0, 533.0, 113.0, 676.0, 147.0, 111.0, 106.0, 427.0, 314.0, 362.0, 206.0, 221.0, 211.0, 103.0, 89.0, 273.0, 100.0, 106.0, 90.0, 131.0, 107.0, 104.0, 133.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0006635128, 0.0072312057, -0.045463063, -0.0026613208, 0.011449724, 0.0012519209, -0.0129951555, -0.048880216, 0.010056867, -0.016071202, 0.004044283, 0.047784686, -0.028789243, -0.010524293, 0.011243572, 0.1309193, 0.0018161258, -0.012418916, 0.0016366508, 0.025190813, -0.004426241, -0.07959441, 0.06763743, -0.031985328, 0.013141751, -0.01663907, 0.00018877823, -0.007983308, 0.023237929, -0.062420085, 0.0015380004, -0.0011695836, -0.014126318], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, 13, 15, 17, -1, -1, 19, 21, -1, 23, -1, -1, 25, 27, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.6270965, 1.9227718, 1.2638996, 0.9751945, 0.0, 0.0, 0.0, 2.1188743, 1.9067183, 0.0, 2.8785293, 2.5222416, 1.8605847, 0.0, 0.0, 4.9808326, 2.2773826, 0.0, 2.1206563, 0.0, 0.0, 1.3437476, 5.7092237, 0.5564394, 0.0, 0.0, 0.0, 0.0, 0.0, 0.93982476, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 16, 16, 18, 18, 21, 21, 22, 22, 23, 23, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, 14, 16, 18, -1, -1, 20, 22, -1, 24, -1, -1, 26, 28, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.3461539, 0.9230769, 1.0, 1.0, 0.011449724, 0.0012519209, -0.0129951555, -0.46153846, 1.0, -0.016071202, 1.0, -0.34615386, 1.0, -0.010524293, 0.011243572, 1.0, 0.0, -0.012418916, 1.0, 0.025190813, -0.004426241, 1.0, 1.0, 0.07692308, 0.013141751, -0.01663907, 0.00018877823, -0.007983308, 0.023237929, 1.0, 0.0015380004, -0.0011695836, -0.014126318], "split_indices": [1, 1, 17, 89, 0, 0, 0, 1, 126, 0, 108, 1, 59, 0, 0, 97, 1, 0, 105, 0, 0, 124, 71, 1, 0, 0, 0, 0, 0, 93, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1812.0, 258.0, 1659.0, 153.0, 153.0, 105.0, 358.0, 1301.0, 115.0, 243.0, 660.0, 641.0, 121.0, 122.0, 235.0, 425.0, 155.0, 486.0, 139.0, 96.0, 190.0, 235.0, 386.0, 100.0, 92.0, 98.0, 124.0, 111.0, 235.0, 151.0, 143.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.001746877, -0.00566247, 0.0061457297, -0.05317665, 0.0038109438, -0.012511491, 0.0007770253, -0.008810453, 0.061477683, -0.0092771165, 0.010282963, 0.010520097, -0.019543158, -0.0013153608, 0.012647915, -0.046142846, 0.0029611462, -0.0060753347, -0.0782749, -0.035849605, 0.058098204, 0.0060865185, -0.0048633567, -0.038458165, -0.014698516, -0.010006732, 0.005590014, -0.0015193599, 0.013527672, -0.0099417595, 0.0025709628, -0.0058835554, 0.007340643], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, -1, 17, 19, 21, 23, 25, 27, -1, -1, 29, -1, -1, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5090699, 0.87188554, 0.0, 1.2497897, 1.1754533, 0.0, 1.756618, 1.6213373, 1.4068321, 0.0, 0.0, 0.0, 0.72491384, 0.0, 0.0, 0.7145351, 1.4037814, 0.703671, 0.8426316, 1.0245459, 1.5329262, 0.0, 0.0, 0.76276857, 0.0, 0.0, 1.0223722, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 12, 12, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 26, 26], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, -1, 18, 20, 22, 24, 26, 28, -1, -1, 30, -1, -1, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.0061457297, 1.0, 1.0, -0.012511491, 0.03846154, -0.5, 1.0, -0.0092771165, 0.010282963, 0.010520097, 1.0, -0.0013153608, 0.012647915, 1.0, 1.0, 1.0, 0.23076923, 1.0, 1.0, 0.0060865185, -0.0048633567, -0.26923078, -0.014698516, -0.010006732, 1.0, -0.0015193599, 0.013527672, -0.0099417595, 0.0025709628, -0.0058835554, 0.007340643], "split_indices": [102, 0, 0, 124, 42, 0, 1, 1, 13, 0, 0, 0, 111, 0, 0, 127, 106, 97, 1, 69, 81, 0, 0, 1, 0, 0, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2057.0, 1937.0, 120.0, 322.0, 1615.0, 138.0, 184.0, 1325.0, 290.0, 96.0, 88.0, 114.0, 1211.0, 135.0, 155.0, 555.0, 656.0, 247.0, 308.0, 385.0, 271.0, 96.0, 151.0, 195.0, 113.0, 151.0, 234.0, 139.0, 132.0, 100.0, 95.0, 120.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.00031841572, 0.0029862158, -0.00666134, -0.011399268, 0.017284164, 0.0014584538, -0.0084259715, 0.04512455, -0.0074628373, -0.0325275, 0.029150717, -0.020811563, 0.12350145, -0.03391751, 0.005753505, 0.009043336, -0.010963983, -0.01528063, 0.08771933, -0.013028326, 0.004542926, 0.0069400603, 0.018764159, -0.08905325, 0.0053991205, 0.0045957267, -0.0043743555, 0.010331097, -0.00842794, 0.013297225, 0.0036627275, -0.0017874032, -0.01923779], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, 15, 17, 19, 21, 23, -1, 25, -1, 27, 29, -1, -1, -1, -1, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.45218235, 0.4043726, 0.0, 0.91808313, 0.67932063, 0.78397626, 0.0, 2.397891, 0.8975778, 1.1989037, 1.1944479, 1.8273771, 0.73564696, 1.7982045, 0.0, 0.47350287, 0.0, 2.135678, 0.45778823, 0.0, 0.0, 0.0, 0.0, 1.6768407, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 18, 18, 23, 23], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, 16, 18, 20, 22, 24, -1, 26, -1, 28, 30, -1, -1, -1, -1, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.00666134, 2.0, 1.0, 1.0, -0.0084259715, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.005753505, 1.0, -0.010963983, 1.0, 1.0, -0.013028326, 0.004542926, 0.0069400603, 0.018764159, 1.0, 0.0053991205, 0.0045957267, -0.0043743555, 0.010331097, -0.00842794, 0.013297225, 0.0036627275, -0.0017874032, -0.01923779], "split_indices": [117, 71, 0, 0, 121, 17, 0, 109, 111, 109, 12, 59, 61, 58, 0, 12, 0, 39, 81, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2064.0, 1966.0, 98.0, 980.0, 986.0, 833.0, 147.0, 464.0, 522.0, 374.0, 459.0, 252.0, 212.0, 371.0, 151.0, 243.0, 131.0, 261.0, 198.0, 95.0, 157.0, 115.0, 97.0, 228.0, 143.0, 143.0, 100.0, 96.0, 165.0, 105.0, 93.0, 135.0, 93.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002628564, -0.004479584, 0.0048446716, -0.016274517, 0.028959166, -0.0038522803, -0.06420721, -0.007947139, 0.061233543, 0.047105365, -0.02637609, -0.011702912, -0.0009907739, 0.01856387, 0.018704308, 0.10986159, -0.006514152, 0.017873926, -0.064078376, 0.010982061, -0.0067726495, 0.02214378, -0.0014588768, 0.006937492, -0.031094212, -0.115336955, -0.01402871, -0.008609119, 0.002827749, -0.02167412, -0.0008912659, -0.008616298, 0.0073133507], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, 19, -1, 21, -1, 23, 25, -1, -1, -1, -1, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42537287, 0.7517413, 0.0, 0.8389624, 1.7392657, 1.2843436, 0.8317783, 0.0, 2.0560398, 2.416158, 1.2946217, 0.0, 0.0, 2.2521293, 0.0, 3.0548549, 0.0, 0.90032125, 1.0749334, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5975436, 2.233918, 1.3329253, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 13, 13, 15, 15, 17, 17, 18, 18, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, 20, -1, 22, -1, 24, 26, -1, -1, -1, -1, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0048446716, 1.0, 1.0, 0.0, 1.0, -0.007947139, 1.0, 1.0, 1.0, -0.011702912, -0.0009907739, 1.0, 0.018704308, 1.0, -0.006514152, 1.0, 1.0, 0.010982061, -0.0067726495, 0.02214378, -0.0014588768, 0.006937492, 1.0, 1.0, 1.0, -0.008609119, 0.002827749, -0.02167412, -0.0008912659, -0.008616298, 0.0073133507], "split_indices": [31, 0, 0, 80, 5, 0, 12, 0, 61, 121, 126, 0, 0, 109, 0, 97, 0, 105, 97, 0, 0, 0, 0, 0, 58, 12, 108, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1906.0, 165.0, 1409.0, 497.0, 1119.0, 290.0, 114.0, 383.0, 343.0, 776.0, 147.0, 143.0, 286.0, 97.0, 220.0, 123.0, 357.0, 419.0, 139.0, 147.0, 116.0, 104.0, 174.0, 183.0, 207.0, 212.0, 95.0, 88.0, 106.0, 101.0, 116.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.001757404, 0.004273511, -0.044443153, -0.005283425, 0.010833795, 0.00171052, -0.013212055, 0.00022052693, -0.009147534, -0.05626034, 0.011934757, 0.0071276138, -0.013664699, -0.010410975, 0.024531044, -0.013389022, 0.06801736, -0.011884867, 0.009886247, 0.021657294, 0.027848486, -0.06399371, 0.056857854, 0.069506, -0.0056346594, -0.014379221, 0.0021623424, -0.005910326, 0.12057907, 0.13492739, -0.0047363266, 0.0006568303, 0.023458982, 0.01915305, 0.007708022], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, -1, -1, -1, 15, 17, 19, -1, 21, -1, 23, 25, 27, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [0.53443336, 1.8090634, 1.3868752, 0.7903443, 0.0, 0.0, 0.0, 1.0361122, 0.0, 2.7578502, 1.8958633, 0.0, 0.0, 0.0, 1.9293351, 1.5341263, 3.2521837, 0.0, 1.7767731, 0.0, 1.5046563, 1.3595924, 2.3128147, 2.1943307, 0.0, 0.0, 0.0, 0.0, 2.6256864, 0.6024759, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 16, 16, 18, 18, 20, 20, 21, 21, 22, 22, 23, 23, 28, 28, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, -1, -1, -1, 16, 18, 20, -1, 22, -1, 24, 26, 28, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 0.9230769, 1.0, 0.6923077, 0.010833795, 0.00171052, -0.013212055, -1.0, -0.009147534, -0.30769232, 1.0, 0.0071276138, -0.013664699, -0.010410975, 1.0, 1.0, 1.0, -0.011884867, 1.0, 0.021657294, 1.0, 1.0, 1.0, 1.0, -0.0056346594, -0.014379221, 0.0021623424, -0.005910326, -0.1923077, 1.0, -0.0047363266, 0.0006568303, 0.023458982, 0.01915305, 0.007708022], "split_indices": [1, 1, 17, 1, 0, 0, 0, 0, 0, 1, 104, 0, 0, 0, 124, 89, 81, 0, 17, 0, 97, 122, 81, 111, 0, 0, 0, 0, 1, 39, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2076.0, 1819.0, 257.0, 1666.0, 153.0, 151.0, 106.0, 1566.0, 100.0, 269.0, 1297.0, 104.0, 165.0, 127.0, 1170.0, 625.0, 545.0, 113.0, 512.0, 116.0, 429.0, 199.0, 313.0, 287.0, 142.0, 103.0, 96.0, 111.0, 202.0, 184.0, 103.0, 101.0, 101.0, 93.0, 91.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.002489783, -0.018048106, 0.0151768485, -0.0011129556, -0.01085965, 0.105964005, -0.00053952576, 0.023577902, -0.0077471742, 0.0022860623, 0.019454671, 0.013696292, -0.007618801, -0.008041292, 0.013467239, -0.0063103214, 0.08794766, -0.060892712, 0.04590548, 0.019645635, -0.011480251, 0.0011923629, 0.015794994, -0.016291564, 0.0021474406, 0.0095876595, 0.00040248997, 0.009209753, -0.008127584, 0.0073553855, -0.04004286, 0.0026051847, -0.011353279], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, -1, 19, 21, 23, 25, 27, -1, -1, -1, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [0.5370255, 1.2068253, 1.8178009, 1.2499964, 0.0, 1.3839655, 1.1695333, 1.7598724, 0.0, 0.0, 0.0, 1.357764, 0.0, 1.1119539, 0.0, 2.0275338, 1.0324395, 1.6554576, 0.40391386, 1.1691015, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0948919, 0.0, 1.4669037, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, -1, 20, 22, 24, 26, 28, -1, -1, -1, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [-0.1923077, 3.0, -0.03846154, 1.0, -0.01085965, 1.0, 1.0, 1.0, -0.0077471742, 0.0022860623, 0.019454671, 1.0, -0.007618801, 1.0, 0.013467239, 1.0, 1.0, 1.0, 1.0, 0.0, -0.011480251, 0.0011923629, 0.015794994, -0.016291564, 0.0021474406, 0.0095876595, 0.00040248997, 0.009209753, 1.0, 0.0073553855, 1.0, 0.0026051847, -0.011353279], "split_indices": [1, 0, 1, 42, 0, 124, 64, 58, 0, 0, 0, 0, 0, 97, 0, 15, 39, 124, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 0, 106, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 787.0, 1274.0, 663.0, 124.0, 188.0, 1086.0, 501.0, 162.0, 97.0, 91.0, 914.0, 172.0, 390.0, 111.0, 720.0, 194.0, 197.0, 193.0, 581.0, 139.0, 93.0, 101.0, 88.0, 109.0, 88.0, 105.0, 161.0, 420.0, 118.0, 302.0, 159.0, 143.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.004206481, 0.008196045, -0.0066698915, -0.03518314, 0.014585821, 0.003552143, -0.016614733, 0.001428512, 0.07927591, 0.01946097, -0.027999917, -0.007046061, 0.14733799, -0.0036350912, 0.012077193, 0.010764507, -0.058589566, 0.0071598575, 0.02420122, -0.054321826, 0.025857404, -0.0124917785, -0.024163146, -0.00077289564, -0.014541346, 0.01078245, -0.000506274, -0.007295515, 0.0047565233, -0.0075631835, 0.049699575, 0.015050563, -0.003400543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, 15, -1, 17, 19, -1, -1, 21, -1, -1, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [0.5841503, 0.54189336, 0.0, 2.324202, 1.450355, 0.0, 0.0, 0.75142425, 2.9351168, 2.0544186, 2.232341, 0.0, 1.4197721, 1.0688381, 0.0, 0.0, 1.002431, 0.0, 0.0, 1.1162298, 0.9767517, 0.0, 1.0114338, 0.0, 0.0, 0.0, 1.2899361, 0.0, 0.0, 0.0, 1.7297845, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 12, 12, 13, 13, 16, 16, 19, 19, 20, 20, 22, 22, 26, 26, 30, 30], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, 16, -1, 18, 20, -1, -1, 22, -1, -1, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, -0.5, -0.0066698915, 1.0, 1.0, 0.003552143, -0.016614733, 1.0, 1.0, 1.0, -0.34615386, -0.007046061, 1.0, 1.0, 0.012077193, 0.010764507, 1.0, 0.0071598575, 0.02420122, 1.0, 0.0, -0.0124917785, 1.0, -0.00077289564, -0.014541346, 0.01078245, 0.07692308, -0.007295515, 0.0047565233, -0.0075631835, 0.6923077, 0.015050563, -0.003400543], "split_indices": [43, 1, 0, 121, 42, 0, 0, 109, 109, 105, 1, 0, 50, 111, 0, 0, 69, 0, 0, 15, 0, 0, 93, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2065.0, 1955.0, 110.0, 251.0, 1704.0, 163.0, 88.0, 1416.0, 288.0, 878.0, 538.0, 90.0, 198.0, 715.0, 163.0, 99.0, 439.0, 110.0, 88.0, 263.0, 452.0, 150.0, 289.0, 174.0, 89.0, 110.0, 342.0, 172.0, 117.0, 137.0, 205.0, 93.0, 112.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0023254976, -0.0014099549, 0.006169472, -0.046216436, 0.0075125457, 0.00261706, -0.010626479, -0.0040549696, 0.06089182, 0.00941248, -0.052519158, 0.012282605, -0.0018737887, 0.04111694, -0.022049032, -0.010387286, 0.0016787443, 0.00490996, 0.0148079535, 0.00342032, -0.049453977, -0.006940824, 0.05246102, 0.010344454, -0.0130523015, 0.00015239518, 0.011493098, -0.008306815, 0.012659128], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, -1, 5, 7, -1, -1, 9, 11, 13, 15, -1, -1, 17, 19, -1, -1, 21, -1, -1, 23, -1, 25, 27, -1, -1, -1, -1, -1], "loss_changes": [0.45707002, 0.77518487, 0.0, 1.3996449, 0.99844205, 0.0, 0.0, 0.86742383, 1.4203603, 1.0373693, 1.0285947, 0.0, 0.0, 2.0061064, 0.8047097, 0.0, 0.0, 1.3676227, 0.0, 0.0, 1.7015784, 0.0, 0.7509604, 2.1935017, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 17, 17, 20, 20, 22, 22, 23, 23], "right_children": [2, 4, -1, 6, 8, -1, -1, 10, 12, 14, 16, -1, -1, 18, 20, -1, -1, 22, -1, -1, 24, -1, 26, 28, -1, -1, -1, -1, -1], "split_conditions": [1.0, -1.0, 0.006169472, 1.0, 1.0, 0.00261706, -0.010626479, 2.0, 1.0, 1.0, 1.0, 0.012282605, -0.0018737887, 1.0, 1.0, -0.010387286, 0.0016787443, 1.0, 0.0148079535, 0.00342032, 1.0, -0.006940824, 1.0, 1.0, -0.0130523015, 0.00015239518, 0.011493098, -0.008306815, 0.012659128], "split_indices": [102, 0, 0, 127, 42, 0, 0, 0, 12, 108, 122, 0, 0, 116, 16, 0, 0, 106, 0, 0, 109, 0, 83, 50, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1939.0, 122.0, 322.0, 1617.0, 146.0, 176.0, 1329.0, 288.0, 1040.0, 289.0, 162.0, 126.0, 518.0, 522.0, 166.0, 123.0, 387.0, 131.0, 171.0, 351.0, 151.0, 236.0, 202.0, 149.0, 130.0, 106.0, 112.0, 90.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "29", "size_leaf_vector": "1"}}, {"base_weights": [0.00043333502, -0.0032626549, 0.006071837, -0.010810942, 0.035407424, -0.035323974, 0.013402048, 0.0093825385, -0.0029631215, -0.059927024, 0.04739546, -0.003850366, 0.012788071, -0.013102474, -0.09333627, 0.01143565, -0.002483215, -0.08201188, 0.022892982, 0.007495012, -0.0067585036, -0.13714018, 0.0010089626, -0.0024341724, -0.01429587, 0.009325797, -0.0002673436, -0.019996813, -0.010345532, 0.04087659, -0.006083328, -0.0014708723, 0.011835917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, 15, 17, -1, 19, 21, -1, -1, 23, 25, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.45899472, 0.566564, 0.0, 0.9638989, 1.2044178, 1.6423663, 1.6136025, 0.0, 0.0, 0.97303987, 0.89474094, 1.4841137, 0.0, 1.2425089, 1.6445572, 0.0, 0.0, 0.63618064, 0.8620988, 0.0, 0.0, 0.53966856, 0.0, 0.0, 0.0, 0.0, 0.9917845, 0.0, 0.0, 1.0207343, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 10, 10, 11, 11, 13, 13, 14, 14, 17, 17, 18, 18, 21, 21, 26, 26, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, 16, 18, -1, 20, 22, -1, -1, 24, 26, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.2692307, 0.006071837, 1.0, 1.0, 1.0, 1.0, 0.0093825385, -0.0029631215, 1.0, 1.0, 1.0, 0.012788071, 1.0, 1.0, 0.01143565, -0.002483215, -0.23076923, 1.0, 0.007495012, -0.0067585036, -0.30769232, 0.0010089626, -0.0024341724, -0.01429587, 0.009325797, 1.0, -0.019996813, -0.010345532, 0.0, -0.006083328, -0.0014708723, 0.011835917], "split_indices": [102, 1, 0, 124, 115, 62, 105, 0, 0, 105, 13, 5, 0, 59, 122, 0, 0, 1, 81, 0, 0, 1, 0, 0, 0, 0, 97, 0, 0, 1, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1941.0, 119.0, 1624.0, 317.0, 807.0, 817.0, 167.0, 150.0, 622.0, 185.0, 710.0, 107.0, 259.0, 363.0, 96.0, 89.0, 181.0, 529.0, 99.0, 160.0, 255.0, 108.0, 93.0, 88.0, 131.0, 398.0, 89.0, 166.0, 237.0, 161.0, 138.0, 99.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00016712355, 0.007138, -0.046529964, -0.0021058558, 0.0099463845, 0.0048594074, -0.014379968, 0.026091676, -0.026097657, 0.010096642, 0.01469632, -0.048127398, 0.008632996, 0.060335703, -0.04277841, -0.08524856, 0.022054786, -0.004461817, 0.09911386, 0.007901278, -0.010773409, -0.05685611, -0.12995154, 0.009864008, -0.002784916, 0.02183811, -0.0013630933, 0.008983116, -0.00777108, -0.012451978, 0.00046959796, -0.010351471, -0.015935875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, 13, -1, 15, -1, 17, 19, 21, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.67415106, 1.5379114, 2.4889731, 1.1081227, 0.0, 0.0, 0.0, 1.4558077, 2.1919246, 1.7665015, 0.0, 1.9278809, 0.0, 1.3878418, 1.0665869, 0.614305, 0.9784088, 0.0, 3.3482442, 1.276582, 0.0, 1.2327931, 0.14615703, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 21, 21, 22, 22], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, 14, -1, 16, -1, 18, 20, 22, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.0099463845, 0.0048594074, -0.014379968, 1.1153846, 1.0, -0.03846154, 0.01469632, 1.0, 0.008632996, 1.0, 1.0, 1.0, 1.0, -0.004461817, 1.0, 1.0, -0.010773409, 1.0, 1.0, 0.009864008, -0.002784916, 0.02183811, -0.0013630933, 0.008983116, -0.00777108, -0.012451978, 0.00046959796, -0.010351471, -0.015935875], "split_indices": [0, 125, 109, 108, 0, 0, 0, 1, 64, 1, 0, 23, 0, 53, 39, 106, 1, 0, 126, 23, 0, 109, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 1802.0, 269.0, 1638.0, 164.0, 136.0, 133.0, 753.0, 885.0, 665.0, 88.0, 740.0, 145.0, 341.0, 324.0, 484.0, 256.0, 92.0, 249.0, 182.0, 142.0, 296.0, 188.0, 101.0, 155.0, 121.0, 128.0, 93.0, 89.0, 141.0, 155.0, 99.0, 89.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005878495, 0.0069979127, -0.0044881753, -0.006189523, 0.00021627694, -0.0061538694, 0.008892453, -0.023095885, 0.0142208375, -0.007280646, -0.009388789, 0.009814842, -0.0067610512, 0.023373948, -0.057511166, -0.013456436, 0.03149853, 0.0772303, -0.05109068, -0.014662999, -0.0135571165, 0.003125175, 0.0149980625, 0.0009840323, 0.019811537, -0.0015761302, -0.0090218885, -0.006721951, 0.0037893483, -0.05189773, 0.0083873095, -0.016276663, 0.0051390366], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, 15, 17, 19, -1, 21, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.5675327, 0.0, 0.5277145, 0.0, 1.0205431, 0.58164287, 0.0, 1.0300248, 1.3471341, 1.1579268, 0.0, 0.0, 2.9924972, 1.8728535, 0.953247, 0.0, 1.5833766, 2.2076855, 0.2709453, 0.5082422, 0.0, 1.6883346, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.588025, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 12, 12, 13, 13, 14, 14, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 29, 29], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, 16, 18, 20, -1, 22, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [-0.5769231, 0.0069979127, -0.5, -0.006189523, 1.0, 1.0, 0.008892453, 1.0, -0.30769232, 1.0, -0.009388789, 0.009814842, -0.115384616, 1.0, 0.61538464, -0.013456436, 2.0, 0.42307693, 1.0, 1.0, -0.0135571165, 1.0, 0.0149980625, 0.0009840323, 0.019811537, -0.0015761302, -0.0090218885, -0.006721951, 0.0037893483, 1.0, 0.0083873095, -0.016276663, 0.0051390366], "split_indices": [1, 0, 1, 0, 90, 93, 0, 61, 1, 109, 0, 0, 1, 12, 1, 0, 0, 1, 83, 2, 0, 109, 0, 0, 0, 0, 0, 0, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 108.0, 1954.0, 148.0, 1806.0, 1685.0, 121.0, 920.0, 765.0, 752.0, 168.0, 153.0, 612.0, 467.0, 285.0, 141.0, 471.0, 271.0, 196.0, 184.0, 101.0, 380.0, 91.0, 174.0, 97.0, 103.0, 93.0, 92.0, 92.0, 226.0, 154.0, 109.0, 117.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0087729925, -0.0073148273, 0.026051901, -0.009542311, 0.006774773, 0.012352822, 0.01487901, 0.062925346, -0.024857992, 0.059498295, -0.037979636, 0.01590671, -0.004112679, -0.08549595, 0.0076950053, -0.0037439326, 0.13686259, -0.07307188, 0.0041848505, -0.01910904, -4.5967015e-05, 0.04449427, -0.0063906643, -0.0068308706, 0.0062903548, 0.0056085265, 0.023124447, 0.0076761567, -0.14649029, 0.00904259, -0.0032221295, -0.024145301, -0.006879348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, -1, 7, 9, -1, 11, 13, 15, 17, -1, -1, 19, 21, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.5684691, 1.3146539, 1.6578603, 0.0, 1.6216688, 2.1048045, 0.0, 3.291236, 1.1527853, 2.2408528, 1.2017787, 0.0, 0.0, 1.8317889, 1.0012574, 1.084376, 1.5705261, 3.2781591, 0.0, 0.0, 0.0, 0.8844416, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.4756598, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 21, 21, 28, 28], "right_children": [2, 4, 6, -1, 8, 10, -1, 12, 14, 16, 18, -1, -1, 20, 22, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 1.0, -0.009542311, -0.07692308, 1.0, 0.01487901, 1.0, 0.34615386, 1.0, 1.0, 0.01590671, -0.004112679, 1.0, 1.0, 1.0, 1.0, -0.30769232, 0.0041848505, -0.01910904, -4.5967015e-05, 1.0, -0.0063906643, -0.0068308706, 0.0062903548, 0.0056085265, 0.023124447, 0.0076761567, 1.0, 0.00904259, -0.0032221295, -0.024145301, -0.006879348], "split_indices": [39, 1, 88, 0, 1, 108, 0, 122, 1, 12, 115, 0, 0, 122, 127, 71, 93, 1, 0, 0, 0, 115, 0, 0, 0, 0, 0, 0, 97, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2045.0, 1059.0, 986.0, 146.0, 913.0, 887.0, 99.0, 329.0, 584.0, 458.0, 429.0, 171.0, 158.0, 204.0, 380.0, 252.0, 206.0, 298.0, 131.0, 91.0, 113.0, 251.0, 129.0, 128.0, 124.0, 111.0, 95.0, 98.0, 200.0, 157.0, 94.0, 90.0, 110.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.007870281, 0.0043379124, -0.033379946, 0.012923222, -0.0063149123, -0.05461827, 0.009676124, 0.0057863398, 0.009020544, -0.0032978537, -0.09153832, 0.019199463, -0.042295367, -0.008063907, 0.0074687838, -0.051597565, -0.018539906, 0.029128887, -0.006003531, 0.0006465796, -0.011326461, -0.01196138, 0.0052255136, 0.0030797836, 0.077640615, 0.03948874, -0.010121953, 0.0015268767, 0.015298578, 0.075482115, -0.0073577366, 0.014957634, 0.00039080856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, -1, -1, 21, -1, 23, -1, -1, -1, -1, -1, 25, 27, 29, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.6446538, 0.8111558, 1.851867, 0.68502975, 0.0, 1.0913773, 0.0, 0.73328054, 0.0, 1.4535936, 1.2558699, 0.69942576, 0.858215, 0.0, 0.0, 1.6599627, 0.0, 0.9983125, 0.0, 0.0, 0.0, 0.0, 0.0, 1.9518784, 1.2970386, 1.5505292, 0.0, 0.0, 0.0, 1.5326306, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 23, 23, 24, 24, 25, 25, 29, 29], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, -1, -1, 22, -1, 24, -1, -1, -1, -1, -1, 26, 28, 30, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.8076923, 3.1923077, -0.0063149123, 1.0, 0.009676124, 0.7307692, 0.009020544, 1.0, 1.0, 1.0, 1.0, -0.008063907, 0.0074687838, 1.0, -0.018539906, 1.0, -0.006003531, 0.0006465796, -0.011326461, -0.01196138, 0.0052255136, 1.0, 1.0, 1.0, -0.010121953, 0.0015268767, 0.015298578, 1.0, -0.0073577366, 0.014957634, 0.00039080856], "split_indices": [121, 73, 1, 1, 0, 69, 0, 1, 0, 15, 50, 40, 115, 0, 0, 106, 0, 115, 0, 0, 0, 0, 0, 106, 12, 80, 0, 0, 0, 39, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1400.0, 670.0, 1242.0, 158.0, 576.0, 94.0, 1137.0, 105.0, 241.0, 335.0, 889.0, 248.0, 121.0, 120.0, 235.0, 100.0, 790.0, 99.0, 147.0, 101.0, 142.0, 93.0, 514.0, 276.0, 381.0, 133.0, 151.0, 125.0, 289.0, 92.0, 142.0, 147.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.00043318557, 0.017709693, -0.015416965, -0.05721611, 0.0441541, -0.004438887, -0.009341908, -0.016053824, 0.010339349, 0.09352369, -0.013884544, 0.0072360435, -0.018929329, 0.021682194, 0.028051352, 0.009626187, -0.0618971, -0.06915653, 0.0026611337, -0.005618257, 0.013116531, -0.01404615, 9.543982e-05, -0.00018177263, -0.018264884, -0.020253744, 0.007851118, 0.008242569, -0.058552194, -0.0012450838, -0.010932596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, 17, -1, 19, -1, 21, 23, 25, -1, -1, -1, -1, -1, -1, 27, -1, -1, 29, -1, -1], "loss_changes": [0.56601745, 1.9595735, 0.9231054, 4.281388, 2.0945666, 1.0516489, 0.0, 0.0, 0.0, 3.1886857, 1.776906, 0.0, 0.86212075, 0.0, 2.2409093, 0.0, 1.1554668, 1.8265419, 0.9663807, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.6791615, 0.0, 0.0, 0.72796965, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 12, 12, 14, 14, 16, 16, 17, 17, 18, 18, 25, 25, 28, 28], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, 18, -1, 20, -1, 22, 24, 26, -1, -1, -1, -1, -1, -1, 28, -1, -1, 30, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.009341908, -0.016053824, 0.010339349, -0.34615386, 1.0, 0.0072360435, -0.115384616, 0.021682194, 0.1923077, 0.009626187, -0.1923077, 1.0, 1.0, -0.005618257, 0.013116531, -0.01404615, 9.543982e-05, -0.00018177263, -0.018264884, 0.0, 0.007851118, 0.008242569, 1.0, -0.0012450838, -0.010932596], "split_indices": [126, 89, 64, 97, 97, 89, 0, 0, 0, 1, 109, 0, 1, 0, 1, 0, 1, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 989.0, 1078.0, 258.0, 731.0, 945.0, 133.0, 157.0, 101.0, 395.0, 336.0, 150.0, 795.0, 137.0, 258.0, 102.0, 234.0, 239.0, 556.0, 142.0, 116.0, 104.0, 130.0, 150.0, 89.0, 427.0, 129.0, 116.0, 311.0, 163.0, 148.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.004040983, 0.010158228, -0.037359234, 0.0035825523, 0.011783487, -0.0147361, 0.009944966, -0.0022159242, 0.0074332976, -0.013040001, 0.03155972, -0.0023801445, -0.008489018, -0.023249071, 0.009242475, 0.038694493, -0.0146665545, -0.0025317345, -0.002094128, -0.00016613833, 0.008523102, -0.050355624, 0.0057773767, -0.011726455, -0.0096927285, 0.049290765, -0.036386747, -0.012357621, 0.010293926, 0.010305143, 0.0004948008, -0.00014259365, -0.008417718], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, 13, 15, -1, 17, -1, 19, 21, -1, -1, -1, -1, 23, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.5252513, 1.27944, 4.0181413, 0.6986469, 0.0, 0.0, 0.0, 0.57543886, 0.0, 0.9129675, 1.2743288, 0.52383685, 0.0, 0.00095926225, 0.0, 0.44884762, 0.5829705, 0.0, 0.0, 0.0, 0.0, 0.7917264, 0.9320295, 0.0, 2.3216736, 0.59597373, 0.43106416, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 10, 10, 11, 11, 13, 13, 15, 15, 16, 16, 21, 21, 22, 22, 24, 24, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, 14, 16, -1, 18, -1, 20, 22, -1, -1, -1, -1, 24, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, 1.0, 0.011783487, -0.0147361, 0.009944966, 1.0, 0.0074332976, 1.0, 1.0, 1.0, -0.008489018, 1.0, 0.009242475, 1.0, 1.0, -0.0025317345, -0.002094128, -0.00016613833, 0.008523102, 1.0, 1.0, -0.011726455, 1.0, 1.0, 1.0, -0.012357621, 0.010293926, 0.010305143, 0.0004948008, -0.00014259365, -0.008417718], "split_indices": [0, 102, 122, 125, 0, 0, 0, 105, 0, 0, 126, 5, 0, 15, 0, 69, 137, 0, 0, 0, 0, 93, 106, 0, 12, 53, 126, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1807.0, 267.0, 1703.0, 104.0, 148.0, 119.0, 1574.0, 129.0, 1192.0, 382.0, 1038.0, 154.0, 201.0, 181.0, 239.0, 799.0, 106.0, 95.0, 128.0, 111.0, 291.0, 508.0, 110.0, 181.0, 250.0, 258.0, 90.0, 91.0, 113.0, 137.0, 149.0, 109.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.003905216, -0.019323166, 0.012541618, -0.0023985722, -0.02038012, -0.002107437, 0.015270998, -0.009091254, 0.011604816, 0.008213724, -0.02120745, 0.0878002, -0.031935398, -0.087147504, 0.046719827, 0.02171716, -0.0033434597, -0.015966091, -0.008594538, -0.15326872, 0.0015857645, -0.009194239, 0.10052961, 0.014621369, -0.007498699, -0.0007359443, -0.024948424, 0.0069401637, -0.009046956, 0.00659204, 0.013907166, 0.0073814685, -0.0029490825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, -1, 9, -1, -1, 11, -1, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, -1, 27, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.52617115, 3.3438926, 2.061548, 1.2159454, 0.0, 1.4626485, 0.0, 0.0, 2.8099763, 0.0, 3.319034, 4.8307686, 0.46488827, 2.5608711, 1.0981846, 0.0, 0.0, 0.7510047, 0.0, 3.2148695, 0.0, 1.1434351, 0.24810672, 0.7154544, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 8, 8, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 21, 21, 22, 22, 23, 23], "right_children": [2, 4, 6, 8, -1, 10, -1, -1, 12, -1, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, -1, 28, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, -0.5, -0.02038012, -0.42307693, 0.015270998, -0.009091254, -0.07692308, 0.008213724, 1.0, 1.0, 1.0, 1.0, 1.0, 0.02171716, -0.0033434597, 1.0, -0.008594538, 1.0, 0.0015857645, 0.34615386, 0.115384616, 1.0, -0.007498699, -0.0007359443, -0.024948424, 0.0069401637, -0.009046956, 0.00659204, 0.013907166, 0.0073814685, -0.0029490825], "split_indices": [39, 88, 88, 1, 0, 1, 0, 0, 1, 0, 115, 122, 15, 122, 81, 0, 0, 115, 0, 69, 0, 1, 1, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2075.0, 1071.0, 1004.0, 981.0, 90.0, 909.0, 95.0, 134.0, 847.0, 168.0, 741.0, 308.0, 539.0, 376.0, 365.0, 149.0, 159.0, 416.0, 123.0, 229.0, 147.0, 179.0, 186.0, 274.0, 142.0, 91.0, 138.0, 91.0, 88.0, 98.0, 88.0, 117.0, 157.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0035761204, -0.0029674694, 0.034298643, 0.004198908, -0.06309418, 0.014226481, -0.024259267, -0.054124974, 0.014700814, -0.011218268, -0.0016116969, 0.0024853032, -0.008449136, 0.0015085469, -0.015466228, 0.0021809766, 0.072926246, -0.010282656, 0.009763419, 0.013412449, 0.0007868634, 0.008833662, -0.06348591, -0.03566203, 0.106437124, -0.021162681, 0.0026354354, 0.0010162517, -0.012735776, -0.0010796384, 0.027186666, -0.014685704, 0.06892374, 0.017905926, -0.0072524804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, 17, 19, 21, -1, -1, -1, 23, 25, 27, 29, -1, -1, 31, -1, -1, -1, -1, 33, -1, -1], "loss_changes": [0.41674685, 0.7363924, 2.301308, 0.93530566, 0.41969913, 0.0, 0.6981205, 1.6212683, 0.94329077, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2670242, 0.9117429, 0.95806164, 0.0, 0.0, 0.0, 3.009653, 3.3139458, 1.6009033, 4.208474, 0.0, 0.0, 3.414179, 0.0, 0.0, 0.0, 0.0, 3.629793, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 15, 15, 16, 16, 17, 17, 21, 21, 22, 22, 23, 23, 24, 24, 27, 27, 32, 32], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, 18, 20, 22, -1, -1, -1, 24, 26, 28, 30, -1, -1, 32, -1, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.3461539, 1.0, -0.5, 3.1923077, 0.014226481, 1.0, 1.0, 1.0, -0.011218268, -0.0016116969, 0.0024853032, -0.008449136, 0.0015085469, -0.015466228, 0.88461536, -0.03846154, 0.23076923, 0.009763419, 0.013412449, 0.0007868634, -0.07692308, 1.0, 1.0, 1.0, -0.021162681, 0.0026354354, 1.0, -0.012735776, -0.0010796384, 0.027186666, -0.014685704, -0.26923078, 0.017905926, -0.0072524804], "split_indices": [74, 1, 122, 1, 1, 0, 127, 42, 42, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 69, 0, 59, 0, 0, 15, 0, 0, 0, 0, 1, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1709.0, 364.0, 1527.0, 182.0, 128.0, 236.0, 233.0, 1294.0, 89.0, 93.0, 130.0, 106.0, 138.0, 95.0, 1065.0, 229.0, 942.0, 123.0, 118.0, 111.0, 693.0, 249.0, 476.0, 217.0, 94.0, 155.0, 340.0, 136.0, 127.0, 90.0, 107.0, 233.0, 131.0, 102.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-8.8797206e-05, 0.01874818, -0.013962836, 0.012035741, -0.0029547578, -0.00025361587, -0.0145669235, 0.010205734, -0.026707498, -0.0148854, 0.07258014, -0.0865655, 0.047261503, 0.016513823, -0.062071953, 0.003967175, 0.01054885, -0.034861416, -0.01641216, -0.0044080876, 0.0097231185, 0.010268743, -0.018360607, -0.014069705, 0.005146379, 0.0073250546, -0.013142749, 0.008334498, -0.069412835, -0.00655999, 0.008955958, -0.013210984, 0.0008133433], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, -1, 7, 9, -1, -1, 11, 13, 15, 17, 19, 21, 23, -1, -1, 25, -1, -1, -1, -1, 27, -1, 29, -1, -1, -1, 31, -1, -1, -1, -1], "loss_changes": [0.5391546, 1.9295667, 2.145043, 0.0, 1.798408, 1.14668, 0.0, 0.0, 2.6034505, 1.3275326, 0.19493246, 1.3032386, 1.2004241, 1.6168278, 1.8920474, 0.0, 0.0, 2.0357897, 0.0, 0.0, 0.0, 0.0, 1.9886496, 0.0, 1.1525805, 0.0, 0.0, 0.0, 1.2397898, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 4, 4, 5, 5, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 22, 22, 24, 24, 28, 28], "right_children": [2, 4, 6, -1, 8, 10, -1, -1, 12, 14, 16, 18, 20, 22, 24, -1, -1, 26, -1, -1, -1, -1, 28, -1, 30, -1, -1, -1, 32, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 4.0, 0.012035741, 1.0, 1.0, -0.0145669235, 0.010205734, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 0.003967175, 0.01054885, 0.26923078, -0.01641216, -0.0044080876, 0.0097231185, 0.010268743, 1.0, -0.014069705, 1.0, 0.0073250546, -0.013142749, 0.008334498, 1.0, -0.00655999, 0.008955958, -0.013210984, 0.0008133433], "split_indices": [53, 81, 0, 0, 5, 61, 0, 0, 12, 108, 124, 15, 71, 0, 111, 0, 0, 1, 0, 0, 0, 0, 115, 0, 2, 0, 0, 0, 137, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 875.0, 1188.0, 154.0, 721.0, 1076.0, 112.0, 133.0, 588.0, 896.0, 180.0, 325.0, 263.0, 538.0, 358.0, 90.0, 90.0, 195.0, 130.0, 93.0, 170.0, 155.0, 383.0, 165.0, 193.0, 92.0, 103.0, 128.0, 255.0, 105.0, 88.0, 141.0, 114.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0072315168, -0.029068943, 0.0062926016, -0.06060622, 0.0011379676, 0.014441574, -0.009484041, 0.0068691815, -0.10892044, -0.01095172, 0.033871986, -0.0069371974, 0.07257565, -0.017160365, -0.0031810116, -0.017721971, 0.012683409, 0.040300813, -0.023124682, 0.022944314, -0.009628923, -0.0111209145, 0.00965401, 0.01289337, -0.0029596642, -0.05275895, 0.03614384, 0.006365478, -0.009031504, -0.01599955, -0.024605393, -0.0025459726, 0.009330753, -0.06716684, 0.008157882, -0.012817119, -0.0029090282], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, 19, -1, -1, 21, -1, 23, 25, -1, 27, -1, -1, -1, -1, 29, 31, -1, -1, -1, 33, -1, -1, 35, -1, -1, -1], "loss_changes": [0.6092699, 0.751636, 1.0499414, 2.4113166, 1.4597417, 1.465303, 0.0, 0.0, 1.3582191, 0.0, 1.4916444, 0.6591408, 4.0877867, 0.0, 0.0, 2.1364074, 0.0, 1.3629472, 1.1275954, 0.0, 1.2298995, 0.0, 0.0, 0.0, 0.0, 1.29217, 0.7535985, 0.0, 0.0, 0.0, 1.5320611, 0.0, 0.0, 0.5621258, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 11, 11, 12, 12, 15, 15, 17, 17, 18, 18, 20, 20, 25, 25, 26, 26, 30, 30, 33, 33], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, 20, -1, -1, 22, -1, 24, 26, -1, 28, -1, -1, -1, -1, 30, 32, -1, -1, -1, 34, -1, -1, 36, -1, -1, -1], "split_conditions": [-0.1923077, 1.0, 1.0, 0.0, 0.0, 1.0, -0.009484041, 0.0068691815, 1.0, -0.01095172, 1.0, 0.0, 0.07692308, -0.017160365, -0.0031810116, 1.0, 0.012683409, 1.0, 1.0, 0.022944314, 1.0, -0.0111209145, 0.00965401, 0.01289337, -0.0029596642, 0.07692308, 1.0, 0.006365478, -0.009031504, -0.01599955, 1.0, -0.0025459726, 0.009330753, 1.0, 0.008157882, -0.012817119, -0.0029090282], "split_indices": [1, 69, 41, 0, 0, 105, 0, 0, 97, 0, 50, 0, 1, 0, 0, 106, 0, 122, 109, 0, 126, 0, 0, 0, 0, 1, 17, 0, 0, 0, 0, 0, 0, 81, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2063.0, 789.0, 1274.0, 386.0, 403.0, 1179.0, 95.0, 105.0, 281.0, 92.0, 311.0, 862.0, 317.0, 155.0, 126.0, 200.0, 111.0, 220.0, 642.0, 109.0, 208.0, 110.0, 90.0, 97.0, 123.0, 428.0, 214.0, 109.0, 99.0, 89.0, 339.0, 103.0, 111.0, 242.0, 97.0, 93.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0032997783, 0.007044385, -0.0069849784, 0.020004276, -0.0047247973, -0.040080864, 0.040289436, 0.008714507, -0.010157315, -0.01458084, 0.011795393, 0.09780076, -0.027192887, 0.0082618, -0.0053300573, 0.032269474, 0.023584615, -0.074074104, 0.0078881765, -0.007834061, 0.015284681, 0.015182912, -0.0033272249, -0.0137405265, -0.0018193655, -0.027672166, 0.057242543, 0.0025783645, -0.008647358, -0.0024614385, 0.105064236, 0.017032674, 0.0041161343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, -1, 19, 21, -1, 23, -1, -1, 25, -1, -1, -1, -1, 27, 29, -1, -1, -1, 31, -1, -1], "loss_changes": [0.5681023, 0.30093646, 0.0, 1.1444875, 1.3458283, 3.9599454, 2.7244606, 0.94245195, 0.0, 0.0, 0.0, 3.428544, 1.6062497, 0.0, 1.1483864, 2.0138898, 0.0, 0.7927296, 0.0, 0.0, 1.0724146, 0.0, 0.0, 0.0, 0.0, 0.9241237, 1.1782761, 0.0, 0.0, 0.0, 0.79238725, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 14, 14, 15, 15, 17, 17, 20, 20, 25, 25, 26, 26, 30, 30], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, -1, 20, 22, -1, 24, -1, -1, 26, -1, -1, -1, -1, 28, 30, -1, -1, -1, 32, -1, -1], "split_conditions": [1.0, 1.0, -0.0069849784, 1.0, 1.0, 1.0, 1.0, 1.0, -0.010157315, -0.01458084, 0.011795393, 1.0, 1.0, 0.0082618, 1.0, 1.0, 0.023584615, -0.15384616, 0.0078881765, -0.007834061, 1.0, 0.015182912, -0.0033272249, -0.0137405265, -0.0018193655, 1.0, 1.0, 0.0025783645, -0.008647358, -0.0024614385, 1.0, 0.017032674, 0.0041161343], "split_indices": [117, 126, 0, 89, 64, 97, 97, 89, 0, 0, 0, 61, 115, 0, 59, 17, 0, 1, 0, 0, 81, 0, 0, 0, 0, 69, 17, 0, 0, 0, 124, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2074.0, 1973.0, 101.0, 939.0, 1034.0, 237.0, 702.0, 908.0, 126.0, 142.0, 95.0, 379.0, 323.0, 145.0, 763.0, 257.0, 122.0, 224.0, 99.0, 168.0, 595.0, 91.0, 166.0, 105.0, 119.0, 294.0, 301.0, 154.0, 140.0, 111.0, 190.0, 94.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0033704787, -0.016477225, 0.015902575, 0.010712163, -0.06838425, 0.09190994, 0.0028245249, -0.044383377, 0.07225034, -0.026192915, -0.014962497, 0.0017855464, 0.01692195, 0.01745786, -0.007397932, 0.0017604433, -0.012668654, 0.00022596498, 0.017411795, 0.0030817369, -0.00813442, 0.0008954316, 0.07723392, 0.019672606, -0.00799585, 0.018882703, -0.0012859575, 0.008679039, -0.006527581, 0.0068272217, -0.03520085, 0.0022226493, -0.010083212], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, -1, -1, 21, -1, -1, -1, -1, -1, -1, -1, 23, 25, 27, -1, -1, -1, -1, 29, -1, 31, -1, -1], "loss_changes": [0.51413184, 1.1290563, 1.2594334, 1.7800015, 0.9426044, 1.064872, 1.2149317, 1.413197, 1.7681859, 0.56909865, 0.0, 0.0, 0.0, 0.898953, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.079446, 1.9806018, 1.0146539, 0.0, 0.0, 0.0, 0.0, 0.8900731, 0.0, 1.1307089, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 21, 21, 22, 22, 23, 23, 28, 28, 30, 30], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, -1, -1, 22, -1, -1, -1, -1, -1, -1, -1, 24, 26, 28, -1, -1, -1, -1, 30, -1, 32, -1, -1], "split_conditions": [-0.1923077, 1.0, -0.03846154, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.014962497, 0.0017855464, 0.01692195, 1.0, -0.007397932, 0.0017604433, -0.012668654, 0.00022596498, 0.017411795, 0.0030817369, -0.00813442, 1.0, 1.0, 0.0, -0.00799585, 0.018882703, -0.0012859575, 0.008679039, 1.0, 0.0068272217, 1.0, 0.0022226493, -0.010083212], "split_indices": [1, 121, 1, 69, 12, 124, 64, 126, 50, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 124, 0, 0, 0, 0, 0, 26, 0, 106, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 800.0, 1267.0, 525.0, 275.0, 186.0, 1081.0, 277.0, 248.0, 181.0, 94.0, 95.0, 91.0, 908.0, 173.0, 158.0, 119.0, 147.0, 101.0, 89.0, 92.0, 711.0, 197.0, 577.0, 134.0, 88.0, 109.0, 162.0, 415.0, 115.0, 300.0, 160.0, 140.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011636559, -0.020880787, 0.01113637, -0.007862659, -0.008295271, 0.084475204, -0.0015361467, 0.020797746, -0.009419341, 0.002847834, 0.013929315, 0.009697514, -0.0060951184, 0.047959674, -0.02742229, 0.031455897, -0.026084332, -0.010594114, 0.118172504, -0.008660632, 0.0030446504, 0.012422871, 0.009572894, -0.0060659116, 0.0054341005, 0.024758158, -0.0007707199, 0.011947977, -0.0246858, 0.0027673973, -0.0017052848, -0.011631254, 0.040076543, 0.011751336, -0.0038175364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, 5, 7, -1, 9, 11, 13, -1, -1, -1, 15, -1, 17, 19, 21, 23, -1, 25, -1, -1, 27, -1, -1, 29, -1, -1, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [0.502504, 0.64321554, 1.1858988, 1.6280727, 0.0, 0.5770905, 0.726184, 0.64701617, 0.0, 0.0, 0.0, 0.712378, 0.0, 3.4146364, 0.60963386, 0.69606346, 0.37705123, 0.0, 3.5349255, 0.0, 0.0, 1.7440327, 0.0, 0.0, 0.09051938, 0.0, 0.0, 0.0, 1.9344722, 0.0, 0.0, 0.0, 1.1573797, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 6, 6, 7, 7, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 21, 21, 24, 24, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, 12, 14, -1, -1, -1, 16, -1, 18, 20, 22, 24, -1, 26, -1, -1, 28, -1, -1, 30, -1, -1, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [-0.1923077, 1.0, -0.03846154, 1.0, -0.008295271, 1.0, 1.0, -0.34615386, -0.009419341, 0.002847834, 0.013929315, 1.0, -0.0060951184, 1.0, 1.0, 1.0, 1.0, -0.010594114, 1.0, -0.008660632, 0.0030446504, 1.0, 0.009572894, -0.0060659116, 1.0, 0.024758158, -0.0007707199, 0.011947977, 1.0, 0.0027673973, -0.0017052848, -0.011631254, 1.0, 0.011751336, -0.0038175364], "split_indices": [1, 64, 1, 7, 0, 124, 64, 1, 0, 0, 0, 109, 0, 89, 106, 15, 17, 0, 53, 0, 0, 26, 0, 0, 50, 0, 0, 0, 137, 0, 0, 0, 17, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2072.0, 796.0, 1276.0, 658.0, 138.0, 188.0, 1088.0, 494.0, 164.0, 93.0, 95.0, 915.0, 173.0, 316.0, 178.0, 569.0, 346.0, 99.0, 217.0, 88.0, 90.0, 439.0, 130.0, 165.0, 181.0, 107.0, 110.0, 113.0, 326.0, 91.0, 90.0, 135.0, 191.0, 96.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009954597, 0.008472984, -0.020602684, 0.016472243, -0.00569327, -8.1466336e-05, -0.012890908, 0.00974014, 0.009480941, -0.033702496, 0.057122074, 0.02035106, -0.009606586, 0.0053488985, -0.0914265, -0.0016278139, 0.012982986, 0.054188065, -0.006579246, -0.015887974, -0.0022712431, -0.003853361, 0.0980469, 0.009113244, -0.028795803, 0.019874787, 0.033535317, -0.011565852, 0.0032457642, -0.002419696, 0.009248304, 0.0053089648, -0.029665492, -0.011187993, 0.003624778], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, -1, -1, 19, -1, -1, 21, 23, -1, -1, -1, 25, -1, 27, -1, 29, -1, 31, -1, -1, -1, 33, -1, -1], "loss_changes": [0.38652316, 0.7345685, 1.5069082, 0.6597444, 0.0, 1.0962479, 0.0, 1.293349, 0.0, 1.8068622, 1.1260581, 0.9540694, 0.0, 0.0, 1.0011564, 0.0, 0.0, 1.8869325, 1.2655863, 0.0, 0.0, 0.0, 2.0463595, 0.0, 1.3220286, 0.0, 0.653412, 0.0, 0.5692275, 0.0, 0.0, 0.0, 1.1325759, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 14, 14, 17, 17, 18, 18, 22, 22, 24, 24, 26, 26, 28, 28, 32, 32], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, -1, -1, 20, -1, -1, 22, 24, -1, -1, -1, 26, -1, 28, -1, 30, -1, 32, -1, -1, -1, 34, -1, -1], "split_conditions": [1.0, 1.0, 3.0, 4.0, -0.00569327, 0.115384616, -0.012890908, 1.0, 0.009480941, 1.0, 1.0, -0.03846154, -0.009606586, 0.0053488985, 1.0, -0.0016278139, 0.012982986, 1.0, -1.0, -0.015887974, -0.0022712431, -0.003853361, 1.0, 0.009113244, 1.0, 0.019874787, 1.0, -0.011565852, 0.6923077, -0.002419696, 0.009248304, 0.0053089648, 1.0, -0.011187993, 0.003624778], "split_indices": [121, 73, 0, 0, 0, 1, 0, 88, 0, 69, 17, 1, 0, 0, 53, 0, 0, 2, 0, 0, 0, 0, 93, 0, 137, 0, 69, 0, 1, 0, 0, 0, 80, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2082.0, 1404.0, 678.0, 1251.0, 153.0, 570.0, 108.0, 1152.0, 99.0, 359.0, 211.0, 1047.0, 105.0, 143.0, 216.0, 105.0, 106.0, 464.0, 583.0, 109.0, 107.0, 149.0, 315.0, 108.0, 475.0, 123.0, 192.0, 128.0, 347.0, 97.0, 95.0, 138.0, 209.0, 93.0, 116.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.008330786, 0.014891832, -0.018564614, 0.021587776, -0.004545281, -0.007694316, 0.004746256, 0.011956471, 0.014835407, 0.03730691, -0.0061403248, 0.01822822, -0.0071127987, 0.0070907795, 0.0008552276, 0.052938756, -0.012175167, -0.008903757, 0.1300682, -0.009911115, 0.009558823, -0.009212799, 0.039841853, 0.024616545, -0.0043535135, 0.014193683, -0.033712428, 0.014999959, -0.0060301526, -0.09782104, 0.008107662, -0.016822632, 0.0022155268], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, 13, -1, 15, -1, -1, -1, 17, 19, 21, 23, -1, 25, -1, 27, -1, -1, -1, 29, -1, -1, 31, -1, -1, -1], "loss_changes": [0.36262935, 0.6675143, 0.5484247, 1.8155147, 0.0, 0.0, 0.6203148, 0.72013897, 0.0, 0.18647271, 0.0, 1.3560836, 0.0, 0.0, 0.0, 2.8619275, 1.2942842, 1.35092, 5.3813505, 0.0, 3.1390326, 0.0, 2.3166296, 0.0, 0.0, 0.0, 3.0392537, 0.0, 0.0, 2.238446, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 9, 9, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 22, 22, 26, 26, 29, 29], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, 14, -1, 16, -1, -1, -1, 18, 20, 22, 24, -1, 26, -1, 28, -1, -1, -1, 30, -1, -1, 32, -1, -1, -1], "split_conditions": [1.0, 1.3461539, 0.0, 0.9230769, -0.004545281, -0.007694316, 1.0, 0.61538464, 0.014835407, 1.0, -0.0061403248, 1.0, -0.0071127987, 0.0070907795, 0.0008552276, -0.1923077, 1.0, 1.0, 0.23076923, -0.009911115, -0.3846154, -0.009212799, 1.0, 0.024616545, -0.0043535135, 0.014193683, 1.0, 0.014999959, -0.0060301526, 1.0, 0.008107662, -0.016822632, 0.0022155268], "split_indices": [80, 1, 0, 1, 0, 0, 58, 1, 0, 12, 0, 69, 0, 0, 0, 1, 89, 53, 1, 0, 1, 0, 115, 0, 0, 0, 115, 0, 0, 116, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2055.0, 1652.0, 403.0, 1487.0, 165.0, 115.0, 288.0, 1382.0, 105.0, 193.0, 95.0, 1285.0, 97.0, 89.0, 104.0, 600.0, 685.0, 333.0, 267.0, 137.0, 548.0, 123.0, 210.0, 160.0, 107.0, 135.0, 413.0, 100.0, 110.0, 265.0, 148.0, 167.0, 98.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.008499429, -0.016408931, 0.028012542, -0.061342023, -0.009311025, -0.011736922, 0.008831277, 0.0012429844, -0.016662067, -0.023178138, 0.056100886, 0.0071951016, -0.010410949, -0.013183935, -0.00906549, 0.014258119, -0.004976291, -0.022766054, 0.00841502, -0.0006795042, -0.060048636, -0.009860418, 0.020796927, -0.021441728, -0.013321492, 0.07322889, -0.017885383, -0.00960639, 0.008287701, 0.013078307, 0.0009343714, -0.009899775, 0.0039913226], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, -1, 13, 15, -1, -1, 17, -1, -1, -1, 19, -1, 21, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.6001088, 0.54473394, 0.8868538, 1.8096186, 1.3379344, 1.7238951, 0.0, 0.0, 0.0, 0.8207159, 2.3620248, 0.0, 0.0, 0.9886272, 0.0, 0.0, 0.0, 0.7946229, 0.0, 1.2744621, 1.0140755, 0.0, 1.0080103, 1.8293556, 0.0, 0.7758168, 1.3408203, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 13, 13, 17, 17, 19, 19, 20, 20, 22, 22, 23, 23, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, -1, 14, 16, -1, -1, 18, -1, -1, -1, 20, -1, 22, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, -0.5, 0.46153846, 1.0, 1.0, -0.07692308, 0.008831277, 0.0012429844, -0.016662067, 1.3461539, 1.0, 0.0071951016, -0.010410949, 1.0384616, -0.00906549, 0.014258119, -0.004976291, 1.0, 0.00841502, 1.0, 1.0, -0.009860418, 1.0, 1.0, -0.013321492, 1.0, 1.0, -0.00960639, 0.008287701, 0.013078307, 0.0009343714, -0.009899775, 0.0039913226], "split_indices": [74, 1, 1, 42, 42, 1, 0, 0, 0, 1, 126, 0, 0, 1, 0, 0, 0, 121, 0, 81, 50, 0, 53, 97, 0, 16, 17, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1708.0, 370.0, 233.0, 1475.0, 223.0, 147.0, 137.0, 96.0, 1217.0, 258.0, 117.0, 106.0, 1060.0, 157.0, 142.0, 116.0, 965.0, 95.0, 606.0, 359.0, 109.0, 497.0, 235.0, 124.0, 211.0, 286.0, 137.0, 98.0, 111.0, 100.0, 119.0, 167.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025547938, 0.004765507, -0.052934058, -0.00047125222, 0.009155338, -0.013661297, 0.005475702, -0.039561026, 0.008791323, 0.001689114, -0.082355425, 0.002644934, 0.0077048554, -0.0012781656, -0.015044888, -0.023561323, 0.02795595, 0.008311795, -0.06511443, 0.0060773594, 0.011201578, -0.026556471, 0.009868468, -0.00019204243, -0.013733613, 0.059068274, -0.03798676, -0.010099999, 0.0046723834, 2.2184016e-05, 0.014393099, -0.015488622, 0.007975369], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, 13, 15, -1, -1, -1, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, 29, 31, -1, -1, -1, -1, -1, -1], "loss_changes": [0.7645046, 0.822622, 2.3700173, 0.61805695, 0.0, 0.0, 0.0, 0.7899788, 0.57895875, 0.0, 0.8811779, 0.83974665, 0.0, 0.0, 0.0, 0.8237938, 1.1843867, 1.1092036, 1.2322738, 1.193184, 0.0, 1.385632, 0.0, 0.0, 0.0, 1.1585771, 3.840099, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8, 10, 10, 11, 11, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 21, 21, 25, 25, 26, 26], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, 14, 16, -1, -1, -1, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, 30, 32, -1, -1, -1, -1, -1, -1], "split_conditions": [3.0, 1.0, 1.0, -1.0, 0.009155338, -0.013661297, 0.005475702, 1.0, 1.0, 0.001689114, -0.115384616, 1.0, 0.0077048554, -0.0012781656, -0.015044888, 1.0, 1.0, 0.53846157, 1.0, 1.0, 0.011201578, 1.0, 0.009868468, -0.00019204243, -0.013733613, 1.0, 1.0, -0.010099999, 0.0046723834, 2.2184016e-05, 0.014393099, -0.015488622, 0.007975369], "split_indices": [0, 102, 122, 0, 0, 0, 0, 122, 125, 0, 1, 111, 0, 0, 0, 12, 105, 1, 39, 39, 0, 93, 0, 0, 0, 83, 12, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2073.0, 1810.0, 263.0, 1707.0, 103.0, 148.0, 115.0, 327.0, 1380.0, 141.0, 186.0, 1266.0, 114.0, 92.0, 94.0, 622.0, 644.0, 352.0, 270.0, 511.0, 133.0, 254.0, 98.0, 144.0, 126.0, 232.0, 279.0, 126.0, 128.0, 137.0, 95.0, 140.0, 139.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0059285285, 0.008780091, 0.0014680661, -0.0062341937, 0.006668443, 0.0022468662, 0.0069722324, 0.00761429, -0.008826481, -0.00086009374, 0.008242804, -0.008961773, 0.009799521, -0.041674837, 0.007487166, 0.005346301, -0.103070326, 0.06815354, -0.024414973, -0.008257508, 0.007456103, -0.000655726, -0.019282745, -0.0021316328, 0.0134616885, 0.029153083, -0.07139538, -0.006164881, 0.008570039, -0.0025119458, -0.012108763], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, -1, 3, -1, 5, 7, -1, 9, -1, 11, -1, 13, -1, 15, 17, 19, 21, 23, 25, -1, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.7563054, 0.0, 0.6517258, 0.0, 0.50629616, 0.824427, 0.0, 1.015669, 0.0, 1.1524861, 0.0, 0.71566665, 0.0, 1.2846644, 1.7128177, 1.5335346, 1.6719072, 1.8136725, 1.4596567, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.3914773, 0.710562, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 7, 7, 9, 9, 11, 11, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 25, 25, 26, 26], "right_children": [2, -1, 4, -1, 6, 8, -1, 10, -1, 12, -1, 14, -1, 16, 18, 20, 22, 24, 26, -1, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [-0.5769231, 0.008780091, -0.5, -0.0062341937, 1.0, 1.0, 0.0069722324, 1.0, -0.008826481, 3.1923077, 0.008242804, -0.15384616, 0.009799521, 1.0, 0.1923077, 1.0, -0.34615386, 1.0, 1.0, -0.008257508, 0.007456103, -0.000655726, -0.019282745, -0.0021316328, 0.0134616885, 0.53846157, 1.0, -0.006164881, 0.008570039, -0.0025119458, -0.012108763], "split_indices": [1, 0, 1, 0, 90, 43, 0, 119, 0, 1, 0, 1, 0, 109, 1, 13, 1, 59, 12, 0, 0, 0, 0, 0, 0, 1, 115, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2071.0, 107.0, 1964.0, 148.0, 1816.0, 1697.0, 119.0, 1602.0, 95.0, 1439.0, 163.0, 1330.0, 109.0, 445.0, 885.0, 252.0, 193.0, 305.0, 580.0, 111.0, 141.0, 93.0, 100.0, 130.0, 175.0, 271.0, 309.0, 104.0, 167.0, 160.0, 149.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0043122377, 0.006792167, -0.0042758705, 0.019885173, -0.005232018, -0.04251307, 0.041724563, 0.006509356, -0.008714861, 0.00861448, -0.011853819, 0.094577886, -0.020320635, 0.018362876, -0.0071285567, 0.04429424, 0.020401873, -0.06089999, 0.0071085365, 0.0022373728, 0.011884863, 0.01229972, -0.004007531, -0.01047445, 0.0005113051, -0.040071912, 0.05811482, 0.0032069464, -0.07379318, 0.010665137, 0.016187837, -0.012750148, 0.0017012547, -0.0051834253, 0.007908556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, 15, 17, 19, -1, 21, -1, 23, -1, 25, -1, -1, -1, -1, -1, 27, 29, -1, 31, 33, -1, -1, -1, -1, -1], "loss_changes": [0.2425703, 0.31077224, 0.0, 1.2877886, 0.98970616, 2.3964012, 2.2955067, 0.82992923, 0.0, 0.0, 0.0, 2.0801651, 1.1943612, 1.2655191, 0.0, 1.7197946, 0.0, 0.64543134, 0.0, 1.5910629, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9317238, 1.4278283, 0.0, 1.2729027, 0.8509707, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 11, 11, 12, 12, 13, 13, 15, 15, 17, 17, 19, 19, 25, 25, 26, 26, 28, 28, 29, 29], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, 16, 18, 20, -1, 22, -1, 24, -1, 26, -1, -1, -1, -1, -1, 28, 30, -1, 32, 34, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, -0.0042758705, 1.0, 1.0, 1.0, 1.0, 1.0, -0.008714861, 0.00861448, -0.011853819, 1.0, 1.0, 1.0, -0.0071285567, 1.0, 0.020401873, 1.0, 0.0071085365, 1.0, 0.011884863, 0.01229972, -0.004007531, -0.01047445, 0.0005113051, 1.0, 0.0, 0.0032069464, 1.1538461, 1.0, 0.016187837, -0.012750148, 0.0017012547, -0.0051834253, 0.007908556], "split_indices": [117, 126, 0, 89, 64, 15, 97, 61, 0, 0, 0, 61, 115, 113, 0, 39, 0, 116, 0, 15, 0, 0, 0, 0, 0, 53, 1, 0, 1, 5, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1974.0, 104.0, 945.0, 1029.0, 245.0, 700.0, 900.0, 129.0, 91.0, 154.0, 378.0, 322.0, 781.0, 119.0, 259.0, 119.0, 223.0, 99.0, 673.0, 108.0, 134.0, 125.0, 134.0, 89.0, 383.0, 290.0, 122.0, 261.0, 199.0, 91.0, 164.0, 97.0, 104.0, 95.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [0.0010478569, -0.0050636395, 0.038896255, -0.05875185, 0.0033856512, 0.011362597, -0.0036359709, -0.012011657, -0.0011825872, -0.018326841, 0.024452943, -0.0648622, 0.02185753, 0.054678824, -0.0065641813, -0.0012739674, -0.011758728, -0.009860843, 0.013216701, 0.11617951, -0.009384382, -0.008660022, 0.056938577, 0.029330641, -0.0110066375, 0.0021431537, 0.021479473, 0.007336676, -0.008394238, 0.015580249, -0.0024180508, 0.009895024, -0.0031586476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, -1, 11, 13, 15, 17, 19, 21, -1, -1, 23, -1, 25, 27, -1, 29, 31, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.47418636, 0.8006524, 1.6027994, 0.6911034, 0.69757074, 0.0, 0.0, 0.0, 0.0, 1.4043658, 0.72564065, 0.9563606, 1.4100318, 1.5444534, 1.9415188, 0.0, 0.0, 1.229215, 0.0, 1.8687181, 1.184594, 0.0, 1.7082072, 0.95423084, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 19, 19, 20, 20, 22, 22, 23, 23], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, -1, 12, 14, 16, 18, 20, 22, -1, -1, 24, -1, 26, 28, -1, 30, 32, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.3076923, 1.0, 1.0, 1.0, 1.0, 0.011362597, -0.0036359709, -0.012011657, -0.0011825872, 1.0, 1.0, 1.0, 0.0, 1.0, -0.34615386, -0.0012739674, -0.011758728, 1.0, 0.013216701, 1.0, 1.0, -0.008660022, 1.0, 1.0, -0.0110066375, 0.0021431537, 0.021479473, 0.007336676, -0.008394238, 0.015580249, -0.0024180508, 0.009895024, -0.0031586476], "split_indices": [1, 26, 115, 116, 124, 0, 0, 0, 0, 15, 15, 93, 1, 126, 1, 0, 0, 0, 0, 39, 53, 0, 5, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2050.0, 1765.0, 285.0, 240.0, 1525.0, 143.0, 142.0, 104.0, 136.0, 751.0, 774.0, 348.0, 403.0, 392.0, 382.0, 175.0, 173.0, 313.0, 90.0, 200.0, 192.0, 169.0, 213.0, 225.0, 88.0, 102.0, 98.0, 91.0, 101.0, 96.0, 117.0, 105.0, 120.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0029298055, 0.006326218, -0.0060372823, 0.011263692, -0.0427175, 0.015273624, -0.0062222634, -0.0008514301, -0.007808668, 0.05395462, 0.0037110436, 0.018721635, 0.017406704, -0.0071340506, 0.008928332, -0.008251838, 0.010267679, -0.020861095, 0.057038177, 0.019160334, -0.05725886, 0.000890935, 0.010860475, -0.0244546, 0.05568342, -0.027826535, -0.11093938, 0.005784131, -0.008582785, -0.0046924683, 0.012836418, 0.012831226, -0.009074928, -0.016808515, -0.0053793574, 0.0076261656, -0.005258137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, 17, -1, -1, -1, 19, 21, 23, 25, -1, -1, 27, 29, 31, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1], "loss_changes": [0.4433336, 0.47389156, 0.0, 0.5239323, 0.21654305, 0.75406694, 0.0, 0.0, 0.0, 1.6419841, 1.2045953, 2.5498865, 0.0, 1.0147909, 0.0, 0.0, 0.0, 1.3823994, 0.50381285, 0.72001445, 0.7852311, 0.0, 0.0, 1.0404583, 1.8345788, 0.82121396, 0.57475233, 0.0, 0.0, 0.0, 0.0, 0.8090841, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 13, 13, 17, 17, 18, 18, 19, 19, 20, 20, 23, 23, 24, 24, 25, 25, 26, 26, 31, 31], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, 18, -1, -1, -1, 20, 22, 24, 26, -1, -1, 28, 30, 32, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.0, -0.0060372823, 1.0, 0.115384616, 0.0, -0.0062222634, -0.0008514301, -0.007808668, 1.0, 1.0, 1.0, 0.017406704, 1.0, 0.008928332, -0.008251838, 0.010267679, 1.0, 1.0, 1.0, 1.0, 0.000890935, 0.010860475, 0.115384616, 1.0, 1.0, 1.0, 0.005784131, -0.008582785, -0.0046924683, 0.012836418, 1.0, -0.009074928, -0.016808515, -0.0053793574, 0.0076261656, -0.005258137], "split_indices": [1, 88, 0, 14, 1, 0, 0, 0, 0, 58, 125, 13, 0, 42, 0, 0, 0, 13, 69, 111, 0, 0, 0, 1, 81, 115, 59, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1957.0, 105.0, 1778.0, 179.0, 1686.0, 92.0, 91.0, 88.0, 388.0, 1298.0, 300.0, 88.0, 1152.0, 146.0, 136.0, 164.0, 949.0, 203.0, 452.0, 497.0, 105.0, 98.0, 206.0, 246.0, 321.0, 176.0, 88.0, 118.0, 102.0, 144.0, 195.0, 126.0, 88.0, 88.0, 99.0, 96.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007316709, -0.0036182445, 0.0059702094, -0.015461103, 0.009439263, -0.00084712217, -0.016842073, -0.0040436173, 0.013165508, -0.017233731, 0.047352806, 0.021481555, -0.033976495, -0.047266427, 0.020187194, -0.0058369343, 0.012211349, -0.03579185, 0.09716429, -0.098774396, 0.030156797, -0.11373849, 0.0075488524, 0.071305655, -0.005177185, 0.0014478419, -0.010074285, -0.002096881, 0.020166667, -0.016511243, -0.0028907717, 0.009923053, -0.0029049232, -0.007028024, -0.017023418, 0.003660119, 0.01041641], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, -1, 25, 27, 29, 31, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.35970858, 0.30432802, 0.0, 2.3068795, 1.5423611, 0.7440229, 0.0, 0.6440874, 0.0, 0.7900675, 1.8890228, 1.9722466, 1.6124128, 3.1823125, 1.1513504, 0.0, 0.0, 0.84566224, 2.4196577, 0.89451945, 0.79746854, 0.62116575, 0.0, 0.20868087, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 14, 14, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 23, 23], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, -1, 26, 28, 30, 32, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0059702094, 1.0, 1.0, 1.0, -0.016842073, 1.0, 0.013165508, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -0.0058369343, 0.012211349, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0075488524, 1.0, -0.005177185, 0.0014478419, -0.010074285, -0.002096881, 0.020166667, -0.016511243, -0.0028907717, 0.009923053, -0.0029049232, -0.007028024, -0.017023418, 0.003660119, 0.01041641], "split_indices": [114, 39, 0, 88, 88, 50, 0, 108, 0, 106, 109, 93, 137, 116, 12, 0, 0, 69, 122, 59, 111, 121, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1968.0, 94.0, 1032.0, 936.0, 942.0, 90.0, 843.0, 93.0, 703.0, 239.0, 455.0, 388.0, 390.0, 313.0, 99.0, 140.0, 259.0, 196.0, 193.0, 195.0, 253.0, 137.0, 183.0, 130.0, 146.0, 113.0, 92.0, 104.0, 99.0, 94.0, 90.0, 105.0, 143.0, 110.0, 89.0, 94.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [0.0005301582, 0.0035645554, -0.0060250643, -0.0029120117, 0.038771607, 0.0042955545, -0.0075372094, 0.009759516, -0.0025289643, -0.0054522757, 0.051303443, 0.0045650043, -0.010237163, 0.015184872, -0.007860458, -0.006413102, 0.011785909, -0.010202209, 0.008716938, -0.06535949, 0.034041263, 0.002645715, -0.017204171, 0.014914478, 0.008392037, -0.00426061, 0.006816186], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, 17, -1, -1, 19, 21, 23, -1, -1, -1, 25, -1, -1], "loss_changes": [0.38011643, 0.44760504, 0.0, 0.8659087, 1.149335, 0.69100326, 0.0, 0.0, 0.0, 1.2126149, 3.3829646, 1.4079305, 0.0, 0.0, 0.0, 1.4928584, 0.0, 0.0, 1.6714584, 2.2235117, 1.9603374, 0.0, 0.0, 0.0, 1.6551453, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 9, 9, 10, 10, 11, 11, 15, 15, 18, 18, 19, 19, 20, 20, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, 18, -1, -1, 20, 22, 24, -1, -1, -1, 26, -1, -1], "split_conditions": [1.0, 1.2692307, -0.0060250643, 1.0, 1.0, 1.0, -0.0075372094, 0.009759516, -0.0025289643, 1.0, -0.03846154, 1.0, -0.010237163, 0.015184872, -0.007860458, 1.0, 0.011785909, -0.010202209, 1.0, 1.0, -0.3846154, 0.002645715, -0.017204171, 0.014914478, 1.0, -0.00426061, 0.006816186], "split_indices": [52, 1, 0, 40, 115, 64, 0, 0, 0, 119, 1, 90, 0, 0, 0, 89, 0, 0, 5, 108, 1, 0, 0, 0, 115, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1963.0, 98.0, 1658.0, 305.0, 1508.0, 150.0, 159.0, 146.0, 1249.0, 259.0, 1132.0, 117.0, 146.0, 113.0, 1032.0, 100.0, 141.0, 891.0, 227.0, 664.0, 122.0, 105.0, 121.0, 543.0, 293.0, 250.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "27", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022815894, -0.008133875, 0.033726215, -0.0033524209, -0.008807976, 0.072665215, -0.003214253, -0.019315457, 0.01589588, 0.012866613, 0.0018490375, -0.0040308405, -0.009971671, -0.004014746, 0.015157553, -0.041044652, 0.04977517, -0.009740568, 0.016280148, -0.08724004, 0.009176931, 0.012961322, -0.0014833646, 0.010787181, -0.00567675, -0.0023587404, -0.016590508, 0.006359412, -0.0056233592, -0.059128035, 0.041784294, 0.0007266474, -0.012299326, -0.001447691, 0.0107510025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, -1, 15, -1, 17, -1, 19, 21, -1, 23, 25, 27, -1, -1, -1, 29, -1, -1, -1, -1, 31, 33, -1, -1, -1, -1], "loss_changes": [0.4340995, 0.67736024, 0.7386807, 0.51374096, 0.0, 0.54912484, 0.0, 1.1232164, 2.0477126, 0.0, 0.0, 1.5295221, 0.0, 1.2528327, 0.0, 1.0556029, 1.6145303, 0.0, 1.0920105, 1.1867143, 0.7759616, 0.0, 0.0, 0.0, 1.111142, 0.0, 0.0, 0.0, 0.0, 0.87350214, 0.857892, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 11, 11, 13, 13, 15, 15, 16, 16, 18, 18, 19, 19, 20, 20, 24, 24, 29, 29, 30, 30], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, -1, 16, -1, 18, -1, 20, 22, -1, 24, 26, 28, -1, -1, -1, 30, -1, -1, -1, -1, 32, 34, -1, -1, -1, -1], "split_conditions": [1.3076923, 1.0, 3.6538463, 1.0, -0.008807976, 1.0, -0.003214253, 1.0, 1.0, 0.012866613, 0.0018490375, 1.0, -0.009971671, -1.0, 0.015157553, 1.0, 1.0, -0.009740568, 1.0, -0.15384616, 1.0, 0.012961322, -0.0014833646, 0.010787181, 1.0, -0.0023587404, -0.016590508, 0.006359412, -0.0056233592, 1.0, 1.0, 0.0007266474, -0.012299326, -0.001447691, 0.0107510025], "split_indices": [1, 1, 1, 108, 0, 23, 0, 64, 105, 0, 0, 12, 0, 0, 0, 97, 69, 0, 81, 1, 39, 0, 0, 0, 109, 0, 0, 0, 0, 39, 39, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2060.0, 1772.0, 288.0, 1672.0, 100.0, 181.0, 107.0, 914.0, 758.0, 89.0, 92.0, 768.0, 146.0, 661.0, 97.0, 455.0, 313.0, 118.0, 543.0, 237.0, 218.0, 140.0, 173.0, 105.0, 438.0, 131.0, 106.0, 119.0, 99.0, 206.0, 232.0, 101.0, 105.0, 125.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "35", "size_leaf_vector": "1"}}, {"base_weights": [-0.003159253, -0.01544355, 0.009984942, -0.0031044842, -0.015145051, -0.0020020867, 0.012157556, 0.009937876, -0.006532423, -0.026412535, 0.026321035, -0.026064368, 0.037271474, 0.005292063, -0.014256114, -0.010621251, 0.014535732, -0.01368197, 0.052274752, 0.07958033, -0.029994842, 0.007860321, -0.039750785, -0.08286172, 0.010602932, 0.011701507, -0.0015702546, -0.0041951607, 0.14002481, -0.008664626, 0.0027944073, -0.008410612, 0.0012818464, -0.012666044, -0.0032874022, 0.0284705, 0.0013966784], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, -1, 23, -1, -1, 25, 27, 29, -1, 31, 33, -1, -1, -1, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.33423707, 1.7956727, 1.3376404, 0.7960742, 0.0, 0.6243162, 0.0, 0.7980815, 0.0, 1.7859862, 1.8381438, 3.0367665, 1.3119881, 1.2581165, 0.0, 2.688179, 0.0, 0.0, 0.902179, 2.0788999, 0.58425343, 0.0, 0.5502876, 0.43131077, 0.0, 0.0, 0.0, 0.0, 3.4469998, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 15, 15, 18, 18, 19, 19, 20, 20, 22, 22, 23, 23, 28, 28], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, -1, 24, -1, -1, 26, 28, 30, -1, 32, 34, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0384616, -0.015145051, 0.03846154, 0.012157556, 1.0, -0.006532423, -0.15384616, 1.0, -0.15384616, -0.07692308, 1.0, -0.014256114, 1.0, 0.014535732, -0.01368197, 1.0, -0.5, 0.3846154, 0.007860321, 1.0, 1.0, 0.010602932, 0.011701507, -0.0015702546, -0.0041951607, 1.0, -0.008664626, 0.0027944073, -0.008410612, 0.0012818464, -0.012666044, -0.0032874022, 0.0284705, 0.0013966784], "split_indices": [39, 88, 88, 1, 0, 1, 0, 13, 0, 1, 0, 1, 1, 53, 0, 93, 0, 0, 106, 1, 1, 0, 97, 13, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2070.0, 1070.0, 1000.0, 981.0, 89.0, 903.0, 97.0, 811.0, 170.0, 485.0, 418.0, 350.0, 461.0, 381.0, 104.0, 319.0, 99.0, 145.0, 205.0, 283.0, 178.0, 145.0, 236.0, 197.0, 122.0, 105.0, 100.0, 94.0, 189.0, 90.0, 88.0, 128.0, 108.0, 105.0, 92.0, 88.0, 101.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0042219604, 0.0028729658, -0.054493804, -0.003111856, 0.0069062733, -0.012306307, -0.0014950767, 0.0028228173, -0.006899535, -0.003431382, 0.0073838267, 0.013978609, -0.037090704, 0.049433976, -0.017441181, 0.0039300453, -0.09501449, 0.014055828, 0.010295331, -0.052198026, 0.005812691, 0.008082531, -0.0062271156, -0.003013665, -0.014858332, -0.061657473, 0.011390739, 0.000119527955, -0.016016247, -0.018102124, 0.0055082887, -0.005630141, 0.0063395374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, -1, 11, -1, 13, 15, 17, 19, 21, 23, -1, 25, 27, -1, -1, -1, -1, -1, 29, -1, 31, -1, -1, -1, -1, -1], "loss_changes": [0.7411708, 0.7213602, 0.6968389, 0.65296525, 0.0, 0.0, 0.0, 0.68043, 0.0, 0.82510006, 0.0, 1.0337921, 1.1405172, 1.5549858, 1.2922422, 1.4304471, 0.6916101, 0.0, 2.2738295, 1.9035232, 0.0, 0.0, 0.0, 0.0, 0.0, 2.5082233, 0.0, 0.8104089, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 9, 9, 11, 11, 12, 12, 13, 13, 14, 14, 15, 15, 16, 16, 18, 18, 19, 19, 25, 25, 27, 27], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, -1, 12, -1, 14, 16, 18, 20, 22, 24, -1, 26, 28, -1, -1, -1, -1, -1, 30, -1, 32, -1, -1, -1, -1, -1], "split_conditions": [1.3461539, 0.9230769, 1.0, 1.0, 0.0069062733, -0.012306307, -0.0014950767, 1.0, -0.006899535, 1.0, 0.0073838267, 1.0, 1.0, 1.0, 1.0, -0.26923078, 1.0, 0.014055828, 1.0, 1.0, 0.005812691, 0.008082531, -0.0062271156, -0.003013665, -0.014858332, -0.30769232, 0.011390739, 1.0, -0.016016247, -0.018102124, 0.0055082887, -0.005630141, 0.0063395374], "split_indices": [1, 1, 97, 88, 0, 0, 0, 44, 0, 121, 0, 126, 12, 108, 74, 1, 69, 0, 50, 0, 0, 0, 0, 0, 0, 1, 0, 12, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2078.0, 1821.0, 257.0, 1670.0, 151.0, 94.0, 163.0, 1532.0, 138.0, 1408.0, 124.0, 928.0, 480.0, 436.0, 492.0, 281.0, 199.0, 131.0, 305.0, 337.0, 155.0, 130.0, 151.0, 90.0, 109.0, 180.0, 125.0, 227.0, 110.0, 89.0, 91.0, 120.0, 107.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023840505, -0.005812199, 0.0036516178, -0.016052356, 0.0229921, -0.0037098962, -0.06446608, -0.00603176, 0.012050211, 0.04619769, -0.02563313, -0.012316755, -0.00065855845, -0.009511619, 0.035110272, 0.111668654, -0.0035749392, 0.04375476, -0.049002115, 0.008171971, 0.00024231165, 0.005260407, 0.017394325, 0.010976038, -0.003485864, -0.07971421, -0.013113814, -0.0027720674, -0.014037331, 0.00303404, -0.008015749], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, 15, 17, -1, -1, -1, 19, 21, -1, 23, 25, -1, -1, -1, -1, -1, -1, 27, 29, -1, -1, -1, -1], "loss_changes": [0.27497947, 0.55895025, 0.0, 0.83536696, 1.4065685, 1.2188672, 0.96493804, 1.4037391, 0.0, 1.8241528, 1.25506, 0.0, 0.0, 0.0, 0.39916456, 0.6951833, 0.0, 1.0118408, 0.6381763, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9840106, 0.7778594, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9, 10, 10, 14, 14, 15, 15, 17, 17, 18, 18, 25, 25, 26, 26], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, 16, 18, -1, -1, -1, 20, 22, -1, 24, 26, -1, -1, -1, -1, -1, -1, 28, 30, -1, -1, -1, -1], "split_conditions": [1.0, 1.0, 0.0036516178, 1.0, 1.0, 0.0, 1.0, 1.0, 0.012050211, 1.0, 1.0, -0.012316755, -0.00065855845, -0.009511619, 1.0, 1.0, -0.0035749392, 1.0, 1.0, 0.008171971, 0.00024231165, 0.005260407, 0.017394325, 0.010976038, -0.003485864, 1.0, 1.0, -0.0027720674, -0.014037331, 0.00303404, -0.008015749], "split_indices": [31, 0, 0, 80, 42, 0, 97, 39, 0, 15, 5, 0, 0, 0, 59, 93, 0, 115, 59, 0, 0, 0, 0, 0, 0, 39, 109, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2062.0, 1895.0, 167.0, 1398.0, 497.0, 1114.0, 284.0, 383.0, 114.0, 340.0, 774.0, 141.0, 143.0, 121.0, 262.0, 189.0, 151.0, 195.0, 579.0, 108.0, 154.0, 97.0, 92.0, 106.0, 89.0, 312.0, 267.0, 168.0, 144.0, 162.0, 105.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029944875, 0.0045660646, -0.005676855, -0.0065700794, -0.0007356557, -0.014798364, 0.013141811, -0.07211431, 0.006914881, 0.024462454, -0.0061480068, 0.0005169995, -0.017491704, 0.024695927, -0.008571587, 0.0071375584, 0.010864859, -0.0029679602, 0.011525196, 0.04062864, -0.052072402, -0.030344158, 0.0059975716, 0.011201031, 0.009990726, -0.012343247, 0.0028930893, 0.001929989, -0.008651081, 0.057511136, -0.0083432635, 0.0013876221, 0.01030432], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, -1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, 17, -1, 19, -1, 21, -1, 23, 25, 27, -1, 29, -1, -1, -1, -1, -1, 31, -1, -1, -1], "loss_changes": [0.269766, 0.0, 0.5810201, 0.0, 0.35323012, 1.1188195, 0.7695832, 1.9624249, 1.0738906, 1.1536857, 0.0, 0.0, 0.0, 1.3703074, 0.0, 1.3008517, 0.0, 0.7220034, 0.0, 0.73091495, 1.3699554, 0.8141954, 0.0, 1.2270986, 0.0, 0.0, 0.0, 0.0, 0.0, 0.37351567, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 13, 13, 15, 15, 17, 17, 19, 19, 20, 20, 21, 21, 23, 23, 29, 29], "right_children": [2, -1, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, 18, -1, 20, -1, 22, -1, 24, 26, 28, -1, 30, -1, -1, -1, -1, -1, 32, -1, -1, -1], "split_conditions": [-0.5769231, 0.0045660646, -0.5, -0.0065700794, 1.0, -0.15384616, 1.3461539, 1.0, 2.0, 0.53846157, -0.0061480068, 0.0005169995, -0.017491704, 1.0, -0.008571587, -0.03846154, 0.010864859, 1.2692307, 0.011525196, 1.0, 1.0, 1.0, 0.0059975716, 1.0, 0.009990726, -0.012343247, 0.0028930893, 0.001929989, -0.008651081, 1.0, -0.0083432635, 0.0013876221, 0.01030432], "split_indices": [1, 0, 1, 0, 13, 1, 1, 111, 0, 1, 0, 0, 0, 83, 0, 1, 0, 1, 0, 105, 122, 126, 0, 0, 0, 0, 0, 0, 0, 106, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2067.0, 108.0, 1959.0, 149.0, 1810.0, 899.0, 911.0, 247.0, 652.0, 791.0, 120.0, 141.0, 106.0, 547.0, 105.0, 656.0, 135.0, 419.0, 128.0, 419.0, 237.0, 292.0, 127.0, 280.0, 139.0, 126.0, 111.0, 155.0, 137.0, 188.0, 92.0, 96.0, 92.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "33", "size_leaf_vector": "1"}}, {"base_weights": [0.0024894527, -0.003167031, 0.03396041, 0.0034263257, -0.05656637, 0.015979528, -0.015037229, -0.0023958064, 0.009819773, -0.012068305, 0.0013125644, -0.0056569935, 0.0031980898, 0.02064201, -0.022242876, -0.018913062, 0.11385101, 0.008751954, -0.07282976, -0.013946285, 0.02217573, -0.0034259006, 0.030572085, 0.07870826, -0.038606774, 0.0005873557, -0.12633035, 0.014511511, -0.023392525, 0.01602043, 0.0010160168, -0.009055956, 0.0012285736, -0.016522577, -0.008829923, -0.007580233, 0.005471821], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, -1, -1, -1, -1, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, 29, 31, -1, 33, -1, 35, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [0.3668889, 0.6150854, 1.9360026, 0.8580052, 0.85793716, 0.0, 0.44133067, 0.6698466, 0.0, 0.0, 0.0, 0.0, 0.0, 2.499711, 1.2339627, 2.357745, 5.740406, 1.6167644, 1.2589917, 0.0, 1.9887576, 0.0, 0.0, 1.1005198, 0.7694065, 0.0, 0.26330352, 0.0, 1.0602859, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 23, 23, 24, 24, 26, 26, 28, 28], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, -1, -1, -1, -1, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, 30, 32, -1, 34, -1, 36, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2692307, 0.6923077, 1.0, 0.53846157, 1.0, 0.015979528, 3.0384614, 1.0, 0.009819773, -0.012068305, 0.0013125644, -0.0056569935, 0.0031980898, 1.0, 1.0, 1.0, -0.23076923, 1.0, 1.0, -0.013946285, 1.0, -0.0034259006, 0.030572085, -0.30769232, 1.0, 0.0005873557, 1.0, 0.014511511, 1.0, 0.01602043, 0.0010160168, -0.009055956, 0.0012285736, -0.016522577, -0.008829923, -0.007580233, 0.005471821], "split_indices": [1, 1, 108, 1, 23, 0, 1, 69, 0, 0, 0, 0, 0, 105, 121, 53, 1, 53, 17, 0, 59, 0, 0, 1, 59, 0, 53, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2061.0, 1747.0, 314.0, 1555.0, 192.0, 88.0, 226.0, 1465.0, 90.0, 100.0, 92.0, 120.0, 106.0, 678.0, 787.0, 476.0, 202.0, 488.0, 299.0, 121.0, 355.0, 114.0, 88.0, 197.0, 291.0, 121.0, 178.0, 96.0, 259.0, 90.0, 107.0, 144.0, 147.0, 88.0, 90.0, 155.0, 104.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "37", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019790363, 0.0013266304, -0.006371632, -0.0053021405, 0.064585075, 0.0036132052, -0.0527026, 0.016226117, -0.003960271, -0.001781165, 0.007017591, 0.0012910916, -0.013821532, 0.02182146, -0.024185354, -0.04039234, 0.10498922, 0.03710403, -0.088486075, 0.008349019, -0.083859906, 0.002050367, 0.021832347, -0.008355807, 0.075715914, -0.014466709, -0.0030322184, -0.021971457, 0.00015344423, 0.013783373, -0.00010475414], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, 13, -1, -1, -1, 15, 17, 19, 21, 23, 25, -1, 27, -1, -1, -1, 29, -1, -1, -1, -1, -1, -1], "loss_changes": [0.42163545, 0.822298, 0.0, 0.7501, 1.8928581, 0.5364416, 1.5766324, 0.0, 0.0, 0.7307984, 0.0, 0.0, 0.0, 3.482225, 2.7941349, 2.0731754, 2.7576303, 1.6912136, 1.1306257, 0.0, 3.3063483, 0.0, 0.0, 0.0, 1.3113042, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 9, 9, 13, 13, 14, 14, 15, 15, 16, 16, 17, 17, 18, 18, 20, 20, 24, 24], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, 14, -1, -1, -1, 16, 18, 20, 22, 24, 26, -1, 28, -1, -1, -1, 30, -1, -1, -1, -1, -1, -1], "split_conditions": [3.6538463, 1.3076923, -0.006371632, 0.5769231, 1.0, 1.0, 1.0, 0.016226117, -0.003960271, 1.0, 0.007017591, 0.0012910916, -0.013821532, -0.1923077, -0.1923077, 1.0, 1.0, 1.0, 0.07692308, 0.008349019, 1.0, 0.002050367, 0.021832347, -0.008355807, 1.0, -0.014466709, -0.0030322184, -0.021971457, 0.00015344423, 0.013783373, -0.00010475414], "split_indices": [1, 1, 0, 1, 115, 44, 2, 0, 0, 69, 0, 0, 0, 1, 1, 5, 71, 89, 1, 0, 53, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2066.0, 1961.0, 105.0, 1775.0, 186.0, 1494.0, 281.0, 96.0, 90.0, 1382.0, 112.0, 159.0, 122.0, 673.0, 709.0, 385.0, 288.0, 363.0, 346.0, 100.0, 285.0, 165.0, 123.0, 88.0, 275.0, 176.0, 170.0, 110.0, 175.0, 152.0, 123.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}, {"base_weights": [0.0012914449, -0.0041067316, 0.02575645, -0.018153202, 0.014283806, 0.010814238, -0.020779524, -0.04725288, 0.032418877, 0.03784004, -0.0315988, 0.0019360479, -0.007200584, -0.070038944, 0.0050826254, 0.010661184, -0.003190798, -0.023896605, 0.0895211, 0.0031783336, -0.013976182, 0.00072965113, -0.10562695, -0.0071516596, 0.0021616276, -0.0002840153, 0.014229898, -0.12527594, -0.005043313, -0.0084004635, -0.015516202], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, 21, -1, -1, -1, 23, 25, -1, -1, -1, 27, -1, -1, -1, -1, 29, -1, -1, -1], "loss_changes": [0.27324548, 0.43785605, 1.4338825, 1.4142376, 0.7933228, 0.0, 0.49143785, 1.3632512, 1.6751826, 1.5474489, 1.7070457, 0.0, 0.0, 1.3623464, 0.0, 0.0, 0.0, 0.4789785, 1.2869029, 0.0, 0.0, 0.0, 0.36764574, 0.0, 0.0, 0.0, 0.0, 0.3083589, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 13, 13, 17, 17, 18, 18, 22, 22, 27, 27], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, 22, -1, -1, -1, 24, 26, -1, -1, -1, 28, -1, -1, -1, -1, 30, -1, -1, -1], "split_conditions": [1.0, 1.0, 1.0, 1.0, 1.0, 0.010814238, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0019360479, -0.007200584, 1.0, 0.0050826254, 0.010661184, -0.003190798, 1.0, 1.0, 0.0031783336, -0.013976182, 0.00072965113, 1.0, -0.0071516596, 0.0021616276, -0.0002840153, 0.014229898, 1.0, -0.005043313, -0.0084004635, -0.015516202], "split_indices": [74, 12, 122, 106, 115, 0, 127, 7, 15, 127, 121, 0, 0, 2, 0, 0, 0, 23, 109, 0, 0, 0, 119, 0, 0, 0, 0, 15, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [2069.0, 1695.0, 374.0, 961.0, 734.0, 135.0, 239.0, 610.0, 351.0, 485.0, 249.0, 134.0, 105.0, 495.0, 115.0, 163.0, 188.0, 221.0, 264.0, 157.0, 92.0, 156.0, 339.0, 108.0, 113.0, 96.0, 168.0, 250.0, 89.0, 105.0, 145.0], "tree_param": {"num_deleted": "0", "num_feature": "138", "num_nodes": "31", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.6258356E-8", "boost_from_average": "1", "num_class": "0", "num_feature": "138", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [3, 0, 2]}