{"uid": "c1fa3d8d-477b-43ba-86f6-40a8689df19a", "name": "2_Optuna_LightGBM_Stacked", "preprocessing": [{"scale_y": {"scale": [0.8211706673631429], "mean": [0.4207229727151287], "var": [0.6743212649376294], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8346999263942918], "mean": [0.42996936912635636], "var": [0.6967239671226361], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8351871699359255], "mean": [0.4267552162709529], "var": [0.6975376088255804], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.81886101693931], "mean": [0.41553713076390103], "var": [0.670533365062881], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8337176677697381], "mean": [0.4309213625254967], "var": [0.6950851495514113], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8134753858563379], "mean": [0.41305252889866456], "var": [0.6617422033941178], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8218611565977175], "mean": [0.4247006703486496], "var": [0.6754557607241379], "n_samples_seen": 2130, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8298397500459297], "mean": [0.42137784377512366], "var": [0.6886340107562912], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8160733190945939], "mean": [0.4163287823768878], "var": [0.6659756621380668], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}, {"scale_y": {"scale": [0.8086812963234827], "mean": [0.414534039849729], "var": [0.6539654390234284], "n_samples_seen": 2131, "n_features_in": 1, "columns": ["target"], "scale_method": "scale_log_and_normal", "X_min_values": [0.0]}, "ml_task": "regression", "params": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}}], "learners": [{"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "9e1aac8d-07bf-4585-aad1-6244231c3e34", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_0"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "a860ad95-4019-4907-a26c-5ebb9e23f4bd", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_1"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "85c9debf-9248-4a68-9a75-1096f71f955c", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_2"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "9ecfa1c9-80d6-402e-880d-7500731bde53", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_3"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "ec8de07a-281b-4389-b813-8209873804d2", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_4"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "394fb5c0-9433-4d4e-9d92-eb9c931f5011", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_5"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "c2097756-128e-4389-a2fd-00b5ea94275e", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_6"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "6b824642-326c-4cc0-80b7-34b2fd3c2ff7", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_7"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "6109f7dd-d21d-4e4a-8a00-115cc30039d1", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_8"}, {"library_version": "4.6.0", "algorithm_name": "LightGBM", "algorithm_short_name": "LightGBM", "uid": "d6e49ba5-c360-4ee4-a5ec-f6892ce6f610", "params": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "name": "learner_fold_9"}], "params": {"additional": {"max_rounds": 10000, "early_stopping_rounds": 50, "max_rows_limit": null, "max_cols_limit": null}, "preprocessing": {"columns_preprocessing": {}, "target_preprocessing": ["na_exclude", "scale_log_and_normal"], "ml_task": "regression"}, "validation_strategy": {"validation_type": "kfold", "k_folds": 10, "shuffle": true, "X_path": "AutoML_model2_ads/X_stacked.data", "y_path": "AutoML_model2_ads/y.data", "results_path": "AutoML_model2_ads"}, "learner": {"model_type": "LightGBM", "ml_task": "regression", "n_jobs": -1, "objective": "regression", "num_leaves": 1598, "learning_rate": 0.1, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "min_data_in_leaf": 36, "seed": 1234, "metric": "l2", "custom_eval_metric_name": null, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "bagging_freq": 7, "extra_trees": false, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "explain_level": 0}, "automl_random_state": 1234, "ml_task": "regression", "explain_level": 0, "name": "2_Optuna_LightGBM_Stacked", "status": "initialized", "final_loss": null, "train_time": null, "data_type": "original_stacked", "optuna_time_budget": 1, "optuna_init_params": {"original_LightGBM": {"learning_rate": 0.1, "num_leaves": 1598, "lambda_l1": 2.840098794801191e-06, "lambda_l2": 3.0773599420974e-06, "feature_fraction": 0.8613105322932351, "bagging_fraction": 0.970697557159987, "bagging_freq": 7, "min_data_in_leaf": 36, "extra_trees": false, "metric": "l2", "custom_eval_metric_name": null, "num_boost_round": 1000, "early_stopping_rounds": 50, "cat_feature": [], "feature_pre_filter": false, "seed": 1234}, "original_Xgboost": {"eta": 0.1, "max_depth": 10, "lambda": 2.840098794801191e-06, "alpha": 3.0773599420974e-06, "colsample_bytree": 0.8613105322932351, "subsample": 0.970697557159987, "min_child_weight": 88, "objective": "reg:<PERSON><PERSON><PERSON><PERSON>", "eval_metric": "mse", "max_rounds": 1000, "early_stopping_rounds": 50, "seed": 1234}, "original_CatBoost": {"learning_rate": 0.1, "depth": 8, "l2_leaf_reg": 7.7997800836072235, "random_strength": 2.7259260601004898, "rsm": 0.34881782962878705, "min_data_in_leaf": 81, "eval_metric": "mse", "num_boost_round": 1000, "early_stopping_rounds": 50, "seed": 1234}, "original_Random Forest": {"max_depth": 7, "min_samples_split": 63, "min_samples_leaf": 44, "max_features": 0.7875049978766315, "max_steps": 10, "seed": 1234, "eval_metric_name": "mse"}, "original_Extra Trees": {"max_depth": 7, "min_samples_split": 63, "min_samples_leaf": 44, "max_features": 0.7875049978766315, "max_steps": 10, "seed": 1234, "eval_metric_name": "mse"}}, "optuna_verbose": true, "max_time_for_learner": 3600, "is_stacked": true}, "saved": ["2_Optuna_LightGBM_Stacked/learner_fold_0.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_1.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_2.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_3.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_4.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_5.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_6.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_7.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_8.lightgbm", "2_Optuna_LightGBM_Stacked/learner_fold_9.lightgbm"], "predictions_fname": "2_Optuna_LightGBM_Stacked/predictions_out_of_folds.csv", "metric_name": "mse", "final_loss": "73.87692728338027", "train_time": 15.721805095672607, "is_stacked": true, "joblib_version": "1.3.2"}