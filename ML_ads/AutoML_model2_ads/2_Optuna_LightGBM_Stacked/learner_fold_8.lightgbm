tree
version=v4
num_class=1
num_tree_per_iteration=1
label_index=0
max_feature_idx=143
objective=regression
feature_names=Column_0 Column_1 Column_2 Column_3 Column_4 Column_5 Column_6 Column_7 Column_8 Column_9 Column_10 Column_11 Column_12 Column_13 Column_14 Column_15 Column_16 Column_17 Column_18 Column_19 Column_20 Column_21 Column_22 Column_23 Column_24 Column_25 Column_26 Column_27 Column_28 Column_29 Column_30 Column_31 Column_32 Column_33 Column_34 Column_35 Column_36 Column_37 Column_38 Column_39 Column_40 Column_41 Column_42 Column_43 Column_44 Column_45 Column_46 Column_47 Column_48 Column_49 Column_50 Column_51 Column_52 Column_53 Column_54 Column_55 Column_56 Column_57 Column_58 Column_59 Column_60 Column_61 Column_62 Column_63 Column_64 Column_65 Column_66 Column_67 Column_68 Column_69 Column_70 Column_71 Column_72 Column_73 Column_74 Column_75 Column_76 Column_77 Column_78 Column_79 Column_80 Column_81 Column_82 Column_83 Column_84 Column_85 Column_86 Column_87 Column_88 Column_89 Column_90 Column_91 Column_92 Column_93 Column_94 Column_95 Column_96 Column_97 Column_98 Column_99 Column_100 Column_101 Column_102 Column_103 Column_104 Column_105 Column_106 Column_107 Column_108 Column_109 Column_110 Column_111 Column_112 Column_113 Column_114 Column_115 Column_116 Column_117 Column_118 Column_119 Column_120 Column_121 Column_122 Column_123 Column_124 Column_125 Column_126 Column_127 Column_128 Column_129 Column_130 Column_131 Column_132 Column_133 Column_134 Column_135 Column_136 Column_137 Column_138 Column_139 Column_140 Column_141 Column_142 Column_143
feature_infos=[-4:31] [-0.76923076923076927:13.384615384615385] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] none [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [0:1] [1.0741435151424494:3.5257747796191232] [-0.090697550172045838:6.008098920878794] [-0.12052393504539227:3.4265554295268315] [-0.090659460636520972:4.5031860626017739] [0.037575296505757416:1.8250263004316918] [0.0082957548054640728:2.1360714373989484]
tree_sizes=3806 3886 3875 4088

Tree=0
num_leaves=42
num_cat=0
split_feature=142 142 141 119 1 7 69 1 142 142 109 143 69 141 122 142 1 12 126 93 1 1 106 141 143 1 122 108 141 141 50 1 1 1 59 142 142 142 142 142 1
split_gain=135.683 24.7647 19.2278 9.55011 9.46924 9.19734 6.44574 16.0049 9.74825 5.9921 8.77157 5.90206 8.38096 5.5728 5.42989 4.37304 4.06678 3.70027 3.34624 3.25183 3.90392 3.23741 2.87472 2.46562 3.23442 3.67973 2.35873 3.73906 1.99095 2.19454 1.90007 1.35859 2.14478 0.588324 0.692663 0.558807 0.738412 0.495292 0.437563 0.140339 0.123574
threshold=0.71134039872554533 0.39320760325116283 0.97860348151374454 1.0000000180025095e-35 3.4807692307692313 1.0000000180025095e-35 1.0000000180025095e-35 -0.36538461538461536 1.2445184223196177 0.83338806663123366 1.0000000180025095e-35 0.52091414162013061 1.0000000180025095e-35 0.32068903718898517 1.0000000180025095e-35 1.0723055404252817 -0.096153846153846145 1.0000000180025095e-35 1.0000000180025095e-35 1.0000000180025095e-35 -0.28846153846153838 0.44230769230769235 1.0000000180025095e-35 0.60371503000639537 0.45252716502164997 0.86538461538461553 1.0000000180025095e-35 1.0000000180025095e-35 0.48336356516394591 0.3972539293796496 1.0000000180025095e-35 0.25000000000000006 0.67307692307692324 0.36538461538461547 1.0000000180025095e-35 0.19755779380527297 0.30707249973077416 0.5999783004258521 0.25843256231776263 0.15360781912426394 0.59615384615384626
decision_type=2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2
left_child=1 4 5 6 19 9 7 -4 -8 10 -2 13 21 -3 16 17 -14 -11 -16 26 -21 22 -13 24 25 28 27 -1 29 -15 40 -22 -33 35 -35 39 38 -18 -37 -28 -29
right_child=2 11 3 -5 -6 -7 8 -9 -10 15 -12 12 14 23 18 -17 37 -19 -20 20 31 -23 -24 -25 -26 -27 33 30 -30 -31 -32 32 -34 34 -36 36 -38 -39 -40 -41 -42
leaf_value=-0.0063022134971540515 0.085625332789876357 0.023391900009949857 0.052651883743205313 0.028088667226382459 0.012304066968267118 -0.020963051705139832 0.031680209301004056 0.1391421838948271 0.090907687840367138 -0.0088601582482382406 0.026380371667126964 0.0150832158687453 0.060323922172489837 -0.0072235045436420094 -0.015346176177888256 0.047248370656463271 0.028258699639537289 0.027544923621598747 0.017997013238442876 0.006702796795056744 -0.03884631428953976 -0.030298500269702554 -0.015615386851624218 -0.038808860259024386 -0.037182713098144991 0.016546042982729246 -0.038799100424754131 -0.051016070207545498 0.00076693617837623313 -0.039937351160730071 -0.022598627559676863 -0.0039116532963235614 -0.033004760488689791 -0.03598958558161941 -0.051016077269385819 -0.034091002555181674 -0.044129297695902077 0.011896355942859534 -0.019091538141657406 -0.047155305790106197 -0.042786462912866606
leaf_weight=62 41 37 46 65 54 39 66 40 48 59 64 56 38 37 54 39 37 53 68 42 64 58 67 47 46 47 37 36 45 46 58 45 58 48 85 41 37 37 37 44 37
leaf_count=62 41 37 46 65 54 39 66 40 48 59 64 56 38 37 54 39 37 53 68 42 64 58 67 47 46 47 37 36 45 46 58 45 58 48 85 41 37 37 37 44 37
internal_value=-0.000184289 -0.0158204 0.0418378 0.0613883 -0.0281055 0.0242754 0.0722107 0.0928799 0.0566181 0.0311672 0.0495141 -0.0024262 0.00533558 -0.0129873 0.017834 0.0184094 0.0337326 0.00836724 0.00323855 -0.0310906 -0.02055 -0.0108226 -0.00163878 -0.0180098 -0.0135865 -0.00738407 -0.0353109 -0.0265344 -0.0161709 -0.0253541 -0.0361099 -0.027404 -0.0202942 -0.0404595 -0.045593 -0.036976 -0.0324948 0.0200775 -0.0269759 -0.0433383 -0.0468449
internal_weight=2065 1505 560 265 785 295 200 86 114 256 105 720 415 305 234 151 112 112 122 731 209 181 123 268 221 175 522 193 128 83 131 167 103 329 133 196 115 74 78 81 73
internal_count=2065 1505 560 265 785 295 200 86 114 256 105 720 415 305 234 151 112 112 122 731 209 181 123 268 221 175 522 193 128 83 131 167 103 329 133 196 115 74 78 81 73
is_linear=0
shrinkage=1


Tree=1
num_leaves=43
num_cat=0
split_feature=139 138 140 140 140 143 143 139 138 139 139 142 1 140 141 17 143 58 143 143 143 13 50 69 80 139 108 1 142 143 121 13 142 143 143 143 142 141 141 143 1 83
split_gain=119.425 50.7414 27.0738 10.6231 8.70998 7.16338 16.2344 7.37 12.915 6.33834 6.18393 5.7299 5.69126 4.32431 3.94811 3.50419 7.0397 4.96488 5.2396 4.57715 4.39125 3.1441 3.14493 2.80409 2.36022 4.07335 4.59233 1.80896 1.87741 1.70054 1.49595 2.89377 1.636 1.44909 1.17032 1.48242 0.853721 0.724594 1.17994 0.6266 0.178892 0.0235323
threshold=0.76258647489011011 1.8835796317936337 0.415406250747338 0.68477796056661244 0.79854607666302713 0.77094906849709455 0.94079729146335922 0.96654658109322167 1.6268058735669755 0.47106549946376292 0.84947438702426703 0.45032949767112579 1.2500000000000002 1.0213416124780623 0.56312921648500336 1.0000000180025095e-35 0.54348724484687216 1.0000000180025095e-35 0.66989224405727099 0.92268099970710615 0.55614095923841778 1.0000000180025095e-35 1.0000000180025095e-35 1.0000000180025095e-35 1.0000000180025095e-35 0.37937342831167536 1.0000000180025095e-35 3.1730769230769238 0.13875096802709255 0.73684204563297984 1.0000000180025095e-35 1.0000000180025095e-35 0.15360781912426394 0.28038960581602063 0.34270262779027533 0.43420656717958678 0.28539981164195577 0.14873477409076011 0.25015258263775236 0.19004170004917034 0.36538461538461547 1.0000000180025095e-35
decision_type=2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2
left_child=2 3 11 10 -5 13 -7 -8 -9 14 -2 27 15 -6 24 16 -11 18 20 29 -17 -13 -23 -10 25 26 -4 28 40 -20 32 -32 -30 37 36 -36 -35 -34 41 -40 -1 -39
right_child=1 -3 9 4 5 6 7 8 23 12 -12 21 -14 -15 -16 17 -18 -19 19 -21 -22 22 -24 -25 -26 -27 -28 -29 30 -31 31 -33 33 34 35 -37 -38 38 39 -41 -42 -43
leaf_value=-0.037798278075453495 0.030157014986527144 0.15144221624688323 -0.0050689231207948554 0.077620246081541056 -0.01290034030032494 0.091582798744492877 -0.0070861928258497816 0.087217383751218153 0.03964114187427057 -0.008673246605746622 -0.022725805033969655 0.0087930172050233422 0.041781762024103414 0.029387351470923934 -0.032572282015730827 0.0015540400580447717 0.049079848633039602 -0.028815915651593318 -0.0063234095646052396 0.017061913080769667 0.043898980758930513 -0.03396177138481557 0.0035433216263268835 0.0044236709369063877 -0.024355979128794742 -0.015438408437920929 0.044162440473865722 -0.012250934922689971 -0.010144130863641037 -0.03520241700467816 -0.034179275216674958 0.0027254680003300554 -0.024636217819037347 -0.0081945768570689942 -0.043601890283676302 -0.020368787571477361 -0.028476934159553897 -0.047940296966611257 -0.022383557869497195 -0.038421816303200247 -0.045901716857884356 -0.044874895763747424
leaf_weight=48 41 39 40 53 42 54 43 41 40 39 48 65 47 57 64 48 46 51 38 48 50 59 36 52 43 68 36 59 37 44 43 42 39 49 57 53 36 67 42 58 63 40
leaf_count=48 41 39 40 53 42 54 43 41 40 39 48 65 47 57 64 48 46 51 38 48 50 59 36 52 43 68 36 59 37 44 43 42 39 49 57 53 36 67 42 58 63 40
internal_value=-0.000165876 0.0418262 -0.0139382 0.0327497 0.0399987 0.0339381 0.0436189 0.0289027 0.0405383 0.00138703 0.00163594 -0.0252991 0.00903374 0.0114471 -0.0111341 0.00480528 0.0225814 -0.000610368 0.00569877 -0.00746326 0.0231586 -0.00815399 -0.0197493 0.0197356 -0.00379694 0.00234221 0.0182512 -0.0290415 -0.0305113 -0.0218195 -0.0281679 -0.015944 -0.0303416 -0.0320362 -0.0255977 -0.0324078 -0.0167848 -0.0371398 -0.0394955 -0.0316858 -0.0423975 -0.0467944
internal_weight=2065 510 1555 471 382 329 230 176 133 662 89 893 411 99 251 364 85 279 228 130 98 160 95 92 187 144 76 733 674 82 563 85 478 441 195 110 85 246 207 100 111 107
internal_count=2065 510 1555 471 382 329 230 176 133 662 89 893 411 99 251 364 85 279 228 130 98 160 95 92 187 144 76 733 674 82 563 85 478 441 195 110 85 246 207 100 111 107
is_linear=0
shrinkage=0.1


Tree=2
num_leaves=43
num_cat=0
split_feature=139 138 139 138 142 140 141 142 140 139 1 142 106 140 143 139 141 140 59 142 138 139 138 142 138 139 142 138 53 140 93 140 138 140 141 1 1 139 143 141 40 126
split_gain=97.6011 48.2768 11.8276 13.0503 10.3831 8.56683 8.24257 6.67423 5.93189 7.44431 7.67481 5.40873 7.91399 4.52471 7.00376 3.0832 4.64603 2.50452 2.635 2.20256 3.14841 6.14664 2.37396 2.08105 3.56275 2.65144 3.28667 2.53899 2.03241 3.86659 1.32158 1.00188 1.47844 2.68809 1.4043 0.897377 0.778078 0.866463 0.566733 0.481528 0.0531897 0.00316556
threshold=0.59681114702174309 1.8835796317936337 0.99689874928129851 1.6950025717363972 0.52739054504808036 0.44147099245863425 0.48007621214840063 0.69053449927992483 0.87477282683775304 1.0965429080788816 -0.17307692307692304 0.839287234036589 1.0000000180025095e-35 0.415406250747338 0.26392612597936466 0.6916013074218651 0.66348924290227407 0.47184891710141302 1.0000000180025095e-35 0.80942672828125961 1.3285701752302745 0.41853251201429492 1.3633708230301662 0.24506033920154693 1.2283667386328931 0.42866810093702984 0.4156265020859497 1.1971296052135336 1.0000000180025095e-35 0.85770381147294139 1.0000000180025095e-35 0.093469522475981739 1.1753114512827942 0.16880108310009337 0.23921245858745502 0.1730769230769231 0.59615384615384626 0.28785961784070829 0.45252716502164997 0.1724603529523451 1.0000000180025095e-35 1.0000000180025095e-35
decision_type=2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2
left_child=4 2 5 8 13 -2 -7 15 -4 -10 -11 12 -9 23 -15 -8 -17 -16 -19 20 21 -6 -22 31 27 30 -27 -25 29 -13 36 40 33 -33 39 -36 37 -26 -20 -34 -1 -41
right_child=1 -3 3 -5 19 6 7 11 9 10 -12 28 -14 14 17 16 -18 18 38 -21 22 -23 -24 24 25 26 -28 -29 -30 -31 -32 32 34 -35 35 -37 -38 -39 -40 41 -42 -43
leaf_value=-0.037925803352252568 -0.024489800233557509 0.13629799504020756 0.024881504239662774 0.0043697226604673895 -0.0062698097449636961 0.061950774508585355 -0.022551973474435008 0.014512632464360038 0.10583152716536098 0.024768621815408218 0.086126849884648984 0.044168697381165876 0.067710715851554512 0.025588376125549508 0.0004453123830813015 0.030822617325766688 -0.012425319898560588 -0.0084038815773305551 -0.028391881376822803 -0.021399845191338425 -0.021408138893474617 0.045438153464468702 0.0065611096724304367 -0.018654233403634545 -0.021256688635892664 0.011542215603728088 -0.027772180201101577 0.012819414800957486 -0.0037608388651196932 0.0026840185742673804 -0.019778862560883147 0.0023415331754276796 -0.031241059778033112 -0.03479893950796744 -0.03419757713145679 -0.01588408465188294 -0.046180392648467551 -0.041840030329233113 -0.04477845968835472 -0.044231137711989499 -0.043135990877969183 -0.042956409266798286
leaf_weight=43 53 39 44 69 47 39 52 58 36 47 36 39 54 46 55 46 54 59 46 37 53 45 71 55 43 37 50 48 57 53 62 40 53 38 45 66 58 39 39 39 36 39
leaf_count=43 53 39 44 69 47 39 52 58 36 47 36 39 54 46 55 46 54 59 46 37 53 45 71 55 43 37 50 48 57 53 62 40 53 38 45 66 58 39 39 39 36 39
internal_value=-0.000149289 0.0278704 0.0221327 0.040823 -0.0170176 0.0135463 0.0180063 0.0138565 0.0562541 0.067854 0.0513818 0.0235578 0.0401617 -0.0214528 -0.0095782 -0.00280151 0.00746873 -0.0177072 -0.0246404 0.00114406 0.00500575 0.0190221 -0.00539349 -0.0251308 -0.019956 -0.0256474 -0.0110523 -0.0039869 0.0110769 0.0202699 -0.0319334 -0.0302149 -0.0277251 -0.0157525 -0.031584 -0.0233085 -0.0373162 -0.0310463 -0.0359104 -0.0385961 -0.0403001 -0.0435938
internal_weight=2065 776 737 232 1289 505 452 413 163 119 83 261 112 1036 245 152 100 199 144 253 216 92 124 791 392 289 87 103 149 92 202 399 320 78 242 111 140 82 85 131 79 78
internal_count=2065 776 737 232 1289 505 452 413 163 119 83 261 112 1036 245 152 100 199 144 253 216 92 124 791 392 289 87 103 149 92 202 399 320 78 242 111 140 82 85 131 79 78
is_linear=0
shrinkage=0.1


Tree=3
num_leaves=45
num_cat=0
split_feature=140 138 139 139 140 97 141 140 140 142 138 139 61 141 93 142 138 142 140 142 142 140 138 121 42 12 138 139 121 138 12 141 140 140 138 140 139 108 140 142 142 69 108 40
split_gain=80.6381 31.4576 14.5243 11.8041 12.7397 10.6763 9.03799 8.18549 7.37073 7.34041 6.62118 6.1826 5.95753 4.76255 4.21923 4.14365 4.87691 4.06963 3.26071 2.81986 4.13961 2.42583 3.47706 2.33672 1.71972 1.75466 1.66697 4.48457 1.76866 1.61106 1.56186 1.17665 0.881569 0.959419 2.13273 1.37911 1.00587 3.51563 0.625409 0.609351 0.714371 0.464684 0.123918 0.0444679
threshold=0.69851575117521636 1.8835796317936337 0.52313654244613961 0.80777177333871042 0.79854607666302713 1.0000000180025095e-35 0.46762671392650096 0.94176120719596068 0.40833758016099103 0.79037939141003688 1.718772673825653 0.96654658109322167 1.0000000180025095e-35 1.1441433519646445 1.0000000180025095e-35 0.24506033920154693 1.2399403553236474 0.54406864799500698 1.0815190570600335 0.28539981164195577 0.38793524680004449 0.50861096465596256 1.4304206654236995 1.0000000180025095e-35 1.0000000180025095e-35 1.0000000180025095e-35 1.3508255690859594 0.41485570499135244 1.0000000180025095e-35 1.4164698330686771 1.0000000180025095e-35 0.58323811792233493 0.093469522475981739 0.19270727819483394 1.1776060182946537 0.15871293128309152 0.22740648679454192 1.0000000180025095e-35 0.35763569454133853 0.31190070133108322 0.43427121665625962 1.0000000180025095e-35 1.0000000180025095e-35 1.0000000180025095e-35
decision_type=2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2
left_child=2 3 15 12 -5 7 8 -6 -4 21 11 -9 14 -13 -2 32 19 24 -7 -17 41 31 23 -23 25 26 27 28 38 -16 -19 -8 43 34 -34 -36 42 -38 -18 -27 -41 -21 -35 -1
right_child=1 -3 6 4 5 18 9 10 -10 -11 -12 13 -14 -15 29 16 17 30 -20 20 -22 22 -24 -25 -26 39 -28 -29 -30 -31 -32 -33 33 36 35 -37 37 -39 -40 40 -42 -43 -44 -45
leaf_value=-0.034032711994097208 0.015655756487851072 0.1184600935665646 -0.0039522692247415479 0.087852589643167278 -0.0016089671176274919 0.019740077035103911 -0.013014985749121522 0.030050312365866368 0.05404450302125352 0.028255330662258684 0.012340454165830468 0.095323838309740672 0.032898653565498814 0.050481245721422034 -0.030134130987516973 0.023884024782855156 -0.033994784629309265 0.0080541608665170485 -0.020249323465122152 -0.029691601217027203 0.016254635895149975 -0.0074548453095943171 -0.022642735485445899 0.027850593581701905 -0.040015012834599004 -0.019126591654036809 -0.032446180189417327 0.021197273980742629 -0.00035892429711721369 -0.0042025969840607507 -0.017543466557270661 -0.035974130129056786 -0.0027864745435286957 -0.0410765876089631 -0.038746021312076981 -0.013076775868662667 0.0023932408022606729 -0.035812533451267385 -0.016708739635746497 -0.039034572129537252 -0.022322534544588421 -0.013624287531921121 -0.033503864363553744 -0.038822393004634165
leaf_weight=42 70 38 36 42 38 38 41 48 56 54 38 50 64 45 46 41 50 67 44 36 48 38 58 37 53 47 41 36 36 50 37 49 55 40 50 36 37 69 36 68 41 36 47 36
leaf_count=42 70 38 36 42 38 38 41 48 56 54 38 50 64 45 46 41 50 67 44 36 48 38 58 37 53 47 41 36 36 50 37 49 55 40 50 36 37 69 36 68 41 36 47 36
internal_value=-0.00013436 0.0303496 -0.0129443 0.0245064 0.0362595 0.0290605 0.00419393 0.0405848 0.0313501 -0.00482545 0.0494431 0.0593025 0.00697877 0.0740826 -0.00301444 -0.0187729 -0.0139377 -0.0187113 -0.00171765 0.00124283 -0.00649291 -0.012836 -0.00425632 0.0099625 -0.0232125 -0.0207039 -0.0144792 -0.00981692 -0.0189687 -0.0166281 -0.00105268 -0.025515 -0.0266713 -0.0244359 -0.0181654 -0.0280008 -0.0290169 -0.0224766 -0.0267588 -0.0286444 -0.0327484 -0.0216579 -0.0369856 -0.0362433
internal_weight=2065 611 1454 573 343 301 369 219 92 277 181 143 230 95 166 1085 673 512 82 161 120 223 133 75 408 355 199 158 122 96 104 90 412 334 141 86 193 106 86 156 109 72 87 78
internal_count=2065 611 1454 573 343 301 369 219 92 277 181 143 230 95 166 1085 673 512 82 161 120 223 133 75 408 355 199 158 122 96 104 90 412 334 141 86 193 106 86 156 109 72 87 78
is_linear=0
shrinkage=0.1


end of trees

feature_importances:
Column_142=27
Column_140=21
Column_139=17
Column_1=16
Column_138=16
Column_141=15
Column_143=15
Column_69=4
Column_108=4
Column_12=3
Column_93=3
Column_121=3
Column_13=2
Column_40=2
Column_50=2
Column_59=2
Column_106=2
Column_122=2
Column_126=2
Column_7=1
Column_17=1
Column_42=1
Column_53=1
Column_58=1
Column_61=1
Column_80=1
Column_83=1
Column_97=1
Column_109=1
Column_119=1

parameters:
[boosting: gbdt]
[objective: regression]
[metric: l2]
[tree_learner: serial]
[device_type: cpu]
[data_sample_strategy: bagging]
[data: ]
[valid: ]
[num_iterations: 1000]
[learning_rate: 0.1]
[num_leaves: 1598]
[num_threads: 0]
[seed: 1234]
[deterministic: 0]
[force_col_wise: 0]
[force_row_wise: 0]
[histogram_pool_size: -1]
[max_depth: -1]
[min_data_in_leaf: 36]
[min_sum_hessian_in_leaf: 0.001]
[bagging_fraction: 0.970698]
[pos_bagging_fraction: 1]
[neg_bagging_fraction: 1]
[bagging_freq: 7]
[bagging_seed: 213]
[bagging_by_query: 0]
[feature_fraction: 0.861311]
[feature_fraction_bynode: 1]
[feature_fraction_seed: 8758]
[extra_trees: 0]
[extra_seed: 7717]
[early_stopping_round: 0]
[early_stopping_min_delta: 0]
[first_metric_only: 0]
[max_delta_step: 0]
[lambda_l1: 2.8401e-06]
[lambda_l2: 3.07736e-06]
[linear_lambda: 0]
[min_gain_to_split: 0]
[drop_rate: 0.1]
[max_drop: 50]
[skip_drop: 0.5]
[xgboost_dart_mode: 0]
[uniform_drop: 0]
[drop_seed: 12761]
[top_rate: 0.2]
[other_rate: 0.1]
[min_data_per_group: 100]
[max_cat_threshold: 32]
[cat_l2: 10]
[cat_smooth: 10]
[max_cat_to_onehot: 4]
[top_k: 20]
[monotone_constraints: ]
[monotone_constraints_method: basic]
[monotone_penalty: 0]
[feature_contri: ]
[forcedsplits_filename: ]
[refit_decay_rate: 0.9]
[cegb_tradeoff: 1]
[cegb_penalty_split: 0]
[cegb_penalty_feature_lazy: ]
[cegb_penalty_feature_coupled: ]
[path_smooth: 0]
[interaction_constraints: ]
[verbosity: -1]
[saved_feature_importance_type: 0]
[use_quantized_grad: 0]
[num_grad_quant_bins: 4]
[quant_train_renew_leaf: 0]
[stochastic_rounding: 1]
[linear_tree: 0]
[max_bin: 255]
[max_bin_by_feature: ]
[min_data_in_bin: 3]
[bin_construct_sample_cnt: 200000]
[data_random_seed: 4068]
[is_enable_sparse: 1]
[enable_bundle: 1]
[use_missing: 1]
[zero_as_missing: 0]
[feature_pre_filter: 0]
[pre_partition: 0]
[two_round: 0]
[header: 0]
[label_column: ]
[weight_column: ]
[group_column: ]
[ignore_column: ]
[categorical_feature: ]
[forcedbins_filename: ]
[precise_float_parser: 0]
[parser_config_file: ]
[objective_seed: 23056]
[num_class: 1]
[is_unbalance: 0]
[scale_pos_weight: 1]
[sigmoid: 1]
[boost_from_average: 1]
[reg_sqrt: 0]
[alpha: 0.9]
[fair_c: 1]
[poisson_max_delta_step: 0.7]
[tweedie_variance_power: 1.5]
[lambdarank_truncation_level: 30]
[lambdarank_norm: 1]
[label_gain: ]
[lambdarank_position_bias_regularization: 0]
[eval_at: ]
[multi_error_top_k: 1]
[auc_mu_weights: ]
[num_machines: 1]
[local_listen_port: 12400]
[time_out: 120]
[machine_list_filename: ]
[machines: ]
[gpu_platform_id: -1]
[gpu_device_id: -1]
[gpu_use_dp: 0]
[num_gpu: 1]

end of parameters

pandas_categorical:null
