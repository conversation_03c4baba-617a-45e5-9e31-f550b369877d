{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "markdown", "metadata": {"id": "suq8_Qc2PLUs"}, "source": ["# Preprocessing"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle</th>\n", "      <th>Strategic_Offer_Architecture.OfferStructure.No_Price</th>\n", "      <th>Strategic_Offer_Architecture.CTA_Present</th>\n", "      <th>Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown</th>\n", "      <th>...</th>\n", "      <th>Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Clinical_Room</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.White_Background</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Recorded</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Self_Recording</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>DURACION</th>\n", "      <th>RESULTADOS</th>\n", "      <th>AD SPEND</th>\n", "    </tr>\n", "    <tr>\n", "      <th>original_index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>28.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>14.28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>20.31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>2.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>36</td>\n", "      <td>0.0</td>\n", "      <td>5.04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 36 columns</p>\n", "</div>"], "text/plain": ["                Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction  \\\n", "original_index                                                                       \n", "0                                                               1                    \n", "1                                                               1                    \n", "2                                                               1                    \n", "3                                                               1                    \n", "4                                                               1                    \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction  \\\n", "original_index                                                                             \n", "0                                                               0                          \n", "1                                                               0                          \n", "2                                                               0                          \n", "3                                                               0                          \n", "4                                                               0                          \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting  \\\n", "original_index                                                                       \n", "0                                                               0                    \n", "1                                                               0                    \n", "2                                                               0                    \n", "3                                                               0                    \n", "4                                                               1                    \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting  \\\n", "original_index                                                                          \n", "0                                                               0                       \n", "1                                                               0                       \n", "2                                                               0                       \n", "3                                                               0                       \n", "4                                                               0                       \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity  \\\n", "original_index                                                                   \n", "0                                                               0                \n", "1                                                               0                \n", "2                                                               0                \n", "3                                                               0                \n", "4                                                               0                \n", "\n", "                Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno  \\\n", "original_index                                                             \n", "0                                                               1          \n", "1                                                               1          \n", "2                                                               1          \n", "3                                                               1          \n", "4                                                               1          \n", "\n", "                Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle  \\\n", "original_index                                                                    \n", "0                                                               1                 \n", "1                                                               1                 \n", "2                                                               1                 \n", "3                                                               1                 \n", "4                                                               1                 \n", "\n", "                Strategic_Offer_Architecture.OfferStructure.No_Price  \\\n", "original_index                                                         \n", "0                                                               0      \n", "1                                                               0      \n", "2                                                               0      \n", "3                                                               0      \n", "4                                                               0      \n", "\n", "                Strategic_Offer_Architecture.CTA_Present  \\\n", "original_index                                             \n", "0                                                      1   \n", "1                                                      1   \n", "2                                                      1   \n", "3                                                      1   \n", "4                                                      1   \n", "\n", "                Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown  \\\n", "original_index                                                                    \n", "0                                                               1                 \n", "1                                                               1                 \n", "2                                                               1                 \n", "3                                                               1                 \n", "4                                                               1                 \n", "\n", "                ...  \\\n", "original_index  ...   \n", "0               ...   \n", "1               ...   \n", "2               ...   \n", "3               ...   \n", "4               ...   \n", "\n", "                Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker  \\\n", "original_index                                                                        \n", "0                                                               0                     \n", "1                                                               0                     \n", "2                                                               0                     \n", "3                                                               0                     \n", "4                                                               0                     \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.Clinical_Room  \\\n", "original_index                                                              \n", "0                                                               1           \n", "1                                                               1           \n", "2                                                               1           \n", "3                                                               1           \n", "4                                                               1           \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom  \\\n", "original_index                                                              \n", "0                                                               0           \n", "1                                                               0           \n", "2                                                               0           \n", "3                                                               0           \n", "4                                                               0           \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.White_Background  \\\n", "original_index                                                                 \n", "0                                                               0              \n", "1                                                               0              \n", "2                                                               0              \n", "3                                                               0              \n", "4                                                               0              \n", "\n", "                Production_Visual_Aesthetics.Camera_Angle.Recorded  \\\n", "original_index                                                       \n", "0                                                               1    \n", "1                                                               1    \n", "2                                                               1    \n", "3                                                               1    \n", "4                                                               1    \n", "\n", "                Production_Visual_Aesthetics.Camera_Angle.Self_Recording  \\\n", "original_index                                                             \n", "0                                                               0          \n", "1                                                               0          \n", "2                                                               0          \n", "3                                                               0          \n", "4                                                               0          \n", "\n", "                FACIAL/CORPORAL  DURACION  RESULTADOS  AD SPEND  \n", "original_index                                                   \n", "0                        Facial        27         0.0     28.35  \n", "1                        Facial        27         0.0     14.28  \n", "2                        Facial        27         0.0     20.31  \n", "3                        Facial        27         0.0      2.91  \n", "4                        Facial        36         0.0      5.04  \n", "\n", "[5 rows x 36 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Read CSV file\n", "file_path = '/home/<USER>/Documents/Clibel/ML_ads/data/ml_training_data.csv'\n", "df = pd.read_csv(file_path, delimiter=',', encoding='utf-8', index_col=0)\n", "df.head()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2907, 36)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["RESULTADOS    318\n", "AD SPEND      303\n", "dtype: int64"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# Count number of NaN values in each column\n", "nan_counts = df.isna().sum()\n", "nan_counts[nan_counts > 0]"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["idx_to_drop = df[df[\"RESULTADOS\"].isna()].index\n", "df.drop(idx_to_drop, inplace=True)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["idx_to_drop = df[df[\"AD SPEND\"].isna()].index\n", "df.drop(idx_to_drop, inplace=True)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle</th>\n", "      <th>Strategic_Offer_Architecture.OfferStructure.No_Price</th>\n", "      <th>Strategic_Offer_Architecture.CTA_Present</th>\n", "      <th>Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown</th>\n", "      <th>...</th>\n", "      <th>Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Clinical_Room</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.White_Background</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Recorded</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Self_Recording</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>DURACION</th>\n", "      <th>RESULTADOS</th>\n", "      <th>AD SPEND</th>\n", "    </tr>\n", "    <tr>\n", "      <th>original_index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>28.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>14.28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>20.31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "      <td>2.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>36</td>\n", "      <td>0.0</td>\n", "      <td>5.04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 36 columns</p>\n", "</div>"], "text/plain": ["                Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction  \\\n", "original_index                                                                       \n", "0                                                               1                    \n", "1                                                               1                    \n", "2                                                               1                    \n", "3                                                               1                    \n", "4                                                               1                    \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction  \\\n", "original_index                                                                             \n", "0                                                               0                          \n", "1                                                               0                          \n", "2                                                               0                          \n", "3                                                               0                          \n", "4                                                               0                          \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting  \\\n", "original_index                                                                       \n", "0                                                               0                    \n", "1                                                               0                    \n", "2                                                               0                    \n", "3                                                               0                    \n", "4                                                               1                    \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting  \\\n", "original_index                                                                          \n", "0                                                               0                       \n", "1                                                               0                       \n", "2                                                               0                       \n", "3                                                               0                       \n", "4                                                               0                       \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity  \\\n", "original_index                                                                   \n", "0                                                               0                \n", "1                                                               0                \n", "2                                                               0                \n", "3                                                               0                \n", "4                                                               0                \n", "\n", "                Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno  \\\n", "original_index                                                             \n", "0                                                               1          \n", "1                                                               1          \n", "2                                                               1          \n", "3                                                               1          \n", "4                                                               1          \n", "\n", "                Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle  \\\n", "original_index                                                                    \n", "0                                                               1                 \n", "1                                                               1                 \n", "2                                                               1                 \n", "3                                                               1                 \n", "4                                                               1                 \n", "\n", "                Strategic_Offer_Architecture.OfferStructure.No_Price  \\\n", "original_index                                                         \n", "0                                                               0      \n", "1                                                               0      \n", "2                                                               0      \n", "3                                                               0      \n", "4                                                               0      \n", "\n", "                Strategic_Offer_Architecture.CTA_Present  \\\n", "original_index                                             \n", "0                                                      1   \n", "1                                                      1   \n", "2                                                      1   \n", "3                                                      1   \n", "4                                                      1   \n", "\n", "                Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown  \\\n", "original_index                                                                    \n", "0                                                               1                 \n", "1                                                               1                 \n", "2                                                               1                 \n", "3                                                               1                 \n", "4                                                               1                 \n", "\n", "                ...  \\\n", "original_index  ...   \n", "0               ...   \n", "1               ...   \n", "2               ...   \n", "3               ...   \n", "4               ...   \n", "\n", "                Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker  \\\n", "original_index                                                                        \n", "0                                                               0                     \n", "1                                                               0                     \n", "2                                                               0                     \n", "3                                                               0                     \n", "4                                                               0                     \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.Clinical_Room  \\\n", "original_index                                                              \n", "0                                                               1           \n", "1                                                               1           \n", "2                                                               1           \n", "3                                                               1           \n", "4                                                               1           \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom  \\\n", "original_index                                                              \n", "0                                                               0           \n", "1                                                               0           \n", "2                                                               0           \n", "3                                                               0           \n", "4                                                               0           \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.White_Background  \\\n", "original_index                                                                 \n", "0                                                               0              \n", "1                                                               0              \n", "2                                                               0              \n", "3                                                               0              \n", "4                                                               0              \n", "\n", "                Production_Visual_Aesthetics.Camera_Angle.Recorded  \\\n", "original_index                                                       \n", "0                                                               1    \n", "1                                                               1    \n", "2                                                               1    \n", "3                                                               1    \n", "4                                                               1    \n", "\n", "                Production_Visual_Aesthetics.Camera_Angle.Self_Recording  \\\n", "original_index                                                             \n", "0                                                               0          \n", "1                                                               0          \n", "2                                                               0          \n", "3                                                               0          \n", "4                                                               0          \n", "\n", "                FACIAL/CORPORAL  DURACION  RESULTADOS  AD SPEND  \n", "original_index                                                   \n", "0                        Facial        27         0.0     28.35  \n", "1                        Facial        27         0.0     14.28  \n", "2                        Facial        27         0.0     20.31  \n", "3                        Facial        27         0.0      2.91  \n", "4                        Facial        36         0.0      5.04  \n", "\n", "[5 rows x 36 columns]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle</th>\n", "      <th>Strategic_Offer_Architecture.OfferStructure.No_Price</th>\n", "      <th>Strategic_Offer_Architecture.CTA_Present</th>\n", "      <th>Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown</th>\n", "      <th>...</th>\n", "      <th>Hook_Deconstruction.Hook_Content_Features.Hook_Actual_Treatment_Shown</th>\n", "      <th>Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Clinical_Room</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.White_Background</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Recorded</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Self_Recording</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>DURACION</th>\n", "      <th>RESULTADOS</th>\n", "    </tr>\n", "    <tr>\n", "      <th>original_index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "<p>0 rows × 35 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction, Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction, Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting, Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting, Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity, Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno, Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle, Strategic_Offer_Architecture.OfferStructure.No_Price, Strategic_Offer_Architecture.CTA_Present, Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown, Strategic_Offer_Architecture.Ad_Features.Drawings, Hook_Deconstruction.Hook_People.Age.Young, Hook_Deconstruction.Hook_People.Age.Middle_Age, Hook_Deconstruction.Hook_People.Weight.Fat, Hook_Deconstruction.Hook_People_Weight_Skinny, Hook_Deconstruction.Hook_Narrative_Angle.Benefit_Focused, Hook_Deconstruction.Hook_Narrative_Angle.Problem_Focused, Hook_Deconstruction.Hook_Visual_Subject.Treatment_In_Process, Hook_Deconstruction.Hook_Text, Hook_Deconstruction.Hook_Motion_Video_Hook_Format_Style.Studio_Demonstration, Hook_Deconstruction.Hook_Motion_Video.Hook_Format_Style.Interview_Style, Hook_Deconstruction.Hook_Motion_Video.Hook_Format_Style.Static_Image_Only, Hook_Deconstruction.Hook_Audio_Elements.Hook_Voiceover_Narration, Hook_Deconstruction.Hook_Audio_Elements.Hook_Only_Voice, Hook_Deconstruction.Hook_Audio_Elements.Hook_Only_Music, Hook_Deconstruction.Hook_Content_Features.Hook_Actual_Treatment_Shown, Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker, Production_Visual_Aesthetics.Visual_Setting.Clinical_Room, Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom, Production_Visual_Aesthetics.Visual_Setting.White_Background, Production_Visual_Aesthetics.Camera_Angle.Recorded, Production_Visual_Aesthetics.Camera_Angle.Self_Recording, FACIAL/CORPORAL, DURACION, RESULTADOS]\n", "Index: []\n", "\n", "[0 rows x 35 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df.isna().any(axis=1)]"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["a = df[df.isna().any(axis=1)]"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["a.to_csv('/home/<USER>/Documents/Clibel/ML_ads/data/nans.csv', index=True)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["# id of the columns with NaN values\n", "nan_columns = nan_counts[nan_counts > 0].index.tolist()\n", "# print the id of the columns with NaN values\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["HAY FALLO EN LA GENERACIÓN DEL DATASET:\n", "- Facial/Corpora: Nombre solo Facial y todos NaN\n", "- Columnas desconocidas: '5', 'Okay'\n", "- Coste por resultado casi todo <PERSON>, puede ser que esté bien, podemos poner -1 (para más adelante)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## NULL FROM GENERATED DATA (GEMINI)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# find nan or null values\n", "nan_columns = df.columns[df.isna().any()].tolist()\n", "nan_columns"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0, 35)\n"]}], "source": ["# Get rows with NaN values\n", "nan_rows = df[df.isna().any(axis=1)]\n", "print(nan_rows.shape)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Delete the rows with NaN values\n", "df.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting</th>\n", "      <th>Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno</th>\n", "      <th>Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle</th>\n", "      <th>Strategic_Offer_Architecture.OfferStructure.No_Price</th>\n", "      <th>Strategic_Offer_Architecture.CTA_Present</th>\n", "      <th>Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown</th>\n", "      <th>...</th>\n", "      <th>Hook_Deconstruction.Hook_Content_Features.Hook_Actual_Treatment_Shown</th>\n", "      <th>Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Clinical_Room</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom</th>\n", "      <th>Production_Visual_Aesthetics.Visual_Setting.White_Background</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Recorded</th>\n", "      <th>Production_Visual_Aesthetics.Camera_Angle.Self_Recording</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>DURACION</th>\n", "      <th>RESULTADOS</th>\n", "    </tr>\n", "    <tr>\n", "      <th>original_index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Facial</td>\n", "      <td>36</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 35 columns</p>\n", "</div>"], "text/plain": ["                Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction  \\\n", "original_index                                                                       \n", "0                                                               1                    \n", "1                                                               1                    \n", "2                                                               1                    \n", "3                                                               1                    \n", "4                                                               1                    \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction  \\\n", "original_index                                                                             \n", "0                                                               0                          \n", "1                                                               0                          \n", "2                                                               0                          \n", "3                                                               0                          \n", "4                                                               0                          \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting  \\\n", "original_index                                                                       \n", "0                                                               0                    \n", "1                                                               0                    \n", "2                                                               0                    \n", "3                                                               0                    \n", "4                                                               1                    \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting  \\\n", "original_index                                                                          \n", "0                                                               0                       \n", "1                                                               0                       \n", "2                                                               0                       \n", "3                                                               0                       \n", "4                                                               0                       \n", "\n", "                Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity  \\\n", "original_index                                                                   \n", "0                                                               0                \n", "1                                                               0                \n", "2                                                               0                \n", "3                                                               0                \n", "4                                                               0                \n", "\n", "                Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno  \\\n", "original_index                                                             \n", "0                                                               1          \n", "1                                                               1          \n", "2                                                               1          \n", "3                                                               1          \n", "4                                                               1          \n", "\n", "                Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle  \\\n", "original_index                                                                    \n", "0                                                               1                 \n", "1                                                               1                 \n", "2                                                               1                 \n", "3                                                               1                 \n", "4                                                               1                 \n", "\n", "                Strategic_Offer_Architecture.OfferStructure.No_Price  \\\n", "original_index                                                         \n", "0                                                               0      \n", "1                                                               0      \n", "2                                                               0      \n", "3                                                               0      \n", "4                                                               0      \n", "\n", "                Strategic_Offer_Architecture.CTA_Present  \\\n", "original_index                                             \n", "0                                                      1   \n", "1                                                      1   \n", "2                                                      1   \n", "3                                                      1   \n", "4                                                      1   \n", "\n", "                Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown  \\\n", "original_index                                                                    \n", "0                                                               1                 \n", "1                                                               1                 \n", "2                                                               1                 \n", "3                                                               1                 \n", "4                                                               1                 \n", "\n", "                ...  \\\n", "original_index  ...   \n", "0               ...   \n", "1               ...   \n", "2               ...   \n", "3               ...   \n", "4               ...   \n", "\n", "                Hook_Deconstruction.Hook_Content_Features.Hook_Actual_Treatment_Shown  \\\n", "original_index                                                                          \n", "0                                                               1                       \n", "1                                                               1                       \n", "2                                                               1                       \n", "3                                                               1                       \n", "4                                                               1                       \n", "\n", "                Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker  \\\n", "original_index                                                                        \n", "0                                                               0                     \n", "1                                                               0                     \n", "2                                                               0                     \n", "3                                                               0                     \n", "4                                                               0                     \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.Clinical_Room  \\\n", "original_index                                                              \n", "0                                                               1           \n", "1                                                               1           \n", "2                                                               1           \n", "3                                                               1           \n", "4                                                               1           \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom  \\\n", "original_index                                                              \n", "0                                                               0           \n", "1                                                               0           \n", "2                                                               0           \n", "3                                                               0           \n", "4                                                               0           \n", "\n", "                Production_Visual_Aesthetics.Visual_Setting.White_Background  \\\n", "original_index                                                                 \n", "0                                                               0              \n", "1                                                               0              \n", "2                                                               0              \n", "3                                                               0              \n", "4                                                               0              \n", "\n", "                Production_Visual_Aesthetics.Camera_Angle.Recorded  \\\n", "original_index                                                       \n", "0                                                               1    \n", "1                                                               1    \n", "2                                                               1    \n", "3                                                               1    \n", "4                                                               1    \n", "\n", "                Production_Visual_Aesthetics.Camera_Angle.Self_Recording  \\\n", "original_index                                                             \n", "0                                                               0          \n", "1                                                               0          \n", "2                                                               0          \n", "3                                                               0          \n", "4                                                               0          \n", "\n", "                FACIAL/CORPORAL  DURACION  RESULTADOS  \n", "original_index                                         \n", "0                        Facial        27         0.0  \n", "1                        Facial        27         0.0  \n", "2                        Facial        27         0.0  \n", "3                        Facial        27         0.0  \n", "4                        Facial        36         0.0  \n", "\n", "[5 rows x 35 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["df = df.sample(frac=1, random_state=42).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["df.to_csv('/home/<USER>/Documents/Clibel/ML_ads/data/ads_ready.csv', index=True)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2589, 36)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CORRELATION OF GENERATED DATA WITH RESULTS"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'train_test_split' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[135]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Split\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m X_train, X_test, y_train, y_test = \u001b[43mtrain_test_split\u001b[49m(\n\u001b[32m      3\u001b[39m     X, y, test_size=\u001b[32m0.1\u001b[39m, random_state=\u001b[32m42\u001b[39m\n\u001b[32m      4\u001b[39m )\n\u001b[32m      5\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mDivisi<PERSON> de datos: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(X_train)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m para entrenamiento, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(X_test)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m para prueba.\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'train_test_split' is not defined"]}], "source": ["# Split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.1, random_state=42\n", ")\n", "print(f\"\\nDivisión de datos: {len(X_train)} para entrenamiento, {len(X_test)} para prueba.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define pipeline elements\n", "ss = StandardScaler()\n", "rs = RobustScaler()\n", "ohe = OneHotEncoder(handle_unknown='error', sparse_output=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pipelines\n", "preprocess1 = ColumnTransformer(\n", "    transformers=[\n", "        ('num', ss, numerical_features),\n", "        ('cat', ohe, categorical_features)\n", "    ],\n", "    remainder='passthrough'\n", ")\n", "\n", "# En principio no seberia ser mejor que el anterior ya que no debería haber outliers\n", "preprocess2 = ColumnTransformer(\n", "    transformers=[\n", "        ('num', rs, numerical_features),\n", "        ('cat', ohe, categorical_features)\n", "    ],\n", "    remainder='passthrough'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Engineering"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Deep Feature Synthesis (Featuretools + BorutaShap)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SciPy: 1.16.0\n"]}], "source": ["import scipy\n", "print(\"SciPy:\", scipy.__version__)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'binom_test' from 'scipy.stats' (/home/<USER>/miniconda3/envs/clibel/lib/python3.13/site-packages/scipy/stats/__init__.py)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mImportError\u001b[39m                               Trace<PERSON> (most recent call last)", "\u001b[36m<PERSON><PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[421]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m,\u001b[38;5;250m \u001b[39m\u001b[34;01mfeaturetools\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mft\u001b[39;00m,\u001b[38;5;250m \u001b[39m\u001b[34;01mlightgbm\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlgb\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mB<PERSON><PERSON><PERSON><PERSON>p\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m <PERSON><PERSON><PERSON><PERSON><PERSON>\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mf<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msklearn\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmetrics\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m roc_auc_score\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m# ------------ 0) Datos -----------------\u001b[39;00m\n\u001b[32m      6\u001b[39m \n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# Deep Feature Synthesis\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/clibel/lib/python3.13/site-packages/BorutaShap.py:9\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON>n\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01minspection\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m permutation_importance\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mscipy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msparse\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m issparse\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mscipy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mstats\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m binom_test, ks_2samp\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtqdm\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mauto\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tqdm\n", "\u001b[31mImportError\u001b[39m: cannot import name 'binom_test' from 'scipy.stats' (/home/<USER>/miniconda3/envs/clibel/lib/python3.13/site-packages/scipy/stats/__init__.py)"]}], "source": ["import pandas as pd, featuretools as ft, lightgbm as lgb\n", "from BorutaShap import BorutaShap\n", "from sklearn.metrics import roc_auc_score\n", "\n", "# ------------ 0) Datos -----------------\n", "\n", "# Deep Feature Synthesis\n", "es = ft.EntitySet(id=\"es\")\n", "es = es.add_dataframe(dataframe_name=\"main\",\n", "                      dataframe=df.reset_index(drop=False).rename(columns={'index': 'idx'}),\n", "                      index=\"idx\")\n", "\n", "feature_matrix, feature_defs = ft.dfs(\n", "    entityset              = es,\n", "    target_dataframe_name  = \"main\",\n", "    max_depth              = 2,          # 2 niveles suele ser buen punto de partida\n", "    agg_primitives         = ['mean','sum','count','max','min','std'],\n", "    trans_primitives       = ['add_numeric','subtract_numeric','divide_numeric',\n", "                              'month','year','weekday'],\n", "    verbose=True\n", ")\n", "\n", "\n", "# BorutaShap\n", "lgb_estimator = lgb.LGBMClassifier(random_state=0, n_estimators=300, n_jobs=-1)\n", "bs = BorutaShap(model=lgb_estimator, importance_measure='shap', classification=True) # Train with diferent models\n", "bs.fit(X=X_train, y=y_train, n_trials=100, random_state=0)\n", "\n", "X_train_sel = bs.transform(X_train)\n", "X_test_sel  = bs.transform(X_test)\n", "print(f\"Quedan {X_train_sel.shape[1]} features despu<PERSON> de Boruta\")\n", "\n", "# Train\n", "lgb_estimator.fit(X_train_sel, y_train)\n", "print(\"AUC DFS+Boruta:\", roc_auc_score(y_test,\n", "                                       lgb_estimator.predict_proba(X_test_sel)[:,1]))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### AutoFeat"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'autofeat'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[16]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mautofeat\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AutoFeatClassifier\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msklearn\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmetrics\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m roc_auc_score\n\u001b[32m      4\u001b[39m X_train, X_test, y_train, y_test = train_test_split(X_base, y,\n\u001b[32m      5\u001b[39m                                                     test_size=\u001b[32m.2\u001b[39m, stratify=y,\n\u001b[32m      6\u001b[39m                                                     random_state=\u001b[32m0\u001b[39m)\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'autofeat'"]}], "source": ["from autofeat import AutoFeatClassifier\n", "from sklearn.metrics import roc_auc_score\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X_base, y,\n", "                                                    test_size=.2, stratify=y,\n", "                                                    random_state=0)\n", "\n", "af = AutoFeatClassifier(feateng_steps=2, verbose=1)\n", "X_train_af = af.fit_transform(X_train, y_train)   # genera y selecciona\n", "X_test_af  = af.transform(X_test)\n", "\n", "# modelo ligero de ejemplo (tú puedes cambiarlo)\n", "from sklearn.linear_model import LogisticRegression\n", "clf = LogisticRegression(max_iter=1000).fit(X_train_af, y_train)\n", "print(\"AUC AutoFeat:\", roc_auc_score(y_test, clf.predict_proba(X_test_af)[:,1]))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bo<PERSON><PERSON>Shap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder, OrdinalEncoder\n", "import numpy as np\n", "\n", "# ---------------- 0) Separación columnas -----------------\n", "ordinal_cols = ['num1','num2']                 # ajusta nombres\n", "cat_cols     = [c for c in df.columns if c not in ordinal_cols+[TARGET]]\n", "\n", "\n", "X_enc = preprocess1.fit_transform(df.drop(columns=[TARGET]))\n", "feat_names = (\n", "    preprocess1.named_transformers_['ord'].get_feature_names_out(ordinal_cols).tolist() +\n", "    preprocess1.named_transformers_['ohe'].get_feature_names_out(cat_cols).tolist()\n", ")\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X_enc, y, test_size=.2,\n", "                                                    stratify=y, random_state=0)\n", "\n", "lgbm = lgb.LGBMClassifier(random_state=0, n_estimators=300, n_jobs=-1)\n", "boruta = BorutaShap(model=lgbm,\n", "                    importance_measure='shap',\n", "                    classification=True)\n", "boruta.fit(X=X_train, y=y_train, n_trials=100, random_state=0)\n", "X_train_sel = boruta.transform(X_train)\n", "X_test_sel  = boruta.transform(X_test)\n", "\n", "print(\"Quedan\", X_train_sel.shape[1], \"features\")\n", "lgbm.fit(X_train_sel, y_train)\n", "print(\"AUC Boruta:\", roc_auc_score(y_test, lgbm.predict_proba(X_test_sel)[:,1]))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Regularization L1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'feat_names' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[20]\u001b[39m\u001b[32m, line 18\u001b[39m\n\u001b[32m     14\u001b[39m selector = SelectFromModel(pipe_l1.named_steps[\u001b[33m'\u001b[39m\u001b[33mclf\u001b[39m\u001b[33m'\u001b[39m],\n\u001b[32m     15\u001b[39m                            prefit=\u001b[38;5;28;01mTrue\u001b[39;00m, threshold=\u001b[33m'\u001b[39m\u001b[33mmedian\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     17\u001b[39m mask = selector.get_support()\n\u001b[32m---> \u001b[39m\u001b[32m18\u001b[39m selected_names = [n \u001b[38;5;28;01mfor\u001b[39;00m n, keep \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(\u001b[43mfeat_names\u001b[49m, mask) \u001b[38;5;28;01mif\u001b[39;00m keep]\n\u001b[32m     19\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mSeleccionadas \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(selected_names)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m features vía L1\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'feat_names' is not defined"]}], "source": ["from sklearn.linear_model import LogisticRegression\n", "from sklearn.feature_selection import SelectFromModel\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "pipe_l1 = Pipeline([\n", "    ('prep', preprocess1),            # mismo ColumnTransformer de arriba\n", "    ('scale', StandardScaler(with_mean=False)),   # sparse → with_mean=False\n", "    ('clf',  LogisticRegression(penalty='l1', solver='saga',\n", "                                C=0.1, max_iter=2000))\n", "])\n", "\n", "pipe_l1.fit(X_train, y_train)\n", "selector = SelectFromModel(pipe_l1.named_steps['clf'],\n", "                           prefit=True, threshold='median')\n", "\n", "mask = selector.get_support()\n", "selected_names = [n for n, keep in zip(feat_names, mask) if keep]\n", "print(f\"Seleccionadas {len(selected_names)} features vía L1\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 0}