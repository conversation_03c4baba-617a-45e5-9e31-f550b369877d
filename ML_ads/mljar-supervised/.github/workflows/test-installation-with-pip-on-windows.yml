name: Test installation with pip on Windows

on: 
  schedule:
    - cron:  '0 8 * * 1'
  workflow_dispatch:
  
jobs:
  build:
    name: Run (${{ matrix.python-version }}, ${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest]
        python-version: ['3.9']

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Check Python version
        run: python --version

      - name: Upgrade pip
        run: python -m pip install --upgrade pip

      - name: Install MLJAR AutoML
        run: pip install mljar-supervised

      - name: Try to import
        run: python -c "import supervised; print(supervised.__version__)"
