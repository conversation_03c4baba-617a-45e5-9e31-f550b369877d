name: Tests

on: [ push,pull_request ]

jobs:
  build:

    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        python-version: [ '3.10']
        #os: [ ubuntu-latest, macos-latest, windows-latest ]
        #python-version: [ '3.8', '3.9', '3.10', '3.11' ]

    steps:
      - name: Install OS Dependencies
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get -y install graphviz

      - name: Install OS Dependencies
        if: matrix.os == 'macos-latest'
        run: |
          brew install graphviz

      - name: Install OS Dependencies
        if: matrix.os == 'windows-latest'
        run: |
          choco install graphviz
      - uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install Python Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install --upgrade setuptools
          pip install -U importlib-metadata>=1.7.0
          pip install -U -r requirements.txt
          pip install -U -r requirements_dev.txt
          pip install ipython
          python setup.py install
      - name: Test with pytest
        run: |
          pytest tests --cov=supervised/
    continue-on-error: true
