import pandas as pd
import numpy as np
import shutil

from supervised.automl import AutoML

from sklearn.model_selection import train_test_split
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler, OneHotEncoder, RobustScaler, OrdinalEncoder
from sklearn.compose import ColumnTransformer
from sklearn.metrics import mean_squared_error, r2_score, mean_tweedie_deviance, mean_absolute_error

from joblib import dump, load

import warnings
warnings.filterwarnings("ignore")

df = pd.read_csv('/home/<USER>/Documents/Clibel/ML_ads/data/ads_ready.csv', index_col=0)
df

df = pd.read_csv('/home/<USER>/Documents/Clibel/ML_ads/data/ads_ready.csv', index_col=0)

zeros = (df['AD SPEND'] == float(0))
zeros_count = zeros.sum()
print(f'There are {zeros_count} rows where AD SPEND is zero.')
zeros_idx = zeros[zeros].index
df.drop(zeros_idx, inplace=True)
print(f'Dropped rows where AD SPEND is zero.')

y = df[['RESULTADOS', 'AD SPEND']]
y

X = df
X

x_train, x_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=0, stratify=(y['RESULTADOS'] > 0).astype(int))

# Stage A target on ALL rows
yA_train = (y_train["RESULTADOS"] > 0).astype(int)
yA_test  = (y_test["RESULTADOS"]  > 0).astype(int)
print(yA_train.index)
xA_train = x_train[yA_train.index]
xA_test  = x_test[yA_test.index]

# Stage B target on winners only (deposits>0)
mask_B_train = y_train["RESULTADOS"] > 0
mask_B_test  = y_test["RESULTADOS"]  > 0

yB_train = y_train.loc[mask_B_train, "AD SPEND"] / y_train.loc[mask_B_train, "RESULTADOS"]
yB_test  = y_test.loc[mask_B_test,  "AD SPEND"] / y_test.loc[mask_B_test,  "RESULTADOS"]
xB_train = x_train[yB_train.index]
xB_test  = x_test[yB_test.index]

# Log-transform for stability
#yB_train = np.log1p(yB_train)
#yB_test  = np.log1p(yB_test)


yB_train

yA_train.hist()

yA_test.hist()

yB_train.hist()

yB_test.hist()

print(f"Train data A model: {x_train.shape}, {yA_train.shape}")
print(f"Test data A model: {x_test.shape}, {yA_test.shape}")
print(f"Train data B model: {x_train.shape}, {yB_train.shape}")
print(f"Test data B model: {x_test.shape}, {yB_test.shape}")

numerical_features = [
    'DURACION'
]
categorical_features = [
    'FACIAL/CORPORAL'
]
'''
categorical_features = [
    'Strategic_Offer_Architecture.Sub_Treatment_Focus.Wrinkle_Reduction',
    'Strategic_Offer_Architecture.Sub_Treatment_Focus.Abdominal_Fat_Reduction',
    'Strategic_Offer_Architecture.Sub_Treatment_Focus.Jawline_Sculpting',
    'Strategic_Offer_Architecture.Sub_Treatment_Focus.Glute_Toning/Lifting',
    'Strategic_Offer_Architecture.Sub_Treatment_Focus.Arm_Flacidity',
    'Strategic_Offer_Architecture.Offer_Structure.Dos_Por_Uno',
    'Strategic_Offer_Architecture.Offer_Structure.Value_Stack_Bundle',
    'Strategic_Offer_Architecture.OfferStructure.No_Price',
    'Strategic_Offer_Architecture.CTA_Present',
    'Strategic_Offer_Architecture.Ad_Features.Actual_Treatment_Shown',
    'Strategic_Offer_Architecture.Ad_Features.Drawings',
    'Hook_Deconstruction.Hook_People.Age.Young',
    'Hook_Deconstruction.Hook_People.Age.Middle_Age',
    'Hook_Deconstruction.Hook_People.Weight.Fat',
    'Hook_Deconstruction.Hook_People_Weight_Skinny',
    'Hook_Deconstruction.Hook_Narrative_Angle.Benefit_Focused',
    'Hook_Deconstruction.Hook_Narrative_Angle.Problem_Focused',
    'Hook_Deconstruction.Hook_Visual_Subject.Treatment_In_Process',
    'Hook_Deconstruction.Hook_Text',
    'Hook_Deconstruction.Hook_Motion_Video_Hook_Format_Style.Studio_Demonstration',
    'Hook_Deconstruction.Hook_Motion_Video.Hook_Format_Style.Interview_Style',
    'Hook_Deconstruction.Hook_Motion_Video.Hook_Format_Style.Static_Image_Only',
    'Hook_Deconstruction.Hook_Audio_Elements.Hook_Voiceover_Narration',
    'Hook_Deconstruction.Hook_Audio_Elements.Hook_Only_Voice',
    'Hook_Deconstruction.Hook_Audio_Elements.Hook_Only_Music',
    'Hook_Deconstruction.Hook_Content_Features.Hook_Actual_Treatment_Shown',
    'Hook_Deconstruction.Hook_Content_Features.Hook_Ad_Recording_Speaker',
    'Production_Visual_Aesthetics.Visual_Setting.Clinical_Room',
    'Production_Visual_Aesthetics.Visual_Setting.Home_Bathroom',
    'Production_Visual_Aesthetics.Visual_Setting.White_Background',
    'Production_Visual_Aesthetics.Camera_Angle.Recorded',
    'Production_Visual_Aesthetics.Camera_Angle.Self_Recording'
]

multi_select_cols = [
    'Strategic_Offer_Architecture.Ad_Features',
    'Hook_Deconstruction.Hook_Content_Features',
    'Strategic_Offer_Architecture.Sub_Treatment_Focus',
    'Hook_Deconstruction.Hook_Visual_Subject']
'''

# Define pipeline elements
ss = StandardScaler()
rs = RobustScaler()
oe = OrdinalEncoder(handle_unknown='ignore') # No deberia haber ningún -1
ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False)

# Pipelines
preprocess1 = ColumnTransformer(
    transformers=[
        ('num', ss, numerical_features),
        ('cat', ohe, categorical_features)
    ],
    remainder='passthrough'
)

# En principio no seberia ser mejor que el anterior ya que no debería haber outliers
preprocess2 = ColumnTransformer(
    transformers=[
        ('num', rs, numerical_features),
        ('cat', ohe, categorical_features)
    ],
    remainder='passthrough'
)

automl = AutoML(
    mode='Optuna',
    ml_task='binary_classification',
    algorithms=['Baseline','Random Forest','Extra Trees',
                'Xgboost','LightGBM','CatBoost'],
    results_path='AutoML_model1A',
    optuna_time_budget=60*1,
    eval_metric='logloss'
)

pipe = Pipeline([('prep1A', preprocess1), ('model1A', automl)])
pipe.fit(x_train, yA_train)

dump(pipe,   'pipelines/pipeline1A.pkl') 
shutil.make_archive('AutoML_model1A', 'zip', base_dir='AutoML_model1A')
print("✅  Pipeline guardada")

automl = AutoML(
    mode='Optuna',
    ml_task='regression',
    algorithms=['Baseline','Random Forest','Extra Trees',
                'Xgboost','LightGBM','CatBoost'],
    results_path='AutoML_model1B',
    optuna_time_budget=60*1,
    eval_metric='mae' # ahora no tenemos outliers, solo entrenamos con ads con resultados
)

pipe = Pipeline([('prep1B', preprocess1), ('model1B', automl)])
pipe.fit(x_train, yB_train)

dump(pipe,   'pipeline1B.pkl') 
shutil.make_archive('AutoML_model1B', 'zip', base_dir='AutoML_model1B')
print("✅  Pipeline guardada")

automl = AutoML(
    mode='Optuna',
    ml_task='regression',
    algorithms=['Baseline','Random Forest','Extra Trees',
                'Xgboost','LightGBM','CatBoost'],
    results_path='AutoML_model1',
    optuna_time_budget=60*5,
    eval_metric='mse'
)

pipe = Pipeline([('prep', preprocess1), ('model', automl)])
pipe.fit(x_train, y_train)

dump(pipe,   'pipeline1.pkl') 
shutil.make_archive('AutoML_model1', 'zip', base_dir='AutoML_model1')
print("✅  Pipeline guardada")

automl2 = AutoML(
    mode='Optuna',
    ml_task='regression',
    algorithms=['Baseline','Random Forest','Extra Trees',
                'Xgboost','LightGBM','CatBoost'],
    results_path='AutoML_model2',
    optuna_time_budget=60*10,
    eval_metric='mse'
)

pipe2 = Pipeline([('prep2', preprocess2), ('model2', automl2)])
pipe2.fit(x_train, y_train)

dump(pipe2,   'pipeline2.pkl') 
shutil.make_archive('AutoML_model2', 'zip', base_dir='AutoML_model2')
print("✅  Pipeline guardada")

optuna_init_params = {
    # LightGBM
    "lightgbm": {
        "objective":       "tweedie",
        "tweedie_variance_power": 1.3,   # 1<P<2  → mezcla Poisson-Gamma
        "metric":          "tweedie"
    },
    # XGBoost
    "xgboost": {
        "objective":       "reg:tweedie",
        "tweedie_variance_power": 1.3,
        "eval_metric":     "tweedie-nloglik"
    },
    # CatBoost
    "catboost": {
        "loss_function":   "Tweedie:variance_power=1.3"
    }
}

automl3 = AutoML(
    mode='Optuna',
    ml_task='regression',
    algorithms=['Baseline','Random Forest','Extra Trees',
                'Xgboost','LightGBM','CatBoost'],
    optuna_init_params=optuna_init_params,
    results_path='AutoML_model3',
    optuna_time_budget=60*10,
    eval_metric='mse'
)

pipe2 = Pipeline([('prep3', preprocess1), ('model3', automl3)])
pipe2.fit(x_train, y_train)

dump(pipe2,   'pipeline3.pkl') 
shutil.make_archive('AutoML_model3', 'zip', base_dir='AutoML_model3')
print("✅  Pipeline guardada")

pipe1 = load('pipeline1.pkl')
y_pred_model1 = pipe1.predict(x_test)
rmse_model1 = mean_squared_error(y_test, y_pred_model1)
r2_model1 = r2_score(y_test, y_pred_model1)
tweedie_model1 = mean_tweedie_deviance(y_test, y_pred_model1, power=1.3)
mae_model1 = mean_absolute_error(y_test, y_pred_model1)
print(f"MSE Model 1: {rmse_model1}")
print(f"R2 Model 1: {r2_model1}")
print(f"Tweedie Model 1: {tweedie_model1}")
print(f"MAE Model 1: {mae_model1}")

pipe1 = load('pipeline1.pkl')
pipe2 = load('pipeline2.pkl')
pipe3 = load('ads_pipeline3.pkl')

y_pred_model1 = pipe1.predict(x_test)
y_pred_model2 = pipe2.predict(x_test)
y_pred_model3 = pipe3.predict(x_test)

# Métricas “clásicas” (RMSE y MAE)
rmse_mse1  = mean_squared_error(y_test, y_pred_model1)
rmse_mse2  = mean_squared_error(y_test, y_pred_model2)
rmse_twd  = mean_squared_error(y_test, y_pred_model2)

mae_mse1   = mean_absolute_error(y_test, y_pred_model1)
mae_mse2   = mean_absolute_error(y_test, y_pred_model2)
mae_twd   = mean_absolute_error(y_test, y_pred_model3)

# Métrica Tweedie (power 1.5)
dev_mse1   = mean_tweedie_deviance(y_test, y_pred_model1, power=1.3)
dev_mse2   = mean_tweedie_deviance(y_test, y_pred_model2, power=1.3)
dev_twd   = mean_tweedie_deviance(y_test, y_pred_model3, power=1.3)

# R² score
r2_mse1    = r2_score(y_test, y_pred_model1)
r2_mse2    = r2_score(y_test, y_pred_model2)
r2_twd    = r2_score(y_test, y_pred_model3)

print(f"PIPE 1   ->  RMSE: {rmse_mse1:.2f} | MSE: {mae_mse1:.2f} | Tweedie: {dev_mse1:.2f} | R²: {r2_mse1:.2f}")
print(f"PIPE 2   ->  RMSE: {rmse_mse2:.2f} | MSE: {mae_mse2:.2f} | Tweedie: {dev_mse2:.2f} | R²: {r2_mse2:.2f}")
print(f"TWEEDIE (1.3)-> RMSE: {rmse_twd :.2f} | MSE2: {mae_twd :.2f} | Tweedie: {dev_twd :.2f} | R²: {r2_twd:.2f}")


x_test1 = preprocess1.transform(x_test)
x_test2 = preprocess2.transform(x_test)

predictions = model.predict(x_test1)
print("Test MSE:", mean_squared_error(y_test, predictions))

numerical_features = [
    'Hook_Deconstruction.Hook_Duration_Secs',
    'Production_Visual_Aesthetics.Video_Duration_Secs'
]
categorical_features = [
    'Strategic_Offer_Architecture.Primary_Treatment_Subject',
    'Strategic_Offer_Architecture.Offer_Structure', 'Strategic_Offer_Architecture.Ad_Funnel_Stage',
    'Strategic_Offer_Architecture.CTA_Present', 'Hook_Deconstruction.Hook_Narrative_Angle',
    'Hook_Deconstruction.Hook_Emotional_Tone',
    'Hook_Deconstruction.Hook_Text_Type_OCR', 'Hook_Deconstruction.Hook_Motion_Video',
    'Hook_Deconstruction.Hook_Format_Style', 'Hook_Deconstruction.Hook_Audio_Elements',
    'Production_Visual_Aesthetics.Visual_Setting', 'Production_Visual_Aesthetics.Lighting_Style',
    'Production_Visual_Aesthetics.Camera_Angle', 'Production_Visual_Aesthetics.Editing_Pacing',
    'Production_Visual_Aesthetics.Color_Grading_Scheme', 'Production_Visual_Aesthetics.Focal_Length_Style',
    'Production_Visual_Aesthetics.Sound_Score'
]
multi_select_cols = [
    'Strategic_Offer_Architecture.Ad_Features',
    'Hook_Deconstruction.Hook_Content_Features',
    'Strategic_Offer_Architecture.Sub_Treatment_Focus',
    'Hook_Deconstruction.Hook_Visual_Subject']

ss = StandardScaler()
ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False)
msd = MultiSelectDummies(sep='|')

preprocess = ColumnTransformer(
    transformers=[
        ('num', ss, numerical_features),
        ('cat', ohe, categorical_features),
        ('multi', msd, multi_select_cols)
    ],
    remainder='passthrough'
)


automl = AutoML(
    mode='Optuna',
    ml_task='regression',
    algorithms=['Baseline','Random Forest','Extra Trees',
                'Xgboost','LightGBM','CatBoost'],
    results_path='AutoML_ads',
    optuna_time_budget=60*5,
    eval_metric='mse'
)

pipe = Pipeline([('prep', preprocess), ('model', automl)])
pipe.fit(x_train, y_train)

dump(pipe,   'ads_pipeline.pkl') 
shutil.make_archive('AutoML_ads', 'zip', base_dir='AutoML_ads')
print("✅  Pipeline guardada")

dump(model, "/home/<USER>/Documents/Clibel/ML_ads/mljar-supervised/pipelines/ads_pipeline.pkl") 