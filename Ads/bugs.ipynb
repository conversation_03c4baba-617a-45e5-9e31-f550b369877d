{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e2143435", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 4, "id": "13133021", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NOTES</th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "      <th>AD NAME</th>\n", "      <th>FULL NAME</th>\n", "      <th>AD LINK</th>\n", "      <th>gemini_tags_test</th>\n", "      <th>RESULTADOS</th>\n", "      <th>COSTE POR RESULTADO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON>res 4, OP4</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "      <td>ad1v, *OPATRA MAQUINA*, [Madres 4, OP4], B&amp;F 3...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad1v, *OPAT...</td>\n", "      <td>OP, Facial, C39, -  -, ad1v, OPATRA MAQUINA...</td>\n", "      <td>Error: Request failed for https://generativela...</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON><PERSON> 4, OP1</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "      <td>ad2v, *OPATRA MAQUINA*, [Madres 4, OP1], B&amp;F 3...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad2v, *OPAT...</td>\n", "      <td>OP, Facial, C39, -  -, ad2v, OPATRA MAQUINA...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4, B&amp;F3</td>\n", "      <td>OP6, valuestack</td>\n", "      <td>ad3v, *OPATRA MAQUINA*, [Madres 4, B&amp;F3], OP6,...</td>\n", "      <td>O<PERSON>, Facial, C39, - BROAD -, ad3v, *OPAT...</td>\n", "      <td>OP, <PERSON>ac<PERSON>, C39, -  -, ad3v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4</td>\n", "      <td>B&amp;F 3, OP1, OP6, valuestack</td>\n", "      <td>ad4v, *OPATRA MAQUINA*, [Madres 4], B&amp;F 3, OP1...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad4v, *OPAT...</td>\n", "      <td>OP, <PERSON>ac<PERSON>, C39, -  -, ad4v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>OP, Facial, C39, -  -, ad1v, OPATRA MAQUINA, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>OP3</td>\n", "      <td>B$F 3, <PERSON><PERSON><PERSON>, <PERSON>stack , OP6</td>\n", "      <td>ad5v, *OPATRA MAQUINA*, [OP3], B$F 3, Madres4,...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad5v, *OPAT...</td>\n", "      <td>OP, Facial, C39, -  -, ad5v, OPATRA MAQUINA, ...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NOTES DATE CREATED USED?     CENTRO FACIAL/CORPORAL CAMPAIGN # AUDIENCE  \\\n", "0   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "1   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "2   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "3   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "4   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "\n", "   Ad#           ANGLE            HOOK                          FEATURES  \\\n", "0  1.0  OPATRA MAQUINA   Madres 4, OP4            B&F 3, OP6, valuestack   \n", "1  2.0  OPATRA MAQUINA   Madres 4, OP1            B&F 3, OP6, valuestack   \n", "2  3.0  OPATRA MAQUINA  Madres 4, B&F3                   OP6, valuestack   \n", "3  4.0  OPATRA MAQUINA        Madres 4       B&F 3, OP1, OP6, valuestack   \n", "4  5.0  OPATRA MAQUINA             OP3  B$F 3, <PERSON><PERSON>4, <PERSON><PERSON><PERSON> , OP6   \n", "\n", "                                             AD NAME  \\\n", "0  ad1v, *OPATRA MAQUINA*, [Madres 4, OP4], B&F 3...   \n", "1  ad2v, *OPATRA MAQUINA*, [Madres 4, OP1], B&F 3...   \n", "2  ad3v, *OPATRA MAQUINA*, [Madres 4, B&F3], OP6,...   \n", "3  ad4v, *OPATRA MAQUINA*, [Madres 4], B&F 3, OP1...   \n", "4  ad5v, *OPATRA MAQUINA*, [OP3], B$F 3, Madres4,...   \n", "\n", "                                           FULL NAME  \\\n", "0  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad1v, *OPAT...   \n", "1  OP <PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad2v, *OPAT...   \n", "2  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad3v, *OPAT...   \n", "3  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad4v, *OPAT...   \n", "4  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad5v, *OPAT...   \n", "\n", "                                             AD LINK  \\\n", "0     OP, <PERSON><PERSON><PERSON>, C39, -  -, ad1v, OPATRA MAQUINA...   \n", "1     OP, Facial, C39, -  -, ad2v, OPATRA MAQUINA...   \n", "2  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad3v, OPATRA MAQUINA, [...   \n", "3  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad4v, OPATRA MAQUINA, [...   \n", "4   OP, <PERSON><PERSON>ial, C39, -  -, ad5v, OPATRA MAQUINA, ...   \n", "\n", "                                    gemini_tags_test RESULTADOS  \\\n", "0  Error: Request failed for https://generativela...  Not Found   \n", "1                                                NaN  Not Found   \n", "2                                                NaN  Not Found   \n", "3                                                NaN  Not Found   \n", "4                                                NaN  Not Found   \n", "\n", "                                 COSTE POR RESULTADO  \n", "0                                                NaN  \n", "1                                                NaN  \n", "2                                                NaN  \n", "3   OP, <PERSON><PERSON><PERSON>, C39, -  -, ad1v, OPATRA MAQUINA, ...  \n", "4                                                NaN  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('data/ads_raw.csv')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "96474165", "metadata": {}, "outputs": [], "source": ["ad_link = df['AD LINK']"]}, {"cell_type": "code", "execution_count": 6, "id": "24165377", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" \n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "9\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "4\n", ",\n", " \n", "O\n", "P\n", "4\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "3\n", ",\n", " \n", "O\n", "P\n", "6\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "9\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "4\n", ",\n", " \n", "O\n", "P\n", "1\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "3\n", ",\n", " \n", "O\n", "P\n", "6\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "9\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "4\n", ",\n", " \n", "B\n", "&\n", "F\n", "3\n", "]\n", ",\n", " \n", "O\n", "P\n", "6\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "9\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "4\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "3\n", ",\n", " \n", "O\n", "P\n", "1\n", ",\n", " \n", "O\n", "P\n", "6\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "9\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "O\n", "P\n", "3\n", "]\n", ",\n", " \n", "B\n", "$\n", "F\n", " \n", "3\n", ",\n", " \n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", "4\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", " \n", ",\n", " \n", "O\n", "P\n", "6\n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "0\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "1\n", "0\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "0\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "5\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "0\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "7\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "0\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "O\n", "P\n", "3\n", ",\n", " \n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "1\n", "0\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "0\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "O\n", "P\n", "3\n", ",\n", " \n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "5\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "1\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "*\n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "\"\n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "\"\n", "'\n", "*\n", ",\n", " \n", "[\n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "1\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "*\n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", "*\n", ",\n", " \n", "[\n", "O\n", "P\n", "6\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "1\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "*\n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", "*\n", ",\n", " \n", "[\n", "O\n", "P\n", "2\n", ",\n", " \n", "O\n", "P\n", "6\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", " \n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "1\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "*\n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", "*\n", ",\n", " \n", "[\n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", "\"\n", "'\n", "a\n", "r\n", "t\n", "i\n", "c\n", "u\n", "l\n", "o\n", "'\n", "'\n", "]\n", ",\n", " \n", "O\n", "P\n", "2\n", ",\n", " \n", "O\n", "P\n", "6\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "1\n", ",\n", " \n", "-\n", " \n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "*\n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", "*\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "5\n", " \n", "m\n", "u\n", "t\n", "e\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", "]\n", ",\n", " \n", "O\n", "P\n", "2\n", ",\n", " \n", "O\n", "P\n", "6\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", "M\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "6\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", "M\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "7\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", "M\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "6\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "2\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", "M\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "7\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "2\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", "M\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "S\n", "A\n", "R\n", "A\n", " \n", "2\n", "5\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "2\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "B\n", "&\n", "F\n", " \n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "4\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", " \n", "A\n", "E\n", "M\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "B\n", "&\n", "F\n", " \n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "4\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "3\n", ",\n", "5\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", " \n", "A\n", "E\n", "M\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "B\n", "&\n", "F\n", " \n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "6\n", " \n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", " \n", "A\n", "E\n", "M\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "B\n", "&\n", "F\n", " \n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "6\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "3\n", ",\n", "5\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", " \n", "A\n", "E\n", "M\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "B\n", "&\n", "F\n", " \n", ",\n", " \n", "[\n", "S\n", "A\n", "R\n", "A\n", " \n", "3\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "3\n", ",\n", "5\n", " \n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", " \n", "A\n", "E\n", "M\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "9\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "R\n", "D\n", "R\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "4\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "9\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "R\n", "D\n", "R\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "4\n", " \n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "9\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "R\n", "D\n", "R\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "3\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "9\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "R\n", "D\n", "R\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "3\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", " \n", "A\n", "E\n", "M\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "2\n", "9\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "A\n", "E\n", "M\n", " \n", "R\n", "D\n", "R\n", ",\n", " \n", "[\n", "S\n", "A\n", "R\n", "A\n", " \n", "3\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "2\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "5\n", ",\n", "9\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "2\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "s\n", " \n", "1\n", "1\n", ",\n", "5\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "2\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", "'\n", "'\n", "y\n", "o\n", " \n", "t\n", "e\n", "n\n", "i\n", "a\n", " \n", "e\n", "s\n", "t\n", "a\n", "s\n", " \n", "a\n", "r\n", "r\n", "u\n", "g\n", "a\n", "s\n", "'\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "2\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "O\n", "P\n", " \n", "5\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "2\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "M\n", "A\n", "Q\n", "U\n", "I\n", "N\n", "A\n", ",\n", " \n", "[\n", "O\n", "P\n", " \n", "5\n", ",\n", " \n", "S\n", "A\n", "R\n", "A\n", " \n", "2\n", "5\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", "]\n", ",\n", " \n", "A\n", "I\n", " \n", "F\n", "o\n", "o\n", "t\n", "a\n", "g\n", "e\n", ",\n", " \n", "3\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", " \n", "A\n", "I\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", "]\n", ",\n", " \n", "A\n", "I\n", " \n", "F\n", "o\n", "o\n", "t\n", "a\n", "g\n", "e\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "3\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", ",\n", " \n", "c\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "A\n", "I\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", ".\n", "m\n", "p\n", "4\n", " \n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "A\n", "I\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", ",\n", " \n", "A\n", "I\n", " \n", "f\n", "o\n", "o\n", "t\n", "a\n", "g\n", "e\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "3\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", ",\n", " \n", "A\n", "I\n", " \n", "f\n", "o\n", "o\n", "t\n", "a\n", "g\n", "e\n", ",\n", " \n", "3\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "A\n", "I\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "8\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "3\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "A\n", "I\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "2\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", "]\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "2\n", "]\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "A\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "4\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", "]\n", ",\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "4\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", "]\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ",\n", " \n", "v\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "1\n", "8\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "4\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "2\n", "5\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "4\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "2\n", "1\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "4\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "4\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "7\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ",\n", " \n", "4\n", " \n", "p\n", "o\n", "i\n", "n\n", "t\n", "s\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "T\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "T\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "s\n", "a\n", "r\n", "a\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "T\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "c\n", "a\n", "r\n", "l\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "T\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "c\n", "a\n", "r\n", "l\n", "a\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "O\n", "P\n", " \n", "O\n", "p\n", "a\n", "t\n", "r\n", "a\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", "3\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "O\n", "P\n", "A\n", "T\n", "R\n", "A\n", " \n", "'\n", "C\n", "O\n", "N\n", "S\n", "U\n", "L\n", "T\n", "A\n", "'\n", ",\n", " \n", "[\n", "T\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", "'\n", "'\n", "y\n", "o\n", " \n", "t\n", "e\n", "n\n", "i\n", "a\n", " \n", "e\n", "s\n", "t\n", "a\n", "s\n", " \n", "a\n", "r\n", "r\n", "u\n", "g\n", "a\n", "s\n", "'\n", "'\n", ".\n", "m\n", "p\n", "4\n", "E\n", "N\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "N\n", "i\n", "k\n", "a\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "I\n", "n\n", "e\n", "s\n", " \n", "1\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "c\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "N\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "N\n", "i\n", "k\n", "a\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "I\n", "n\n", "e\n", "s\n", " \n", "3\n", " \n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "c\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "N\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "N\n", "i\n", "k\n", "a\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "2\n", ",\n", "3\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "c\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "N\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "N\n", "i\n", "k\n", "a\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", ",\n", "2\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "c\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "N\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "N\n", "i\n", "k\n", "a\n", ",\n", " \n", "C\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", ",\n", " \n", "C\n", "3\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "t\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "c\n", "o\n", "r\n", "p\n", "o\n", "r\n", "a\n", "l\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "7\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "3\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "2\n", "2\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "4\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "2\n", "2\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "5\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "5\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "5\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "1\n", "0\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "5\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "3\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "5\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "t\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "1\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", "'\n", "'\n", "y\n", "o\n", " \n", "n\n", "o\n", " \n", "l\n", "o\n", " \n", "c\n", "r\n", "e\n", "i\n", "a\n", "'\n", "'\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "1\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", " \n", "f\n", "a\n", "c\n", "e\n", "t\n", "i\n", "m\n", "e\n", " \n", "m\n", "o\n", "d\n", "e\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "1\n", " \n", "]\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", "1\n", ",\n", " \n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", " \n", "f\n", "a\n", "c\n", "e\n", "t\n", "i\n", "m\n", "e\n", " \n", "m\n", "o\n", "d\n", "e\n", "]\n", ",\n", " \n", "v\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "6\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", ",\n", " \n", "[\n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", ".\n", "m\n", "p\n", "4\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "1\n", "v\n", ",\n", " \n", "*\n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", "*\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "6\n", ",\n", " \n", "B\n", "&\n", "f\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "2\n", "v\n", ",\n", " \n", "*\n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", "*\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "1\n", "0\n", ",\n", " \n", "B\n", "&\n", "f\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", "]\n", ",\n", " \n", "V\n", "o\n", "i\n", "c\n", "e\n", "o\n", "v\n", "e\n", "r\n", " \n", "a\n", "l\n", "b\n", "a\n", " \n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "3\n", "v\n", ",\n", " \n", "*\n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", "*\n", ",\n", " \n", "[\n", "B\n", "&\n", "F\n", " \n", "1\n", ",\n", "3\n", " \n", "f\n", "a\n", "s\n", "t\n", " \n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "4\n", "v\n", ",\n", " \n", "*\n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", "*\n", ",\n", " \n", "[\n", "M\n", "a\n", "d\n", "r\n", "e\n", " \n", "1\n", "0\n", " \n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", "E\n", "G\n", " \n", "E\n", "s\n", "t\n", "é\n", "t\n", "i\n", "c\n", "a\n", " \n", "G\n", "a\n", "l\n", "e\n", "n\n", "a\n", "s\n", ",\n", " \n", "F\n", "a\n", "c\n", "i\n", "a\n", "l\n", ",\n", " \n", "C\n", "7\n", ",\n", " \n", "-\n", " \n", "B\n", "R\n", "O\n", "A\n", "D\n", " \n", "-\n", ",\n", " \n", "a\n", "d\n", "5\n", "v\n", ",\n", " \n", "*\n", "G\n", "E\n", "N\n", "E\n", "R\n", "A\n", "L\n", "*\n", ",\n", " \n", "[\n", "t\n", "e\n", "s\n", "t\n", "i\n", "m\n", "o\n", "n\n", "i\n", "a\n", "l\n", " \n", "s\n", "a\n", "r\n", "a\n", "]\n", ",\n", " \n", "V\n", "a\n", "l\n", "u\n", "e\n", "s\n", "t\n", "a\n", "c\n", "k\n", ".\n", "m\n", "p\n", "4\n"]}, {"ename": "TypeError", "evalue": "'float' object is not iterable", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m ad \u001b[38;5;129;01min\u001b[39;00m ad_link:\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43melem\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mad\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m      3\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mprint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43melem\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mTypeError\u001b[39m: 'float' object is not iterable"]}], "source": ["for ad in ad_link:\n", "    for elem in ad:\n", "        print(elem)"]}, {"cell_type": "code", "execution_count": 7, "id": "de9a8588", "metadata": {}, "outputs": [{"data": {"text/plain": ["0       OP, <PERSON><PERSON><PERSON>, C39, -  -, ad1v, OPATRA MAQUINA, [...\n", "1       OP, Facial, C39, -  -, ad2v, OPATRA MAQUINA, [...\n", "2       OP, <PERSON><PERSON><PERSON>, C39, -  -, ad3v, OPATRA MAQUINA, [...\n", "3       OP, <PERSON><PERSON><PERSON>, C39, -  -, ad4v, OPATRA MAQUINA, [...\n", "4       OP, <PERSON><PERSON><PERSON>, C39, -  -, ad5v, OPATRA MAQUINA, [...\n", "                              ...                        \n", "2704                                                  NaN\n", "2705                                                  NaN\n", "2706                                                  NaN\n", "2707                                                  NaN\n", "2708                                                  NaN\n", "Name: AD LINK, Length: 2709, dtype: object"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# In the colum AD LINK, get rid of the fisrt spaces in the string\n", "ad_link = ad_link.str.strip()\n", "ad_link"]}, {"cell_type": "code", "execution_count": 9, "id": "e1528b2b", "metadata": {}, "outputs": [], "source": ["# replace the column AD LINK with the new ad_link\n", "df['AD LINK'] = ad_link"]}, {"cell_type": "code", "execution_count": 10, "id": "d7e293b4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NOTES</th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "      <th>AD NAME</th>\n", "      <th>FULL NAME</th>\n", "      <th>AD LINK</th>\n", "      <th>gemini_tags_test</th>\n", "      <th>RESULTADOS</th>\n", "      <th>COSTE POR RESULTADO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON>res 4, OP4</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "      <td>ad1v, *OPATRA MAQUINA*, [Madres 4, OP4], B&amp;F 3...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad1v, *OPAT...</td>\n", "      <td>OP, <PERSON>acial, C39, -  -, ad1v, OPATRA MAQUINA, [...</td>\n", "      <td>Error: Request failed for https://generativela...</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON><PERSON> 4, OP1</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "      <td>ad2v, *OPATRA MAQUINA*, [Madres 4, OP1], B&amp;F 3...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad2v, *OPAT...</td>\n", "      <td>OP, <PERSON>acial, C39, -  -, ad2v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4, B&amp;F3</td>\n", "      <td>OP6, valuestack</td>\n", "      <td>ad3v, *OPATRA MAQUINA*, [Madres 4, B&amp;F3], OP6,...</td>\n", "      <td>O<PERSON>, Facial, C39, - BROAD -, ad3v, *OPAT...</td>\n", "      <td>OP, <PERSON>ac<PERSON>, C39, -  -, ad3v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4</td>\n", "      <td>B&amp;F 3, OP1, OP6, valuestack</td>\n", "      <td>ad4v, *OPATRA MAQUINA*, [Madres 4], B&amp;F 3, OP1...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad4v, *OPAT...</td>\n", "      <td>OP, <PERSON>ac<PERSON>, C39, -  -, ad4v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>OP, Facial, C39, -  -, ad1v, OPATRA MAQUINA, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>OP3</td>\n", "      <td>B$F 3, <PERSON><PERSON><PERSON>, <PERSON>stack , OP6</td>\n", "      <td>ad5v, *OPATRA MAQUINA*, [OP3], B$F 3, Madres4,...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad5v, *OPAT...</td>\n", "      <td>OP, <PERSON>acial, C39, -  -, ad5v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NOTES DATE CREATED USED?     CENTRO FACIAL/CORPORAL CAMPAIGN # AUDIENCE  \\\n", "0   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "1   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "2   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "3   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "4   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "\n", "   Ad#           ANGLE            HOOK                          FEATURES  \\\n", "0  1.0  OPATRA MAQUINA   Madres 4, OP4            B&F 3, OP6, valuestack   \n", "1  2.0  OPATRA MAQUINA   Madres 4, OP1            B&F 3, OP6, valuestack   \n", "2  3.0  OPATRA MAQUINA  Madres 4, B&F3                   OP6, valuestack   \n", "3  4.0  OPATRA MAQUINA        Madres 4       B&F 3, OP1, OP6, valuestack   \n", "4  5.0  OPATRA MAQUINA             OP3  B$F 3, <PERSON><PERSON>4, <PERSON><PERSON><PERSON> , OP6   \n", "\n", "                                             AD NAME  \\\n", "0  ad1v, *OPATRA MAQUINA*, [Madres 4, OP4], B&F 3...   \n", "1  ad2v, *OPATRA MAQUINA*, [Madres 4, OP1], B&F 3...   \n", "2  ad3v, *OPATRA MAQUINA*, [Madres 4, B&F3], OP6,...   \n", "3  ad4v, *OPATRA MAQUINA*, [Madres 4], B&F 3, OP1...   \n", "4  ad5v, *OPATRA MAQUINA*, [OP3], B$F 3, Madres4,...   \n", "\n", "                                           FULL NAME  \\\n", "0  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad1v, *OPAT...   \n", "1  OP <PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad2v, *OPAT...   \n", "2  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad3v, *OPAT...   \n", "3  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad4v, *OPAT...   \n", "4  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad5v, *OPAT...   \n", "\n", "                                             AD LINK  \\\n", "0  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad1v, OPATRA MAQUINA, [...   \n", "1  OP, Facial, C39, -  -, ad2v, OPATRA MAQUINA, [...   \n", "2  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad3v, OPATRA MAQUINA, [...   \n", "3  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad4v, OPATRA MAQUINA, [...   \n", "4  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad5v, OPATRA MAQUINA, [...   \n", "\n", "                                    gemini_tags_test RESULTADOS  \\\n", "0  Error: Request failed for https://generativela...  Not Found   \n", "1                                                NaN  Not Found   \n", "2                                                NaN  Not Found   \n", "3                                                NaN  Not Found   \n", "4                                                NaN  Not Found   \n", "\n", "                                 COSTE POR RESULTADO  \n", "0                                                NaN  \n", "1                                                NaN  \n", "2                                                NaN  \n", "3   OP, <PERSON><PERSON><PERSON>, C39, -  -, ad1v, OPATRA MAQUINA, ...  \n", "4                                                NaN  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "7c649483", "metadata": {}, "outputs": [], "source": ["# save csv file\n", "df.to_csv('data/ads_cleaned.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "af7e6d78", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}