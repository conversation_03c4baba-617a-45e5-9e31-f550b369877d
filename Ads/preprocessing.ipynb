{"cells": [{"cell_type": "code", "execution_count": 261, "id": "39af324c", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 262, "id": "d70788bb", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"data/ads.csv\")"]}, {"cell_type": "code", "execution_count": 263, "id": "4dcdbee0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NOTES</th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "      <th>AD NAME</th>\n", "      <th>FULL NAME</th>\n", "      <th>AD LINK</th>\n", "      <th>Unnamed: 14</th>\n", "      <th>Unnamed: 15</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON>res 4, OP4</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "      <td>ad1v, *OPATRA MAQUINA*, [Madres 4, OP4], B&amp;F 3...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad1v, *OPAT...</td>\n", "      <td>OP, <PERSON>acial, C39, -  -, ad1v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON><PERSON> 4, OP1</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "      <td>ad2v, *OPATRA MAQUINA*, [Madres 4, OP1], B&amp;F 3...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad2v, *OPAT...</td>\n", "      <td>OP, <PERSON>acial, C39, -  -, ad2v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4, B&amp;F3</td>\n", "      <td>OP6, valuestack</td>\n", "      <td>ad3v, *OPATRA MAQUINA*, [Madres 4, B&amp;F3], OP6,...</td>\n", "      <td>O<PERSON>, Facial, C39, - BROAD -, ad3v, *OPAT...</td>\n", "      <td>OP, <PERSON>ac<PERSON>, C39, -  -, ad3v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4</td>\n", "      <td>B&amp;F 3, OP1, OP6, valuestack</td>\n", "      <td>ad4v, *OPATRA MAQUINA*, [Madres 4], B&amp;F 3, OP1...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad4v, *OPAT...</td>\n", "      <td>OP, <PERSON>ac<PERSON>, C39, -  -, ad4v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>OP3</td>\n", "      <td>B$F 3, <PERSON><PERSON><PERSON>, <PERSON>stack , OP6</td>\n", "      <td>ad5v, *OPATRA MAQUINA*, [OP3], B$F 3, Madres4,...</td>\n", "      <td>OP <PERSON>, Facial, C39, - BROAD -, ad5v, *OPAT...</td>\n", "      <td>OP, <PERSON>acial, C39, -  -, ad5v, OPATRA MAQUINA, [...</td>\n", "      <td>NaN</td>\n", "      <td>Not Found</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NOTES DATE CREATED USED?     CENTRO FACIAL/CORPORAL CAMPAIGN # AUDIENCE  \\\n", "0   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "1   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "2   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "3   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "4   NaN   18/12/2024   Yes  OP Opatra          Facial         39    BROAD   \n", "\n", "   Ad#           ANGLE            HOOK                          FEATURES  \\\n", "0  1.0  OPATRA MAQUINA   Madres 4, OP4            B&F 3, OP6, valuestack   \n", "1  2.0  OPATRA MAQUINA   Madres 4, OP1            B&F 3, OP6, valuestack   \n", "2  3.0  OPATRA MAQUINA  Madres 4, B&F3                   OP6, valuestack   \n", "3  4.0  OPATRA MAQUINA        Madres 4       B&F 3, OP1, OP6, valuestack   \n", "4  5.0  OPATRA MAQUINA             OP3  B$F 3, <PERSON><PERSON>4, <PERSON><PERSON><PERSON> , OP6   \n", "\n", "                                             AD NAME  \\\n", "0  ad1v, *OPATRA MAQUINA*, [Madres 4, OP4], B&F 3...   \n", "1  ad2v, *OPATRA MAQUINA*, [Madres 4, OP1], B&F 3...   \n", "2  ad3v, *OPATRA MAQUINA*, [Madres 4, B&F3], OP6,...   \n", "3  ad4v, *OPATRA MAQUINA*, [Madres 4], B&F 3, OP1...   \n", "4  ad5v, *OPATRA MAQUINA*, [OP3], B$F 3, Madres4,...   \n", "\n", "                                           FULL NAME  \\\n", "0  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad1v, *OPAT...   \n", "1  OP <PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad2v, *OPAT...   \n", "2  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad3v, *OPAT...   \n", "3  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad4v, *OPAT...   \n", "4  O<PERSON>, <PERSON><PERSON><PERSON>, C39, - BROAD -, ad5v, *OPAT...   \n", "\n", "                                             AD LINK Unnamed: 14 Unnamed: 15  \n", "0  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad1v, OPATRA MAQUINA, [...         NaN   Not Found  \n", "1  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad2v, OPATRA MAQUINA, [...         NaN   Not Found  \n", "2  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad3v, OPATRA MAQUINA, [...         NaN   Not Found  \n", "3  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad4v, OPATRA MAQUINA, [...         NaN   Not Found  \n", "4  OP, <PERSON><PERSON><PERSON>, C39, -  -, ad5v, OPATRA MAQUINA, [...         NaN   Not Found  "]}, "execution_count": 263, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 264, "id": "25a9ecbd", "metadata": {}, "outputs": [], "source": ["df.drop(columns=[\"NOTES\", \"AD NAME\", \"FULL NAME\", \"AD LINK\", \"Unnamed: 14\", \"Unnamed: 15\"], inplace=True)"]}, {"cell_type": "code", "execution_count": 265, "id": "20007488", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON>res 4, OP4</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON><PERSON> 4, OP1</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4, B&amp;F3</td>\n", "      <td>OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4</td>\n", "      <td>B&amp;F 3, OP1, OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>OP3</td>\n", "      <td>B$F 3, <PERSON><PERSON><PERSON>, <PERSON>stack , OP6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  DATE CREATED USED?     CENTRO FACIAL/CORPORAL CAMPAIGN # AUDIENCE  Ad#  \\\n", "0   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  1.0   \n", "1   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  2.0   \n", "2   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  3.0   \n", "3   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  4.0   \n", "4   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  5.0   \n", "\n", "            ANGLE            HOOK                          FEATURES  \n", "0  OPATRA MAQUINA   Madres 4, OP4            B&F 3, OP6, valuestack  \n", "1  OPATRA MAQUINA   Madres 4, OP1            B&F 3, OP6, valuestack  \n", "2  OPATRA MAQUINA  Madres 4, B&F3                   OP6, valuestack  \n", "3  OPATRA MAQUINA        Madres 4       B&F 3, OP1, OP6, valuestack  \n", "4  OPATRA MAQUINA             OP3  B$F 3, <PERSON><PERSON>4, <PERSON><PERSON><PERSON> , OP6  "]}, "execution_count": 265, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "id": "3bd664d3", "metadata": {}, "source": ["DATA: <br> \n", "- Timestamp <br> \n", "- USED? Todos used? Serían de los que tenemos resultados\n", "- Centro\n", "- T<PERSON><PERSON><PERSON>\n", "- Campaign\n", "- Audience --->"]}, {"cell_type": "code", "execution_count": 266, "id": "480a0e7a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'B&F 3, OP6, valuestack'"]}, "execution_count": 266, "metadata": {}, "output_type": "execute_result"}], "source": ["df.il<PERSON>[1, -1]"]}, {"cell_type": "code", "execution_count": 267, "id": "2af39dd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["DATE CREATED       170\n", "USED?               17\n", "CENTRO              16\n", "FACIAL/CORPORAL     16\n", "CAMPAIGN #          50\n", "AUDIENCE            39\n", "Ad#                 19\n", "ANGLE               52\n", "HOOK                63\n", "FEATURES            62\n", "dtype: int64"]}, "execution_count": 267, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check for missing values\n", "df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 268, "id": "6fa3626f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1977, 10)"]}, "execution_count": 268, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 269, "id": "70171700", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON>res 4, OP4</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td><PERSON><PERSON> 4, OP1</td>\n", "      <td>B&amp;F 3, OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4, B&amp;F3</td>\n", "      <td>OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>Madres 4</td>\n", "      <td>B&amp;F 3, OP1, OP6, valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>18/12/2024</td>\n", "      <td>Yes</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>39</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>OPATRA MAQUINA</td>\n", "      <td>OP3</td>\n", "      <td>B$F 3, <PERSON><PERSON><PERSON>, <PERSON>stack , OP6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  DATE CREATED USED?     CENTRO FACIAL/CORPORAL CAMPAIGN # AUDIENCE  Ad#  \\\n", "0   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  1.0   \n", "1   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  2.0   \n", "2   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  3.0   \n", "3   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  4.0   \n", "4   18/12/2024   Yes  OP Opatra          Facial         39    BROAD  5.0   \n", "\n", "            ANGLE            HOOK                          FEATURES  \n", "0  OPATRA MAQUINA   Madres 4, OP4            B&F 3, OP6, valuestack  \n", "1  OPATRA MAQUINA   Madres 4, OP1            B&F 3, OP6, valuestack  \n", "2  OPATRA MAQUINA  Madres 4, B&F3                   OP6, valuestack  \n", "3  OPATRA MAQUINA        Madres 4       B&F 3, OP1, OP6, valuestack  \n", "4  OPATRA MAQUINA             OP3  B$F 3, <PERSON><PERSON>4, <PERSON><PERSON><PERSON> , OP6  "]}, "execution_count": 269, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 270, "id": "4738ef4b", "metadata": {}, "outputs": [{"data": {"text/plain": ["DATE CREATED       209\n", "USED?                2\n", "CENTRO              47\n", "FACIAL/CORPORAL      2\n", "CAMPAIGN #          75\n", "AUDIENCE             6\n", "Ad#                  7\n", "ANGLE               38\n", "HOOK               876\n", "FEATURES           511\n", "dtype: int64"]}, "execution_count": 270, "metadata": {}, "output_type": "execute_result"}], "source": ["# show how many different values there are per column\n", "df.nunique()"]}, {"cell_type": "markdown", "id": "b3fbe08b", "metadata": {}, "source": ["## CENTROS"]}, {"cell_type": "code", "execution_count": 271, "id": "ba1215bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['OP Opatra', 'OP Opatra, GNB GONOWBEAUTY', 'OPM Opatra Madrid',\n", "       'AEM Aurora Estética Madrid', 'EN Estética Nika',\n", "       'EG Estética Galenas', 'DX Definixion',\n", "       '<PERSON> <PERSON>, GNB GONOWBEAUTY', 'R<PERSON>',\n", "       '<PERSON> <PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> i Laser',\n", "       'AE aurora estetica BCN', 'ED Edyta Beauty', 'PB Premier Boutique',\n", "       'GNB GONOWBEAUTY', 'PC Premier Cosmetics',\n", "       'PBS - Premier Boutique Sitges', 'AO', 'HG - HG Estética Clínica',\n", "       'GNB GONOWBEAUTY, EVO', 'EVO', 'Beauty Bar', nan,\n", "       'TB - The Beauty Bar', 'HG - Estética clínica',\n", "       'BB - Benestar Balmes', 'DV - DMV CLINICS',\n", "       'VE - Vintage Esthetic', 'EV - <PERSON>', 'VH - Valeria House',\n", "       'ID - Individuel', 'MC - <PERSON>', 'DV - DMV CLINICS ', 'OP',\n", "       'BB Benestar Belleza', 'LL Laer i Laser', 'AE Aurora estetica BCN',\n", "       'MC - <PERSON>', '<PERSON><PERSON> <PERSON> <PERSON>', 'ZE - <PERSON>', 'AR - <PERSON><PERSON>',\n", "       'OZ - Orizzonte Estetica', 'KO - Koah Beauty',\n", "       'CM - Centro Estétic Mandy', 'NR - <PERSON>',\n", "       'FG - <PERSON>', 'AZ - Amazonia Estética'], dtype=object)"]}, "execution_count": 271, "metadata": {}, "output_type": "execute_result"}], "source": ["# Show the different values in column \"CENTRO\"\n", "df[\"CENTRO\"].unique()"]}, {"cell_type": "code", "execution_count": 272, "id": "080d70de", "metadata": {}, "outputs": [], "source": ["# Show the the rows that have nan in the column \"CENTRO\"\n", "idx = df[df[\"CENTRO\"].isnull()].index\n", "df.drop(index=idx, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8e914969", "metadata": {}, "outputs": [], "source": ["replacements = {r'.*(GNB GONOWBEAUTY|EVO).*': 'GNB GONOWBEAUTY',\n", "              r'.*(AE |AE aurora Estetica).*': 'AE Autora Estetica',\n", "              r'.*(Beauty Bar).*': 'TB - The Beauty Bar'}"]}, {"cell_type": "code", "execution_count": 274, "id": "28b3a8ab", "metadata": {}, "outputs": [], "source": ["df['CENTRO'] = df['CENTRO'].replace(replacements, regex=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "999f742f", "metadata": {}, "outputs": [], "source": ["# Replace the \"CENTRO\" columns with \"OP\" value for \"OP Opatra\"\n", "df.replace(\n", "    {'CENTRO': {\n", "        \"OP\": 'OP Opatra'\n", "    }},\n", "    inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": 281, "id": "d8b42e44", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['OP Opatra', 'GNB GONOWBEAUTY', 'OPM Opatra Madrid',\n", "       'AEM Aurora Estética Madrid', 'EN Estética Nika',\n", "       'EG Estética Galenas', 'DX Definixion', 'RG Rosa Garvín',\n", "       '<PERSON> <PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> i Laser',\n", "       'AE Autora Estetica', 'ED Edyta Beauty', 'PB Premier Boutique',\n", "       'PC Premier Cosmetics', 'PBS - Premier Bouti<PERSON> Sitges', 'AO',\n", "       'HG - HG Estética Clínica', 'TB - The Beauty Bar',\n", "       'HG - Estética clínica', 'BB - Benestar Balmes',\n", "       'DV - DMV CLINICS', 'VE - Vintage Esthetic', 'EV - <PERSON>',\n", "       'VH - Valeria House', 'ID - Individuel', 'MC - <PERSON>',\n", "       'DV - DMV CLINICS ', 'BB Benestar Belleza', 'LL Laer i Laser',\n", "       'MC - <PERSON>', '<PERSON><PERSON> <PERSON> <PERSON>', 'ZE - <PERSON>', 'AR - <PERSON><PERSON>',\n", "       'OZ - Orizzonte Estetica', 'KO - Koah Beauty',\n", "       'CM - Centro Estétic Mandy', 'NR - <PERSON>',\n", "       'FG - <PERSON>', 'AZ - Amazonia Estética'], dtype=object)"]}, "execution_count": 281, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"CENTRO\"].unique()"]}, {"cell_type": "markdown", "id": "e049b8b9", "metadata": {}, "source": ["## AUDIENCE"]}, {"cell_type": "code", "execution_count": 282, "id": "69d39a01", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['BROAD', 'Webview+leadyschedule+GNB retargetting', 'look alike',\n", "       'broad', nan, 'PAGE VIEW', 'BORAD'], dtype=object)"]}, "execution_count": 282, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"AUDIENCE\"].unique()"]}, {"cell_type": "code", "execution_count": 283, "id": "63ab7481", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>948</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>949</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>950</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1116</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1117</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1118</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1119</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1120</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1126</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1127</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1128</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1129</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1130</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1131</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1132</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1133</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1134</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1135</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1136</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>EG Estética Galenas</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1137</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>EG Estética Galenas</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1138</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>EG Estética Galenas</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>EG Estética Galenas</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1140</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>EG Estética Galenas</td>\n", "      <td>Facial</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DATE CREATED USED?               CENTRO FACIAL/CORPORAL CAMPAIGN #  \\\n", "948           NaN    No            OP Opatra          Facial        NaN   \n", "949           NaN    No            OP Opatra          Facial        NaN   \n", "950           NaN    No            OP Opatra          Facial        NaN   \n", "1116          NaN    No            OP Opatra          Facial        NaN   \n", "1117          NaN    No            OP Opatra          Facial        NaN   \n", "1118          NaN    No            OP Opatra          Facial        NaN   \n", "1119          NaN    No            OP Opatra          Facial        NaN   \n", "1120          NaN    No            OP Opatra          Facial        NaN   \n", "1126          NaN    No            OP Opatra          Facial        NaN   \n", "1127          NaN    No            OP Opatra          Facial        NaN   \n", "1128          NaN    No            OP Opatra          Facial        NaN   \n", "1129          NaN    No            OP Opatra          Facial        NaN   \n", "1130          NaN    No            OP Opatra          Facial        NaN   \n", "1131          NaN    No            OP Opatra          Facial        NaN   \n", "1132          NaN    No            OP Opatra          Facial        NaN   \n", "1133          NaN    No            OP Opatra          Facial        NaN   \n", "1134          NaN    No            OP Opatra          Facial        NaN   \n", "1135          NaN    No            OP Opatra          Facial        NaN   \n", "1136          NaN    No  EG Estética Galenas          Facial        NaN   \n", "1137          NaN    No  EG Estética Galenas          Facial        NaN   \n", "1138          NaN    No  EG Estética Galenas          Facial        NaN   \n", "1139          NaN    No  EG Estética Galenas          Facial        NaN   \n", "1140          NaN    No  EG Estética Galenas          Facial        NaN   \n", "\n", "     AUDIENCE  Ad# ANGLE HOOK FEATURES  \n", "948       NaN  NaN   NaN  NaN      NaN  \n", "949       NaN  NaN   NaN  NaN      NaN  \n", "950       NaN  NaN   NaN  NaN      NaN  \n", "1116      NaN  1.0   NaN  NaN      NaN  \n", "1117      NaN  2.0   NaN  NaN      NaN  \n", "1118      NaN  3.0   NaN  NaN      NaN  \n", "1119      NaN  4.0   NaN  NaN      NaN  \n", "1120      NaN  5.0   NaN  NaN      NaN  \n", "1126      NaN  1.0   NaN  NaN      NaN  \n", "1127      NaN  2.0   NaN  NaN      NaN  \n", "1128      NaN  3.0   NaN  NaN      NaN  \n", "1129      NaN  4.0   NaN  NaN      NaN  \n", "1130      NaN  5.0   NaN  NaN      NaN  \n", "1131      NaN  1.0   NaN  NaN      NaN  \n", "1132      NaN  2.0   NaN  NaN      NaN  \n", "1133      NaN  3.0   NaN  NaN      NaN  \n", "1134      NaN  4.0   NaN  NaN      NaN  \n", "1135      NaN  5.0   NaN  NaN      NaN  \n", "1136      NaN  1.0   NaN  NaN      NaN  \n", "1137      NaN  2.0   NaN  NaN      NaN  \n", "1138      NaN  3.0   NaN  NaN      NaN  \n", "1139      NaN  4.0   NaN  NaN      NaN  \n", "1140      NaN  5.0   NaN  NaN      NaN  "]}, "execution_count": 283, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"AUDIENCE\"].isnull()]"]}, {"cell_type": "code", "execution_count": 284, "id": "c297fe97", "metadata": {}, "outputs": [], "source": ["idx = df[df[\"AUDIENCE\"].isnull()].index\n", "df.drop(index=idx, inplace=True)"]}, {"cell_type": "code", "execution_count": 285, "id": "1ae2e46d", "metadata": {}, "outputs": [], "source": ["df.replace(\n", "    {'AUDIENCE': {\n", "        \"broad\": 'BROAD',\n", "        \"BORAD\": 'BROAD'\n", "    }},\n", "    inplace=True\n", ")"]}, {"cell_type": "code", "execution_count": 286, "id": "419bd560", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['BROAD', 'Webview+leadyschedule+GNB retargetting', 'look alike',\n", "       'PAGE VIEW'], dtype=object)"]}, "execution_count": 286, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"AUDIENCE\"].unique()"]}, {"cell_type": "markdown", "id": "acf51f38", "metadata": {}, "source": ["## AN<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 287, "id": "1214ed3f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>420</th>\n", "      <td>06/02/2025</td>\n", "      <td>No</td>\n", "      <td>GNB GONOWBEAUTY</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421</th>\n", "      <td>06/02/2025</td>\n", "      <td>No</td>\n", "      <td>GNB GONOWBEAUTY</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>422</th>\n", "      <td>06/02/2025</td>\n", "      <td>No</td>\n", "      <td>GNB GONOWBEAUTY</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>423</th>\n", "      <td>06/02/2025</td>\n", "      <td>No</td>\n", "      <td>GNB GONOWBEAUTY</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>424</th>\n", "      <td>07/02/2025</td>\n", "      <td>No</td>\n", "      <td>TB - The Beauty Bar</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>425</th>\n", "      <td>07/02/2025</td>\n", "      <td>No</td>\n", "      <td>TB - The Beauty Bar</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>426</th>\n", "      <td>07/02/2025</td>\n", "      <td>No</td>\n", "      <td>TB - The Beauty Bar</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>427</th>\n", "      <td>07/02/2025</td>\n", "      <td>No</td>\n", "      <td>TB - The Beauty Bar</td>\n", "      <td>Corporal</td>\n", "      <td>1 test</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1121</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>AE Autora Estetica</td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1122</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>AE Autora Estetica</td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1123</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>AE Autora Estetica</td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1124</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>AE Autora Estetica</td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1125</th>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "      <td>AE Autora Estetica</td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DATE CREATED USED?               CENTRO FACIAL/CORPORAL CAMPAIGN #  \\\n", "420    06/02/2025    No      GNB GONOWBEAUTY        Corporal     1 test   \n", "421    06/02/2025    No      GNB GONOWBEAUTY        Corporal     1 test   \n", "422    06/02/2025    No      GNB GONOWBEAUTY        Corporal     1 test   \n", "423    06/02/2025    No      GNB GONOWBEAUTY        Corporal     1 test   \n", "424    07/02/2025    No  TB - The Beauty Bar        Corporal     1 test   \n", "425    07/02/2025    No  TB - The Beauty Bar        Corporal     1 test   \n", "426    07/02/2025    No  TB - The Beauty Bar        Corporal     1 test   \n", "427    07/02/2025    No  TB - The Beauty Bar        Corporal     1 test   \n", "1121          NaN    No   AE Autora Estetica        Corporal         10   \n", "1122          NaN    No   AE Autora Estetica        Corporal         10   \n", "1123          NaN    No   AE Autora Estetica        Corporal         10   \n", "1124          NaN    No   AE Autora Estetica        Corporal         10   \n", "1125          NaN    No   AE Autora Estetica        Corporal         10   \n", "\n", "     AUDIENCE  Ad# ANGLE HOOK FEATURES  \n", "420     BROAD  1.0   NaN  NaN      NaN  \n", "421     BROAD  2.0   NaN  NaN      NaN  \n", "422     BROAD  3.0   NaN  NaN      NaN  \n", "423     BROAD  4.0   NaN  NaN      NaN  \n", "424     BROAD  1.0   NaN  NaN      NaN  \n", "425     BROAD  2.0   NaN  NaN      NaN  \n", "426     BROAD  3.0   NaN  NaN      NaN  \n", "427     BROAD  4.0   NaN  NaN      NaN  \n", "1121    BROAD  1.0   NaN  NaN      NaN  \n", "1122    BROAD  2.0   NaN  NaN      NaN  \n", "1123    BROAD  3.0   NaN  NaN      NaN  \n", "1124    BROAD  4.0   NaN  NaN      NaN  \n", "1125    BROAD  5.0   NaN  NaN      NaN  "]}, "execution_count": 287, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"ANGLE\"].isnull()]"]}, {"cell_type": "code", "execution_count": 288, "id": "ae3edf8c", "metadata": {}, "outputs": [], "source": ["idx = df[df[\"ANGLE\"].isnull()].index\n", "df.drop(index=idx, inplace=True)"]}, {"cell_type": "code", "execution_count": 289, "id": "8dbbf675", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['OPATRA MAQUINA', 'OPATRA \"\\'CONSULTA\"\\'', 'AEM B&F ', 'AEM RDR',\n", "       'GENERAL', 'Social proof', 'Cinturón', 'Testimonial', 'Headlines',\n", "       'Headlines Papada', 'Headlines Patas de gallo',\n", "       'Headlines Lineas de expresion', 'Headlines Arrugas',\n", "       'Headlines Lineas de marioneta', 'OPATRA \"\\'MAQUINA\"\\'',\n", "       'HEADLINE', \"'sin manchas'' HEADLINE\", \"'sin manchas''\", 'general',\n", "       \"HEADLINE ''El tratamiento que está remplazando al bótox''\",\n", "       'AEM ', \"1.1 size ''grasa en el abdomen?\",\n", "       \"1.1 size ''grasa corporal?\",\n", "       \"1.1 size ''La forma mas sencilla de transformar tu cuerpo''\",\n", "       'headline', '1.1 size', \"no sale ''eliminar''\", 'headliine',\n", "       'Foto Estetica', '<PERSON><PERSON><PERSON> gratis', 'FUNCIONARA?',\n", "       'OPATRA CONSULTA', 'OPATRA \"\\'CONSULTA', 'AEM RDR ', 'AEM B&F',\n", "       'Maquina DX', 'Headline', 'Headline, Maquina DX'], dtype=object)"]}, "execution_count": 289, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"ANGLE\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "9f1bed6d", "metadata": {}, "outputs": [], "source": ["df.replace(\n", "    {'ANGLE': {\n", "        '1.1 size ': '1.1 size',\n", "        'AEM B&F ':  'AEM B&F',\n", "        'AEM RDR ':  'AEM RDR',\n", "        'AEM ': 'AEM',\n", "        'headline': 'Headline',\n", "        'headline,': 'Headline',\n", "        'headliine': 'Headline',\n", "        'Headlines': 'Headline',\n", "        'HEADLINE': 'Headline',\n", "        'OPATRA \"\\'CONSULTA\"\\'': 'OPATRA CONSULTA',\n", "        'OPATRA \"\\'CONSULTA': 'OPATRA CONSULTA',\n", "        'OPATRA \"\\'MAQUINA\"\\'': 'OPATRA MAQUINA',\n", "        'Headline, Maquina DX': 'Maquina DX',\n", "        \"'sin manchas'' HEADLINE\": 'sin manchas',\n", "        \"'sin manchas''\": 'sin manchas',\n", "\n", "    }},\n", "    inplace=True\n", ")\n"]}, {"cell_type": "code", "execution_count": 291, "id": "bdfe4434", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['OPATRA MAQUINA', 'OPATRA CONSULTA', 'AEM B&F', 'AEM RDR',\n", "       'GENERAL', 'Social proof', 'Cinturón', 'Testimonial', 'Headline',\n", "       'Headlines Papada', 'Headlines Patas de gallo',\n", "       'Headlines Lineas de expresion', 'Headlines Arrugas',\n", "       'Headlines Lineas de marioneta', 'sin manchas', 'general',\n", "       \"HEADLINE ''El tratamiento que está remplazando al bótox''\", 'AEM',\n", "       \"1.1 size ''grasa en el abdomen?\", \"1.1 size ''grasa corporal?\",\n", "       \"1.1 size ''La forma mas sencilla de transformar tu cuerpo''\",\n", "       '1.1 size', \"no sale ''eliminar''\", 'Foto Estetica',\n", "       'Ana<PERSON>is gratis', 'FUNCIONARA?', 'Maquina DX'], dtype=object)"]}, "execution_count": 291, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"ANGLE\"].unique()"]}, {"cell_type": "markdown", "id": "24d82505", "metadata": {}, "source": ["## AD"]}, {"cell_type": "code", "execution_count": 292, "id": "80831039", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [DATE CREATED, USED?, CENTRO, FACIAL/CORPORAL, CAMPAIGN #, AUDIENCE, Ad#, ANGLE, HOOK, FEATURES]\n", "Index: []"]}, "execution_count": 292, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"Ad#\"].isnull()]"]}, {"cell_type": "code", "execution_count": 293, "id": "d8ab2d9b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1., 2., 3., 4., 5., 6., 7.])"]}, "execution_count": 293, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Ad#'].unique()"]}, {"cell_type": "code", "execution_count": 294, "id": "10f21eac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1468</th>\n", "      <td>09/04/2025</td>\n", "      <td>No</td>\n", "      <td>AR - <PERSON>ony BMC</td>\n", "      <td>Facial</td>\n", "      <td>2</td>\n", "      <td>BROAD</td>\n", "      <td>7.0</td>\n", "      <td>general</td>\n", "      <td>testimonio madres</td>\n", "      <td>hook reduce el 15% + b&amp;f voiceover</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DATE CREATED USED?           CENTRO FACIAL/CORPORAL CAMPAIGN # AUDIENCE  \\\n", "1468   09/04/2025    No  AR - Armony BMC          Facial          2    BROAD   \n", "\n", "      Ad#    ANGLE               HOOK                            FEATURES  \n", "1468  7.0  general  testimonio madres  hook reduce el 15% + b&f voiceover  "]}, "execution_count": 294, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df['Ad#']==7.0]"]}, {"cell_type": "code", "execution_count": 295, "id": "2cbcff43", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>307</th>\n", "      <td>22/01/25</td>\n", "      <td>No</td>\n", "      <td>GNB GONOWBEAUTY</td>\n", "      <td>Facial</td>\n", "      <td>8</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>Headline</td>\n", "      <td>tik<PERSON> scroll</td>\n", "      <td>carolina 1, <PERSON>, valuestack (regalo)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>733</th>\n", "      <td>06/03/2025</td>\n", "      <td>No</td>\n", "      <td>GNB GONOWBEAUTY</td>\n", "      <td>Corporal</td>\n", "      <td>1</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f</td>\n", "      <td>articulo, b&amp;f, valuestack (musica justin timbe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>836</th>\n", "      <td>13/03/2025</td>\n", "      <td>No</td>\n", "      <td>ED Edyta Beauty</td>\n", "      <td>Corporal</td>\n", "      <td>15</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f</td>\n", "      <td>voiceover acompañame</td>\n", "    </tr>\n", "    <tr>\n", "      <th>842</th>\n", "      <td>13/03/2025</td>\n", "      <td>No</td>\n", "      <td>TB - The Beauty Bar</td>\n", "      <td>Corporal</td>\n", "      <td>6</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f</td>\n", "      <td>voiceover corto</td>\n", "    </tr>\n", "    <tr>\n", "      <th>848</th>\n", "      <td>13/03/2025</td>\n", "      <td>No</td>\n", "      <td>DX Definixion</td>\n", "      <td>Facial</td>\n", "      <td>47</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>testimonial</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>854</th>\n", "      <td>13/03/2025</td>\n", "      <td>No</td>\n", "      <td><PERSON>V - <PERSON></td>\n", "      <td>Corporal</td>\n", "      <td>4</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>sticker reclama 2x1</td>\n", "      <td>articulo, valuestack, canva creative chica en ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>860</th>\n", "      <td>13/03/2025</td>\n", "      <td>No</td>\n", "      <td>AO</td>\n", "      <td>Corporal</td>\n", "      <td>9</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>hook sara 1</td>\n", "      <td>valuestack, canva creative chica en la playa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>866</th>\n", "      <td>14/03/2025</td>\n", "      <td>No</td>\n", "      <td>DV - DMV CLINICS</td>\n", "      <td>Facial</td>\n", "      <td>5</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>faster hook results voiceover</td>\n", "      <td>voiceover articulo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>872</th>\n", "      <td>14/03/2025</td>\n", "      <td>No</td>\n", "      <td><PERSON>V - <PERSON></td>\n", "      <td>Corporal</td>\n", "      <td>5</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f</td>\n", "      <td>voiceover articulo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>878</th>\n", "      <td>14/03/2025</td>\n", "      <td>No</td>\n", "      <td>ID - Individuel</td>\n", "      <td>Corporal</td>\n", "      <td>1</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>testimonio madres</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>894</th>\n", "      <td>17/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>41</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f elimina la grasa</td>\n", "      <td>descubre la oferta</td>\n", "    </tr>\n", "    <tr>\n", "      <th>900</th>\n", "      <td>17/03/2025</td>\n", "      <td>No</td>\n", "      <td>ID - Individuel</td>\n", "      <td>Corporal</td>\n", "      <td>2</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>testimonio madres</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>906</th>\n", "      <td>17/03/2025</td>\n", "      <td>No</td>\n", "      <td>VE - Vintage Esthetic</td>\n", "      <td>Corporal</td>\n", "      <td>5</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f ¿grasa abdominal?</td>\n", "      <td>voiceover articulo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>912</th>\n", "      <td>17/03/2025</td>\n", "      <td>No</td>\n", "      <td>DV - DMV CLINICS</td>\n", "      <td>Facial</td>\n", "      <td>6</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>articulo</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>922</th>\n", "      <td>18/03/2027</td>\n", "      <td>No</td>\n", "      <td>DV - DMV CLINICS</td>\n", "      <td>Facial</td>\n", "      <td>7</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>faster hook results</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>928</th>\n", "      <td>18/03/2027</td>\n", "      <td>No</td>\n", "      <td>ID - Individuel</td>\n", "      <td>Corporal</td>\n", "      <td>3</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>internet footage b&amp;f + voiceover (el tratamien...</td>\n", "      <td>voiceover articulo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>934</th>\n", "      <td>18/03/2027</td>\n", "      <td>No</td>\n", "      <td>RG Rosa <PERSON></td>\n", "      <td>Corporal</td>\n", "      <td>48</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>voiceover articulo</td>\n", "      <td>testimonio madres</td>\n", "    </tr>\n", "    <tr>\n", "      <th>940</th>\n", "      <td>18/03/2025</td>\n", "      <td>No</td>\n", "      <td>VE - Vintage Esthetic</td>\n", "      <td>Corporal</td>\n", "      <td>6</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>articulo</td>\n", "      <td>valuestack, cta</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1260</th>\n", "      <td>26/03/2025</td>\n", "      <td>No</td>\n", "      <td>VH - Valeria House</td>\n", "      <td>Corporal</td>\n", "      <td>4</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>clips rapidos</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1266</th>\n", "      <td>26/03/2025</td>\n", "      <td>No</td>\n", "      <td>DV - DMV CLINICS</td>\n", "      <td>Facial</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f</td>\n", "      <td>voiceover articulo corto</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1280</th>\n", "      <td>27/03/2029</td>\n", "      <td>No</td>\n", "      <td>ZE - Zero</td>\n", "      <td>Corporal</td>\n", "      <td>3</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f</td>\n", "      <td>voiceover articulo corto</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1286</th>\n", "      <td>27/03/2029</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>45</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>clip telecinco + b&amp;f</td>\n", "      <td>testimonio madres</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1353</th>\n", "      <td>01/04/2025</td>\n", "      <td>No</td>\n", "      <td>ZE - Zero</td>\n", "      <td>Corporal</td>\n", "      <td>5</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>voiceover whatsap me acabo de eliminar la gras...</td>\n", "      <td>testimonios</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1467</th>\n", "      <td>09/04/2025</td>\n", "      <td>No</td>\n", "      <td>AR - <PERSON>ony BMC</td>\n", "      <td>Facial</td>\n", "      <td>2</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>reclama tu 2x1</td>\n", "      <td>hook reduce el 15% + b&amp;f voiceover + testimoni...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1566</th>\n", "      <td>15/04/2032</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>48</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f voiceover de esto</td>\n", "      <td>testimonio madres</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1572</th>\n", "      <td>15/04/2032</td>\n", "      <td>No</td>\n", "      <td>PB Premier Boutique</td>\n", "      <td>Facial</td>\n", "      <td>30</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f internet footage + b&amp;f ia fooatage + b&amp;f i...</td>\n", "      <td>voiceover pasar de esto a esto + valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1590</th>\n", "      <td>16/04/2025</td>\n", "      <td>No</td>\n", "      <td>DX Definixion</td>\n", "      <td>Facial</td>\n", "      <td>55</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>testimonio sara</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1596</th>\n", "      <td>16/04/2025</td>\n", "      <td>No</td>\n", "      <td>MC - <PERSON></td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>b&amp;f + reducción de arrugas sin bótox</td>\n", "      <td>voiceover articulo corto</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1687</th>\n", "      <td>24/04/2025</td>\n", "      <td>No</td>\n", "      <td>PB Premier Boutique</td>\n", "      <td>Facial</td>\n", "      <td>31</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>testimonio madres cortos</td>\n", "      <td>pantalla verde b&amp;f</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1811</th>\n", "      <td>05/05/2025</td>\n", "      <td>No</td>\n", "      <td>TB - The Beauty Bar</td>\n", "      <td>Corporal</td>\n", "      <td>17</td>\n", "      <td>BROAD</td>\n", "      <td>6.0</td>\n", "      <td>general</td>\n", "      <td>testimonio corto + b&amp;f</td>\n", "      <td>valuestack</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DATE CREATED USED?                      CENTRO FACIAL/CORPORAL  \\\n", "307      22/01/25    No             GNB GONOWBEAUTY          Facial   \n", "733    06/03/2025    No             GNB GONOWBEAUTY        Corporal   \n", "836    13/03/2025    No             ED Edyta Beauty        Corporal   \n", "842    13/03/2025    No         TB - The Beauty Bar        Corporal   \n", "848    13/03/2025    No               DX Definixion          Facial   \n", "854    13/03/2025    No             EV - <PERSON>   \n", "860    13/03/2025    No                          AO        Corporal   \n", "866    14/03/2025    No            DV - DMV CLINICS          Facial   \n", "872    14/03/2025    No             EV - <PERSON>   \n", "878    14/03/2025    No             ID - Individuel        Corporal   \n", "894    17/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "900    17/03/2025    No             ID - Individuel        Corporal   \n", "906    17/03/2025    No       VE - Vintage Esthetic        Corporal   \n", "912    17/03/2025    No           DV - DMV CLINICS           Facial   \n", "922    18/03/2027    No            DV - DMV CLINICS          Facial   \n", "928    18/03/2027    No             ID - Individuel        Corporal   \n", "934    18/03/2027    No              RG <PERSON>   \n", "940    18/03/2025    No       VE - Vintage Esthetic        Corporal   \n", "1260   26/03/2025    No          VH - Valeria House        Corporal   \n", "1266   26/03/2025    No            DV - DMV CLINICS          Facial   \n", "1280   27/03/2029    No                   ZE - Zero        Corporal   \n", "1286   27/03/2029    No  AEM Aurora Estética Madrid        Corporal   \n", "1353   01/04/2025    No                   ZE - Zero        Corporal   \n", "1467   09/04/2025    No             AR - Armony BMC          Facial   \n", "1566   15/04/2032    No  AEM Aurora Estética Madrid        Corporal   \n", "1572   15/04/2032    No         PB Premier Boutique          Facial   \n", "1590   16/04/2025    No               DX Definixion          Facial   \n", "1596   16/04/2025    No           MC - Mar Chancosa          Facial   \n", "1687   24/04/2025    No         PB Premier Boutique          Facial   \n", "1811   05/05/2025    No         TB - The Beauty Bar        Corporal   \n", "\n", "     CAMPAIGN # AUDIENCE  Ad#     ANGLE  \\\n", "307           8    BROAD  6.0  Headline   \n", "733           1    BROAD  6.0   general   \n", "836          15    BROAD  6.0   general   \n", "842           6    BROAD  6.0   general   \n", "848          47    BROAD  6.0   general   \n", "854           4    BROAD  6.0   general   \n", "860           9    BROAD  6.0   general   \n", "866           5    BROAD  6.0   general   \n", "872           5    BROAD  6.0   general   \n", "878           1    BROAD  6.0   general   \n", "894          41    BROAD  6.0   general   \n", "900           2    BROAD  6.0   general   \n", "906           5    BROAD  6.0   general   \n", "912           6    BROAD  6.0   general   \n", "922           7    BROAD  6.0   general   \n", "928           3    BROAD  6.0   general   \n", "934          48    BROAD  6.0   general   \n", "940           6    BROAD  6.0   general   \n", "1260          4    BROAD  6.0   general   \n", "1266         10    BROAD  6.0   general   \n", "1280          3    BROAD  6.0   general   \n", "1286         45    BROAD  6.0   general   \n", "1353          5    BROAD  6.0   general   \n", "1467          2    BROAD  6.0   general   \n", "1566         48    BROAD  6.0   general   \n", "1572         30    BROAD  6.0   general   \n", "1590         55    BROAD  6.0   general   \n", "1596         12    BROAD  6.0   general   \n", "1687         31    BROAD  6.0   general   \n", "1811         17    BROAD  6.0   general   \n", "\n", "                                                   HOOK  \\\n", "307                                       tiktok scroll   \n", "733                                                 b&f   \n", "836                                                 b&f   \n", "842                                                 b&f   \n", "848                                         testimonial   \n", "854                                 sticker reclama 2x1   \n", "860                                         hook sara 1   \n", "866                       faster hook results voiceover   \n", "872                                                 b&f   \n", "878                                   testimonio madres   \n", "894                                b&f elimina la grasa   \n", "900                                   testimonio madres   \n", "906                               b&f ¿grasa abdominal?   \n", "912                                            articulo   \n", "922                                 faster hook results   \n", "928   internet footage b&f + voiceover (el tratamien...   \n", "934                                  voiceover articulo   \n", "940                                            articulo   \n", "1260                                      clips rapidos   \n", "1266                                                b&f   \n", "1280                                                b&f   \n", "1286                               clip telecinco + b&f   \n", "1353  voiceover whatsap me acabo de eliminar la gras...   \n", "1467                                     reclama tu 2x1   \n", "1566                              b&f voiceover de esto   \n", "1572  b&f internet footage + b&f ia fooatage + b&f i...   \n", "1590                                    testimonio sara   \n", "1596               b&f + reducción de arrugas sin bótox   \n", "1687                           testimonio madres cortos   \n", "1811                             testimonio corto + b&f   \n", "\n", "                                               FEATURES  \n", "307                 carolina 1, <PERSON>, valuestack (regalo)  \n", "733   articulo, b&f, valuestack (musica justin timbe...  \n", "836                                voiceover acompañame  \n", "842                                     voiceover corto  \n", "848                                          valuestack  \n", "854   articulo, valuestack, canva creative chica en ...  \n", "860        valuestack, canva creative chica en la playa  \n", "866                                  voiceover articulo  \n", "872                                  voiceover articulo  \n", "878                                          valuestack  \n", "894                                  descubre la oferta  \n", "900                                          valuestack  \n", "906                                  voiceover articulo  \n", "912                                          valuestack  \n", "922                                          valuestack  \n", "928                                  voiceover articulo  \n", "934                                   testimonio madres  \n", "940                                     valuestack, cta  \n", "1260                                         valuestack  \n", "1266                           voiceover articulo corto  \n", "1280                           voiceover articulo corto  \n", "1286                                  testimonio madres  \n", "1353                                        testimonios  \n", "1467  hook reduce el 15% + b&f voiceover + testimoni...  \n", "1566                                  testimonio madres  \n", "1572        voiceover pasar de esto a esto + valuestack  \n", "1590                                         valuestack  \n", "1596                           voiceover articulo corto  \n", "1687                                 pantalla verde b&f  \n", "1811                                         valuestack  "]}, "execution_count": 295, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df['Ad#']==6.0]"]}, {"cell_type": "markdown", "id": "467f52ef", "metadata": {}, "source": ["## HOOK"]}, {"cell_type": "code", "execution_count": 296, "id": "da676ada", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>27/12/24</td>\n", "      <td>No</td>\n", "      <td>RG Rosa <PERSON></td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>Social proof</td>\n", "      <td>NaN</td>\n", "      <td>periodico, imagen laser zerona, static, CTA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>27/12/24</td>\n", "      <td>No</td>\n", "      <td>RG Rosa <PERSON></td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>GENERAL</td>\n", "      <td>NaN</td>\n", "      <td>imagen laser zerona, static, CTA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>27/12/24</td>\n", "      <td>No</td>\n", "      <td>RG Rosa <PERSON></td>\n", "      <td>Corporal</td>\n", "      <td>10</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>Testimonial</td>\n", "      <td>NaN</td>\n", "      <td>imagen laser zerona, static, CTA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>756</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>757</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>758</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>759</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>760</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1143</th>\n", "      <td>12/2/2025</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>GENERAL</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1144</th>\n", "      <td>12/2/2025</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>GENERAL</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1145</th>\n", "      <td>12/2/2025</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>GENERAL</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DATE CREATED USED?                      CENTRO FACIAL/CORPORAL  \\\n", "85       27/12/24    No              RG <PERSON>   \n", "87       27/12/24    No              RG <PERSON>   \n", "88       27/12/24    No              RG <PERSON>   \n", "756    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "757    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "758    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "759    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "760    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "1143    12/2/2025    No                   OP Opatra          Facial   \n", "1144    12/2/2025    No                   OP Opatra          Facial   \n", "1145    12/2/2025    No                   OP Opatra          Facial   \n", "\n", "     CAMPAIGN # AUDIENCE  Ad#         ANGLE HOOK  \\\n", "85           10    BROAD  1.0  Social proof  NaN   \n", "87           10    BROAD  3.0       GENERAL  NaN   \n", "88           10    BROAD  4.0   Testimonial  NaN   \n", "756          37    BROAD  1.0       general  NaN   \n", "757          37    BROAD  2.0       general  NaN   \n", "758          37    BROAD  3.0       general  NaN   \n", "759          37    BROAD  4.0       general  NaN   \n", "760          37    BROAD  5.0       general  NaN   \n", "1143         12    BROAD  3.0       GENERAL  NaN   \n", "1144         12    BROAD  4.0       GENERAL  NaN   \n", "1145         12    BROAD  5.0       GENERAL  NaN   \n", "\n", "                                         FEATURES  \n", "85    periodico, imagen laser zerona, static, CTA  \n", "87               imagen laser zerona, static, CTA  \n", "88               imagen laser zerona, static, CTA  \n", "756                                           NaN  \n", "757                                           NaN  \n", "758                                           NaN  \n", "759                                           NaN  \n", "760                                           NaN  \n", "1143                                          NaN  \n", "1144                                          NaN  \n", "1145                                          NaN  "]}, "execution_count": 296, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"HOOK\"].isnull()]"]}, {"cell_type": "code", "execution_count": 297, "id": "507666e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['<PERSON><PERSON> 4, OP4', 'Mad<PERSON> 4, OP1', 'Madres 4, B&F3', 'Madres 4',\n", "       'OP3', 'Mad<PERSON> 10', 'Madres 5', 'Madres 7', 'OP3, Madres 10',\n", "       'OP3, <PERSON><PERSON> 5', 'B&F 1,3 fast', 'OP6', 'OP2, OP6',\n", "       'Voiceover alba \"\\'articulo\\'\\'', 'Madres 5 mute, Voiceover alba',\n", "       'Madres 6', 'Madres 6, B&F 1,2,3 fast', 'Madres 7, B&F 1,2,3 fast',\n", "       'SARA 25, B&F 1,2,3 fast', 'Madres 4, B&F 3,5 fast', 'Madres 6 ',\n", "       'Madres 6, B&F 3,5 fast', 'SARA 3, B&F 3,5 fast',\n", "       'Madre 4, B&F 1,3 fast', 'Madre 4 ', 'Madre 3, B&F 1,3 fast',\n", "       'Madre 3', 'SARA 3, B&F 1,3 fast', 'Madres 5,9', 'Madres 11,5',\n", "       'Voiceover alba \\'\\'yo tenia estas arrugas\"\\'', 'OP 5',\n", "       'OP 5, SARA 25', 'B&F Voiceover Alba', 'Voiceover alba, AI voice',\n", "       'Madre 6', 'Madre 2', '4 points, B&F 1,3', '18 sara', '25 sara',\n", "       '21 sara', '<PERSON><PERSON> 7', 'Testimonial sara',\n", "       'Testimonial sara, B&F 1,3 fast', 'Testimonial carla',\n", "       'Testimonial carla, B&F 1,3 fast', 'Ines 1', 'Ines 3 ',\n", "       'B&F 1,2,3', '<PERSON>re 6,2', 'testimonial sara', '22 sara',\n", "       'Madre 6, B&<PERSON> 1,3 fast', 'Madre 10', 'Madre 1',\n", "       'Madre1, B&F 1,3 fast \"facetime mode\"', 'Madre 1 ',\n", "       'Madre 6, B&fF 1,3 fast', 'Madre 10, B&fF 1,3 fast',\n", "       'B&F 1,3 fast ', 'Madre 10 ', 'madre 4', 'madre 3', 'madre 8',\n", "       'madre 6', 'madre 1', nan, 'Copy personalizado',\n", "       'madre 4 (aprieta el cinturon un escalon mas)',\n", "       'madre 4 (menos grasa, mas definicion)',\n", "       'madre 4 (despidete de la grasa con el metodo mas avanzado)',\n", "       'madre 4 (la forma mas sencilla de transformar tu cuerpo)',\n", "       'madre 4 (un metodo facil para un cuerpo sin grasa)',\n", "       'madre 4 (menos grasa, mas confianza)',\n", "       'madre 4, (pierde grasa mientras te relajas)',\n", "       'madre 4 (este 2025 dile adios a la grasa)',\n", "       'madre 4 ( elimina la grasa localizada de forma rapida y sencilla) ',\n", "       'madre 4 (eliminar grasa nunca fue tan facil)',\n", "       'mare 4 (adios a la grasa, hola a tu mejor version)',\n", "       'madre 4 (pierde grasa sin dietas ni rutinas agotadoras)',\n", "       'madres 4 (grasa localizada? ya no mas)',\n", "       'madre 4 (este 2025 consigue el cuerpo que deseas)',\n", "       'madre 4 (estas a un click de consegui el cuerpo que deseas)',\n", "       'B&F (despidete de la grasa con el metodo mas avanzado)',\n", "       'B&F  (la forma mas sencilla de transformar tu cuerpo)',\n", "       'B&F (aprieta el cinturon un escalon mas)',\n", "       'B&F 1,5,3 fast (la forma mas sencilla de transformar tu cuerpo)',\n", "       'B&F 1,5,3 (aprieta el cinturon un escalon mas)',\n", "       'Madre 2 (la forma mas sencilla de transformar tu cuerpo)',\n", "       \"'un articulo''Voiceover alba corporal\", 'madre 2  ',\n", "       'Madre 2 (la forma mas sencilla de transformar tu cara)',\n", "       'B&F 1,3 fast (la forma mas sencilla de transformar tu cara)',\n", "       'Voiceover alba \"Lo que hacen es elimar arrugas\" (la forma mas sencilla de transformar tu cara)',\n", "       'Testimonials (primera sesion) (la forma mas sencilla de transformar tu cara)',\n", "       'B&F alba (la forma mas sencilla de transformar tu cara)',\n", "       'IF papada (la forma mas sencilla para eliminar las lineas de marioneta)',\n", "       'IF voiceover alba(la forma mas sencilla para eliminar las patas de gallo)',\n", "       'IF voiceover alba(la forma mas sencilla para eliminar las lineas de expresion)',\n", "       'B&F 1 (la forma mas sencilla de elimar las arrugas)',\n", "       'IF voiceover alba(la forma mas sencilla para eliminar las lineas de marioneta)',\n", "       'madres 6,2', 'madres 4 ', 'hook 3 sara', 'faster hook 4 if',\n", "       'faster hook results', 'chica inclinando first',\n", "       'hook5, (ad4) CTA4', 'madres4,7', 'hook 25 ', 'hook1, B&F 3',\n", "       'hook 25 sara', 'madres 6',\n", "       \"B&F 1,3 fast ''El tratamiento alternativo al botox \\r\\nque se hacen las famosas''\",\n", "       \"Hook 22 sara ''El tratamiento alternativo al botox \\nque se hacen las famosas''\",\n", "       \"madres 6 ''El tratamiento alternativo al botox \\nque se hacen las famosas''\",\n", "       'B&F 1,3,5 corporal ',\n", "       \"B&F 1,3,5 corporal ''el tratamiento adelgazante que deja en ridiculo a las cirugias''\",\n", "       \"madres 4, ''el tratamiento adelgazante que deja en ridiculo a las cirugias''\",\n", "       \"madres 6,2 ''el tratamiento adelgazante que deja en ridiculo a las cirugias''\",\n", "       'madres 4',\n", "       \"B&F 1,3 fast ''El tratamiento alternativo al botox \\nque se hacen las famosas''\",\n", "       'madre 7',\n", "       \"madre 7 'El tratamiento alternativo al botox \\nque se hacen las famosas''\",\n", "       \"Voiceover alba ''articulo''\", 'Madres 3, 4', 'Madres 6, 2',\n", "       'Madres 4 \\'\\'la forma mas sencilla de transformar tu cuerpo\"\\'',\n", "       'Voice over alba ',\n", "       '<PERSON>res 6, 2 \"\\'la forma mas sencilla de transformar tu cuerpo\\'\\'',\n", "       'B&F 3, <PERSON>re 6', 'B&F 3 ', \"Voice over alba ''articulo''\",\n", "       \"B&F 3 ''no te hagas botox''\",\n", "       \"B&F 3 ''El tratamiento que está remplazando al bótox''\",\n", "       \"Madre 6 ''El tratamiento que está remplazando al bótox''\",\n", "       \"Madre 7 ''El tratamiento que está remplazando al bótox''\",\n", "       \"madres 6,2 ''El tratamiento que está\\n remplazando la liposucción''\",\n", "       \"B&F voiceover alba 'El tratamiento que está\\n remplazando la liposucción''\",\n", "       \"madres 4 'El tratamiento que está\\n remplazando la liposucción''\",\n", "       \"B&F voiceover alba ''Grasa en el abdomen?''\\n\",\n", "       \"B&F 3 fast ''destruir arrufas es mas facil de lo que crees''\",\n", "       \"madres 6 ''destruir arrufas es mas facil de lo que crees''\",\n", "       \"B&F 3 fast ''¡Destruye esas arrugas este 2025!''\",\n", "       \"madre 6 ''¡Destruye esas arrugas este 2025!''\",\n", "       \"Madre 6 ''destruir arrugas es mas facil de lo que crees''\",\n", "       \"B&F 3 fast ''destruir arrugas es mas facil de lo que crees''\",\n", "       'B&F 3 fast',\n", "       \"B&F 3 fast ''El tratamiento que está remplazando al bótox''\",\n", "       'madres 6 \"sin manchas\\'\\'', 'Maria 1, B&F 3 fast',\n", "       'Maria 2, B&F 3 fast', '<PERSON> 3, <PERSON> 1', '<PERSON> 4, 1, <PERSON> 1',\n", "       '<PERSON><PERSON> 5, <PERSON> 1 (porque NO te funcionan los tratamientos faciales)',\n", "       'Carolina 1, B&F 3 fast (porque NO te funcionan los tratamientos faciales)',\n", "       'Carolina 1, B&F 3 fast ', 'Carolina 1, B&F 3 ', '(articulo) alba',\n", "       'madres 6,2 ',\n", "       \"B&F voiceover alba 'El tratamiento que está\\n remplazando la liposucción'' (Te hacen pasar de esto a esto)\",\n", "       'B&F voiceover alba ', 'RDR video popping AEM',\n", "       \"madres 4 ''reclama tu 2por1''\", \"madres 6,2 ''reclama tu 2por1''\",\n", "       \"valuestack, ''Apr<PERSON>a el cinturon un escalon mas''\",\n", "       \"madres 4 ''Aprieta el cinturon un escalon mas''\",\n", "       \"madres 4 ''Menos grasa mas definicion''\",\n", "       \"B&F ''despidete de la grasa con el metodo mas avanzado'' valuestack \",\n", "       \"B&F ''la forma mas sencilla de transformar tu cuerpo'' valuestack\",\n", "       \"B&F ''aprieta el cinturon un escalon mas''\",\n", "       \"B&F 3 fast ''no te hagas botox'' (diferencia antes y despues)\",\n", "       \"B&F 3 fast ''el tratamiento que esta remplazando al botox'' (diferencia antes y despues)\",\n", "       'madre 7 ¡Destruye esas arrugas este 2025!',\n", "       'B&F 3 FAST ¡Destruye esas arrugas este 2025!',\n", "       'madre 6 ¡Destruye esas arrugas este 2025!',\n", "       'madre 6 ¡NO TE HAGAS BOTOX!', \"madre 7 ''destruye esas arrugas''\",\n", "       \"madre 6 ''destruye esas arrugas''\",\n", "       \"B&F 3 fast ''destruye esas arrugas''\",\n", "       \"madre 6 ''Destruir arrugas es mas facil\\n de lo que crees''\",\n", "       \"B&F 3 fast ''Destruir arrugas es mas facil\\n de lo que crees''\",\n", "       \"madre 7 ''Destruir arrugas es mas facil\\n de lo que crees''\",\n", "       'voice over articulo ', 'voiceover alba articulo ',\n", "       \"madre 6 ''eliminar arrugas nunca fue tan facil''\",\n", "       \"madre 7 (solo arrugas) ''eliminar arrugas nunca fue tan facil''\",\n", "       \"madre 6 ''destruir arrugas nunca fue tan facil''\",\n", "       \"madre 7 ''destruir arrugas nunca fue tan facil''\",\n", "       \"B&F 3 fast ''destruir arrugas nunca fue tan facil''\",\n", "       \"Laura1 ''Tu piel es única\\n ¿por qué usar cremas genéricas?''\",\n", "       \"Laura 1 ''Tu piel es única\\n ¿por qué usar cremas genéricas?''\",\n", "       \"Laura 1 ''Las cremas genéricas \\r\\nNO borran tus arrugas.''\",\n", "       \"madres 6 ''Las cremas genéricas \\nNO borran tus arrugas.''\",\n", "       \"madre 6 ''Las cremas genéricas \\nNO borran tus arrugas.''\",\n", "       \"madre 7 ''Las cremas genéricas \\nNO borran tus arrugas.''\",\n", "       'tiktok scroll', 'tiktok scroll RED', 'IG scroll', 'fall hook',\n", "       'valuestack AEM RDR', 'valuestack AEM',\n", "       'valuestack AEM RDR. voice over carolina talking',\n", "       'valuestack AEM,  voice over carolina talking',\n", "       \"'destruir arrugas nunca fue tan facil'' voice over carolina \",\n", "       \"'arrugas?l'' voice over carolina \",\n", "       \" ''articulo'' voice over carolina \",\n", "       \"B&F3 fast ''destruye esar arrugas este  2025''\",\n", "       '<PERSON>oto estetica, Carolina Voiceover',\n", "       'b&f whoop fast, <PERSON> Voiceover',\n", "       \"b&f whoop fast, ''despidete de la grasa con el metodo mas avanzado''\",\n", "       \"b&f whoop fast, ''la forma mas sencilla de transformar tu cuerpo''\",\n", "       \"'articulo'' Carolina Voiceover\", 'b&f 1,3 fast', 'madres 7',\n", "       \"'articulo'' voiceover alba\", 'madres 11', 'voiceover carolina ',\n", "       'b&f 3 fast whoop', 'b&f 3 fast \\'\"amtes depues\"\\'',\n", "       'Foto estetica, ',\n", "       \"B&F, ''la forma mas sencilla de transformar tu cuerpo'' voiceover carolina  \",\n", "       \"B&F ''grasa en el abdomen?'' voiceover carolina  \",\n", "       \"B&F fast ''La forma mas sencilla de transformar tu cuerpo''\",\n", "       \"'articulo'' voiceover carolina\",\n", "       \"madres 6 ''destruir arrugas nunca fue tan facil''\",\n", "       'b&f 3 fast \\'\"antes depues\"\\'',\n", "       \"'<PERSON><PERSON> San Valentín, sorprende a todos con una piel renovada'' voiceover carolina\",\n", "       'hook 20 sara',\n", "       \"madres 10 ''Destruir arrugas nunca fue tan fácil''\",\n", "       \"b&f 3 fast whoop ''Destruir arrugas nunca fue tan fácil''\",\n", "       \"hook 20 sara ''Este mes del amor, rejuvenece tu piel y destruye esas arrugas''\",\n", "       \"madres 10 ''Este 14 de febrero, dile sí al amor propio y adiós a las arrugas''\",\n", "       \"b&f 3 fast whoop ''<PERSON> radiante \\r\\neste 14 de febrero''\",\n", "       \"sara testi, ''Este mes del amor, rejuvenece tu piel\\r\\n y destruye esas arrugas''\",\n", "       \"madres 6  ''Destruir arrugas nunca fue tan fácil''\",\n", "       'b&f 3 fast whoop, voice over carolina 48 segs', 'madres 10',\n", "       \"testi sara, ''arrugas?''\",\n", "       'valuestack AEM b&f, voice over carolina',\n", "       \"'grasa en el abdomen?'' valuestack AEM b&f, voice over carolina\",\n", "       \"'grasa en el abdomen?'' madres 4,2\",\n", "       \"'arrugas?'' b&f voiceover carolina\",\n", "       \"foto estetica ''hace falta mas que una crema pero menos que un pinchazo''\",\n", "       'b&f 3 fast whoop voiceover carolina',\n", "       \"'destruir arrugas nunca fue tan facil'' utilizan tecnologia...\",\n", "       'testimonials', \"'bye bye arrugas'' b&f 3 \",\n", "       \"'bye bye grasa abdominal'' b&f 3 red points, voiceover carolina (terapia ultrasonica)\",\n", "       \"'bye bye grasa abdominal'' sara testi\",\n", "       \"'bye bye grasa abdominal''  b&f 3 red points, voiceover carolina (terapia ultrasonica)\",\n", "       \"'bye bye grasa abdominal''  b&f 5 red points, voiceover carolina (terapia ultrasonica)\",\n", "       \"'bye bye grasa abdominal''  sara testi\",\n", "       \"B&f 3 results ''haace falta mas que una crema''\",\n", "       'hook9 (ad4) CTA4', 'hook12 (ad4)',\n", "       \"Foto estetica, <PERSON> Voiceover'' 30% menos colageno''\",\n", "       \"Foto estetica, <PERSON> Voiceover  ''25 anos''\",\n", "       \"articulo ''colageno''\", 'foto estetica, voiceover carolina ',\n", "       'B&F fast, voiceover carolina ',\n", "       \"B&F 3 fast  ''eliminar arrugas nunca fue tan facil''\",\n", "       'madres 6, <PERSON> Voiceover', 'B&F fast, voiceover carolina  ',\n", "       \"B&F fast ''despidete de la grasa con el metodo mas avanzado''\",\n", "       \"B&F fast ''dLa forma mas sencilla de transformar tu cuerpo''\",\n", "       'articulo, voiceover carolina  ',\n", "       \"'eliminar arrugas nunca fue tan facil'' \", \"'articulo''\",\n", "       \"'eliminar arrugas nunca fue tan facil''  madres 6\",\n", "       'foto facial estetica',\n", "       \"b&f fast whoop ''Bye bye grasa abdominal''\",\n", "       \"b&f mujer acostandose ''Bye bye grasa abdominal'' voiceover carolina\",\n", "       'valuestack AEM, voice over carolina',\n", "       \"sara testi ''Bye bye grasa abdominal'' \",\n", "       'hook 4 AEM, b&f flechas',\n", "       \"'grasa en el abdomen?'' hook 4 AEM, b&f flechas\",\n", "       \"'Bye bye arrugas'' Hook 1 gemma\",\n", "       \"'Bye bye arrugas'' Hook 2 gemma\",\n", "       \"'Bye bye arrugas'' Hook 1, b&f 3 fast whoop\",\n", "       \"'Bye bye arrugas'' Hook 3 gemma\",\n", "       \"'Bye bye arrugas'' Hook 4 gemma\",\n", "       \"'Bye bye arrugas'' Hook 5 gemma\",\n", "       \"'Bye bye arrugas'' Hook 6 gemma\",\n", "       \"'Bye bye arrugas'' Hook 7 gemma\",\n", "       \"'Bye bye arrugas'' Hook 8 gemma\",\n", "       \"'Bye bye arrugas'' Hook 9 gemma\",\n", "       \"'Bye bye arrugas'' Hook 10 gemma\",\n", "       \"'Bye bye arrugas'' Hook 11 gemma\",\n", "       \"'Bye bye arrugas'' Hook 12 gemma\",\n", "       \"'Bye bye arrugas'' Hook 13 gemma\",\n", "       \"'Bye bye arrugas'' Hook 14 gemma\",\n", "       \"'Bye bye arrugas'' Hook 15 gemma\",\n", "       \"'Bye bye arrugas'' Hook 1 gemma, b&f 3 fast whoop\",\n", "       \"'Bye bye arrugas'' Hook 4 gemma, b&f 3 fast whoop\",\n", "       \"'Bye bye arrugas'' b&f 3 con flechas, voiceover carolina\",\n", "       'voiceover gemma ', 'hook 2 gemma ', 'voiceover articulo alba',\n", "       'body facial 1 carla 3', 'testimonial facial miriam',\n", "       'facial 1 gemma', 'ad recording 4 hook facial',\n", "       'testimoni facial marta',\n", "       'voiceover (te hacen pasar de esto a esto)',\n", "       'voiceover (estaba buscando un tratamiento facial)',\n", "       'hook 5 gemma', 'hook 21 sara',\n", "       'testimonio madres 1 silvia i raquel', 'hook 6 gemma',\n", "       'testimonial ad recording 4, testimonial gemma, testimonial madres',\n", "       'testimonial facial carla 3, testimonial facial madres',\n", "       'hook sara', 'voiceover b&f ', 'testimonio madres 1',\n", "       'testimonio ad recording 4',\n", "       'voiceover, b&f puntos rojos, valuestack',\n", "       'voiceover, b&f puntos rojos, bye bye grasa',\n", "       'voiceover, b&f flechas',\n", "       'voiceover testimonio gemma, b&f puntos rojos',\n", "       'voiceover gemma, b&f flechas',\n", "       'faster hook results, ¿grasa abdominal?',\n", "       'faster hook results, voiceover sara',\n", "       'faster hook results, voiceover testimonio madres',\n", "       'testimonios madres', 'b&f internet footage',\n", "       'internet footage (ilustración)', 'b&f, no voiceover',\n", "       'internet footage', 'ad recording 3 corporal 4, b&f stickers',\n", "       'b&f', 'b&f, voiceover', 'testimonio madres',\n", "       'testimonio carla, testimonio madres, testionio ad recording, testimonio sara, testimonio madres',\n", "       'testimonio madres, testimonio ad recording, testimonio carla, testimonio sara, testimonio madres.',\n", "       'testimonio madres, testimonio sara, testimonio madres, testimonio ad recording, testimonio carla.',\n", "       'testimonio ad recording, testimonio madres, testimonio carla, testimonio madres, testimonio sara.',\n", "       'testimonio madres, testimonio sara, testimonio madres, testimonio ad recording, testimonio carla, testimonio madres.',\n", "       'b&f stickers', 'pantalla partida internet footage',\n", "       'internet fooatge fast video',\n", "       'inteernet fooatge (acompañame a un tratamientode body sculpt)',\n", "       'faster hook results interent footage',\n", "       'internet footage, elimina las arrguas',\n", "       'b&f stickers, voiceover (de esto a esto)',\n", "       'b&f stickers, voiceover (de esto a esto) stikers y persona diferente',\n", "       'b&f stickers sin voiceover',\n", "       'b&f stickers sin voiceover, persona diferente',\n", "       'b&f stickers, voiceover',\n", "       'b&f stickers, voiceover (de esto a esto) stikers',\n", "       'fast internet footage', 'fast internet footage results ',\n", "       'efecto borrador', 'results', 'tratamiento',\n", "       'before + tratamiento', 'acompañame a hacerme el tratamiento',\n", "       'asmr results', 'list ', 'asmr', 'el tratamiento', 'zoom in b&f',\n", "       'fast before images', 'beneficios del antiaging',\n", "       'antes del antiaging, durante', 'hook cansada de las arrugas',\n", "       'testimonio madres + fotos descriptivas', 'b&f voiceover',\n", "       'after results', 'inetrnet fooatge (asi sali del tratamiento)',\n", "       'pantalla partida', 'b&f reclama tu 2x1', 'b&f valuestack',\n", "       'b&f stickers voiceover', 'hooks facial sara1',\n", "       'hooks facial sara1 audio + b&f 1,3 fast', 'b&f fast hook results',\n", "       'un b&f', 'un b&f ', 'voiceover art<PERSON><PERSON><PERSON>', 'before',\n", "       'beneficios del antiaging + internet footage', 'voiceover body ',\n", "       'hook 1 imagenes espcaio', 'hook 1 gral 4 voiceover + b&f',\n", "       'voiceover articulo', 'hook 1 gral 7 voiceover + b&f',\n", "       'hook 1 facial 7 + b&f 1, 3 fast', 'hook 3',\n", "       'b&f hook reclama tu 2x1', 'hook 2 stickers', 'beneficios',\n", "       'beneficios + b&f 1,3 fast', 'hooks 1 gral 1 + b&f 1, 3 fast',\n", "       'hooks 1 gral 4 + b&f 1,3 fast', 'body gemma 1 + b&f',\n", "       'acompañame sin voiceover', 'acompañame voiceover',\n", "       'reclama tu 2x1', 'b&f ', 'testimonio gemma internet footage',\n", "       'testimonials facial 3 gemma',\n", "       'testimonials facial 3 gemma + b&f 1,3 fast',\n", "       'facial 7 gemma + b&f 1,3 fast', 'facial 7 gemma ',\n", "       'facial 8 gemma', 'facial 8 gemma + b&f 1,3 fast',\n", "       'facial 6 gemma', 'voiceover acompañame', 'testimonio ',\n", "       'testimonio + testimonio msadres',\n", "       'b&f reclama tu 2x1 internet footage', 'b&f voiceover testimonios',\n", "       'b&f reclama tu 2x1 + voiceover', 'b&f + voiceover',\n", "       'testimonial corporal miriam', 'b&f reclama 2x1',\n", "       'b&f reclama 2x1 + voiceover', 'b&f + voiceover articulo',\n", "       'testimonial corporal marta 2', 'te imaginas poder eliminar',\n", "       'testimonio + internet footage', 'testimonial vi este anuncio',\n", "       'mensaje whatsap voiceover', 'gemma facial 1 voiceover + b&f',\n", "       'testimonial', 'testimonial miriam', 'testimonial marta',\n", "       'ARRUGAS, b&f3 red circles whoop, ', 'ARRUGAS, gemma voiceover',\n", "       'ARRUGAS, b&f 3 fast', 'GARASA ABDOMINAL, madre 3',\n", "       'GARASA ABDOMINAL, gemma testi', 'GARASA ABDOMINAL, madres 6,2',\n", "       'GARASA ABDOMINAL, b&f', 'ADELGAZA SIN SUFRIR, ines',\n", "       'faster hook results stickers', 'voiceover dibujo',\n", "       'faster hook results + voiceover', 'voiceover internet fooatge',\n", "       'valuestack', 'testimonio marta',\n", "       'b&f + voiceover testimonial gemma', 'testimonio raquel',\n", "       'gemma 7', 'gemma 7 voiceover + b&f 1,3 fast',\n", "       'sticker reclama tu 2x1', 'sticker reclama tu 2x2', 'hook sara 8',\n", "       'hook sara 11', 'hook gemma 3 voiceover + b&f', 'voiceover gemma',\n", "       'testimonio ad recording', 'voiceover sara',\n", "       'b&f voiceover de esto a esto', 'b&f ¿grasa abdominal?',\n", "       'b&f ¿grasa abdominal? voiceover', 'testimonial raquel',\n", "       'testimonial madres', 'sticker reserva tu 2x1',\n", "       'sticker reclama 2x1', 'b&f fast',\n", "       'b&f fast (bye bya grasa abdominal)',\n", "       'b&f stickers (bye bya grasa abdominal)', 'voiceover dibujos',\n", "       'resultados', 'hook porque nadie me habia dicho esto antes gemma',\n", "       'internet footage famosas sin arrugas',\n", "       'voiceover articulo (el tratamiento que usan las famosas)',\n", "       'after', 'inetrent footage', 'articulo', 'articulo + b&f',\n", "       'hook gemma 1', 'voiceover hook gemma 1 + b&f 1,3 fast',\n", "       'tstimonio madres', 'hook gemma internet footage', 'hook gemma',\n", "       'internet footage voiceover de esto a esto', 'testimoniales',\n", "       'voiceover me aparecio el anuncio', 'voiceover ',\n", "       'testimonio selfie madres', 'testimonio ad recording selfie',\n", "       'articulo + canva creative chica investigando',\n", "       'articulo + canva creative chica mirandose al espejo',\n", "       'hook sara 1',\n", "       'testimonio sara testimonio mardes testimonio gemma',\n", "       'faster hook results voiceover', 'voiceover articulo animacion',\n", "       'voiceover gemma (por que nadie me habia dicho esto antes)',\n", "       'sticker reclama tu 2x1+ b&f', 'sticker reclama tu 2x1 + b&f',\n", "       'sticker reclama tu 2x1 + chica mirandose la panza',\n", "       'hook gemma 2', 'hook gemma 3', 'articulo + internet footage',\n", "       'hook gemma 11 + internet footage', 'b&f elimina la grasa',\n", "       'testimonio madres, ad recording, gemma', 'b&f de esto a esto',\n", "       'hook gemma 11 ', 'hook gemma 11', 'bye bye arrugas',\n", "       'testimonio madre',\n", "       'internet footage b&f + voiceover (el tratamiento se lllama) + titulo ¿grasa en el abdomen?',\n", "       'voiceover dinternet footage',\n", "       'b&f + titulo (el tratamiento que esta remplazando la liposuccion)',\n", "       'gem<PERSON> 2', '<PERSON><PERSON> 10,6', '<PERSON><PERSON> 2,7',\n", "       'B&F  (la forma mas sencilla de transformar tu cuerpo', 'madres 3',\n", "       '[IF papada (la forma mas sencilla para eliminar la papada)',\n", "       \"un articulo''Voiceover alba corporal\", 'madre 2',\n", "       'madre 4 ( elimina la grasa localizada de forma rapida y sencilla)',\n", "       'Ines 3', 'Madre1, B&F 1,3 fast facetime mode',\n", "       \"Voiceover alba ''yo tenia estas arrugas\", 'Madre 4',\n", "       'B&F3, Madre 3', 'madre 9, B&F 3 pantalla dividida',\n", "       'Sara testimonial B&F 3 pantalla dividida', 'carla testimonial ',\n", "       'Hook18 carla', '2 madres B&F 3 pantalla dividida',\n", "       'Testimonial Sara ', 'B&F 1, Testimonial carla',\n", "       'IF 9, Valuestack', 'B&F 3, Valuestack ', 'madre 10',\n", "       '9 IF Valuestack', '\"adios a la flacidez\\'\\' B&F 3, valuestack',\n", "       \"'adios a las arrugas'' IF 9, valuestack \",\n", "       '\\'adios a las manchas\"\\' Madre 10', \"'adios arrugas y flacidez''\",\n", "       '20 sara',\n", "       'Antes y despues 2 con banner diciendo eliminar las arrugas',\n", "       'video de ',\n", "       'antes y despues con un texto que dice \"elimina la grasa en los primeros dias\"',\n", "       'internet footage voiceover', 'internet footage preguntas',\n", "       'internet footage antes (reto)',\n", "       'internet footage no reconoci a mi amiga',\n", "       'internet footage modelos', 'voiceover informacion',\n", "       'esto no es botox + b&f', 'mito del botox',\n", "       'por qué solo las cremas no funcionan',\n", "       'usas cremas y no ves resultados',\n", "       'el botox paraliza los musculos', 'clip telecinco',\n", "       'b&f + voiceover de esto a esto',\n", "       'voiceover de esto a esto + internet footage piel estirada ',\n", "       'voiceover estos fueron los resultados + internet footage piel estirada',\n", "       'voiceover articulo dibujos', 'b&f + valuestack',\n", "       'estos fueron los resultados', 'b&f internet footage (rubia)',\n", "       'voiceover testimonio madres + b&f internet footage (rubia)',\n", "       'crema sin resultados?', 'por que aparecen las arrugas',\n", "       'testimonio', 'testimonio selfie',\n", "       'tetsimonio madres+ad recording+testimonio dx',\n", "       'voiceover internet footage', 'testimonio DX', 'telecinco',\n", "       'testimonio ad recording4',\n", "       'las arrugas no aparecen x falta de cremas',\n", "       'usando cremas sin resultados', 'no necesitas cirugia + b&f',\n", "       'elimina la grasa sin cirugia', 'capturas articulo',\n", "       'before + voiceover anuncio', 'testimonios',\n", "       'el tratamiento de botox que las clinicas no quieren que conozcas',\n", "       'parece que me he quitado 10 años de encima',\n", "       'por qué nadie habla de la alternativa al botox sin pinchazos',\n", "       'sabias que puedes eliminar arrugas...',\n", "       'si tienes arrugas necesitas ver esto',\n", "       'dile adios a la grasa localizada',\n", "       'grasa rebelde que no desaparece?',\n", "       'si haces dieta y ejercicio y no ves resultados',\n", "       'la tecnica que esta dejando en ridiculo a la lipo',\n", "       'eliminar grasa sin cirguia',\n", "       'b&f (esto es el antes y el despues)',\n", "       'por que nadio me dijo esto antes', 'clips rapidos',\n", "       'testimonio madres + ad recording',\n", "       'voiceover (por que nadie me dijo esto antes)',\n", "       'testimonio miriam', 'testimonio madre + cllip telecinco',\n", "       'testimonio ad recording + clip telecinco', 'clip telecinco + b&f',\n", "       'dile adios a la grasa localizada + b&f',\n", "       'grasa rebelde que no desaparece? + b&f',\n", "       'la tecnica que esta dejando en ridiculo a la lipo & bf',\n", "       'si tienes arrugas necesitas ver esto + b&f',\n", "       'parece que me he quitado 10 años de encima + b&f',\n", "       'testimonio raquel selfie', 'testimonio ad recording + b&f',\n", "       'voiceover articulo short', 'clips tratamiento',\n", "       'articulo voiceover + b&f', 'articulo corto',\n", "       'grasa rebelde + b&f',\n", "       'la tecnica q deja en ridiculo a la lipo + b&f',\n", "       'si haces dieta y ejercicio y no ves resultados + b&f',\n", "       'voiceover alternativa al botox + ia footage',\n", "       'voiceover alternativa al botox', 'voiceover b&f',\n", "       'b&f internet footage fast', 'b&f ia footage footage fast',\n", "       'voiceover articulo corto', 'testimonio dx',\n", "       'si tienes arrugas necesitas ver esto + b&f ',\n", "       'sabias que puedes eliminar arrugas + b&f',\n", "       'testimonio madres + hook cuadrado', 'testimonio + hook cuadrado',\n", "       'voicoever',\n", "       'voiceover whatsap me acabo de eliminar la grasa sin dolor',\n", "       'de esto a esto + b&f',\n", "       'puedes rejuvenecer la piel sin agujas ni cirugías',\n", "       'Si pudieras verte 5 años más joven sin dolor',\n", "       'sin agujas, sin dolor y con resultados inmediatos',\n", "       'Si te da miedo el botox, pero quieres eliminar arrugas',\n", "       'tetsimonio sara', 'tetsimonio sara + madres + ad recording',\n", "       'testimonio madres + ia footage', 'voiceover b&f ia footage',\n", "       'voiceover articulo + b&f', 'testimonio raquel + b&f',\n", "       'elimina las arrugas + b&f',\n", "       'puedes eliminar las arrugas + b&f + sticker elimina las arrugas ',\n", "       'b&f recap', 'testimonio madres recap',\n", "       '\"me siento enga<PERSON>...\" - ai avatar',\n", "       '\"llevo años malgastando dinero\"',\n", "       '\"no te hagas este tratamiento...\"',\n", "       '\"no te hagas este tratamiento...\" + b&f', 'greenscrean gemma',\n", "       'greenscrean articulo voiceover',\n", "       ' internet footage (elimina la graasa)',\n", "       'como pasar de esto a esto en una sesión', 'voiceover whatsap',\n", "       'cansada de los pinchazos (error sound)',\n", "       'testimonio madres greenscrean', 'internet footage graficos',\n", "       'captura pantaalla anuncio', 'ai avatar \"me estafaron en el gym\"',\n", "       'ai avatar \"ya no guardare mas secretos\"', 'testimonio + b&f',\n", "       'body 2 gemma + b&f', 'testimonio miriam + b&f',\n", "       'testimonio madres + b&f', 'testimonio sara / b&f',\n", "       'elimina la grasa/reclama tu 2x1 + cuadro testimonio raquel + b&f',\n", "       'testimonio ad recording greenscrean',\n", "       'greenscrean gemma (de esto a esto)', 'greenscrean testimonial',\n", "       'greenscrean testimonio madres',\n", "       'greenscrean testimonio madres + voiceover articulo',\n", "       'hook adelgaza sin dolor', 'testimonio marta + b&f',\n", "       'testimonio sara + b&f', 'de esto a esto ai avatar + b&f',\n", "       'voiceover de esto a esto', 'testimonio madre greenscrean',\n", "       'testimonio madre + b&f', 'ai avatar + b&f',\n", "       'ai avatar de esto a esto + b&f',\n", "       '\"sin pinchazos ni dolor\" ai avatar + b&f', 'b&f ia footage fast',\n", "       'hook reduce el 15% + b&f voiceover',\n", "       'b&f voiceover + hook reduce el 15%',\n", "       'hook reduce desde la primera sesión + b&f voiceover',\n", "       'b&f voiceover + hook reduce desde la primera sesión',\n", "       'reclama tu 2x1 b&f', 'ai avatar \"no te hagas este tratamiento\"',\n", "       'resultados reales ai avatar',\n", "       'hook elimina grasa sin dolor + b&f',\n", "       'b&f voiceover te hacen pasar',\n", "       'hook los 60 pensaba que ya nada funcionaria para adelgazar',\n", "       'hook hasta que no entendí esto, no me funcionaban los tratamientos corporales\\n',\n", "       'hook encontre la solucion',\n", "       'voiceover pasar de esto a esto + valuestack',\n", "       'hook porfin la ciencia', 'hook borra arrugas sin botox',\n", "       'hook como lo ha hecho marta',\n", "       'internet footage con muchas arrugas', 'elimina la grasa + b&f',\n", "       'b&f + sin agujas, sin dolor y con resultados inmediatos',\n", "       'testimonios bodyscultping', 'b&f + sin agujas y sin dolor',\n", "       'b&f + articulo', 'hook adelgaza sin dolor + testimonio madres',\n", "       'hook baja grasa sin dolor + testimonio madres',\n", "       'hook adiós grasa sin dolor + testimonio madres',\n", "       'testimonio sara', 'ai avatar',\n", "       'hook elimina arrugasa sin dolor b&f imagen',\n", "       'hook elimina arrugasa sin dolor b&f video',\n", "       'hook baja grasa sin dolor testimonio madres',\n", "       'hook estos fueron los resultados internet footage',\n", "       'hook adios grasa sin dolor testimonio madres',\n", "       'hook adelgaza sin dolor testimonio madres',\n", "       'hook adelgaza sin dolor testimonio sara',\n", "       'clip telecinco + b&f voiceover de esto',\n", "       'b&f voiceover de esto + clip telecinco', 'b&f voiceover de esto',\n", "       'b&f voiceover de esto + testimonio madres',\n", "       'testimonio madre hook baja grasa abdominal',\n", "       'b&f internet footage + b&f ia fooatage + b&f internet footage',\n", "       'testimonio madres adelgaza sin dolor',\n", "       'de esto a esto elimina arrugas sin dolor',\n", "       'b&f + hook elimina arrugas sin dolor',\n", "       'b&f + hook elimina arrugas sin bótox',\n", "       'b&f + elimina arrugas sin pinchazos',\n", "       'b&f + reduce arrugas sin pinchazos',\n", "       'b&f + reduce arrugas sin dolor',\n", "       'b&f + reducción de arrugas sin bótox', 'de esto a esto',\n", "       'b&f rejuvenece sin dolor', 'de esto a esto rejuvenece sin dolor',\n", "       'de esto a esto zoomeado rejuvenece sin dolor',\n", "       'b&f de esto a esto sin dieta sin dolor',\n", "       'testimonio madres sin dieta sin dolor', 'b&f sin dieta sin dolor',\n", "       'b&f adelgaza sin dolor', 'maquina adelgaza sin dolor',\n", "       'maquina 3 adelgaza sin dolor',\n", "       'b&f + testimonio carla elimina la grasa', 'testimonio madres ',\n", "       'b&f + testimonio madres', 'testimonio madres + voiceover gemma',\n", "       'de esto a esto adelgaza sin dolor',\n", "       'testimonio carla elimina la grasa', 'b&f + adelgaza sin dolor',\n", "       'b&f + elimina la grasa, reclama tu 2x0',\n", "       'testimonio madres + b&f + elimina la grasa, reclama tu 2x1',\n", "       'b&f no voiceover',\n", "       'voiceover gemma (escuchar a las clientas) testimonio madres',\n", "       'testimonio VH', 'fast footage',\n", "       'elimina arrugas sin agujas y sin dolor + b&f',\n", "       'elimina arrugas sin dolor + b&f', 'de esto a esto ',\n", "       'articulo voiceover + b&f elimina arrugas sin dolor',\n", "       'testimonio madres cortos',\n", "       'b&f de esto a esto hook rejuvenece sin dolor',\n", "       'b&f foto de esto a esto hook rejuvenece sin dolor',\n", "       'estos fueron mis resultados despues de, interent footage',\n", "       'estos fueron mis resultados despues de, ia footage',\n", "       'ad recording testimonial pantalla verde',\n", "       'ad recording testimonial + tetsimonio madres pantalla verde',\n", "       'articulo corto + b&f', 'b&f + testimonios',\n", "       'b&f foto internet footage', 'foto testimonio + b&f',\n", "       'maquina radiofrecuencia adelgaza sin dolor',\n", "       'elimina grasa sin dolor tratamientos fast',\n", "       'voiceover articulo corto + acompañame 1', 'de esto a esto corto',\n", "       'voiceover largo de esto a esto hook adelgaza sin dolor',\n", "       'testimonio madres silvia',\n", "       'testimonio madres silvia + testimonio madres',\n", "       'voiceover de esto a esto hook adelgaza sin dolor',\n", "       'testimonio + testimonio madres', 'voiceover testimonio',\n", "       'de esto a esto corto adelgaza sin dolor',\n", "       'testimonio carla corto + b&f', 'hook carla b&f voiceover',\n", "       'hoko madres + carla', 'esto a esto corto ',\n", "       'b&f + articulo voiceover', 'b&f + testimonios corto',\n", "       'de esto a esto corto + testimonio carla',\n", "       'b&f fast adelgaza sin dolor y sin agujas',\n", "       'testimonio sara greenscrean', 'testimonio selfie greenscrean',\n", "       'testimonio greenscrean', 'hook adelgaza sin dolor +  b&f',\n", "       'b&f fast voiceover', 'de esto a esto corto + reclama tu 2x1',\n", "       'testimonio corto + reclama tu 2x1', 'testimonio corto + b&f',\n", "       'acompañame 2 + b&f', 'acompañame 6 + b&f',\n", "       'testimonio raquel y silvia',\n", "       'testimonio madres + testimonio carla',\n", "       'testimonio carla + testimonio madres', 'testimonio carla',\n", "       'b&f fast voicoever + reclama tu 2x1',\n", "       'hok adelgaza sin dieta sin dolor + fast b&f voiceover',\n", "       'hok adelgaza sin dieta sin dolor + fast b&f ',\n", "       'hook reclama tu 2x1 + b&f', 'testimonio pantalla dividida b&f',\n", "       'testimonio marta / b&f', 'testimonio miriam / b&f',\n", "       'testimonio carla / b&f',\n", "       'fast b&f + hook adelgaza sin dieta sin dolor + voiceover el tratamiento',\n", "       'testimonio madres pantalla doble b&f',\n", "       'testimonio carla pantalla doble b&f',\n", "       'testimonio ad recording pantalla doble b&f', 'fast b&f',\n", "       'testimonio / b&f', 'voiceoverpuedes eliminar arrugas sin botox',\n", "       'lo que hacen + b&f', 'hook ¿quieres reducir grasa? + b&f',\n", "       'hook 5 motivos por lo que probar el bdoy sculpt',\n", "       'hook asi elimino la grasa del abdomen Sara + b&f',\n", "       'hook asi elimino la grasa del abdomen Maria + b&f',\n", "       '<PERSON> elimino, sin dolor, la gras abdominal., asi lop hizo',\n", "       'estos son los resultados reales de nuestro tratamiento bodysculpt',\n", "       '¿cansada de la grasa abdominal?',\n", "       '3 clientas, como tu cansadas de no poder reducir grasa abdominal',\n", "       'espera! mira los resultados de nuestro tratamiento body sculpt',\n", "       'de esto a esto corto ',\n", "       'estos son los resultados desps de 1 sesion',\n", "       'es posible eliminar grasa sin cirugia',\n", "       'reduce grasa sin dolor reclamando el 2x1 ',\n", "       'testimonio madres / b&f',\n", "       'aprovecha el 2x1 antes que llegue el verano',\n", "       'el bodysculpting no solo cambia tu cuerpo, lo celebra',\n", "       'testimonio madre espejo', 'voiceover estaba buscando + b&f',\n", "       'pantalla partida b&f', 'b&f foto + reclama tu 2x1',\n", "       'b&f + reclama tu 2x1',\n", "       'adelgaza sin dieta sin dolor de esto a esto',\n", "       'articulo corto voiceover + b&f',\n", "       'reclama tu 2x1 adelgaza sin dolor',\n", "       'puedes eliminar arrugas sin agujas', 'testimoio + b&f',\n", "       'hook gemma 8', 'hook gemma 9', 'cta gemma 5 + b&f',\n", "       'testimonial gemma 2 / b&f', 'testimonial gemma 3',\n", "       'testimonial gemma 4', 'hook gemma 5', 'hook gemma 7',\n", "       'testimonial gemma 1 + b&f', 'testimonial gemma 2 ',\n", "       'testimonial gemma 3 + b&f', 'testimonial gemma 1',\n", "       'testimonial gemma 2',\n", "       'testimonial gemma 3 voiceover + gemma footage',\n", "       'testimonial gemma 5/b&f', 'hook gemma 4 / b&f',\n", "       'hook gemma 8 / b&f', 'testimonio gemma 5 / b&f',\n", "       'b&f + voiceover articulo adelgaza',\n", "       'b&f + voiceover articulo adios gasa', 'testimonio gemma 1 / b&f',\n", "       'testimonios / b&f', 'hook gemma 2 + b&f', 'hook gemma 3 ',\n", "       'hook gemma 4 + b&f', 'b&f gemma hook rejuvenece sin dolor',\n", "       'testimonio 2 + testimonio madres', 'hook 2 + b&f',\n", "       'hook gemma 3 + b&f', 'hook 7 + b&f', 'hook 7',\n", "       'testimonio 4 + b&f', 'testimonio 4 voiceover + b&f',\n", "       'testimonio 7 + b&f', 'video gemma no voiceover', 'hooke gemma 6',\n", "       'hooke gemma 5', 'hook 10 gemma', 'hook 9 gemma', 'hook 8 gemma',\n", "       'hook 7 gemma'], dtype=object)"]}, "execution_count": 297, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"HOOK\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "c79ed010", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "f985998d", "metadata": {}, "source": ["## FEATURES"]}, {"cell_type": "code", "execution_count": 298, "id": "a1a7599f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE CREATED</th>\n", "      <th>USED?</th>\n", "      <th>CENTRO</th>\n", "      <th>FACIAL/CORPORAL</th>\n", "      <th>CAMPAIGN #</th>\n", "      <th>AUDIENCE</th>\n", "      <th>Ad#</th>\n", "      <th>ANGLE</th>\n", "      <th>HOOK</th>\n", "      <th>FEATURES</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>416</th>\n", "      <td>06/02/2025</td>\n", "      <td>No</td>\n", "      <td>GNB GONOWBEAUTY</td>\n", "      <td>Facial</td>\n", "      <td>CBO Facial bcn  7</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>Foto Estetica</td>\n", "      <td>B&amp;f 3 results ''haace falta mas que una crema''</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>756</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>1.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>757</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>758</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>759</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>760</th>\n", "      <td>07/03/2025</td>\n", "      <td>No</td>\n", "      <td>AEM Aurora Estética Madrid</td>\n", "      <td>Corporal</td>\n", "      <td>37</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>general</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1142</th>\n", "      <td>12/2/2025</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>2.0</td>\n", "      <td>GENERAL</td>\n", "      <td>video de</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1143</th>\n", "      <td>12/2/2025</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>3.0</td>\n", "      <td>GENERAL</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1144</th>\n", "      <td>12/2/2025</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>4.0</td>\n", "      <td>GENERAL</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1145</th>\n", "      <td>12/2/2025</td>\n", "      <td>No</td>\n", "      <td>OP Opatra</td>\n", "      <td>Facial</td>\n", "      <td>12</td>\n", "      <td>BROAD</td>\n", "      <td>5.0</td>\n", "      <td>GENERAL</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DATE CREATED USED?                      CENTRO FACIAL/CORPORAL  \\\n", "416    06/02/2025    No             GNB GONOWBEAUTY          Facial   \n", "756    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "757    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "758    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "759    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "760    07/03/2025    No  AEM Aurora Estética Madrid        Corporal   \n", "1142    12/2/2025    No                   OP Opatra          Facial   \n", "1143    12/2/2025    No                   OP Opatra          Facial   \n", "1144    12/2/2025    No                   OP Opatra          Facial   \n", "1145    12/2/2025    No                   OP Opatra          Facial   \n", "\n", "             CAMPAIGN # AUDIENCE  Ad#          ANGLE  \\\n", "416   CBO Facial bcn  7    BROAD  1.0  Foto Estetica   \n", "756                  37    BROAD  1.0        general   \n", "757                  37    BROAD  2.0        general   \n", "758                  37    BROAD  3.0        general   \n", "759                  37    BROAD  4.0        general   \n", "760                  37    BROAD  5.0        general   \n", "1142                 12    BROAD  2.0        GENERAL   \n", "1143                 12    BROAD  3.0        GENERAL   \n", "1144                 12    BROAD  4.0        GENERAL   \n", "1145                 12    BROAD  5.0        GENERAL   \n", "\n", "                                                 HOOK FEATURES  \n", "416   B&f 3 results ''haace falta mas que una crema''      Na<PERSON>  \n", "756                                               NaN      NaN  \n", "757                                               NaN      NaN  \n", "758                                               NaN      NaN  \n", "759                                               NaN      NaN  \n", "760                                               NaN      NaN  \n", "1142                                        video de       NaN  \n", "1143                                              NaN      NaN  \n", "1144                                              NaN      NaN  \n", "1145                                              NaN      NaN  "]}, "execution_count": 298, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df[\"FEATURES\"].isnull()]"]}, {"cell_type": "code", "execution_count": 299, "id": "796a5391", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['B&F 3, OP6, valuestack', 'OP6, valuestack',\n", "       'B&F 3, OP1, OP6, valuestack', 'B$F 3, Madres4, Valuestack , OP6',\n", "       'Voiceover alba', 'Voiceover  alba',\n", "       'B&F 1,3 fast, Voiceover alba',\n", "       'OP2, OP6 B&F 1,3 fast, Voiceover alba ', 'Voiceover alba ',\n", "       'Valuestack AEM ', 'Valuestack ', 'Valuestack',\n", "       'B&F 1,3 fast, Voiceover alba ',\n", "       'AI Footage, 3 points, valuestack, voiceover alba. AI voice',\n", "       '<PERSON> Footage, <PERSON><PERSON><PERSON>, 3 points, coice<PERSON> alba, AI voice',\n", "       'B&F Voiceover Alba, AI footage, Valuestack, 3 points',\n", "       'B&F Voiceover Alba, AI footage, 3 points, valuestack, AI voice',\n", "       'B&F Voiceover Alba, values<PERSON><PERSON>, 3 points, Voiceover alba, AI voice',\n", "       'B&F Voiceover Alba, valuestack, Voiceover alba',\n", "       'valuestack, voiceover alba, B&F Voiceover Alba',\n", "       'voiceover alba, B&F alba, valuestack, voiceover alba',\n", "       'valuestack, voiceover alba ', 'Voiceover alba, 4 points ',\n", "       \"Voiceover alba ''yo tenia estas arrugas''\",\n", "       'Voiceover corporal alba ', \"Voiceover alba ''yo no lo creia''\",\n", "       'valuestack', 'B&F 1,5,3 fast, valuestack',\n", "       'periodico, imagen laser zerona, static, CTA',\n", "       'imagen laser zerona, static, CTA', 'Voiceover alba corporal',\n", "       'Voiceover alba \"\\'articulo\"', '1 BODY ,1 CTA SARA ',\n", "       'B&F AEM, body 1 sara, IF jose, 4 points', 'valuestack ',\n", "       'b&f 1,3,5 valuestack', 'madres 8,2', 'body 1, CTA 1 sara',\n", "       ' Voiceover alba', 'Testimonial CTA1 alba',\n", "       'b&f voiceoveralba valuestack',\n", "       'Voiceover alba \\'\\'articulo\" OP2, OP6 B&F 1,3 fast, Voiceover alba ',\n", "       'Voiveover alba corporal ',\n", "       'madre 10, 5 Valuestcak Voiceover alba', 'Valuestack, banner',\n", "       'voiceover alba', 'CTA 4 ad4, Madres 8,2',\n", "       '<PERSON><PERSON> 6, 9, 4 Voice over alba ', 'madre 6, 9, 4 Voiceover alba',\n", "       'valuestack\\n', 'testimonial madres voiceover alba',\n", "       \"Laura 1, IF, B&F 3 fast, ''yo no lo creia'' alba, valuestack\",\n", "       \"IF, Laura 1, ''yo no lo creia'' alba, valuestack\",\n", "       \"IF, B&F 3 fast, ''yo no lo creia'' alba, valuestack\",\n", "       'Laura 1, IF, B&F, Valuestack',\n", "       'Carolina 1, B&F 3 fast, Laura 1, IF, B&F, Valuestack',\n", "       'Carolina 1, B&F 3, Laura 1, IF, B&F, Valuestack',\n", "       'valuestack (Bodysculpting personalizado)\\n',\n", "       'RDR video popping AEM', 'Voiceover alba recortado, Valuestack',\n", "       'B&F alba, valuestack',\n", "       '(artic<PERSON>, yo no lo creia, B&Falba) Voiceover alba',\n", "       'madres 6, valuestack', 'Voiceover alba recortado',\n", "       'Voiceover alba B&F, voice 3 maria',\n", "       'Voiceover alba B&F, voice 2 maria',\n", "       'Voiceover alba B&F, voice 5 maria', '<PERSON><PERSON><PERSON>, voice 3 maria',\n", "       '<PERSON><PERSON><PERSON>, voice 2 maria', 'carolina 1, <PERSON>, valuestack',\n", "       'carolina 1, <PERSON>, valuestack (regalo)',\n", "       'carolina 3, IF, valuestack',\n", "       'carolina 3, <PERSON>, valuestack (regalo)',\n", "       'carolina 3, IF, valuestack ', 'valuestack AEM RDR',\n", "       'valuestack AEM', 'voice over carolina ',\n", "       'valuestack voice over carolina ',\n", "       \" ''articulo'' voice over carolina \", 'Carolina voiceover B&F ',\n", "       'Carolina voiceover B&F, valuestack ',\n", "       'IF, Carolina voiceover B&F3, valuestack ',\n", "       'Foto estetica, Carolina Voiceover, IF, B&F3 , valuestack ',\n", "       'if, b&f, valuestack  ', 'Carolina voiceover ', ' valuestack ',\n", "       ' Carolina voiceover ', 'voiceover alba ', 'voiceover carolina ',\n", "       'voice over carolina',\n", "       \"' Y lo que haces es...''voiceover carolina \",\n", "       'b&f 3 fast whoop, voiceover carolina valuestack',\n", "       'madres 5, b&f 3 fast whoop, voiceover carolina valuestack',\n", "       'madres 10, madres 5, voiceover carolina valuestack',\n", "       'madres 6, madres 10, b&f 3 fast whoop, voiceover carolina valuestack',\n", "       'sara testi, madres 10, b&f 3 fast whoop, voiceover carolina valuestack',\n", "       'madres 6, sara testi, madres 10, voiceover carolina valuestack',\n", "       'voice over carolina 48 segs',\n", "       'testi carla, madres 6, voice over carolina 48 segs',\n", "       'madres 11, testi carla, madres 7, voice over carolina 48 segs',\n", "       'voice over carolina, valuestack AEM',\n", "       'valuestack AEM b&f, voice over carolina',\n", "       'madres 6, voiceover carolina', 'b&f voiceover carolina',\n", "       'foto estetica, voiceover carolina 48 segs',\n", "       'voiceover carolina 48 segs',\n", "       'testimonials, b&f fast whoop, valuestack carolina',\n", "       'voice over carolina, tetstimonials, voiceover carolina',\n", "       'voiceover carolina, valuestack carolina',\n", "       'testimonials, b&f 3 red points, voiceover carolina (terapia ultrasonica)  valuestack carolina',\n", "       'testimonials, valuestack carolina',\n", "       'testimonials, b&f 5 red points, voiceover carolina (terapia ultrasonica)  valuestack carolina',\n", "       nan, 'madres 7,3 cta1',\n", "       \"Foto estetica, Carolina Voiceover'' 30% menos colageno'', IF, Carolina voiceover B&F3, valuestack \",\n", "       \" Carolina Voiceover  ''25 anos'', IF, Carolina voiceover B&F3, valuestack \",\n", "       'if, b&f, valuetsack ', 'if, madres 4 b&f, valuetsack ',\n", "       'voiceovcer carolina', 'voice over carolina, valuestack',\n", "       ',voice over carolina, if, valuestack',\n", "       'testimonials, valuestack RDR, Voiceover carolina',\n", "       'testimonials, valuestack, Voiceover carolina',\n", "       'valuestack, voiceover carolina, b&f fast whoop, testimonials',\n", "       'valuestack AEM, voiceover carolina',\n", "       'testimonials, valuestack , voiceover carolina',\n", "       'b&f fast whoop, valuestack, testimonial voiceover',\n", "       'b&f fast whoop, testimonials, valuestack',\n", "       'b&f fast whoop, testimonials,valuestack voiceover carolina',\n", "       'b&f fast whoop, testimonial valuestack', 'Body 1, cta 5',\n", "       'b&f 3 con flechas, Body 1, cta 5', 'testimonial 1 gemma, cta 5',\n", "       'voiceover gemma ', 'voiceover carolina', 'b&f alba, valuestack',\n", "       'b&f carol, cta carla', 'internet footage', 'testimonio gemma',\n", "       'facial body 1',\n", "       'voiceover (estaba buscando un tratamiento facial para las arrugas que no hiciese daño) b&f barbara',\n", "       'testimonio facial madres', 'videos k+', 'voiceover b&f cta gemma',\n", "       'voiceover internet footage cta sara',\n", "       'body 2 facial sara 1, cta sara 1', 'voiceover b&f',\n", "       'voiceover gemma, internet footage, cta 2 gemma',\n", "       'body facial 1 sara 1, cta 5 sara', 'voiceover b&f, cta sara',\n", "       'cta carla', 'voiceover b&f ', 'testimonio madres 1',\n", "       'voiceover internet footage', 'testimonios madres 1',\n", "       'voiceover carolina, testimonios madres, valuestack',\n", "       'voiceover, internet footage, testimonio madres, valuestack',\n", "       'b&f stickers, valuestack', 'voiceover art<PERSON><PERSON><PERSON>, valuestack',\n", "       'tratamiento', 'results', 'cta oferta', 'after results', 'amr',\n", "       'los resultados os sorprenderan', 'testimonio madres',\n", "       'tratamiento, before results', 'voiceover, valuestack',\n", "       'voiceover valuestack', 'reclama tu 2x1', 'valuestack voiceover',\n", "       'b&f 1,3 fast + hooks facial sara1 audio, Body 2 facial sara1 + 10(dx), CT SARA',\n", "       'Body 1 facial sara1 + 10(dx), CT SARA',\n", "       'b&f 3 + VoiceOver bodys gemma, CTA 1 (musica de fondo en todo el clip)',\n", "       'b&f 3 + voiceover bodys gemma, CTA 1', 'voiceover art<PERSON><PERSON><PERSON>',\n", "       'after, voiceover art<PERSON><PERSON><PERSON>', 'voiceover, cta 3', 'cta 1',\n", "       'testimonios madres', 'cta 2',\n", "       'acompañame + vocals hooks 1 y vocals acompañame, cta + background music',\n", "       'voice over articulo + articulo + acompañame, after + voiceover articulo, valuestack canva + voiceover body 1, CTA (musica de fondo)',\n", "       'voiceover articulo + articulo, b&f 1, 3 fast + voiceover ben<PERSON><PERSON><PERSON>, CTA 2',\n", "       'voiceover articulo + articulo, b&f 1, 3 fast + voiceover ben<PERSON><PERSON><PERSON>, CTA 3',\n", "       'voiceover articulo', 'testimonios, voiceover',\n", "       'hooks 1 gral 1, voiceover articulo + canva valuestack, CTA 3',\n", "       'voiceover facial 3 + maquinas, after results + voiceover facial 6, CTA 3',\n", "       'hooks 1 gral 1, voiceover facial 3 + maquinas, after results + voiceover facial 6, CTA 4',\n", "       'hooks 1 gral 1, voiceover facial 3 + maquinas, voiceover acompañame body 7 + videos interior y productos, CTA 1',\n", "       'b&f + voiceover bodys 1, CTA 1',\n", "       'voiceover gemma testimonials facial 3 + articulo, b&f 1,3 fast, valuestack ',\n", "       'voiceover gemma testimonials facial 3 + articulo + valuestack ',\n", "       'voiceover testimonials + valuestack, cta testimonnials',\n", "       'b&f 1,3 fast + voiceover testimonials valuestack + canva',\n", "       'b&f 1, 3 fast + voiceover body 3 gemma 1, CTA 2 gemma 1 ',\n", "       'b&f 1, 3 fast + voiceover body 3 gemma 1, CTA 2 gemma 2',\n", "       'b&f 1, 3 fast + voiceover body 3 gemma 1, CTA 2 gemma 3',\n", "       'testimonials facial gemma + articulo, voiceover body 3 gemma + b&f 1,3 fast, CTA 2 gemma 3',\n", "       'b&f reclama tu 2x1, valuestack', 'b&f + valuestack',\n", "       'b&f + voiceover', 'articulo, b&f',\n", "       'testimonial marta + miriam + gemma, voiceover articulo',\n", "       'cta es posible', 'valuestack + inetrent footage', 'b&f',\n", "       'testimonial facial gemma voiceover + b&f 3 + articulo + canva + b&f 1,3 fast + valuestack (musica justin timberlake)',\n", "       'testimonial facial gemma voiceover + b&f 3 + articulo + canva + b&f 1,3 fast + valuestack (musica idea 22 sped up + birds)',\n", "       'b&f + valuestack (musica idea 22 sped up + birds)',\n", "       'articulo, b&f, testimonios madres, valuestack (musica idea 22 sped up + birds)',\n", "       'articulo, b&f, valuestack (musica idea 22 sped up + birds)',\n", "       'articulo, b&f, valuestack (musica justin timberlake)',\n", "       'voiceover gemma', 'madres6, voiceover gemma',\n", "       'gemma, b&f 1,5,3 valuestack', 'voiceover corporal alba',\n", "       'b&f, voiceover corporal alba', 'voiceover dibujo',\n", "       'testimonio madres + valuestack', 'valuestack + voiceover',\n", "       'art<PERSON><PERSON><PERSON>, b&f, valuestack (musica idea 22 sped up + birds)',\n", "       'voiceover art<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (musica idea 22 sped up + birds)',\n", "       'voiceover art<PERSON><PERSON><PERSON>, testimonios madres, values<PERSON>ck (musica idea 22 sped up + birds)',\n", "       'b&f, articulo, valuestack, audio testimonial gemma (musica idea 22 sped up + birds)',\n", "       'b&f, articulo, valuestack, audio testimonial gemma (musica cant stop the feeling)',\n", "       'articulo, valuestack (musica idea 22)',\n", "       'articulo, testimonial madres, valuestack (musica idea 22)',\n", "       'valuestack + testimonial raquel', 'valuestack + cta',\n", "       'valuestack, cta + voiceover testimonial', 'valuestack, cta',\n", "       'voiceover testimonials + valuestack, cta', 'b&f stickers',\n", "       'voiceover anuncio',\n", "       'gemma 7,  voiceover testimonials + valuestack, cta',\n", "       'b&f 1,3 fast, voiceover testimonials + valuestack, cta',\n", "       'b&f + sticker valuestack',\n", "       'b&f + voiceover gemma 3, valuestack + voiceover testimonials, sticker 2x1',\n", "       'valuestack + voiceover testimonials, sticker 2x1',\n", "       'valuestack + voiceover testimonials, testimonio madres, sticker 2x1',\n", "       'b&f + valuestack, sticker 2x1',\n", "       'articulo, b&f, valuestack, sticker 2x1', 'voiceover dibujos',\n", "       'valuestack no voiceover', 'internet footage voiceover gemma',\n", "       'testimonio', 'testimonial madres, articulo, valuestack',\n", "       'b&f, testimonial madres, valuestack',\n", "       'b&f, testimonial, valuestack', 'testimonial, valuestack',\n", "       'testimonial madres, valuestack, b&f 1,3 fast cta',\n", "       'testimonial madres, valuestack, cta', 'cta gemma',\n", "       'b&f valuestack', 'voiceover acompañame', 'cta sara',\n", "       'voiceover corto', 'beneficios antiaging',\n", "       'articulo + voiceover gemma testimonios 1, b&f, testimonio madres, valuestack + canva creative chica en bikini',\n", "       'testimonio madres, valuestack + canva creative chica en bikini',\n", "       'valuestack + canva creative chica en bikini',\n", "       'articulo, valuestack + canva creative chica en bikini',\n", "       'articulo, valuestack, canva creative chica en la playa',\n", "       'articulo, valuestack + canva creative chica señalando',\n", "       'testimonial marta, valuestack, canva creative chica señalando',\n", "       'valuestack, canva creative chica señalando',\n", "       'valuestack, testimonio raquel, canva creative chica en bikini',\n", "       'valuestack, testimonio raquel, canva creative chica en la playa',\n", "       'valuestack, canva creative chica en la playa',\n", "       'voiceover (lo que hacen)', 'testimonio mardes',\n", "       'b&f, valuestack + voiceover gemma testimonials, cta canva creativo chica en la playa',\n", "       'b&f, valuestack, cta canva creativo chica en la playa',\n", "       'valuestack, canva creativo chica en la playa',\n", "       'valuestack, cta gemma 4 + canva creativo chica en la playa',\n", "       'testimonio raquel, <PERSON><PERSON><PERSON>, cta gemma 4 + canva creativo chica en la playa',\n", "       'testimonio madres, valuestack + b&f', 'valuestack + b&f',\n", "       'b&f + articulo, testimonio madres, valuestack',\n", "       'b&f + articulo,  valuestack', 'b&f reclama tu 2x1',\n", "       'descubre la oferta', 'voiceover',\n", "       'testimonio madres, ad recording, gemma', 'b&f de esto a esto',\n", "       'testimonio miriam, valuestack', 'testimonio raquel, valuestack',\n", "       'gemma 11, valuestack', 'articulo',\n", "       'testimonio madres, valuestack', 'articulo, valuestack',\n", "       'testimonio miriam, articulo, valuestack',\n", "       'articulo, testimonio madres, valuestack',\n", "       'testimonio ad recording + testimonio madres + testimonio sara',\n", "       'b&f + titulo (el tratamiento que esta remplazando la liposuccion)',\n", "       'testimonio raquel, b&f + valuestack, cta ',\n", "       'gemma 2, b&f + valuestack, cta', 'lo que hacen, valuestack, cta',\n", "       'lo que hacen, testimonio, valuestack, cta',\n", "       'articulo, testimonio madres, b&f, valuestack',\n", "       'articulo, b&f. testimonio, valuestack',\n", "       'articulo, b&f, valuestack', 'Body 1 SARA', 'Body 1 Alba',\n", "       'madres 1, values<PERSON>ck, madre 4.', ' BODY ,1 CTA SARA',\n", "       \" Voiceover alba ''yo tenia estas arrugas''\",\n", "       'Voiceover corporal alba', \"Voiceover alba ''yo no lo creia'\",\n", "       'B&F 1,3 fast, Voiceover alba.',\n", "       'OP2, OP6 B&F 1,3 fast, Voiceover alba', ' B&F 3, OP6, valuestack',\n", "       'B&F 3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> , OP6', 'Voiceover alba, 4 points',\n", "       ' Voiceover alba, 4 points', 'valuestack, voiceover alba',\n", "       'AI Footage, 3 points, valuestack, voiceover alba',\n", "       ' <PERSON> Footage, <PERSON><PERSON><PERSON>, 3 points, coice<PERSON> alba, AI voice',\n", "       'B&F3, Madre 3', 'madre 7, B&F 3 pantalla dividida',\n", "       'carla testimonial B&F 3 pantalla dividida',\n", "       'Sara testimonial B&F 3 pantalla dividida',\n", "       'carla testimonial, 10IF', 'body1, IF 9,6 carla',\n", "       'body1, B&F 3 carla', 'IF 3, CTA4 carla',\n", "       'Testimonial Sara, B&F 1', 'B&F 1, Testimonial carla, CTA 5',\n", "       'B&F 3', 'B&F 3, Valuestack ', 'B&F 3, Madres 1',\n", "       'valuestack, B&F 3', \"B&F 3, Valuestack CTA ''22 de nov''\",\n", "       'B&F 3 ', 'B&F 3, valuestack', 'Valuestack , B&F 3, madre10',\n", "       'B&F 3, Valuestack', 'B&F 3, Valuestack \"\\'hasta el 22 de nov\\'\\'',\n", "       'body 1. CTA 1 sara',\n", "       'testimonios de madres (marta y raquel), valuestack, CTA2 de GEMMA',\n", "       'testimonials, valuestack, voiceover', 'conversacion whatsap',\n", "       'me encontra este anuncio', 'resultados',\n", "       'internet footage resultados', 'internet footage, valuestack, cta',\n", "       'b&f, internet footage + funciona, valuestack, cta',\n", "       'internet footage, b&f, valuestack, cta',\n", "       'b&f, internet footage, elimina arrugas,valuestack,cta',\n", "       'b&f + voiceover de esto a esto', 'b&f + voiceover no me lo creia',\n", "       'b&f + testimonio madres', 'valuestack stickers',\n", "       'b&f piel estirada', 'valuestack + b&f, cta',\n", "       'testimonio madres, cta', 'faster hook results',\n", "       'b&f internet footage (rubia) + valuestack',\n", "       'internet footage, valuestack', 'b&f, valuestack (sin musica)',\n", "       'b&f, valuestack (con musica)', 'asi fue el tratamiento',\n", "       'cta selfie', 'acompañame', 'telecinco',\n", "       'voiceover + testimonio madres', 'telecinco + results',\n", "       'testimonio amdres+ ad recxording + sara', 'b&f, valuestack',\n", "       'internet footage, b&f, valuestack',\n", "       'usando cremas sin resultados, b&f',\n", "       'internet footage, testimonio madres', '2x1',\n", "       'voiceover dibujo + testimonio madres', 'testimonios',\n", "       'b&f + voiceover body 1, testimonio raquel',\n", "       'testimonio miriam, valuestack, b&f', 'voiceover body 2',\n", "       'voiceover articulo corto', 'testimonio, valuestack',\n", "       'no necesitas cirugia + b&f, articulo, valuestack',\n", "       'testimonio madre', 'testimonio ad recording',\n", "       'testimonio madre + cllip telecinco',\n", "       'valuestack, sticker reserva tu 2x1', 'voiceover articulo short',\n", "       'articulo corto', 'articulo corto, testimonio madres',\n", "       'voiceover alternativa al botox + ia footage',\n", "       'voiceover el tratamiento se llama', 'b&f internet footage fast',\n", "       'b&f ia footage footage fast', 'si haces dieta, articulo corto',\n", "       'articulo corto, testimonio miriam',\n", "       'testimonio miriam, valuestack, cta',\n", "       'puedes eliminar arrugas sin agujas + b&f, valuestack, cta',\n", "       'testimonio madres, articulo corto, reclama tu 2x1',\n", "       'dile adios a la grasa localizada, articulo corto, reclama tu 2x1',\n", "       'articulo corto, reclama tu 2x1',\n", "       'articulo corto, testimonio madres, reclama tu 2x1',\n", "       'internet footage graficos', 'b&f, testimonio madres, cta',\n", "       'b&f, testimonio madres', 'b&f, testimonio sara',\n", "       'b&f, testimonio ad recording',\n", "       'b&f, testimonio sara + madres + ad recording',\n", "       'voiceover articulo corto + testimonio madres',\n", "       'voiceover articulo corto + testimonio ad recording',\n", "       'voiceover b&f ia footage',\n", "       'testimonio madres, articulo voiceover + b&f, valuestack, testimonio madres',\n", "       'articulo voiceover + b&f, valuestack + voiceover articulo',\n", "       'testimonio madres, b&f, valuestack + voiceover articulo',\n", "       'voiceover corto articulo + testimonio madres', 'b&f recap',\n", "       'voiceover corto articulo', 'cta', 'voiceover cta',\n", "       'internet footage + b&f, valuestack', 'internet footage + b&f',\n", "       'captura pantalla anuncio', 'ia footage', 'ia fooatge',\n", "       'b&f, testimonio', 'body 2 gemma + b&f', 'cta ',\n", "       'cta creativo canva',\n", "       'voiceover valuestack + b&f + creativo canva',\n", "       'greenscrean testimonio madres', 'testimonio sara',\n", "       'b&f greenscrean (testimonio parado)', 'valuestack corto',\n", "       'testimonio madre + valuestack', 'gemma voiceover greenscrean',\n", "       'testimonio madres + reclama tu 2x1',\n", "       'hook reduce el 15% + b&f voiceover + testimonio madres',\n", "       'hook reduce el 15% + b&f voiceover',\n", "       'b&f voiceover + hook reduce desde la primera sesión',\n", "       'hook reduce desde la primera sesión + b&f voiceover',\n", "       'hook reduce desde la primera sesión + b&f voiceover + testimonio madres',\n", "       'tvoiceover \"el botox paraliza la cara\" + b&f, cta ia',\n", "       'ai avatar \"no te hagas este tratamiento\" voiceover + b&f',\n", "       'voiceover pasar de esto a esto + valuestack',\n", "       'cta reclama tu 2x1', 'internet footage zoom arrugas',\n", "       'cta puedes ser como ella',\n", "       'crees que se puede eliminar estas arrugas sin dolor? dale click',\n", "       ' ai avatar reclama tu 2x1', 'b&f + reclama tu 2x1',\n", "       'testimonio madres + b&f', 'reserva tu 2x1 stickers',\n", "       'b&f + testimonio ad recording', 'b&f voiceover de esto a esto',\n", "       'de esto a esto + b&f, valuestack',\n", "       'b&f + body (te hacen bajar), voiceover de esto a esto + cta reclama tu 2x1',\n", "       'b&f + de esto a esto, cta reclama tu 2x1',\n", "       'b&f + de esto a esto, cta reclama tu 2x2',\n", "       'b&f + de esto a esto, cta reclama tu 2x3',\n", "       'b&f + de esto a esto, cta reclama tu 2x4',\n", "       'b&f voiceover de esto + valuestack',\n", "       'testimonio carla, valuestack', 'de esto a esto, valuestack',\n", "       'voiceover porque nadie habla', 'testimonios, valuestack',\n", "       'articulo ', 'voiceover dibujo canva valuestack',\n", "       'articulo voiceover', 'articulo corto voiceover + b&f, valuestack',\n", "       '(escuchar a las clientas) testimonio madres',\n", "       'b&f + testimonial VH', 'pantalla verde b&f',\n", "       'b&f fast + valuestack', 'hok reclama tu 2x1', 'body 1 + b&f',\n", "       'b&f, valuestack ipad', 'valuestack ipad',\n", "       'de esto a esto, valuestack ipad',\n", "       'articulo corto, valuestack ipad', 'b&f voiceover',\n", "       'testimonio madres, valuestack ipad',\n", "       'de esto a esto + valuestack ipad',\n", "       'de esto a esto corto, valuestack ipad',\n", "       'de esto a esto corto, valuestack',\n", "       'b&f fast voicoever + reclama tu 2x1', 'testimonio carla',\n", "       'voiceover valuestack + b&f', 'todo por 99€, reclama tu 2x1',\n", "       'valustack', 'testimonio + valuestack',\n", "       '5 motivos por lo que probar el bdoy sculpt + b&f',\n", "       'solo hay una manera de reducirla sin dietas ni dolor + b&f + valuestack',\n", "       'estos fueron los resultados + testimonio madres',\n", "       'sin pinchazos, sin dolor b&f',\n", "       'testimonio madres / b&f, valuestack',\n", "       'valuestack voiceover + b&f',\n", "       'de esto a esto, testimonio, valuestack',\n", "       'b&f + testimonial 4, cta testimonial 2', 'b&f, valuestack, cta 3',\n", "       ' b&f', 'b&f, testimonial gemma 1',\n", "       'valuestack + test 5 voiceover', 'voiceover aticulo, valuestack',\n", "       'gemma testimonial 1 + b&f, valuestack',\n", "       'testimonio gemma 1, valuestack', 'testimonio gemma 4, valuestack',\n", "       'b&f + test gemma 1 voiceover, valuestack', 'testimonio gemma 1',\n", "       'testimonio 3', 'b&f voiceover (esto lo consiguen)',\n", "       'b&f, cta 2, valuestack', 'b&f + arrticulo voiceover'],\n", "      dtype=object)"]}, "execution_count": 299, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"FEATURES\"].unique()"]}, {"cell_type": "markdown", "id": "c85156cc", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab61ba03", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clibel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}