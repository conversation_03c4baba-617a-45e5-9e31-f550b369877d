#!/usr/bin/env python3
"""
Actualiza los tres agentes de Mistral AI usando los MISMO comandos
que ya ejecutas a mano (jq + curl), pero de forma automática.
"""

import os
import subprocess
from pathlib import Path
import sys
import json
import shutil

# Carpetas -> ID de agente en Mistral
AGENTS = {
    "corporal":   "ag_0685bfc469e17f2b8000a9cceb0be6d4",
    "facial":    "ag_0685d0ddaa3d794b80007667fd756c5d",
    "instagram": "ag_01983707182272f3b553c2791048bca8",
}

# Token de Mistral (exporta la variable antes de ejecutar el script)
MISTRAL_TOKEN = "5to7CRk1LoFKFlJ2JZ69hSLTsYUo8KEc"

if not MISTRAL_TOKEN:
    sys.exit("❌  Exporta primero tu token:  export MISTRAL_TOKEN='…'")

ROOT = Path(__file__).resolve().parent
JQ_BIN = shutil.which("jq")  # Usa jq si está instalado


def build_payload(folder: Path) -> Path:
    """Genera payload.json dentro de la carpeta usando jq o, en su defecto, Python."""
    prompt = folder / "prompt.json"
    payload = folder / "payload.json"

    if not prompt.exists():
        print(f"⚠️  {prompt} no encontrado; se omite esta carpeta.")
        return None

    if JQ_BIN:
        cmd = f'''{JQ_BIN} '(.description, .instructions) |= join("\\n")' "{prompt}" > "{payload}"'''
        subprocess.run(cmd, shell=True, check=True, cwd=folder)
    else:
        # Reemplazo en Python (por si jq no está disponible)
        with prompt.open(encoding="utf-8") as f:
            data = json.load(f)
        for field in ("description", "instructions"):
            if isinstance(data.get(field), list):
                data[field] = "\n".join(data[field])
        payload.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")

    return payload


def patch_agent(folder: Path, agent_id: str, payload_path: Path) -> None:
    """Lanza el mismo curl que usas manualmente."""
    curl_cmd = [
        "curl",
        "--silent", "--show-error", "--fail",
        "--location",
        "--request", "PATCH",
        f"https://api.mistral.ai/v1/agents/{agent_id}",
        "--header", "Content-Type: application/json",
        "--header", f"Authorization: Bearer {MISTRAL_TOKEN}",
        "--data", f"@{payload_path.name}",          # nombre relativo dentro de la carpeta
    ]

    print(f"⏳  Actualizando {folder.name}…")
    try:
        subprocess.run(curl_cmd, cwd=folder, check=True)
        print(f"✅  {folder.name}: actualizado correctamente.\n")
    except subprocess.CalledProcessError as e:
        print(f"❌  {folder.name}: error al actualizar (código {e.returncode}).\n")


def main() -> None:
    for dirname, agent_id in AGENTS.items():
        folder = ROOT / dirname
        payload = build_payload(folder)
        if payload:
            patch_agent(folder, agent_id, payload)


if __name__ == "__main__":
    main()
